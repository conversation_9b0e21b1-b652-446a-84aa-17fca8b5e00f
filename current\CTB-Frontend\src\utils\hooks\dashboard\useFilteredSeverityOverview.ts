import { useState, useEffect, useCallback } from "react";
import { getFilteredSeverityOverview } from "../../api/endpoints/dashboard/dashboardApi";
import { SeverityOverview, ProgramSeverityData } from "./useSeverityAndTrend";

export interface FilteredSeverityData {
  total: SeverityOverview;
  programs: ProgramSeverityData[];
}

/**
 * Hook to fetch severity overview data with filtering capability
 * @param filter - Filter type ('all' or 'accepted-risk')
 * @returns Filtered severity data with loading and error states
 */
export default function useFilteredSeverityOverview(filter: 'all' | 'accepted-risk' = 'all') {
  const [data, setData] = useState<FilteredSeverityData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await getFilteredSeverityOverview(filter);
      console.log("Filtered severity overview response:", response);
      
      if (
        response &&
        typeof response === 'object' &&
        'data' in response &&
        response.success === true
      ) {
        setData(response.data);
      } else {
        console.error("Unexpected filtered severity response format:", response);
        setError("The severity overview data format is invalid");
      }
    } catch (err) {
      console.error(`Error fetching filtered severity data (${filter}):`, err);
      setError(`Failed to load severity data with filter: ${filter}`);
    } finally {
      setLoading(false);
    }
  }, [filter]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const retryFetch = () => {
    fetchData();
  };

  return { data, loading, error, retryFetch };
} 