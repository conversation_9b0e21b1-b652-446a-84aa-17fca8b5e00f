import React from 'react';
import { ReportData, DetailedFinding } from '../../types/report.types';
import BaseFindingEditor from './BaseFindingEditor';

interface HighFindingsEditorProps {
  reportData: ReportData;
  onFindingChange: (index: number, field: keyof DetailedFinding, value: string) => void;
  onRemoveFinding: (index: number) => void;
  onAddFinding: (severity: 'Critical' | 'High' | 'Medium' | 'Low') => void;
}

const HighFindingsEditor: React.FC<HighFindingsEditorProps> = (props) => {
  return <BaseFindingEditor {...props} severity="High" />;
};

export default HighFindingsEditor; 