import axios from "../../axios";
import { AxiosError } from "axios";
import { ProgramSeverityData } from "../../../hooks/dashboard/useSeverityAndTrend";

/**
 * Fetches unified business dashboard data from the API
 * 
 * This is the main method for retrieving all dashboard data in a single request,
 * which is more efficient than making multiple API calls.
 * 
 * The unified endpoint returns:
 * - Dashboard metrics (totalReports, activeIssues, resolvedIssues, retestsRemaining)
 * - Severity overview (total and per program)
 * - Reports trend data (weekly, monthly, quarterly, yearly)
 * - Business review reports (issues requiring business attention)
 * - Retest summary metrics (status counts, pass rates, etc.)
 * - Retest actions data (recent retest activities)
 * 
 * @returns Promise resolving to the unified dashboard data
 */
export const getUnifiedBusinessDashboard = async () => {
  try {
    const response = await axios.get('/dashboard/businessDashboard');
    
    if (response.status !== 200) {
      throw new Error(`API returned status ${response.status}`);
    }
    
    return response.data;
  } catch (error) {
    const axiosError = error as AxiosError;
    throw error;
  }
};

// The following functions are kept for backward compatibility
// They now use the unified dashboard endpoint internally

/**
 * Fetches the dashboard summary metrics by extracting from the unified dashboard
 * @returns Dashboard summary data including total reports, active reports, resolved issues, and retests remaining
 */
export const getDashboardSummary = async () => {
  try {
    const response = await getUnifiedBusinessDashboard();
    
    if (!response?.success || !response?.data?.metrics) {
      throw new Error("Invalid dashboard data structure");
    }
    
    // Check if we have severityOverview data to calculate active reports
    if (response.data.severityOverview && response.data.severityOverview.total) {
      const severityData = response.data.severityOverview.total;
      
      // Calculate active reports from severity data in the same way as the donut chart:
      // Active = Total - (BusinessResolved + RealResolved)
      let activeIssuesCount = 0;
      let totalIssuesCount = 0;
      
      // Process each severity category
      ['critical', 'high', 'medium', 'low'].forEach(category => {
        const categoryData = severityData[category];
        if (categoryData) {
          // Total count of issues
          totalIssuesCount += categoryData.total || 0;
          
          // Business accepted issues
          const businessResolved = categoryData.businessResolved || 0;
          
          // Truly resolved issues (fixed)
          const realResolved = categoryData.realResolved || 0;
          
          // Active reports = Total - (BusinessResolved + RealResolved)
          const activeInCategory = Math.max(0, (categoryData.total || 0) - businessResolved - realResolved);
          activeIssuesCount += activeInCategory;
        }
      });
      
      // Calculate a change percentage (can be null if no history data available)
      const changePercent = totalIssuesCount > 0 ? 
        Math.round((activeIssuesCount / totalIssuesCount) * 100) - 100 : null;
      
      // Add activeIssues to the response
      return {
        success: true,
        data: {
          ...response.data.metrics,
          activeIssues: {
            current: activeIssuesCount,
            changePercent: changePercent
          }
        }
      };
    }
    
    // If no severity data available, just return metrics as is
    return {
      success: true,
      data: response.data.metrics
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Fetches severity overview and trend analysis data from unified dashboard
 * @returns Data for severity donut chart and reports trend line chart
 */
export const getSeverityAndTrend = async () => {
  try {
    const response = await getUnifiedBusinessDashboard();
    
    if (!response?.success || !response?.data?.severityOverview || !response?.data?.reportsTrend) {
      throw new Error("Invalid dashboard data structure");
    }
    
    return {
      success: true,
      data: {
        severityOverview: response.data.severityOverview,
        reportsTrend: response.data.reportsTrend,
        metadata: {
          dataStartDate: null,
          dataEndDate: null
        }
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Fetches severity overview data with optional filtering from unified dashboard
 * @param filter - Optional filter parameter ('all' or 'accepted-risk')
 * @returns Filtered severity overview data for donut chart
 */
export const getFilteredSeverityOverview = async (filter: 'all' | 'accepted-risk' = 'all') => {
  try {
    const response = await getUnifiedBusinessDashboard();
    
    if (!response?.success || !response?.data?.severityOverview) {
      throw new Error("Invalid dashboard data structure");
    }
    
    // If we want the 'all' data, just return the data as is
    if (filter === 'all') {
      return {
        success: true,
        data: response.data.severityOverview,
        filter: filter
      };
    }
    
    // For 'accepted-risk' filter, we need to transform the data to include only business resolved items
    if (filter === 'accepted-risk') {
      const originalData = response.data.severityOverview;
      
      // Create a filtered version where:
      // 1. total = businessResolved (only show accepted risk items)
      // 2. businessResolved remains the same
      // 3. Set realResolved to 0 (we don't want to show these in accepted risk view)
      const filteredData = {
        total: {
          critical: { 
            total: originalData.total.critical.businessResolved, 
            businessResolved: originalData.total.critical.businessResolved,
            realResolved: 0
          },
          high: { 
            total: originalData.total.high.businessResolved, 
            businessResolved: originalData.total.high.businessResolved,
            realResolved: 0
          },
          medium: { 
            total: originalData.total.medium.businessResolved, 
            businessResolved: originalData.total.medium.businessResolved,
            realResolved: 0
          },
          low: { 
            total: originalData.total.low.businessResolved, 
            businessResolved: originalData.total.low.businessResolved,
            realResolved: 0
          }
        },
        programs: originalData.programs.map((program: ProgramSeverityData) => ({
          ...program,
          severityOverview: {
            critical: { 
              total: program.severityOverview.critical.businessResolved, 
              businessResolved: program.severityOverview.critical.businessResolved,
              realResolved: 0
            },
            high: { 
              total: program.severityOverview.high.businessResolved, 
              businessResolved: program.severityOverview.high.businessResolved,
              realResolved: 0
            },
            medium: { 
              total: program.severityOverview.medium.businessResolved, 
              businessResolved: program.severityOverview.medium.businessResolved,
              realResolved: 0
            },
            low: { 
              total: program.severityOverview.low.businessResolved, 
              businessResolved: program.severityOverview.low.businessResolved,
              realResolved: 0
            }
          }
        }))
      };
      
      return {
        success: true,
        data: filteredData,
        filter: filter
      };
    }
    
    return {
      success: true,
      data: response.data.severityOverview,
      filter: filter
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Fetches business review issues from unified dashboard
 * @returns List of reports requiring business review and other high priority issues
 */
export const getBusinessReviewIssues = async () => {
  try {
    const response = await getUnifiedBusinessDashboard();
    
    if (!response?.success || !response?.data?.businessReviewReports) {
      throw new Error("Invalid dashboard data structure");
    }
    
    // Use totalReports from metrics if available for more accurate total count
    const totalCount = response.data?.metrics?.totalReports?.current || response.data.businessReviewReports.length;
    const hasMore = totalCount > response.data.businessReviewReports.length;
    
    return {
      success: true,
      data: response.data.businessReviewReports,
      meta: {
        hasMore,
        totalCount
      }
    };
  } catch (error) {
    throw error;
  }
}; 