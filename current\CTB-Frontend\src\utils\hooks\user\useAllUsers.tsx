import { useState } from "react";
import toast from "react-hot-toast";
import {
  UserFilters,
  useGetAllUsersQuery,
  useSetUserActivationMutation
} from "../../api/endpoints/usersApi";

const useAllUsers = () => {
  const [filters, setFilters] = useState<UserFilters>({
    limit: 10
  });
  const { data, isError, isLoading } = useGetAllUsersQuery(filters);
  const [setUserActivation] = useSetUserActivationMutation();

  /**
   * Attempts to update the given users "approved" status.
   */
  const updateUserStatus = (id: number, isApproved: boolean) => {
    const loader = toast.loading("Processing...");

    setUserActivation({ userId: id, isApproved }).then(res => {
      toast.remove(loader);

      if ("error" in res) {
        toast.error(`Failed to ${isApproved ? "activate" : "deactivate"} user`);
      } else {
        toast.success(`${isApproved ? "Activated" : "Deactivated"} user`);
      }
    });
  };

  return {
    users: data?.users || [],
    count: data?.count || 0,
    filters,
    isError,
    isLoading,
    setPage: (page: number) => setFilters(filters => ({ ...filters, page })),
    setFilters,
    updateUserStatus
  };
};

export default useAllUsers;
