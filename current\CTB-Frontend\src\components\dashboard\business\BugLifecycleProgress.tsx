import React, { useEffect } from "react";
import { motion, AnimatePresence, useAnimation } from "framer-motion";
import { BugLifecycle } from "../../../utils/hooks/dashboard/useBusinessReviewIssues";

interface BugLifecycleProgressProps {
  lifecycle: BugLifecycle;
  expanded?: boolean;
}

const BugLifecycleProgress: React.FC<BugLifecycleProgressProps> = ({ lifecycle, expanded = false }) => {
  const { currentStage, lifecycleStages } = lifecycle;
  const controls = useAnimation();
  
  // Check if all stages are completed (task is closed/verified)
  const isTaskClosed = currentStage === "VERIFIED" || 
    (lifecycleStages.length > 0 && 
     lifecycleStages[lifecycleStages.length - 1].isCompleted);

  // Special case for Risk Accepted stages - show them differently
  const isRiskAccepted = currentStage === "RISK_ACCEPTED";

  useEffect(() => {
    controls.start("animate");
  }, [controls, currentStage]);

  // Animation variants for lifecycle stages
  const stageVariants = {
    initial: { scale: 0.8, opacity: 0 },
    animate: (custom: number) => ({
      scale: 1,
      opacity: 1,
      transition: {
        delay: custom * 0.1,
        duration: 0.3,
        type: "spring",
        stiffness: 300
      }
    }),
    hover: { 
      scale: 1.05,
      boxShadow: "0 0 12px rgba(99, 102, 241, 0.5)",
      transition: { duration: 0.2 } 
    }
  };

  // Animation variants for the connector lines
  const lineVariants = {
    initial: { scaleX: 0 },
    animate: (custom: number) => ({
      scaleX: 1,
      transition: {
        delay: custom * 0.1 + 0.1,
        duration: 0.4,
        ease: "easeInOut"
      }
    })
  };

  // Particles animation for current step
  const ParticlesAnimation = () => {
    return (
      <div className="absolute inset-0 -z-10">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 rounded-full bg-indigo-300"
            initial={{ 
              x: "0%", 
              y: "0%", 
              opacity: 0.7,
              scale: 0 
            }}
            animate={{ 
              x: `${Math.random() * 200 - 100}%`, 
              y: `${Math.random() * 200 - 100}%`,
              opacity: 0,
              scale: Math.random() * 1.5 + 0.5
            }}
            transition={{ 
              repeat: Infinity, 
              duration: 1.5 + Math.random() * 1, 
              delay: Math.random() * 0.5,
              ease: "easeOut" 
            }}
          />
        ))}
      </div>
    );
  };

  // Get appropriate icon based on stage status
  const getStageIcon = (stage: any, index: number) => {
    // Specifically handle the verified/closed state
    const isVerifiedStep = index === lifecycleStages.length - 1;
    const shouldShowCheckmark = stage.isCompleted || (isVerifiedStep && isTaskClosed);
    
    if (shouldShowCheckmark) {
      return (
        <motion.svg 
          className="w-4 h-4" 
          fill="currentColor" 
          viewBox="0 0 20 20"
          initial={{ scale: 0, rotate: -45 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 300, damping: 15 }}
        >
          <path 
            fillRule="evenodd" 
            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" 
            clipRule="evenodd" 
          />
        </motion.svg>
      );
    } else if (stage.isCurrent) {
      return (
        <>
          <motion.div 
            className="w-3 h-3 bg-white rounded-full" 
            initial={{ scale: 0 }}
            animate={{ scale: [0.8, 1.2, 0.8] }}
            transition={{ 
              repeat: Infinity, 
              duration: 2
            }}
          />
          {/* Pulsing ring for current stage */}
          <motion.div 
            className="absolute inset-0 rounded-full border-2 border-white"
            initial={{ scale: 0.8, opacity: 0.5 }}
            animate={{ scale: 1.4, opacity: 0 }}
            transition={{ 
              repeat: Infinity, 
              duration: 1.5 
            }}
          />
        </>
      );
    } else {
      return <span className="text-xs font-medium">{index + 1}</span>;
    }
  };

  if (isRiskAccepted) {
    return (
      <motion.div 
        className="mt-1 mb-2"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
      >
        <div className="flex items-center justify-center py-2">
          <motion.span 
            className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg"
            whileHover={{ scale: 1.05, boxShadow: "0 5px 15px rgba(56, 189, 248, 0.3)" }}
            whileTap={{ scale: 0.95 }}
          >
            <motion.svg 
              className="w-4 h-4 mr-2" 
              fill="currentColor" 
              viewBox="0 0 20 20"
              initial={{ rotate: -10 }}
              animate={{ rotate: 0 }}
              transition={{ duration: 0.4, type: "spring" }}
            >
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </motion.svg>
            Risk Accepted
          </motion.span>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div 
      className="mt-2 mb-3 px-8 overflow-hidden w-full relative"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.4 }}
    >
      <div className="flex items-center justify-between mb-2">
        <div className="text-sm text-gray-600">
          Current Stage: 
          <motion.span 
            className="font-medium ml-1 text-indigo-700"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.4 }}
          >
            {lifecycle.currentStageLabel}
          </motion.span>
        </div>
      </div>
      
      <div className="relative flex items-center w-full">
        {/* Glass-morphic backdrop for the entire progress bar */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-50 to-blue-50 rounded-full opacity-30 backdrop-blur-sm"></div>
        
        {lifecycleStages.map((stage, index) => (
          <React.Fragment key={stage.key}>
            {/* Stage indicator */}
            <div className="relative flex flex-col items-center z-10">
              <motion.div
                custom={index}
                initial="initial"
                animate="animate"
                whileHover={expanded ? "hover" : undefined}
                variants={stageVariants}
                className={`flex items-center justify-center w-8 h-8 rounded-full shadow-lg ${
                  stage.isCurrent
                    ? "bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-500 text-white ring-4 ring-indigo-200"
                    : stage.isCompleted || (index === lifecycleStages.length - 1 && isTaskClosed)
                    ? "bg-gradient-to-br from-green-500 to-emerald-600 text-white"
                    : "bg-gray-100 text-gray-500 border border-gray-200 backdrop-blur-sm"
                }`}
                style={{ 
                  boxShadow: stage.isCurrent ? "0 0 15px rgba(99, 102, 241, 0.5)" : ""
                }}
                aria-current={stage.isCurrent ? "step" : undefined}
              >
                {getStageIcon(stage, index)}
                {stage.isCurrent && <ParticlesAnimation />}
                
                {/* Enhanced tooltip on hover */}
                <AnimatePresence>
                  {expanded && (
                    <motion.div
                      className="absolute -bottom-16 z-20 pointer-events-none bg-white text-gray-800 px-3 py-2 rounded-lg shadow-xl min-w-[120px] border border-gray-100"
                      initial={{ opacity: 0, y: -5, scale: 0.95 }}
                      whileHover={{ opacity: 1, y: 0, scale: 1 }}
                      transition={{ duration: 0.2 }}
                      style={{ left: "50%", transform: "translateX(-50%)" }}
                    >
                      <div className="text-sm font-bold">{stage.label}</div>
                      <div className="text-xs text-gray-500 mt-1">
                        {stage.isCurrent && "In progress"}
                        {stage.isCompleted && "Completed"}
                        {!stage.isCurrent && !stage.isCompleted && "Upcoming"}
                      </div>
                      <div className="absolute -top-2 left-1/2 -ml-2 h-2 w-4 bg-white transform rotate-45 border-t border-l border-gray-100"></div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
              
              {/* Stage label */}
              <div className="absolute -bottom-6 text-xs text-center font-medium text-gray-700 whitespace-nowrap min-w-[80px]" 
                   style={{ transform: "translateX(-50%)", left: "50%" }}>
                {stage.label}
              </div>
            </div>
            
            {/* Connector line with gradient */}
            {index < lifecycleStages.length - 1 && (
              <motion.div
                custom={index}
                initial="initial"
                animate="animate"
                variants={lineVariants}
                style={{ originX: 0 }}
                className={`flex-1 h-1 mx-1 rounded-full ${
                  stage.isCompleted && lifecycleStages[index + 1].isCompleted
                    ? "bg-gradient-to-r from-green-500 to-emerald-600" 
                    : stage.isCompleted && lifecycleStages[index + 1].isCurrent
                    ? "bg-gradient-to-r from-green-500 to-blue-500"
                    : stage.isCompleted 
                    ? "bg-gradient-to-r from-green-500 to-gray-200"
                    : "bg-gray-200"
                }`}
              ></motion.div>
            )}
          </React.Fragment>
        ))}
      </div>
      
      <div className="h-12"></div> {/* Spacer for labels */}
    </motion.div>
  );
};

export default BugLifecycleProgress; 