import React from 'react';
import { ReportData } from '../../types/report.types';

interface FindingsSummaryEditorProps {
  reportData: ReportData;
  onTableChange: (field: string, value: number) => void;
}

const FindingsSummaryEditor: React.FC<FindingsSummaryEditorProps> = ({ reportData, onTableChange }) => {
  const severityColors = {
    Critical: 'bg-red-50 border-red-200 text-red-700',
    High: 'bg-orange-50 border-orange-200 text-orange-700',
    Medium: 'bg-yellow-50 border-yellow-200 text-yellow-700',
    Low: 'bg-blue-50 border-blue-200 text-blue-700'
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 px-4 py-3 border-b border-gray-200">
          <h3 className="text-sm font-semibold text-gray-800">Findings Summary</h3>
          <p className="text-xs text-gray-600">Manage the findings summary and severity counts</p>
        </div>
        <div className="p-4">
          {/* Severity Counts */}
          <div className="grid grid-cols-2 gap-4">
            {Object.entries(reportData.open_close_counts_by_severity).map(([severity, counts]) => (
              <div 
                key={severity} 
                className={`rounded-lg p-4 border ${severityColors[severity as keyof typeof severityColors]}`}
              >
                <div className="flex items-center gap-2 mb-3">
                  <div className={`w-2 h-2 rounded-full ${
                    severity === 'Critical' ? 'bg-red-500' :
                    severity === 'High' ? 'bg-orange-500' :
                    severity === 'Medium' ? 'bg-yellow-500' :
                    'bg-blue-500'
                  }`} />
                  <h4 className="font-medium text-sm">{severity}</h4>
                </div>
                <div className="space-y-3">
                  {Object.entries(counts).map(([type, value]) => (
                    <div key={type} className="flex items-center justify-between">
                      <span className="text-sm font-medium min-w-[80px]">{type}:</span>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => onTableChange(`open_close_counts_by_severity.${severity}.${type}`, Math.max(0, value - 1))}
                          className="w-6 h-6 flex items-center justify-center bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200 text-gray-600 hover:text-gray-900"
                        >
                          −
                        </button>
                        <input
                          type="number"
                          value={value}
                          onChange={(e) => onTableChange(`open_close_counts_by_severity.${severity}.${type}`, parseInt(e.target.value) || 0)}
                          className="w-14 border border-gray-300 rounded-md px-2 py-1 text-center text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          min="0"
                        />
                        <button
                          onClick={() => onTableChange(`open_close_counts_by_severity.${severity}.${type}`, value + 1)}
                          className="w-6 h-6 flex items-center justify-center bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200 text-gray-600 hover:text-gray-900"
                        >
                          +
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Total Counts */}
          <div className="mt-6 grid grid-cols-3 gap-4">
            {[
              { 
                label: 'Total Open', 
                value: reportData.total_open, 
                field: 'total_open',
                icon: (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                )
              },
              { 
                label: 'Total Closed', 
                value: reportData.total_closed, 
                field: 'total_closed',
                icon: (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                )
              },
              { 
                label: 'Total Findings', 
                value: reportData.total_findings, 
                field: 'total_findings',
                icon: (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                )
              }
            ].map(({ label, value, field, icon }) => (
              <div key={field} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div className="flex items-center gap-2 mb-3">
                  {icon}
                  <h4 className="font-medium text-sm text-gray-800">{label}</h4>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <button
                    onClick={() => onTableChange(field, Math.max(0, value - 1))}
                    className="w-6 h-6 flex items-center justify-center bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200 text-gray-600 hover:text-gray-900"
                  >
                    −
                  </button>
                  <input
                    type="number"
                    value={value}
                    onChange={(e) => onTableChange(field, parseInt(e.target.value) || 0)}
                    className="w-16 border border-gray-300 rounded-md px-2 py-1 text-center text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    min="0"
                  />
                  <button
                    onClick={() => onTableChange(field, value + 1)}
                    className="w-6 h-6 flex items-center justify-center bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200 text-gray-600 hover:text-gray-900"
                  >
                    +
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* Info Box */}
          <div className="mt-6 bg-blue-50 rounded-lg border border-blue-200 p-4">
            <div className="flex items-start gap-3">
              <svg className="w-5 h-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <p className="text-sm text-blue-800 font-medium mb-1">About Findings Summary</p>
                <p className="text-sm text-blue-700">
                  This section helps you track and manage the number of findings across different severity levels. 
                  The counts are automatically calculated based on your findings in each severity category.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FindingsSummaryEditor; 