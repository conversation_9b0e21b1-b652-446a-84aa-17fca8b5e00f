import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { getProgramReportById, getProgramReportChangeLog } from '../../utils/api/endpoints/program-reports/program-reports';
import axios from '../../utils/api/axios';
import { ReportData } from './types/report.types';
import EditorNavigation from './components/EditorNavigation';
import PreviewSection from './components/PreviewSection';
import CoverPageEditor from './components/editors/CoverPageEditor';
import DocumentReferenceEditor from './components/editors/DocumentReferenceEditor';
import ExecutiveSummaryEditor from './components/editors/ExecutiveSummaryEditor';
import DisclaimerEditor from './components/editors/DisclaimerEditor';
import MethodologyEditor from './components/editors/MethodologyEditor';
import RecommendationsEditor from './components/editors/RecommendationsEditor';
import ConclusionEditor from './components/editors/ConclusionEditor';
import FindingsEditor from './components/editors/FindingsEditor';
import TargetDetailsEditor from './components/editors/TargetDetailsEditor';
import ProjectObjectivesEditor from './components/editors/ProjectObjectivesEditor';
import ScopeEditor from './components/editors/ScopeEditor';
import FindingsSummaryEditor from './components/editors/FindingsSummaryEditor';
import VulnerabilityRatingsEditor from './components/editors/VulnerabilityRatingsEditor';
import CriticalFindingsEditor from './components/editors/CriticalFindingsEditor';
import HighFindingsEditor from './components/editors/HighFindingsEditor';
import MediumFindingsEditor from './components/editors/MediumFindingsEditor';
import LowFindingsEditor from './components/editors/LowFindingsEditor';
import KeyFindingsEditor from './components/editors/KeyFindingsEditor';
import useUserCredentials from '../../utils/hooks/user/useUserCredentials';
import { UserRole } from '../../utils/api/endpoints/user/credentials';
import ChangeLogSidebar from './components/ChangeLogSidebar';
import ReportTemplate from './components/ReportTemplate';
import PDFDownloader from './components/PDFDownloader';
import { useReactToPrint } from 'react-to-print';
import './PDFEditor.css';
import './report-print.css';
// Import custom-scrollbar class globally for chat scroll UX
// If not present, add the .custom-scrollbar CSS to your global stylesheet (e.g., index.css or App.scss)
import PreviewOnly from './PreviewOnly';
import { useRoleString } from './hooks/useRoleString';
import EditorLayout from './EditorLayout';
import { usePDFEditorLogic } from './hooks/usePDFEditorLogic';
import ChatSection from './components/ChatSection';
import ChatModal from './components/ChatModal';
import ConfirmationModal from '../../components/common/ConfirmationModal';

// Constants
const PREVIEW_UPDATE_DELAY = 500;
const SEVERITY_CATEGORIES = ['Critical', 'High', 'Medium', 'Low'] as const;
const STATUS_TYPES = ['Open', 'Closed', 'Total'] as const;

// Types
interface SeverityCounts {
  Open: number;
  Closed: number;
  Total: number;
}

interface OpenCloseCounts {
  Critical: SeverityCounts;
  High: SeverityCounts;
  Medium: SeverityCounts;
  Low: SeverityCounts;
}

type SeverityCategory = typeof SEVERITY_CATEGORIES[number];
type StatusType = typeof STATUS_TYPES[number];

// Utility functions
const normalizeSeverityCategory = (category: string): SeverityCategory => {
  const normalized = category.charAt(0).toUpperCase() + category.slice(1).toLowerCase();
  return normalized as SeverityCategory;
};

const getDefaultDisclaimer = (companyName: string) => `
  <p>
    Capture the bug Ltd. has prepared this document exclusively for ${companyName}.
    Copying, or modification of this document is strictly prohibited without Capture the bug Ltd.'s written consent, 
    except for specific purposes when such permission is granted. This document is confidential and proprietary material 
    of Capture the bug Ltd. and must always be treated as such, not to be disclosed to third parties without prior consent.
  </p>
  
  <p>
    The information within is provided 'as-is,' without any warranty. Vulnerability assessments offer a snapshot of 
    security at a specific moment and are not exhaustive risk assessments. New vulnerabilities may emerge post-assessment. 
    Thus, this report is a guide, not a definitive risk analysis.
  </p>
  
  <p>
    Capture the bug Ltd. assumes no liability for any changes, omissions, or errors in this document. 
    Capture the bug Ltd. shall not be liable for any damages, financial or otherwise, arising out of the use or misuse 
    of this report by any current employee of ${companyName} or any member of the general public.
  </p>
`;

const getDefaultOpenCloseCounts = (): OpenCloseCounts => ({
  Critical: { Open: 0, Closed: 0, Total: 0 },
  High: { Open: 0, Closed: 0, Total: 0 },
  Medium: { Open: 0, Closed: 0, Total: 0 },
  Low: { Open: 0, Closed: 0, Total: 0 }
});

// Notification component
interface NotificationProps {
  type: 'success' | 'error' | 'info';
  title: string;
  message: string;
  isVisible: boolean;
  onClose: () => void;
}

const Notification: React.FC<NotificationProps> = ({ type, title, message, isVisible, onClose }) => {
  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => {
        onClose();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [isVisible, onClose]);

  if (!isVisible) return null;

  const getIcon = () => {
    switch (type) {
      case 'success':
        return (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      case 'info':
        return (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  const getStyles = () => {
    switch (type) {
      case 'success':
        return 'bg-gradient-to-r from-emerald-50 to-green-50 border-emerald-200 text-emerald-800';
      case 'error':
        return 'bg-gradient-to-r from-red-50 to-pink-50 border-red-200 text-red-800';
      case 'info':
        return 'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 text-blue-800';
    }
  };

  return (
    <div className="fixed top-4 right-4 z-50 animate-slide-in">
      <div className={`max-w-sm w-full rounded-xl border shadow-2xl backdrop-blur-sm ${getStyles()}`}>
        <div className="p-4">
          <div className="flex items-start">
            <div className={`flex-shrink-0 p-2 rounded-lg ${
              type === 'success' ? 'bg-emerald-100 text-emerald-600' :
              type === 'error' ? 'bg-red-100 text-red-600' :
              'bg-blue-100 text-blue-600'
            }`}>
              {getIcon()}
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-semibold">{title}</h3>
              <p className="mt-1 text-sm opacity-90">{message}</p>
            </div>
            <div className="ml-4 flex-shrink-0">
              <button
                onClick={onClose}
                className={`inline-flex rounded-lg p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                  type === 'success' ? 'hover:bg-emerald-200 focus:ring-emerald-500' :
                  type === 'error' ? 'hover:bg-red-200 focus:ring-red-500' :
                  'hover:bg-blue-200 focus:ring-blue-500'
                }`}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Full View Modal component
interface FullViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  previewHtml: string;
  previewLoading: boolean;
}

const FullViewModal: React.FC<FullViewModalProps> = ({ isOpen, onClose, previewHtml, previewLoading }) => {
  const [zoom, setZoom] = useState(0.9); // 0.7 = 70%
  const handleZoomIn = () => setZoom(z => Math.min(z + 0.1, 2));
  const handleZoomOut = () => setZoom(z => Math.max(z - 0.1, 0.5));

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-75 transition-opacity"
        onClick={onClose}
      />
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-7xl h-[90vh] bg-white rounded-2xl shadow-2xl overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-600 rounded-lg">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </div>
              <h2 className="text-xl font-bold text-gray-900">Full Preview</h2>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={handleZoomOut}
                title="Zoom Out"
                className="flex items-center justify-center p-2 rounded-lg bg-blue-100 hover:bg-blue-200 transition-colors shadow-sm"
              >
                <svg className="w-5 h-5 text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                </svg>
              </button>
              <span className="text-xs text-blue-900 font-semibold px-2 select-none">{Math.round(zoom * 100)}%</span>
              <button
                onClick={handleZoomIn}
                title="Zoom In"
                className="flex items-center justify-center p-2 rounded-lg bg-blue-100 hover:bg-blue-200 transition-colors shadow-sm"
              >
                <svg className="w-5 h-5 text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </button>
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
          {/* Content */}
          <div className="h-full overflow-y-auto bg-gray-50">
            {previewLoading ? (
              <div className="flex items-center justify-center h-full">
                <div className="flex flex-col items-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-600"></div>
                  <p className="mt-4 text-gray-600">Loading preview...</p>
                </div>
              </div>
            ) : (
              <div className="max-w-5xl mx-auto p-8 flex flex-col items-center">
                <div
                  className="bg-white rounded-lg shadow-lg p-8"
                  style={{
                    transform: `scale(${zoom})`,
                    transformOrigin: 'top center',
                    transition: 'transform 0.2s',
                    minWidth: '220mm',
                    width: 'auto',
                    display: 'inline-block'
                  }}
                  dangerouslySetInnerHTML={{ __html: previewHtml }}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Add this mapping at the top of the file (after imports)
const ROLE_MAP: Record<number, string> = {
  1: 'HACKER',
  2: 'BUSINESS',
  3: 'ADMIN',
  4: 'QA',
  5: 'SUB_ADMIN',
  6: 'ADMIN_MANAGER',
};

const MIN_EDITOR_WIDTH = 320; // px
const MAX_EDITOR_WIDTH = 700; // px
const DEFAULT_EDITOR_WIDTH = '50%'; // 50% of the container

// Component
const PDFEditor: React.FC = () => {
  const logic = usePDFEditorLogic();
  const {
    report_id,
    role,
    loading,
    previewLoading,
    previewHtml,
    reportData,
    activeSection,
    setActiveSection,
    sectionData,
    saveLoading,
    approveLoading,
    rejectLoading,
    notification,
    setNotification,
    isFullViewOpen,
    setIsFullViewOpen,
    isChangeLogOpen,
    setIsChangeLogOpen,
    changeLog,
    setChangeLog,
    changeLogLoading,
    setChangeLogLoading,
    previewMode,
    setPreviewMode,
    editorWidth,
    setEditorWidth,
    dragging,
    viewMode,
    setViewMode,
    previewRef,
    handlePrint,
    showNotification,
    hideNotification,
    openFullView,
    closeFullView,
    openChangeLog,
    closeChangeLog,
    updatePreview,
    debouncedUpdatePreview,
    handleDataUpdate,
    fetchReportData,
    updateReportStatus,
    handleSave,
    handleApprove,
    handleReject,
    canApprove,
    canReject,
    handleInputChange,
    handleHtmlChange,
    handleDataChange,
    handleFindingChange,
    handleAddFinding,
    handleRemoveFinding,
    handleTargetDetailsChange,
    handleAddTarget,
    handleRemoveTarget,
    handleTableChange,
    handleKeyFindingChange,
    handleAddKeyFinding,
    handleRemoveKeyFinding,
    handleRecommendationsChange,
    saveSectionChanges,
    currentData,
  } = logic;

  const roleStr = useRoleString(role);

  const [selectedChat, setSelectedChat] = useState<'QA' | 'BUSINESS' | 'ADMIN_SUBADMIN'>('QA');
  const [adminQaMessages, setAdminQaMessages] = useState<{ sender: string; text: string; timestamp: string }[]>([]);
  const [adminBusinessMessages, setAdminBusinessMessages] = useState<{ sender: string; text: string; timestamp: string }[]>([]);

  // Determine label for Admin/Sub Admin tab
  let adminSubAdminLabel = 'Admin/Sub Admin';
  if (roleStr.toUpperCase() === 'ADMIN') {
    adminSubAdminLabel = 'Sub Admin';
  } else if (roleStr.toUpperCase() === 'SUB_ADMIN') {
    adminSubAdminLabel = 'Admin';
  }

  const handleSendAdminQa = (msg: string) => {
    setAdminQaMessages(prev => [...prev, { sender: roleStr, text: msg, timestamp: new Date().toISOString() }]);
  };
  const handleSendAdminBusiness = (msg: string) => {
    setAdminBusinessMessages(prev => [...prev, { sender: roleStr, text: msg, timestamp: new Date().toISOString() }]);
  };

  const [chatOpen, setChatOpen] = useState(false);

  // Get currentUserId from user credentials
  const { id: currentUserId } = useUserCredentials();

  // For BUSINESS users, pass automated_report_id to ChatSection
  let chatId = report_id || '';
  if (roleStr.toUpperCase() === 'BUSINESS' && reportData && reportData.automated_report_id) {
    chatId = reportData.automated_report_id;
    if (report_id && report_id === chatId) {
      // eslint-disable-next-line no-console
      console.warn('BUSINESS user: programReportId should be automated_report.id, not program_report.id');
    }
  }

  // Chat modal/button for all roles
  const chatSection = (
    <>
      {/* Floating chat button */}
      <button
        className="fixed bottom-6 right-6 z-50 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg w-16 h-16 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-blue-400"
        onClick={() => setChatOpen(open => !open)}
        aria-label={chatOpen ? "Close chat" : "Open chat"}
      >
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M21 12c0 4.418-4.03 8-9 8a9.77 9.77 0 01-4-.8l-4 1 1-3.2A7.96 7.96 0 013 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      </button>
      {/* Chat modal */}
      <ChatModal
        isOpen={chatOpen}
        onClose={() => setChatOpen(false)}
        header={
          <div className="flex items-center justify-between px-5 py-3 rounded-t-3xl bg-gradient-to-r from-blue-700 via-blue-500 to-blue-400 text-white shadow-md">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center shadow-md">
                <svg className="w-7 h-7 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M21 12c0 4.418-4.03 8-9 8a9.77 9.77 0 01-4-.8l-4 1 1-3.2A7.96 7.96 0 013 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <div>
                <div className="font-bold text-lg leading-tight">Pentest Report Thread</div>
                <div className="flex items-center gap-1 text-xs text-blue-100">
                  <span className="inline-block w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                  Online
                </div>
              </div>
            </div>
            <button
              className="p-2 rounded-full hover:bg-blue-500 text-white transition"
              onClick={() => setChatOpen(false)}
              aria-label="Close chat"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        }
        tabs={['ADMIN', 'SUB_ADMIN'].includes(roleStr.toUpperCase()) && (
          <div className="sticky top-0 z-10 bg-white mb-3 flex justify-center">
            <div className="inline-flex bg-gray-100 rounded-full p-1 shadow-sm gap-1">
              <button
                className={`px-4 py-1.5 rounded-full font-semibold text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400
                  ${selectedChat === 'QA'
                    ? 'bg-gradient-to-r from-blue-600 to-blue-500 text-white shadow-md'
                    : 'bg-transparent text-blue-700 hover:bg-blue-100'}
                `}
                onClick={() => setSelectedChat('QA')}
                tabIndex={0}
              >
                QA
              </button>
              <button
                className={`px-4 py-1.5 rounded-full font-semibold text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400
                  ${selectedChat === 'BUSINESS'
                    ? 'bg-gradient-to-r from-green-500 to-teal-400 text-white shadow-md'
                    : 'bg-transparent text-green-700 hover:bg-green-100'}
                `}
                onClick={() => setSelectedChat('BUSINESS')}
                tabIndex={0}
              >
                Business
              </button>
              <button
                className={`px-4 py-1.5 rounded-full font-semibold text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400
                  ${selectedChat === 'ADMIN_SUBADMIN'
                    ? 'bg-gradient-to-r from-purple-600 to-violet-500 text-white shadow-md'
                    : 'bg-transparent text-purple-700 hover:bg-purple-100'}
                `}
                onClick={() => setSelectedChat('ADMIN_SUBADMIN')}
                tabIndex={0}
              >
                {adminSubAdminLabel}
              </button>
            </div>
          </div>
        )}
      >
        {['ADMIN', 'SUB_ADMIN'].includes(roleStr.toUpperCase()) ? (
          selectedChat === 'QA' ? (
            <ChatSection
              programReportId={chatId}
              chatType="admin_qa"
            />
          ) : selectedChat === 'BUSINESS' ? (
            <ChatSection
              programReportId={chatId}
              chatType="admin_business"
            />
          ) : (
            <ChatSection
              programReportId={chatId}
              chatType="admin_subadmin"
            />
          )
        ) : roleStr.toUpperCase() === 'QA' ? (
          <ChatSection
            programReportId={chatId}
            chatType="admin_qa"
          />
        ) : (
          <ChatSection
            programReportId={chatId}
            chatType="admin_business"
          />
        )}
      </ChatModal>
    </>
  );

  // Confirmation modal state
  const [approveModalOpen, setApproveModalOpen] = useState(false);
  const [rejectModalOpen, setRejectModalOpen] = useState(false);
  const handleApproveWithConfirm = () => setApproveModalOpen(true);
  const handleRejectWithConfirm = () => setRejectModalOpen(true);
  const confirmApprove = () => {
    setApproveModalOpen(false);
    handleApprove();
  };
  const confirmReject = () => {
    setRejectModalOpen(false);
    handleReject();
  };

  // Move all useState hooks to the top level, before any conditionals
  const [businessActionLoading, setBusinessActionLoading] = useState(false);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-600"></div>
          <p className="mt-4 text-gray-700 text-lg font-medium">Loading report data...</p>
        </div>
      </div>
    );
  }

  if (!reportData) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="bg-white p-8 rounded-xl shadow-xl text-center max-w-md">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-4">No Report Data Available</h2>
          <p className="text-gray-600">Please check if the report ID is valid or try again later.</p>
        </div>
      </div>
    );
  }

  if (roleStr.toUpperCase() === 'BUSINESS') {
    // Business action handlers
    const handleBusinessApprove = async () => {
      setBusinessActionLoading(true);
      try {
        await updateReportStatus('business_approved');
        showNotification('success', 'Report Approved', 'You have approved the report.');
        await fetchReportData();
      } catch (e) {
        showNotification('error', 'Error', 'Failed to approve report.');
      } finally {
        setBusinessActionLoading(false);
      }
    };
    const handleBusinessRequestChanges = async () => {
      setBusinessActionLoading(true);
      try {
        await updateReportStatus('business_requested_changes');
        showNotification('info', 'Requested Changes', 'You have requested changes.');
        await fetchReportData();
      } catch (e) {
        showNotification('error', 'Error', 'Failed to request changes.');
      } finally {
        setBusinessActionLoading(false);
      }
    };
    return (
      <>
        {chatSection}
        <PreviewOnly
          reportData={reportData}
          currentData={currentData}
          openFullView={openFullView}
          previewMode={previewMode || 'full'}
          setPreviewMode={mode => setPreviewMode(mode as 'full' | 'technical')}
          previewHtml={previewHtml}
          previewRef={previewRef}
          isFullViewOpen={isFullViewOpen}
          closeFullView={closeFullView}
          notification={notification}
          hideNotification={hideNotification}
          isChangeLogOpen={isChangeLogOpen}
          closeChangeLog={closeChangeLog}
          changeLog={changeLog}
          changeLogLoading={changeLogLoading}
          previewLoading={previewLoading}
          onBusinessApprove={handleBusinessApprove}
          onBusinessRequestChanges={handleBusinessRequestChanges}
          businessActionLoading={businessActionLoading}
          isBusinessUser={true}
        />
      </>
    );
  }

  return (
    <>
      <EditorLayout
        currentData={currentData}
        reportData={reportData}
        editorWidth={editorWidth}
        dragging={dragging}
        setEditorWidth={setEditorWidth}
        activeSection={activeSection}
        setActiveSection={setActiveSection}
        openChangeLog={openChangeLog}
        handleSave={handleSave}
        saveLoading={saveLoading}
        approveLoading={approveLoading}
        rejectLoading={rejectLoading}
        canApprove={canApprove}
        handleApprove={handleApproveWithConfirm}
        canReject={canReject}
        handleReject={handleRejectWithConfirm}
        reportDataStatus={reportData.status}
        handleInputChange={handleInputChange}
        handleHtmlChange={handleHtmlChange}
        handleDataChange={handleDataChange}
        handleFindingChange={handleFindingChange}
        handleRemoveFinding={handleRemoveFinding}
        handleAddFinding={handleAddFinding}
        handleTargetDetailsChange={handleTargetDetailsChange}
        handleAddTarget={handleAddTarget}
        handleRemoveTarget={handleRemoveTarget}
        handleTableChange={handleTableChange}
        handleKeyFindingChange={handleKeyFindingChange}
        handleAddKeyFinding={handleAddKeyFinding}
        handleRemoveKeyFinding={handleRemoveKeyFinding}
        handleRecommendationsChange={handleRecommendationsChange}
        saveSectionChanges={saveSectionChanges}
        previewRef={previewRef}
        openFullView={openFullView}
        previewMode={previewMode || 'full'}
        setPreviewMode={mode => setPreviewMode(mode as 'full' | 'technical')}
        previewHtml={previewHtml}
        isFullViewOpen={isFullViewOpen}
        closeFullView={closeFullView}
        notification={notification}
        hideNotification={hideNotification}
        isChangeLogOpen={isChangeLogOpen}
        closeChangeLog={closeChangeLog}
        changeLog={changeLog}
        changeLogLoading={changeLogLoading}
        previewLoading={previewLoading}
        sectionData={sectionData}
      >
        {['ADMIN', 'QA', 'BUSINESS', 'SUB_ADMIN'].includes(roleStr.toUpperCase()) && chatSection}
      </EditorLayout>
      <ConfirmationModal
        isOpen={approveModalOpen}
        onClose={() => setApproveModalOpen(false)}
        onConfirm={confirmApprove}
        title="Approve Report?"
        description="Are you sure you want to approve this report? This action cannot be undone."
        confirmText="Approve"
        cancelText="Cancel"
      />
      <ConfirmationModal
        isOpen={rejectModalOpen}
        onClose={() => setRejectModalOpen(false)}
        onConfirm={confirmReject}
        title="Reject Report?"
        description="Are you sure you want to reject this report? This will send it back for editing."
        confirmText="Reject"
        cancelText="Cancel"
      />
    </>
  );
};

export default PDFEditor;