import { useState, useEffect, useRef } from "react";
import { Link, useNavigate } from "react-router-dom";
import { CTBReport } from "../../../utils/api/endpoints/reports/parseReports";
import useProgram from "../../../utils/hooks/programs/useProgram";
import ReportStatusPill from "../ReportStatusPill";
import VulnerabilityFlag, {
  getVulnerabilityColour
} from "../VulnerabilityFlag";
import { CgProfile } from "react-icons/cg";
import FilterSectionReports from "../utils/FIlterSectionReports";
import { FilterOptions } from "../../../utils/api/endpoints/reports/reports";
import useUserCredentials from "../../../utils/hooks/user/useUserCredentials";
import { UserRole } from "../../../utils/api/endpoints/user/credentials";
import Pagination from "../../retests/utils/Pagination";
import ConfirmationModal from "../../common/ConfirmationModal";
import { REPORT_STATUS_INFO } from "../../../utils/constants/reportStatusInfo";
import { useUpdateReportStateMutation } from "../../../utils/api/endpoints/reportsApi";
import { ReportState } from "../../../utils/api/endpoints/reports/reports";
import { ReportRejectReason } from "../../../utils/api/endpoints/reports/parseReports";
import toast from "react-hot-toast";
import { FiChevronDown } from "react-icons/fi";
import { createPortal } from "react-dom";
import { retestRequest } from "../../../utils/api/endpoints/retests/retests";

// Types
type ReportStateType =
  | "approved"
  | "rejected"
  | "closed"
  | "request info"
  | "acceptRisk"
  | "nonActionableIssue"
  | "requestFix"
  | "out of scope"
  | "QA Modified";

type ActionKeyType =
  | "approved"
  | "rejected"
  | "closed"
  | "request_info"
  | "acceptRisk"
  | "nonActionableIssue"
  | "requestFix"
  | "retest_requested";

type ColumnVisibility = {
  createdAt: boolean;
  pentester: boolean;
  action: boolean;
};

// Global variable to track active dropdown
let activeDropdownId: number | null = null;

const ReportTable = ({
  reports,
  filterOptions,
  filters,
  onFilter,
  onClick,
  count,
  currentPage,
  onPageChange
}: {
  reports: CTBReport[];
  filterOptions: FilterOptions;
  filters: {
    searchQuery: string;
    program: string;
    reportState: string;
    severityCategory: string;
    vulnerabilityType: string;
    sortOrder: string;
    pentesterUsername: string;
  };
  onFilter: (filters: {
    searchQuery: string;
    program: string;
    reportState: string;
    severityCategory: string;
    vulnerabilityType: string;
    sortOrder: string;
    pentesterUsername: string;
  }) => void;
  onClick: (report: CTBReport) => void;
  count: number;
  currentPage: number;
  onPageChange: (page: number) => void;
}) => {
  const navigate = useNavigate();
  const { role } = useUserCredentials();
  const pageSize = 7;
  const [modalConfig, setModalConfig] = useState({
    isOpen: false,
    title: "",
    description: "",
    onConfirm: () => {},
    info: undefined as string | undefined
  });
  const [updateReportState] = useUpdateReportStateMutation();
  const [columnVisibility, setColumnVisibility] = useState<ColumnVisibility>(
    () => {
      if (role === UserRole.RESEARCHER) {
        return {
          createdAt: true,
          pentester: false,
          action: false
        };
      }
      return {
        createdAt: false,
        pentester: false,
        action: true
      };
    }
  );

  // Update column visibility when role changes
  useEffect(() => {
    if (role === UserRole.RESEARCHER) {
      setColumnVisibility({
        createdAt: true,
        pentester: false,
        action: false
      });
    }
  }, [role]);

  const handleReportClick = (report: CTBReport) => {
    localStorage.setItem("lastViewedReportId", report.id.toString());
    onClick(report);
  };

  const totalPages = Math.ceil(count / pageSize);

  // Helper to show confirmation modal
  const showConfirmation = (
    title: string,
    description: string,
    onConfirm: () => void,
    info?: string
  ) => {
    setModalConfig({
      isOpen: true,
      title,
      description,
      onConfirm,
      info
    });
  };

  // Helper to close modal
  const closeModal = () => {
    setModalConfig(prev => ({ ...prev, isOpen: false }));
  };

  // Helper to get available actions for a report based on role/state
  const getAvailableActions = (
    report: CTBReport,
    role: UserRole
  ): { key: ActionKeyType; label: string; isRetestRequest?: boolean }[] => {
    const actions: {
      key: ActionKeyType;
      label: string;
      isRetestRequest?: boolean;
    }[] = [];

    if (
      [
        UserRole.ADMIN,
        UserRole.SUB_ADMIN,
        UserRole.ADMIN_MANAGER,
        UserRole.QA
      ].includes(role)
    ) {
      if (
        report.triageStatus === "underReview" ||
        report.state === ReportState.TRIAGE_REJECTED ||
        report.state === ReportState.TRIAGE_APPROVED
      ) {
        actions.push({ key: "approved", label: "Approve" });
        actions.push({ key: "rejected", label: "Reject" });
      }
    }

    if (
      [
        UserRole.BUSINESS,
        UserRole.DEVELOPER,
        UserRole.BUSINESS_MANAGER,
        UserRole.BUSINESS_ADMINISTRATOR
      ].includes(role)
    ) {
      if (
        report.state === ReportState.TRIAGE_APPROVED ||
        report.state === ReportState.BUSINESS_REVIEW
      ) {
        actions.push({ key: "approved", label: "Approve" });
        actions.push({ key: "request_info", label: "Request More Info" });
        actions.push({ key: "acceptRisk", label: "Accept Risk" });
        actions.push({
          key: "nonActionableIssue",
          label: "Non-Actionable Issue"
        });
      }
      if (
        report.state &&
        [
          ReportState.TRIAGE_APPROVED,
          ReportState.FIX_APPROVED,
          ReportState.FIX_VERIFIED
        ].includes(report.state)
      ) {
        actions.push({ key: "closed", label: "Close" });
      }
      if (report.state === ReportState.AWAITING_FIX) {
        actions.push({
          key: "retest_requested",
          label: "Request a retest",
          isRetestRequest: true
        });
      }
      if (report.state === ReportState.BUSINESS_REQUEST_INFO) {
        actions.push({ key: "approved", label: "Approve" });
        actions.push({ key: "acceptRisk", label: "Accept Risk" });
        actions.push({
          key: "nonActionableIssue",
          label: "Non-Actionable Issue"
        });
      }
      if (report.state === ReportState.BUSINESS_APPROVED) {
        actions.push({ key: "requestFix", label: "Fixing the Vulnerability" });
      }
      if (report.state === ReportState.QA_MODIFIED) {
        actions.push({ key: "approved", label: "Approve" });
        actions.push({ key: "acceptRisk", label: "Accept Risk" });
        actions.push({
          key: "nonActionableIssue",
          label: "Non-Actionable Issue"
        });
      }
      if (report.state === ReportState.BUSINESS_NON_ACTIONABLE_ISSUE) {
        actions.push({ key: "request_info", label: "Request More Info" });
      }
    }
    return actions;
  };

  // Handler for status change
  const handleStatusChange = (
    report: CTBReport,
    actionKey: ActionKeyType,
    isRetestRequest?: boolean
  ) => {
    if (isRetestRequest) {
      showConfirmation(
        "Request Retest",
        "Are you sure you want to request a retest for this report?",
        async () => {
          try {
            await retestRequest(report.id);
            toast.success("Retest request submitted successfully");
          } catch (err) {
            toast.error("Failed to submit retest request. Please try again.");
          }
        }
      );
      return;
    }

    let state: ReportStateType | undefined;
    let rejectReason: ReportRejectReason | undefined;

    switch (actionKey) {
      case "approved":
        state = "approved";
        break;
      case "rejected":
        state = "rejected";
        rejectReason = ReportRejectReason.INFORMATIVE;
        break;
      case "closed":
        state = "closed";
        rejectReason = ReportRejectReason.INFORMATIVE;
        break;
      case "request_info":
        state = "request info";
        break;
      case "acceptRisk":
        state = "acceptRisk";
        break;
      case "nonActionableIssue":
        state = "nonActionableIssue";
        break;
      case "requestFix":
        state = "requestFix";
        break;
      default:
        return;
    }

    if (!state) return;

    const infoKey =
      actionKey === "approved"
        ? "APPROVE"
        : actionKey === "rejected"
        ? "REJECT"
        : actionKey === "closed"
        ? "CLOSE"
        : actionKey === "request_info"
        ? "REQUEST_INFO"
        : actionKey === "acceptRisk"
        ? "ACCEPT_RISK"
        : actionKey === "nonActionableIssue"
        ? "NON_ACTIONABLE"
        : actionKey === "requestFix"
        ? "REQUEST_FIX"
        : null;

    if (infoKey && REPORT_STATUS_INFO[infoKey]) {
      showConfirmation(
        REPORT_STATUS_INFO[infoKey].title,
        REPORT_STATUS_INFO[infoKey].description,
        async () => {
          try {
            await updateReportState({
              id: report.id,
              state: state as ReportStateType,
              rejectReason
            });
            toast.success("Report status updated");
          } catch (e) {
            toast.error("Failed to update report status");
          }
        },
        REPORT_STATUS_INFO[infoKey].info
      );
    }
  };

  return (
    <div className="min-h-[500px] bg-white">
      <FilterSectionReports
        reports={reports}
        programNames={filterOptions.programNames}
        vulnerabilityTypes={filterOptions.vulnerabilityTypes}
        reportStates={filterOptions.reportStates}
        severityCategories={filterOptions.severityCategories}
        pentesters={filterOptions.pentesters}
        filters={filters}
        onFilter={onFilter}
        columnVisibility={columnVisibility}
        onColumnVisibilityChange={setColumnVisibility}
      />
      {reports.length === 0 ? (
        <div className="mt-8 flex flex-col items-center justify-center p-8 text-center">
          <div className="mb-4 rounded-full bg-gray-100 p-4">
            <svg
              className="h-8 w-8 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900">
            No reports found
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your filters or search criteria
          </p>
        </div>
      ) : (
        <div className="mt-2 px-3">
          <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-300 hover:shadow-md">
            <div className="overflow-x-auto">
              <table className="w-full table-fixed border-collapse">
                <thead>
                  <tr className="bg-gray-200">
                    <th className="w-[30%] px-4 py-3.5 text-left text-xs font-semibold uppercase tracking-wider text-gray-700">
                      Report Name
                    </th>
                    <th className="w-[20%] px-4 py-3.5 text-left text-xs font-semibold uppercase tracking-wider text-gray-700">
                      Program Name
                    </th>
                    <th className="w-[12%] px-4 py-3.5 text-left text-xs font-semibold uppercase tracking-wider text-gray-700">
                      Severity
                    </th>
                    <th className="w-[20%] px-4 py-3.5 text-left text-xs font-semibold uppercase tracking-wider text-gray-700">
                      Status
                    </th>
                    {columnVisibility.createdAt && (
                      <th className="w-[10%] px-4 py-3.5 text-left text-xs font-semibold uppercase tracking-wider text-gray-700">
                        Created At
                      </th>
                    )}
                    {role !== UserRole.RESEARCHER &&
                      columnVisibility.pentester && (
                        <th className="w-[8%] px-4 py-3.5 text-center text-xs font-semibold uppercase tracking-wider text-gray-700">
                          Pentester
                        </th>
                      )}
                    {role !== UserRole.RESEARCHER &&
                      columnVisibility.action && (
                        <th className="w-[10%] px-4 py-3.5 text-center text-xs font-semibold uppercase tracking-wider text-gray-700">
                          Actions
                        </th>
                      )}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {reports.map((report, index) => (
                    <ReportTableRow
                      key={report.id}
                      report={report}
                      onClick={handleReportClick}
                      actions={getAvailableActions(
                        report,
                        role ?? UserRole.RESEARCHER
                      )}
                      onAction={(actionKey, isRetestRequest) =>
                        handleStatusChange(report, actionKey, isRetestRequest)
                      }
                      columnVisibility={columnVisibility}
                      index={index}
                    />
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          <div className="py-4">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={onPageChange}
            />
          </div>
          <ConfirmationModal
            isOpen={modalConfig.isOpen}
            onClose={closeModal}
            onConfirm={modalConfig.onConfirm}
            title={modalConfig.title}
            description={modalConfig.description}
            info={modalConfig.info}
          />
        </div>
      )}
    </div>
  );
};

const ReportTableRow = ({
  report,
  onClick,
  actions = [],
  onAction,
  columnVisibility,
  index
}: {
  report: CTBReport;
  onClick: (report: CTBReport) => void;
  actions?: { key: ActionKeyType; label: string; isRetestRequest?: boolean }[];
  onAction?: (actionKey: ActionKeyType, isRetestRequest?: boolean) => void;
  columnVisibility: ColumnVisibility;
  index: number;
}) => {
  const { program } = useProgram(report.programId);
  const { role } = useUserCredentials();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Update dropdown position
  const updateDropdownPosition = () => {
    if (buttonRef.current) {
      const buttonRect = buttonRef.current.getBoundingClientRect();
      const dropdownHeight = 60;
      const spaceBelow = window.innerHeight - buttonRect.bottom;

      setDropdownPosition({
        top:
          spaceBelow < dropdownHeight
            ? buttonRect.top - dropdownHeight - 8
            : buttonRect.bottom + 8,
        left: buttonRect.left - 192 + buttonRect.width
      });
    }
  };

  // Update position when dropdown opens
  useEffect(() => {
    if (dropdownOpen) {
      updateDropdownPosition();
      window.addEventListener("scroll", updateDropdownPosition, true);
      window.addEventListener("resize", updateDropdownPosition);
      return () => {
        window.removeEventListener("scroll", updateDropdownPosition, true);
        window.removeEventListener("resize", updateDropdownPosition);
      };
    }
  }, [dropdownOpen]);

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
        if (activeDropdownId === report.id) {
          activeDropdownId = null;
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [report.id]);

  // Handle dropdown state
  const toggleDropdown = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (activeDropdownId !== report.id) {
      if (activeDropdownId !== null) {
        const event = new MouseEvent("mousedown", {
          bubbles: true,
          cancelable: true,
          view: window
        });
        document.dispatchEvent(event);
      }
      activeDropdownId = report.id;
    }
    setDropdownOpen(!dropdownOpen);
  };

  return (
    <tr
      id={`report-${report.id}`}
      onClick={() => onClick(report)}
      className="group cursor-pointer transition-all duration-300 hover:bg-gray-50"
      style={{
        animation: `fadeInUp 0.5s ease-out ${index * 0.05}s both`,
        opacity: 0,
        transform: "translateY(10px)"
      }}
    >
      <td className="px-4 py-4">
        <div className="flex flex-col gap-2">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-50 text-blue-600 transition-all duration-300 group-hover:scale-110 group-hover:bg-blue-100">
                <svg
                  className="h-5 w-5 transition-transform duration-300 group-hover:scale-110"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
            </div>
            <div className="min-w-0 flex-1 items-center justify-center">
              <div
                className="line-clamp-1 text-sm font-semibold text-gray-900 transition-all duration-300 group-hover:text-blue-600"
                title={report.title}
              >
                {report.title}
              </div>
              {report.description && (
                <div className="relative mt-1">
                  <div
                    className="line-clamp-2 text-xs font-medium leading-relaxed text-gray-700 transition-all duration-300 group-hover:text-gray-600"
                    title={report.description
                      .replace(/<\/?[^>]+(>|$)/g, "")
                      .trim()}
                  >
                    {report.description
                      .replace(/<\/?[^>]+(>|$)/g, "")
                      .trim()}
                  </div>
                  <div className="absolute bottom-0 right-0 h-6 w-12 bg-gradient-to-l from-white to-transparent group-hover:from-gray-50" />
                </div>
              )}
            </div>
          </div>
        </div>
      </td>
      <td className="px-4 py-4">
        {program && (
          <Link
            to={`/dashboard/programs/${program.id}`}
            className="line-clamp-1 text-sm font-medium text-gray-700 transition-all duration-300 hover:text-blue-600 hover:underline"
            title={program.title}
            onClick={e => e.stopPropagation()}
          >
            {program.title}
          </Link>
        )}
      </td>
      <td className="px-4 py-4">
        {report.severity && (
          <div className="flex justify-start">
            <VulnerabilityFlag
              label={report.severity.category}
              className={`text-${getVulnerabilityColour(
                report.severity
              )} inline-flex items-center rounded-md border px-2.5 py-1 text-xs font-medium shadow-sm transition-all duration-300 group-hover:scale-105`}
              flagClassName={`fill-${getVulnerabilityColour(report.severity)}`}
            />
          </div>
        )}
      </td>
      <td className="px-4 py-4">
        <div className="flex items-center gap-2">
          <ReportStatusPill
            report={report}
            className="px-2.5 py-1 text-xs font-medium transition-all duration-300 group-hover:scale-105"
          />
        </div>
      </td>
      {columnVisibility.createdAt && (
        <td className="px-4 py-4">
          {report.submittedDate !== undefined && (
            <p className="text-xs font-medium text-gray-600 transition-all duration-300 group-hover:text-gray-900">
              {report.submittedDate}
            </p>
          )}
        </td>
      )}
      {role !== UserRole.RESEARCHER && columnVisibility.pentester && (
        <td className="px-4 py-4">
          {report.creator && (
            <div className="flex justify-center">
              {report.creator.pfp ? (
                <img
                  src={report.creator.pfp}
                  alt={report.creator.displayName}
                  title={report.creator.displayName}
                  className="h-8 w-8 rounded-full border border-gray-200 object-cover shadow-sm transition-all duration-300 group-hover:scale-110 group-hover:border-blue-200"
                />
              ) : (
                <CgProfile
                  size={32}
                  className="text-gray-400 transition-all duration-300 group-hover:scale-110 group-hover:text-gray-600"
                  title={report.creator.displayName}
                />
              )}
            </div>
          )}
        </td>
      )}
      {role !== UserRole.RESEARCHER && columnVisibility.action && (
        <td className="px-4 py-4">
          {actions.length > 0 && (
            <div className="relative flex justify-center">
              <button
                ref={buttonRef}
                className="inline-flex items-center gap-1.5 rounded-md border border-gray-200 bg-white px-3 py-1.5 text-xs font-medium text-gray-700 shadow-sm transition-all duration-300 hover:border-blue-200 hover:bg-blue-50 hover:text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                onClick={toggleDropdown}
              >
                update
                <FiChevronDown
                  className={`h-3.5 w-3.5 transition-transform duration-300 ${
                    dropdownOpen ? "rotate-180" : ""
                  }`}
                />
              </button>
              {dropdownOpen &&
                createPortal(
                  <div
                    ref={dropdownRef}
                    className="animate-fade-in fixed z-50 w-48 rounded-lg border border-gray-200 bg-white py-1 shadow-lg ring-1 ring-black/5"
                    style={{
                      top: `${dropdownPosition.top}px`,
                      left: `${dropdownPosition.left}px`,
                      animation: "fadeIn 0.2s ease-out"
                    }}
                  >
                    {actions.map((action, actionIndex) => (
                      <button
                        key={action.key}
                        className="block w-full px-3 py-2 text-left text-xs font-medium text-gray-700 transition-all duration-200 hover:bg-blue-50 hover:text-blue-900"
                        onClick={e => {
                          e.stopPropagation();
                          setDropdownOpen(false);
                          activeDropdownId = null;
                          onAction &&
                            onAction(action.key, action.isRetestRequest);
                        }}
                        title={action.label}
                        style={{
                          animation: `fadeIn 0.2s ease-out ${
                            actionIndex * 0.05
                          }s both`
                        }}
                      >
                        {action.label}
                      </button>
                    ))}
                  </div>,
                  document.body
                )}
            </div>
          )}
        </td>
      )}
    </tr>
  );
};

// Add this at the end of the file, before the export
const styles = `
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
`;

// Add the styles to the document
const styleSheet = document.createElement("style");
styleSheet.textContent = styles;
document.head.appendChild(styleSheet);

export default ReportTable;
