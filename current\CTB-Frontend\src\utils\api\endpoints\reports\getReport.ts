import axios from "../../axios";  
import { ReportsInfo } from "./parseTarget";
import { parseReportsInfo } from "./reports";

const BASE_URL = "/v2/reports";
 
const getUserRole = () => {
  return localStorage.getItem("userRole") || "GUEST";  
};

export const fetchReportsInfo = async (programId: number): Promise<ReportsInfo | null> => {
  try {
    const userRole = getUserRole();

    if (userRole === "BUSINESS_MANAGER" || userRole === "ADMIN_MANAGER") {
      console.warn("Skipping API call for BUSINESS_MANAGER or ADMIN_MANAGER.");
      return null;  
    }

    console.log(`Fetching Reports Info for Program ID: ${programId}`);

    const response = await axios.get(`${BASE_URL}/reports-info/${programId}`);
    console.log("Raw API Response:", response.data);

    const parsedData: ReportsInfo = parseReportsInfo(response.data);
    console.log("Parsed Reports Info:", parsedData);

    return parsedData;
  } catch (error: any) {
    if (error.response && error.response.status === 401) {
      console.warn("Unauthorized access: Skipping logout and returning null.");
      return null;  
    }
    console.error("Error fetching reports info:", error);
    throw error;
  }
};
