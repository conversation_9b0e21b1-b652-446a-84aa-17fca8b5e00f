import React from 'react';
import { ReportData } from '../../types/report.types';

interface VulnerabilityRatingsEditorProps {
  reportData: ReportData;
  onDataChange: (field: string, value: any) => void;
}

const VulnerabilityRatingsEditor: React.FC<VulnerabilityRatingsEditorProps> = ({ 
  reportData, 
  onDataChange 
}) => {
  const severityMeta = [
              {
                severity: 'Critical',
                color: 'red',
                field: 'vulnerability_ratings.critical',
                defaultText: 'Exploitation is straightforward and usually results in a system-level compromise. It is advised to form a plan of action and patch it immediately.'
              },
              {
                severity: 'High',
      color: 'orange',
                field: 'vulnerability_ratings.high',
                defaultText: 'Exploitation is more difficult but could cause elevated privileges and potentially a loss of data or downtime. It is advised to form a plan of action and patch as soon as possible.'
              },
              {
                severity: 'Medium',
                color: 'yellow',
                field: 'vulnerability_ratings.medium',
                defaultText: 'Vulnerabilities exist but are not exploitable or require extra steps such as social engineering. It is advised to form a plan of action and patch after high-priority issues have been resolved.'
              },
              {
                severity: 'Low',
      color: 'blue',
                field: 'vulnerability_ratings.low',
      defaultText: "Vulnerabilities are non-exploitable but would reduce an organization's attack surface. It is advised to form a plan of action and patch during the next maintenance window."
              }
  ];

  return (
    <div className="h-full overflow-y-auto">
      <div className="bg-white rounded-xl shadow-lg border border-slate-100 p-6 mb-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-1 h-8 bg-gradient-to-b from-blue-500 to-blue-700 rounded-full" />
          <div>
            <h3 className="text-base font-bold text-blue-900 tracking-tight">Vulnerability Rating Definitions</h3>
            <p className="text-xs text-blue-700/80 font-medium mt-0.5">Edit the vulnerability rating descriptions</p>
          </div>
                  </div>
        <div className="pt-2 flex flex-col gap-6">
          {severityMeta.map(({ severity, color, field, defaultText }) => (
            <div
              key={severity}
              className="bg-white border border-slate-200 rounded-lg p-4 shadow-sm flex flex-col"
            >
              <div className="flex items-center gap-2 mb-2">
                <div className={`w-2 h-8 rounded-full bg-gradient-to-b from-${color}-400 to-${color}-600`} />
                <h4 className={`font-semibold text-sm text-${color}-700`}>{severity}</h4>
                </div>
                <textarea
                  value={reportData.vulnerability_ratings?.[severity.toLowerCase() as keyof typeof reportData.vulnerability_ratings] || defaultText}
                  onChange={(e) => onDataChange(field, e.target.value)}
                className="w-full h-28 p-3 border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 text-sm text-slate-800 bg-slate-50 resize-none transition-all duration-200"
                  placeholder={`Enter ${severity.toLowerCase()} severity description...`}
                />
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default VulnerabilityRatingsEditor; 