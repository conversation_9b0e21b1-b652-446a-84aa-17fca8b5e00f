import About from "../../components/profile/sections/About";
import PillsWrapper from "../../components/profile/sections/PillsWrapper";
import UserProfileForAdmin from "../../components/profile/UserProfileForAdmin";
import useUserDetailsForAdmin from "../../utils/hooks/user/useUserDetailsForAdmin";
import { useParams } from "react-router-dom";

const ResearcherProfile = () => {
  const { user_id } = useParams();
  const { about } = useUserDetailsForAdmin(user_id);
  const {
    achievements,
    languages,
    links,
    skills,
    hobbies,
    education,
    work_experience,
    tools_used,
    community_engagement,
    testimonials,
    publications,
    security_clearance
  } = useUserDetailsForAdmin(user_id);

  const Profile_Items = [
    {
      id: "SkillsSection",
      profileTitle: "Skills",
      item: skills
    },
    {
      id: "WorkExperienceSection",
      profileTitle: "Work Experience",
      item: work_experience
    },
    {
      id: "EducationSection",
      profileTitle: "Education",
      item: education
    },
    {
      id: "LinksSection",
      profileTitle: "Links",
      item: links
    },
    {
      id: "LangSection",
      profileTitle: "Languages",
      item: languages
    },
    {
      id: "PublicationsSection",
      profileTitle: "Publications",
      item: publications
    },
    {
      id: "SecurityClearanceSection",
      profileTitle: "Security Clearances",
      item: security_clearance
    },
    {
      id: "ToolsUsedSection",
      profileTitle: "Tools Used",
      item: tools_used
    },
    {
      id: "AcheivementSection",
      profileTitle: "Bug Bounty Achievements",
      item: achievements
    },
    {
      id: "CommunityEngagementSection",
      profileTitle: "Community Engagement",
      item: community_engagement
    },
    {
      id: "Testimonials",
      profileTitle: "Testimonials",
      item: testimonials
    },
    {
      id: "HobbiesSection",
      profileTitle: "Hobbies",
      item: hobbies
    }
  ];

  return (
    <UserProfileForAdmin>
      {about && (
        <About id="AboutMeSection" profileTitle="Bio" sectionText={about} />
      )}
      {Profile_Items.map(el => (
        <PillsWrapper
          id={el.id}
          item={el.item}
          profileTitle={el.profileTitle}
          key={el.id}
        />
      ))}
    </UserProfileForAdmin>
  );
};

export default ResearcherProfile;
