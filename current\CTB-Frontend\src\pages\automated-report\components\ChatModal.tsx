import React from 'react';

interface ChatModalProps {
  isOpen: boolean;
  onClose: () => void;
  header?: React.ReactNode;
  tabs?: React.ReactNode;
  children: React.ReactNode;
  typingIndicator?: string;
}

const ChatModal: React.FC<ChatModalProps> = ({ isOpen, onClose, header, tabs, children, typingIndicator }) => {
  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 z-50 pointer-events-none">
      <div className="fixed right-4 sm:right-8 mb-24 sm:mb-28 bottom-0 w-[98vw] max-w-md sm:max-w-lg flex flex-col h-[75vh] bg-white rounded-3xl shadow-2xl border border-gray-200 pointer-events-auto animate-fade-in overflow-hidden">
        {header}
        {tabs}
        <div className="flex-1 min-h-0 flex flex-col bg-gradient-to-br from-blue-50 via-white to-gray-50 px-2 sm:px-4 py-2 rounded-b-3xl">
          {children}
          {typingIndicator && (
            <div className="flex items-center gap-2 mt-2 mb-1">
              <span className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></span>
              <span className="text-xs text-blue-500 font-medium">{typingIndicator}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatModal; 