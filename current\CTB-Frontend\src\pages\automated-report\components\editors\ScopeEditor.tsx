import React from 'react';
import { ReportData } from '../../types/report.types';
import HtmlEditor from '../HtmlEditor';

interface ScopeEditorProps {
  reportData: ReportData;
  onHtmlChange: (field: string, value: string) => void;
  onTargetDetailsChange: (index: number, field: 'type' | 'url', value: string) => void;
  onAddTarget: () => void;
  onRemoveTarget: (index: number) => void;
}

const ScopeEditor: React.FC<ScopeEditorProps> = ({
  reportData,
  onHtmlChange,
  onTargetDetailsChange,
  onAddTarget,
  onRemoveTarget
}) => {
  return (
    <div className="h-full overflow-y-auto space-y-6">
      {/* Scope Content Section */}
      <div className="bg-white rounded-xl shadow-lg border border-slate-100 p-6 mb-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-1 h-8 bg-gradient-to-b from-blue-500 to-blue-700 rounded-full" />
          <div>
            <h3 className="text-base font-bold text-blue-900 tracking-tight">Scope Content</h3>
            <p className="text-xs text-blue-700/80 font-medium mt-0.5">Define the scope of your assessment</p>
          </div>
        </div>
        <div className="pt-2">
          <div className="flex gap-1 mb-2">
            <button
              onClick={() => {
                const editor = document.querySelector('[data-field="scope"]');
                if (editor) {
                  const selection = window.getSelection();
                  const range = selection?.getRangeAt(0);
                  const strong = document.createElement('strong');
                  range?.surroundContents(strong);
                }
              }}
              className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors duration-200 flex items-center gap-1"
            >
              <span className="font-bold">B</span>
              <span>Bold</span>
            </button>
            <button
              onClick={() => {
                const editor = document.querySelector('[data-field="scope"]');
                if (editor) {
                  const selection = window.getSelection();
                  const range = selection?.getRangeAt(0);
                  const em = document.createElement('em');
                  range?.surroundContents(em);
                }
              }}
              className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors duration-200 flex items-center gap-1"
            >
              <span className="italic">I</span>
              <span>Italic</span>
            </button>
            <button
              onClick={() => {
                const editor = document.querySelector('[data-field="scope"]');
                if (editor) {
                  const p = document.createElement('p');
                  p.innerHTML = '<br>';
                  editor.appendChild(p);
                }
              }}
              className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors duration-200 flex items-center gap-1"
            >
              <span>¶</span>
              <span>New Line</span>
            </button>
          </div>
          <HtmlEditor
            field="scope"
            title=""
            reportData={reportData}
            onHtmlChange={onHtmlChange as any}
          />
        </div>
      </div>

      {/* Target Details Section */}
      <div className="bg-white rounded-xl shadow-lg border border-slate-100 p-6 mb-6">
        <div className="flex items-center gap-3 mb-4 justify-between">
          <div className="flex items-center gap-3">
            <div className="w-1 h-8 bg-gradient-to-b from-blue-500 to-blue-700 rounded-full" />
            <div>
              <h3 className="text-base font-bold text-blue-900 tracking-tight">Target Details</h3>
              <p className="text-xs text-blue-700/80 font-medium mt-0.5">Manage assessment targets and their URLs</p>
            </div>
          </div>
          <button
            onClick={onAddTarget}
            className="px-3 py-1.5 bg-blue-600 text-white text-xs rounded-md hover:bg-blue-700 transition-colors duration-200 flex items-center gap-1.5 shadow-sm"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
            </svg>
            Add Target
          </button>
        </div>
        <div className="pt-2">
          {reportData.target_details?.length === 0 ? (
            <div className="text-center py-8 bg-blue-50 rounded-lg border border-dashed border-blue-200">
              <svg className="w-12 h-12 mx-auto text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
              <p className="mt-2 text-sm text-blue-700">No targets added yet</p>
              <button
                onClick={onAddTarget}
                className="mt-2 text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                Add your first target
              </button>
            </div>
          ) : (
            <div className="space-y-3">
              {reportData.target_details?.map((target, index) => (
                <div key={index} className="group relative bg-blue-50 rounded-lg p-3 border border-blue-100 hover:border-blue-300 transition-colors duration-200">
                  <div className="flex items-center gap-3">
                    <div className="flex-1 space-y-2">
                      <div>
                        <label className="block text-xs font-medium text-blue-900 mb-1">Target Type</label>
                        <input
                          type="text"
                          value={target.type}
                          onChange={(e) => onTargetDetailsChange(index, 'type', e.target.value)}
                          className="w-full px-2.5 py-1.5 text-sm border border-blue-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400"
                          placeholder="e.g., Web Application, API, Mobile App"
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-blue-900 mb-1">Target URL</label>
                        <input
                          type="text"
                          value={target.url}
                          onChange={(e) => onTargetDetailsChange(index, 'url', e.target.value)}
                          className="w-full px-2.5 py-1.5 text-sm border border-blue-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400"
                          placeholder="e.g., https://example.com"
                        />
                      </div>
                    </div>
                    <button
                      onClick={() => onRemoveTarget(index)}
                      className="opacity-0 group-hover:opacity-100 text-blue-400 hover:text-red-600 transition-all duration-200"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ScopeEditor; 