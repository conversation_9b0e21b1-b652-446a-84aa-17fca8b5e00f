import React from 'react';
import { ReportData, DetailedFinding } from '../../types/report.types';
import BaseFindingEditor from './BaseFindingEditor';

interface CriticalFindingsEditorProps {
  reportData: ReportData;
  onFindingChange: (index: number, field: keyof DetailedFinding, value: string) => void;
  onRemoveFinding: (index: number) => void;
  onAddFinding: (severity: 'Critical' | 'High' | 'Medium' | 'Low') => void;
}

const CriticalFindingsEditor: React.FC<CriticalFindingsEditorProps> = (props) => {
  return <BaseFindingEditor {...props} severity="Critical" />;
};

export default CriticalFindingsEditor; 