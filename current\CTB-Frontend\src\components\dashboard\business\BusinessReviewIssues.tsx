import React from "react";
import { motion } from "framer-motion";
import { format } from "date-fns";
import { Link } from "react-router-dom";
import useBusinessReviewIssues from "../../../utils/hooks/dashboard/useBusinessReviewIssues";
import BugLifecycleProgress from "./BugLifecycleProgress";

const BusinessReviewIssues: React.FC = () => {
  const { issues, loading, error, refetch } = useBusinessReviewIssues();

  // Format date to be more readable
  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return format(date, "MMM d, yyyy");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Container animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { duration: 0.5, when: "beforeChildren" }
    }
  };

  // Item animation variants
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({ 
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.4
      }
    })
  };

  return (
    <motion.div
      className="relative rounded-xl border border-gray-200 bg-white/80 backdrop-blur-sm p-4 shadow-lg overflow-hidden max-w-full"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      style={{ 
        boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.05), 0 8px 10px -6px rgba(0, 0, 0, 0.02)"
      }}
    >
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          Business Review Issues
        </h3>
        <p className="text-sm text-gray-500">
          Reports requiring business attention and action
        </p>
      </div>

      {loading && (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800">
                Failed to load business review issues
              </p>
            </div>
            <div className="ml-auto pl-3">
              <button
                onClick={() => refetch()}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 text-red-800"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      )}

      {!loading && !error && issues.length === 0 && (
        <div className="py-8 text-center">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No issues found</h3>
          <p className="mt-1 text-sm text-gray-500">There are no business review issues at this time.</p>
        </div>
      )}

      {!loading && !error && issues.length > 0 && (
        <div className="space-y-4 overflow-hidden">
          {issues.map((issue, i) => (
            <motion.div
              key={issue.report_id}
              custom={i}
              variants={itemVariants}
              className={`rounded-lg border p-4 transition-all overflow-hidden ${
                issue.is_business_review
                  ? "border-blue-200 bg-blue-50"
                  : "border-gray-200 bg-gray-50"
              }`}
            >
              <div className="flex flex-col md:flex-row md:items-center md:justify-between overflow-hidden">
                <div className="flex-1 min-w-0 overflow-hidden">
                  <Link
                    to={`/dashboard/reports/${issue.report_id}`}
                    className="block text-lg font-medium text-gray-900 hover:text-blue-600 transition-colors break-words"
                  >
                    {issue.title}
                  </Link>
                  <div className="mt-1 flex flex-wrap items-center gap-2 overflow-hidden">
                    <span
                      className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium"
                      style={{ 
                        backgroundColor: `${issue.severity_color}20`,  
                        color: issue.severity_color 
                      }}
                    >
                      {issue.severity_category}
                    </span>
                    <span className="text-xs text-gray-500">
                      {formatDate(issue.submitted_date)}
                      <span className="mx-1">•</span>
                      {issue.days_open} {issue.days_open === 1 ? 'day' : 'days'} open
                    </span>
                  </div>
                  <div className="mt-1 flex flex-wrap items-center text-sm text-gray-500 overflow-hidden">
                    <span className="mr-2">
                      Program: <span className="font-medium break-words">{issue.program_name}</span>
                    </span>
                    <span>
                      Status: <span className="font-medium break-words">{issue.status_label}</span>
                    </span>
                  </div>
                </div>
                {issue.is_business_review && (
                  <div className="mt-2 md:mt-0 md:ml-4 flex-shrink-0">
                    <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      Needs Review
                    </span>
                  </div>
                )}
              </div>
              
              <div className="mt-3 overflow-hidden">
                <BugLifecycleProgress lifecycle={issue.lifecycle} />
              </div>
            </motion.div>
          ))}
          
          <div className="pt-2 flex justify-center">
            <Link
              to="/dashboard/reports"
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              View All Reports
              <svg className="ml-2 -mr-1 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </Link>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default BusinessReviewIssues; 