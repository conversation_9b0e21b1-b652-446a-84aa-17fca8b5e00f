import { Model, DataTypes, Sequelize, ModelC<PERSON> } from "sequelize";

export type ReportAttributes = {
  report_id?: number;
  user_id?: number;
  program_id?: number;
  is_delete?: boolean;
  report_title?: string;
  scope?: string;
  category?: string;
  severity?: number;
  severity_category?: string;
  description?: DataTypes.TextDataType;
  instructions?: DataTypes.TextDataType;
  impact?: DataTypes.TextDataType;
  fix?: DataTypes.TextDataType;
  additional_info?: DataTypes.TextDataType;
  jira_issue_key?: string;
  submitted_date?: Date;
  state?: string;
  payout?: number;
  triage_status?: string;
  comments?: string;
  attachments?: string;
  reject_reason?: string;
  certificate_generated?: boolean;
  stateCount?: number;
  severityCount?: number;
};

interface ReportCreationAttributes extends ReportAttributes {}

export class Report<T = unknown> extends Model<
  ReportAttributes & T,
  ReportCreationAttributes
> {
  declare report_id: number;
  declare user_id: number;
  declare program_id: number;
  declare is_delete: boolean;
  declare report_title: string;
  declare scope: string;
  declare category: string;
  declare severity: number;
  declare severity_category: string;
  declare description: DataTypes.TextDataType;
  declare instructions: DataTypes.TextDataType;
  declare impact: DataTypes.TextDataType;
  declare fix: DataTypes.TextDataType;
  declare additional_info: DataTypes.TextDataType;
  declare jira_issue_key: string;
  declare submitted_date: Date;
  declare state: string;
  declare payout: number;
  declare triage_status: string;
  declare comments: string;
  declare attachments: string;
  declare reject_reason: string;
  declare certificate_generated: boolean;
  declare stateCount?: number;

  public static associate(models: Record<string, ModelCtor<Model>>) {
    this.belongsTo(models.user, {
      foreignKey: "user_id"
    });
    this.belongsTo(models.program, {
      foreignKey: "program_id"
    });
  }
}

export default (sequelize: Sequelize) => {
  Report.init(
    {
      report_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        autoIncrement: true,
        primaryKey: true
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "user",
          key: "user_id"
        }
      },
      program_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "program",
          key: "program_id"
        }
      },
      is_delete: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      report_title: {
        type: DataTypes.STRING
      },
      scope: {
        type: DataTypes.STRING
      },
      category: {
        type: DataTypes.STRING
      },
      severity: {
        type: DataTypes.NUMBER
      },
      severity_category: {
        type: DataTypes.STRING
      },
      description: {
        type: DataTypes.TEXT
      },
      instructions: {
        type: DataTypes.TEXT
      },
      impact: {
        type: DataTypes.TEXT
      },
      fix: {
        type: DataTypes.TEXT
      },
      additional_info: {
        type: DataTypes.TEXT
      },
      jira_issue_key: {
        type: DataTypes.STRING,
        allowNull: true
      },
      submitted_date: {
        type: DataTypes.DATE
      },
      state: {
        type: DataTypes.STRING
      },
      payout: {
        type: DataTypes.FLOAT
      },
      triage_status: {
        type: DataTypes.STRING
      },
      comments: {
        type: DataTypes.STRING
      },
      attachments: {
        type: DataTypes.STRING
      },
      reject_reason: {
        type: DataTypes.STRING
      },
      certificate_generated: {
        type: DataTypes.BOOLEAN
      }
    },
    {
      sequelize,
      timestamps: false,
      modelName: "report"
    }
  );

  return Report;
};
