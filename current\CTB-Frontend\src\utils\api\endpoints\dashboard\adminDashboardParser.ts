import moment from "moment";
import { DATE_FORMAT } from "../../../..";

// Define the types for the API response (updated to include group9)
export type AdminDashboardAPIResponse = {
  group1: {
    totalReports: number;
    reportsAddedLastMonth: number;
    totalResolvedReports: number;
    resolvedReportsLastMonth: number;
    activePrograms: number;
    activeProgramsAddedLastMonth: number;
    activeResearchers: number;
    underReviewReports: number;
    totalRetestReports: number;
  };
  group2: {
    programsWithSeverityCounts: Array<{
      programId: number;
      programTitle: string;
      researchers: Array<{
        userId: number;
        username: string;
      }>;
      severityCounts: Array<{
        severity: string;
        count: number;
      }>;
      totalReportsForProgram: number;
    }>;
  };
  group3: {
    pentesters: Array<{
      userId: number;
      username: string;
      email: string;
      pfp: string;
      severityCounts: Array<{
        severity: string;
        count: number;
      }>;
      totalReportsCreated: number;
      triageRejectedCount: number;
      programs: Array<{
        programId: number;
        programTitle: string;
      }>;
    }>;
  };
  group4: {
    underReviewReports: Array<{
      reportId: number;
      reportName: string;
      severityCategory: string;
      submittedDate: string;
    }>;
  };
  group5: {
    retestReports: Array<{
      retestId: number;
      reportTitle: string;
      status: string;
    }>;
  };
  group6: {
    programsWithRetestCounts: Array<{
      programId: number;
      programTitle: string;
      retestCounts: {
        completed: number;
        inProgress: number;
      };
    }>;
  };
  group7: {
    programsWithResearchers: Array<{
      programId: number;
      programTitle: string;
      researchers: Array<{
        userId: number;
        username: string;
        reportCount: number; 
      }>;
    }>;
  };
  group8: {
    programsWithSeverityCategoryCounts: Array<{
      programId: number;
      programTitle: string;
      severityCategoryCounts: Array<{
        severity: string;
        count: number;
      }>;
    }>;
  };
  group9: {
    programs: Array<{
      programId: number;
      programTitle: string;
      expectedStartDate: string | null;
    }>;
  };
};

// Define the types for the parsed response (updated to include group9)
export type ParsedAdminDashboard = {
  totalReports: number;
  reportsAddedLastMonth: number;
  totalResolvedReports: number;
  resolvedReportsLastMonth: number;
  activePrograms: number;
  activeProgramsAddedLastMonth: number;
  activeResearchers: number;
  underReviewReports: number;
  totalRetestReports: number;
  programsWithSeverityCounts: Array<{
    programId: number;
    programTitle: string;
    researchers: Array<{
      userId: number;
      username: string;
    }>;
    severityCounts: Array<{
      severity: string;
      count: number;
    }>;
    totalReportsForProgram: number;
  }>;
  pentesters: Array<{
    userId: number;
    username: string;
    email: string;
    pfp: string;
    severityCounts: Array<{
      severity: string;
      count: number;
    }>;
    totalReportsCreated: number;
    triageRejectedCount: number;
    programs: Array<{
      programId: number;
      programTitle: string;
    }>;
  }>;
  underReviewReportsList: Array<{
    reportId: number;
    reportName: string;
    severityCategory: string;
    submittedDate: string;
  }>;
  retestReportsList: Array<{
    retestId: number;
    reportTitle: string;
    status: string;
  }>;
  programsWithRetestCounts: Array<{
    programId: number;
    programTitle: string;
    retestCounts: {
      completed: number;
      inProgress: number;
    };
  }>;
  programsWithResearchers: Array<{
    programId: number;
    programTitle: string;
    researchers: Array<{
      userId: number;
      username: string;
      reportCount: number; 
    }>;
  }>;
  programsWithSeverityCategoryCounts: Array<{
    programId: number;
    programTitle: string;
    severityCategoryCounts: Array<{
      severity: string;
      count: number;
    }>;
  }>;
  programs: Array<{
    programId: number;
    programTitle: string;
    expectedStartDate: string | null;
  }>;
};

/**
 * Parse the Admin dashboard API response into a structured format
 */
export const parseAdminDashboard = (
  response: AdminDashboardAPIResponse
): ParsedAdminDashboard => {
  return {
    totalReports: response.group1.totalReports,
    reportsAddedLastMonth: response.group1.reportsAddedLastMonth,
    totalResolvedReports: response.group1.totalResolvedReports,
    resolvedReportsLastMonth: response.group1.resolvedReportsLastMonth,
    activePrograms: response.group1.activePrograms,
    activeProgramsAddedLastMonth: response.group1.activeProgramsAddedLastMonth,
    activeResearchers: response.group1.activeResearchers,
    underReviewReports: response.group1.underReviewReports,
    totalRetestReports: response.group1.totalRetestReports,
    programsWithSeverityCounts: response.group2.programsWithSeverityCounts.map(
      program => ({
        programId: program.programId,
        programTitle: program.programTitle,
        researchers: program.researchers,
        severityCounts: program.severityCounts,
        totalReportsForProgram: program.totalReportsForProgram
      })
    ),
    pentesters: response.group3.pentesters.map(pentester => ({
      userId: pentester.userId,
      username: pentester.username,
      email: pentester.email,
      pfp: pentester.pfp,
      severityCounts: pentester.severityCounts,
      totalReportsCreated: pentester.totalReportsCreated,
      triageRejectedCount: pentester.triageRejectedCount,
      programs: pentester.programs
    })),
    underReviewReportsList: response.group4.underReviewReports.map(report => ({
      reportId: report.reportId,
      reportName: report.reportName,
      severityCategory: report.severityCategory,
      submittedDate: report.submittedDate
    })),
    retestReportsList: response.group5.retestReports.map(retest => ({
      retestId: retest.retestId,
      reportTitle: retest.reportTitle,
      status: retest.status
    })),
    programsWithRetestCounts: response.group6.programsWithRetestCounts.map(
      program => ({
        programId: program.programId,
        programTitle: program.programTitle,
        retestCounts: program.retestCounts
      })
    ),
    programsWithResearchers: response.group7.programsWithResearchers.map(
      program => ({
        programId: program.programId,
        programTitle: program.programTitle,
        researchers: program.researchers.map(researcher => ({
          userId: researcher.userId,
          username: researcher.username,
          reportCount: researcher.reportCount 
        }))
      })
    ),
    programsWithSeverityCategoryCounts: response.group8.programsWithSeverityCategoryCounts.map(
      program => ({
        programId: program.programId,
        programTitle: program.programTitle,
        severityCategoryCounts: program.severityCategoryCounts
      })
    ),
    programs: response.group9.programs.map(program => ({
      programId: program.programId,
      programTitle: program.programTitle,
      expectedStartDate: program.expectedStartDate
    }))
  };
};