import { UserRole } from "../../api/endpoints/user/credentials";

export interface Invitation {
  id: number;
  invitee_email: string;
  role: UserRole;
  status: string;
  expires_at: string;
  Inviter: {
    username: string;
    email: string;
  };
}

export interface ActivityLog {
  id: number;
  user_id: number;
  role: UserRole;
  action: string;
  module: string;
  created_at: string;
  user: {
    username: string;
    email: string;
  };
}

export interface RoleOption {
  value: UserRole;
  label: string;
}

export const ROLE_OPTIONS: Record<UserRole, RoleOption[]> = {
  [UserRole.ADMIN]: [
    { value: UserRole.QA, label: "QA" },
    { value: UserRole.ADMIN_MANAGER, label: "Admin Manager" },
    { value: UserRole.SUB_ADMIN, label: "Sub Admin" }
  ],
  [UserRole.BUSINESS]: [
    { value: UserRole.DEVELOPER, label: "Security Engineer" },
    { value: UserRole.BUSINESS_MANAGER, label: "Program Manager" },
    { value: UserRole.BUSINESS_ADMINISTRATOR, label: "Organisation Admin" }
  ],
  [UserRole.QA]: [],
  [UserRole.DEVELOPER]: [],
  [UserRole.ADMIN_MANAGER]: [],
  [UserRole.BUSINESS_MANAGER]: [],
  [UserRole.BUSINESS_ADMINISTRATOR]: [],
  [UserRole.SUB_ADMIN]: [],
  [UserRole.RESEARCHER]: []
};

export const roleMapping: Partial<Record<UserRole, string>> = {
  [UserRole.BUSINESS_MANAGER]: "Program Manager",
  [UserRole.DEVELOPER]: "Security Engineer",
  [UserRole.BUSINESS_ADMINISTRATOR]: "Organization Admin",
  [UserRole.ADMIN_MANAGER]: "Admin Manager",
  [UserRole.QA]: "QA",
  [UserRole.SUB_ADMIN]: "Sub Admin",
  [UserRole.BUSINESS]: "Business Owner",
  [UserRole.ADMIN]: "Admin",
  [UserRole.RESEARCHER]: "Researcher"
};

export const formatRole = (role: UserRole): string => {
  return roleMapping[role] || String(role).replace(/_/g, " ");
};

export const getRoleStyle = (role: UserRole): string => {
  switch (role) {
    case UserRole.BUSINESS:
      return "bg-green-800 text-white border-green-300";
    case UserRole.BUSINESS_ADMINISTRATOR:
      return "bg-green-700 text-white border-green-300";
    case UserRole.BUSINESS_MANAGER:
      return "bg-green-600 text-white border-green-300";
    case UserRole.DEVELOPER:
      return "bg-green-500 text-white border-green-300";
    case UserRole.RESEARCHER:
      return "bg-yellow-500 text-white border-yellow-300";
    case UserRole.QA:
      return "bg-blue-700 text-white border-blue-300";
    case UserRole.ADMIN_MANAGER:
      return "bg-blue-600 text-white border-purple-300";
    case UserRole.SUB_ADMIN:
      return "bg-blue-500 text-white border-red-300";
    default:
      return "bg-gray-500 text-white";
  }
};
