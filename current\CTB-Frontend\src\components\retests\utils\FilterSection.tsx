import React, { useState, useEffect, useRef } from "react";
import { CTBRetest } from "../../../utils/api/endpoints/retests/parseRetests";
import FilterIcon from "../../../assets/icons/FilterIcon";

interface FilterSectionProps {
  retests: CTBRetest[];
  filters: {
    status: string[];
    month: string[];
    year: string[];
    order: string;
  };
  onFilter: (filters: {
    status: string[];
    month: string[];
    year: string[];
    order: string;
  }) => void;
}

const FilterSection: React.FC<FilterSectionProps> = ({
  retests,
  filters,
  onFilter,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string[]>(filters.status);
  const [monthFilter, setMonthFilter] = useState<string[]>(filters.month);
  const [yearFilter, setYearFilter] = useState<string[]>(filters.year);
  const [orderFilter, setOrderFilter] = useState(filters.order);

  // Extract unique statuses, months, and years from retests
  const uniqueStatuses = Array.from(
    new Set(retests.map((retest) => retest.status))
  );
  const uniqueMonths = Array.from(
    new Set(
      retests.map((retest) =>
        new Date(retest.createdAt).toLocaleString("default", { month: "long" })
      )
    )
  );
  const uniqueYears = Array.from(
    new Set(
      retests.map((retest) => new Date(retest.createdAt).getFullYear().toString())
    )
  );

  // Check if there is only one option available in all sections
  const shouldShowFilter =
    uniqueStatuses.length > 1 ||
    uniqueMonths.length > 1 ||
    uniqueYears.length > 1 ||
    ["asc", "desc"].length > 1;

  const toggleFilter = () => setIsOpen(!isOpen);

  const handleStatusChange = (status: string) => {
    const newStatusFilter = statusFilter.includes(status)
      ? statusFilter.filter((s) => s !== status)
      : [...statusFilter, status];
    setStatusFilter(newStatusFilter);
    onFilter({
      status: newStatusFilter,
      month: monthFilter,
      year: yearFilter,
      order: orderFilter,
    });
  };

  const handleMonthChange = (month: string) => {
    const newMonthFilter = monthFilter.includes(month)
      ? monthFilter.filter((m) => m !== month)
      : [...monthFilter, month];
    setMonthFilter(newMonthFilter);
    onFilter({
      status: statusFilter,
      month: newMonthFilter,
      year: yearFilter,
      order: orderFilter,
    });
  };

  const handleYearChange = (year: string) => {
    const newYearFilter = yearFilter.includes(year)
      ? yearFilter.filter((y) => y !== year)
      : [...yearFilter, year];
    setYearFilter(newYearFilter);
    onFilter({
      status: statusFilter,
      month: monthFilter,
      year: newYearFilter,
      order: orderFilter,
    });
  };

  const handleOrderChange = (order: string) => {
    setOrderFilter(order);
    onFilter({
      status: statusFilter,
      month: monthFilter,
      year: yearFilter,
      order,
    });
  };

  const filterSectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        filterSectionRef.current &&
        !filterSectionRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  // Sync local state with applied filters
  useEffect(() => {
    setStatusFilter(filters.status);
    setMonthFilter(filters.month);
    setYearFilter(filters.year);
    setOrderFilter(filters.order);
  }, [filters]);

  if (!shouldShowFilter) return null;

  return (
    <div className="relative" ref={filterSectionRef}>
      <button
        onClick={toggleFilter}
        className=" text-xsm flex gap-2 rounded-md border border-gray-300 bg-ctb-grey-50 px-4 py-2 font-medium text-black hover:bg-gray-50"
      >
        <FilterIcon className="stroke-black" /> Filter
      </button>
      {isOpen && (
        <div className="top-50 fixed left-64 right-0 z-10 mt-2 w-[450px] rounded-md border border-gray-300 bg-white shadow-lg">
          <div className="grid grid-cols-2 gap-10 p-4">
            <div>
              <h3 className="mb-2 text-lg font-medium">Status</h3>
              {uniqueStatuses.map((status) => (
                <label key={status} className="mb-2 flex items-center">
                  <input
                    type="checkbox"
                    checked={statusFilter.includes(status)}
                    onChange={() => handleStatusChange(status)}
                    className="mr-2"
                  />
                  {status}
                </label>
              ))}
            </div>
            <div>
              <h3 className="mb-2 text-lg font-medium">Month</h3>
              {uniqueMonths.map((month) => (
                <label key={month} className="mb-2 flex items-center">
                  <input
                    type="checkbox"
                    checked={monthFilter.includes(month)}
                    onChange={() => handleMonthChange(month)}
                    className="mr-2"
                  />
                  {month}
                </label>
              ))}
            </div>
            <div>
              <h3 className="mb-2 text-lg font-medium">Year</h3>
              {uniqueYears.map((year) => (
                <label key={year} className="mb-2 flex items-center">
                  <input
                    type="checkbox"
                    checked={yearFilter.includes(year)}
                    onChange={() => handleYearChange(year)}
                    className="mr-2"
                  />
                  {year}
                </label>
              ))}
            </div>
            <div>
              <h3 className="mb-2 text-lg font-medium">Order</h3>
              <label className="mb-2 flex items-center">
                <input
                  type="radio"
                  name="order"
                  value="desc"
                  checked={orderFilter === "desc"}
                  onChange={() => handleOrderChange("desc")}
                  className="mr-2"
                />
                Recent
              </label>
              <label className="mb-2 flex items-center">
                <input
                  type="radio"
                  name="order"
                  value="asc"
                  checked={orderFilter === "asc"}
                  onChange={() => handleOrderChange("asc")}
                  className="mr-2"
                />
                Oldest
              </label>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FilterSection;