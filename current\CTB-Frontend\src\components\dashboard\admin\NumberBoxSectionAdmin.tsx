import React, { useRef, useState, useEffect } from "react";
import { MdRadar } from "react-icons/md";
import { TbClipboardText } from "react-icons/tb";
import {
  FaChartLine,
  FaCheckCircle,
  FaCode,
  FaUsers,
  FaSync,
  FaExclamationCircle,
  FaArrowUp,
  FaArrowDown,
  FaInfoCircle,
} from "react-icons/fa";
import { PiClockCounterClockwiseBold } from "react-icons/pi";
import { MdOutlinePreview } from "react-icons/md";
import { TbDeviceImacCode } from "react-icons/tb";
import BrowserCodeIcon from "../../../assets/icons/BrowserCodeIcon";
import CircleCodeIcon from "../../../assets/icons/CircleCodeIcon";

type StatCardProps = {
  title: string;
  value: number;
  description?: string;
  change?: number;
  icon?: React.ReactNode;
  tooltip?: string;
};

const StatCard = ({
  title,
  value,
  description,
  change,
  icon,
  tooltip
}: StatCardProps) => {
  const isPositive = change && change > 0;
  const isNegative = change && change < 0;
  const showTrend = typeof change !== "undefined";

  return (
    <div className="relative w-72 flex-none rounded-xl border border-gray-200 bg-white p-6 shadow-sm">
      {tooltip && (
        <div className="absolute right-4 top-4">
          <div className="group relative">
            <FaInfoCircle className="h-4 w-4 cursor-help text-gray-400" />
            <div className="invisible absolute left-1/2 top-full z-10 mt-2 w-48 -translate-x-1/2 rounded-lg bg-gray-900 px-3 py-2 text-center text-sm text-white opacity-0 transition-all duration-200 group-hover:visible group-hover:opacity-100">
              {tooltip}
              <div className="absolute -top-1 left-1/2 h-2 w-2 -translate-x-1/2 rotate-45 transform bg-gray-900" />
            </div>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between gap-4">
        <div className="min-w-0 flex-1">
          <p className="mb-1 text-sm font-medium text-gray-500">{title}</p>
          <p className="truncate text-2xl font-semibold text-gray-900">
            {value.toLocaleString()}
          </p>

          {(description || showTrend) && (
            <div className="mt-2 flex items-center gap-2">
              {showTrend && (
                <div
                  className={`flex items-center gap-1 text-sm font-medium
                  ${
                    isPositive
                      ? "text-green-600"
                      : isNegative
                      ? "text-red-600"
                      : "text-gray-600"
                  }`}
                >
                  {isPositive ? (
                    <FaArrowUp className="h-3 w-3" />
                  ) : (
                    <FaArrowDown className="h-3 w-3" />
                  )}
                  {Math.abs(change)}%
                </div>
              )}
              {description && (
                <span className="text-sm text-gray-500">{description}</span>
              )}
            </div>
          )}
        </div>

        <div className="flex-shrink-0 rounded-2xl bg-ctb-blue-250 p-2 text-lg">
          {icon}
        </div>
      </div>
    </div>
  );
};

type AdminDashboardStatsProps = {
  totalReports: number;
  reportsAddedLastMonth: number;
  totalResolvedReports: number;
  resolvedReportsLastMonth: number;
  activePrograms: number;
  activeProgramsAddedLastMonth: number;
  activeResearchers: number;
  underReviewReports: number;
  totalRetestReports: number;
};

const AdminDashboardStats = ({
  totalReports,
  reportsAddedLastMonth,
  totalResolvedReports,
  resolvedReportsLastMonth,
  activePrograms,
  activeProgramsAddedLastMonth,
  activeResearchers,
  underReviewReports,
  totalRetestReports
}: AdminDashboardStatsProps) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);

  // Calculate percentage changes
  const reportsGrowth = (
    (reportsAddedLastMonth / (totalReports - reportsAddedLastMonth)) *
    100
  ).toFixed(1);
  const resolvedGrowth = (
    (resolvedReportsLastMonth /
      (totalResolvedReports - resolvedReportsLastMonth)) *
    100
  ).toFixed(1);
  const programsGrowth = (
    (activeProgramsAddedLastMonth /
      (activePrograms - activeProgramsAddedLastMonth)) *
    100
  ).toFixed(1);

  const stats = [
    {
      title: "Total Reports",
      value: totalReports,
      description: "vs. last month",
      change: Number(reportsGrowth),
      icon: <TbClipboardText className="text-blue-600  text-3xl" />,
      tooltip: "Total number of vulnerability reports submitted"
    },
    {
      title: "Resolved Reports",
      value: totalResolvedReports,
      description: "vs. last month",
      change: Number(resolvedGrowth),
      icon: <MdRadar className="text-blue-600 text-3xl" />,
      tooltip: "Reports that have been verified and resolved"
    },
    {
      title: "Pending Review",
      value: underReviewReports,
      description: "Requires attention",
      icon: <MdOutlinePreview className="text-blue-600 text-3xl" />,
      tooltip: "Reports awaiting admin review"
    },    
    {
      title: "Pending Retests",
      value: totalRetestReports,
      description: "Needs verification",
      icon: <CircleCodeIcon className="text-blue-600 text-2xl" />,
      tooltip: "Fixed vulnerabilities pending retest"
    },
    {
      title: "Active Programs",
      value: activePrograms,
      description: "vs. last month",
      change: Number(programsGrowth),
      icon: <BrowserCodeIcon className="text-blue-600  text-2xl " />,
      tooltip: "Currently active security programs"
    },
    {
      title: "Active Researchers",
      value: activeResearchers,
      description: "Contributors",
      icon: <PiClockCounterClockwiseBold   className="text-blue-600  text-3xl " />,
      tooltip: "Researchers with recent submissions"
    }
  ];

  // Scroll handling logic
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const handleWheel = (e: WheelEvent) => {
      const { scrollLeft, scrollWidth, clientWidth } = container;
      const isAtStart = scrollLeft === 0;
      const isAtEnd = scrollLeft + clientWidth >= scrollWidth;

      const isScrollingRight = e.deltaY > 0;
      const isScrollingLeft = e.deltaY < 0;

      if ((isAtStart && isScrollingLeft) || (isAtEnd && isScrollingRight)) {
        e.preventDefault();
        return;
      }

      if (scrollWidth > clientWidth) {
        e.preventDefault();
        container.scrollLeft += e.deltaY;
      }
    };

    container.addEventListener("wheel", handleWheel, { passive: false });
    return () => container.removeEventListener("wheel", handleWheel);
  }, []);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!scrollContainerRef.current) return;
    setIsDragging(true);
    setStartX(e.pageX - scrollContainerRef.current.offsetLeft);
    setScrollLeft(scrollContainerRef.current.scrollLeft);
  };

  const handleMouseLeave = () => setIsDragging(false);
  const handleMouseUp = () => setIsDragging(false);

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !scrollContainerRef.current) return;
    e.preventDefault();
    const x = e.pageX - scrollContainerRef.current.offsetLeft;
    const walk = (x - startX) * 2;
    scrollContainerRef.current.scrollLeft = scrollLeft - walk;
  };

  return (
    <div className="relative overflow-hidden">
      <div
        ref={scrollContainerRef}
        className="flex cursor-grab gap-6 overflow-x-auto px-4 pb-4"
        style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
        onMouseDown={handleMouseDown}
        onMouseLeave={handleMouseLeave}
        onMouseUp={handleMouseUp}
        onMouseMove={handleMouseMove}
      >
        {stats.map(stat => (
          <StatCard
            key={stat.title}
            title={stat.title}
            value={stat.value}
            description={stat.description}
            change={stat.change}
            icon={stat.icon}
            tooltip={stat.tooltip}
          />
        ))}
      </div>
    </div>
  );
};

export default AdminDashboardStats;
