export const testingTypes = {
  "Black Box": "Black Box",
  "Grey Box": "Grey Box",
  "White Box": "White Box"
};

export const environmentTypes = {
  Production: "Production",
  Staging: "Staging"
};

export const complianceTypes = {
  SOC2: "SOC2",
  HIPAA: "HIPAA",
  ISO27001: "ISO27001",
  PCIDSS: "PCIDSS",
  Other: "Other"
};

export const testingTypeOptions = [
  {
    label: testingTypes["Black Box"],
    value: testingTypes["Black Box"]
  },
  {
    label: testingTypes["Grey Box"],
    value: testingTypes["Grey Box"]
  },
  {
    label: testingTypes["White Box"],
    value: testingTypes["White Box"]
  }
];

export const environmentTypeOptions = [
  {
    label: environmentTypes["Production"],
    value: environmentTypes["Production"]
  },
  {
    label: environmentTypes["Staging"],
    value: environmentTypes["Staging"]
  }
];

export const complianceTypeOptions = [
  {
    label: complianceTypes["SOC2"],
    value: complianceTypes["SOC2"]
  },
  {
    label: complianceTypes["HIPAA"],
    value: complianceTypes["HIPAA"]
  },
  {
    label: complianceTypes["ISO27001"],
    value: complianceTypes["ISO27001"]
  },
  {
    label: complianceTypes["PCIDSS"],
    value: complianceTypes["PCIDSS"]
  },
  {
    label: complianceTypes["Other"],
    value: complianceTypes["Other"]
  }
];

export const researcherSkills = {
  1  : "Red Teaming",
  2  : "IoT Penetration Testing",
  3  : "Mobile Penetration Testing",
  4  : "Application Security",
  5  : "Security Operations",
  6  : "Threat Modeling",
  7  : "Reverse Engineering",
  8  : "Hardware Security",
  9  : "Security Architecture",
  10 : "Compliance and Standards",
  11 : "Emerging Technologies",
  12 : "Security Research"
}

export const researcherTools = {
  1: 'Nessus',
  2: 'OpenVAS',
  3: 'Metasploit',
  4: 'Burp Suite',
  5: 'Wireshark',
  6: 'Tcpdump',
  7: 'GDB',
  8: 'OllyDbg',
  9: 'OWASP ZAP',
  10: 'SQLmap',
  11: 'Fortify',
  12: 'Checkmarx',
  13: 'Aircrack-ng',
  14: 'Kismet',
  15: 'OpenSSL',
  16: 'John the Ripper',
  17: 'Scout Suite',
  18: 'CloudSploit',
  19: 'Kali Linux',
  20: 'Parrot Security OS',
  21: 'Python scripts',
  22: 'Bash scripts',
  23: 'Autopsy',
  24: 'Sleuth Kit',
  25: 'TheHive',
  26: 'GRR',
  27: 'IDA Pro',
  28: 'Ghidra',
  29: 'Immunity Debugger',
  30: 'Binary Ninja'
}
