import { useState, useEffect } from "react";
import { getRetestDetailsRightSection } from "../../api/endpoints/retests/retests";

const useRetestDetailsRightSection = (retestId: string) => {
  const [details, setDetails] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDetails = async () => {
      try {
        const response = await getRetestDetailsRightSection(retestId);
        setDetails(response.data);
      } catch (err) {
        if (err instanceof Error) {
          setError(
            err.message || "An error occurred while fetching retest details."
          );
        } else {
          setError("An error occurred while fetching retest details.");
        }
      } finally {
        setLoading(false);
      }
    };

    fetchDetails();
  }, [retestId]);

  return { details, loading, error };
};

export default useRetestDetailsRightSection;
