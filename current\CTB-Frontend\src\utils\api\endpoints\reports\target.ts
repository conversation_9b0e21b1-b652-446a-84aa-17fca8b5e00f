import axios from "../../axios";

const BASE_URL = "/v2/programs";

export const getProgramTargets = async (programId: number) => {
  console.log(`Fetching targets for program ID: ${programId}`);  

  try {
    const response = await axios.get(`${BASE_URL}/${programId}/targets`);
    
     if (!response.data || !response.data.data) {
      console.warn("Warning: No valid data found in response.");
    }

    return response.data.data;  
  } catch (error) {
    console.error("Error fetching program targets:", error);

    if ((error as any).response) {
      console.error("Error Response Status:", (error as any).response.status);
      console.error("Error Response Data:", (error as any).response.data);
    } else if ((error as any).request) {
      console.error("Error: No response received from server");
    } else {
      console.error("Error Message:", (error as any).message);
    }

    throw error;
  }
};
