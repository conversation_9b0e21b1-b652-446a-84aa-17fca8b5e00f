import React from "react";
import { cropSentence } from "../../../utils/formatText";
import StatusFormatter from "../../retests/utils/StatusFormatter";

// Define the types for the recent retest details
type RecentRetest = {
  retestId: number;
  reportTitle: string;
  status: string;
  latestComment: string;
};

// Define the props for the component
type RecentRetestsTableProps = {
  recentRetests: RecentRetest[];
};

const getStatusColor = (status: string) => {
  switch (status) {
    case "QA Review In Process":
    case "Retest In Process":
    case "Fix in Progress":
    case "Reopen Retest":
    case "Request Further Action":
    case "Need Information":
      return "bg-[#0132f5b0] border-[3px] border-blue-600 text-white";
    case "Fix Approved":
    case "Fix Verified":
    case "Risk Accepted and Closed":
      return "bg-[#23b92f] border-[3px] border-green-600 text-white";
    case "Fix Rejected":
    case "Fix Failed":
    case "Findings Rejected":
    case "Close Retest":
      return "bg-[#F50101B2] border-[3px] border-red-600 text-white";
    default:
      return "bg-blue-600 text-white";
  }
};

const RecentRetestsTable: React.FC<RecentRetestsTableProps> = ({
  recentRetests
}) => {
  return (
    <div className="w-full overflow-x-auto border border-gray-200 shadow-sm">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-100">
          <tr>
            {["Title", "Status"].map(header => (
              <th
                key={header}
                className="px-6 py-3 text-left text-xs font-semibold uppercase tracking-wider text-black"
              >
                {header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 bg-white">
          {recentRetests.length === 0 ? (
            <tr>
              <td
                colSpan={3}
                className="px-6 py-4 text-center text-sm text-gray-500"
              >
                No retests found
              </td>
            </tr>
          ) : (
            recentRetests.map(retest => (
              <tr
                key={retest.retestId}
                className="cursor-pointer transition-all duration-200 ease-in-out hover:bg-gray-50"
                onClick={() => {
                  window.location.href = `/dashboard/retests/${retest.retestId}`;
                }}
              >
                <td className="whitespace-nowrap px-6 py-4">
                  <div className="text-sm font-medium text-gray-900 transition-colors duration-200 hover:text-blue-600">
                    {cropSentence(retest.reportTitle, 40)}
                  </div>
                </td>
                <td className="whitespace-nowrap px-6 py-4">
                  <span
                    className={`inline-flex rounded-full px-3 py-1 text-xs font-semibold leading-5 ${getStatusColor(
                      retest.status
                    )}`}
                  >
                    <StatusFormatter status={retest.status} />
                  </span>
                </td>
                {/* <td className="whitespace-nowrap px-6 py-4">
                  <div className="text-sm text-gray-500">
                    {cropSentence(retest.latestComment)}
                  </div>
                </td> */}
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
};

export default RecentRetestsTable;
