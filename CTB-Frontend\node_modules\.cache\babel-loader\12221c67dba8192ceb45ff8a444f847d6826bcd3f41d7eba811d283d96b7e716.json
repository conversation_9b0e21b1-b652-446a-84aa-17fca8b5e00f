{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\jira-old\\\\CTB-Frontend\\\\src\\\\components\\\\users\\\\InvitationManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { UserRole } from \"../../utils/api/endpoints/user/credentials\";\nimport toast from \"react-hot-toast\";\nimport { createInvitation, deleteInvitation, getInvitations } from \"../../utils/api/endpoints/user/invitation\";\nimport { requestChangeUserRole, getActivityLogs } from \"../../utils/api/endpoints/user/multiTenant\";\nimport useUserCredentials from \"../../utils/hooks/user/useUserCredentials\";\nimport usePageTitle from \"../../utils/hooks/usePageTitle\";\n\n// Import types\nimport { ROLE_OPTIONS } from \"../../utils/hooks/multi-tenant/invitation\";\n\n// Import components\nimport SpinnerOverlay from \"../multi_tenant/SpinnerOverlay\";\nimport DeleteConfirmationModal from \"../multi_tenant/DeleteConfirmationModal\";\nimport RolePermissionsModal from \"../multi_tenant/RolePermissionsModal\";\nimport TabNavigation from \"../multi_tenant/TabNavigation\";\nimport CreateInvitationForm from \"../multi_tenant/CreateInvitationForm\";\nimport InvitationsTable from \"../multi_tenant/InvitationsTable\";\nimport InvitationsFilters from \"../multi_tenant/InvitationsFilters\";\nimport ActivityLogsTable from \"../multi_tenant/ActivityLogsTable\";\nimport ActivityLogsFilters from \"../multi_tenant/ActivityLogsFilters\";\nimport Pagination from \"../retests/utils/Pagination\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InvitationsManager = () => {\n  _s();\n  const {\n    role\n  } = useUserCredentials();\n  const [activeTab, setActiveTab] = useState(\"invitations\");\n  const [invitations, setInvitations] = useState([]);\n  const [activityLogs, setActivityLogs] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [logsLoading, setLogsLoading] = useState(false);\n  const [isError, setIsError] = useState(false);\n  const [logsError, setLogsError] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [currentInvitationsPage, setCurrentInvitationsPage] = useState(1);\n  const [currentLogsPage, setCurrentLogsPage] = useState(1);\n\n  // Invitations filters\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [filterRole, setFilterRole] = useState(\"all\");\n  const [filterStatus, setFilterStatus] = useState(\"all\");\n\n  // Activity logs filters\n  const [logsSearchQuery, setLogsSearchQuery] = useState(\"\");\n  const [filterLogsRole, setFilterLogsRole] = useState(\"all\");\n  const [filterLogsModule, setFilterLogsModule] = useState(\"all\");\n  const [sortLogsOrder, setSortLogsOrder] = useState(\"newest\");\n\n  // Modals\n  const [isPermissionsModalOpen, setIsPermissionsModalOpen] = useState(false);\n  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);\n  const [invitationToDelete, setInvitationToDelete] = useState(null);\n  const itemsPerPage = 10;\n  const availableRoles = role ? ROLE_OPTIONS[role] : [];\n  usePageTitle(\"Tenant and role management | Capture The Bug\");\n  useEffect(() => {\n    setCurrentInvitationsPage(1);\n  }, [searchQuery, filterRole, filterStatus]);\n  useEffect(() => {\n    setCurrentLogsPage(1);\n  }, [logsSearchQuery, filterLogsRole, filterLogsModule, sortLogsOrder]);\n  const getRoleFilterOptions = () => {\n    if (role === UserRole.ADMIN) {\n      return [{\n        value: \"QA\",\n        label: \"QA\"\n      }, {\n        value: \"ADMIN_MANAGER\",\n        label: \"Admin Manager\"\n      }, {\n        value: \"SUB_ADMIN\",\n        label: \"Sub Admin\"\n      }];\n    }\n    if (role === UserRole.BUSINESS) {\n      return [{\n        value: \"DEVELOPER\",\n        label: \"Security Engineer\"\n      }, {\n        value: \"BUSINESS_MANAGER\",\n        label: \"Program Manager\"\n      }, {\n        value: \"BUSINESS_ADMINISTRATOR\",\n        label: \"Organisation Admin\"\n      }];\n    }\n    return [];\n  };\n  const fetchInvitations = async () => {\n    try {\n      setIsLoading(true);\n      const data = await getInvitations();\n      setInvitations(data);\n      setIsError(false);\n    } catch (error) {\n      setIsError(true);\n      toast.error(\"Failed to load invitations\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const fetchActivityLogs = async () => {\n    try {\n      setLogsLoading(true);\n      const logs = await getActivityLogs();\n      setActivityLogs(logs);\n      setLogsError(false);\n    } catch (error) {\n      setLogsError(true);\n      toast.error(\"Failed to load activity logs\");\n    } finally {\n      setLogsLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchInvitations();\n  }, []);\n  useEffect(() => {\n    if (activeTab === \"activityLogs\") {\n      fetchActivityLogs();\n    }\n  }, [activeTab]);\n  const handleDeleteClick = id => {\n    setInvitationToDelete(id);\n    setIsDeleteModalOpen(true);\n  };\n  const handleConfirmDelete = async () => {\n    if (invitationToDelete !== null) {\n      try {\n        setIsProcessing(true);\n        await deleteInvitation(invitationToDelete);\n        await fetchInvitations();\n        toast.success(\"Invitation deleted successfully\");\n      } catch (error) {\n        toast.error(\"Failed to delete invitation\");\n      } finally {\n        setIsProcessing(false);\n        setIsDeleteModalOpen(false);\n        setInvitationToDelete(null);\n      }\n    }\n  };\n  const handleResendInvitation = async (email, role) => {\n    try {\n      setIsProcessing(true);\n      await createInvitation({\n        email,\n        role\n      });\n      await fetchInvitations();\n      toast.success(\"New invitation sent successfully\");\n    } catch (error) {\n      var _error$response, _error$response$data;\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || \"Failed to send invitation\");\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n  const handleRoleChange = async (invitationId, newRole) => {\n    setIsProcessing(true);\n    try {\n      await requestChangeUserRole({\n        invitation_id: invitationId,\n        new_role: newRole\n      });\n      await fetchInvitations();\n      toast.success(\"Role updated successfully\");\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      toast.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || \"Failed to update role\");\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n  const handleCreateInvitation = async () => {\n    await fetchInvitations();\n  };\n  if (!role || ![UserRole.ADMIN, UserRole.BUSINESS].includes(role)) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex min-h-screen items-center justify-center bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-lg bg-white p-8 text-center shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"mb-4 text-2xl font-bold text-red-600\",\n          children: \"Access Denied\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"You don't have permission to access this page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Filter and paginate invitations\n  const filteredInvitations = invitations.filter(invitation => {\n    const roleMatch = filterRole === \"all\" || UserRole[invitation.role] === filterRole;\n    const statusMatch = filterStatus === \"all\" || invitation.status.toLowerCase() === filterStatus.toLowerCase();\n    const searchMatch = invitation.invitee_email.toLowerCase().includes(searchQuery.toLowerCase());\n    return roleMatch && statusMatch && searchMatch;\n  });\n  const totalInvitationsPages = Math.ceil(filteredInvitations.length / itemsPerPage);\n  const paginatedInvitations = filteredInvitations.slice((currentInvitationsPage - 1) * itemsPerPage,\n  // Adjusted\n  currentInvitationsPage * itemsPerPage // Adjusted\n  );\n\n  // Filter and sort activity logs\n  const filteredLogs = activityLogs.filter(log => {\n    const searchMatch = log.user.email.toLowerCase().includes(logsSearchQuery.toLowerCase()) || log.user.username.toLowerCase().includes(logsSearchQuery.toLowerCase()) || log.action.toLowerCase().includes(logsSearchQuery.toLowerCase());\n    const roleMatch = filterLogsRole === \"all\" || UserRole[log.role] === filterLogsRole;\n    const moduleMatch = filterLogsModule === \"all\" || log.module === filterLogsModule;\n    return searchMatch && roleMatch && moduleMatch;\n  });\n  const sortedLogs = [...filteredLogs].sort((a, b) => {\n    const dateA = new Date(a.created_at).getTime();\n    const dateB = new Date(b.created_at).getTime();\n    return sortLogsOrder === \"newest\" ? dateB - dateA : dateA - dateB;\n  });\n  const totalLogsPages = Math.ceil(filteredLogs.length / itemsPerPage);\n  const paginatedLogs = sortedLogs.slice((currentLogsPage - 1) * itemsPerPage, currentLogsPage * itemsPerPage);\n\n  // Get unique roles and modules for filters\n  const uniqueRoles = Array.from(new Set(activityLogs.map(log => log.role)));\n  const uniqueModules = Array.from(new Set(activityLogs.map(log => log.module)));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 p-6 sm:p-10\",\n    children: [isProcessing && /*#__PURE__*/_jsxDEV(SpinnerOverlay, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 24\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-8xl mx-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8 rounded-xl bg-white p-6 shadow-xl\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"mb-4 border-b pb-4 text-3xl font-bold text-gray-900\",\n          children: \"Tenant and role management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabNavigation, {\n          activeTab: activeTab,\n          setActiveTab: tab => setActiveTab(tab),\n          tabs: [{\n            id: \"invitations\",\n            label: \"Manage Invitations\"\n          }, {\n            id: \"activityLogs\",\n            label: \"Activity Logs\"\n          }]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), activeTab === \"invitations\" ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"items-right flex justify-end text-right\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsPermissionsModalOpen(true),\n              className: \"rounded-md px-4 py-2 text-blue-700 transition-colors hover:text-blue-800 hover:underline\",\n              children: \"View role access guide\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CreateInvitationForm, {\n            availableRoles: availableRoles,\n            onInvitationCreated: handleCreateInvitation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(InvitationsFilters, {\n            searchQuery: searchQuery,\n            setSearchQuery: setSearchQuery,\n            filterRole: filterRole,\n            setFilterRole: setFilterRole,\n            filterStatus: filterStatus,\n            setFilterStatus: setFilterStatus,\n            roleFilterOptions: getRoleFilterOptions()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(InvitationsTable, {\n            invitations: paginatedInvitations,\n            isLoading: isLoading,\n            isError: isError,\n            availableRoles: availableRoles,\n            onRoleChange: handleRoleChange,\n            onDeleteClick: handleDeleteClick,\n            onResendInvitation: handleResendInvitation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Pagination, {\n            currentPage: currentInvitationsPage,\n            totalPages: totalInvitationsPages,\n            onPageChange: setCurrentInvitationsPage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(ActivityLogsFilters, {\n            searchQuery: logsSearchQuery,\n            setSearchQuery: setLogsSearchQuery,\n            filterRole: filterLogsRole,\n            setFilterRole: setFilterLogsRole,\n            filterModule: filterLogsModule,\n            setFilterModule: setFilterLogsModule,\n            sortOrder: sortLogsOrder,\n            setSortOrder: setSortLogsOrder,\n            availableRoles: uniqueRoles,\n            availableModules: uniqueModules\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ActivityLogsTable, {\n            logs: paginatedLogs,\n            isLoading: logsLoading,\n            isError: logsError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Pagination, {\n            currentPage: currentLogsPage,\n            totalPages: totalLogsPages,\n            onPageChange: setCurrentLogsPage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this), isPermissionsModalOpen && /*#__PURE__*/_jsxDEV(RolePermissionsModal, {\n      isOpen: isPermissionsModalOpen,\n      onClose: () => setIsPermissionsModalOpen(false),\n      userRole: role\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 9\n    }, this), isDeleteModalOpen && /*#__PURE__*/_jsxDEV(DeleteConfirmationModal, {\n      isOpen: isDeleteModalOpen,\n      onClose: () => setIsDeleteModalOpen(false),\n      onConfirm: handleConfirmDelete\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 265,\n    columnNumber: 5\n  }, this);\n};\n_s(InvitationsManager, \"GcrUeknDD5rsOV1ito/4C3ucLSM=\", false, function () {\n  return [useUserCredentials, usePageTitle];\n});\n_c = InvitationsManager;\nexport default InvitationsManager;\nvar _c;\n$RefreshReg$(_c, \"InvitationsManager\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "UserRole", "toast", "createInvitation", "deleteInvitation", "getInvitations", "requestChangeUserRole", "getActivityLogs", "useUserCredentials", "usePageTitle", "ROLE_OPTIONS", "Spinner<PERSON><PERSON><PERSON>", "DeleteConfirmationModal", "RolePermissionsModal", "TabNavigation", "CreateInvitationForm", "InvitationsTable", "InvitationsFilters", "ActivityLogsTable", "ActivityLogsFilters", "Pagination", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InvitationsManager", "_s", "role", "activeTab", "setActiveTab", "invitations", "setInvitations", "activityLogs", "setActivityLogs", "isLoading", "setIsLoading", "logsLoading", "setLogsLoading", "isError", "setIsError", "logsError", "setLogsError", "isProcessing", "setIsProcessing", "currentInvitationsPage", "setCurrentInvitationsPage", "currentLogsPage", "setCurrentLogsPage", "searchQuery", "setSearch<PERSON>uery", "filterRole", "setFilterRole", "filterStatus", "setFilterStatus", "logsSearchQuery", "setLogsSearchQuery", "filterLogsRole", "setFilterLogsRole", "filterLogsModule", "setFilterLogsModule", "sortLogsOrder", "setSortLogsOrder", "isPermissionsModalOpen", "setIsPermissionsModalOpen", "isDeleteModalOpen", "setIsDeleteModalOpen", "invitationToDelete", "setInvitationToDelete", "itemsPerPage", "availableRoles", "getRoleFilterOptions", "ADMIN", "value", "label", "BUSINESS", "fetchInvitations", "data", "error", "fetchActivityLogs", "logs", "handleDeleteClick", "id", "handleConfirmDelete", "success", "handleResendInvitation", "email", "_error$response", "_error$response$data", "response", "message", "handleRoleChange", "invitationId", "newRole", "invitation_id", "new_role", "_error$response2", "_error$response2$data", "handleCreateInvitation", "includes", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "filteredInvitations", "filter", "invitation", "roleMatch", "statusMatch", "status", "toLowerCase", "searchMatch", "invitee_email", "totalInvitationsPages", "Math", "ceil", "length", "paginatedInvitations", "slice", "filteredLogs", "log", "user", "username", "action", "moduleMatch", "module", "sortedLogs", "sort", "a", "b", "dateA", "Date", "created_at", "getTime", "dateB", "totalLogsPages", "paginatedLogs", "uniqueRoles", "Array", "from", "Set", "map", "uniqueModules", "tab", "tabs", "onClick", "onInvitationCreated", "roleFilterOptions", "onRoleChange", "onDeleteClick", "onResendInvitation", "currentPage", "totalPages", "onPageChange", "filterModule", "setFilterModule", "sortOrder", "setSortOrder", "availableModules", "isOpen", "onClose", "userRole", "onConfirm", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/jira-old/CTB-Frontend/src/components/users/InvitationManager.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { UserRole } from \"../../utils/api/endpoints/user/credentials\";\r\nimport toast from \"react-hot-toast\";\r\nimport {\r\n  createInvitation,\r\n  deleteInvitation,\r\n  getInvitations\r\n} from \"../../utils/api/endpoints/user/invitation\";\r\nimport {\r\n  requestChangeUserRole,\r\n  getActivityLogs\r\n} from \"../../utils/api/endpoints/user/multiTenant\";\r\nimport useUserCredentials from \"../../utils/hooks/user/useUserCredentials\";\r\nimport usePageTitle from \"../../utils/hooks/usePageTitle\";\r\n\r\n// Import types\r\nimport {\r\n  Invitation,\r\n  ActivityLog,\r\n  ROLE_OPTIONS\r\n} from \"../../utils/hooks/multi-tenant/invitation\";\r\n\r\n// Import components\r\nimport SpinnerOverlay from \"../multi_tenant/SpinnerOverlay\";\r\nimport DeleteConfirmationModal from \"../multi_tenant/DeleteConfirmationModal\";\r\nimport RolePermissionsModal from \"../multi_tenant/RolePermissionsModal\";\r\nimport TabNavigation from \"../multi_tenant/TabNavigation\";\r\nimport CreateInvitationForm from \"../multi_tenant/CreateInvitationForm\";\r\nimport InvitationsTable from \"../multi_tenant/InvitationsTable\";\r\nimport InvitationsFilters from \"../multi_tenant/InvitationsFilters\";\r\nimport ActivityLogsTable from \"../multi_tenant/ActivityLogsTable\";\r\nimport ActivityLogsFilters from \"../multi_tenant/ActivityLogsFilters\";\r\nimport Pagination from \"../retests/utils/Pagination\";\r\n\r\nconst InvitationsManager: React.FC = () => {\r\n  const { role } = useUserCredentials();\r\n  const [activeTab, setActiveTab] = useState<\"invitations\" | \"activityLogs\">(\r\n    \"invitations\"\r\n  );\r\n  const [invitations, setInvitations] = useState<Invitation[]>([]);\r\n  const [activityLogs, setActivityLogs] = useState<ActivityLog[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [logsLoading, setLogsLoading] = useState(false);\r\n  const [isError, setIsError] = useState(false);\r\n  const [logsError, setLogsError] = useState(false);\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n  const [currentInvitationsPage, setCurrentInvitationsPage] = useState(1);\r\n  const [currentLogsPage, setCurrentLogsPage] = useState(1);\r\n\r\n  // Invitations filters\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [filterRole, setFilterRole] = useState<string>(\"all\");\r\n  const [filterStatus, setFilterStatus] = useState<string>(\"all\");\r\n\r\n  // Activity logs filters\r\n  const [logsSearchQuery, setLogsSearchQuery] = useState(\"\");\r\n  const [filterLogsRole, setFilterLogsRole] = useState(\"all\");\r\n  const [filterLogsModule, setFilterLogsModule] = useState(\"all\");\r\n  const [sortLogsOrder, setSortLogsOrder] = useState<\"newest\" | \"oldest\">(\r\n    \"newest\"\r\n  );\r\n\r\n  // Modals\r\n  const [isPermissionsModalOpen, setIsPermissionsModalOpen] = useState(false);\r\n  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);\r\n  const [invitationToDelete, setInvitationToDelete] = useState<number | null>(\r\n    null\r\n  );\r\n\r\n  const itemsPerPage = 10;\r\n  const availableRoles = role ? ROLE_OPTIONS[role] : [];\r\n\r\n  usePageTitle(\"Tenant and role management | Capture The Bug\");\r\n\r\n  useEffect(() => {\r\n    setCurrentInvitationsPage(1);\r\n  }, [searchQuery, filterRole, filterStatus]);\r\n\r\n  useEffect(() => {\r\n    setCurrentLogsPage(1);\r\n  }, [logsSearchQuery, filterLogsRole, filterLogsModule, sortLogsOrder]);\r\n\r\n  const getRoleFilterOptions = () => {\r\n    if (role === UserRole.ADMIN) {\r\n      return [\r\n        { value: \"QA\", label: \"QA\" },\r\n        { value: \"ADMIN_MANAGER\", label: \"Admin Manager\" },\r\n        { value: \"SUB_ADMIN\", label: \"Sub Admin\" }\r\n      ];\r\n    }\r\n    if (role === UserRole.BUSINESS) {\r\n      return [\r\n        { value: \"DEVELOPER\", label: \"Security Engineer\" },\r\n        { value: \"BUSINESS_MANAGER\", label: \"Program Manager\" },\r\n        { value: \"BUSINESS_ADMINISTRATOR\", label: \"Organisation Admin\" }\r\n      ];\r\n    }\r\n    return [];\r\n  };\r\n\r\n  const fetchInvitations = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      const data = await getInvitations();\r\n      setInvitations(data);\r\n      setIsError(false);\r\n    } catch (error) {\r\n      setIsError(true);\r\n      toast.error(\"Failed to load invitations\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const fetchActivityLogs = async () => {\r\n    try {\r\n      setLogsLoading(true);\r\n      const logs = await getActivityLogs();\r\n      setActivityLogs(logs);\r\n      setLogsError(false);\r\n    } catch (error) {\r\n      setLogsError(true);\r\n      toast.error(\"Failed to load activity logs\");\r\n    } finally {\r\n      setLogsLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchInvitations();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (activeTab === \"activityLogs\") {\r\n      fetchActivityLogs();\r\n    }\r\n  }, [activeTab]);\r\n\r\n  const handleDeleteClick = (id: number) => {\r\n    setInvitationToDelete(id);\r\n    setIsDeleteModalOpen(true);\r\n  };\r\n\r\n  const handleConfirmDelete = async () => {\r\n    if (invitationToDelete !== null) {\r\n      try {\r\n        setIsProcessing(true);\r\n        await deleteInvitation(invitationToDelete);\r\n        await fetchInvitations();\r\n        toast.success(\"Invitation deleted successfully\");\r\n      } catch (error) {\r\n        toast.error(\"Failed to delete invitation\");\r\n      } finally {\r\n        setIsProcessing(false);\r\n        setIsDeleteModalOpen(false);\r\n        setInvitationToDelete(null);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleResendInvitation = async (email: string, role: UserRole) => {\r\n    try {\r\n      setIsProcessing(true);\r\n      await createInvitation({ email, role });\r\n      await fetchInvitations();\r\n      toast.success(\"New invitation sent successfully\");\r\n    } catch (error: any) {\r\n      toast.error(error.response?.data?.message || \"Failed to send invitation\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  const handleRoleChange = async (invitationId: number, newRole: UserRole) => {\r\n    setIsProcessing(true);\r\n    try {\r\n      await requestChangeUserRole({\r\n        invitation_id: invitationId,\r\n        new_role: newRole\r\n      });\r\n      await fetchInvitations();\r\n      toast.success(\"Role updated successfully\");\r\n    } catch (error: any) {\r\n      toast.error(error.response?.data?.message || \"Failed to update role\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  const handleCreateInvitation = async () => {\r\n    await fetchInvitations();\r\n  };\r\n\r\n  if (!role || ![UserRole.ADMIN, UserRole.BUSINESS].includes(role)) {\r\n    return (\r\n      <div className=\"flex min-h-screen items-center justify-center bg-gray-50\">\r\n        <div className=\"rounded-lg bg-white p-8 text-center shadow-lg\">\r\n          <h2 className=\"mb-4 text-2xl font-bold text-red-600\">\r\n            Access Denied\r\n          </h2>\r\n          <p className=\"text-gray-600\">\r\n            You don't have permission to access this page.\r\n          </p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Filter and paginate invitations\r\n  const filteredInvitations = invitations.filter(invitation => {\r\n    const roleMatch =\r\n      filterRole === \"all\" || UserRole[invitation.role] === filterRole;\r\n    const statusMatch =\r\n      filterStatus === \"all\" ||\r\n      invitation.status.toLowerCase() === filterStatus.toLowerCase();\r\n    const searchMatch = invitation.invitee_email\r\n      .toLowerCase()\r\n      .includes(searchQuery.toLowerCase());\r\n    return roleMatch && statusMatch && searchMatch;\r\n  });\r\n\r\n  const totalInvitationsPages = Math.ceil(\r\n    filteredInvitations.length / itemsPerPage\r\n  );\r\n  const paginatedInvitations = filteredInvitations.slice(\r\n    (currentInvitationsPage - 1) * itemsPerPage, // Adjusted\r\n    currentInvitationsPage * itemsPerPage // Adjusted\r\n  );\r\n\r\n  // Filter and sort activity logs\r\n  const filteredLogs = activityLogs.filter(log => {\r\n    const searchMatch =\r\n      log.user.email.toLowerCase().includes(logsSearchQuery.toLowerCase()) ||\r\n      log.user.username.toLowerCase().includes(logsSearchQuery.toLowerCase()) ||\r\n      log.action.toLowerCase().includes(logsSearchQuery.toLowerCase());\r\n    const roleMatch =\r\n      filterLogsRole === \"all\" || UserRole[log.role] === filterLogsRole;\r\n    const moduleMatch =\r\n      filterLogsModule === \"all\" || log.module === filterLogsModule;\r\n    return searchMatch && roleMatch && moduleMatch;\r\n  });\r\n\r\n  const sortedLogs = [...filteredLogs].sort((a, b) => {\r\n    const dateA = new Date(a.created_at).getTime();\r\n    const dateB = new Date(b.created_at).getTime();\r\n    return sortLogsOrder === \"newest\" ? dateB - dateA : dateA - dateB;\r\n  });\r\n\r\n  const totalLogsPages = Math.ceil(filteredLogs.length / itemsPerPage);\r\n  const paginatedLogs = sortedLogs.slice(\r\n    (currentLogsPage - 1) * itemsPerPage,\r\n    currentLogsPage * itemsPerPage\r\n  );\r\n\r\n  // Get unique roles and modules for filters\r\n  const uniqueRoles = Array.from(\r\n    new Set(activityLogs.map(log => log.role))\r\n  ) as UserRole[];\r\n\r\n  const uniqueModules = Array.from(\r\n    new Set(activityLogs.map(log => log.module))\r\n  );\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50 p-6 sm:p-10\">\r\n      {isProcessing && <SpinnerOverlay />}\r\n      <div className=\"max-w-8xl mx-auto\">\r\n        <div className=\"mb-8 rounded-xl bg-white p-6 shadow-xl\">\r\n          <h2 className=\"mb-4 border-b pb-4 text-3xl font-bold text-gray-900\">\r\n            Tenant and role management\r\n          </h2>\r\n\r\n          <TabNavigation\r\n            activeTab={activeTab}\r\n            setActiveTab={tab =>\r\n              setActiveTab(tab as \"invitations\" | \"activityLogs\")\r\n            }\r\n            tabs={[\r\n              { id: \"invitations\", label: \"Manage Invitations\" },\r\n              { id: \"activityLogs\", label: \"Activity Logs\" }\r\n            ]}\r\n          />\r\n\r\n          {activeTab === \"invitations\" ? (\r\n            <>\r\n              <div className=\"items-right flex justify-end text-right\">\r\n                <button\r\n                  onClick={() => setIsPermissionsModalOpen(true)}\r\n                  className=\"rounded-md px-4 py-2 text-blue-700 transition-colors hover:text-blue-800 hover:underline\"\r\n                >\r\n                  View role access guide\r\n                </button>\r\n              </div>\r\n\r\n              <CreateInvitationForm\r\n                availableRoles={availableRoles}\r\n                onInvitationCreated={handleCreateInvitation}\r\n              />\r\n\r\n              <InvitationsFilters\r\n                searchQuery={searchQuery}\r\n                setSearchQuery={setSearchQuery}\r\n                filterRole={filterRole}\r\n                setFilterRole={setFilterRole}\r\n                filterStatus={filterStatus}\r\n                setFilterStatus={setFilterStatus}\r\n                roleFilterOptions={getRoleFilterOptions()}\r\n              />\r\n\r\n              <InvitationsTable\r\n                invitations={paginatedInvitations}\r\n                isLoading={isLoading}\r\n                isError={isError}\r\n                availableRoles={availableRoles}\r\n                onRoleChange={handleRoleChange}\r\n                onDeleteClick={handleDeleteClick}\r\n                onResendInvitation={handleResendInvitation}\r\n              />\r\n\r\n              <Pagination\r\n                currentPage={currentInvitationsPage}\r\n                totalPages={totalInvitationsPages}\r\n                onPageChange={setCurrentInvitationsPage}\r\n              />\r\n            </>\r\n          ) : (\r\n            <>\r\n              <ActivityLogsFilters\r\n                searchQuery={logsSearchQuery}\r\n                setSearchQuery={setLogsSearchQuery}\r\n                filterRole={filterLogsRole}\r\n                setFilterRole={setFilterLogsRole}\r\n                filterModule={filterLogsModule}\r\n                setFilterModule={setFilterLogsModule}\r\n                sortOrder={sortLogsOrder}\r\n                setSortOrder={setSortLogsOrder}\r\n                availableRoles={uniqueRoles}\r\n                availableModules={uniqueModules}\r\n              />\r\n\r\n              <ActivityLogsTable\r\n                logs={paginatedLogs}\r\n                isLoading={logsLoading}\r\n                isError={logsError}\r\n              />\r\n\r\n              <Pagination\r\n                currentPage={currentLogsPage}\r\n                totalPages={totalLogsPages}\r\n                onPageChange={setCurrentLogsPage}\r\n              />\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {isPermissionsModalOpen && (\r\n        <RolePermissionsModal\r\n          isOpen={isPermissionsModalOpen}\r\n          onClose={() => setIsPermissionsModalOpen(false)}\r\n          userRole={role}\r\n        />\r\n      )}\r\n\r\n      {isDeleteModalOpen && (\r\n        <DeleteConfirmationModal\r\n          isOpen={isDeleteModalOpen}\r\n          onClose={() => setIsDeleteModalOpen(false)}\r\n          onConfirm={handleConfirmDelete}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InvitationsManager;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,QAAQ,QAAQ,4CAA4C;AACrE,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SACEC,gBAAgB,EAChBC,gBAAgB,EAChBC,cAAc,QACT,2CAA2C;AAClD,SACEC,qBAAqB,EACrBC,eAAe,QACV,4CAA4C;AACnD,OAAOC,kBAAkB,MAAM,2CAA2C;AAC1E,OAAOC,YAAY,MAAM,gCAAgC;;AAEzD;AACA,SAGEC,YAAY,QACP,2CAA2C;;AAElD;AACA,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,uBAAuB,MAAM,yCAAyC;AAC7E,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,mBAAmB,MAAM,qCAAqC;AACrE,OAAOC,UAAU,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM;IAAEC;EAAK,CAAC,GAAGnB,kBAAkB,CAAC,CAAC;EACrC,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CACxC,aACF,CAAC;EACD,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4C,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG7C,QAAQ,CAAC,CAAC,CAAC;EACvE,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAC,CAAC,CAAC;;EAEzD;EACA,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAS,KAAK,CAAC;EAC3D,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAS,KAAK,CAAC;;EAE/D;EACA,MAAM,CAACsD,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC0D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC4D,aAAa,EAAEC,gBAAgB,CAAC,GAAG7D,QAAQ,CAChD,QACF,CAAC;;EAED;EACA,MAAM,CAAC8D,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACgE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACkE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnE,QAAQ,CAC1D,IACF,CAAC;EAED,MAAMoE,YAAY,GAAG,EAAE;EACvB,MAAMC,cAAc,GAAG1C,IAAI,GAAGjB,YAAY,CAACiB,IAAI,CAAC,GAAG,EAAE;EAErDlB,YAAY,CAAC,8CAA8C,CAAC;EAE5DV,SAAS,CAAC,MAAM;IACd8C,yBAAyB,CAAC,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACG,WAAW,EAAEE,UAAU,EAAEE,YAAY,CAAC,CAAC;EAE3CrD,SAAS,CAAC,MAAM;IACdgD,kBAAkB,CAAC,CAAC,CAAC;EACvB,CAAC,EAAE,CAACO,eAAe,EAAEE,cAAc,EAAEE,gBAAgB,EAAEE,aAAa,CAAC,CAAC;EAEtE,MAAMU,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI3C,IAAI,KAAK1B,QAAQ,CAACsE,KAAK,EAAE;MAC3B,OAAO,CACL;QAAEC,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAK,CAAC,EAC5B;QAAED,KAAK,EAAE,eAAe;QAAEC,KAAK,EAAE;MAAgB,CAAC,EAClD;QAAED,KAAK,EAAE,WAAW;QAAEC,KAAK,EAAE;MAAY,CAAC,CAC3C;IACH;IACA,IAAI9C,IAAI,KAAK1B,QAAQ,CAACyE,QAAQ,EAAE;MAC9B,OAAO,CACL;QAAEF,KAAK,EAAE,WAAW;QAAEC,KAAK,EAAE;MAAoB,CAAC,EAClD;QAAED,KAAK,EAAE,kBAAkB;QAAEC,KAAK,EAAE;MAAkB,CAAC,EACvD;QAAED,KAAK,EAAE,wBAAwB;QAAEC,KAAK,EAAE;MAAqB,CAAC,CACjE;IACH;IACA,OAAO,EAAE;EACX,CAAC;EAED,MAAME,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFxC,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMyC,IAAI,GAAG,MAAMvE,cAAc,CAAC,CAAC;MACnC0B,cAAc,CAAC6C,IAAI,CAAC;MACpBrC,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOsC,KAAK,EAAE;MACdtC,UAAU,CAAC,IAAI,CAAC;MAChBrC,KAAK,CAAC2E,KAAK,CAAC,4BAA4B,CAAC;IAC3C,CAAC,SAAS;MACR1C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM2C,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFzC,cAAc,CAAC,IAAI,CAAC;MACpB,MAAM0C,IAAI,GAAG,MAAMxE,eAAe,CAAC,CAAC;MACpC0B,eAAe,CAAC8C,IAAI,CAAC;MACrBtC,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACdpC,YAAY,CAAC,IAAI,CAAC;MAClBvC,KAAK,CAAC2E,KAAK,CAAC,8BAA8B,CAAC;IAC7C,CAAC,SAAS;MACRxC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAEDtC,SAAS,CAAC,MAAM;IACd4E,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN5E,SAAS,CAAC,MAAM;IACd,IAAI6B,SAAS,KAAK,cAAc,EAAE;MAChCkD,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAAClD,SAAS,CAAC,CAAC;EAEf,MAAMoD,iBAAiB,GAAIC,EAAU,IAAK;IACxCd,qBAAqB,CAACc,EAAE,CAAC;IACzBhB,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMiB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAIhB,kBAAkB,KAAK,IAAI,EAAE;MAC/B,IAAI;QACFvB,eAAe,CAAC,IAAI,CAAC;QACrB,MAAMvC,gBAAgB,CAAC8D,kBAAkB,CAAC;QAC1C,MAAMS,gBAAgB,CAAC,CAAC;QACxBzE,KAAK,CAACiF,OAAO,CAAC,iCAAiC,CAAC;MAClD,CAAC,CAAC,OAAON,KAAK,EAAE;QACd3E,KAAK,CAAC2E,KAAK,CAAC,6BAA6B,CAAC;MAC5C,CAAC,SAAS;QACRlC,eAAe,CAAC,KAAK,CAAC;QACtBsB,oBAAoB,CAAC,KAAK,CAAC;QAC3BE,qBAAqB,CAAC,IAAI,CAAC;MAC7B;IACF;EACF,CAAC;EAED,MAAMiB,sBAAsB,GAAG,MAAAA,CAAOC,KAAa,EAAE1D,IAAc,KAAK;IACtE,IAAI;MACFgB,eAAe,CAAC,IAAI,CAAC;MACrB,MAAMxC,gBAAgB,CAAC;QAAEkF,KAAK;QAAE1D;MAAK,CAAC,CAAC;MACvC,MAAMgD,gBAAgB,CAAC,CAAC;MACxBzE,KAAK,CAACiF,OAAO,CAAC,kCAAkC,CAAC;IACnD,CAAC,CAAC,OAAON,KAAU,EAAE;MAAA,IAAAS,eAAA,EAAAC,oBAAA;MACnBrF,KAAK,CAAC2E,KAAK,CAAC,EAAAS,eAAA,GAAAT,KAAK,CAACW,QAAQ,cAAAF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBV,IAAI,cAAAW,oBAAA,uBAApBA,oBAAA,CAAsBE,OAAO,KAAI,2BAA2B,CAAC;IAC3E,CAAC,SAAS;MACR9C,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAM+C,gBAAgB,GAAG,MAAAA,CAAOC,YAAoB,EAAEC,OAAiB,KAAK;IAC1EjD,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMrC,qBAAqB,CAAC;QAC1BuF,aAAa,EAAEF,YAAY;QAC3BG,QAAQ,EAAEF;MACZ,CAAC,CAAC;MACF,MAAMjB,gBAAgB,CAAC,CAAC;MACxBzE,KAAK,CAACiF,OAAO,CAAC,2BAA2B,CAAC;IAC5C,CAAC,CAAC,OAAON,KAAU,EAAE;MAAA,IAAAkB,gBAAA,EAAAC,qBAAA;MACnB9F,KAAK,CAAC2E,KAAK,CAAC,EAAAkB,gBAAA,GAAAlB,KAAK,CAACW,QAAQ,cAAAO,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnB,IAAI,cAAAoB,qBAAA,uBAApBA,qBAAA,CAAsBP,OAAO,KAAI,uBAAuB,CAAC;IACvE,CAAC,SAAS;MACR9C,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMsD,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,MAAMtB,gBAAgB,CAAC,CAAC;EAC1B,CAAC;EAED,IAAI,CAAChD,IAAI,IAAI,CAAC,CAAC1B,QAAQ,CAACsE,KAAK,EAAEtE,QAAQ,CAACyE,QAAQ,CAAC,CAACwB,QAAQ,CAACvE,IAAI,CAAC,EAAE;IAChE,oBACEL,OAAA;MAAK6E,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvE9E,OAAA;QAAK6E,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAC5D9E,OAAA;UAAI6E,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlF,OAAA;UAAG6E,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE7B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAMC,mBAAmB,GAAG3E,WAAW,CAAC4E,MAAM,CAACC,UAAU,IAAI;IAC3D,MAAMC,SAAS,GACb1D,UAAU,KAAK,KAAK,IAAIjD,QAAQ,CAAC0G,UAAU,CAAChF,IAAI,CAAC,KAAKuB,UAAU;IAClE,MAAM2D,WAAW,GACfzD,YAAY,KAAK,KAAK,IACtBuD,UAAU,CAACG,MAAM,CAACC,WAAW,CAAC,CAAC,KAAK3D,YAAY,CAAC2D,WAAW,CAAC,CAAC;IAChE,MAAMC,WAAW,GAAGL,UAAU,CAACM,aAAa,CACzCF,WAAW,CAAC,CAAC,CACbb,QAAQ,CAAClD,WAAW,CAAC+D,WAAW,CAAC,CAAC,CAAC;IACtC,OAAOH,SAAS,IAAIC,WAAW,IAAIG,WAAW;EAChD,CAAC,CAAC;EAEF,MAAME,qBAAqB,GAAGC,IAAI,CAACC,IAAI,CACrCX,mBAAmB,CAACY,MAAM,GAAGjD,YAC/B,CAAC;EACD,MAAMkD,oBAAoB,GAAGb,mBAAmB,CAACc,KAAK,CACpD,CAAC3E,sBAAsB,GAAG,CAAC,IAAIwB,YAAY;EAAE;EAC7CxB,sBAAsB,GAAGwB,YAAY,CAAC;EACxC,CAAC;;EAED;EACA,MAAMoD,YAAY,GAAGxF,YAAY,CAAC0E,MAAM,CAACe,GAAG,IAAI;IAC9C,MAAMT,WAAW,GACfS,GAAG,CAACC,IAAI,CAACrC,KAAK,CAAC0B,WAAW,CAAC,CAAC,CAACb,QAAQ,CAAC5C,eAAe,CAACyD,WAAW,CAAC,CAAC,CAAC,IACpEU,GAAG,CAACC,IAAI,CAACC,QAAQ,CAACZ,WAAW,CAAC,CAAC,CAACb,QAAQ,CAAC5C,eAAe,CAACyD,WAAW,CAAC,CAAC,CAAC,IACvEU,GAAG,CAACG,MAAM,CAACb,WAAW,CAAC,CAAC,CAACb,QAAQ,CAAC5C,eAAe,CAACyD,WAAW,CAAC,CAAC,CAAC;IAClE,MAAMH,SAAS,GACbpD,cAAc,KAAK,KAAK,IAAIvD,QAAQ,CAACwH,GAAG,CAAC9F,IAAI,CAAC,KAAK6B,cAAc;IACnE,MAAMqE,WAAW,GACfnE,gBAAgB,KAAK,KAAK,IAAI+D,GAAG,CAACK,MAAM,KAAKpE,gBAAgB;IAC/D,OAAOsD,WAAW,IAAIJ,SAAS,IAAIiB,WAAW;EAChD,CAAC,CAAC;EAEF,MAAME,UAAU,GAAG,CAAC,GAAGP,YAAY,CAAC,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAClD,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAACH,CAAC,CAACI,UAAU,CAAC,CAACC,OAAO,CAAC,CAAC;IAC9C,MAAMC,KAAK,GAAG,IAAIH,IAAI,CAACF,CAAC,CAACG,UAAU,CAAC,CAACC,OAAO,CAAC,CAAC;IAC9C,OAAO1E,aAAa,KAAK,QAAQ,GAAG2E,KAAK,GAAGJ,KAAK,GAAGA,KAAK,GAAGI,KAAK;EACnE,CAAC,CAAC;EAEF,MAAMC,cAAc,GAAGrB,IAAI,CAACC,IAAI,CAACI,YAAY,CAACH,MAAM,GAAGjD,YAAY,CAAC;EACpE,MAAMqE,aAAa,GAAGV,UAAU,CAACR,KAAK,CACpC,CAACzE,eAAe,GAAG,CAAC,IAAIsB,YAAY,EACpCtB,eAAe,GAAGsB,YACpB,CAAC;;EAED;EACA,MAAMsE,WAAW,GAAGC,KAAK,CAACC,IAAI,CAC5B,IAAIC,GAAG,CAAC7G,YAAY,CAAC8G,GAAG,CAACrB,GAAG,IAAIA,GAAG,CAAC9F,IAAI,CAAC,CAC3C,CAAe;EAEf,MAAMoH,aAAa,GAAGJ,KAAK,CAACC,IAAI,CAC9B,IAAIC,GAAG,CAAC7G,YAAY,CAAC8G,GAAG,CAACrB,GAAG,IAAIA,GAAG,CAACK,MAAM,CAAC,CAC7C,CAAC;EAED,oBACExG,OAAA;IAAK6E,SAAS,EAAC,qCAAqC;IAAAC,QAAA,GACjD1D,YAAY,iBAAIpB,OAAA,CAACX,cAAc;MAAA0F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnClF,OAAA;MAAK6E,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChC9E,OAAA;QAAK6E,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD9E,OAAA;UAAI6E,SAAS,EAAC,qDAAqD;UAAAC,QAAA,EAAC;QAEpE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELlF,OAAA,CAACR,aAAa;UACZc,SAAS,EAAEA,SAAU;UACrBC,YAAY,EAAEmH,GAAG,IACfnH,YAAY,CAACmH,GAAqC,CACnD;UACDC,IAAI,EAAE,CACJ;YAAEhE,EAAE,EAAE,aAAa;YAAER,KAAK,EAAE;UAAqB,CAAC,EAClD;YAAEQ,EAAE,EAAE,cAAc;YAAER,KAAK,EAAE;UAAgB,CAAC;QAC9C;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAED5E,SAAS,KAAK,aAAa,gBAC1BN,OAAA,CAAAE,SAAA;UAAA4E,QAAA,gBACE9E,OAAA;YAAK6E,SAAS,EAAC,yCAAyC;YAAAC,QAAA,eACtD9E,OAAA;cACE4H,OAAO,EAAEA,CAAA,KAAMnF,yBAAyB,CAAC,IAAI,CAAE;cAC/CoC,SAAS,EAAC,0FAA0F;cAAAC,QAAA,EACrG;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENlF,OAAA,CAACP,oBAAoB;YACnBsD,cAAc,EAAEA,cAAe;YAC/B8E,mBAAmB,EAAElD;UAAuB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAEFlF,OAAA,CAACL,kBAAkB;YACjB+B,WAAW,EAAEA,WAAY;YACzBC,cAAc,EAAEA,cAAe;YAC/BC,UAAU,EAAEA,UAAW;YACvBC,aAAa,EAAEA,aAAc;YAC7BC,YAAY,EAAEA,YAAa;YAC3BC,eAAe,EAAEA,eAAgB;YACjC+F,iBAAiB,EAAE9E,oBAAoB,CAAC;UAAE;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAEFlF,OAAA,CAACN,gBAAgB;YACfc,WAAW,EAAEwF,oBAAqB;YAClCpF,SAAS,EAAEA,SAAU;YACrBI,OAAO,EAAEA,OAAQ;YACjB+B,cAAc,EAAEA,cAAe;YAC/BgF,YAAY,EAAE3D,gBAAiB;YAC/B4D,aAAa,EAAEtE,iBAAkB;YACjCuE,kBAAkB,EAAEnE;UAAuB;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eAEFlF,OAAA,CAACF,UAAU;YACToI,WAAW,EAAE5G,sBAAuB;YACpC6G,UAAU,EAAEvC,qBAAsB;YAClCwC,YAAY,EAAE7G;UAA0B;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA,eACF,CAAC,gBAEHlF,OAAA,CAAAE,SAAA;UAAA4E,QAAA,gBACE9E,OAAA,CAACH,mBAAmB;YAClB6B,WAAW,EAAEM,eAAgB;YAC7BL,cAAc,EAAEM,kBAAmB;YACnCL,UAAU,EAAEM,cAAe;YAC3BL,aAAa,EAAEM,iBAAkB;YACjCkG,YAAY,EAAEjG,gBAAiB;YAC/BkG,eAAe,EAAEjG,mBAAoB;YACrCkG,SAAS,EAAEjG,aAAc;YACzBkG,YAAY,EAAEjG,gBAAiB;YAC/BQ,cAAc,EAAEqE,WAAY;YAC5BqB,gBAAgB,EAAEhB;UAAc;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eAEFlF,OAAA,CAACJ,iBAAiB;YAChB6D,IAAI,EAAE0D,aAAc;YACpBvG,SAAS,EAAEE,WAAY;YACvBE,OAAO,EAAEE;UAAU;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eAEFlF,OAAA,CAACF,UAAU;YACToI,WAAW,EAAE1G,eAAgB;YAC7B2G,UAAU,EAAEjB,cAAe;YAC3BkB,YAAY,EAAE3G;UAAmB;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA,eACF,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL1C,sBAAsB,iBACrBxC,OAAA,CAACT,oBAAoB;MACnBmJ,MAAM,EAAElG,sBAAuB;MAC/BmG,OAAO,EAAEA,CAAA,KAAMlG,yBAAyB,CAAC,KAAK,CAAE;MAChDmG,QAAQ,EAAEvI;IAAK;MAAA0E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CACF,EAEAxC,iBAAiB,iBAChB1C,OAAA,CAACV,uBAAuB;MACtBoJ,MAAM,EAAEhG,iBAAkB;MAC1BiG,OAAO,EAAEA,CAAA,KAAMhG,oBAAoB,CAAC,KAAK,CAAE;MAC3CkG,SAAS,EAAEjF;IAAoB;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9E,EAAA,CAnVID,kBAA4B;EAAA,QACfjB,kBAAkB,EAqCnCC,YAAY;AAAA;AAAA2J,EAAA,GAtCR3I,kBAA4B;AAqVlC,eAAeA,kBAAkB;AAAC,IAAA2I,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}