import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom"; 
import { getScopeStats } from "../../../utils/api/endpoints/reports/scopeStats";
import { getProgramTargets } from "../../../utils/api/endpoints/reports/target";
import SortArrows from "../../../assets/icons/SortArrows";
import { IoMdSearch } from "react-icons/io";
import { ScopeStatsInfo, TargetsInfo } from "../../../utils/api/endpoints/reports/parseTarget";
import Pagination from "../../../components/retests/utils/Pagination";

const ScopeTable: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  const [scopeData, setScopeData] = useState<ScopeStatsInfo | null>(null);
  const [targetData, setTargetData] = useState<TargetsInfo | null>(null);
  const [mergedData, setMergedData] = useState<
    { targetName: string; targetType: string; reportCount: number; lastSubmitted: string }[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [typeFilter, setTypeFilter] = useState("");
  const [resolveFilter, setResolveFilter] = useState("");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [showDropdown, setShowDropdown] = useState(false);
  const [scopeSuggestions, setScopeSuggestions] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 12;
  

  useEffect(() => {
    const fetchData = async () => {
      if (!id) return;
      setLoading(true);

      try {
        const [scopeResponse, targetResponse] = await Promise.all([
          getScopeStats(Number(id)),
          getProgramTargets(Number(id)),
        ]);

        setScopeData(scopeResponse);
        setTargetData(targetResponse);

        if (targetResponse?.targets && scopeResponse?.scopes) {
          const merged = targetResponse.targets.map((target: { targetName: string; targetType: any; }) => {
            const matchingScope = scopeResponse.scopes.find(
              (scope) => scope.name === target.targetName
            );

            return {
              targetName: target.targetName || "Unknown",
              targetType: target.targetType || "N/A",
              reportCount: matchingScope ? matchingScope.reportCount : 0,
              lastSubmitted: matchingScope
                ? new Date(matchingScope.lastSubmitted).toLocaleDateString()
                : "—",
            };
          });

          setMergedData(merged);
          const uniqueScopes: string[] = Array.from(new Set(merged.map((item: { targetName: string; }) => item.targetName)));
          setScopeSuggestions(uniqueScopes);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);
 
  const filteredData = mergedData
    .filter((item) =>
      item.targetName.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .filter((item) => (typeFilter ? item.targetType === typeFilter : true))
    .filter((item) =>
      resolveFilter ? item.reportCount.toString() === resolveFilter : true
    )
    .sort((a, b) => {
      return sortOrder === "asc"
        ? a.reportCount - b.reportCount
        : b.reportCount - a.reportCount;
    });

    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredData.slice(indexOfFirstItem, indexOfLastItem);


  return (
    <div className="container"> 
      <div className="flex flex-wrap items-center gap-4 mb-4 relative">
        <div className="relative w-[440px] z-20">
          <IoMdSearch className="absolute top-1/2 left-3 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search"
            className="border p-2 pl-8 rounded w-full outline-none"
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setShowDropdown(e.target.value.length > 0);  
            }}
            onBlur={() => setTimeout(() => setShowDropdown(false), 200)}  
          />
          {showDropdown && searchQuery.length > 0 && (
            <div className="absolute z-50 mt-1 bg-white border rounded shadow-md w-full max-h-40 overflow-auto">
              {scopeSuggestions
                .filter((scope) => scope.toLowerCase().includes(searchQuery.toLowerCase()))
                .map((scope, index) => (
                  <div
                    key={index}
                    className="px-3 py-2 hover:bg-gray-200 cursor-pointer"
                    onMouseDown={() => {
                      setSearchQuery(scope);
                      setShowDropdown(false);
                    }}
                  >
                    {scope}
                  </div>
                ))}
            </div>
          )}
        </div>

        <select
          className="border p-2 rounded w-[150px]"
          value={typeFilter}
          onChange={(e) => setTypeFilter(e.target.value)}
        >
          <option value="">Type</option>
          {Array.from(new Set(mergedData.map((item) => item.targetType))).map(
            (type) => (
              <option key={type} value={type}>
                {type}
              </option>
            )
          )}
        </select>

        <select
          className="border p-2 rounded w-[150px]"
          value={resolveFilter}
          onChange={(e) => setResolveFilter(e.target.value)}
        >
          <option value="">No of Resolve</option>
          {Array.from(new Set(mergedData.map((item) => item.reportCount))).map(
            (count) => (
              <option key={count} value={count}>
                {count}
              </option>
            )
          )}
        </select>

        <button
          className="border p-2 rounded flex items-center bg-white gap-2"
          onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
        >
          <SortArrows className="w-4 h-4 text-black" />
          {sortOrder === "asc" ? "Sort by Reports" : "Sort by Reports"}
        </button>
      </div>
 
      <div className="overflow-hidden rounded-lg shadow-lg border border-gray-200 w-full max-h-[25rem] overflow-y-auto">
  <table className="w-full border-collapse">
    <thead className="bg-blue-700 text-white sticky top-0 z-10">
      <tr>
        <th className="px-7 py-3 text-left w-1/4">Asset</th>
        <th className="px-7 py-3 text-left w-1/4">Type</th>
        <th className="px-5 py-3 text-left w-1/4">Resolved Report</th>
        <th className="px-6 py-3 text-left w-1/4">Last Update</th>
      </tr>
    </thead>
    <tbody>
      {loading ? (
        <tr>
          <td colSpan={4} className="text-center py-4 text-gray-500">
            Loading data...
          </td>
        </tr>
      ) : currentItems.length === 0 ? (
        <tr>
          <td colSpan={4} className="text-center py-4 text-gray-500">
            No matching records found
          </td>
        </tr>
      ) : (
        currentItems.map((item, index) => (
          <tr key={index} className="border-b bg-white">
            <td className="px-7 py-3 text-left w-1/4">{item.targetName}</td>
            <td className="px-4 py-3 text-left w-1/4">{item.targetType}</td>
            <td className="px-6 py-3 text-left w-1/4">{item.reportCount}</td>
            <td className="px-9 py-3 text-left w-1/4">{item.lastSubmitted}</td>
          </tr>
        ))
      )}
    </tbody>
  </table>
</div>

                <Pagination currentPage={currentPage} totalPages={totalPages} onPageChange={setCurrentPage} />
              </div>
    
   
  );
};

export default ScopeTable;
