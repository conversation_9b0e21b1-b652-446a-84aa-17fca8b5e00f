import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import {
  UserRole,
  getCredentials
} from "../../../api/endpoints/user/credentials";
import { LoadingState as LoadingStatus } from "../reducer";
import { requestOTPVerfication } from "../../../api/endpoints/user/authentication";
import { RootState } from "../../store";
import { toast } from "react-hot-toast";
import {
  getUserDetails,
  postUserDetails,
  requestAuthenticationChange
} from "../../../api/endpoints/user/user";

export enum LoginStatus {
  SIGNED_IN,
  OTP_PENDING,
  SIGNED_OUT
}

export type UserCredentials = {
  userId?: number;
  userEmail?: string;
  username?: string;
  userRole?: UserRole;
  accessToken?: string;
  refreshToken?: string;
};

export type UserDetails = {
  user_id?: number;
  username?: string;
  displayName?: string;
  about?: string;
  country?: string;
  links?: string[];
  skills?: string[];
  profilePicture?: string;
  bannerPicture?: string;
  otpEnabled?: boolean;
  firstTimeUXP?: boolean;
  paymentDetails?: {
    mode: string;
    details: unknown;
  };
  languages?: string[];
  hobbies?: string[];
  education?: string[];
  work_experience?: string[];
  achievements?: string[];
  tools_used?: string[];
  community_engagement?: string[];
  testimonials?: string[];
  publications?: string[];
  availability?: boolean;
  security_clearance?: string[];
};

// Modified type for updating user details w/ files
export type SetUserDetails = {
  profilePicture?: Blob;
  bannerPicture?: Blob;
} & Omit<UserDetails, "profilePicture" | "bannerPicture">;

type UserState = {
  credentials: {
    status: LoadingStatus;
  } & UserCredentials;
  details: {
    status: LoadingStatus;
  } & UserDetails;
  error?: string;
  loginStatus: LoginStatus;
};

/**
 * Make an attempt to verify a OTP code (requires valid user email)
 */
export const verfiyOTP = createAsyncThunk(
  "user/verify",
  async (code: string, { getState }) => {
    // Ensure a valid email is in use
    const email = (getState() as RootState).user?.credentials.userEmail;
    if (email === undefined) {
      toast.error("Invalid email");
      throw new Error("Invalid attempt");
    }

    const response: any = await requestOTPVerfication(email, code);
    if (response.response?.data?.message) {
      toast.error(response.response?.data?.message);
      throw new Error("Invalid attempt");
    }
    return response;
  }
);

/**
 * Fetch the latest user details from the backend API
 */
export const fetchUserDetails = createAsyncThunk(
  "user/details",
  getUserDetails
);

/**
 * Enable/disable two factor authentication
 */
export const changeAuthentication = createAsyncThunk(
  "user/2fa",
  requestAuthenticationChange
);

/**
 * Update the current user details
 */
export const updateUserDetails = createAsyncThunk(
  "user/update-details",
  postUserDetails
);

// Retrieve any previous user credentials from local storage
const savedUserCredentials = getCredentials();

// Define the initial user state
const initialState: UserState = {
  loginStatus:
    savedUserCredentials.accessToken !== undefined
      ? LoginStatus.SIGNED_IN
      : LoginStatus.SIGNED_OUT,
  credentials: {
    status: LoadingStatus.IDLE,
    ...savedUserCredentials
  },
  details: {
    status: LoadingStatus.IDLE
  }
};

const userReducer = createSlice({
  name: "user",
  initialState,
  reducers: {
    setCredentials: (state, action) => {
      return {
        ...state,
        credentials: {
          status: LoadingStatus.SUCCEEDED,
          ...action.payload
        },
        loginStatus:
          action.payload?.accessToken !== undefined
            ? LoginStatus.SIGNED_IN
            : LoginStatus.OTP_PENDING
      };
    }
  },
  extraReducers(builder) {
    builder
      // States while verifing OTP code
      .addCase(verfiyOTP.pending, state => {
        state.credentials.status = LoadingStatus.LOADING;
      })
      .addCase(verfiyOTP.fulfilled, (state, action) => {
        return {
          ...state,
          credentials: {
            status: LoadingStatus.SUCCEEDED,
            ...action.payload
          },
          loginStatus: LoginStatus.SIGNED_IN
        };
      })
      .addCase(verfiyOTP.rejected, (state, action) => {
        state.credentials.status = LoadingStatus.FAILED;
        state.error = action.error.message;
      })

      // States while fetching user details
      .addCase(fetchUserDetails.pending, state => {
        state.details.status = LoadingStatus.LOADING;
      })
      .addCase(fetchUserDetails.fulfilled, (state, action): UserState => {
        return {
          ...state,
          details: {
            status: LoadingStatus.SUCCEEDED,
            profilePicture: action.payload.profilePicture || "",
            ...action.payload
          },
          credentials: {
            ...state.credentials,
            username: action.payload.username
          }
        };
      })
      .addCase(fetchUserDetails.rejected, (state, action) => {
        state.details.status = LoadingStatus.FAILED;
        state.error = action.error.message;
      })

      // Update 2FA status after successful API response
      .addCase(changeAuthentication.fulfilled, (state, action) => {
        state.details.otpEnabled = action.payload;
      })

      // Update user details state after successful API response
      .addCase(updateUserDetails.fulfilled, (state, action) => {
        state.details = {
          ...state.details,
          ...action.payload
        };
        state.credentials.username = action.payload.username;
      });
  }
});

export const { setCredentials } = userReducer.actions;
export default userReducer.reducer;
