import React, { useRef, useEffect } from "react";
import DOMPurify from "dompurify";
import ReactMarkdown from "react-markdown";
import gfm from "remark-gfm"; // For GitHub Flavored Markdown
import AssistantStyles from "../assets/stylesheets/Assistant.module.scss";
import { HiOutlineClipboard } from "react-icons/hi";
import hljs from "highlight.js";
import "highlight.js/styles/default.css"; // Import a code style from highlight.js
import toast from "react-hot-toast";

export const sanitizeHtml = (html: string) => {
  const cleanHtml = DOMPurify.sanitize(html);
  return { __html: cleanHtml };
};

interface MarkdownViewerProps {
  data: string;
  languageUsed: string;
}

// const CodeBlock: React.ElementType = ({ children }) => {
//   return <pre className={AssistantStyles.codeBlock}>{children}</pre>;
// };

const CodeBlock: React.ElementType = ({ children, languageUsed }) => {
  console.log(languageUsed, "ll");
  const codeRef = useRef(null);
  // Function to copy the code to the clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(children.toString());
    toast("Copied to clipboard", {
      icon: "📎",
      duration: 1000
    });
    // You can also add a notification or tooltip to indicate that the code has been copied.
  };

  useEffect(() => {
    if (codeRef.current) {
      // Highlight the code block with auto-detection
      hljs.highlightBlock(codeRef.current);
    }
  }, [languageUsed]);

  return (
    <div className={`${AssistantStyles.codeBlock}`}>
      <div className={AssistantStyles.copyBtnContainer}>
        <button
          className={`${AssistantStyles.copyButton}`}
          onClick={copyToClipboard}
        >
          <HiOutlineClipboard className="mr-1" />
          Copy
        </button>
      </div>
      <pre>
        <code className={`language-${languageUsed}`} ref={codeRef}>
          {children}
        </code>
      </pre>
    </div>
  );
};

export const MarkdownViewer: React.FC<MarkdownViewerProps> = ({
  data,
  languageUsed
}) => {
  return (
    <div
      className={`max-w-full overflow-auto ${AssistantStyles.markdownContainer}`}
    >
      <ReactMarkdown
        remarkPlugins={[gfm]}
        components={{
          code: props => <CodeBlock {...props} languageUsed={languageUsed} />
        }}
      >
        {data}
      </ReactMarkdown>
    </div>
  );
};

export function formatDateRelativeToToday(dateString: string | undefined) {
  try {
    if (dateString) {
      const currentDate = new Date().getTime();
      const providedDate = new Date(dateString).getTime();
      const timeDifference = currentDate - providedDate;
      const daysDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24));

      if (daysDifference === 0) {
        return "Today";
      } else if (daysDifference === 1) {
        return "1 day ago";
      } else if (daysDifference > 1) {
        return `${daysDifference} days ago`;
      } else {
        return "Long time ago";
      }
    } else {
      return "Long time ago";
    }
  } catch (e) {
    console.log(e);
    return "Long time ago";
  }
}

export const checkIfValidLink = (link: string) => {
  const urlRegex = /^(https?):\/\/[^\s/$.?#].[^\s]*$/;
  return urlRegex.test(link);
};
