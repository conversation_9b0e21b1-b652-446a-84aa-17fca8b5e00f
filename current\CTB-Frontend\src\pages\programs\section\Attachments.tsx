import { CTB<PERSON>rogram } from "../../../utils/api/endpoints/programs/parsePrograms";
import { FiPaperclip } from "react-icons/fi";
import { FaDownload } from "react-icons/fa";

const Attachments = ({ program }: { program: CTBProgram }) => {
  let attachments: string[] = [];

  try {
    if (Array.isArray(program.attachments)) {
      attachments = program.attachments;
    } else if (typeof program.attachments === "string") {
      const parsed = JSON.parse(program.attachments);
      if (Array.isArray(parsed)) {
        attachments = parsed;
      }
    }
  } catch (err) {
    console.error("Failed to parse attachments:", err);
  }

  if (!attachments || attachments.length === 0) {
    return (
      <div className="flex items-center justify-center p-6 text-sm text-gray-500 bg-gray-50 rounded-md border border-dashed border-gray-300">
      <span className="inline-flex items-center gap-2">
        <svg
          className="w-5 h-5 text-gray-400"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          viewBox="0 0 24 24"
        >
          <path d="M4 16v1a2 2 0 002 2h12a2 2 0 002-2v-1M12 12v4m-4-4v4m8-4v4M4 10l8-6 8 6" />
        </svg>
        No attachments available.
      </span>
    </div>
    
    );
  }

  return (
    <div className="bg-white p-6 rounded-xl shadow-md">
      <h2 className="text-xl font-semibold text-ctb-blue-600 mb-4 flex items-center gap-2">
        📁 Attachments
      </h2>

      <ul className="grid grid-cols-1 md:grid-cols-2 gap-3 p-4 max-w-[800px] md:max-w-[1000px]">
        {attachments.map((url: string, index: number) => {
          if (typeof url !== "string" || !url) return null;

          const decodedUrl = decodeURIComponent(url);
          const dashIndex = decodedUrl.indexOf("-");
          
          const fileName =
            dashIndex !== -1
              ? decodedUrl.slice(dashIndex + 1)
              : decodedUrl.split("/").pop() || `Attachment-${index + 1}`;
          const extension = fileName.split(".").pop()?.toLowerCase() || "";

          return (
            <li
              key={index}
              className="group flex items-center justify-between bg-white border border-gray-200 shadow-sm rounded-xl px-4 py-3 hover:bg-blue-50 transition-all"
            >
              <div className="flex items-center gap-3 overflow-hidden">
                <div className="bg-blue-100 text-blue-700 p-2 rounded-full">
                  <FiPaperclip size={18} />
                </div>
                <div className="flex flex-col overflow-hidden">
                  <span className="text-sm font-medium text-gray-800 truncate max-w-xs">
                    {fileName}
                  </span>
                  <span className="text-xs text-gray-500 uppercase">{extension}</span>
                </div>
              </div>

              <a
                href={url}
                target="_blank"
                rel="noopener noreferrer"
                download
                className="text-sm font-medium text-blue-600 group-hover:underline hover:scale-125 transition-transform"
              >
                <FaDownload className="hover:text-green-600" />
              </a>
            </li>
          );
        })}
      </ul>
    </div>
  );
};

export default Attachments;
