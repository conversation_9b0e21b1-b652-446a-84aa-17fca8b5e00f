export const REPORT_STATUS_INFO = {
  APPROVE: {
    title: "Approve Report",
    description:
      "Are you sure you want to approve this report? This action cannot be undone.",
    info: "Approving a report indicates that the vulnerability has been verified and accepted. This typically means the issue is valid and requires attention. The report will move to the next stage in the workflow."
  },
  CLOSE: {
    title: "Close Report",
    description:
      "Are you sure you want to close this report? This action cannot be undone.",
    info: "Closing a report indicates that the issue has been resolved or is no longer relevant. This is typically used when the vulnerability has been fixed or when the issue is determined to be a false positive."
  },
  REJECT: {
    title: "Reject Report",
    description:
      "Are you sure you want to reject this report? This action cannot be undone.",
    info: "Rejecting a report indicates that the vulnerability is not valid or does not meet the program's criteria. This could be due to false positives, out-of-scope issues, or insufficient evidence."
  },
  GENERATE_CERTIFICATE: {
    title: "Generate Certificate",
    description:
      "Are you sure you want to generate a certificate for this report?",
    info: "Generating a certificate provides formal recognition of the vulnerability discovery. This certificate can be used for professional development, bug bounty programs, or security research portfolios."
  },
  INITIATE_PAYMENT: {
    title: "Initiate Payment",
    description: "Are you sure you want to initiate payment for this report?",
    info: "Initiating payment starts the process of rewarding the researcher for their findings. This typically involves processing the bounty amount based on the severity and impact of the vulnerability."
  },
  REQUEST_RETEST: {
    title: "Request Retest",
    description: "Are you sure you want to request a retest for this report?",
    info: "Requesting a retest means asking the researcher to verify if the reported vulnerability has been properly fixed. This ensures that the security issue has been adequately addressed."
  },
  DELETE_RETEST: {
    title: "Delete Retest Request",
    description: "Are you sure you want to delete the retest request?",
    info: "Deleting a retest request cancels the verification process. This should only be done if the retest is no longer needed or if you want to request it again later."
  },
  REQUEST_FIX: {
    title: "Fixing the vulnerability",
    description: "Are you sure you want to mark this report as being fixed?",
    info: "Marking a report as being fixed indicates that you are actively working on resolving the vulnerability. This helps track the progress of vulnerability remediation."
  },
  ACCEPT_RISK: {
    title: "Accept Risk",
    description: "Are you sure you want to accept the risk for this report?",
    info: "Accepting risk means acknowledging the vulnerability but choosing not to fix it immediately. This is typically used for low-risk issues or when the fix would cause more problems than the vulnerability itself."
  },
  NON_ACTIONABLE: {
    title: "Mark as Non-Actionable",
    description: "Are you sure you want to mark this report as non-actionable?",
    info: "Marking a report as non-actionable indicates that the issue cannot or will not be fixed. This could be due to technical limitations, business decisions, or the issue being out of scope."
  },
  REQUEST_INFO: {
    title: "Request More Info",
    description:
      "Are you sure you want to request more information for this report?",
    info: "Requesting more information means you need additional details to properly assess the vulnerability. This helps ensure accurate evaluation and appropriate remediation."
  },
  MODIFIED: {
    title: "Mark as Modified",
    description: "Are you sure you want to mark this report as modified?",
    info: "Marking a report as modified indicates that changes have been made to address the vulnerability. This helps track the progress of vulnerability remediation."
  },
  OUT_OF_SCOPE: {
    title: "Mark as Out of Scope",
    description: "Are you sure you want to mark this report as out of scope?",
    info: "Marking a report as out of scope indicates that the vulnerability is not covered by the program's scope. This could be due to the affected component being excluded or the issue type not being in scope."
  },
  SUBMIT: {
    title: "Submit Report",
    description:
      "Are you sure you want to submit this report? This action cannot be undone.",
    info: "Submitting a report makes it available for review by the admin team. Ensure all required information is included and the vulnerability is properly documented."
  }
} as const;

export type ReportStatusType = keyof typeof REPORT_STATUS_INFO;
