import React from 'react';
import { ReportData } from '../../types/report.types';

interface RecommendationsEditorProps {
  reportData: ReportData;
  onRecommendationsChange: (recommendations: { title: string; description: string }[]) => void;
}

const RecommendationsEditor: React.FC<RecommendationsEditorProps> = ({ reportData, onRecommendationsChange }) => {
  const recommendations = Array.isArray(reportData.recommendations_list) ? reportData.recommendations_list : [];

  const handleChange = (index: number, field: 'title' | 'description', value: string) => {
    const updated = recommendations.map((rec, i) =>
      i === index ? { ...rec, [field]: value } : rec
    );
    onRecommendationsChange(updated);
  };

  const handleAdd = () => {
    onRecommendationsChange([
      ...recommendations,
      { title: '', description: '' }
    ]);
  };

  const handleRemove = (index: number) => {
    const updated = recommendations.filter((_, i) => i !== index);
    onRecommendationsChange(updated);
  };

  return (
    <div className="h-full flex flex-col">
      <div className="bg-white rounded-xl shadow-lg border border-slate-100 p-6 mb-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-1 h-8 bg-gradient-to-b from-blue-500 to-blue-700 rounded-full" />
          <div className="flex-1">
            <h2 className="text-base font-bold text-blue-900 tracking-tight">Recommendations</h2>
            <p className="text-xs text-blue-700/80 font-medium mt-0.5">Add, edit, or remove recommendations for the report</p>
          </div>
          <button
            onClick={handleAdd}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors shadow-sm"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
            </svg>
            Add Recommendation
          </button>
        </div>
        <div className="space-y-5">
          {recommendations.map((rec, index) => (
            <div key={index} className="bg-white border border-slate-200 rounded-lg shadow-sm flex flex-col">
              <div className="flex items-center justify-between px-4 py-2 border-b border-slate-100 bg-slate-50 rounded-t-lg">
                <div className="flex items-center gap-3">
                  <div className="w-1 h-6 bg-gradient-to-b from-blue-400 to-blue-700 rounded-full" />
                  <span className="text-sm font-semibold text-slate-800">Recommendation #{String(index + 1).padStart(2, '0')}</span>
                </div>
                <button
                  onClick={() => handleRemove(index)}
                  className="inline-flex items-center px-2 py-1 text-red-600 hover:text-red-900 focus:outline-none focus:underline text-xs"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Delete
                </button>
              </div>
              <div className="p-4 space-y-3">
                <div className="grid grid-cols-3 gap-3 items-center">
                  <label className="text-xs font-medium text-slate-700">Title</label>
                  <div className="col-span-2">
                    <input
                      type="text"
                      value={rec.title}
                      onChange={e => handleChange(index, 'title', e.target.value)}
                      className="w-full px-3 py-2 text-xs border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 bg-slate-50"
                      placeholder="e.g., Enforce Network Segmentation and Isolation"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-3 items-center">
                  <label className="text-xs font-medium text-slate-700">Description</label>
                  <div className="col-span-2">
                    <textarea
                      value={rec.description}
                      onChange={e => handleChange(index, 'description', e.target.value)}
                      className="w-full px-3 py-2 text-xs border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 bg-slate-50"
                      placeholder="e.g., Use VLANs and strict firewall rules to isolate guest networks from internal infrastructure. Enable client/AP isolation to prevent device-to-device communication on wireless networks."
                      rows={3}
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
          {recommendations.length === 0 && (
            <div className="text-center py-16 bg-white rounded-lg border border-slate-200">
              <svg className="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="mt-4 text-base font-medium text-slate-900">No recommendations added yet</h3>
              <p className="mt-2 text-xs text-slate-500">Get started by adding your first recommendation.</p>
              <div className="mt-6">
                <button
                  onClick={handleAdd}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-xs font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg className="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                  </svg>
                  Add Recommendation
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RecommendationsEditor; 