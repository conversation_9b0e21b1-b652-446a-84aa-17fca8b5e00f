import React from 'react';
import { ReportData, DetailedFinding } from '../../types/report.types';
import HtmlEditor from '../HtmlEditor';

interface BaseFindingEditorProps {
  reportData: ReportData;
  severity: 'Critical' | 'High' | 'Medium' | 'Low';
  onFindingChange: (index: number, field: keyof DetailedFinding, value: string) => void;
  onRemoveFinding: (index: number) => void;
  onAddFinding: (severity: 'Critical' | 'High' | 'Medium' | 'Low') => void;
}

const BaseFindingEditor: React.FC<BaseFindingEditorProps> = ({
  reportData,
  severity,
  onFindingChange,
  onRemoveFinding,
  onAddFinding,
}) => {
  // Debug: Log severity prop
  console.log('BaseFindingEditor: severity prop', severity);

  // Map UI severity to data severity
  const severityMap: Record<string, string> = {
    'Critical': 'CRITICAL',
    'High': 'HIGH',
    'Medium': 'MEDIUM',
    'Low': 'LOW',
  };
  const normalizedSeverity = severityMap[severity] || severity.toUpperCase();

  const findings = reportData.detailed_findings?.filter(
    f => (f.severity_category || '').toUpperCase() === normalizedSeverity
  ) || [];

  // Debug: Log findings data
  console.log('BaseFindingEditor: reportData.detailed_findings', reportData.detailed_findings);
  console.log('BaseFindingEditor: filtered findings', findings);

  const severityAccent = {
    Critical: 'from-red-500 to-red-700',
    High: 'from-orange-400 to-orange-600',
    Medium: 'from-yellow-400 to-yellow-600',
    Low: 'from-blue-400 to-blue-700',
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="bg-white rounded-xl shadow-lg border border-slate-100 p-6 mb-6">
        <div className="flex items-center gap-3 mb-4">
          <div className={`w-1 h-8 bg-gradient-to-b ${severityAccent[severity]} rounded-full`} />
          <div className="flex-1">
            <h3 className="text-base font-bold text-blue-900 tracking-tight">{severity} Findings</h3>
            <p className="text-xs text-blue-700/80 font-medium mt-0.5">Manage {severity.toLowerCase()} severity findings</p>
          </div>
          <button
            onClick={() => onAddFinding(severity)}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors shadow-sm"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
            </svg>
            Add Finding
          </button>
        </div>
        <div className="pt-2">
          {findings.length === 0 ? (
            <div className="text-gray-500">No findings added yet.</div>
          ) : (
            <div>
              {findings.map((finding, idx) => (
                <div key={idx} className="mb-6 p-4 border rounded bg-white">
                  <div className="font-bold text-lg mb-2">{finding.title}</div>
                  {finding.scope && <div className="text-xs text-gray-400 mb-2">Scope: {finding.scope}</div>}
                  <div className="flex items-center justify-between px-4 py-2 border-b border-slate-100 bg-slate-50 rounded-t-lg">
                    <div className="flex items-center gap-3">
                      <div className={`w-1 h-6 bg-gradient-to-b ${severityAccent[severity]} rounded-full`} />
                      <span className="text-sm font-semibold text-slate-800">Finding {idx + 1}</span>
                    </div>
                    <button
                      onClick={() => onRemoveFinding(idx)}
                      className="inline-flex items-center px-2 py-1 text-red-600 hover:text-red-900 focus:outline-none focus:underline text-xs"
                    >
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                      Delete
                    </button>
                  </div>
                  <div className="p-4 space-y-3">
                    <div>
                      <label className="block text-xs font-medium text-slate-700 mb-1">Abbreviation</label>
                      <input
                        type="text"
                        value={finding.abbreviation || `${severity[0]}${idx + 1}`}
                        onChange={(e) => onFindingChange(idx, 'abbreviation', e.target.value)}
                        className="w-full px-3 py-2 text-xs border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 bg-slate-50"
                        placeholder={`e.g., ${severity[0]}${idx + 1}`}
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-slate-700 mb-1">Title</label>
                      <input
                        type="text"
                        value={finding.title}
                        onChange={(e) => onFindingChange(idx, 'title', e.target.value)}
                        className="w-full px-3 py-2 text-xs border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 bg-slate-50"
                        placeholder="Enter finding title"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-slate-700 mb-1">Scope</label>
                      <input
                        type="text"
                        value={finding.scope}
                        onChange={(e) => onFindingChange(idx, 'scope', e.target.value)}
                        className="w-full px-3 py-2 text-xs border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 bg-slate-50"
                        placeholder="Enter vulnerable URL/component"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-slate-700 mb-1">Description</label>
                      <textarea
                        value={finding.description}
                        onChange={(e) => onFindingChange(idx, 'description', e.target.value)}
                        className="w-full px-3 py-2 text-xs border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 bg-slate-50 resize-none"
                        rows={3}
                        placeholder="Enter finding description"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-slate-700 mb-1">Steps to Reproduce</label>
                      <textarea
                        value={finding.instructions}
                        onChange={(e) => onFindingChange(idx, 'instructions', e.target.value)}
                        className="w-full px-3 py-2 text-xs border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 bg-slate-50 resize-none"
                        rows={3}
                        placeholder="Enter steps to reproduce the finding"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-slate-700 mb-1">Impact</label>
                      <textarea
                        value={finding.impact}
                        onChange={(e) => onFindingChange(idx, 'impact', e.target.value)}
                        className="w-full px-3 py-2 text-xs border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 bg-slate-50 resize-none"
                        rows={3}
                        placeholder="Enter impact of the finding"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-slate-700 mb-1">Remediation</label>
                      <textarea
                        value={finding.fix}
                        onChange={(e) => onFindingChange(idx, 'fix', e.target.value)}
                        className="w-full px-3 py-2 text-xs border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 bg-slate-50 resize-none"
                        rows={3}
                        placeholder="Enter remediation steps"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BaseFindingEditor; 