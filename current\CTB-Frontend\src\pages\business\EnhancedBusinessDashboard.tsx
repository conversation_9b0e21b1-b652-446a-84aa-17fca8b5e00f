import React, { useEffect, useRef } from "react";
import { motion } from "framer-motion";
import usePageTitle from "../../utils/hooks/usePageTitle";
import useDashboardSummary from "../../utils/hooks/dashboard/useDashboardSummary";
import useSeverityAndTrend from "../../utils/hooks/dashboard/useSeverityAndTrend";
import useUnifiedBusinessDashboard from "../../utils/hooks/dashboard/useUnifiedBusinessDashboard";
import EnhancedDashboardStats from "../../components/dashboard/business/EnhancedDashboardStats";
import EnhancedSeverityDonut from "../../components/dashboard/business/EnhancedSeverityDonut";
import EnhancedTrendLineChart from "../../components/dashboard/business/EnhancedTrendLineChart";
import ActionableReportsSection from "../../components/dashboard/business/ActionableReportsSection";
import RetestingSummarySection from "../../components/dashboard/business/RetestingSummarySection";
import RetestActionsTable from "../../components/dashboard/business/RetestActionsTable";
import { FiRefreshCw } from "react-icons/fi";

// Animation variants for page sections
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.4 }
  }
};

// Loading Skeleton components 
const StatsSkeleton = () => (
  <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
    {[...Array(4)].map((_, index) => (
      <div
        key={index}
        className="h-[110px] animate-pulse rounded-xl bg-gray-200 shadow-sm"
      ></div>
    ))}
  </div>
);

const ChartSkeleton = () => (
  <div className="h-[380px] animate-pulse rounded-xl bg-gray-200 shadow-sm"></div>
);

const EnhancedBusinessDashboard: React.FC = () => {
  usePageTitle("Business Dashboard");
  const isLoadingRef = useRef(false);
  
  const { 
    data: summaryData, 
    loading: summaryLoading, 
    error: summaryError, 
    refetch: refetchSummary 
  } = useDashboardSummary();
  
  const { 
    data: severityAndTrendData, 
    loading: trendLoading, 
    error: trendError, 
    refetch: refetchTrend 
  } = useSeverityAndTrend();

  const {
    retestActions,
    loading: unifiedLoading,
    error: unifiedError,
    refetch: refetchUnified
  } = useUnifiedBusinessDashboard();

  // Only for debugging - single logger useEffect
  useEffect(() => {
    if (!isLoadingRef.current) {
      console.log("Dashboard data loaded:", {
        summaryData,
        severityAndTrendData
      });
    }
  }, [summaryData, severityAndTrendData]);

  const handleRetryAll = () => {
    if (isLoadingRef.current) return;
    
    isLoadingRef.current = true;
    Promise.all([refetchSummary(), refetchTrend()]).finally(() => {
      isLoadingRef.current = false;
    });
  };

  // Handle critical errors when both data sources fail
  if (summaryError && trendError) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-slate-50 p-6">
        <div className="text-center">
          <h2 className="mb-2 text-xl font-bold text-gray-800">Oops! Something went wrong</h2>
          <p className="text-gray-600">
            Failed to load critical dashboard data. Please try again.
          </p>
          <button 
            onClick={handleRetryAll}
            disabled={isLoadingRef.current}
            className="mt-4 rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 disabled:bg-blue-300"
          >
            {isLoadingRef.current ? "Retrying..." : "Retry All"}
          </button>
        </div>
      </div>
    );
  }

  return (
    <motion.div 
      className="min-h-screen bg-slate-50 p-6 md:p-8 max-w-full mx-auto"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
    
      
      {/* Stats Section */}
      <motion.div variants={itemVariants} className="mb-8">
        {summaryLoading && <StatsSkeleton />}
        {summaryError && !summaryData && (
          <div className="rounded-lg border border-red-200 bg-red-50 p-4">
            <p className="text-red-600">Error loading stats: {summaryError}</p>
            <button 
              onClick={() => refetchSummary()}
              className="mt-2 text-sm text-red-600 underline"
            >
              Retry
            </button>
          </div>
        )}
        {summaryData && (
          <EnhancedDashboardStats data={summaryData} />
        )}
      </motion.div>
      
      {/* Charts Section - SINGLE INSTANCE OF CHARTS */}
      <motion.div 
        variants={itemVariants}
        className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"
      >
        {/* Severity Distribution Chart */}
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200/80">
         
          {trendLoading && <ChartSkeleton />}
          {trendError && !severityAndTrendData && (
            <div className="flex flex-col items-center justify-center py-10 text-center">
              <p className="text-red-600">Failed to load severity data</p>
              <button 
                onClick={() => refetchTrend()}
                className="mt-2 text-sm text-red-600 underline"
              >
                Retry
              </button>
            </div>
          )}
          {severityAndTrendData && (
            <EnhancedSeverityDonut data={severityAndTrendData.severityOverview} />
          )}
        </div>

        {/* Report Trends Chart */}
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200/80">
      
          {trendLoading && <ChartSkeleton />}
          {trendError && !severityAndTrendData && (
            <div className="flex flex-col items-center justify-center py-10 text-center">
              <p className="text-red-600">Failed to load trend data</p>
              <button 
                onClick={() => refetchTrend()}
                className="mt-2 text-sm text-red-600 underline"
              >
                Retry
              </button>
            </div>
          )}
          {severityAndTrendData && (
            <EnhancedTrendLineChart data={severityAndTrendData} />
          )}
        </div>
      </motion.div>
      
      {/* Actionable Reports Section - Now ABOVE RetestingSummary */}
      <motion.div variants={itemVariants} className="mb-8">
        <ActionableReportsSection />
      </motion.div>
      
      {/* Retesting Summary Section - Moved below ActionableReportsSection */}
      <motion.div variants={itemVariants} className="mb-8">
        <RetestingSummarySection />
      </motion.div>

      {/* Retest Actions Table Section */}
      <motion.div variants={itemVariants} className="mb-8">
        <div className="rounded-xl border border-neutral-200/80 bg-white/95 backdrop-blur-sm p-6 shadow-lg">          
          {unifiedError ? (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
              <p className="text-red-600">Failed to load retest actions</p>
              <button 
                onClick={() => refetchUnified()}
                className="mt-2 text-sm text-red-600 underline"
              >
                Retry
              </button>
            </div>
          ) : (
            <RetestActionsTable 
              retestActions={retestActions || []} 
              onRefresh={refetchUnified}
            />
          )}
        </div>
      </motion.div>
    </motion.div>
  );
};

export default EnhancedBusinessDashboard; 