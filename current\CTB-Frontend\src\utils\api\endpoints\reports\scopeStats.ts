import axios from "../../axios";
import { parseScopeStats, ScopeStatsInfo } from "./parseTarget";
 
 

const BASE_URL = "/v2/reports/";

export const getScopeStats = async (programId: number): Promise<ScopeStatsInfo> => {
  try {
    const response = await axios.get(`${BASE_URL}/getScopeStats/${programId}`);
    const parsedData = parseScopeStats(response.data);
    if (parsedData === null) {
      throw new Error("Parsed scope stats is null");
    }
    return parsedData;
  } catch (error) {
    console.error("Error fetching scope stats:", error);
    throw error;
  }
};
