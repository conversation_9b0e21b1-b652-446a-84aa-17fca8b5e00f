import { UserCredentials } from "../../../store/reducer/slices/userReducer";

// Friendly names for the local storage keys
export const LocalStorage = {
  ACCESS_TOKEN: "ctb_user_accessToken",
  USER_ID: "ctb_user_id",
  USER_EMAIL: "ctb_user_email",
  USERNAME: "ctb_user_username",
  USER_ROLE: "ctb_user_role",
  REFRESH_TOKEN: "ctb_user_refreshToken"
} as const;

export enum UserRole {
  RESEARCHER = 1,
  BUSINESS = 2,
  ADMIN = 3,
  QA=4,
  ADMIN_MANAGER = 5,
  DEVELOPER = 6,
  BUSINESS_MANAGER = 7,
  BUSINESS_ADMINISTRATOR = 8,
  SUB_ADMIN = 9,
}

/**
 * Update the given credentials stored locally on the users device
 */
export const setCredentials = ({
  userId,
  userEmail,
  username,
  userRole,
  accessToken,
  refreshToken
}: UserCredentials) => {
  userId && localStorage.setItem(LocalStorage.USER_ID, `${userId}`);
  userEmail && localStorage.setItem(LocalStorage.USER_EMAIL, userEmail);
  username && localStorage.setItem(LocalStorage.USERNAME, username);
  userRole && localStorage.setItem(LocalStorage.USER_ROLE, `${userRole}`);
  accessToken && localStorage.setItem(LocalStorage.ACCESS_TOKEN, accessToken);
  refreshToken &&
    localStorage.setItem(LocalStorage.REFRESH_TOKEN, refreshToken);
};

/**
 * Retrieve the user credentials stored on the users device
 */
export const getCredentials = (): UserCredentials => {
  const userId = localStorage.getItem(LocalStorage.USER_ID);

  return {
    userId: userId === null ? undefined : parseInt(userId),
    userEmail: getCredential(LocalStorage.USER_EMAIL),
    userRole: getUserRole(),
    accessToken: getCredential(LocalStorage.ACCESS_TOKEN),
    refreshToken: getCredential(LocalStorage.REFRESH_TOKEN)
  };
};

export const getUserRole = () => {
  const userRole = getCredential(LocalStorage.USER_ROLE);
  return userRole ? parseInt(userRole) : undefined;
};

export const getCredential = (
  key: (typeof LocalStorage)[keyof typeof LocalStorage]
) => localStorage.getItem(key) || undefined;

/**
 * Log the user out by clearing their credentials from this device and
 * returning to the landing page
 */
export const logOut = () => {
  // Clear each of the user credentials from the local storage
  for (const key of Object.values(LocalStorage)) {
    localStorage.removeItem(key);
  }

  // Return to the landing page
  window.location.pathname = "/";
};
