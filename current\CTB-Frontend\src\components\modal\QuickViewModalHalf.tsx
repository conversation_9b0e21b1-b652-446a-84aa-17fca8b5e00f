import { PropsWithChildren } from "react";
import FullscreenModalLayout from "./FullscreenModalLayout";
import CloseButton from "../buttons/CloseButton";

export const QuickViewSection = ({
  title,
  children,
  className
}: PropsWithChildren<{ title?: string; className?: string }>) => (
  <article className="mt-5">
    {title !== undefined && (
      <h2 className="mb-2 text-xl font-medium leading-[150%] text-ctb-blue">
        {title}
      </h2>
    )}
    <span className={className}>{children}</span>
  </article>
);

const QuickViewModalHalf = ({
  title,
  enabled,
  children,
  className,
  onClick
}: PropsWithChildren<{
  title?: string;
  enabled: boolean;
  className?: string;
  onClick: () => void;
}>) => {
  return (
    <>
      {enabled && (
        <FullscreenModalLayout containerClassName="md:!w-[512px] w-full">
          <section
            className={
              "fixed bottom-0 right-0 top-[19rem] h-auto animate-slideOutFromRight overflow-y-auto rounded-s-lg bg-white p-9 shadow-2xl max-md:border-l-2 md:w-[512px] " +
              className
            }
          >
            {title && (
              <h1 className="mb-3 flex justify-between text-2xl font-medium leading-[150%] text-ctb-blue">
                {title}
                <CloseButton iconClassName="mb-1 h-7 w-7" onClick={onClick} />
              </h1>
            )}

            {/* Render modal content with internal scroll */}
            {children}
          </section>
        </FullscreenModalLayout>
      )}
    </>
  );
};

export default QuickViewModalHalf;
