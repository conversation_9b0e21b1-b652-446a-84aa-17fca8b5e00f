.pdf-editor-container {
  display: flex;
  gap: 20px;
  height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
}

.editor-section {
  flex: 0 0 40%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-header {
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.editor-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.save-button {
  background-color: #0a2242;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.save-button:hover {
  background-color: #0d2b5b;
}

.save-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.editor-navigation {
  display: flex;
  gap: 10px;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  overflow-x: auto;
}

.nav-button {
  background: none;
  border: 1px solid #e0e0e0;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  white-space: nowrap;
  color: #666;
  transition: all 0.2s;
}

.nav-button:hover {
  background-color: #f0f0f0;
  border-color: #ccc;
}

.nav-button.active {
  background-color: #0a2242;
  color: white;
  border-color: #0a2242;
}

.editor-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.editor-section-content {
  max-width: 800px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #0a2242;
}

.form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.2s;
}

.form-textarea:focus {
  outline: none;
  border-color: #0a2242;
}

.editable-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.editable-table th {
  background-color: #f8f9fa;
  padding: 12px;
  text-align: left;
  font-weight: 500;
  color: #333;
  border-bottom: 2px solid #e0e0e0;
}

.editable-table td {
  padding: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.table-input {
  width: 100%;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
}

.table-select {
  width: 100%;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
}

.preview-section {
  flex: 0 0 60%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.preview-header {
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.preview-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.preview-html {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.preview-placeholder {
  text-align: center;
  color: #666;
  margin-top: 100px;
}

.preview-loading {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #666;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #0a2242;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .pdf-editor-container {
    flex-direction: column;
    height: auto;
  }

  .editor-section,
  .preview-section {
    height: 600px;
  }
}

@media (max-width: 768px) {
  .editor-navigation {
    flex-wrap: wrap;
  }

  .nav-button {
    flex: 1 1 calc(50% - 10px);
    text-align: center;
  }
}

/* Print styles */
@media print {
  .pdf-editor-container {
    display: block;
    padding: 0;
    height: auto;
  }

  .editor-section {
    display: none;
  }

  .preview-section {
    box-shadow: none;
  }

  .preview-header {
    display: none;
  }

  .preview-content {
    padding: 0;
    background: none;
  }

  .preview-html {
    box-shadow: none;
    margin: 0;
    padding: 20mm;
  }
}

.pdf-rich-text-editor {
  margin-bottom: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.pdf-rich-text-editor .jodit-container {
  border: none !important;
}

.pdf-rich-text-editor .jodit-toolbar {
  background-color: #f8f9fa !important;
  border-bottom: 1px solid #e0e0e0 !important;
}

.pdf-rich-text-editor .jodit-toolbar__box {
  background-color: #f8f9fa !important;
}

.pdf-rich-text-editor .jodit-workplace {
  background-color: white !important;
}

.pdf-rich-text-editor .jodit-status-bar {
  background-color: #f8f9fa !important;
  border-top: 1px solid #e0e0e0 !important;
}

.pdf-rich-text-editor .jodit-toolbar-button {
  color: #333 !important;
}

.pdf-rich-text-editor .jodit-toolbar-button:hover {
  background-color: #e9ecef !important;
}

.pdf-rich-text-editor .jodit-toolbar-button__trigger {
  color: #333 !important;
}

.editor-html-content {
  border: 1px solid #ddd;
  border-radius: 4px;
  min-height: 200px;
  padding: 10px;
  background: white;
  margin-bottom: 20px;
}

.editor-html-content [contenteditable] {
  outline: none;
  min-height: 180px;
  padding: 10px;
  line-height: 1.6;
}

.editor-html-content [contenteditable]:focus {
  outline: 2px solid #0a2242;
  border-radius: 4px;
}

.editor-html-content [contenteditable] p {
  margin: 0 0 1em 0;
}

.editor-html-content [contenteditable] ul,
.editor-html-content [contenteditable] ol {
  margin: 0 0 1em 1.5em;
  padding: 0;
}

.editor-html-content [contenteditable] li {
  margin: 0.5em 0;
}

.editor-html-content [contenteditable] h1,
.editor-html-content [contenteditable] h2,
.editor-html-content [contenteditable] h3,
.editor-html-content [contenteditable] h4,
.editor-html-content [contenteditable] h5,
.editor-html-content [contenteditable] h6 {
  margin: 1em 0 0.5em 0;
  font-weight: 600;
}

.editor-html-content [contenteditable] table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

.editor-html-content [contenteditable] table th,
.editor-html-content [contenteditable] table td {
  border: 1px solid #ddd;
  padding: 8px;
}

.editor-html-content [contenteditable] table th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.editor-html-content [contenteditable] blockquote {
  margin: 1em 0;
  padding: 0.5em 1em;
  border-left: 4px solid #0a2242;
  background-color: #f8f9fa;
}

.editor-html-content [contenteditable] code {
  background-color: #f8f9fa;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

.editor-html-content [contenteditable] pre {
  background-color: #f8f9fa;
  padding: 1em;
  border-radius: 4px;
  overflow-x: auto;
  font-family: monospace;
}

.editor-html-content [contenteditable] img {
  max-width: 100%;
  height: auto;
  margin: 1em 0;
}

.editor-html-content [contenteditable] a {
  color: #0a2242;
  text-decoration: underline;
}

.editor-html-content [contenteditable] a:hover {
  color: #0d2b5c;
}

.table-container {
  margin-top: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #ddd;
}

.table-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.add-row-button {
  background: #0a2242;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.add-row-button:hover {
  background: #0d2b5c;
}

.delete-row-button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.delete-row-button:hover {
  background: #c82333;
}

.editable-table {
  width: 100%;
  border-collapse: collapse;
}

.editable-table th,
.editable-table td {
  padding: 12px;
  border: 1px solid #ddd;
  text-align: left;
}

.editable-table th {
  background: #f8f9fa;
  font-weight: 600;
}

.editable-table td {
  background: white;
}

.table-input,
.table-select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.table-input:focus,
.table-select:focus {
  outline: none;
  border-color: #0a2242;
}

/* Add notification animations */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-slide-out {
  animation: slideOut 0.3s ease-in;
}

/* Enhanced notification styles */
.notification-enter {
  transform: translateX(100%);
  opacity: 0;
}

.notification-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: all 0.3s ease-out;
}

.notification-exit {
  transform: translateX(0);
  opacity: 1;
}

.notification-exit-active {
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease-in;
}

.page {
  background: white;
  width: 210mm;
  min-height: 297mm;
  box-shadow: 0 4px 24px rgba(0,0,0,0.10), 0 1.5px 4px rgba(0,0,0,0.08);
  margin: 24px 0;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  display: block;
} 