{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\jira-old\\\\CTB-Frontend\\\\src\\\\pages\\\\automated-report\\\\components\\\\editors\\\\CoverPageEditor.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst formFields = [{\n  id: 'report_title',\n  label: 'Report Title',\n  type: 'text',\n  placeholder: 'Enter report title',\n  required: true\n}, {\n  id: 'program_name',\n  label: 'Program Name',\n  type: 'text',\n  placeholder: 'Enter program name for cover page'\n}, {\n  id: 'company_name',\n  label: 'Company Name',\n  type: 'text',\n  placeholder: 'Enter company name',\n  required: true\n}, {\n  id: 'document_number',\n  label: 'Document Number',\n  type: 'text',\n  placeholder: 'Enter document number'\n}, {\n  id: 'version_number',\n  label: 'Version Number',\n  type: 'text',\n  placeholder: 'Enter version number'\n}, {\n  id: 'current_date',\n  label: 'Current Date',\n  type: 'date',\n  required: true\n}, {\n  id: 'revision_date',\n  label: 'Revision Date',\n  type: 'date'\n}];\nconst CoverPageEditor = ({\n  reportData,\n  onInputChange\n}) => {\n  const handleInputChange = (field, value) => {\n    onInputChange(field, value);\n  };\n  const renderField = field => {\n    const labelClasses = \"block text-xs font-medium text-gray-700 mb-0.5\";\n    const fieldValue = reportData[field.id] || '';\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col mb-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        htmlFor: field.id,\n        className: labelClasses,\n        children: [field.label, field.required && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-red-500 ml-0.5\",\n          children: \"*\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 30\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        id: field.id,\n        type: field.type,\n        value: fieldValue,\n        onChange: e => handleInputChange(field.id, e.target.value),\n        className: \"w-full px-2.5 py-1.5 text-sm bg-white border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200 placeholder:text-slate-400\",\n        placeholder: field.placeholder,\n        required: field.required,\n        \"aria-required\": field.required\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, field.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-full overflow-y-auto\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-xl shadow-lg border border-slate-100 p-6 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-1 h-8 bg-gradient-to-b from-blue-500 to-blue-700 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-base font-bold text-blue-900 tracking-tight\",\n            children: \"Cover Page\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-blue-700/80 font-medium mt-0.5\",\n            children: \"Edit cover page details\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col gap-2\",\n          children: [renderField(formFields[0]), \" \", renderField(formFields[1]), \" \", renderField(formFields[2]), \" \", renderField(formFields[3]), \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col gap-2\",\n          children: [renderField(formFields[4]), \" \", /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-2\",\n            children: [renderField(formFields[5]), \" \", renderField(formFields[6]), \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n};\n_c = CoverPageEditor;\nexport default CoverPageEditor;\nvar _c;\n$RefreshReg$(_c, \"CoverPageEditor\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "formFields", "id", "label", "type", "placeholder", "required", "CoverPageEditor", "reportData", "onInputChange", "handleInputChange", "field", "value", "renderField", "labelClasses", "fieldValue", "className", "children", "htmlFor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "e", "target", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/jira-old/CTB-Frontend/src/pages/automated-report/components/editors/CoverPageEditor.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { ReportData } from '../../types/report.types';\r\n\r\ninterface CoverPageEditorProps {\r\n  reportData: ReportData;\r\n  onInputChange: (field: string, value: string) => void;\r\n}\r\n\r\ntype CoverPageField =\r\n  | 'report_title'\r\n  | 'company_name'\r\n  | 'program_name'\r\n  | 'document_number'\r\n  | 'version_number'\r\n  | 'current_date'\r\n  | 'revision_date';\r\n\r\ninterface FormField {\r\n  id: CoverPageField;\r\n  label: string;\r\n  type: 'text' | 'date';\r\n  placeholder?: string;\r\n  required?: boolean;\r\n}\r\n\r\nconst formFields: FormField[] = [\r\n  { id: 'report_title', label: 'Report Title', type: 'text', placeholder: 'Enter report title', required: true },\r\n  { id: 'program_name', label: 'Program Name', type: 'text', placeholder: 'Enter program name for cover page' },\r\n  { id: 'company_name', label: 'Company Name', type: 'text', placeholder: 'Enter company name', required: true },\r\n  { id: 'document_number', label: 'Document Number', type: 'text', placeholder: 'Enter document number' },\r\n  { id: 'version_number', label: 'Version Number', type: 'text', placeholder: 'Enter version number' },\r\n  { id: 'current_date', label: 'Current Date', type: 'date', required: true },\r\n  { id: 'revision_date', label: 'Revision Date', type: 'date' },\r\n];\r\n\r\nconst CoverPageEditor: React.FC<CoverPageEditorProps> = ({ reportData, onInputChange }) => {\r\n  const handleInputChange = (field: CoverPageField, value: string) => {\r\n    onInputChange(field, value);\r\n  };\r\n\r\n  const renderField = (field: FormField) => {\r\n    const labelClasses = \"block text-xs font-medium text-gray-700 mb-0.5\";\r\n    const fieldValue = (reportData[field.id] as string) || '';\r\n    return (\r\n      <div key={field.id} className=\"flex flex-col mb-2\">\r\n        <label htmlFor={field.id} className={labelClasses}>\r\n          {field.label}\r\n          {field.required && <span className=\"text-red-500 ml-0.5\">*</span>}\r\n        </label>\r\n        <input\r\n          id={field.id}\r\n          type={field.type}\r\n          value={fieldValue}\r\n          onChange={(e) => handleInputChange(field.id, e.target.value)}\r\n          className=\"w-full px-2.5 py-1.5 text-sm bg-white border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200 placeholder:text-slate-400\"\r\n          placeholder={field.placeholder}\r\n          required={field.required}\r\n          aria-required={field.required}\r\n        />\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"h-full overflow-y-auto\">\r\n      <div className=\"bg-white rounded-xl shadow-lg border border-slate-100 p-6 mb-6\">\r\n        <div className=\"flex items-center gap-3 mb-4\">\r\n          <div className=\"w-1 h-8 bg-gradient-to-b from-blue-500 to-blue-700 rounded-full\" />\r\n          <div>\r\n            <h3 className=\"text-base font-bold text-blue-900 tracking-tight\">Cover Page</h3>\r\n            <p className=\"text-xs text-blue-700/80 font-medium mt-0.5\">Edit cover page details</p>\r\n          </div>\r\n        </div>\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-2\">\r\n          {/* First column: Report Title, Program Name, Company Name, Document Number */}\r\n          <div className=\"flex flex-col gap-2\">\r\n            {renderField(formFields[0])} {/* Report Title */}\r\n            {renderField(formFields[1])} {/* Program Name */}\r\n            {renderField(formFields[2])} {/* Company Name */}\r\n            {renderField(formFields[3])} {/* Document Number */}\r\n          </div>\r\n          {/* Second column: Version Number, Dates side by side */}\r\n          <div className=\"flex flex-col gap-2\">\r\n            {renderField(formFields[4])} {/* Version Number */}\r\n            <div className=\"grid grid-cols-2 gap-2\">\r\n              {renderField(formFields[5])} {/* Current Date */}\r\n              {renderField(formFields[6])} {/* Revision Date */}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CoverPageEditor; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAyB1B,MAAMC,UAAuB,GAAG,CAC9B;EAAEC,EAAE,EAAE,cAAc;EAAEC,KAAK,EAAE,cAAc;EAAEC,IAAI,EAAE,MAAM;EAAEC,WAAW,EAAE,oBAAoB;EAAEC,QAAQ,EAAE;AAAK,CAAC,EAC9G;EAAEJ,EAAE,EAAE,cAAc;EAAEC,KAAK,EAAE,cAAc;EAAEC,IAAI,EAAE,MAAM;EAAEC,WAAW,EAAE;AAAoC,CAAC,EAC7G;EAAEH,EAAE,EAAE,cAAc;EAAEC,KAAK,EAAE,cAAc;EAAEC,IAAI,EAAE,MAAM;EAAEC,WAAW,EAAE,oBAAoB;EAAEC,QAAQ,EAAE;AAAK,CAAC,EAC9G;EAAEJ,EAAE,EAAE,iBAAiB;EAAEC,KAAK,EAAE,iBAAiB;EAAEC,IAAI,EAAE,MAAM;EAAEC,WAAW,EAAE;AAAwB,CAAC,EACvG;EAAEH,EAAE,EAAE,gBAAgB;EAAEC,KAAK,EAAE,gBAAgB;EAAEC,IAAI,EAAE,MAAM;EAAEC,WAAW,EAAE;AAAuB,CAAC,EACpG;EAAEH,EAAE,EAAE,cAAc;EAAEC,KAAK,EAAE,cAAc;EAAEC,IAAI,EAAE,MAAM;EAAEE,QAAQ,EAAE;AAAK,CAAC,EAC3E;EAAEJ,EAAE,EAAE,eAAe;EAAEC,KAAK,EAAE,eAAe;EAAEC,IAAI,EAAE;AAAO,CAAC,CAC9D;AAED,MAAMG,eAA+C,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAc,CAAC,KAAK;EACzF,MAAMC,iBAAiB,GAAGA,CAACC,KAAqB,EAAEC,KAAa,KAAK;IAClEH,aAAa,CAACE,KAAK,EAAEC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMC,WAAW,GAAIF,KAAgB,IAAK;IACxC,MAAMG,YAAY,GAAG,gDAAgD;IACrE,MAAMC,UAAU,GAAIP,UAAU,CAACG,KAAK,CAACT,EAAE,CAAC,IAAe,EAAE;IACzD,oBACEF,OAAA;MAAoBgB,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBAChDjB,OAAA;QAAOkB,OAAO,EAAEP,KAAK,CAACT,EAAG;QAACc,SAAS,EAAEF,YAAa;QAAAG,QAAA,GAC/CN,KAAK,CAACR,KAAK,EACXQ,KAAK,CAACL,QAAQ,iBAAIN,OAAA;UAAMgB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAAC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACRtB,OAAA;QACEE,EAAE,EAAES,KAAK,CAACT,EAAG;QACbE,IAAI,EAAEO,KAAK,CAACP,IAAK;QACjBQ,KAAK,EAAEG,UAAW;QAClBQ,QAAQ,EAAGC,CAAC,IAAKd,iBAAiB,CAACC,KAAK,CAACT,EAAE,EAAEsB,CAAC,CAACC,MAAM,CAACb,KAAK,CAAE;QAC7DI,SAAS,EAAC,2MAA2M;QACrNX,WAAW,EAAEM,KAAK,CAACN,WAAY;QAC/BC,QAAQ,EAAEK,KAAK,CAACL,QAAS;QACzB,iBAAeK,KAAK,CAACL;MAAS;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC;IAAA,GAdMX,KAAK,CAACT,EAAE;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAeb,CAAC;EAEV,CAAC;EAED,oBACEtB,OAAA;IAAKgB,SAAS,EAAC,wBAAwB;IAAAC,QAAA,eACrCjB,OAAA;MAAKgB,SAAS,EAAC,gEAAgE;MAAAC,QAAA,gBAC7EjB,OAAA;QAAKgB,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3CjB,OAAA;UAAKgB,SAAS,EAAC;QAAiE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnFtB,OAAA;UAAAiB,QAAA,gBACEjB,OAAA;YAAIgB,SAAS,EAAC,kDAAkD;YAAAC,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFtB,OAAA;YAAGgB,SAAS,EAAC,6CAA6C;YAAAC,QAAA,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNtB,OAAA;QAAKgB,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAE9DjB,OAAA;UAAKgB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,GACjCJ,WAAW,CAACZ,UAAU,CAAC,CAAC,CAAC,CAAC,EAAC,GAAC,EAC5BY,WAAW,CAACZ,UAAU,CAAC,CAAC,CAAC,CAAC,EAAC,GAAC,EAC5BY,WAAW,CAACZ,UAAU,CAAC,CAAC,CAAC,CAAC,EAAC,GAAC,EAC5BY,WAAW,CAACZ,UAAU,CAAC,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAENtB,OAAA;UAAKgB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,GACjCJ,WAAW,CAACZ,UAAU,CAAC,CAAC,CAAC,CAAC,EAAC,GAAC,eAC7BD,OAAA;YAAKgB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,GACpCJ,WAAW,CAACZ,UAAU,CAAC,CAAC,CAAC,CAAC,EAAC,GAAC,EAC5BY,WAAW,CAACZ,UAAU,CAAC,CAAC,CAAC,CAAC,EAAC,GAAC;UAAA;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACI,EAAA,GA1DInB,eAA+C;AA4DrD,eAAeA,eAAe;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}