{"ast": null, "code": "import moment from \"moment\";\nimport { DATE_FORMAT } from \"../../../..\";\nimport DOMPurify from \"dompurify\";\nexport let TargetType = /*#__PURE__*/function (TargetType) {\n  TargetType[\"WEB\"] = \"Web\";\n  TargetType[\"IOS\"] = \"iOS\";\n  TargetType[\"ANDROID\"] = \"Android\";\n  TargetType[\"API\"] = \"API\";\n  TargetType[\"WEB3\"] = \"Web3\";\n  TargetType[\"CODE\"] = \"Code\";\n  TargetType[\"PROTOCOL\"] = \"Protocol\";\n  TargetType[\"DATABASE\"] = \"Database\";\n  TargetType[\"NETWORK\"] = \"Network/Infrastructure\";\n  TargetType[\"CONTRACT\"] = \"Smart Contract\";\n  TargetType[\"OTHER\"] = \"Other\";\n  return TargetType;\n}({});\nexport let ProgramType = /*#__PURE__*/function (ProgramType) {\n  ProgramType[\"BugBounty\"] = \"Bug Bounty\";\n  ProgramType[\"VDP\"] = \"VDP\";\n  ProgramType[\"PTAAS\"] = \"PTAAS\";\n  return ProgramType;\n}({});\nconst parsePayoutRange = range => {\n  const values = range === null || range === void 0 ? void 0 : range.split(\",\").map(v => parseInt(v));\n  return values && values.length === 4 ? {\n    low: values[0],\n    medium: values[1],\n    high: values[2],\n    critical: values[3]\n  } : undefined;\n};\nexport const parseProgramDetails = program => {\n  // Parse attachments properly\n  let attachments = [];\n  if (program.attachments) {\n    if (typeof program.attachments === \"string\") {\n      try {\n        attachments = JSON.parse(program.attachments);\n      } catch (e) {\n        console.error(\"Failed to parse attachments:\", e);\n        attachments = [];\n      }\n    } else if (Array.isArray(program.attachments)) {\n      attachments = program.attachments;\n    }\n  }\n  return {\n    id: program.program_id,\n    isActivated: program.is_activated,\n    userId: program.user_id,\n    isDelete: program.is_delete,\n    private: program.private,\n    privateAccessUsers: program.private_access_users,\n    type: program.type,\n    triaged: program.triage_service_opted === \"1\",\n    payoutRange: parsePayoutRange(program.payout_range),\n    title: program.program_title,\n    description: program.description,\n    scope: program.scope,\n    knownVulnerabilities: program.known_vuln,\n    other: program.other,\n    numTargets: JSON.parse(program.targets),\n    vpn: program.vpn,\n    credentials: program.credentials,\n    rewardPolicy: program.reward_policy,\n    termsOfService: program.tos,\n    profilePicture: program.pfp,\n    outOfScope: program.out_of_scope,\n    targets: JSON.parse(program.targets) || [],\n    creator: program.creator,\n    updatedAt: moment(program.updatedAt).format(DATE_FORMAT),\n    createdAt: moment(program.createdAt).format(DATE_FORMAT),\n    testing_type: program.testing_type,\n    compliance_type: program.compliance_type,\n    other_compliance_type: program.other_compliance_type,\n    environment_type: program.environment_type,\n    expected_start_date: program.expected_start_date,\n    expected_end_date: program.expected_end_date,\n    slack_channel_link: program.slack_channel_link,\n    notification_methods: program.notification_methods,\n    attachments: attachments,\n    // Use the properly parsed attachments\n    test_lead: program.test_lead,\n    prepared_by: program.prepared_by,\n    reviewed_by: program.reviewed_by,\n    approved_by: program.approved_by\n  };\n};\n\n/**\r\n * Convert program update fields into form data\r\n * to be used in an API request\r\n */\nexport const prepareProgramFormData = ({\n  details,\n  icon,\n  attachments\n}) => {\n  var _details$payoutRange;\n  // Format payload for backend API\n  console.log(\"Details before constructing body:\", {\n    ...details,\n    jira_url: details.jira_url ? \"[EXISTS]\" : \"[MISSING]\",\n    jira_email: details.jira_email ? \"[EXISTS]\" : \"[MISSING]\",\n    jira_api_token: details.jira_api_token ? \"[EXISTS]\" : \"[MISSING]\",\n    jira_project_key: details.jira_project_key ? \"[EXISTS]\" : \"[MISSING]\",\n    slack_channel_link: details.slack_channel_link ? \"[EXISTS]\" : \"[MISSING]\"\n  });\n  const body = {\n    program_title: details.title,\n    description: details.description,\n    scope: details.scope,\n    out_of_scope: details.outOfScope,\n    known_vuln: details.knownVulnerabilities,\n    other: details.other,\n    payout_range: (_details$payoutRange = details.payoutRange) === null || _details$payoutRange === void 0 ? void 0 : _details$payoutRange.join(\",\"),\n    targets: details.targets,\n    vpn: details.vpn,\n    tos: details.termsOfService,\n    credentials: details.credentials,\n    reward_policy: details.rewardPolicy,\n    type: details.type,\n    private: details.private,\n    triage_service_opted: details.triaged,\n    compliance_type: details.complianceType,\n    other_compliance_type: details.otherComplianceType,\n    testing_type: details.testingType,\n    environment_type: details.environmentType,\n    expected_start_date: details.expectedStartDate,\n    expected_end_date: details.expectedEndDate,\n    notification_methods: details.notification_methods,\n    // Individual Jira fields\n    jira_url: details.jira_url,\n    jira_email: details.jira_email,\n    jira_api_token: details.jira_api_token,\n    jira_project_key: details.jira_project_key,\n    // Slack channel link\n    slack_channel_link: details.slack_channel_link,\n    // Include the existing attachments that weren't deleted\n    existing_attachments: details.existingAttachments || []\n  };\n  const formData = new FormData();\n  formData.append(\"data\", JSON.stringify(body));\n\n  // Only append profile picture if provided\n  if (icon) {\n    formData.append(\"program_pfp\", icon);\n  }\n\n  // Append all attachments if provided\n  if (attachments && attachments.length > 0) {\n    attachments.forEach(file => {\n      formData.append(\"attachments\", file);\n    });\n  }\n  return formData;\n};\nexport let NotificationMethods = /*#__PURE__*/function (NotificationMethods) {\n  NotificationMethods[\"CTB\"] = \"notify-ctb\";\n  NotificationMethods[\"SLACK\"] = \"notify-slack\";\n  NotificationMethods[\"JIRA\"] = \"notify-jira\";\n  return NotificationMethods;\n}({});\nexport const parseProgramOverview = data => {\n  var _data$user, _data$user$about, _data$program, _data$program$descrip, _data$program$id, _data$program2, _data$program$status, _data$program3, _data$program$total_r, _data$program4, _data$program$testing, _data$program5, _data$program$testing2, _data$program6, _data$program7, _data$program8, _data$program9, _data$user$username, _data$user2, _data$user$profile_pi, _data$user3, _ref, _data$user$display_na, _data$user4, _data$user5;\n  const about = (_data$user = data.user) === null || _data$user === void 0 ? void 0 : (_data$user$about = _data$user.about) === null || _data$user$about === void 0 ? void 0 : _data$user$about.trim();\n  const rawDescription = ((_data$program = data.program) === null || _data$program === void 0 ? void 0 : (_data$program$descrip = _data$program.description) === null || _data$program$descrip === void 0 ? void 0 : _data$program$descrip.trim()) || \"No description available\";\n  return {\n    program: {\n      id: (_data$program$id = (_data$program2 = data.program) === null || _data$program2 === void 0 ? void 0 : _data$program2.id) !== null && _data$program$id !== void 0 ? _data$program$id : 0,\n      status: (_data$program$status = (_data$program3 = data.program) === null || _data$program3 === void 0 ? void 0 : _data$program3.status) !== null && _data$program$status !== void 0 ? _data$program$status : \"Inactive\",\n      totalReports: (_data$program$total_r = (_data$program4 = data.program) === null || _data$program4 === void 0 ? void 0 : _data$program4.total_reports) !== null && _data$program$total_r !== void 0 ? _data$program$total_r : 0,\n      testingPeriod: (_data$program$testing = (_data$program5 = data.program) === null || _data$program5 === void 0 ? void 0 : _data$program5.testing_period) !== null && _data$program$testing !== void 0 ? _data$program$testing : \"N/A\",\n      testingStatus: (_data$program$testing2 = (_data$program6 = data.program) === null || _data$program6 === void 0 ? void 0 : _data$program6.testing_status) !== null && _data$program$testing2 !== void 0 ? _data$program$testing2 : \"N/A\",\n      type: (_data$program7 = data.program) === null || _data$program7 === void 0 ? void 0 : _data$program7.type,\n      expected_start_date: (_data$program8 = data.program) !== null && _data$program8 !== void 0 && _data$program8.expected_start_date ? moment(data.program.expected_start_date).format(\"YYYY-MM-DD\") : \"Invalid date\",\n      createdAt: (_data$program9 = data.program) !== null && _data$program9 !== void 0 && _data$program9.created_at ? moment(data.program.created_at).format(\"YYYY-MM-DD\") : \"Invalid date\",\n      description: DOMPurify.sanitize(rawDescription) // Sanitize HTML before storing\n    },\n    user: {\n      username: (_data$user$username = (_data$user2 = data.user) === null || _data$user2 === void 0 ? void 0 : _data$user2.username) !== null && _data$user$username !== void 0 ? _data$user$username : \"Unknown\",\n      profilePicture: (_data$user$profile_pi = (_data$user3 = data.user) === null || _data$user3 === void 0 ? void 0 : _data$user3.profile_picture) !== null && _data$user$profile_pi !== void 0 ? _data$user$profile_pi : \"\",\n      about: about || rawDescription,\n      // Fallback to description if about is empty\n      displayName: (_ref = (_data$user$display_na = (_data$user4 = data.user) === null || _data$user4 === void 0 ? void 0 : _data$user4.display_name) !== null && _data$user$display_na !== void 0 ? _data$user$display_na : (_data$user5 = data.user) === null || _data$user5 === void 0 ? void 0 : _data$user5.username) !== null && _ref !== void 0 ? _ref : \"Unknown\"\n    }\n  };\n};", "map": {"version": 3, "names": ["moment", "DATE_FORMAT", "DOMPurify", "TargetType", "ProgramType", "parsePayoutRange", "range", "values", "split", "map", "v", "parseInt", "length", "low", "medium", "high", "critical", "undefined", "parseProgramDetails", "program", "attachments", "JSON", "parse", "e", "console", "error", "Array", "isArray", "id", "program_id", "isActivated", "is_activated", "userId", "user_id", "isDelete", "is_delete", "private", "privateAccessUsers", "private_access_users", "type", "triaged", "triage_service_opted", "payoutRange", "payout_range", "title", "program_title", "description", "scope", "knownVulnerabilities", "known_vuln", "other", "numTargets", "targets", "vpn", "credentials", "rewardPolicy", "reward_policy", "termsOfService", "tos", "profilePicture", "pfp", "outOfScope", "out_of_scope", "creator", "updatedAt", "format", "createdAt", "testing_type", "compliance_type", "other_compliance_type", "environment_type", "expected_start_date", "expected_end_date", "slack_channel_link", "notification_methods", "test_lead", "prepared_by", "reviewed_by", "approved_by", "prepareProgramFormData", "details", "icon", "_details$payoutRange", "log", "jira_url", "jira_email", "jira_api_token", "jira_project_key", "body", "join", "complianceType", "otherComplianceType", "testingType", "environmentType", "expectedStartDate", "expectedEndDate", "existing_attachments", "existingAttachments", "formData", "FormData", "append", "stringify", "for<PERSON>ach", "file", "NotificationMethods", "parseProgramOverview", "data", "_data$user", "_data$user$about", "_data$program", "_data$program$descrip", "_data$program$id", "_data$program2", "_data$program$status", "_data$program3", "_data$program$total_r", "_data$program4", "_data$program$testing", "_data$program5", "_data$program$testing2", "_data$program6", "_data$program7", "_data$program8", "_data$program9", "_data$user$username", "_data$user2", "_data$user$profile_pi", "_data$user3", "_ref", "_data$user$display_na", "_data$user4", "_data$user5", "about", "user", "trim", "rawDescription", "status", "totalReports", "total_reports", "testingPeriod", "testing_period", "testingStatus", "testing_status", "created_at", "sanitize", "username", "profile_picture", "displayName", "display_name"], "sources": ["C:/Users/<USER>/Desktop/jira-old/CTB-Frontend/src/utils/api/endpoints/programs/parsePrograms.ts"], "sourcesContent": ["import moment from \"moment\";\r\nimport { BaseItem } from \"../../axios\";\r\nimport { RewardTiers } from \"../../../../components/editor/inputs/programs/ProgramRewardsInput\";\r\nimport { DATE_FORMAT } from \"../../../..\";\r\nimport { ProgramUpdateValues } from \"../../../hooks/programs/useProgram\";\r\nimport DOMPurify from \"dompurify\";\r\n\r\nexport type ProgramCreator = {\r\n  displayName: string;\r\n  pfp?: string;\r\n};\r\n\r\nexport type ProgramAPIResponse = {\r\n  program_id: number;\r\n  is_activated: boolean;\r\n  user_id: number;\r\n  is_delete: boolean;\r\n  private: boolean;\r\n  private_access_users: number[];\r\n  type: ProgramType;\r\n  triage_service_opted: string;\r\n  payout_range: string;\r\n  program_title: string;\r\n  description: string;\r\n  scope: string;\r\n  out_of_scope: string;\r\n  known_vuln: string;\r\n  other: string;\r\n  targets: string;\r\n  vpn: string;\r\n  credentials: string;\r\n  reward_policy: string;\r\n  tos: string;\r\n  pfp: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  creator?: ProgramCreator;\r\n  testing_type?: string;\r\n  environment_type?: string;\r\n  compliance_type?: string;\r\n  other_compliance_type?: string;\r\n  expected_start_date?: string;\r\n  expected_end_date?: string;\r\n  slack_channel_link: string;\r\n  notification_methods?: string[];\r\n  attachments?: string[];\r\n  test_lead?: string;\r\n  prepared_by?: string;\r\n  reviewed_by?: string;\r\n  approved_by?: string;\r\n  jira_url?: string;\r\n  jira_email?: string;\r\n  jira_api_token?: string;\r\n  jira_project_key?: string;\r\n};\r\n\r\nexport type CTBProgram = {\r\n  title: string;\r\n  isActivated: boolean;\r\n  isDelete: boolean;\r\n  private: boolean;\r\n  triaged: boolean;\r\n  targets: ProgramTarget[];\r\n  updatedAt: string;\r\n  createdAt: string;\r\n  userId?: number;\r\n  privateAccessUsers?: number[];\r\n  type?: ProgramType;\r\n  payoutRange?: RewardTiers;\r\n  description?: string;\r\n  scope?: string;\r\n  knownVulnerabilities?: string;\r\n  other?: string;\r\n  numTargets?: string;\r\n  vpn?: string;\r\n  credentials?: string;\r\n  rewardPolicy?: string;\r\n  termsOfService?: string;\r\n  profilePicture?: string;\r\n  outOfScope?: string;\r\n  creator?: ProgramCreator;\r\n  testing_type?: string;\r\n  environment_type?: string;\r\n  compliance_type?: string;\r\n  other_compliance_type?: string;\r\n  expected_start_date?: string;\r\n  expected_end_date?: string;\r\n  slack_channel_link: string;\r\n  notification_methods?: string[];\r\n  attachments?: string[];\r\n  test_lead?: string;\r\n  prepared_by?: string;\r\n  reviewed_by?: string;\r\n  approved_by?: string;\r\n} & BaseItem;\r\n\r\nexport enum TargetType {\r\n  WEB = \"Web\",\r\n  IOS = \"iOS\",\r\n  ANDROID = \"Android\",\r\n  API = \"API\",\r\n  WEB3 = \"Web3\",\r\n  CODE = \"Code\",\r\n  PROTOCOL = \"Protocol\",\r\n  DATABASE = \"Database\",\r\n  NETWORK = \"Network/Infrastructure\",\r\n  CONTRACT = \"Smart Contract\",\r\n  OTHER = \"Other\"\r\n}\r\n\r\nexport type ProgramTarget = { targetName: string; targetType: TargetType };\r\n\r\nexport enum ProgramType {\r\n  BugBounty = \"Bug Bounty\",\r\n  VDP = \"VDP\",\r\n  PTAAS = \"PTAAS\"\r\n}\r\n\r\nconst parsePayoutRange = (range?: string): RewardTiers | undefined => {\r\n  const values = range?.split(\",\").map(v => parseInt(v));\r\n\r\n  return values && values.length === 4\r\n    ? {\r\n        low: values[0],\r\n        medium: values[1],\r\n        high: values[2],\r\n        critical: values[3]\r\n      }\r\n    : undefined;\r\n};\r\n\r\nexport const parseProgramDetails = (\r\n  program: ProgramAPIResponse\r\n): CTBProgram => {\r\n  // Parse attachments properly\r\n  let attachments: string[] = [];\r\n  if (program.attachments) {\r\n    if (typeof program.attachments === \"string\") {\r\n      try {\r\n        attachments = JSON.parse(program.attachments);\r\n      } catch (e) {\r\n        console.error(\"Failed to parse attachments:\", e);\r\n        attachments = [];\r\n      }\r\n    } else if (Array.isArray(program.attachments)) {\r\n      attachments = program.attachments;\r\n    }\r\n  }\r\n\r\n  return {\r\n    id: program.program_id,\r\n    isActivated: program.is_activated,\r\n    userId: program.user_id,\r\n    isDelete: program.is_delete,\r\n    private: program.private,\r\n    privateAccessUsers: program.private_access_users,\r\n    type: program.type,\r\n    triaged: program.triage_service_opted === \"1\",\r\n    payoutRange: parsePayoutRange(program.payout_range),\r\n    title: program.program_title,\r\n    description: program.description,\r\n    scope: program.scope,\r\n    knownVulnerabilities: program.known_vuln,\r\n    other: program.other,\r\n    numTargets: JSON.parse(program.targets),\r\n    vpn: program.vpn,\r\n    credentials: program.credentials,\r\n    rewardPolicy: program.reward_policy,\r\n    termsOfService: program.tos,\r\n    profilePicture: program.pfp,\r\n    outOfScope: program.out_of_scope,\r\n    targets: JSON.parse(program.targets) || [],\r\n    creator: program.creator,\r\n    updatedAt: moment(program.updatedAt).format(DATE_FORMAT),\r\n    createdAt: moment(program.createdAt).format(DATE_FORMAT),\r\n    testing_type: program.testing_type,\r\n    compliance_type: program.compliance_type,\r\n    other_compliance_type: program.other_compliance_type,\r\n    environment_type: program.environment_type,\r\n    expected_start_date: program.expected_start_date,\r\n    expected_end_date: program.expected_end_date,\r\n    slack_channel_link: program.slack_channel_link,\r\n    notification_methods: program.notification_methods,\r\n    attachments: attachments, // Use the properly parsed attachments\r\n    test_lead: program.test_lead,\r\n    prepared_by: program.prepared_by,\r\n    reviewed_by: program.reviewed_by,\r\n    approved_by: program.approved_by\r\n  };\r\n};\r\n\r\n/**\r\n * Convert program update fields into form data\r\n * to be used in an API request\r\n */\r\nexport const prepareProgramFormData = ({\r\n  details,\r\n  icon,\r\n  attachments\r\n}: {\r\n  details: ProgramUpdateValues & {\r\n    payoutRange?: number[];\r\n    triaged?: boolean;\r\n    existingAttachments?: string[];\r\n  };\r\n  icon?: Blob;\r\n  attachments?: Blob[];\r\n}) => {\r\n  // Format payload for backend API\r\n  console.log(\"Details before constructing body:\", {\r\n    ...details,\r\n    jira_url: details.jira_url ? \"[EXISTS]\" : \"[MISSING]\",\r\n    jira_email: details.jira_email ? \"[EXISTS]\" : \"[MISSING]\",\r\n    jira_api_token: details.jira_api_token ? \"[EXISTS]\" : \"[MISSING]\",\r\n    jira_project_key: details.jira_project_key ? \"[EXISTS]\" : \"[MISSING]\",\r\n    slack_channel_link: details.slack_channel_link ? \"[EXISTS]\" : \"[MISSING]\"\r\n  });\r\n  \r\n  const body = {\r\n    program_title: details.title,\r\n    description: details.description,\r\n    scope: details.scope,\r\n    out_of_scope: details.outOfScope,\r\n    known_vuln: details.knownVulnerabilities,\r\n    other: details.other,\r\n    payout_range: details.payoutRange?.join(\",\"),\r\n    targets: details.targets,\r\n    vpn: details.vpn,\r\n    tos: details.termsOfService,\r\n    credentials: details.credentials,\r\n    reward_policy: details.rewardPolicy,\r\n    type: details.type,\r\n    private: details.private,\r\n    triage_service_opted: details.triaged,\r\n    compliance_type: details.complianceType,\r\n    other_compliance_type: details.otherComplianceType,\r\n    testing_type: details.testingType,\r\n    environment_type: details.environmentType,\r\n    expected_start_date: details.expectedStartDate,\r\n    expected_end_date: details.expectedEndDate,\r\n    notification_methods: details.notification_methods,\r\n    // Individual Jira fields\r\n    jira_url: details.jira_url,\r\n    jira_email: details.jira_email,\r\n    jira_api_token: details.jira_api_token,\r\n    jira_project_key: details.jira_project_key,\r\n    // Slack channel link\r\n    slack_channel_link: details.slack_channel_link,\r\n    // Include the existing attachments that weren't deleted\r\n    existing_attachments: details.existingAttachments || []\r\n  };\r\n\r\n  const formData = new FormData();\r\n  formData.append(\"data\", JSON.stringify(body));\r\n  \r\n  // Only append profile picture if provided\r\n  if (icon) {\r\n    formData.append(\"program_pfp\", icon);\r\n  }\r\n\r\n  // Append all attachments if provided\r\n  if (attachments && attachments.length > 0) {\r\n    attachments.forEach(file => {\r\n      formData.append(\"attachments\", file);\r\n    });\r\n  }\r\n\r\n  return formData;\r\n};\r\n\r\nexport enum NotificationMethods {\r\n  CTB = \"notify-ctb\",\r\n  SLACK = \"notify-slack\",\r\n  JIRA = \"notify-jira\"\r\n}\r\n\r\nexport type ProgramOverview = {\r\n  id: number;\r\n  status: string;\r\n  totalReports: number;\r\n  testingPeriod: string | null;\r\n  testingStatus: string;\r\n  createdAt: string;\r\n  description: string;\r\n  expected_start_date?: string;\r\n  type?: string;\r\n};\r\n\r\nexport type UserDetails = {\r\n  username: string;\r\n  profilePicture: string;\r\n  about: string;\r\n  displayName: string;\r\n};\r\n\r\nexport type ProgramOverviewResponse = {\r\n  program: ProgramOverview;\r\n  user: UserDetails;\r\n};\r\n\r\nexport const parseProgramOverview = (data: any): ProgramOverviewResponse => {\r\n  const about = data.user?.about?.trim();\r\n  const rawDescription =\r\n    data.program?.description?.trim() || \"No description available\";\r\n\r\n  return {\r\n    program: {\r\n      id: data.program?.id ?? 0,\r\n      status: data.program?.status ?? \"Inactive\",\r\n      totalReports: data.program?.total_reports ?? 0,\r\n      testingPeriod: data.program?.testing_period ?? \"N/A\",\r\n      testingStatus: data.program?.testing_status ?? \"N/A\",\r\n      type: data.program?.type,\r\n      expected_start_date: data.program?.expected_start_date\r\n        ? moment(data.program.expected_start_date).format(\"YYYY-MM-DD\")\r\n        : \"Invalid date\",\r\n      createdAt: data.program?.created_at\r\n        ? moment(data.program.created_at).format(\"YYYY-MM-DD\")\r\n        : \"Invalid date\",\r\n      description: DOMPurify.sanitize(rawDescription) // Sanitize HTML before storing\r\n    },\r\n    user: {\r\n      username: data.user?.username ?? \"Unknown\",\r\n      profilePicture: data.user?.profile_picture ?? \"\",\r\n      about: about || rawDescription, // Fallback to description if about is empty\r\n      displayName: data.user?.display_name ?? data.user?.username ?? \"Unknown\"\r\n    }\r\n  };\r\n};\r\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,QAAQ;AAG3B,SAASC,WAAW,QAAQ,aAAa;AAEzC,OAAOC,SAAS,MAAM,WAAW;AA2FjC,WAAYC,UAAU,0BAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAA,OAAVA,UAAU;AAAA;AAgBtB,WAAYC,WAAW,0BAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAA,OAAXA,WAAW;AAAA;AAMvB,MAAMC,gBAAgB,GAAIC,KAAc,IAA8B;EACpE,MAAMC,MAAM,GAAGD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIC,QAAQ,CAACD,CAAC,CAAC,CAAC;EAEtD,OAAOH,MAAM,IAAIA,MAAM,CAACK,MAAM,KAAK,CAAC,GAChC;IACEC,GAAG,EAAEN,MAAM,CAAC,CAAC,CAAC;IACdO,MAAM,EAAEP,MAAM,CAAC,CAAC,CAAC;IACjBQ,IAAI,EAAER,MAAM,CAAC,CAAC,CAAC;IACfS,QAAQ,EAAET,MAAM,CAAC,CAAC;EACpB,CAAC,GACDU,SAAS;AACf,CAAC;AAED,OAAO,MAAMC,mBAAmB,GAC9BC,OAA2B,IACZ;EACf;EACA,IAAIC,WAAqB,GAAG,EAAE;EAC9B,IAAID,OAAO,CAACC,WAAW,EAAE;IACvB,IAAI,OAAOD,OAAO,CAACC,WAAW,KAAK,QAAQ,EAAE;MAC3C,IAAI;QACFA,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAACC,WAAW,CAAC;MAC/C,CAAC,CAAC,OAAOG,CAAC,EAAE;QACVC,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEF,CAAC,CAAC;QAChDH,WAAW,GAAG,EAAE;MAClB;IACF,CAAC,MAAM,IAAIM,KAAK,CAACC,OAAO,CAACR,OAAO,CAACC,WAAW,CAAC,EAAE;MAC7CA,WAAW,GAAGD,OAAO,CAACC,WAAW;IACnC;EACF;EAEA,OAAO;IACLQ,EAAE,EAAET,OAAO,CAACU,UAAU;IACtBC,WAAW,EAAEX,OAAO,CAACY,YAAY;IACjCC,MAAM,EAAEb,OAAO,CAACc,OAAO;IACvBC,QAAQ,EAAEf,OAAO,CAACgB,SAAS;IAC3BC,OAAO,EAAEjB,OAAO,CAACiB,OAAO;IACxBC,kBAAkB,EAAElB,OAAO,CAACmB,oBAAoB;IAChDC,IAAI,EAAEpB,OAAO,CAACoB,IAAI;IAClBC,OAAO,EAAErB,OAAO,CAACsB,oBAAoB,KAAK,GAAG;IAC7CC,WAAW,EAAErC,gBAAgB,CAACc,OAAO,CAACwB,YAAY,CAAC;IACnDC,KAAK,EAAEzB,OAAO,CAAC0B,aAAa;IAC5BC,WAAW,EAAE3B,OAAO,CAAC2B,WAAW;IAChCC,KAAK,EAAE5B,OAAO,CAAC4B,KAAK;IACpBC,oBAAoB,EAAE7B,OAAO,CAAC8B,UAAU;IACxCC,KAAK,EAAE/B,OAAO,CAAC+B,KAAK;IACpBC,UAAU,EAAE9B,IAAI,CAACC,KAAK,CAACH,OAAO,CAACiC,OAAO,CAAC;IACvCC,GAAG,EAAElC,OAAO,CAACkC,GAAG;IAChBC,WAAW,EAAEnC,OAAO,CAACmC,WAAW;IAChCC,YAAY,EAAEpC,OAAO,CAACqC,aAAa;IACnCC,cAAc,EAAEtC,OAAO,CAACuC,GAAG;IAC3BC,cAAc,EAAExC,OAAO,CAACyC,GAAG;IAC3BC,UAAU,EAAE1C,OAAO,CAAC2C,YAAY;IAChCV,OAAO,EAAE/B,IAAI,CAACC,KAAK,CAACH,OAAO,CAACiC,OAAO,CAAC,IAAI,EAAE;IAC1CW,OAAO,EAAE5C,OAAO,CAAC4C,OAAO;IACxBC,SAAS,EAAEhE,MAAM,CAACmB,OAAO,CAAC6C,SAAS,CAAC,CAACC,MAAM,CAAChE,WAAW,CAAC;IACxDiE,SAAS,EAAElE,MAAM,CAACmB,OAAO,CAAC+C,SAAS,CAAC,CAACD,MAAM,CAAChE,WAAW,CAAC;IACxDkE,YAAY,EAAEhD,OAAO,CAACgD,YAAY;IAClCC,eAAe,EAAEjD,OAAO,CAACiD,eAAe;IACxCC,qBAAqB,EAAElD,OAAO,CAACkD,qBAAqB;IACpDC,gBAAgB,EAAEnD,OAAO,CAACmD,gBAAgB;IAC1CC,mBAAmB,EAAEpD,OAAO,CAACoD,mBAAmB;IAChDC,iBAAiB,EAAErD,OAAO,CAACqD,iBAAiB;IAC5CC,kBAAkB,EAAEtD,OAAO,CAACsD,kBAAkB;IAC9CC,oBAAoB,EAAEvD,OAAO,CAACuD,oBAAoB;IAClDtD,WAAW,EAAEA,WAAW;IAAE;IAC1BuD,SAAS,EAAExD,OAAO,CAACwD,SAAS;IAC5BC,WAAW,EAAEzD,OAAO,CAACyD,WAAW;IAChCC,WAAW,EAAE1D,OAAO,CAAC0D,WAAW;IAChCC,WAAW,EAAE3D,OAAO,CAAC2D;EACvB,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMC,sBAAsB,GAAGA,CAAC;EACrCC,OAAO;EACPC,IAAI;EACJ7D;AASF,CAAC,KAAK;EAAA,IAAA8D,oBAAA;EACJ;EACA1D,OAAO,CAAC2D,GAAG,CAAC,mCAAmC,EAAE;IAC/C,GAAGH,OAAO;IACVI,QAAQ,EAAEJ,OAAO,CAACI,QAAQ,GAAG,UAAU,GAAG,WAAW;IACrDC,UAAU,EAAEL,OAAO,CAACK,UAAU,GAAG,UAAU,GAAG,WAAW;IACzDC,cAAc,EAAEN,OAAO,CAACM,cAAc,GAAG,UAAU,GAAG,WAAW;IACjEC,gBAAgB,EAAEP,OAAO,CAACO,gBAAgB,GAAG,UAAU,GAAG,WAAW;IACrEd,kBAAkB,EAAEO,OAAO,CAACP,kBAAkB,GAAG,UAAU,GAAG;EAChE,CAAC,CAAC;EAEF,MAAMe,IAAI,GAAG;IACX3C,aAAa,EAAEmC,OAAO,CAACpC,KAAK;IAC5BE,WAAW,EAAEkC,OAAO,CAAClC,WAAW;IAChCC,KAAK,EAAEiC,OAAO,CAACjC,KAAK;IACpBe,YAAY,EAAEkB,OAAO,CAACnB,UAAU;IAChCZ,UAAU,EAAE+B,OAAO,CAAChC,oBAAoB;IACxCE,KAAK,EAAE8B,OAAO,CAAC9B,KAAK;IACpBP,YAAY,GAAAuC,oBAAA,GAAEF,OAAO,CAACtC,WAAW,cAAAwC,oBAAA,uBAAnBA,oBAAA,CAAqBO,IAAI,CAAC,GAAG,CAAC;IAC5CrC,OAAO,EAAE4B,OAAO,CAAC5B,OAAO;IACxBC,GAAG,EAAE2B,OAAO,CAAC3B,GAAG;IAChBK,GAAG,EAAEsB,OAAO,CAACvB,cAAc;IAC3BH,WAAW,EAAE0B,OAAO,CAAC1B,WAAW;IAChCE,aAAa,EAAEwB,OAAO,CAACzB,YAAY;IACnChB,IAAI,EAAEyC,OAAO,CAACzC,IAAI;IAClBH,OAAO,EAAE4C,OAAO,CAAC5C,OAAO;IACxBK,oBAAoB,EAAEuC,OAAO,CAACxC,OAAO;IACrC4B,eAAe,EAAEY,OAAO,CAACU,cAAc;IACvCrB,qBAAqB,EAAEW,OAAO,CAACW,mBAAmB;IAClDxB,YAAY,EAAEa,OAAO,CAACY,WAAW;IACjCtB,gBAAgB,EAAEU,OAAO,CAACa,eAAe;IACzCtB,mBAAmB,EAAES,OAAO,CAACc,iBAAiB;IAC9CtB,iBAAiB,EAAEQ,OAAO,CAACe,eAAe;IAC1CrB,oBAAoB,EAAEM,OAAO,CAACN,oBAAoB;IAClD;IACAU,QAAQ,EAAEJ,OAAO,CAACI,QAAQ;IAC1BC,UAAU,EAAEL,OAAO,CAACK,UAAU;IAC9BC,cAAc,EAAEN,OAAO,CAACM,cAAc;IACtCC,gBAAgB,EAAEP,OAAO,CAACO,gBAAgB;IAC1C;IACAd,kBAAkB,EAAEO,OAAO,CAACP,kBAAkB;IAC9C;IACAuB,oBAAoB,EAAEhB,OAAO,CAACiB,mBAAmB,IAAI;EACvD,CAAC;EAED,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE/E,IAAI,CAACgF,SAAS,CAACb,IAAI,CAAC,CAAC;;EAE7C;EACA,IAAIP,IAAI,EAAE;IACRiB,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEnB,IAAI,CAAC;EACtC;;EAEA;EACA,IAAI7D,WAAW,IAAIA,WAAW,CAACR,MAAM,GAAG,CAAC,EAAE;IACzCQ,WAAW,CAACkF,OAAO,CAACC,IAAI,IAAI;MAC1BL,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEG,IAAI,CAAC;IACtC,CAAC,CAAC;EACJ;EAEA,OAAOL,QAAQ;AACjB,CAAC;AAED,WAAYM,mBAAmB,0BAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAA,OAAnBA,mBAAmB;AAAA;AA8B/B,OAAO,MAAMC,oBAAoB,GAAIC,IAAS,IAA8B;EAAA,IAAAC,UAAA,EAAAC,gBAAA,EAAAC,aAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,cAAA,EAAAC,oBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,sBAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA,EAAAC,WAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,IAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,WAAA;EAC1E,MAAMC,KAAK,IAAAzB,UAAA,GAAGD,IAAI,CAAC2B,IAAI,cAAA1B,UAAA,wBAAAC,gBAAA,GAATD,UAAA,CAAWyB,KAAK,cAAAxB,gBAAA,uBAAhBA,gBAAA,CAAkB0B,IAAI,CAAC,CAAC;EACtC,MAAMC,cAAc,GAClB,EAAA1B,aAAA,GAAAH,IAAI,CAACvF,OAAO,cAAA0F,aAAA,wBAAAC,qBAAA,GAAZD,aAAA,CAAc/D,WAAW,cAAAgE,qBAAA,uBAAzBA,qBAAA,CAA2BwB,IAAI,CAAC,CAAC,KAAI,0BAA0B;EAEjE,OAAO;IACLnH,OAAO,EAAE;MACPS,EAAE,GAAAmF,gBAAA,IAAAC,cAAA,GAAEN,IAAI,CAACvF,OAAO,cAAA6F,cAAA,uBAAZA,cAAA,CAAcpF,EAAE,cAAAmF,gBAAA,cAAAA,gBAAA,GAAI,CAAC;MACzByB,MAAM,GAAAvB,oBAAA,IAAAC,cAAA,GAAER,IAAI,CAACvF,OAAO,cAAA+F,cAAA,uBAAZA,cAAA,CAAcsB,MAAM,cAAAvB,oBAAA,cAAAA,oBAAA,GAAI,UAAU;MAC1CwB,YAAY,GAAAtB,qBAAA,IAAAC,cAAA,GAAEV,IAAI,CAACvF,OAAO,cAAAiG,cAAA,uBAAZA,cAAA,CAAcsB,aAAa,cAAAvB,qBAAA,cAAAA,qBAAA,GAAI,CAAC;MAC9CwB,aAAa,GAAAtB,qBAAA,IAAAC,cAAA,GAAEZ,IAAI,CAACvF,OAAO,cAAAmG,cAAA,uBAAZA,cAAA,CAAcsB,cAAc,cAAAvB,qBAAA,cAAAA,qBAAA,GAAI,KAAK;MACpDwB,aAAa,GAAAtB,sBAAA,IAAAC,cAAA,GAAEd,IAAI,CAACvF,OAAO,cAAAqG,cAAA,uBAAZA,cAAA,CAAcsB,cAAc,cAAAvB,sBAAA,cAAAA,sBAAA,GAAI,KAAK;MACpDhF,IAAI,GAAAkF,cAAA,GAAEf,IAAI,CAACvF,OAAO,cAAAsG,cAAA,uBAAZA,cAAA,CAAclF,IAAI;MACxBgC,mBAAmB,EAAE,CAAAmD,cAAA,GAAAhB,IAAI,CAACvF,OAAO,cAAAuG,cAAA,eAAZA,cAAA,CAAcnD,mBAAmB,GAClDvE,MAAM,CAAC0G,IAAI,CAACvF,OAAO,CAACoD,mBAAmB,CAAC,CAACN,MAAM,CAAC,YAAY,CAAC,GAC7D,cAAc;MAClBC,SAAS,EAAE,CAAAyD,cAAA,GAAAjB,IAAI,CAACvF,OAAO,cAAAwG,cAAA,eAAZA,cAAA,CAAcoB,UAAU,GAC/B/I,MAAM,CAAC0G,IAAI,CAACvF,OAAO,CAAC4H,UAAU,CAAC,CAAC9E,MAAM,CAAC,YAAY,CAAC,GACpD,cAAc;MAClBnB,WAAW,EAAE5C,SAAS,CAAC8I,QAAQ,CAACT,cAAc,CAAC,CAAC;IAClD,CAAC;IACDF,IAAI,EAAE;MACJY,QAAQ,GAAArB,mBAAA,IAAAC,WAAA,GAAEnB,IAAI,CAAC2B,IAAI,cAAAR,WAAA,uBAATA,WAAA,CAAWoB,QAAQ,cAAArB,mBAAA,cAAAA,mBAAA,GAAI,SAAS;MAC1CjE,cAAc,GAAAmE,qBAAA,IAAAC,WAAA,GAAErB,IAAI,CAAC2B,IAAI,cAAAN,WAAA,uBAATA,WAAA,CAAWmB,eAAe,cAAApB,qBAAA,cAAAA,qBAAA,GAAI,EAAE;MAChDM,KAAK,EAAEA,KAAK,IAAIG,cAAc;MAAE;MAChCY,WAAW,GAAAnB,IAAA,IAAAC,qBAAA,IAAAC,WAAA,GAAExB,IAAI,CAAC2B,IAAI,cAAAH,WAAA,uBAATA,WAAA,CAAWkB,YAAY,cAAAnB,qBAAA,cAAAA,qBAAA,IAAAE,WAAA,GAAIzB,IAAI,CAAC2B,IAAI,cAAAF,WAAA,uBAATA,WAAA,CAAWc,QAAQ,cAAAjB,IAAA,cAAAA,IAAA,GAAI;IACjE;EACF,CAAC;AACH,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}