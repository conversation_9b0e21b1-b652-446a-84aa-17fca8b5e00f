import { BaseFilters } from "../../../components/filters/FilterToolBar";
import { api, createURLSearchParams } from "../api";
import axios, { BaseItem } from "../axios";
import { UserRole } from "./user/credentials";

export enum TriageStatus {
  UNDER_REVIEW = "underReview",
  APPROVED = "approved",
  REJECTED = "rejected",
  NOT_OPTED = "triage-not-opted"
}

export type UserAPIResponse = {
  user_id: number;
  email: string;
  username: string;
  role: UserRole;
  two_fa_enabled: boolean;
  is_approved: boolean;
  first_time_uxp: boolean;
  email_verified: boolean;
  verification_status: string;
  triage_status: string;
  about: string | null;
  country: string | null;
  pfp: string | null;
  display_name: string | null;
  links: string | null;
  skills: string | null;
  banner: string | null;
};

export type UserQueryFilters = {
  page?: string;
  limit?: string;
  roles?: string;
  ids?: string;
  active?: string;
  username?: string;
};

export type UserFilters = {
  page?: number;
  roles?: UserRole[];
  ids?: number[];
  active?: ("Active" | "Inactive")[];
} & BaseFilters;

export type User = {
  email: string;
  username: string;
  role: UserRole;
  twoFaEnabled: boolean;
  approved: boolean;
  firstTimeUXP: boolean;
  emailVerified: boolean;
  verificationStatus: string;
  triageStatus: string;
  displayName?: string;
  about?: string;
  country?: string;
  links?: string[];
  skills?: string[];
  profilePicture?: string;
  bannerPicture?: string;
} & BaseItem;

type PaginatedUsers = {
  users: User[];
  count: number;
};

type PaginatedUsersResponse = {
  users: UserAPIResponse[];
  count: number;
};

const parseUserDetails = (user: UserAPIResponse) =>
  ({
    id: user.user_id,
    email: user.email,
    role: user.role,
    approved: user.is_approved,
    username: user.username,
    displayName: user.display_name ? user.display_name : undefined,
    about: user.about ? user.about : undefined,
    links: user.links ? JSON.parse(user.links) : undefined,
    skills: user.skills ? JSON.parse(user.skills) : undefined,
    country: user.country ? user.country : undefined,
    profilePicture: user.pfp ? user.pfp : undefined,
    bannerPicture: user.banner ? user.banner : undefined
  }) as User;

const usersUrl = "/v2/user";

/**
 * Standalone get all users endpoint - used for inifite
 * scrolling when managing private access users
 */
export const getUsers = async ({
  page,
  limit,
  ids
}: {
  page: number;
  limit: number;
  ids: number[];
}) => {
  const response = (
    await axios.get(
      usersUrl + `/all?page=${page}&limit=${limit}&ids=${ids.join(",")}`
    )
  ).data as PaginatedUsersResponse;

  return {
    users: response.users.map(user => parseUserDetails(user)),
    count: response.count
  };
};

/**
 * The Manage Users API slice definition.
 *
 * These are the endpoint definitions for the user endpoints used
 * by admins when managing users - for regular user credetials and details
 * see relevant hooks
 */
export const userApi = api.injectEndpoints({
  endpoints: builder => ({
    /**
     * Retrieve all the users from the backend (for admin management)
     */
    getAllUsers: builder.query<PaginatedUsers, UserFilters>({
      query: filters => {
        const params = createURLSearchParams<UserQueryFilters>({
          page: filters?.page?.toString(),
          limit: filters?.limit.toString(),
          roles: filters?.roles?.join(","),
          ids: filters?.ids?.join(","),
          active: filters?.active?.map(a => a === "Active").join(","),
          username: filters?.search
        });

        return {
          url: usersUrl + "/all?" + params.toString(),
          method: "get"
        };
      },
      transformResponse: (response: PaginatedUsersResponse) => ({
        users: response.users.map(parseUserDetails),
        count: response.count
      }),
      providesTags: ["ManageUsers"]
    }),
    /**
     * Activate/deactivate the given user
     */
    setUserActivation: builder.mutation<
      { userId: number; isApproved: boolean },
      { userId: number; isApproved: boolean }
    >({
      query: ({ userId, isApproved }) => ({
        url: usersUrl + "/status",
        method: "post",
        data: {
          id: userId,
          approved: isApproved
        }
      }),
      invalidatesTags: ["ManageUsers"]
    })
  })
});

export const { useGetAllUsersQuery, useSetUserActivationMutation } = userApi;
