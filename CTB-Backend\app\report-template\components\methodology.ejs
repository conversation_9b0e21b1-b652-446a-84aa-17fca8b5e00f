

<div class="a4-page">
  <div class="absolute top-0 w-full text-center pt-8 pb-3 text-gray-700 text-xs font-medium tracking-wide">
    <p>© 2024 Capture The Bug Ltd. | All Rights Reserved</p>
    <p class="mt-1 font-semibold text-gray-700">CONFIDENTIAL</p>
</div>
    <div class="px-16 pt-24 flex flex-col h-full">
        <!--remove header -->
        
        <div class="flex-grow">
            <h1 class="text-2xl font-bold text-blue-500 mb-8">METHODOLOGY</h1>
            
            <div class="space-y-8 text-base leading-relaxed">
              <p class="text-lg">
                Security Consultants at Capture The Bug Ltd. used a combination of
                open-source and commercial tools when conducting the
                Vulnerability Assessment and Penetration Testing of <%= reportData.company_name %>'s systems.
              </p>
              
              <p class="text-lg">
                Vulnerability assessments are simply the process of locating and
                reporting vulnerabilities. Vulnerability Assessment and Penetration
                Testing (VAPT) is a method of evaluating the security of an
                application by simulating an attack. The process involves an active
                analysis of the application for any weaknesses, functional flaws, and
                vulnerabilities. Any security issues that are identified will be
                explained with an assessment of their impact, with a
                recommendation for their mitigation.
              </p>
              
              <% 
              // Determine test types based on program details
              let testTypes = [];
              if (reportData.program_details && reportData.program_details.length > 0) {
                reportData.program_details.forEach(program => {
                  if (program.testing_type && !testTypes.includes(program.testing_type)) {
                    testTypes.push(program.testing_type);
                  }
                });
              }
              
              let testTypeText = "web application";
              if (testTypes.length > 0) {
                if (testTypes.some(type => type.toLowerCase().includes("network"))) {
                  testTypeText = "network infrastructure";
                } else if (testTypes.some(type => type.toLowerCase().includes("api"))) {
                  testTypeText = "API";
                } else if (testTypes.some(type => type.toLowerCase().includes("mobile"))) {
                  testTypeText = "mobile application";
                } else if (testTypes.some(type => type.toLowerCase().includes("cloud"))) {
                  testTypeText = "cloud infrastructure";
                }
              }
              %>
              
              <p class="text-lg mb-8">
                The <%= testTypeText %> methodology is based on the 'grey-box'
                approach. The testing model consists of following phases:
              </p>
            </div>
            
            <div class="flex justify-center mb-8">
              <div class="bg-blue-50 p-3 rounded-lg border border-blue-100 w-full">
                <div class="h-80 bg-gray-100 flex items-center justify-center border border-dashed border-gray-300 rounded-lg">
                  <p class="text-gray-500 text-lg">
                    <% if (testTypeText.includes("web")) { %>
                      1. Reconnaissance<br>
                      2. Mapping the Application<br>
                      3. Vulnerability Discovery<br>
                      4. Exploitation<br>
                      5. Reporting
                    <% } else if (testTypeText.includes("network")) { %>
                      1. Intelligence Gathering<br>
                      2. Scanning & Enumeration<br>
                      3. Vulnerability Analysis<br>
                      4. Exploitation<br>
                      5. Post-Exploitation<br>
                      6. Reporting
                    <% } else if (testTypeText.includes("API")) { %>
                      1. Information Gathering<br>
                      2. API Endpoint Analysis<br>
                      3. Authentication Testing<br>
                      4. Authorization Testing<br>
                      5. Data Validation Testing<br>
                      6. Documentation & Reporting
                    <% } else if (testTypeText.includes("mobile")) { %>
                      1. Application Mapping<br>
                      2. Static Analysis<br>
                      3. Dynamic Analysis<br>
                      4. Client-Side Controls<br>
                      5. Network Communication<br>
                      6. Reporting
                    <% } else if (testTypeText.includes("cloud")) { %>
                      1. Identity & Access Management<br>
                      2. Configuration Assessment<br>
                      3. Data Protection Review<br>
                      4. Network Security<br>
                      5. Compliance Validation<br>
                      6. Reporting
                    <% } else { %>
                      1. Reconnaissance<br>
                      2. Scanning & Vulnerability Assessment<br>
                      3. Exploitation<br>
                      4. Reporting & Documentation
                    <% } %>
                  </p>
                </div>
              </div>
            </div>
            
            <p class="text-center italic text-base mb-10">Figure 2: <%= testTypeText.charAt(0).toUpperCase() + testTypeText.slice(1) %> Pentesting Methodology</p>
        </div>
    </div>
    <div class="pt-8 flex flex-col h-full">
       <!--remove  footer-->
    </div>
</div> 