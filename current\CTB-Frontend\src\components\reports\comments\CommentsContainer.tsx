import { LuMessagesSquare } from "react-icons/lu";

const CommentsContainer = ({ onClick }: { onClick: () => void }) => {
  return (
    <div
      className="blue-400 fixed bottom-0 right-0 mb-1 mr-2 flex items-center rounded-lg border-[1px] border-ctb-blue-100 bg-white
        p-2 text-ctb-blue-400 hover:cursor-pointer hover:border-ctb-blue-200 hover:bg-ctb-blue-200 hover:text-white"
      onClick={onClick}
    >
      Comments &nbsp; &nbsp; <LuMessagesSquare size={30} />
    </div>
  );
};

export default CommentsContainer;
