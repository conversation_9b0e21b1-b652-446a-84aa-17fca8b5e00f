{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\jira-old\\\\CTB-Frontend\\\\src\\\\pages\\\\automated-report\\\\components\\\\pdfHtmlImageUtils.tsx\";\nimport React from 'react';\nimport { Image, Text, View } from '@react-pdf/renderer';\n\n// ============= Types =============\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// ============= IMAGE PROCESSING UTILITIES =============\nclass ImageProcessor {\n  constructor() {\n    this.cache = void 0;\n    this.processingQueue = void 0;\n    this.cache = new Map();\n    this.processingQueue = new Map();\n  }\n  async processImage(imageUrl, options = {}) {\n    if (!imageUrl) return null;\n    if (this.cache.has(imageUrl)) {\n      var _this$cache$get;\n      return (_this$cache$get = this.cache.get(imageUrl)) !== null && _this$cache$get !== void 0 ? _this$cache$get : null;\n    }\n    if (this.processingQueue.has(imageUrl)) {\n      var _this$processingQueue;\n      return (_this$processingQueue = this.processingQueue.get(imageUrl)) !== null && _this$processingQueue !== void 0 ? _this$processingQueue : null;\n    }\n    const processingPromise = this._processImageInternal(imageUrl, options);\n    this.processingQueue.set(imageUrl, processingPromise);\n    try {\n      const result = await processingPromise;\n      this.cache.set(imageUrl, result);\n      return result;\n    } finally {\n      this.processingQueue.delete(imageUrl);\n    }\n  }\n  async _processImageInternal(imageUrl, options) {\n    if (imageUrl.startsWith('data:')) {\n      return imageUrl;\n    }\n    const canvasResult = await this._tryCanvasMethod(imageUrl);\n    if (canvasResult) return canvasResult;\n    const proxyResult = await this._tryProxyMethod(imageUrl);\n    if (proxyResult) return proxyResult;\n    return this._generatePlaceholder(imageUrl, options);\n  }\n  async _tryCanvasMethod(imageUrl) {\n    return new Promise(resolve => {\n      if (typeof window === 'undefined') return resolve(null);\n      const img = new window.Image();\n      const timeoutId = setTimeout(() => resolve(null), 5000);\n      img.onload = () => {\n        clearTimeout(timeoutId);\n        try {\n          const canvas = document.createElement('canvas');\n          const ctx = canvas.getContext('2d');\n          if (!ctx) return resolve(null);\n          const maxWidth = 800;\n          const maxHeight = 600;\n          let width = img.width;\n          let height = img.height;\n          if (width > maxWidth || height > maxHeight) {\n            const ratio = Math.min(maxWidth / width, maxHeight / height);\n            width *= ratio;\n            height *= ratio;\n          }\n          canvas.width = width;\n          canvas.height = height;\n          ctx.drawImage(img, 0, 0, width, height);\n          const dataURL = canvas.toDataURL('image/jpeg', 0.8);\n          resolve(dataURL);\n        } catch (error) {\n          resolve(null);\n        }\n      };\n      img.onerror = () => {\n        clearTimeout(timeoutId);\n        resolve(null);\n      };\n      img.crossOrigin = 'anonymous';\n      img.src = imageUrl;\n    });\n  }\n  async _tryProxyMethod(imageUrl) {\n    try {\n      const proxyUrl = `https://api.allorigins.win/raw?url=${encodeURIComponent(imageUrl)}`;\n      const response = await fetch(proxyUrl, {\n        method: 'GET'\n      });\n      if (!response.ok) return null;\n      const blob = await response.blob();\n      if (blob.size === 0) return null;\n      return new Promise(resolve => {\n        const reader = new FileReader();\n        reader.onloadend = () => resolve(reader.result);\n        reader.onerror = () => resolve(null);\n        reader.readAsDataURL(blob);\n      });\n    } catch (error) {\n      return null;\n    }\n  }\n  _generatePlaceholder(originalUrl, options = {}) {\n    if (typeof window === 'undefined') return null;\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return null;\n    const width = options.width || 300;\n    const height = options.height || 200;\n    canvas.width = width;\n    canvas.height = height;\n    const gradient = ctx.createLinearGradient(0, 0, width, height);\n    gradient.addColorStop(0, '#f8f9fa');\n    gradient.addColorStop(1, '#e9ecef');\n    ctx.fillStyle = gradient;\n    ctx.fillRect(0, 0, width, height);\n    ctx.strokeStyle = '#dee2e6';\n    ctx.lineWidth = 2;\n    ctx.strokeRect(1, 1, width - 2, height - 2);\n    ctx.fillStyle = '#6c757d';\n    ctx.font = '24px Arial';\n    ctx.textAlign = 'center';\n    ctx.fillText('🖼️', width / 2, height / 2 - 20);\n    ctx.font = '12px Arial';\n    ctx.fillStyle = '#495057';\n    ctx.fillText('Image not available', width / 2, height / 2 + 10);\n    try {\n      const domain = new URL(originalUrl).hostname;\n      ctx.font = '10px Arial';\n      ctx.fillStyle = '#6c757d';\n      ctx.fillText(domain, width / 2, height / 2 + 25);\n    } catch {}\n    return canvas.toDataURL('image/png');\n  }\n  clearCache() {\n    this.cache.clear();\n  }\n}\nconst imageProcessor = new ImageProcessor();\n\n// ============= HTML PARSING =============\nexport async function parseHtmlToElements(htmlString) {\n  if (!htmlString) return [];\n  if (typeof window === 'undefined' || typeof DOMParser === 'undefined') {\n    // SSR fallback: strip tags, return as text\n    return [{\n      type: 'text',\n      content: htmlString.replace(/<[^>]+>/g, '')\n    }];\n  }\n  const parser = new window.DOMParser();\n  const doc = parser.parseFromString(htmlString, 'text/html');\n  const elements = [];\n  const processNodeAsync = async (node, container = elements) => {\n    var _el$textContent$trim, _el$textContent, _el$textContent$trim2, _el$textContent2, _el$textContent$trim3, _el$textContent3, _el$textContent$trim4, _el$textContent4, _el$textContent$trim5, _el$textContent5;\n    if (node.nodeType === Node.TEXT_NODE) {\n      var _node$textContent$tri, _node$textContent;\n      const text = (_node$textContent$tri = (_node$textContent = node.textContent) === null || _node$textContent === void 0 ? void 0 : _node$textContent.trim()) !== null && _node$textContent$tri !== void 0 ? _node$textContent$tri : '';\n      if (text) {\n        container.push({\n          type: 'text',\n          content: text\n        });\n      }\n    } else if (node.nodeType === Node.ELEMENT_NODE) {\n      const el = node;\n      switch (el.tagName.toLowerCase()) {\n        case 'img':\n          {\n            const src = el.getAttribute('src');\n            const width = el.getAttribute('width');\n            const height = el.getAttribute('height');\n            const processedSrc = await imageProcessor.processImage(src !== null && src !== void 0 ? src : '', {\n              width: width ? parseInt(width) : undefined,\n              height: height ? parseInt(height) : undefined\n            });\n            container.push({\n              type: 'image',\n              src: processedSrc,\n              alt: el.getAttribute('alt') || '',\n              width: width !== null && width !== void 0 ? width : undefined,\n              height: height !== null && height !== void 0 ? height : undefined\n            });\n            break;\n          }\n        case 'p':\n          {\n            const paragraph = {\n              type: 'paragraph',\n              children: []\n            };\n            for (const child of Array.from(el.childNodes)) {\n              await processNodeAsync(child, paragraph.children);\n            }\n            if (paragraph.children.length > 0) {\n              container.push(paragraph);\n            }\n            break;\n          }\n        case 'h1':\n        case 'h2':\n        case 'h3':\n        case 'h4':\n        case 'h5':\n        case 'h6':\n          container.push({\n            type: 'heading',\n            level: parseInt(el.tagName.substring(1)),\n            content: (_el$textContent$trim = (_el$textContent = el.textContent) === null || _el$textContent === void 0 ? void 0 : _el$textContent.trim()) !== null && _el$textContent$trim !== void 0 ? _el$textContent$trim : ''\n          });\n          break;\n        case 'strong':\n        case 'b':\n          container.push({\n            type: 'text',\n            content: (_el$textContent$trim2 = (_el$textContent2 = el.textContent) === null || _el$textContent2 === void 0 ? void 0 : _el$textContent2.trim()) !== null && _el$textContent$trim2 !== void 0 ? _el$textContent$trim2 : '',\n            style: 'bold'\n          });\n          break;\n        case 'em':\n        case 'i':\n          container.push({\n            type: 'text',\n            content: (_el$textContent$trim3 = (_el$textContent3 = el.textContent) === null || _el$textContent3 === void 0 ? void 0 : _el$textContent3.trim()) !== null && _el$textContent$trim3 !== void 0 ? _el$textContent$trim3 : '',\n            style: 'italic'\n          });\n          break;\n        case 'ul':\n        case 'ol':\n          {\n            const list = {\n              type: 'list',\n              ordered: el.tagName.toLowerCase() === 'ol',\n              items: []\n            };\n            for (const li of Array.from(el.querySelectorAll('li'))) {\n              var _li$textContent$trim, _li$textContent;\n              list.items.push((_li$textContent$trim = (_li$textContent = li.textContent) === null || _li$textContent === void 0 ? void 0 : _li$textContent.trim()) !== null && _li$textContent$trim !== void 0 ? _li$textContent$trim : '');\n            }\n            if (list.items.length > 0) {\n              container.push(list);\n            }\n            break;\n          }\n        case 'pre':\n        case 'code':\n          container.push({\n            type: 'code',\n            content: (_el$textContent$trim4 = (_el$textContent4 = el.textContent) === null || _el$textContent4 === void 0 ? void 0 : _el$textContent4.trim()) !== null && _el$textContent$trim4 !== void 0 ? _el$textContent$trim4 : '',\n            language: el.getAttribute('data-language') || 'code'\n          });\n          break;\n        case 'blockquote':\n          container.push({\n            type: 'blockquote',\n            content: (_el$textContent$trim5 = (_el$textContent5 = el.textContent) === null || _el$textContent5 === void 0 ? void 0 : _el$textContent5.trim()) !== null && _el$textContent$trim5 !== void 0 ? _el$textContent$trim5 : ''\n          });\n          break;\n        case 'br':\n          container.push({\n            type: 'break'\n          });\n          break;\n        default:\n          for (const child of Array.from(el.childNodes)) {\n            await processNodeAsync(child, container);\n          }\n          break;\n      }\n    }\n  };\n  for (const child of Array.from(doc.body.childNodes)) {\n    await processNodeAsync(child);\n  }\n  return elements;\n}\n\n// ============= PDF RENDERER =============\n// renderPdfElements returns an array of React elements for use directly in PDF rendering. Do not wrap its output in <Text>.\n// If you need a plain text fallback, use renderPdfElementsPlainText.\nexport function renderPdfElements(elements) {\n  // Helper function to check if an element is an image\n  const isImageElement = el => {\n    return el.type === 'image' || el.type === 'paragraph' && el.children.length === 1 && el.children[0].type === 'image';\n  };\n\n  // Helper function to get optimized image dimensions\n  const getImageDimensions = element => {\n    const maxWidth = 400;\n    const maxHeight = 300; // Increased from 200 to allow for better image quality\n\n    let width = typeof element.width === 'number' ? element.width : element.width ? parseInt(element.width) : undefined;\n    let height = typeof element.height === 'number' ? element.height : element.height ? parseInt(element.height) : undefined;\n\n    // If both width and height are specified, respect aspect ratio\n    if (width && height) {\n      const aspectRatio = width / height;\n      if (width > maxWidth) {\n        width = maxWidth;\n        height = width / aspectRatio;\n      }\n      if (height > maxHeight) {\n        height = maxHeight;\n        width = height * aspectRatio;\n      }\n    }\n    return {\n      width,\n      height,\n      maxWidth,\n      maxHeight\n    };\n  };\n\n  // Helper function to get smart margins for images\n  const getImageMargins = currentIndex => {\n    const prevElement = currentIndex > 0 ? elements[currentIndex - 1] : null;\n    const nextElement = currentIndex < elements.length - 1 ? elements[currentIndex + 1] : null;\n    const isPrevImage = prevElement && isImageElement(prevElement);\n    const isNextImage = nextElement && isImageElement(nextElement);\n\n    // Check if this is the first or last element\n    const isFirst = currentIndex === 0;\n    const isLast = currentIndex === elements.length - 1;\n    return {\n      marginTop: isFirst ? 12 : isPrevImage ? 6 : 14,\n      // Smaller gap between consecutive images\n      marginBottom: isLast ? 12 : isNextImage ? 6 : 14 // Smaller gap between consecutive images\n    };\n  };\n  return elements.map((element, index) => {\n    if (element.type === 'text') {\n      const style = {\n        textAlign: 'justify',\n        lineHeight: 1.4\n      };\n      if (element.style === 'bold') style.fontWeight = 'bold';\n      if (element.style === 'italic') style.fontStyle = 'italic';\n      return /*#__PURE__*/_jsxDEV(Text, {\n        style: style,\n        children: element.content\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this);\n    } else if (element.type === 'heading') {\n      const fontSize = element.level === 1 ? 24 : element.level === 2 ? 20 : 16;\n      const fontWeight = 'bold';\n      const marginBottom = element.level === 1 ? 16 : element.level === 2 ? 12 : 10;\n      const marginTop = element.level === 1 ? 20 : element.level === 2 ? 16 : 12;\n      return /*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          fontSize,\n          fontWeight,\n          marginBottom,\n          marginTop\n        },\n        children: element.content\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this);\n    } else if (element.type === 'code') {\n      return /*#__PURE__*/_jsxDEV(View, {\n        style: {\n          backgroundColor: '#1e293b',\n          padding: 12,\n          marginVertical: 8,\n          borderRadius: 4,\n          borderLeft: '4px solid #3b82f6'\n        },\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            fontFamily: 'Courier',\n            fontSize: 10,\n            color: '#e2e8f0',\n            lineHeight: 1.4\n          },\n          children: element.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this);\n    } else if (element.type === 'blockquote') {\n      return /*#__PURE__*/_jsxDEV(View, {\n        style: {\n          borderLeft: '5px solid #e5e7eb',\n          paddingLeft: 16,\n          paddingVertical: 8,\n          marginVertical: 8,\n          backgroundColor: '#f9fafb'\n        },\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            fontStyle: 'italic',\n            color: '#6b7280',\n            fontSize: 12\n          },\n          children: element.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this);\n    } else if (element.type === 'image') {\n      if (!element.src) return null;\n      const imageMargins = getImageMargins(index);\n      const imageDimensions = getImageDimensions(element);\n      return /*#__PURE__*/_jsxDEV(Image, {\n        style: {\n          maxWidth: imageDimensions.maxWidth,\n          maxHeight: imageDimensions.maxHeight,\n          marginTop: imageMargins.marginTop,\n          marginBottom: imageMargins.marginBottom,\n          width: imageDimensions.width,\n          height: imageDimensions.height\n        },\n        src: element.src\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this);\n    } else if (element.type === 'paragraph') {\n      // Check if paragraph contains only an image\n      const hasOnlyImage = element.children.length === 1 && element.children[0].type === 'image';\n      if (hasOnlyImage) {\n        // For image-only paragraphs, apply smart spacing and render image without additional margins\n        const imageMargins = getImageMargins(index);\n        const imageChild = element.children[0];\n\n        // Type guard to ensure we have an image element\n        if (imageChild.type === 'image') {\n          const imageDimensions = getImageDimensions(imageChild);\n          return /*#__PURE__*/_jsxDEV(View, {\n            style: {\n              marginTop: imageMargins.marginTop,\n              marginBottom: imageMargins.marginBottom\n            },\n            children: imageChild.src ? /*#__PURE__*/_jsxDEV(Image, {\n              style: {\n                maxWidth: imageDimensions.maxWidth,\n                maxHeight: imageDimensions.maxHeight,\n                width: imageDimensions.width,\n                height: imageDimensions.height\n              },\n              src: imageChild.src\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 17\n            }, this) : null\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this);\n        } else {\n          // Fallback for non-image content in supposedly image-only paragraph\n          return /*#__PURE__*/_jsxDEV(View, {\n            style: {\n              marginBottom: 4\n            },\n            children: renderPdfElements(element.children)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this);\n        }\n      } else {\n        // Regular paragraph with text content\n        return /*#__PURE__*/_jsxDEV(View, {\n          style: {\n            marginBottom: 4,\n            textAlign: 'justify',\n            lineHeight: 1.4\n          },\n          children: renderPdfElements(element.children)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this);\n      }\n    } else if (element.type === 'list') {\n      return /*#__PURE__*/_jsxDEV(View, {\n        style: {\n          marginBottom: 12,\n          paddingLeft: 8\n        },\n        children: element.items.map((item, itemIndex) => {\n          const bulletText = element.ordered ? `${itemIndex + 1}.` : '•';\n          // Calculate dynamic width based on bullet/number length\n          const bulletWidth = element.ordered ? Math.max(20, bulletText.length * 8) : 12;\n          return /*#__PURE__*/_jsxDEV(View, {\n            style: {\n              flexDirection: 'row',\n              marginBottom: 4,\n              alignItems: 'flex-start'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                width: bulletWidth,\n                marginRight: 6,\n                textAlign: element.ordered ? 'right' : 'left',\n                flexShrink: 0\n              },\n              children: bulletText\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                flex: 1,\n                textAlign: 'justify',\n                lineHeight: 1.4\n              },\n              children: item\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 17\n            }, this)]\n          }, itemIndex, true, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 15\n          }, this);\n        })\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 9\n      }, this);\n    } else if (element.type === 'break') {\n      return /*#__PURE__*/_jsxDEV(Text, {\n        children: ''\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 9\n      }, this);\n    } else {\n      return null;\n    }\n  });\n}\nexport function renderPdfElementsPlainText(input) {\n  return /*#__PURE__*/_jsxDEV(Text, {\n    children: Array.isArray(input) ? input.join('\\n') : input\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 487,\n    columnNumber: 10\n  }, this);\n}\nexport { imageProcessor };", "map": {"version": 3, "names": ["React", "Image", "Text", "View", "jsxDEV", "_jsxDEV", "ImageProcessor", "constructor", "cache", "processingQueue", "Map", "processImage", "imageUrl", "options", "has", "_this$cache$get", "get", "_this$processingQueue", "processingPromise", "_processImageInternal", "set", "result", "delete", "startsWith", "canvasResult", "_tryCanvasMethod", "proxyResult", "_tryProxyMethod", "_generatePlaceholder", "Promise", "resolve", "window", "img", "timeoutId", "setTimeout", "onload", "clearTimeout", "canvas", "document", "createElement", "ctx", "getContext", "max<PERSON><PERSON><PERSON>", "maxHeight", "width", "height", "ratio", "Math", "min", "drawImage", "dataURL", "toDataURL", "error", "onerror", "crossOrigin", "src", "proxyUrl", "encodeURIComponent", "response", "fetch", "method", "ok", "blob", "size", "reader", "FileReader", "onloadend", "readAsDataURL", "originalUrl", "gradient", "createLinearGradient", "addColorStop", "fillStyle", "fillRect", "strokeStyle", "lineWidth", "strokeRect", "font", "textAlign", "fillText", "domain", "URL", "hostname", "clearCache", "clear", "imageProcessor", "parseHtmlToElements", "htmlString", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "content", "replace", "parser", "doc", "parseFromString", "elements", "processNodeAsync", "node", "container", "_el$textContent$trim", "_el$textContent", "_el$textContent$trim2", "_el$textContent2", "_el$textContent$trim3", "_el$textContent3", "_el$textContent$trim4", "_el$textContent4", "_el$textContent$trim5", "_el$textContent5", "nodeType", "Node", "TEXT_NODE", "_node$textContent$tri", "_node$textContent", "text", "textContent", "trim", "push", "ELEMENT_NODE", "el", "tagName", "toLowerCase", "getAttribute", "processedSrc", "parseInt", "undefined", "alt", "paragraph", "children", "child", "Array", "from", "childNodes", "length", "level", "substring", "style", "list", "ordered", "items", "li", "querySelectorAll", "_li$textContent$trim", "_li$textContent", "language", "body", "renderPdfElements", "isImageElement", "getImageDimensions", "element", "aspectRatio", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentIndex", "prevElement", "nextElement", "isPrevImage", "isNextImage", "<PERSON><PERSON><PERSON><PERSON>", "isLast", "marginTop", "marginBottom", "map", "index", "lineHeight", "fontWeight", "fontStyle", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "backgroundColor", "padding", "marginVertical", "borderRadius", "borderLeft", "fontFamily", "color", "paddingLeft", "paddingVertical", "<PERSON><PERSON><PERSON><PERSON>", "imageDimensions", "hasOnlyImage", "image<PERSON><PERSON>d", "item", "itemIndex", "bulletText", "bulletWidth", "max", "flexDirection", "alignItems", "marginRight", "flexShrink", "flex", "renderPdfElementsPlainText", "input", "isArray", "join"], "sources": ["C:/Users/<USER>/Desktop/jira-old/CTB-Frontend/src/pages/automated-report/components/pdfHtmlImageUtils.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Image, Text, View } from '@react-pdf/renderer';\r\n\r\n// ============= Types =============\r\nexport type PdfElement =\r\n  | { type: 'text'; content: string; style?: 'bold' | 'italic' }\r\n  | { type: 'heading'; level: number; content: string }\r\n  | { type: 'image'; src: string | null; alt?: string; width?: string | number; height?: string | number }\r\n  | { type: 'paragraph'; children: PdfElement[] }\r\n  | { type: 'list'; ordered: boolean; items: string[] }\r\n  | { type: 'code'; content: string; language?: string }\r\n  | { type: 'blockquote'; content: string }\r\n  | { type: 'break' };\r\n\r\n// ============= IMAGE PROCESSING UTILITIES =============\r\nclass ImageProcessor {\r\n  private cache: Map<string, string | null>;\r\n  private processingQueue: Map<string, Promise<string | null>>;\r\n\r\n  constructor() {\r\n    this.cache = new Map();\r\n    this.processingQueue = new Map();\r\n  }\r\n\r\n  async processImage(imageUrl: string, options: { width?: number; height?: number } = {}): Promise<string | null> {\r\n    if (!imageUrl) return null;\r\n    if (this.cache.has(imageUrl)) {\r\n      return this.cache.get(imageUrl) ?? null;\r\n    }\r\n    if (this.processingQueue.has(imageUrl)) {\r\n      return this.processingQueue.get(imageUrl) ?? null;\r\n    }\r\n    const processingPromise = this._processImageInternal(imageUrl, options);\r\n    this.processingQueue.set(imageUrl, processingPromise);\r\n    try {\r\n      const result = await processingPromise;\r\n      this.cache.set(imageUrl, result);\r\n      return result;\r\n    } finally {\r\n      this.processingQueue.delete(imageUrl);\r\n    }\r\n  }\r\n\r\n  async _processImageInternal(imageUrl: string, options: { width?: number; height?: number }): Promise<string | null> {\r\n    if (imageUrl.startsWith('data:')) {\r\n      return imageUrl;\r\n    }\r\n    const canvasResult = await this._tryCanvasMethod(imageUrl);\r\n    if (canvasResult) return canvasResult;\r\n    const proxyResult = await this._tryProxyMethod(imageUrl);\r\n    if (proxyResult) return proxyResult;\r\n    return this._generatePlaceholder(imageUrl, options);\r\n  }\r\n\r\n  async _tryCanvasMethod(imageUrl: string): Promise<string | null> {\r\n    return new Promise((resolve) => {\r\n      if (typeof window === 'undefined') return resolve(null);\r\n      const img = new window.Image();\r\n      const timeoutId = setTimeout(() => resolve(null), 5000);\r\n      img.onload = () => {\r\n        clearTimeout(timeoutId);\r\n        try {\r\n          const canvas = document.createElement('canvas');\r\n          const ctx = canvas.getContext('2d');\r\n          if (!ctx) return resolve(null);\r\n          const maxWidth = 800;\r\n          const maxHeight = 600;\r\n          let width = img.width;\r\n          let height = img.height;\r\n          if (width > maxWidth || height > maxHeight) {\r\n            const ratio = Math.min(maxWidth / width, maxHeight / height);\r\n            width *= ratio;\r\n            height *= ratio;\r\n          }\r\n          canvas.width = width;\r\n          canvas.height = height;\r\n          ctx.drawImage(img, 0, 0, width, height);\r\n          const dataURL = canvas.toDataURL('image/jpeg', 0.8);\r\n          resolve(dataURL);\r\n        } catch (error) {\r\n          resolve(null);\r\n        }\r\n      };\r\n      img.onerror = () => {\r\n        clearTimeout(timeoutId);\r\n        resolve(null);\r\n      };\r\n      img.crossOrigin = 'anonymous';\r\n      img.src = imageUrl;\r\n    });\r\n  }\r\n\r\n  async _tryProxyMethod(imageUrl: string): Promise<string | null> {\r\n    try {\r\n      const proxyUrl = `https://api.allorigins.win/raw?url=${encodeURIComponent(imageUrl)}`;\r\n      const response = await fetch(proxyUrl, { method: 'GET' });\r\n      if (!response.ok) return null;\r\n      const blob = await response.blob();\r\n      if (blob.size === 0) return null;\r\n      return new Promise((resolve) => {\r\n        const reader = new FileReader();\r\n        reader.onloadend = () => resolve(reader.result as string);\r\n        reader.onerror = () => resolve(null);\r\n        reader.readAsDataURL(blob);\r\n      });\r\n    } catch (error) {\r\n      return null;\r\n    }\r\n  }\r\n\r\n  _generatePlaceholder(originalUrl: string, options: { width?: number; height?: number } = {}): string | null {\r\n    if (typeof window === 'undefined') return null;\r\n    const canvas = document.createElement('canvas');\r\n    const ctx = canvas.getContext('2d');\r\n    if (!ctx) return null;\r\n    const width = options.width || 300;\r\n    const height = options.height || 200;\r\n    canvas.width = width;\r\n    canvas.height = height;\r\n    const gradient = ctx.createLinearGradient(0, 0, width, height);\r\n    gradient.addColorStop(0, '#f8f9fa');\r\n    gradient.addColorStop(1, '#e9ecef');\r\n    ctx.fillStyle = gradient;\r\n    ctx.fillRect(0, 0, width, height);\r\n    ctx.strokeStyle = '#dee2e6';\r\n    ctx.lineWidth = 2;\r\n    ctx.strokeRect(1, 1, width - 2, height - 2);\r\n    ctx.fillStyle = '#6c757d';\r\n    ctx.font = '24px Arial';\r\n    ctx.textAlign = 'center';\r\n    ctx.fillText('🖼️', width / 2, height / 2 - 20);\r\n    ctx.font = '12px Arial';\r\n    ctx.fillStyle = '#495057';\r\n    ctx.fillText('Image not available', width / 2, height / 2 + 10);\r\n    try {\r\n      const domain = new URL(originalUrl).hostname;\r\n      ctx.font = '10px Arial';\r\n      ctx.fillStyle = '#6c757d';\r\n      ctx.fillText(domain, width / 2, height / 2 + 25);\r\n    } catch {}\r\n    return canvas.toDataURL('image/png');\r\n  }\r\n\r\n  clearCache(): void {\r\n    this.cache.clear();\r\n  }\r\n}\r\n\r\nconst imageProcessor = new ImageProcessor();\r\n\r\n// ============= HTML PARSING =============\r\nexport async function parseHtmlToElements(htmlString: string): Promise<PdfElement[]> {\r\n  if (!htmlString) return [];\r\n  if (typeof window === 'undefined' || typeof DOMParser === 'undefined') {\r\n    // SSR fallback: strip tags, return as text\r\n    return [{ type: 'text', content: htmlString.replace(/<[^>]+>/g, '') }];\r\n  }\r\n  const parser = new window.DOMParser();\r\n  const doc = parser.parseFromString(htmlString, 'text/html');\r\n  const elements: PdfElement[] = [];\r\n\r\n  const processNodeAsync = async (node: Node, container: PdfElement[] = elements): Promise<void> => {\r\n    if (node.nodeType === Node.TEXT_NODE) {\r\n      const text = node.textContent?.trim() ?? '';\r\n      if (text) {\r\n        container.push({ type: 'text', content: text });\r\n      }\r\n    } else if (node.nodeType === Node.ELEMENT_NODE) {\r\n      const el = node as HTMLElement;\r\n      switch (el.tagName.toLowerCase()) {\r\n        case 'img': {\r\n          const src = el.getAttribute('src');\r\n          const width = el.getAttribute('width');\r\n          const height = el.getAttribute('height');\r\n          const processedSrc = await imageProcessor.processImage(src ?? '', {\r\n            width: width ? parseInt(width) : undefined,\r\n            height: height ? parseInt(height) : undefined\r\n          });\r\n          container.push({\r\n            type: 'image',\r\n            src: processedSrc,\r\n            alt: el.getAttribute('alt') || '',\r\n            width: width ?? undefined,\r\n            height: height ?? undefined,\r\n          });\r\n          break;\r\n        }\r\n        case 'p': {\r\n          const paragraph: PdfElement = { type: 'paragraph', children: [] };\r\n          for (const child of Array.from(el.childNodes)) {\r\n            await processNodeAsync(child, paragraph.children);\r\n          }\r\n          if (paragraph.children.length > 0) {\r\n            container.push(paragraph);\r\n          }\r\n          break;\r\n        }\r\n        case 'h1':\r\n        case 'h2':\r\n        case 'h3':\r\n        case 'h4':\r\n        case 'h5':\r\n        case 'h6':\r\n          container.push({\r\n            type: 'heading',\r\n            level: parseInt(el.tagName.substring(1)),\r\n            content: el.textContent?.trim() ?? ''\r\n          });\r\n          break;\r\n        case 'strong':\r\n        case 'b':\r\n          container.push({\r\n            type: 'text',\r\n            content: el.textContent?.trim() ?? '',\r\n            style: 'bold'\r\n          });\r\n          break;\r\n        case 'em':\r\n        case 'i':\r\n          container.push({\r\n            type: 'text',\r\n            content: el.textContent?.trim() ?? '',\r\n            style: 'italic'\r\n          });\r\n          break;\r\n        case 'ul':\r\n        case 'ol': {\r\n          const list: PdfElement = {\r\n            type: 'list',\r\n            ordered: el.tagName.toLowerCase() === 'ol',\r\n            items: []\r\n          };\r\n          for (const li of Array.from(el.querySelectorAll('li'))) {\r\n            list.items.push(li.textContent?.trim() ?? '');\r\n          }\r\n          if (list.items.length > 0) {\r\n            container.push(list);\r\n          }\r\n          break;\r\n        }\r\n        case 'pre':\r\n        case 'code':\r\n          container.push({\r\n            type: 'code',\r\n            content: el.textContent?.trim() ?? '',\r\n            language: el.getAttribute('data-language') || 'code'\r\n          } as any);\r\n          break;\r\n        case 'blockquote':\r\n          container.push({\r\n            type: 'blockquote',\r\n            content: el.textContent?.trim() ?? ''\r\n          } as any);\r\n          break;\r\n        case 'br':\r\n          container.push({ type: 'break' });\r\n          break;\r\n        default:\r\n          for (const child of Array.from(el.childNodes)) {\r\n            await processNodeAsync(child, container);\r\n          }\r\n          break;\r\n      }\r\n    }\r\n  };\r\n\r\n  for (const child of Array.from(doc.body.childNodes)) {\r\n    await processNodeAsync(child);\r\n  }\r\n  return elements;\r\n}\r\n\r\n// ============= PDF RENDERER =============\r\n// renderPdfElements returns an array of React elements for use directly in PDF rendering. Do not wrap its output in <Text>.\r\n// If you need a plain text fallback, use renderPdfElementsPlainText.\r\nexport function renderPdfElements(elements: PdfElement[]): (JSX.Element | null)[] {\r\n  // Helper function to check if an element is an image\r\n  const isImageElement = (el: PdfElement): boolean => {\r\n    return el.type === 'image' || (el.type === 'paragraph' && el.children.length === 1 && el.children[0].type === 'image');\r\n  };\r\n\r\n  // Helper function to get optimized image dimensions\r\n  const getImageDimensions = (element: PdfElement & { type: 'image' }): { width?: number; height?: number; maxWidth: number; maxHeight: number } => {\r\n    const maxWidth = 400;\r\n    const maxHeight = 300; // Increased from 200 to allow for better image quality\r\n\r\n    let width = typeof element.width === 'number' ? element.width : (element.width ? parseInt(element.width as string) : undefined);\r\n    let height = typeof element.height === 'number' ? element.height : (element.height ? parseInt(element.height as string) : undefined);\r\n\r\n    // If both width and height are specified, respect aspect ratio\r\n    if (width && height) {\r\n      const aspectRatio = width / height;\r\n      if (width > maxWidth) {\r\n        width = maxWidth;\r\n        height = width / aspectRatio;\r\n      }\r\n      if (height > maxHeight) {\r\n        height = maxHeight;\r\n        width = height * aspectRatio;\r\n      }\r\n    }\r\n\r\n    return { width, height, maxWidth, maxHeight };\r\n  };\r\n\r\n  // Helper function to get smart margins for images\r\n  const getImageMargins = (currentIndex: number): { marginTop: number; marginBottom: number } => {\r\n    const prevElement = currentIndex > 0 ? elements[currentIndex - 1] : null;\r\n    const nextElement = currentIndex < elements.length - 1 ? elements[currentIndex + 1] : null;\r\n\r\n    const isPrevImage = prevElement && isImageElement(prevElement);\r\n    const isNextImage = nextElement && isImageElement(nextElement);\r\n\r\n    // Check if this is the first or last element\r\n    const isFirst = currentIndex === 0;\r\n    const isLast = currentIndex === elements.length - 1;\r\n\r\n    return {\r\n      marginTop: isFirst ? 12 : (isPrevImage ? 6 : 14),      // Smaller gap between consecutive images\r\n      marginBottom: isLast ? 12 : (isNextImage ? 6 : 14)     // Smaller gap between consecutive images\r\n    };\r\n  };\r\n\r\n  return elements.map((element: PdfElement, index: number): JSX.Element | null => {\r\n    if (element.type === 'text') {\r\n      const style: any = {\r\n        textAlign: 'justify',\r\n        lineHeight: 1.4\r\n      };\r\n      if (element.style === 'bold') style.fontWeight = 'bold';\r\n      if (element.style === 'italic') style.fontStyle = 'italic';\r\n      return (\r\n        <Text key={index} style={style}>{element.content}</Text>\r\n      );\r\n    } else if (element.type === 'heading') {\r\n      const fontSize = element.level === 1 ? 24 : element.level === 2 ? 20 : 16;\r\n      const fontWeight = 'bold' as const;\r\n      const marginBottom = element.level === 1 ? 16 : element.level === 2 ? 12 : 10;\r\n      const marginTop = element.level === 1 ? 20 : element.level === 2 ? 16 : 12;\r\n      return (\r\n        <Text key={index} style={{ fontSize, fontWeight, marginBottom, marginTop }}>{element.content}</Text>\r\n      );\r\n    } else if (element.type === 'code') {\r\n      return (\r\n        <View key={index} style={{\r\n          backgroundColor: '#1e293b',\r\n          padding: 12,\r\n          marginVertical: 8,\r\n          borderRadius: 4,\r\n          borderLeft: '4px solid #3b82f6'\r\n        }}>\r\n          <Text style={{\r\n            fontFamily: 'Courier',\r\n            fontSize: 10,\r\n            color: '#e2e8f0',\r\n            lineHeight: 1.4\r\n          }}>\r\n            {element.content}\r\n          </Text>\r\n        </View>\r\n      );\r\n    } else if (element.type === 'blockquote') {\r\n      return (\r\n        <View key={index} style={{\r\n          borderLeft: '5px solid #e5e7eb',\r\n          paddingLeft: 16,\r\n          paddingVertical: 8,\r\n          marginVertical: 8,\r\n          backgroundColor: '#f9fafb'\r\n        }}>\r\n          <Text style={{\r\n            fontStyle: 'italic',\r\n            color: '#6b7280',\r\n            fontSize: 12\r\n          }}>\r\n            {element.content}\r\n          </Text>\r\n        </View>\r\n      );\r\n    } else if (element.type === 'image') {\r\n      if (!element.src) return null;\r\n      const imageMargins = getImageMargins(index);\r\n      const imageDimensions = getImageDimensions(element);\r\n      return (\r\n        <Image\r\n          key={index}\r\n          style={{\r\n            maxWidth: imageDimensions.maxWidth,\r\n            maxHeight: imageDimensions.maxHeight,\r\n            marginTop: imageMargins.marginTop,\r\n            marginBottom: imageMargins.marginBottom,\r\n            width: imageDimensions.width,\r\n            height: imageDimensions.height\r\n          }}\r\n          src={element.src}\r\n        />\r\n      );\r\n    } else if (element.type === 'paragraph') {\r\n      // Check if paragraph contains only an image\r\n      const hasOnlyImage = element.children.length === 1 && element.children[0].type === 'image';\r\n\r\n      if (hasOnlyImage) {\r\n        // For image-only paragraphs, apply smart spacing and render image without additional margins\r\n        const imageMargins = getImageMargins(index);\r\n        const imageChild = element.children[0];\r\n\r\n        // Type guard to ensure we have an image element\r\n        if (imageChild.type === 'image') {\r\n          const imageDimensions = getImageDimensions(imageChild);\r\n          return (\r\n            <View key={index} style={{\r\n              marginTop: imageMargins.marginTop,\r\n              marginBottom: imageMargins.marginBottom\r\n            }}>\r\n              {imageChild.src ? (\r\n                <Image\r\n                  style={{\r\n                    maxWidth: imageDimensions.maxWidth,\r\n                    maxHeight: imageDimensions.maxHeight,\r\n                    width: imageDimensions.width,\r\n                    height: imageDimensions.height\r\n                  }}\r\n                  src={imageChild.src}\r\n                />\r\n              ) : null}\r\n            </View>\r\n          );\r\n        } else {\r\n          // Fallback for non-image content in supposedly image-only paragraph\r\n          return (\r\n            <View key={index} style={{ marginBottom: 4 }}>\r\n              {renderPdfElements(element.children)}\r\n            </View>\r\n          );\r\n        }\r\n      } else {\r\n        // Regular paragraph with text content\r\n        return (\r\n          <View key={index} style={{\r\n            marginBottom: 4,\r\n            textAlign: 'justify',\r\n            lineHeight: 1.4\r\n          }}>\r\n            {renderPdfElements(element.children)}\r\n          </View>\r\n        );\r\n      }\r\n    } else if (element.type === 'list') {\r\n      return (\r\n        <View key={index} style={{ marginBottom: 12, paddingLeft: 8 }}>\r\n          {element.items.map((item: string, itemIndex: number) => {\r\n            const bulletText = element.ordered ? `${itemIndex + 1}.` : '•';\r\n            // Calculate dynamic width based on bullet/number length\r\n            const bulletWidth = element.ordered ? Math.max(20, bulletText.length * 8) : 12;\r\n\r\n            return (\r\n              <View key={itemIndex} style={{ flexDirection: 'row', marginBottom: 4, alignItems: 'flex-start' }}>\r\n                <Text style={{\r\n                  width: bulletWidth,\r\n                  marginRight: 6,\r\n                  textAlign: element.ordered ? 'right' : 'left',\r\n                  flexShrink: 0\r\n                }}>\r\n                  {bulletText}\r\n                </Text>\r\n                <Text style={{\r\n                  flex: 1,\r\n                  textAlign: 'justify',\r\n                  lineHeight: 1.4\r\n                }}>{item}</Text>\r\n              </View>\r\n            );\r\n          })}\r\n        </View>\r\n      );\r\n    } else if (element.type === 'break') {\r\n      return (\r\n        <Text key={index}>{''}</Text>\r\n      );\r\n    } else {\r\n      return null;\r\n    }\r\n  });\r\n}\r\n\r\nexport function renderPdfElementsPlainText(input: string | string[]): JSX.Element {\r\n  return <Text>{Array.isArray(input) ? input.join('\\n') : input}</Text>;\r\n}\r\n\r\nexport { imageProcessor };"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,EAAEC,IAAI,EAAEC,IAAI,QAAQ,qBAAqB;;AAEvD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAWA;AACA,MAAMC,cAAc,CAAC;EAInBC,WAAWA,CAAA,EAAG;IAAA,KAHNC,KAAK;IAAA,KACLC,eAAe;IAGrB,IAAI,CAACD,KAAK,GAAG,IAAIE,GAAG,CAAC,CAAC;IACtB,IAAI,CAACD,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;EAClC;EAEA,MAAMC,YAAYA,CAACC,QAAgB,EAAEC,OAA4C,GAAG,CAAC,CAAC,EAA0B;IAC9G,IAAI,CAACD,QAAQ,EAAE,OAAO,IAAI;IAC1B,IAAI,IAAI,CAACJ,KAAK,CAACM,GAAG,CAACF,QAAQ,CAAC,EAAE;MAAA,IAAAG,eAAA;MAC5B,QAAAA,eAAA,GAAO,IAAI,CAACP,KAAK,CAACQ,GAAG,CAACJ,QAAQ,CAAC,cAAAG,eAAA,cAAAA,eAAA,GAAI,IAAI;IACzC;IACA,IAAI,IAAI,CAACN,eAAe,CAACK,GAAG,CAACF,QAAQ,CAAC,EAAE;MAAA,IAAAK,qBAAA;MACtC,QAAAA,qBAAA,GAAO,IAAI,CAACR,eAAe,CAACO,GAAG,CAACJ,QAAQ,CAAC,cAAAK,qBAAA,cAAAA,qBAAA,GAAI,IAAI;IACnD;IACA,MAAMC,iBAAiB,GAAG,IAAI,CAACC,qBAAqB,CAACP,QAAQ,EAAEC,OAAO,CAAC;IACvE,IAAI,CAACJ,eAAe,CAACW,GAAG,CAACR,QAAQ,EAAEM,iBAAiB,CAAC;IACrD,IAAI;MACF,MAAMG,MAAM,GAAG,MAAMH,iBAAiB;MACtC,IAAI,CAACV,KAAK,CAACY,GAAG,CAACR,QAAQ,EAAES,MAAM,CAAC;MAChC,OAAOA,MAAM;IACf,CAAC,SAAS;MACR,IAAI,CAACZ,eAAe,CAACa,MAAM,CAACV,QAAQ,CAAC;IACvC;EACF;EAEA,MAAMO,qBAAqBA,CAACP,QAAgB,EAAEC,OAA4C,EAA0B;IAClH,IAAID,QAAQ,CAACW,UAAU,CAAC,OAAO,CAAC,EAAE;MAChC,OAAOX,QAAQ;IACjB;IACA,MAAMY,YAAY,GAAG,MAAM,IAAI,CAACC,gBAAgB,CAACb,QAAQ,CAAC;IAC1D,IAAIY,YAAY,EAAE,OAAOA,YAAY;IACrC,MAAME,WAAW,GAAG,MAAM,IAAI,CAACC,eAAe,CAACf,QAAQ,CAAC;IACxD,IAAIc,WAAW,EAAE,OAAOA,WAAW;IACnC,OAAO,IAAI,CAACE,oBAAoB,CAAChB,QAAQ,EAAEC,OAAO,CAAC;EACrD;EAEA,MAAMY,gBAAgBA,CAACb,QAAgB,EAA0B;IAC/D,OAAO,IAAIiB,OAAO,CAAEC,OAAO,IAAK;MAC9B,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE,OAAOD,OAAO,CAAC,IAAI,CAAC;MACvD,MAAME,GAAG,GAAG,IAAID,MAAM,CAAC9B,KAAK,CAAC,CAAC;MAC9B,MAAMgC,SAAS,GAAGC,UAAU,CAAC,MAAMJ,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;MACvDE,GAAG,CAACG,MAAM,GAAG,MAAM;QACjBC,YAAY,CAACH,SAAS,CAAC;QACvB,IAAI;UACF,MAAMI,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;UACnC,IAAI,CAACD,GAAG,EAAE,OAAOV,OAAO,CAAC,IAAI,CAAC;UAC9B,MAAMY,QAAQ,GAAG,GAAG;UACpB,MAAMC,SAAS,GAAG,GAAG;UACrB,IAAIC,KAAK,GAAGZ,GAAG,CAACY,KAAK;UACrB,IAAIC,MAAM,GAAGb,GAAG,CAACa,MAAM;UACvB,IAAID,KAAK,GAAGF,QAAQ,IAAIG,MAAM,GAAGF,SAAS,EAAE;YAC1C,MAAMG,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACN,QAAQ,GAAGE,KAAK,EAAED,SAAS,GAAGE,MAAM,CAAC;YAC5DD,KAAK,IAAIE,KAAK;YACdD,MAAM,IAAIC,KAAK;UACjB;UACAT,MAAM,CAACO,KAAK,GAAGA,KAAK;UACpBP,MAAM,CAACQ,MAAM,GAAGA,MAAM;UACtBL,GAAG,CAACS,SAAS,CAACjB,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEY,KAAK,EAAEC,MAAM,CAAC;UACvC,MAAMK,OAAO,GAAGb,MAAM,CAACc,SAAS,CAAC,YAAY,EAAE,GAAG,CAAC;UACnDrB,OAAO,CAACoB,OAAO,CAAC;QAClB,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdtB,OAAO,CAAC,IAAI,CAAC;QACf;MACF,CAAC;MACDE,GAAG,CAACqB,OAAO,GAAG,MAAM;QAClBjB,YAAY,CAACH,SAAS,CAAC;QACvBH,OAAO,CAAC,IAAI,CAAC;MACf,CAAC;MACDE,GAAG,CAACsB,WAAW,GAAG,WAAW;MAC7BtB,GAAG,CAACuB,GAAG,GAAG3C,QAAQ;IACpB,CAAC,CAAC;EACJ;EAEA,MAAMe,eAAeA,CAACf,QAAgB,EAA0B;IAC9D,IAAI;MACF,MAAM4C,QAAQ,GAAI,sCAAqCC,kBAAkB,CAAC7C,QAAQ,CAAE,EAAC;MACrF,MAAM8C,QAAQ,GAAG,MAAMC,KAAK,CAACH,QAAQ,EAAE;QAAEI,MAAM,EAAE;MAAM,CAAC,CAAC;MACzD,IAAI,CAACF,QAAQ,CAACG,EAAE,EAAE,OAAO,IAAI;MAC7B,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACI,IAAI,CAAC,CAAC;MAClC,IAAIA,IAAI,CAACC,IAAI,KAAK,CAAC,EAAE,OAAO,IAAI;MAChC,OAAO,IAAIlC,OAAO,CAAEC,OAAO,IAAK;QAC9B,MAAMkC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,SAAS,GAAG,MAAMpC,OAAO,CAACkC,MAAM,CAAC3C,MAAgB,CAAC;QACzD2C,MAAM,CAACX,OAAO,GAAG,MAAMvB,OAAO,CAAC,IAAI,CAAC;QACpCkC,MAAM,CAACG,aAAa,CAACL,IAAI,CAAC;MAC5B,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOV,KAAK,EAAE;MACd,OAAO,IAAI;IACb;EACF;EAEAxB,oBAAoBA,CAACwC,WAAmB,EAAEvD,OAA4C,GAAG,CAAC,CAAC,EAAiB;IAC1G,IAAI,OAAOkB,MAAM,KAAK,WAAW,EAAE,OAAO,IAAI;IAC9C,MAAMM,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;IACnC,IAAI,CAACD,GAAG,EAAE,OAAO,IAAI;IACrB,MAAMI,KAAK,GAAG/B,OAAO,CAAC+B,KAAK,IAAI,GAAG;IAClC,MAAMC,MAAM,GAAGhC,OAAO,CAACgC,MAAM,IAAI,GAAG;IACpCR,MAAM,CAACO,KAAK,GAAGA,KAAK;IACpBP,MAAM,CAACQ,MAAM,GAAGA,MAAM;IACtB,MAAMwB,QAAQ,GAAG7B,GAAG,CAAC8B,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE1B,KAAK,EAAEC,MAAM,CAAC;IAC9DwB,QAAQ,CAACE,YAAY,CAAC,CAAC,EAAE,SAAS,CAAC;IACnCF,QAAQ,CAACE,YAAY,CAAC,CAAC,EAAE,SAAS,CAAC;IACnC/B,GAAG,CAACgC,SAAS,GAAGH,QAAQ;IACxB7B,GAAG,CAACiC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE7B,KAAK,EAAEC,MAAM,CAAC;IACjCL,GAAG,CAACkC,WAAW,GAAG,SAAS;IAC3BlC,GAAG,CAACmC,SAAS,GAAG,CAAC;IACjBnC,GAAG,CAACoC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAEhC,KAAK,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,CAAC;IAC3CL,GAAG,CAACgC,SAAS,GAAG,SAAS;IACzBhC,GAAG,CAACqC,IAAI,GAAG,YAAY;IACvBrC,GAAG,CAACsC,SAAS,GAAG,QAAQ;IACxBtC,GAAG,CAACuC,QAAQ,CAAC,KAAK,EAAEnC,KAAK,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,GAAG,EAAE,CAAC;IAC/CL,GAAG,CAACqC,IAAI,GAAG,YAAY;IACvBrC,GAAG,CAACgC,SAAS,GAAG,SAAS;IACzBhC,GAAG,CAACuC,QAAQ,CAAC,qBAAqB,EAAEnC,KAAK,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,GAAG,EAAE,CAAC;IAC/D,IAAI;MACF,MAAMmC,MAAM,GAAG,IAAIC,GAAG,CAACb,WAAW,CAAC,CAACc,QAAQ;MAC5C1C,GAAG,CAACqC,IAAI,GAAG,YAAY;MACvBrC,GAAG,CAACgC,SAAS,GAAG,SAAS;MACzBhC,GAAG,CAACuC,QAAQ,CAACC,MAAM,EAAEpC,KAAK,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,GAAG,EAAE,CAAC;IAClD,CAAC,CAAC,MAAM,CAAC;IACT,OAAOR,MAAM,CAACc,SAAS,CAAC,WAAW,CAAC;EACtC;EAEAgC,UAAUA,CAAA,EAAS;IACjB,IAAI,CAAC3E,KAAK,CAAC4E,KAAK,CAAC,CAAC;EACpB;AACF;AAEA,MAAMC,cAAc,GAAG,IAAI/E,cAAc,CAAC,CAAC;;AAE3C;AACA,OAAO,eAAegF,mBAAmBA,CAACC,UAAkB,EAAyB;EACnF,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;EAC1B,IAAI,OAAOxD,MAAM,KAAK,WAAW,IAAI,OAAOyD,SAAS,KAAK,WAAW,EAAE;IACrE;IACA,OAAO,CAAC;MAAEC,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAEH,UAAU,CAACI,OAAO,CAAC,UAAU,EAAE,EAAE;IAAE,CAAC,CAAC;EACxE;EACA,MAAMC,MAAM,GAAG,IAAI7D,MAAM,CAACyD,SAAS,CAAC,CAAC;EACrC,MAAMK,GAAG,GAAGD,MAAM,CAACE,eAAe,CAACP,UAAU,EAAE,WAAW,CAAC;EAC3D,MAAMQ,QAAsB,GAAG,EAAE;EAEjC,MAAMC,gBAAgB,GAAG,MAAAA,CAAOC,IAAU,EAAEC,SAAuB,GAAGH,QAAQ,KAAoB;IAAA,IAAAI,oBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA;IAChG,IAAIX,IAAI,CAACY,QAAQ,KAAKC,IAAI,CAACC,SAAS,EAAE;MAAA,IAAAC,qBAAA,EAAAC,iBAAA;MACpC,MAAMC,IAAI,IAAAF,qBAAA,IAAAC,iBAAA,GAAGhB,IAAI,CAACkB,WAAW,cAAAF,iBAAA,uBAAhBA,iBAAA,CAAkBG,IAAI,CAAC,CAAC,cAAAJ,qBAAA,cAAAA,qBAAA,GAAI,EAAE;MAC3C,IAAIE,IAAI,EAAE;QACRhB,SAAS,CAACmB,IAAI,CAAC;UAAE5B,IAAI,EAAE,MAAM;UAAEC,OAAO,EAAEwB;QAAK,CAAC,CAAC;MACjD;IACF,CAAC,MAAM,IAAIjB,IAAI,CAACY,QAAQ,KAAKC,IAAI,CAACQ,YAAY,EAAE;MAC9C,MAAMC,EAAE,GAAGtB,IAAmB;MAC9B,QAAQsB,EAAE,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC;QAC9B,KAAK,KAAK;UAAE;YACV,MAAMlE,GAAG,GAAGgE,EAAE,CAACG,YAAY,CAAC,KAAK,CAAC;YAClC,MAAM9E,KAAK,GAAG2E,EAAE,CAACG,YAAY,CAAC,OAAO,CAAC;YACtC,MAAM7E,MAAM,GAAG0E,EAAE,CAACG,YAAY,CAAC,QAAQ,CAAC;YACxC,MAAMC,YAAY,GAAG,MAAMtC,cAAc,CAAC1E,YAAY,CAAC4C,GAAG,aAAHA,GAAG,cAAHA,GAAG,GAAI,EAAE,EAAE;cAChEX,KAAK,EAAEA,KAAK,GAAGgF,QAAQ,CAAChF,KAAK,CAAC,GAAGiF,SAAS;cAC1ChF,MAAM,EAAEA,MAAM,GAAG+E,QAAQ,CAAC/E,MAAM,CAAC,GAAGgF;YACtC,CAAC,CAAC;YACF3B,SAAS,CAACmB,IAAI,CAAC;cACb5B,IAAI,EAAE,OAAO;cACblC,GAAG,EAAEoE,YAAY;cACjBG,GAAG,EAAEP,EAAE,CAACG,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE;cACjC9E,KAAK,EAAEA,KAAK,aAALA,KAAK,cAALA,KAAK,GAAIiF,SAAS;cACzBhF,MAAM,EAAEA,MAAM,aAANA,MAAM,cAANA,MAAM,GAAIgF;YACpB,CAAC,CAAC;YACF;UACF;QACA,KAAK,GAAG;UAAE;YACR,MAAME,SAAqB,GAAG;cAAEtC,IAAI,EAAE,WAAW;cAAEuC,QAAQ,EAAE;YAAG,CAAC;YACjE,KAAK,MAAMC,KAAK,IAAIC,KAAK,CAACC,IAAI,CAACZ,EAAE,CAACa,UAAU,CAAC,EAAE;cAC7C,MAAMpC,gBAAgB,CAACiC,KAAK,EAAEF,SAAS,CAACC,QAAQ,CAAC;YACnD;YACA,IAAID,SAAS,CAACC,QAAQ,CAACK,MAAM,GAAG,CAAC,EAAE;cACjCnC,SAAS,CAACmB,IAAI,CAACU,SAAS,CAAC;YAC3B;YACA;UACF;QACA,KAAK,IAAI;QACT,KAAK,IAAI;QACT,KAAK,IAAI;QACT,KAAK,IAAI;QACT,KAAK,IAAI;QACT,KAAK,IAAI;UACP7B,SAAS,CAACmB,IAAI,CAAC;YACb5B,IAAI,EAAE,SAAS;YACf6C,KAAK,EAAEV,QAAQ,CAACL,EAAE,CAACC,OAAO,CAACe,SAAS,CAAC,CAAC,CAAC,CAAC;YACxC7C,OAAO,GAAAS,oBAAA,IAAAC,eAAA,GAAEmB,EAAE,CAACJ,WAAW,cAAAf,eAAA,uBAAdA,eAAA,CAAgBgB,IAAI,CAAC,CAAC,cAAAjB,oBAAA,cAAAA,oBAAA,GAAI;UACrC,CAAC,CAAC;UACF;QACF,KAAK,QAAQ;QACb,KAAK,GAAG;UACND,SAAS,CAACmB,IAAI,CAAC;YACb5B,IAAI,EAAE,MAAM;YACZC,OAAO,GAAAW,qBAAA,IAAAC,gBAAA,GAAEiB,EAAE,CAACJ,WAAW,cAAAb,gBAAA,uBAAdA,gBAAA,CAAgBc,IAAI,CAAC,CAAC,cAAAf,qBAAA,cAAAA,qBAAA,GAAI,EAAE;YACrCmC,KAAK,EAAE;UACT,CAAC,CAAC;UACF;QACF,KAAK,IAAI;QACT,KAAK,GAAG;UACNtC,SAAS,CAACmB,IAAI,CAAC;YACb5B,IAAI,EAAE,MAAM;YACZC,OAAO,GAAAa,qBAAA,IAAAC,gBAAA,GAAEe,EAAE,CAACJ,WAAW,cAAAX,gBAAA,uBAAdA,gBAAA,CAAgBY,IAAI,CAAC,CAAC,cAAAb,qBAAA,cAAAA,qBAAA,GAAI,EAAE;YACrCiC,KAAK,EAAE;UACT,CAAC,CAAC;UACF;QACF,KAAK,IAAI;QACT,KAAK,IAAI;UAAE;YACT,MAAMC,IAAgB,GAAG;cACvBhD,IAAI,EAAE,MAAM;cACZiD,OAAO,EAAEnB,EAAE,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,IAAI;cAC1CkB,KAAK,EAAE;YACT,CAAC;YACD,KAAK,MAAMC,EAAE,IAAIV,KAAK,CAACC,IAAI,CAACZ,EAAE,CAACsB,gBAAgB,CAAC,IAAI,CAAC,CAAC,EAAE;cAAA,IAAAC,oBAAA,EAAAC,eAAA;cACtDN,IAAI,CAACE,KAAK,CAACtB,IAAI,EAAAyB,oBAAA,IAAAC,eAAA,GAACH,EAAE,CAACzB,WAAW,cAAA4B,eAAA,uBAAdA,eAAA,CAAgB3B,IAAI,CAAC,CAAC,cAAA0B,oBAAA,cAAAA,oBAAA,GAAI,EAAE,CAAC;YAC/C;YACA,IAAIL,IAAI,CAACE,KAAK,CAACN,MAAM,GAAG,CAAC,EAAE;cACzBnC,SAAS,CAACmB,IAAI,CAACoB,IAAI,CAAC;YACtB;YACA;UACF;QACA,KAAK,KAAK;QACV,KAAK,MAAM;UACTvC,SAAS,CAACmB,IAAI,CAAC;YACb5B,IAAI,EAAE,MAAM;YACZC,OAAO,GAAAe,qBAAA,IAAAC,gBAAA,GAAEa,EAAE,CAACJ,WAAW,cAAAT,gBAAA,uBAAdA,gBAAA,CAAgBU,IAAI,CAAC,CAAC,cAAAX,qBAAA,cAAAA,qBAAA,GAAI,EAAE;YACrCuC,QAAQ,EAAEzB,EAAE,CAACG,YAAY,CAAC,eAAe,CAAC,IAAI;UAChD,CAAQ,CAAC;UACT;QACF,KAAK,YAAY;UACfxB,SAAS,CAACmB,IAAI,CAAC;YACb5B,IAAI,EAAE,YAAY;YAClBC,OAAO,GAAAiB,qBAAA,IAAAC,gBAAA,GAAEW,EAAE,CAACJ,WAAW,cAAAP,gBAAA,uBAAdA,gBAAA,CAAgBQ,IAAI,CAAC,CAAC,cAAAT,qBAAA,cAAAA,qBAAA,GAAI;UACrC,CAAQ,CAAC;UACT;QACF,KAAK,IAAI;UACPT,SAAS,CAACmB,IAAI,CAAC;YAAE5B,IAAI,EAAE;UAAQ,CAAC,CAAC;UACjC;QACF;UACE,KAAK,MAAMwC,KAAK,IAAIC,KAAK,CAACC,IAAI,CAACZ,EAAE,CAACa,UAAU,CAAC,EAAE;YAC7C,MAAMpC,gBAAgB,CAACiC,KAAK,EAAE/B,SAAS,CAAC;UAC1C;UACA;MACJ;IACF;EACF,CAAC;EAED,KAAK,MAAM+B,KAAK,IAAIC,KAAK,CAACC,IAAI,CAACtC,GAAG,CAACoD,IAAI,CAACb,UAAU,CAAC,EAAE;IACnD,MAAMpC,gBAAgB,CAACiC,KAAK,CAAC;EAC/B;EACA,OAAOlC,QAAQ;AACjB;;AAEA;AACA;AACA;AACA,OAAO,SAASmD,iBAAiBA,CAACnD,QAAsB,EAA0B;EAChF;EACA,MAAMoD,cAAc,GAAI5B,EAAc,IAAc;IAClD,OAAOA,EAAE,CAAC9B,IAAI,KAAK,OAAO,IAAK8B,EAAE,CAAC9B,IAAI,KAAK,WAAW,IAAI8B,EAAE,CAACS,QAAQ,CAACK,MAAM,KAAK,CAAC,IAAId,EAAE,CAACS,QAAQ,CAAC,CAAC,CAAC,CAACvC,IAAI,KAAK,OAAQ;EACxH,CAAC;;EAED;EACA,MAAM2D,kBAAkB,GAAIC,OAAuC,IAA+E;IAChJ,MAAM3G,QAAQ,GAAG,GAAG;IACpB,MAAMC,SAAS,GAAG,GAAG,CAAC,CAAC;;IAEvB,IAAIC,KAAK,GAAG,OAAOyG,OAAO,CAACzG,KAAK,KAAK,QAAQ,GAAGyG,OAAO,CAACzG,KAAK,GAAIyG,OAAO,CAACzG,KAAK,GAAGgF,QAAQ,CAACyB,OAAO,CAACzG,KAAe,CAAC,GAAGiF,SAAU;IAC/H,IAAIhF,MAAM,GAAG,OAAOwG,OAAO,CAACxG,MAAM,KAAK,QAAQ,GAAGwG,OAAO,CAACxG,MAAM,GAAIwG,OAAO,CAACxG,MAAM,GAAG+E,QAAQ,CAACyB,OAAO,CAACxG,MAAgB,CAAC,GAAGgF,SAAU;;IAEpI;IACA,IAAIjF,KAAK,IAAIC,MAAM,EAAE;MACnB,MAAMyG,WAAW,GAAG1G,KAAK,GAAGC,MAAM;MAClC,IAAID,KAAK,GAAGF,QAAQ,EAAE;QACpBE,KAAK,GAAGF,QAAQ;QAChBG,MAAM,GAAGD,KAAK,GAAG0G,WAAW;MAC9B;MACA,IAAIzG,MAAM,GAAGF,SAAS,EAAE;QACtBE,MAAM,GAAGF,SAAS;QAClBC,KAAK,GAAGC,MAAM,GAAGyG,WAAW;MAC9B;IACF;IAEA,OAAO;MAAE1G,KAAK;MAAEC,MAAM;MAAEH,QAAQ;MAAEC;IAAU,CAAC;EAC/C,CAAC;;EAED;EACA,MAAM4G,eAAe,GAAIC,YAAoB,IAAkD;IAC7F,MAAMC,WAAW,GAAGD,YAAY,GAAG,CAAC,GAAGzD,QAAQ,CAACyD,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI;IACxE,MAAME,WAAW,GAAGF,YAAY,GAAGzD,QAAQ,CAACsC,MAAM,GAAG,CAAC,GAAGtC,QAAQ,CAACyD,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI;IAE1F,MAAMG,WAAW,GAAGF,WAAW,IAAIN,cAAc,CAACM,WAAW,CAAC;IAC9D,MAAMG,WAAW,GAAGF,WAAW,IAAIP,cAAc,CAACO,WAAW,CAAC;;IAE9D;IACA,MAAMG,OAAO,GAAGL,YAAY,KAAK,CAAC;IAClC,MAAMM,MAAM,GAAGN,YAAY,KAAKzD,QAAQ,CAACsC,MAAM,GAAG,CAAC;IAEnD,OAAO;MACL0B,SAAS,EAAEF,OAAO,GAAG,EAAE,GAAIF,WAAW,GAAG,CAAC,GAAG,EAAG;MAAO;MACvDK,YAAY,EAAEF,MAAM,GAAG,EAAE,GAAIF,WAAW,GAAG,CAAC,GAAG,EAAG,CAAK;IACzD,CAAC;EACH,CAAC;EAED,OAAO7D,QAAQ,CAACkE,GAAG,CAAC,CAACZ,OAAmB,EAAEa,KAAa,KAAyB;IAC9E,IAAIb,OAAO,CAAC5D,IAAI,KAAK,MAAM,EAAE;MAC3B,MAAM+C,KAAU,GAAG;QACjB1D,SAAS,EAAE,SAAS;QACpBqF,UAAU,EAAE;MACd,CAAC;MACD,IAAId,OAAO,CAACb,KAAK,KAAK,MAAM,EAAEA,KAAK,CAAC4B,UAAU,GAAG,MAAM;MACvD,IAAIf,OAAO,CAACb,KAAK,KAAK,QAAQ,EAAEA,KAAK,CAAC6B,SAAS,GAAG,QAAQ;MAC1D,oBACEhK,OAAA,CAACH,IAAI;QAAasI,KAAK,EAAEA,KAAM;QAAAR,QAAA,EAAEqB,OAAO,CAAC3D;MAAO,GAArCwE,KAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAuC,CAAC;IAE5D,CAAC,MAAM,IAAIpB,OAAO,CAAC5D,IAAI,KAAK,SAAS,EAAE;MACrC,MAAMiF,QAAQ,GAAGrB,OAAO,CAACf,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGe,OAAO,CAACf,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE;MACzE,MAAM8B,UAAU,GAAG,MAAe;MAClC,MAAMJ,YAAY,GAAGX,OAAO,CAACf,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGe,OAAO,CAACf,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE;MAC7E,MAAMyB,SAAS,GAAGV,OAAO,CAACf,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGe,OAAO,CAACf,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE;MAC1E,oBACEjI,OAAA,CAACH,IAAI;QAAasI,KAAK,EAAE;UAAEkC,QAAQ;UAAEN,UAAU;UAAEJ,YAAY;UAAED;QAAU,CAAE;QAAA/B,QAAA,EAAEqB,OAAO,CAAC3D;MAAO,GAAjFwE,KAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAmF,CAAC;IAExG,CAAC,MAAM,IAAIpB,OAAO,CAAC5D,IAAI,KAAK,MAAM,EAAE;MAClC,oBACEpF,OAAA,CAACF,IAAI;QAAaqI,KAAK,EAAE;UACvBmC,eAAe,EAAE,SAAS;UAC1BC,OAAO,EAAE,EAAE;UACXC,cAAc,EAAE,CAAC;UACjBC,YAAY,EAAE,CAAC;UACfC,UAAU,EAAE;QACd,CAAE;QAAA/C,QAAA,eACA3H,OAAA,CAACH,IAAI;UAACsI,KAAK,EAAE;YACXwC,UAAU,EAAE,SAAS;YACrBN,QAAQ,EAAE,EAAE;YACZO,KAAK,EAAE,SAAS;YAChBd,UAAU,EAAE;UACd,CAAE;UAAAnC,QAAA,EACCqB,OAAO,CAAC3D;QAAO;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC,GAdEP,KAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAeV,CAAC;IAEX,CAAC,MAAM,IAAIpB,OAAO,CAAC5D,IAAI,KAAK,YAAY,EAAE;MACxC,oBACEpF,OAAA,CAACF,IAAI;QAAaqI,KAAK,EAAE;UACvBuC,UAAU,EAAE,mBAAmB;UAC/BG,WAAW,EAAE,EAAE;UACfC,eAAe,EAAE,CAAC;UAClBN,cAAc,EAAE,CAAC;UACjBF,eAAe,EAAE;QACnB,CAAE;QAAA3C,QAAA,eACA3H,OAAA,CAACH,IAAI;UAACsI,KAAK,EAAE;YACX6B,SAAS,EAAE,QAAQ;YACnBY,KAAK,EAAE,SAAS;YAChBP,QAAQ,EAAE;UACZ,CAAE;UAAA1C,QAAA,EACCqB,OAAO,CAAC3D;QAAO;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC,GAbEP,KAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAcV,CAAC;IAEX,CAAC,MAAM,IAAIpB,OAAO,CAAC5D,IAAI,KAAK,OAAO,EAAE;MACnC,IAAI,CAAC4D,OAAO,CAAC9F,GAAG,EAAE,OAAO,IAAI;MAC7B,MAAM6H,YAAY,GAAG7B,eAAe,CAACW,KAAK,CAAC;MAC3C,MAAMmB,eAAe,GAAGjC,kBAAkB,CAACC,OAAO,CAAC;MACnD,oBACEhJ,OAAA,CAACJ,KAAK;QAEJuI,KAAK,EAAE;UACL9F,QAAQ,EAAE2I,eAAe,CAAC3I,QAAQ;UAClCC,SAAS,EAAE0I,eAAe,CAAC1I,SAAS;UACpCoH,SAAS,EAAEqB,YAAY,CAACrB,SAAS;UACjCC,YAAY,EAAEoB,YAAY,CAACpB,YAAY;UACvCpH,KAAK,EAAEyI,eAAe,CAACzI,KAAK;UAC5BC,MAAM,EAAEwI,eAAe,CAACxI;QAC1B,CAAE;QACFU,GAAG,EAAE8F,OAAO,CAAC9F;MAAI,GATZ2G,KAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUX,CAAC;IAEN,CAAC,MAAM,IAAIpB,OAAO,CAAC5D,IAAI,KAAK,WAAW,EAAE;MACvC;MACA,MAAM6F,YAAY,GAAGjC,OAAO,CAACrB,QAAQ,CAACK,MAAM,KAAK,CAAC,IAAIgB,OAAO,CAACrB,QAAQ,CAAC,CAAC,CAAC,CAACvC,IAAI,KAAK,OAAO;MAE1F,IAAI6F,YAAY,EAAE;QAChB;QACA,MAAMF,YAAY,GAAG7B,eAAe,CAACW,KAAK,CAAC;QAC3C,MAAMqB,UAAU,GAAGlC,OAAO,CAACrB,QAAQ,CAAC,CAAC,CAAC;;QAEtC;QACA,IAAIuD,UAAU,CAAC9F,IAAI,KAAK,OAAO,EAAE;UAC/B,MAAM4F,eAAe,GAAGjC,kBAAkB,CAACmC,UAAU,CAAC;UACtD,oBACElL,OAAA,CAACF,IAAI;YAAaqI,KAAK,EAAE;cACvBuB,SAAS,EAAEqB,YAAY,CAACrB,SAAS;cACjCC,YAAY,EAAEoB,YAAY,CAACpB;YAC7B,CAAE;YAAAhC,QAAA,EACCuD,UAAU,CAAChI,GAAG,gBACblD,OAAA,CAACJ,KAAK;cACJuI,KAAK,EAAE;gBACL9F,QAAQ,EAAE2I,eAAe,CAAC3I,QAAQ;gBAClCC,SAAS,EAAE0I,eAAe,CAAC1I,SAAS;gBACpCC,KAAK,EAAEyI,eAAe,CAACzI,KAAK;gBAC5BC,MAAM,EAAEwI,eAAe,CAACxI;cAC1B,CAAE;cACFU,GAAG,EAAEgI,UAAU,CAAChI;YAAI;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,GACA;UAAI,GAdCP,KAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeV,CAAC;QAEX,CAAC,MAAM;UACL;UACA,oBACEpK,OAAA,CAACF,IAAI;YAAaqI,KAAK,EAAE;cAAEwB,YAAY,EAAE;YAAE,CAAE;YAAAhC,QAAA,EAC1CkB,iBAAiB,CAACG,OAAO,CAACrB,QAAQ;UAAC,GAD3BkC,KAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CAAC;QAEX;MACF,CAAC,MAAM;QACL;QACA,oBACEpK,OAAA,CAACF,IAAI;UAAaqI,KAAK,EAAE;YACvBwB,YAAY,EAAE,CAAC;YACflF,SAAS,EAAE,SAAS;YACpBqF,UAAU,EAAE;UACd,CAAE;UAAAnC,QAAA,EACCkB,iBAAiB,CAACG,OAAO,CAACrB,QAAQ;QAAC,GAL3BkC,KAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMV,CAAC;MAEX;IACF,CAAC,MAAM,IAAIpB,OAAO,CAAC5D,IAAI,KAAK,MAAM,EAAE;MAClC,oBACEpF,OAAA,CAACF,IAAI;QAAaqI,KAAK,EAAE;UAAEwB,YAAY,EAAE,EAAE;UAAEkB,WAAW,EAAE;QAAE,CAAE;QAAAlD,QAAA,EAC3DqB,OAAO,CAACV,KAAK,CAACsB,GAAG,CAAC,CAACuB,IAAY,EAAEC,SAAiB,KAAK;UACtD,MAAMC,UAAU,GAAGrC,OAAO,CAACX,OAAO,GAAI,GAAE+C,SAAS,GAAG,CAAE,GAAE,GAAG,GAAG;UAC9D;UACA,MAAME,WAAW,GAAGtC,OAAO,CAACX,OAAO,GAAG3F,IAAI,CAAC6I,GAAG,CAAC,EAAE,EAAEF,UAAU,CAACrD,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE;UAE9E,oBACEhI,OAAA,CAACF,IAAI;YAAiBqI,KAAK,EAAE;cAAEqD,aAAa,EAAE,KAAK;cAAE7B,YAAY,EAAE,CAAC;cAAE8B,UAAU,EAAE;YAAa,CAAE;YAAA9D,QAAA,gBAC/F3H,OAAA,CAACH,IAAI;cAACsI,KAAK,EAAE;gBACX5F,KAAK,EAAE+I,WAAW;gBAClBI,WAAW,EAAE,CAAC;gBACdjH,SAAS,EAAEuE,OAAO,CAACX,OAAO,GAAG,OAAO,GAAG,MAAM;gBAC7CsD,UAAU,EAAE;cACd,CAAE;cAAAhE,QAAA,EACC0D;YAAU;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACPpK,OAAA,CAACH,IAAI;cAACsI,KAAK,EAAE;gBACXyD,IAAI,EAAE,CAAC;gBACPnH,SAAS,EAAE,SAAS;gBACpBqF,UAAU,EAAE;cACd,CAAE;cAAAnC,QAAA,EAAEwD;YAAI;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAbPgB,SAAS;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcd,CAAC;QAEX,CAAC;MAAC,GAvBOP,KAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAwBV,CAAC;IAEX,CAAC,MAAM,IAAIpB,OAAO,CAAC5D,IAAI,KAAK,OAAO,EAAE;MACnC,oBACEpF,OAAA,CAACH,IAAI;QAAA8H,QAAA,EAAc;MAAE,GAAVkC,KAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAEjC,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASyB,0BAA0BA,CAACC,KAAwB,EAAe;EAChF,oBAAO9L,OAAA,CAACH,IAAI;IAAA8H,QAAA,EAAEE,KAAK,CAACkE,OAAO,CAACD,KAAK,CAAC,GAAGA,KAAK,CAACE,IAAI,CAAC,IAAI,CAAC,GAAGF;EAAK;IAAA7B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC;AACvE;AAEA,SAASpF,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}