import React from "react";
import { Fa<PERSON>hartBar, FaCheckCircle, FaCode } from "react-icons/fa";
type StatCardProps = {
  title: string;
  value: number;
  description?: string;
  icon?: React.ReactNode;
};

const StatCard = ({ title, value, description, icon }: StatCardProps) => (
  <div className="flex-1 rounded-xl border border-gray-100 bg-white px-6 py-4 shadow-sm ">
    <div className="flex items-center justify-between">
      <div>
        <p className="mb-1 text-lg font-semibold text-gray-800">{title}</p>
        <div className="flex items-baseline gap-2">
          <h3 className="text-3xl font-bold text-gray-900">
            {value.toLocaleString()}
          </h3>
        </div>
        {description && (
          <p className="mt-1 text-sm font-medium text-gray-600">
            {description}
          </p>
        )}
      </div>
      {icon && (
        <div className="ml-4 flex items-center justify-center rounded-[24px] bg-blue-200 p-2 text-2xl text-blue-800">
          {icon}
        </div>
      )}
    </div>
  </div>
);

type DashboardStatsProps = {
  totalReports: number;
  totalResolvedReports: number;
  // pentestHours: number;
  numberOfPrograms: number;
};

const DashboardStats = ({
  totalReports,
  totalResolvedReports,
  // pentestHours,
  numberOfPrograms
}: DashboardStatsProps) => {
  const stats = [
    {
      title: "Total Reports",
      value: totalReports,
      description: "All submitted reports",
      icon: <FaChartBar /> // Example icon
    },
    {
      title: "Resolved Reports",
      value: totalResolvedReports,
      description: "Successfully resolved",
      icon: <FaCheckCircle /> // Example icon
    },
    // {
    //   title: "Pentest Hours",
    //   value: pentestHours,
    //   description: "Total hours spent",
    //   icon: <FaClock /> // Example icon
    // },
    {
      title: "Active Programs",
      value: numberOfPrograms,
      description: "Running programs",
      icon: <FaCode /> // Example icon
    }
  ].filter(stat => !(stat.title === "Pentest Hours" && stat.value === 0));

  return (
    <div className="flex justify-between gap-6">
      {stats.map(stat => (
        <StatCard
          key={stat.title}
          title={stat.title}
          value={stat.value}
          description={stat.description}
          icon={stat.icon} // Pass the icon prop
        />
      ))}
    </div>
  );
};

export default DashboardStats;
