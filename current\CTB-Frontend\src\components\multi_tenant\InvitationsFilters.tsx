import React from "react";
import SearchBar from "./SearchBar";

interface InvitationsFiltersProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  filterRole: string;
  setFilterRole: (role: string) => void;
  filterStatus: string;
  setFilterStatus: (status: string) => void;
  roleFilterOptions: { value: string; label: string }[];
}

const InvitationsFilters: React.FC<InvitationsFiltersProps> = ({
  searchQuery,
  setSearchQuery,
  filterRole,
  setFilterRole,
  filterStatus,
  setFilterStatus,
  roleFilterOptions
}) => {
  return (
    <div className="mb-6 grid grid-cols-1 gap-4 sm:grid-cols-3">
      <SearchBar value={searchQuery} onChange={setSearchQuery} />
      <select
        className="rounded-md border border-gray-300 p-2 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
        value={filterRole}
        onChange={e => setFilterRole(e.target.value)}
      >
        <option value="all">All Roles</option>
        {roleFilterOptions.map(roleOption => (
          <option key={roleOption.value} value={roleOption.value}>
            {roleOption.label}
          </option>
        ))}
      </select>
      <select
        className="rounded-md border border-gray-300 p-2 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
        value={filterStatus}
        onChange={e => setFilterStatus(e.target.value)}
      >
        <option value="all">All Status</option>
        <option value="pending">Pending</option>
        <option value="accepted">Accepted</option>
      </select>
    </div>
  );
};

export default InvitationsFilters;
