/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{html,ts,tsx}", "./public/index.html"],
  theme: {
    extend: {
      colors: {
        // Capture the Bug brand colours
        "ctb-blue-0": "#D4E2FF",
        "ctb-blue-50": "#90B0F8",
        "ctb-blue-100": "#8EB0FC",
        "ctb-blue-150": "#F4F7FF",
        "ctb-blue-200": "#187BF7",
        "ctb-blue-250": "#CBD7F7",
        "ctb-blue-300": "#D9E5FF",
        "ctb-blue-400": "#247BF2",
        "ctb-blue-500": "#000842",
        "ctb-blue": "#081549",
        "ctb-blue-600": "#3056D3",
        "ctb-blue-700": "#212B36",
        "ctb-blue-800": "#1814F3",
        "ctb-blue-900": "#062575",
        "ctb-blue-1000": "#12163C",
        "ctb-blue-550":"#0B45DB",

        "ctb-grey-50": "#EFEFEF",
        "ctb-grey-100": "#E5E7EB",
        "ctb-grey-150": "#F4F4F4",
        "ctb-grey-200": "#DBDBDF",
        "ctb-grey-250": "#F4F5F5",
        "ctb-grey-300": "#64748B",
        "ctb-grey-350": "#F8F8F8",
        "ctb-grey-400": "#F0F1F2",
        "ctb-grey-450": "#D8D8D8",
        "ctb-grey-500": "#ECECEC",
        "ctb-grey-600": "#3F3F46",
        "ctb-grey-650": "#D9D9D9",
        "ctb-grey-700": "#D4D4D8",
        "ctb-grey-750": "#727373",
        "ctb-grey-800": "#505050",
        "ctb-grey-900": "#545454",
       

        "ctb-red-50": "#FADEDE",
        "ctb-red-200": "#F87171",
        "ctb-red-300": "#EB4543",
        "ctb-red-400": "#C9190B",
        "ctb-red-500": "#DC2626",

        "ctb-green-50": "#E1F8F0",
        "ctb-green-100": "#34D399",
        "ctb-green-100": "#CFEBCD",
        "ctb-green-200": "#22C55E",
        "ctb-green-300": "#18A16F",
        "ctb-green-400": "#4ADE80",
        "ctb-green-500": "#08943B",
        "ctb-green-600": "#4CAF50",
       "ctb-green-600": "#00BFA5B2",
      "ctb-green-700": "#7CC6745E",

        "ctb-yellow-200": "#FBBF24",
        "ctb-yellow-250": "#F2B200",
        "ctb-yellow-300": "#F2B2005E",
        "ctb-orange-100":"#FFF6E7",
        "ctb-orange-200":"#EF9234",
        
        "ctb-white-100": "#FFF"
      },
      margin: {
        custom: "0 0 5px 0"
      },
      boxShadow: {
        "ctb-modal": "0px 4px 10px 0px rgba(0, 0, 0, 0.25)",
        "ctb-modal-2": "0px 0px 8px rgba(0, 0, 0, 0.25)",
        "verify-button": "0px 4px 4px 0px rgba(0, 0, 0, 0.25)",
        "header-section": "0px 0px 4px 0px rgba(0, 0, 0, 0.60)",
        pagination: "0px 1px 3px 0px rgba(0, 0, 0, 0.13)",
        table: "0px 3px 8px 0px rgba(0, 0, 0, 0.08)",
        "inner-lg": "inset 0px 4px 4px 0px rgba(0, 0, 0, 0.08)",
        "ctb-card": "0px 4px 10px 0px rgba(0, 0, 0, 0.10)"
      },
      dropShadow: {
        white: "5px 5px 5px rgba(255, 255, 255)"
      },
      fontFamily: {
        sans: ["Inter", "Roboto, sans-serif"],
        mono: ["Roboto Mono"]
      },
      fontSize: {
        dashboard: ["32px", "48px"],
        userCounterNum: ["24px", "36px"],
        xxs: ['0.6rem', '0.625rem'],
      },
      animation: {
        slideIntoRight: "slideIntoRight 0.1s",
        slideOutFromRight: "slideOutFromRight 0.1s"
      },
      keyframes: {
        slideIntoRight: {
          "0%": {
            right: "0px"
          },
          "100%": {
            right: "-512px"
          }
        },
        slideOutFromRight: {
          "0%": {
            right: "-512px"
          },
          "100%": {
            right: "0px"
          }
        }
      },
      gridTemplateColumns: {
        // Simple 16 column grid
        23: "repeat(23, minmax(0, 1fr))"
      }
    }
  },
  plugins: [require("@tailwindcss/typography")]
};
