import {
  ReportUpdateFields,
  getCertificate
} from "../../api/endpoints/reports/reports";
import toast from "react-hot-toast";
import {
  CTBReport,
  ReportRejectReason,
  prepareReportFormData
} from "../../api/endpoints/reports/parseReports";
import { useNavigate } from "react-router-dom";
import {
  useDeleteReportMutation,
  useGenerateCertificateMutation,
  useGetReportQuery,
  useSubmitReportMutation,
  useUpdateReportMutation,
  useUpdateReportStateMutation,
  useGetReportRecommendationsQuery
} from "../../api/endpoints/reportsApi";
import { useInitiatePaymentMutation } from "../../api/endpoints/paymentsApi";

const useReport = (id?: number) => {
  const navigate = useNavigate();
  const {
    data: report,
    isError,
    isLoading
  } = useGetReportQuery(id as number, { skip: id === undefined || isNaN(id) });
  const [postReport] = useUpdateReportMutation();
  const [submitReportMutation] = useSubmitReportMutation();
  const [updateReportStateMutatation] = useUpdateReportStateMutation();
  const [initiatePaymentMutation] = useInitiatePaymentMutation();
  const [generateCertificateMutation] = useGenerateCertificateMutation();
  const [deleteReportMutation] = useDeleteReportMutation();
  const { data: recommendations = [], isLoading: isLoadingRecommendations } =
    useGetReportRecommendationsQuery(id as number, {
      skip: id === undefined || isNaN(id)
    });

  /**
   * Saves the report with the new details. Only works when
   * user is a researcher.
   */
  const updateReport = async (details: ReportUpdateFields, files?: Blob[]) => {
    const loader = toast.loading("Saving...");
    let reportId = id;

    await postReport({
      data: await prepareReportFormData({ id: reportId, details, files })
    }).then(res => {
      toast.remove(loader);

      if ("error" in res) {
        toast.error("Failed to save report.");
      } else {
        reportId = (res as { data: CTBReport }).data.id;

        toast.success("Report saved!");
        navigate(`/dashboard/reports/${reportId}`);
      }
    });

    return reportId as number;
  };

  /**
   * Generate a certificate for the researcher who created this report
   * (Admins only)
   */
  const generateCertificate = async () => {
    if (id) {
      const loader = toast.loading("Generating certificate...");

      generateCertificateMutation(id).then(res => {
        toast.remove(loader);

        if ("error" in res) {
          toast.error("Failed to generate certificate - server error");
        } else {
          toast.success("Certificate generated!");
        }
      });
    } else {
      toast.error("Failed to generate certificate. Try refreshing the page.");
    }
  };

  const deleteReport = async () => {
    if (!id) {
      toast.error("No report selected for deletion.");
      return;
    }

    // Confirm deletion
    const confirmDelete = window.confirm(
      "Are you sure you want to delete this report?"
    );
    if (!confirmDelete) return;

    const loader = toast.loading("Deleting report...");
    try {
      await deleteReportMutation(id).unwrap();
      toast.success("Report deleted successfully!");
      navigate("/dashboard/reports"); // Navigate after success
    } catch (error) {
      toast.error("Failed to delete report.");
    } finally {
      toast.dismiss(loader);
    }
  };

  /**
   * Open the certificate associated with this report in a new tab
   */
  const viewCertificate = async () => {
    if (id) {
      getCertificate(id)
        .then(blob => {
          const url = URL.createObjectURL(blob);
          window.open(url, "_blank");
        })
        .catch(() => toast.error(`Failed to retrieve certificate`));
    }
  };

  /**
   * Initiates the payout as a business
   */
  const initiatePayment = async (amount: number) => {
    if (report) {
      const loader = toast.loading("Processing...");

      initiatePaymentMutation({ reportId: report.id, amount }).then(res => {
        toast.remove(loader);

        if ("error" in res) {
          toast.error("Failed to initiate payment");
        } else {
          toast.success("Payment initiated");
        }
      });
    }
  };

  return {
    report,
    isLoading,
    isError,
    updateReport,
    generateCertificate,
    deleteReport,
    viewCertificate,
    initiatePayment,
    submitReport: (id: number) => {
      const loader = toast.loading("Saving...");

      submitReportMutation(id).then(res => {
        toast.remove(loader);

        if ("error" in res) {
          toast.error("Failed to submit report.");
        } else {
          toast.success("Report submitted!");
        }
      });
    },
    updateReportState: async (
      state:
        | "approved"
        | "rejected"
        | "closed"
        | "request info"
        | "acceptRisk"
        | "nonActionableIssue"
        | "requestFix"
        | "out of scope"
        // | "QA Disagrees"
        | "QA Modified",
      rejectReason?: ReportRejectReason
    ) => {
      if (id) {
        const loader = toast.loading("Processing...");

        updateReportStateMutatation({ id, state, rejectReason }).then(res => {
          toast.remove(loader);

          if ("error" in res) {
            toast.error("Failed to update report.");
          } else {
            toast.success(`Report ${state}`);
          }
        });
      }
    },
    recommendations,
    isLoadingRecommendations
  };
};

export default useReport;
