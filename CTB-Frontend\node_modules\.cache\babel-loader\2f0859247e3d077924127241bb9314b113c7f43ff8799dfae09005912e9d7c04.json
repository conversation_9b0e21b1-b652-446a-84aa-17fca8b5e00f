{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\jira-old\\\\CTB-Frontend\\\\src\\\\pages\\\\automated-report\\\\components\\\\sections\\\\DocumentReferencePage.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Page, View, Text, StyleSheet } from '@react-pdf/renderer';\nimport { useSectionPages } from '../SectionPageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst styles = StyleSheet.create({\n  footer: {\n    position: 'absolute',\n    bottom: 30,\n    left: 0,\n    right: 0,\n    fontSize: 10,\n    color: 'grey',\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    paddingHorizontal: 24\n  },\n  footerLeft: {\n    textAlign: 'left',\n    flex: 1\n  },\n  footerRight: {\n    textAlign: 'right',\n    flex: 1\n  }\n});\nconst DocumentReferencePage = ({\n  reportData,\n  sectionId\n}) => {\n  _s();\n  const {\n    updateSectionPage\n  } = useSectionPages();\n  return /*#__PURE__*/_jsxDEV(Page, {\n    size: \"A4\",\n    id: sectionId,\n    style: {\n      flexDirection: 'column',\n      backgroundColor: '#ffffff',\n      padding: '20mm 15mm',\n      fontFamily: 'Helvetica',\n      fontSize: 12\n    },\n    children: [/*#__PURE__*/_jsxDEV(View, {\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          fontSize: 16,\n          fontWeight: 'bold',\n          color: '#2563eb',\n          marginBottom: 12,\n          lineHeight: 1.4\n        },\n        children: \"DOCUMENT REFERENCE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(View, {\n      style: {\n        marginBottom: 20\n      },\n      children: /*#__PURE__*/_jsxDEV(View, {\n        style: {\n          borderRadius: 8,\n          overflow: 'hidden',\n          borderWidth: 1,\n          borderColor: '#e5e7eb',\n          fontSize: 12\n        },\n        children: /*#__PURE__*/_jsxDEV(View, {\n          style: {\n            flexDirection: 'column',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(View, {\n            style: {\n              flexDirection: 'row',\n              borderBottomWidth: 1,\n              borderBottomColor: '#e5e7eb'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                fontWeight: 'bold',\n                backgroundColor: '#eff6ff',\n                width: '30%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"DOCUMENT TITLE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                flex: 1,\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: reportData.report_title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(View, {\n            style: {\n              flexDirection: 'row',\n              borderBottomWidth: 1,\n              borderBottomColor: '#e5e7eb'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                fontWeight: 'bold',\n                backgroundColor: '#eff6ff',\n                width: '30%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"DOCUMENT NUMBER\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                flex: 1,\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: reportData.document_number\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(View, {\n            style: {\n              flexDirection: 'row',\n              borderBottomWidth: 1,\n              borderBottomColor: '#e5e7eb'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                fontWeight: 'bold',\n                backgroundColor: '#eff6ff',\n                width: '30%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"CLASSIFICATION\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(View, {\n              style: {\n                padding: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(View, {\n                style: {\n                  backgroundColor: '#fef2f2',\n                  paddingHorizontal: 16,\n                  paddingVertical: 4,\n                  borderRadius: 12,\n                  alignSelf: 'flex-start'\n                },\n                children: /*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    color: '#dc2626',\n                    fontSize: 10,\n                    lineHeight: 1.4\n                  },\n                  children: \"Confidential\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(View, {\n            style: {\n              flexDirection: 'row',\n              borderBottomWidth: 1,\n              borderBottomColor: '#e5e7eb'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                fontWeight: 'bold',\n                backgroundColor: '#eff6ff',\n                width: '30%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"REFERENCE FILE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                flex: 1,\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: Number(reportData.version_number) >= 2 ? `V${Number(reportData.version_number) - 1}` : 'NONE'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(View, {\n            style: {\n              flexDirection: 'row'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                fontWeight: 'bold',\n                backgroundColor: '#eff6ff',\n                width: '30%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"REVISION DATE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                flex: 1,\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: reportData.revision_date\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(View, {\n      style: {\n        flexDirection: 'row',\n        marginBottom: 20,\n        gap: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(View, {\n        style: {\n          width: '50%',\n          backgroundColor: '#eff6ff',\n          borderRadius: 10,\n          padding: 16,\n          alignItems: 'center',\n          justifyContent: 'center',\n          borderWidth: 1,\n          borderColor: '#dbeafe',\n          minHeight: 100\n        },\n        children: [/*#__PURE__*/_jsxDEV(View, {\n          style: {\n            marginBottom: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"28\",\n            height: \"28\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n              x: \"3\",\n              y: \"5\",\n              width: \"18\",\n              height: \"16\",\n              rx: \"3\",\n              fill: \"#2563eb\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n              x: \"3\",\n              y: \"8\",\n              width: \"18\",\n              height: \"13\",\n              rx: \"2\",\n              fill: \"#fff\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n              x: \"7\",\n              y: \"12\",\n              width: \"2\",\n              height: \"2\",\n              rx: \"1\",\n              fill: \"#2563eb\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n              x: \"11\",\n              y: \"12\",\n              width: \"2\",\n              height: \"2\",\n              rx: \"1\",\n              fill: \"#2563eb\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n              x: \"15\",\n              y: \"12\",\n              width: \"2\",\n              height: \"2\",\n              rx: \"1\",\n              fill: \"#2563eb\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            fontWeight: 'bold',\n            color: '#2563eb',\n            fontSize: 13,\n            marginBottom: 4,\n            lineHeight: 1.4\n          },\n          children: \"PUBLISH DATE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            fontWeight: 'bold',\n            fontSize: 16,\n            color: '#1e293b',\n            lineHeight: 1.4\n          },\n          children: reportData.current_date\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(View, {\n        style: {\n          width: '50%',\n          backgroundColor: '#eff6ff',\n          borderRadius: 10,\n          padding: 16,\n          alignItems: 'center',\n          justifyContent: 'center',\n          borderWidth: 1,\n          borderColor: '#dbeafe',\n          minHeight: 100\n        },\n        children: [/*#__PURE__*/_jsxDEV(View, {\n          style: {\n            marginBottom: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"28\",\n            height: \"28\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n              cx: \"12\",\n              cy: \"8\",\n              r: \"4\",\n              fill: \"#2563eb\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n              x: \"5\",\n              y: \"15\",\n              width: \"14\",\n              height: \"5\",\n              rx: \"2.5\",\n              fill: \"#2563eb\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            fontWeight: 'bold',\n            color: '#2563eb',\n            fontSize: 13,\n            marginBottom: 4,\n            lineHeight: 1.4\n          },\n          children: \"TEST PERFORMED BY\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            fontWeight: 'bold',\n            fontSize: 16,\n            color: '#1e293b',\n            marginBottom: 4,\n            lineHeight: 1.4\n          },\n          children: reportData.test_lead\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(View, {\n          style: {\n            backgroundColor: '#dbeafe',\n            paddingHorizontal: 12,\n            paddingVertical: 4,\n            borderRadius: 12,\n            marginTop: 2,\n            alignSelf: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              color: '#2563eb',\n              fontSize: 12,\n              lineHeight: 1.4\n            },\n            children: \"Lead\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(View, {\n      style: {\n        marginBottom: 20\n      },\n      children: [/*#__PURE__*/_jsxDEV(View, {\n        style: {\n          flexDirection: 'row',\n          alignItems: 'center',\n          marginBottom: 6\n        },\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            fontWeight: 'bold',\n            color: '#2563eb',\n            fontSize: 16,\n            lineHeight: 1.4\n          },\n          children: \"CONTRIBUTION\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(View, {\n        style: {\n          borderRadius: 8,\n          overflow: 'hidden',\n          borderWidth: 1,\n          borderColor: '#e5e7eb',\n          fontSize: 12\n        },\n        children: /*#__PURE__*/_jsxDEV(View, {\n          style: {\n            flexDirection: 'column',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(View, {\n            style: {\n              flexDirection: 'row',\n              backgroundColor: '#eff6ff',\n              borderBottomWidth: 1,\n              borderBottomColor: '#e5e7eb'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                textAlign: 'left',\n                fontWeight: 'bold',\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"ROLE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                textAlign: 'left',\n                fontWeight: 'bold',\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"NAME\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                textAlign: 'left',\n                fontWeight: 'bold',\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"DESIGNATION\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                textAlign: 'left',\n                fontWeight: 'bold',\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"DATE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(View, {\n            style: {\n              flexDirection: 'row',\n              borderBottomWidth: 1,\n              borderBottomColor: '#e5e7eb'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                fontWeight: 'bold',\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"PREPARED BY\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                color: '#1d4ed8',\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: reportData.prepared_by\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                fontWeight: 'bold',\n                color: '#92400e',\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"PENTESTER\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(View, {\n              style: {\n                padding: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(View, {\n                style: {\n                  backgroundColor: '#dbeafe',\n                  paddingHorizontal: 12,\n                  paddingVertical: 4,\n                  borderRadius: 6,\n                  alignSelf: 'flex-start'\n                },\n                children: /*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    color: '#2563eb',\n                    fontSize: 12,\n                    lineHeight: 1.4\n                  },\n                  children: reportData.current_date\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(View, {\n            style: {\n              flexDirection: 'row',\n              borderBottomWidth: 1,\n              borderBottomColor: '#e5e7eb'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                fontWeight: 'bold',\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"REVIEWED BY\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                color: '#1d4ed8',\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: reportData.reviewed_by\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                fontWeight: 'bold',\n                color: '#92400e',\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"SENIOR MANAGER\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(View, {\n              style: {\n                padding: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(View, {\n                style: {\n                  backgroundColor: '#dbeafe',\n                  paddingHorizontal: 12,\n                  paddingVertical: 4,\n                  borderRadius: 6,\n                  alignSelf: 'flex-start'\n                },\n                children: /*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    color: '#2563eb',\n                    fontSize: 12,\n                    lineHeight: 1.4\n                  },\n                  children: reportData.current_date\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(View, {\n            style: {\n              flexDirection: 'row'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                fontWeight: 'bold',\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"APPROVED BY\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                color: '#1d4ed8',\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: reportData.approved_by\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                fontWeight: 'bold',\n                color: '#92400e',\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"MANAGER\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(View, {\n              style: {\n                padding: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(View, {\n                style: {\n                  backgroundColor: '#dbeafe',\n                  paddingHorizontal: 12,\n                  paddingVertical: 4,\n                  borderRadius: 6,\n                  alignSelf: 'flex-start'\n                },\n                children: /*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    color: '#2563eb',\n                    fontSize: 12,\n                    lineHeight: 1.4\n                  },\n                  children: reportData.current_date\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(View, {\n      children: [/*#__PURE__*/_jsxDEV(View, {\n        style: {\n          flexDirection: 'row',\n          alignItems: 'center',\n          marginBottom: 8\n        },\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            fontWeight: 'bold',\n            color: '#2563eb',\n            fontSize: 16,\n            lineHeight: 1.4\n          },\n          children: \"VERSION CONTROL\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(View, {\n        style: {\n          borderRadius: 8,\n          overflow: 'hidden',\n          borderWidth: 1,\n          borderColor: '#e5e7eb',\n          fontSize: 12\n        },\n        children: /*#__PURE__*/_jsxDEV(View, {\n          style: {\n            flexDirection: 'column',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(View, {\n            style: {\n              flexDirection: 'row',\n              backgroundColor: '#eff6ff',\n              borderBottomWidth: 1,\n              borderBottomColor: '#e5e7eb'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                textAlign: 'left',\n                fontWeight: 'bold',\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"VERSION\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                textAlign: 'left',\n                fontWeight: 'bold',\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"DATE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                textAlign: 'left',\n                fontWeight: 'bold',\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"AUTHOR\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                textAlign: 'left',\n                fontWeight: 'bold',\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"DESCRIPTION\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(View, {\n            style: {\n              flexDirection: 'row'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: reportData.version_number\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: reportData.current_date\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: reportData.prepared_by\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 8,\n                width: '25%',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: reportData.version_description ? reportData.version_description : Number(reportData.version_number) > 1 ? `Version ${reportData.version_number} update` : 'Initial Report'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(View, {\n      style: styles.footer,\n      fixed: true,\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        style: styles.footerLeft,\n        children: reportData.document_number || 'Document Number'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        style: styles.footerRight,\n        render: ({\n          pageNumber,\n          totalPages\n        }) => `${pageNumber} / ${totalPages}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      style: {\n        display: 'none'\n      },\n      render: ({\n        pageNumber\n      }) => {\n        updateSectionPage('DocumentReference', pageNumber);\n        return '';\n      },\n      fixed: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n};\n_s(DocumentReferencePage, \"3rf3HaIqG9R7of1rm8wX9C3kpH0=\", false, function () {\n  return [useSectionPages];\n});\n_c = DocumentReferencePage;\nexport default DocumentReferencePage;\nvar _c;\n$RefreshReg$(_c, \"DocumentReferencePage\");", "map": {"version": 3, "names": ["React", "Page", "View", "Text", "StyleSheet", "useSectionPages", "jsxDEV", "_jsxDEV", "styles", "create", "footer", "position", "bottom", "left", "right", "fontSize", "color", "flexDirection", "justifyContent", "alignItems", "paddingHorizontal", "footerLeft", "textAlign", "flex", "footerRight", "DocumentReferencePage", "reportData", "sectionId", "_s", "updateSectionPage", "size", "id", "style", "backgroundColor", "padding", "fontFamily", "children", "marginBottom", "fontWeight", "lineHeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "borderRadius", "overflow", "borderWidth", "borderColor", "width", "borderBottomWidth", "borderBottomColor", "report_title", "document_number", "paddingVertical", "alignSelf", "Number", "version_number", "revision_date", "gap", "minHeight", "height", "viewBox", "fill", "x", "y", "rx", "current_date", "cx", "cy", "r", "test_lead", "marginTop", "prepared_by", "reviewed_by", "approved_by", "version_description", "fixed", "render", "pageNumber", "totalPages", "display", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/jira-old/CTB-Frontend/src/pages/automated-report/components/sections/DocumentReferencePage.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Page, View, Text, StyleSheet } from '@react-pdf/renderer';\r\nimport { ReportData } from '../../types/report.types';\r\nimport { useSectionPages } from '../SectionPageContext';\r\n\r\ninterface DocumentReferencePageProps {\r\n  reportData: ReportData;\r\n  sectionId?: string;\r\n}\r\n\r\nconst styles = StyleSheet.create({\r\n  footer: {\r\n    position: 'absolute',\r\n    bottom: 30,\r\n    left: 0,\r\n    right: 0,\r\n    fontSize: 10,\r\n    color: 'grey',\r\n    flexDirection: 'row',\r\n    justifyContent: 'space-between',\r\n    alignItems: 'center',\r\n    paddingHorizontal: 24,\r\n  },\r\n  footerLeft: {\r\n    textAlign: 'left',\r\n    flex: 1,\r\n  },\r\n  footerRight: {\r\n    textAlign: 'right',\r\n    flex: 1,\r\n  },\r\n});\r\n\r\nconst DocumentReferencePage: React.FC<DocumentReferencePageProps> = ({ reportData, sectionId }) => {\r\n  const { updateSectionPage } = useSectionPages();\r\n  return (\r\n    <Page size=\"A4\" id={sectionId} style={{\r\n      flexDirection: 'column',\r\n      backgroundColor: '#ffffff',\r\n      padding: '20mm 15mm',\r\n      fontFamily: 'Helvetica',\r\n      fontSize: 12,\r\n    }}>\r\n      <View style={{ marginBottom: 16 }}>\r\n        <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#2563eb', marginBottom: 12, lineHeight: 1.4 }}>\r\n          DOCUMENT REFERENCE\r\n        </Text>\r\n      </View>\r\n      {/* Document Information */}\r\n      <View style={{ marginBottom: 20 }}>\r\n      \r\n        <View style={{ borderRadius: 8, overflow: 'hidden', borderWidth: 1, borderColor: '#e5e7eb', fontSize: 12 }}>\r\n          <View style={{ flexDirection: 'column', width: '100%' }}>\r\n            <View style={{ flexDirection: 'row', borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>\r\n              <Text style={{ padding: 8, fontWeight: 'bold', backgroundColor: '#eff6ff', width: '30%', fontSize: 12, lineHeight: 1.4 }}>\r\n                DOCUMENT TITLE\r\n              </Text>\r\n              <Text style={{ padding: 8, flex: 1, fontSize: 12, lineHeight: 1.4 }}>{reportData.report_title}</Text>\r\n            </View>\r\n            <View style={{ flexDirection: 'row', borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>\r\n              <Text style={{ padding: 8, fontWeight: 'bold', backgroundColor: '#eff6ff', width: '30%', fontSize: 12, lineHeight: 1.4 }}>\r\n                DOCUMENT NUMBER\r\n              </Text>\r\n              <Text style={{ padding: 8, flex: 1, fontSize: 12, lineHeight: 1.4 }}>{reportData.document_number}</Text>\r\n            </View>\r\n            <View style={{ flexDirection: 'row', borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>\r\n              <Text style={{ padding: 8, fontWeight: 'bold', backgroundColor: '#eff6ff', width: '30%', fontSize: 12, lineHeight: 1.4 }}>\r\n                CLASSIFICATION\r\n              </Text>\r\n              <View style={{ padding: 8 }}>\r\n                <View style={{\r\n                  backgroundColor: '#fef2f2',\r\n                  paddingHorizontal: 16,\r\n                  paddingVertical: 4,\r\n                  borderRadius: 12,\r\n                  alignSelf: 'flex-start'\r\n                }}>\r\n                  <Text style={{ color: '#dc2626', fontSize: 10, lineHeight: 1.4 }}>Confidential</Text>\r\n                </View>\r\n              </View>\r\n            </View>\r\n            <View style={{ flexDirection: 'row', borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>\r\n              <Text style={{ padding: 8, fontWeight: 'bold', backgroundColor: '#eff6ff', width: '30%', fontSize: 12, lineHeight: 1.4 }}>\r\n                REFERENCE FILE\r\n              </Text>\r\n              <Text style={{ padding: 8, flex: 1, fontSize: 12, lineHeight: 1.4 }}>{Number(reportData.version_number) >= 2 ? `V${Number(reportData.version_number) - 1}` : 'NONE'}</Text>\r\n            </View>\r\n            <View style={{ flexDirection: 'row' }}>\r\n              <Text style={{ padding: 8, fontWeight: 'bold', backgroundColor: '#eff6ff', width: '30%', fontSize: 12, lineHeight: 1.4 }}>\r\n                REVISION DATE\r\n              </Text>\r\n              <Text style={{ padding: 8, flex: 1, fontSize: 12, lineHeight: 1.4 }}>{reportData.revision_date}</Text>\r\n            </View>\r\n          </View>\r\n        </View>\r\n      </View>\r\n      {/* Publish Date and Test Performed By */}\r\n      <View style={{ flexDirection: 'row', marginBottom: 20, gap: 16 }}>\r\n        {/* Publish Date Card */}\r\n        <View style={{\r\n          width: '50%',\r\n          backgroundColor: '#eff6ff',\r\n          borderRadius: 10,\r\n          padding: 16,\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          borderWidth: 1,\r\n          borderColor: '#dbeafe',\r\n          minHeight: 100,\r\n        }}>\r\n          {/* Calendar SVG */}\r\n          <View style={{ marginBottom: 8 }}>\r\n            <svg width=\"28\" height=\"28\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n              <rect x=\"3\" y=\"5\" width=\"18\" height=\"16\" rx=\"3\" fill=\"#2563eb\"/>\r\n              <rect x=\"3\" y=\"8\" width=\"18\" height=\"13\" rx=\"2\" fill=\"#fff\"/>\r\n              <rect x=\"7\" y=\"12\" width=\"2\" height=\"2\" rx=\"1\" fill=\"#2563eb\"/>\r\n              <rect x=\"11\" y=\"12\" width=\"2\" height=\"2\" rx=\"1\" fill=\"#2563eb\"/>\r\n              <rect x=\"15\" y=\"12\" width=\"2\" height=\"2\" rx=\"1\" fill=\"#2563eb\"/>\r\n            </svg>\r\n          </View>\r\n          <Text style={{ fontWeight: 'bold', color: '#2563eb', fontSize: 13, marginBottom: 4, lineHeight: 1.4 }}>\r\n            PUBLISH DATE\r\n          </Text>\r\n          <Text style={{ fontWeight: 'bold', fontSize: 16, color: '#1e293b', lineHeight: 1.4 }}>\r\n            {reportData.current_date}\r\n          </Text>\r\n        </View>\r\n        {/* Test Performed By Card */}\r\n        <View style={{\r\n          width: '50%',\r\n          backgroundColor: '#eff6ff',\r\n          borderRadius: 10,\r\n          padding: 16,\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          borderWidth: 1,\r\n          borderColor: '#dbeafe',\r\n          minHeight: 100,\r\n        }}>\r\n          {/* User SVG */}\r\n          <View style={{ marginBottom: 8 }}>\r\n            <svg width=\"28\" height=\"28\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n              <circle cx=\"12\" cy=\"8\" r=\"4\" fill=\"#2563eb\"/>\r\n              <rect x=\"5\" y=\"15\" width=\"14\" height=\"5\" rx=\"2.5\" fill=\"#2563eb\"/>\r\n            </svg>\r\n          </View>\r\n          <Text style={{ fontWeight: 'bold', color: '#2563eb', fontSize: 13, marginBottom: 4, lineHeight: 1.4 }}>\r\n            TEST PERFORMED BY\r\n          </Text>\r\n          <Text style={{ fontWeight: 'bold', fontSize: 16, color: '#1e293b', marginBottom: 4, lineHeight: 1.4 }}>\r\n            {reportData.test_lead}\r\n          </Text>\r\n          <View style={{\r\n            backgroundColor: '#dbeafe',\r\n            paddingHorizontal: 12,\r\n            paddingVertical: 4,\r\n            borderRadius: 12,\r\n            marginTop: 2,\r\n            alignSelf: 'center'\r\n          }}>\r\n            <Text style={{ color: '#2563eb', fontSize: 12, lineHeight: 1.4 }}>Lead</Text>\r\n          </View>\r\n        </View>\r\n      </View>\r\n      {/* Contribution Table */}\r\n      <View style={{ marginBottom: 20 }}>\r\n        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 6 }}>\r\n          <Text style={{ fontWeight: 'bold', color: '#2563eb', fontSize: 16, lineHeight: 1.4 }}>\r\n            CONTRIBUTION\r\n          </Text>\r\n        </View>\r\n        <View style={{ borderRadius: 8, overflow: 'hidden', borderWidth: 1, borderColor: '#e5e7eb', fontSize: 12 }}>\r\n          <View style={{ flexDirection: 'column', width: '100%' }}>\r\n            <View style={{ flexDirection: 'row', backgroundColor: '#eff6ff', borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>\r\n              <Text style={{ padding: 8, textAlign: 'left', fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>ROLE</Text>\r\n              <Text style={{ padding: 8, textAlign: 'left', fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>NAME</Text>\r\n              <Text style={{ padding: 8, textAlign: 'left', fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>DESIGNATION</Text>\r\n              <Text style={{ padding: 8, textAlign: 'left', fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>DATE</Text>\r\n            </View>\r\n            <View style={{ flexDirection: 'row', borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>\r\n              <Text style={{ padding: 8, fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>PREPARED BY</Text>\r\n              <Text style={{ padding: 8, color: '#1d4ed8', width: '25%', fontSize: 12, lineHeight: 1.4 }}>{reportData.prepared_by}</Text>\r\n              <Text style={{ padding: 8, fontWeight: 'bold', color: '#92400e', width: '25%', fontSize: 12, lineHeight: 1.4 }}>PENTESTER</Text>\r\n              <View style={{ padding: 8 }}>\r\n                <View style={{ backgroundColor: '#dbeafe', paddingHorizontal: 12, paddingVertical: 4, borderRadius: 6, alignSelf: 'flex-start' }}>\r\n                  <Text style={{ color: '#2563eb', fontSize: 12, lineHeight: 1.4 }}>{reportData.current_date}</Text>\r\n                </View>\r\n              </View>\r\n            </View>\r\n            <View style={{ flexDirection: 'row', borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>\r\n              <Text style={{ padding: 8, fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>REVIEWED BY</Text>\r\n              <Text style={{ padding: 8, color: '#1d4ed8', width: '25%', fontSize: 12, lineHeight: 1.4 }}>{reportData.reviewed_by}</Text>\r\n              <Text style={{ padding: 8, fontWeight: 'bold', color: '#92400e', width: '25%', fontSize: 12, lineHeight: 1.4 }}>SENIOR MANAGER</Text>\r\n              <View style={{ padding: 8 }}>\r\n                <View style={{ backgroundColor: '#dbeafe', paddingHorizontal: 12, paddingVertical: 4, borderRadius: 6, alignSelf: 'flex-start' }}>\r\n                  <Text style={{ color: '#2563eb', fontSize: 12, lineHeight: 1.4 }}>{reportData.current_date}</Text>\r\n                </View>\r\n              </View>\r\n            </View>\r\n            <View style={{ flexDirection: 'row' }}>\r\n              <Text style={{ padding: 8, fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>APPROVED BY</Text>\r\n              <Text style={{ padding: 8, color: '#1d4ed8', width: '25%', fontSize: 12, lineHeight: 1.4 }}>{reportData.approved_by}</Text>\r\n              <Text style={{ padding: 8, fontWeight: 'bold', color: '#92400e', width: '25%', fontSize: 12, lineHeight: 1.4 }}>MANAGER</Text>\r\n              <View style={{ padding: 8 }}>\r\n                <View style={{ backgroundColor: '#dbeafe', paddingHorizontal: 12, paddingVertical: 4, borderRadius: 6, alignSelf: 'flex-start' }}>\r\n                  <Text style={{ color: '#2563eb', fontSize: 12, lineHeight: 1.4 }}>{reportData.current_date}</Text>\r\n                </View>\r\n              </View>\r\n            </View>\r\n          </View>\r\n        </View>\r\n      </View>\r\n      {/* Version Control Table */}\r\n      <View>\r\n        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>\r\n          <Text style={{ fontWeight: 'bold', color: '#2563eb', fontSize: 16, lineHeight: 1.4 }}>\r\n            VERSION CONTROL\r\n          </Text>\r\n        </View>\r\n        <View style={{ borderRadius: 8, overflow: 'hidden', borderWidth: 1, borderColor: '#e5e7eb', fontSize: 12 }}>\r\n          <View style={{ flexDirection: 'column', width: '100%' }}>\r\n            <View style={{ flexDirection: 'row', backgroundColor: '#eff6ff', borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>\r\n              <Text style={{ padding: 8, textAlign: 'left', fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>VERSION</Text>\r\n              <Text style={{ padding: 8, textAlign: 'left', fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>DATE</Text>\r\n              <Text style={{ padding: 8, textAlign: 'left', fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>AUTHOR</Text>\r\n              <Text style={{ padding: 8, textAlign: 'left', fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>DESCRIPTION</Text>\r\n            </View>\r\n            <View style={{ flexDirection: 'row' }}>\r\n              <Text style={{ padding: 8, width: '25%', fontSize: 12, lineHeight: 1.4 }}>{reportData.version_number}</Text>\r\n              <Text style={{ padding: 8, width: '25%', fontSize: 12, lineHeight: 1.4 }}>{reportData.current_date}</Text>\r\n              <Text style={{ padding: 8, width: '25%', fontSize: 12, lineHeight: 1.4 }}>{reportData.prepared_by}</Text>\r\n              <Text style={{ padding: 8, width: '25%', fontSize: 12, lineHeight: 1.4 }}>{reportData.version_description ? reportData.version_description : (Number(reportData.version_number) > 1 ? `Version ${reportData.version_number} update` : 'Initial Report')}</Text>\r\n            </View>\r\n          </View>\r\n        </View>\r\n      </View>\r\n      <View style={styles.footer} fixed>\r\n        <Text style={styles.footerLeft}>{reportData.document_number || 'Document Number'}</Text>\r\n        <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />\r\n      </View>\r\n      <Text style={{ display: 'none' }} render={({ pageNumber }) => { updateSectionPage('DocumentReference', pageNumber); return ''; }} fixed />\r\n    </Page>\r\n  );\r\n};\r\n\r\nexport default DocumentReferencePage; "], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,UAAU,QAAQ,qBAAqB;AAElE,SAASC,eAAe,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOxD,MAAMC,MAAM,GAAGJ,UAAU,CAACK,MAAM,CAAC;EAC/BC,MAAM,EAAE;IACNC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,MAAM;IACbC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBC,iBAAiB,EAAE;EACrB,CAAC;EACDC,UAAU,EAAE;IACVC,SAAS,EAAE,MAAM;IACjBC,IAAI,EAAE;EACR,CAAC;EACDC,WAAW,EAAE;IACXF,SAAS,EAAE,OAAO;IAClBC,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AAEF,MAAME,qBAA2D,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACjG,MAAM;IAAEC;EAAkB,CAAC,GAAGxB,eAAe,CAAC,CAAC;EAC/C,oBACEE,OAAA,CAACN,IAAI;IAAC6B,IAAI,EAAC,IAAI;IAACC,EAAE,EAAEJ,SAAU;IAACK,KAAK,EAAE;MACpCf,aAAa,EAAE,QAAQ;MACvBgB,eAAe,EAAE,SAAS;MAC1BC,OAAO,EAAE,WAAW;MACpBC,UAAU,EAAE,WAAW;MACvBpB,QAAQ,EAAE;IACZ,CAAE;IAAAqB,QAAA,gBACA7B,OAAA,CAACL,IAAI;MAAC8B,KAAK,EAAE;QAAEK,YAAY,EAAE;MAAG,CAAE;MAAAD,QAAA,eAChC7B,OAAA,CAACJ,IAAI;QAAC6B,KAAK,EAAE;UAAEjB,QAAQ,EAAE,EAAE;UAAEuB,UAAU,EAAE,MAAM;UAAEtB,KAAK,EAAE,SAAS;UAAEqB,YAAY,EAAE,EAAE;UAAEE,UAAU,EAAE;QAAI,CAAE;QAAAH,QAAA,EAAC;MAExG;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEPpC,OAAA,CAACL,IAAI;MAAC8B,KAAK,EAAE;QAAEK,YAAY,EAAE;MAAG,CAAE;MAAAD,QAAA,eAEhC7B,OAAA,CAACL,IAAI;QAAC8B,KAAK,EAAE;UAAEY,YAAY,EAAE,CAAC;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,WAAW,EAAE,CAAC;UAAEC,WAAW,EAAE,SAAS;UAAEhC,QAAQ,EAAE;QAAG,CAAE;QAAAqB,QAAA,eACzG7B,OAAA,CAACL,IAAI;UAAC8B,KAAK,EAAE;YAAEf,aAAa,EAAE,QAAQ;YAAE+B,KAAK,EAAE;UAAO,CAAE;UAAAZ,QAAA,gBACtD7B,OAAA,CAACL,IAAI;YAAC8B,KAAK,EAAE;cAAEf,aAAa,EAAE,KAAK;cAAEgC,iBAAiB,EAAE,CAAC;cAAEC,iBAAiB,EAAE;YAAU,CAAE;YAAAd,QAAA,gBACxF7B,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEI,UAAU,EAAE,MAAM;gBAAEL,eAAe,EAAE,SAAS;gBAAEe,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAE1H;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPpC,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEX,IAAI,EAAE,CAAC;gBAAER,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAEV,UAAU,CAACyB;YAAY;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjG,CAAC,eACPpC,OAAA,CAACL,IAAI;YAAC8B,KAAK,EAAE;cAAEf,aAAa,EAAE,KAAK;cAAEgC,iBAAiB,EAAE,CAAC;cAAEC,iBAAiB,EAAE;YAAU,CAAE;YAAAd,QAAA,gBACxF7B,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEI,UAAU,EAAE,MAAM;gBAAEL,eAAe,EAAE,SAAS;gBAAEe,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAE1H;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPpC,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEX,IAAI,EAAE,CAAC;gBAAER,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAEV,UAAU,CAAC0B;YAAe;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpG,CAAC,eACPpC,OAAA,CAACL,IAAI;YAAC8B,KAAK,EAAE;cAAEf,aAAa,EAAE,KAAK;cAAEgC,iBAAiB,EAAE,CAAC;cAAEC,iBAAiB,EAAE;YAAU,CAAE;YAAAd,QAAA,gBACxF7B,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEI,UAAU,EAAE,MAAM;gBAAEL,eAAe,EAAE,SAAS;gBAAEe,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAE1H;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPpC,OAAA,CAACL,IAAI;cAAC8B,KAAK,EAAE;gBAAEE,OAAO,EAAE;cAAE,CAAE;cAAAE,QAAA,eAC1B7B,OAAA,CAACL,IAAI;gBAAC8B,KAAK,EAAE;kBACXC,eAAe,EAAE,SAAS;kBAC1Bb,iBAAiB,EAAE,EAAE;kBACrBiC,eAAe,EAAE,CAAC;kBAClBT,YAAY,EAAE,EAAE;kBAChBU,SAAS,EAAE;gBACb,CAAE;gBAAAlB,QAAA,eACA7B,OAAA,CAACJ,IAAI;kBAAC6B,KAAK,EAAE;oBAAEhB,KAAK,EAAE,SAAS;oBAAED,QAAQ,EAAE,EAAE;oBAAEwB,UAAU,EAAE;kBAAI,CAAE;kBAAAH,QAAA,EAAC;gBAAY;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACPpC,OAAA,CAACL,IAAI;YAAC8B,KAAK,EAAE;cAAEf,aAAa,EAAE,KAAK;cAAEgC,iBAAiB,EAAE,CAAC;cAAEC,iBAAiB,EAAE;YAAU,CAAE;YAAAd,QAAA,gBACxF7B,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEI,UAAU,EAAE,MAAM;gBAAEL,eAAe,EAAE,SAAS;gBAAEe,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAE1H;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPpC,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEX,IAAI,EAAE,CAAC;gBAAER,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAEmB,MAAM,CAAC7B,UAAU,CAAC8B,cAAc,CAAC,IAAI,CAAC,GAAI,IAAGD,MAAM,CAAC7B,UAAU,CAAC8B,cAAc,CAAC,GAAG,CAAE,EAAC,GAAG;YAAM;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvK,CAAC,eACPpC,OAAA,CAACL,IAAI;YAAC8B,KAAK,EAAE;cAAEf,aAAa,EAAE;YAAM,CAAE;YAAAmB,QAAA,gBACpC7B,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEI,UAAU,EAAE,MAAM;gBAAEL,eAAe,EAAE,SAAS;gBAAEe,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAE1H;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPpC,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEX,IAAI,EAAE,CAAC;gBAAER,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAEV,UAAU,CAAC+B;YAAa;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEPpC,OAAA,CAACL,IAAI;MAAC8B,KAAK,EAAE;QAAEf,aAAa,EAAE,KAAK;QAAEoB,YAAY,EAAE,EAAE;QAAEqB,GAAG,EAAE;MAAG,CAAE;MAAAtB,QAAA,gBAE/D7B,OAAA,CAACL,IAAI;QAAC8B,KAAK,EAAE;UACXgB,KAAK,EAAE,KAAK;UACZf,eAAe,EAAE,SAAS;UAC1BW,YAAY,EAAE,EAAE;UAChBV,OAAO,EAAE,EAAE;UACXf,UAAU,EAAE,QAAQ;UACpBD,cAAc,EAAE,QAAQ;UACxB4B,WAAW,EAAE,CAAC;UACdC,WAAW,EAAE,SAAS;UACtBY,SAAS,EAAE;QACb,CAAE;QAAAvB,QAAA,gBAEA7B,OAAA,CAACL,IAAI;UAAC8B,KAAK,EAAE;YAAEK,YAAY,EAAE;UAAE,CAAE;UAAAD,QAAA,eAC/B7B,OAAA;YAAKyC,KAAK,EAAC,IAAI;YAACY,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAAA1B,QAAA,gBACzD7B,OAAA;cAAMwD,CAAC,EAAC,GAAG;cAACC,CAAC,EAAC,GAAG;cAAChB,KAAK,EAAC,IAAI;cAACY,MAAM,EAAC,IAAI;cAACK,EAAE,EAAC,GAAG;cAACH,IAAI,EAAC;YAAS;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eAChEpC,OAAA;cAAMwD,CAAC,EAAC,GAAG;cAACC,CAAC,EAAC,GAAG;cAAChB,KAAK,EAAC,IAAI;cAACY,MAAM,EAAC,IAAI;cAACK,EAAE,EAAC,GAAG;cAACH,IAAI,EAAC;YAAM;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eAC7DpC,OAAA;cAAMwD,CAAC,EAAC,GAAG;cAACC,CAAC,EAAC,IAAI;cAAChB,KAAK,EAAC,GAAG;cAACY,MAAM,EAAC,GAAG;cAACK,EAAE,EAAC,GAAG;cAACH,IAAI,EAAC;YAAS;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eAC/DpC,OAAA;cAAMwD,CAAC,EAAC,IAAI;cAACC,CAAC,EAAC,IAAI;cAAChB,KAAK,EAAC,GAAG;cAACY,MAAM,EAAC,GAAG;cAACK,EAAE,EAAC,GAAG;cAACH,IAAI,EAAC;YAAS;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eAChEpC,OAAA;cAAMwD,CAAC,EAAC,IAAI;cAACC,CAAC,EAAC,IAAI;cAAChB,KAAK,EAAC,GAAG;cAACY,MAAM,EAAC,GAAG;cAACK,EAAE,EAAC,GAAG;cAACH,IAAI,EAAC;YAAS;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPpC,OAAA,CAACJ,IAAI;UAAC6B,KAAK,EAAE;YAAEM,UAAU,EAAE,MAAM;YAAEtB,KAAK,EAAE,SAAS;YAAED,QAAQ,EAAE,EAAE;YAAEsB,YAAY,EAAE,CAAC;YAAEE,UAAU,EAAE;UAAI,CAAE;UAAAH,QAAA,EAAC;QAEvG;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPpC,OAAA,CAACJ,IAAI;UAAC6B,KAAK,EAAE;YAAEM,UAAU,EAAE,MAAM;YAAEvB,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEuB,UAAU,EAAE;UAAI,CAAE;UAAAH,QAAA,EAClFV,UAAU,CAACwC;QAAY;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPpC,OAAA,CAACL,IAAI;QAAC8B,KAAK,EAAE;UACXgB,KAAK,EAAE,KAAK;UACZf,eAAe,EAAE,SAAS;UAC1BW,YAAY,EAAE,EAAE;UAChBV,OAAO,EAAE,EAAE;UACXf,UAAU,EAAE,QAAQ;UACpBD,cAAc,EAAE,QAAQ;UACxB4B,WAAW,EAAE,CAAC;UACdC,WAAW,EAAE,SAAS;UACtBY,SAAS,EAAE;QACb,CAAE;QAAAvB,QAAA,gBAEA7B,OAAA,CAACL,IAAI;UAAC8B,KAAK,EAAE;YAAEK,YAAY,EAAE;UAAE,CAAE;UAAAD,QAAA,eAC/B7B,OAAA;YAAKyC,KAAK,EAAC,IAAI;YAACY,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAAA1B,QAAA,gBACzD7B,OAAA;cAAQ4D,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,GAAG;cAACC,CAAC,EAAC,GAAG;cAACP,IAAI,EAAC;YAAS;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eAC7CpC,OAAA;cAAMwD,CAAC,EAAC,GAAG;cAACC,CAAC,EAAC,IAAI;cAAChB,KAAK,EAAC,IAAI;cAACY,MAAM,EAAC,GAAG;cAACK,EAAE,EAAC,KAAK;cAACH,IAAI,EAAC;YAAS;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPpC,OAAA,CAACJ,IAAI;UAAC6B,KAAK,EAAE;YAAEM,UAAU,EAAE,MAAM;YAAEtB,KAAK,EAAE,SAAS;YAAED,QAAQ,EAAE,EAAE;YAAEsB,YAAY,EAAE,CAAC;YAAEE,UAAU,EAAE;UAAI,CAAE;UAAAH,QAAA,EAAC;QAEvG;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPpC,OAAA,CAACJ,IAAI;UAAC6B,KAAK,EAAE;YAAEM,UAAU,EAAE,MAAM;YAAEvB,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEqB,YAAY,EAAE,CAAC;YAAEE,UAAU,EAAE;UAAI,CAAE;UAAAH,QAAA,EACnGV,UAAU,CAAC4C;QAAS;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACPpC,OAAA,CAACL,IAAI;UAAC8B,KAAK,EAAE;YACXC,eAAe,EAAE,SAAS;YAC1Bb,iBAAiB,EAAE,EAAE;YACrBiC,eAAe,EAAE,CAAC;YAClBT,YAAY,EAAE,EAAE;YAChB2B,SAAS,EAAE,CAAC;YACZjB,SAAS,EAAE;UACb,CAAE;UAAAlB,QAAA,eACA7B,OAAA,CAACJ,IAAI;YAAC6B,KAAK,EAAE;cAAEhB,KAAK,EAAE,SAAS;cAAED,QAAQ,EAAE,EAAE;cAAEwB,UAAU,EAAE;YAAI,CAAE;YAAAH,QAAA,EAAC;UAAI;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEPpC,OAAA,CAACL,IAAI;MAAC8B,KAAK,EAAE;QAAEK,YAAY,EAAE;MAAG,CAAE;MAAAD,QAAA,gBAChC7B,OAAA,CAACL,IAAI;QAAC8B,KAAK,EAAE;UAAEf,aAAa,EAAE,KAAK;UAAEE,UAAU,EAAE,QAAQ;UAAEkB,YAAY,EAAE;QAAE,CAAE;QAAAD,QAAA,eAC3E7B,OAAA,CAACJ,IAAI;UAAC6B,KAAK,EAAE;YAAEM,UAAU,EAAE,MAAM;YAAEtB,KAAK,EAAE,SAAS;YAAED,QAAQ,EAAE,EAAE;YAAEwB,UAAU,EAAE;UAAI,CAAE;UAAAH,QAAA,EAAC;QAEtF;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPpC,OAAA,CAACL,IAAI;QAAC8B,KAAK,EAAE;UAAEY,YAAY,EAAE,CAAC;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,WAAW,EAAE,CAAC;UAAEC,WAAW,EAAE,SAAS;UAAEhC,QAAQ,EAAE;QAAG,CAAE;QAAAqB,QAAA,eACzG7B,OAAA,CAACL,IAAI;UAAC8B,KAAK,EAAE;YAAEf,aAAa,EAAE,QAAQ;YAAE+B,KAAK,EAAE;UAAO,CAAE;UAAAZ,QAAA,gBACtD7B,OAAA,CAACL,IAAI;YAAC8B,KAAK,EAAE;cAAEf,aAAa,EAAE,KAAK;cAAEgB,eAAe,EAAE,SAAS;cAAEgB,iBAAiB,EAAE,CAAC;cAAEC,iBAAiB,EAAE;YAAU,CAAE;YAAAd,QAAA,gBACpH7B,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEZ,SAAS,EAAE,MAAM;gBAAEgB,UAAU,EAAE,MAAM;gBAAEU,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5HpC,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEZ,SAAS,EAAE,MAAM;gBAAEgB,UAAU,EAAE,MAAM;gBAAEU,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5HpC,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEZ,SAAS,EAAE,MAAM;gBAAEgB,UAAU,EAAE,MAAM;gBAAEU,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnIpC,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEZ,SAAS,EAAE,MAAM;gBAAEgB,UAAU,EAAE,MAAM;gBAAEU,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxH,CAAC,eACPpC,OAAA,CAACL,IAAI;YAAC8B,KAAK,EAAE;cAAEf,aAAa,EAAE,KAAK;cAAEgC,iBAAiB,EAAE,CAAC;cAAEC,iBAAiB,EAAE;YAAU,CAAE;YAAAd,QAAA,gBACxF7B,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEI,UAAU,EAAE,MAAM;gBAAEU,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChHpC,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAElB,KAAK,EAAE,SAAS;gBAAEgC,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAEV,UAAU,CAAC8C;YAAW;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3HpC,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEI,UAAU,EAAE,MAAM;gBAAEtB,KAAK,EAAE,SAAS;gBAAEgC,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAAS;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChIpC,OAAA,CAACL,IAAI;cAAC8B,KAAK,EAAE;gBAAEE,OAAO,EAAE;cAAE,CAAE;cAAAE,QAAA,eAC1B7B,OAAA,CAACL,IAAI;gBAAC8B,KAAK,EAAE;kBAAEC,eAAe,EAAE,SAAS;kBAAEb,iBAAiB,EAAE,EAAE;kBAAEiC,eAAe,EAAE,CAAC;kBAAET,YAAY,EAAE,CAAC;kBAAEU,SAAS,EAAE;gBAAa,CAAE;gBAAAlB,QAAA,eAC/H7B,OAAA,CAACJ,IAAI;kBAAC6B,KAAK,EAAE;oBAAEhB,KAAK,EAAE,SAAS;oBAAED,QAAQ,EAAE,EAAE;oBAAEwB,UAAU,EAAE;kBAAI,CAAE;kBAAAH,QAAA,EAAEV,UAAU,CAACwC;gBAAY;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACPpC,OAAA,CAACL,IAAI;YAAC8B,KAAK,EAAE;cAAEf,aAAa,EAAE,KAAK;cAAEgC,iBAAiB,EAAE,CAAC;cAAEC,iBAAiB,EAAE;YAAU,CAAE;YAAAd,QAAA,gBACxF7B,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEI,UAAU,EAAE,MAAM;gBAAEU,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChHpC,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAElB,KAAK,EAAE,SAAS;gBAAEgC,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAEV,UAAU,CAAC+C;YAAW;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3HpC,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEI,UAAU,EAAE,MAAM;gBAAEtB,KAAK,EAAE,SAAS;gBAAEgC,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrIpC,OAAA,CAACL,IAAI;cAAC8B,KAAK,EAAE;gBAAEE,OAAO,EAAE;cAAE,CAAE;cAAAE,QAAA,eAC1B7B,OAAA,CAACL,IAAI;gBAAC8B,KAAK,EAAE;kBAAEC,eAAe,EAAE,SAAS;kBAAEb,iBAAiB,EAAE,EAAE;kBAAEiC,eAAe,EAAE,CAAC;kBAAET,YAAY,EAAE,CAAC;kBAAEU,SAAS,EAAE;gBAAa,CAAE;gBAAAlB,QAAA,eAC/H7B,OAAA,CAACJ,IAAI;kBAAC6B,KAAK,EAAE;oBAAEhB,KAAK,EAAE,SAAS;oBAAED,QAAQ,EAAE,EAAE;oBAAEwB,UAAU,EAAE;kBAAI,CAAE;kBAAAH,QAAA,EAAEV,UAAU,CAACwC;gBAAY;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACPpC,OAAA,CAACL,IAAI;YAAC8B,KAAK,EAAE;cAAEf,aAAa,EAAE;YAAM,CAAE;YAAAmB,QAAA,gBACpC7B,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEI,UAAU,EAAE,MAAM;gBAAEU,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChHpC,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAElB,KAAK,EAAE,SAAS;gBAAEgC,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAEV,UAAU,CAACgD;YAAW;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3HpC,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEI,UAAU,EAAE,MAAM;gBAAEtB,KAAK,EAAE,SAAS;gBAAEgC,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9HpC,OAAA,CAACL,IAAI;cAAC8B,KAAK,EAAE;gBAAEE,OAAO,EAAE;cAAE,CAAE;cAAAE,QAAA,eAC1B7B,OAAA,CAACL,IAAI;gBAAC8B,KAAK,EAAE;kBAAEC,eAAe,EAAE,SAAS;kBAAEb,iBAAiB,EAAE,EAAE;kBAAEiC,eAAe,EAAE,CAAC;kBAAET,YAAY,EAAE,CAAC;kBAAEU,SAAS,EAAE;gBAAa,CAAE;gBAAAlB,QAAA,eAC/H7B,OAAA,CAACJ,IAAI;kBAAC6B,KAAK,EAAE;oBAAEhB,KAAK,EAAE,SAAS;oBAAED,QAAQ,EAAE,EAAE;oBAAEwB,UAAU,EAAE;kBAAI,CAAE;kBAAAH,QAAA,EAAEV,UAAU,CAACwC;gBAAY;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEPpC,OAAA,CAACL,IAAI;MAAAkC,QAAA,gBACH7B,OAAA,CAACL,IAAI;QAAC8B,KAAK,EAAE;UAAEf,aAAa,EAAE,KAAK;UAAEE,UAAU,EAAE,QAAQ;UAAEkB,YAAY,EAAE;QAAE,CAAE;QAAAD,QAAA,eAC3E7B,OAAA,CAACJ,IAAI;UAAC6B,KAAK,EAAE;YAAEM,UAAU,EAAE,MAAM;YAAEtB,KAAK,EAAE,SAAS;YAAED,QAAQ,EAAE,EAAE;YAAEwB,UAAU,EAAE;UAAI,CAAE;UAAAH,QAAA,EAAC;QAEtF;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPpC,OAAA,CAACL,IAAI;QAAC8B,KAAK,EAAE;UAAEY,YAAY,EAAE,CAAC;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,WAAW,EAAE,CAAC;UAAEC,WAAW,EAAE,SAAS;UAAEhC,QAAQ,EAAE;QAAG,CAAE;QAAAqB,QAAA,eACzG7B,OAAA,CAACL,IAAI;UAAC8B,KAAK,EAAE;YAAEf,aAAa,EAAE,QAAQ;YAAE+B,KAAK,EAAE;UAAO,CAAE;UAAAZ,QAAA,gBACtD7B,OAAA,CAACL,IAAI;YAAC8B,KAAK,EAAE;cAAEf,aAAa,EAAE,KAAK;cAAEgB,eAAe,EAAE,SAAS;cAAEgB,iBAAiB,EAAE,CAAC;cAAEC,iBAAiB,EAAE;YAAU,CAAE;YAAAd,QAAA,gBACpH7B,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEZ,SAAS,EAAE,MAAM;gBAAEgB,UAAU,EAAE,MAAM;gBAAEU,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/HpC,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEZ,SAAS,EAAE,MAAM;gBAAEgB,UAAU,EAAE,MAAM;gBAAEU,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5HpC,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEZ,SAAS,EAAE,MAAM;gBAAEgB,UAAU,EAAE,MAAM;gBAAEU,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9HpC,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEZ,SAAS,EAAE,MAAM;gBAAEgB,UAAU,EAAE,MAAM;gBAAEU,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/H,CAAC,eACPpC,OAAA,CAACL,IAAI;YAAC8B,KAAK,EAAE;cAAEf,aAAa,EAAE;YAAM,CAAE;YAAAmB,QAAA,gBACpC7B,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEc,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAEV,UAAU,CAAC8B;YAAc;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5GpC,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEc,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAEV,UAAU,CAACwC;YAAY;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1GpC,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEc,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAEV,UAAU,CAAC8C;YAAW;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzGpC,OAAA,CAACJ,IAAI;cAAC6B,KAAK,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEc,KAAK,EAAE,KAAK;gBAAEjC,QAAQ,EAAE,EAAE;gBAAEwB,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAEV,UAAU,CAACiD,mBAAmB,GAAGjD,UAAU,CAACiD,mBAAmB,GAAIpB,MAAM,CAAC7B,UAAU,CAAC8B,cAAc,CAAC,GAAG,CAAC,GAAI,WAAU9B,UAAU,CAAC8B,cAAe,SAAQ,GAAG;YAAiB;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3P,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACPpC,OAAA,CAACL,IAAI;MAAC8B,KAAK,EAAExB,MAAM,CAACE,MAAO;MAACkE,KAAK;MAAAxC,QAAA,gBAC/B7B,OAAA,CAACJ,IAAI;QAAC6B,KAAK,EAAExB,MAAM,CAACa,UAAW;QAAAe,QAAA,EAAEV,UAAU,CAAC0B,eAAe,IAAI;MAAiB;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACxFpC,OAAA,CAACJ,IAAI;QAAC6B,KAAK,EAAExB,MAAM,CAACgB,WAAY;QAACqD,MAAM,EAAEA,CAAC;UAAEC,UAAU;UAAEC;QAAW,CAAC,KAAM,GAAED,UAAW,MAAKC,UAAW;MAAE;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxG,CAAC,eACPpC,OAAA,CAACJ,IAAI;MAAC6B,KAAK,EAAE;QAAEgD,OAAO,EAAE;MAAO,CAAE;MAACH,MAAM,EAAEA,CAAC;QAAEC;MAAW,CAAC,KAAK;QAAEjD,iBAAiB,CAAC,mBAAmB,EAAEiD,UAAU,CAAC;QAAE,OAAO,EAAE;MAAE,CAAE;MAACF,KAAK;IAAA;MAAApC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtI,CAAC;AAEX,CAAC;AAACf,EAAA,CAlNIH,qBAA2D;EAAA,QACjCpB,eAAe;AAAA;AAAA4E,EAAA,GADzCxD,qBAA2D;AAoNjE,eAAeA,qBAAqB;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}