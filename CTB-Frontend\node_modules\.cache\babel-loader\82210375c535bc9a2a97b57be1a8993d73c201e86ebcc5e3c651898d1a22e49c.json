{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\jira-old\\\\CTB-Frontend\\\\src\\\\pages\\\\automated-report\\\\components\\\\sections\\\\KeyFindingsPage.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Page, View, Text, StyleSheet } from '@react-pdf/renderer';\nimport { useSectionPages } from '../SectionPageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DEFAULT_COMPANY = \"Capture The Bug\";\nconst styles = StyleSheet.create({\n  footer: {\n    position: 'absolute',\n    bottom: 30,\n    left: 0,\n    right: 0,\n    fontSize: 10,\n    color: 'grey',\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    paddingHorizontal: 24\n  },\n  footerLeft: {\n    textAlign: 'left',\n    flex: 1\n  },\n  footerRight: {\n    textAlign: 'right',\n    flex: 1\n  }\n});\nconst KeyFindingsPage = ({\n  reportData,\n  sectionId\n}) => {\n  _s();\n  var _reportData$open_clos, _reportData$open_clos2, _reportData$open_clos3, _reportData$open_clos4, _reportData$open_clos5, _reportData$open_clos6, _reportData$open_clos7, _reportData$open_clos8, _reportData$open_clos9, _reportData$open_clos10, _reportData$open_clos11, _reportData$open_clos12;\n  const {\n    updateSectionPage\n  } = useSectionPages();\n  return /*#__PURE__*/_jsxDEV(Page, {\n    size: \"A4\",\n    id: sectionId,\n    style: {\n      flexDirection: 'column',\n      backgroundColor: '#ffffff',\n      padding: '20mm 15mm',\n      fontFamily: 'Helvetica',\n      fontSize: 12\n    },\n    children: [/*#__PURE__*/_jsxDEV(View, {\n      style: {\n        paddingHorizontal: 14,\n        paddingTop: 10,\n        flexDirection: 'column',\n        flex: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(View, {\n        style: {\n          flex: 1\n        },\n        children: [reportData.key_findings ? /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            fontSize: 12,\n            lineHeight: 1.4,\n            marginBottom: 18,\n            color: '#374151',\n            textAlign: 'justify'\n          },\n          children: reportData.key_findings\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              fontSize: 12,\n              lineHeight: 1.4,\n              marginBottom: 10,\n              color: '#374151',\n              textAlign: 'justify'\n            },\n            children: [reportData.branding_company || DEFAULT_COMPANY, \"'s thorough assessment identified \", /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                fontWeight: 'bold'\n              },\n              children: reportData.total_findings\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 101\n            }, this), \" findings, with \", /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                fontWeight: 'bold',\n                color: '#8b0000'\n              },\n              children: ((_reportData$open_clos = reportData.open_close_counts_by_severity) === null || _reportData$open_clos === void 0 ? void 0 : (_reportData$open_clos2 = _reportData$open_clos.Critical) === null || _reportData$open_clos2 === void 0 ? void 0 : _reportData$open_clos2.Total) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 188\n            }, this), \" categorized as \", /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                fontWeight: 'bold',\n                color: '#8b0000'\n              },\n              children: \"Critical Severity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 330\n            }, this), \", \", /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                fontWeight: 'bold',\n                color: '#ff4500'\n              },\n              children: ((_reportData$open_clos3 = reportData.open_close_counts_by_severity) === null || _reportData$open_clos3 === void 0 ? void 0 : (_reportData$open_clos4 = _reportData$open_clos3.High) === null || _reportData$open_clos4 === void 0 ? void 0 : _reportData$open_clos4.Total) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 411\n            }, this), \" categorized as \", /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                fontWeight: 'bold',\n                color: '#ff4500'\n              },\n              children: \"High Severity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 549\n            }, this), \", \", /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                fontWeight: 'bold',\n                color: '#ffd700'\n              },\n              children: ((_reportData$open_clos5 = reportData.open_close_counts_by_severity) === null || _reportData$open_clos5 === void 0 ? void 0 : (_reportData$open_clos6 = _reportData$open_clos5.Medium) === null || _reportData$open_clos6 === void 0 ? void 0 : _reportData$open_clos6.Total) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 626\n            }, this), \" categorized as \", /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                fontWeight: 'bold',\n                color: '#ffd700'\n              },\n              children: \"Medium Severity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 766\n            }, this), \" and \", /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                fontWeight: 'bold',\n                color: '#32cd32'\n              },\n              children: ((_reportData$open_clos7 = reportData.open_close_counts_by_severity) === null || _reportData$open_clos7 === void 0 ? void 0 : (_reportData$open_clos8 = _reportData$open_clos7.Low) === null || _reportData$open_clos8 === void 0 ? void 0 : _reportData$open_clos8.Total) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 848\n            }, this), \" as \", /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                fontWeight: 'bold',\n                color: '#32cd32'\n              },\n              children: \"Low Severity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 973\n            }, this), \". During the assessment, all critical and high vulnerabilities were reported to the \", reportData.company_name, \" team, and the client addressed the reported vulnerabilities concurrently.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              fontSize: 12,\n              lineHeight: 1.4,\n              marginBottom: 18,\n              color: '#374151',\n              textAlign: 'justify'\n            },\n            children: [reportData.branding_company || DEFAULT_COMPANY, \" team has documented the identified vulnerabilities along with their current statuses in the key findings section of this report. Prompt action is recommended to strengthen the application's overall security posture.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(View, {\n          style: {\n            borderRadius: 10,\n            borderWidth: 1,\n            borderColor: '#d1d5db',\n            marginTop: 32,\n            marginBottom: 16,\n            width: '90%',\n            alignSelf: 'center',\n            backgroundColor: '#fff',\n            overflow: 'hidden'\n          },\n          children: [/*#__PURE__*/_jsxDEV(View, {\n            style: {\n              flexDirection: 'row',\n              backgroundColor: '#2563eb',\n              borderTopLeftRadius: 10,\n              borderTopRightRadius: 10\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 14,\n                textAlign: 'left',\n                fontWeight: 'bold',\n                width: '25%',\n                color: '#fff',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"Severity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 14,\n                textAlign: 'center',\n                fontWeight: 'bold',\n                width: '25%',\n                color: '#fff',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"Open\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 14,\n                textAlign: 'center',\n                fontWeight: 'bold',\n                width: '25%',\n                color: '#fff',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"Closed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 14,\n                textAlign: 'center',\n                fontWeight: 'bold',\n                width: '25%',\n                color: '#fff',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"Total\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), [{\n            label: 'Critical',\n            color: '#dc2626',\n            bg: '#fef2f2',\n            value: '!',\n            pillBg: '#dc2626',\n            pillColor: '#fff',\n            data: ((_reportData$open_clos9 = reportData.open_close_counts_by_severity) === null || _reportData$open_clos9 === void 0 ? void 0 : _reportData$open_clos9.Critical) || {}\n          }, {\n            label: 'High',\n            color: '#ea580c',\n            bg: '#fff7ed',\n            value: 'H',\n            pillBg: '#ea580c',\n            pillColor: '#fff',\n            data: ((_reportData$open_clos10 = reportData.open_close_counts_by_severity) === null || _reportData$open_clos10 === void 0 ? void 0 : _reportData$open_clos10.High) || {}\n          }, {\n            label: 'Medium',\n            color: '#eab308',\n            bg: '#fef9c3',\n            value: 'M',\n            pillBg: '#eab308',\n            pillColor: '#fff',\n            data: ((_reportData$open_clos11 = reportData.open_close_counts_by_severity) === null || _reportData$open_clos11 === void 0 ? void 0 : _reportData$open_clos11.Medium) || {}\n          }, {\n            label: 'Low',\n            color: '#16a34a',\n            bg: '#dcfce7',\n            value: 'L',\n            pillBg: '#16a34a',\n            pillColor: '#fff',\n            data: ((_reportData$open_clos12 = reportData.open_close_counts_by_severity) === null || _reportData$open_clos12 === void 0 ? void 0 : _reportData$open_clos12.Low) || {}\n          }].map((row, idx) => /*#__PURE__*/_jsxDEV(View, {\n            style: {\n              flexDirection: 'row',\n              backgroundColor: row.bg,\n              borderTopWidth: idx === 0 ? 0 : 1,\n              borderTopColor: '#e5e7eb'\n            },\n            children: [/*#__PURE__*/_jsxDEV(View, {\n              style: {\n                padding: 12,\n                width: '25%',\n                flexDirection: 'row',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(View, {\n                style: {\n                  minWidth: 22,\n                  minHeight: 22,\n                  backgroundColor: row.pillBg,\n                  borderRadius: 11,\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  marginRight: 8\n                },\n                children: /*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    color: row.pillColor,\n                    fontWeight: 'bold',\n                    fontSize: 10,\n                    lineHeight: 1.4\n                  },\n                  children: row.value\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                style: {\n                  fontWeight: '600',\n                  color: row.color,\n                  fontSize: 12,\n                  lineHeight: 1.4\n                },\n                children: row.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 12,\n                width: '25%',\n                textAlign: 'center',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: row.data.Open || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 12,\n                width: '25%',\n                textAlign: 'center',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: row.data.Closed || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 12,\n                width: '25%',\n                textAlign: 'center',\n                fontWeight: 'bold',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: row.data.Total || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this)]\n          }, row.label, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this)), /*#__PURE__*/_jsxDEV(View, {\n            style: {\n              flexDirection: 'row',\n              backgroundColor: '#dbeafe',\n              borderTopWidth: 1,\n              borderTopColor: '#93c5fd'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 12,\n                width: '25%',\n                fontWeight: 'bold',\n                color: '#2563eb',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: \"TOTAL\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 12,\n                width: '25%',\n                fontWeight: 'bold',\n                textAlign: 'center',\n                color: '#2563eb',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: reportData.total_open || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 12,\n                width: '25%',\n                fontWeight: 'bold',\n                textAlign: 'center',\n                color: '#2563eb',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: reportData.total_closed || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                padding: 12,\n                width: '25%',\n                fontWeight: 'bold',\n                textAlign: 'center',\n                color: '#2563eb',\n                fontSize: 12,\n                lineHeight: 1.4\n              },\n              children: reportData.total_findings || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            fontSize: 12,\n            color: '#64748b',\n            textAlign: 'center',\n            marginTop: 8,\n            lineHeight: 1.4\n          },\n          children: \"Table 1: Scope of Work\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(View, {\n      style: styles.footer,\n      fixed: true,\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        style: styles.footerLeft,\n        children: reportData.document_number || 'Document Number'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        style: styles.footerRight,\n        render: ({\n          pageNumber,\n          totalPages\n        }) => `${pageNumber} / ${totalPages}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      style: {\n        display: 'none'\n      },\n      render: ({\n        pageNumber\n      }) => {\n        updateSectionPage('KeyFindings', pageNumber);\n        return '';\n      },\n      fixed: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(KeyFindingsPage, \"3rf3HaIqG9R7of1rm8wX9C3kpH0=\", false, function () {\n  return [useSectionPages];\n});\n_c = KeyFindingsPage;\nexport default KeyFindingsPage;\nvar _c;\n$RefreshReg$(_c, \"KeyFindingsPage\");", "map": {"version": 3, "names": ["React", "Page", "View", "Text", "StyleSheet", "useSectionPages", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DEFAULT_COMPANY", "styles", "create", "footer", "position", "bottom", "left", "right", "fontSize", "color", "flexDirection", "justifyContent", "alignItems", "paddingHorizontal", "footerLeft", "textAlign", "flex", "footerRight", "KeyFindingsPage", "reportData", "sectionId", "_s", "_reportData$open_clos", "_reportData$open_clos2", "_reportData$open_clos3", "_reportData$open_clos4", "_reportData$open_clos5", "_reportData$open_clos6", "_reportData$open_clos7", "_reportData$open_clos8", "_reportData$open_clos9", "_reportData$open_clos10", "_reportData$open_clos11", "_reportData$open_clos12", "updateSectionPage", "size", "id", "style", "backgroundColor", "padding", "fontFamily", "children", "paddingTop", "key_findings", "lineHeight", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "branding_company", "fontWeight", "total_findings", "open_close_counts_by_severity", "Critical", "Total", "High", "Medium", "Low", "company_name", "borderRadius", "borderWidth", "borderColor", "marginTop", "width", "alignSelf", "overflow", "borderTopLeftRadius", "borderTopRightRadius", "label", "bg", "value", "pillBg", "pillColor", "data", "map", "row", "idx", "borderTopWidth", "borderTopColor", "min<PERSON><PERSON><PERSON>", "minHeight", "marginRight", "Open", "Closed", "total_open", "total_closed", "fixed", "document_number", "render", "pageNumber", "totalPages", "display", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/jira-old/CTB-Frontend/src/pages/automated-report/components/sections/KeyFindingsPage.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Page, View, Text, StyleSheet } from '@react-pdf/renderer';\r\nimport { ReportData } from '../../types/report.types';\r\nimport { useSectionPages } from '../SectionPageContext';\r\n\r\ninterface KeyFindingsPageProps {\r\n  reportData: ReportData;\r\n  sectionId?: string;\r\n}\r\n\r\nconst DEFAULT_COMPANY = \"Capture The Bug\";\r\n\r\nconst styles = StyleSheet.create({\r\n  footer: {\r\n    position: 'absolute',\r\n    bottom: 30,\r\n    left: 0,\r\n    right: 0,\r\n    fontSize: 10,\r\n    color: 'grey',\r\n    flexDirection: 'row',\r\n    justifyContent: 'space-between',\r\n    alignItems: 'center',\r\n    paddingHorizontal: 24,\r\n  },\r\n  footerLeft: {\r\n    textAlign: 'left',\r\n    flex: 1,\r\n  },\r\n  footerRight: {\r\n    textAlign: 'right',\r\n    flex: 1,\r\n  },\r\n});\r\n\r\nconst KeyFindingsPage: React.FC<KeyFindingsPageProps> = ({ reportData, sectionId }) => {\r\n  const { updateSectionPage } = useSectionPages();\r\n  return (\r\n    <Page size=\"A4\" id={sectionId} style={{\r\n      flexDirection: 'column',\r\n      backgroundColor: '#ffffff',\r\n      padding: '20mm 15mm',\r\n      fontFamily: 'Helvetica',\r\n      fontSize: 12,\r\n    }}>\r\n      <View style={{ paddingHorizontal: 14, paddingTop: 10, flexDirection: 'column', flex: 1 }}>\r\n        <View style={{ flex: 1}}>\r\n          {/* No title here, just two paragraphs */}\r\n          {reportData.key_findings ? (\r\n            <Text style={{ fontSize: 12, lineHeight: 1.4, marginBottom: 18, color: '#374151', textAlign: 'justify' }}>{reportData.key_findings}</Text>\r\n          ) : (\r\n            <>\r\n              <Text style={{ fontSize: 12, lineHeight: 1.4, marginBottom: 10, color: '#374151', textAlign: 'justify' }}>\r\n                {(reportData.branding_company || DEFAULT_COMPANY)}'s thorough assessment identified <Text style={{ fontWeight: 'bold' }}>{reportData.total_findings}</Text> findings, with <Text style={{ fontWeight: 'bold', color: '#8b0000' }}>{reportData.open_close_counts_by_severity?.Critical?.Total || 0}</Text> categorized as <Text style={{ fontWeight: 'bold', color: '#8b0000' }}>Critical Severity</Text>, <Text style={{ fontWeight: 'bold', color: '#ff4500' }}>{reportData.open_close_counts_by_severity?.High?.Total || 0}</Text> categorized as <Text style={{ fontWeight: 'bold', color: '#ff4500' }}>High Severity</Text>, <Text style={{ fontWeight: 'bold', color: '#ffd700' }}>{reportData.open_close_counts_by_severity?.Medium?.Total || 0}</Text> categorized as <Text style={{ fontWeight: 'bold', color: '#ffd700' }}>Medium Severity</Text> and <Text style={{ fontWeight: 'bold', color: '#32cd32' }}>{reportData.open_close_counts_by_severity?.Low?.Total || 0}</Text> as <Text style={{ fontWeight: 'bold', color: '#32cd32' }}>Low Severity</Text>. During the assessment, all critical and high vulnerabilities were reported to the {reportData.company_name} team, and the client addressed the reported vulnerabilities concurrently.\r\n              </Text>\r\n              <Text style={{ fontSize: 12, lineHeight: 1.4, marginBottom: 18, color: '#374151', textAlign: 'justify' }}>\r\n              {(reportData.branding_company || DEFAULT_COMPANY)} team has documented the identified vulnerabilities along with their current statuses in the key findings section of this report. Prompt action is recommended to strengthen the application's overall security posture.\r\n              </Text>\r\n            </>\r\n          )}\r\n          <View style={{\r\n            borderRadius: 10,\r\n            borderWidth: 1,\r\n            borderColor: '#d1d5db',\r\n            marginTop: 32,\r\n            marginBottom: 16,\r\n            width: '90%',\r\n            alignSelf: 'center',\r\n            backgroundColor: '#fff',\r\n            overflow: 'hidden',\r\n          }}>\r\n            <View style={{\r\n              flexDirection: 'row',\r\n              backgroundColor: '#2563eb',\r\n              borderTopLeftRadius: 10,\r\n              borderTopRightRadius: 10,\r\n            }}>\r\n              <Text style={{\r\n                padding: 14,\r\n                textAlign: 'left',\r\n                fontWeight: 'bold',\r\n                width: '25%',\r\n                color: '#fff',\r\n                fontSize: 12,\r\n                lineHeight: 1.4,\r\n              }}>Severity</Text>\r\n              <Text style={{\r\n                padding: 14,\r\n                textAlign: 'center',\r\n                fontWeight: 'bold',\r\n                width: '25%',\r\n                color: '#fff',\r\n                fontSize: 12,\r\n                lineHeight: 1.4,\r\n              }}>Open</Text>\r\n              <Text style={{\r\n                padding: 14,\r\n                textAlign: 'center',\r\n                fontWeight: 'bold',\r\n                width: '25%',\r\n                color: '#fff',\r\n                fontSize: 12,\r\n                lineHeight: 1.4,\r\n              }}>Closed</Text>\r\n              <Text style={{\r\n                padding: 14,\r\n                textAlign: 'center',\r\n                fontWeight: 'bold',\r\n                width: '25%',\r\n                color: '#fff',\r\n                fontSize: 12,\r\n                lineHeight: 1.4,\r\n              }}>Total</Text>\r\n            </View>\r\n            {/* Table Rows */}\r\n            {[\r\n              {\r\n                label: 'Critical',\r\n                color: '#dc2626',\r\n                bg: '#fef2f2',\r\n                value: '!',\r\n                pillBg: '#dc2626',\r\n                pillColor: '#fff',\r\n                data: reportData.open_close_counts_by_severity?.Critical || {},\r\n              },\r\n              {\r\n                label: 'High',\r\n                color: '#ea580c',\r\n                bg: '#fff7ed',\r\n                value: 'H',\r\n                pillBg: '#ea580c',\r\n                pillColor: '#fff',\r\n                data: reportData.open_close_counts_by_severity?.High || {},\r\n              },\r\n              {\r\n                label: 'Medium',\r\n                color: '#eab308',\r\n                bg: '#fef9c3',\r\n                value: 'M',\r\n                pillBg: '#eab308',\r\n                pillColor: '#fff',\r\n                data: reportData.open_close_counts_by_severity?.Medium || {},\r\n              },\r\n              {\r\n                label: 'Low',\r\n                color: '#16a34a',\r\n                bg: '#dcfce7',\r\n                value: 'L',\r\n                pillBg: '#16a34a',\r\n                pillColor: '#fff',\r\n                data: reportData.open_close_counts_by_severity?.Low || {},\r\n              },\r\n            ].map((row, idx) => (\r\n              <View key={row.label} style={{\r\n                flexDirection: 'row',\r\n                backgroundColor: row.bg,\r\n                borderTopWidth: idx === 0 ? 0 : 1,\r\n                borderTopColor: '#e5e7eb',\r\n              }}>\r\n                <View style={{\r\n                  padding: 12,\r\n                  width: '25%',\r\n                  flexDirection: 'row',\r\n                  alignItems: 'center',\r\n                }}>\r\n                  <View style={{\r\n                    minWidth: 22,\r\n                    minHeight: 22,\r\n                    backgroundColor: row.pillBg,\r\n                    borderRadius: 11,\r\n                    alignItems: 'center',\r\n                    justifyContent: 'center',\r\n                    marginRight: 8,\r\n                  }}>\r\n                    <Text style={{\r\n                      color: row.pillColor,\r\n                      fontWeight: 'bold',\r\n                      fontSize: 10,\r\n                      lineHeight: 1.4,\r\n                    }}>{row.value}</Text>\r\n                  </View>\r\n                  <Text style={{\r\n                    fontWeight: '600',\r\n                    color: row.color,\r\n                    fontSize: 12,\r\n                    lineHeight: 1.4,\r\n                  }}>{row.label}</Text>\r\n                </View>\r\n                <Text style={{\r\n                  padding: 12,\r\n                  width: '25%',\r\n                  textAlign: 'center',\r\n                  fontSize: 12,\r\n                  lineHeight: 1.4,\r\n                }}>{row.data.Open || 0}</Text>\r\n                <Text style={{\r\n                  padding: 12,\r\n                  width: '25%',\r\n                  textAlign: 'center',\r\n                  fontSize: 12,\r\n                  lineHeight: 1.4,\r\n                }}>{row.data.Closed || 0}</Text>\r\n                <Text style={{\r\n                  padding: 12,\r\n                  width: '25%',\r\n                  textAlign: 'center',\r\n                  fontWeight: 'bold',\r\n                  fontSize: 12,\r\n                  lineHeight: 1.4,\r\n                }}>{row.data.Total || 0}</Text>\r\n              </View>\r\n            ))}\r\n            {/* Total Row */}\r\n            <View style={{\r\n              flexDirection: 'row',\r\n              backgroundColor: '#dbeafe',\r\n              borderTopWidth: 1,\r\n              borderTopColor: '#93c5fd',\r\n            }}>\r\n              <Text style={{\r\n                padding: 12,\r\n                width: '25%',\r\n                fontWeight: 'bold',\r\n                color: '#2563eb',\r\n                fontSize: 12,\r\n                lineHeight: 1.4,\r\n              }}>TOTAL</Text>\r\n              <Text style={{\r\n                padding: 12,\r\n                width: '25%',\r\n                fontWeight: 'bold',\r\n                textAlign: 'center',\r\n                color: '#2563eb',\r\n                fontSize: 12,\r\n                lineHeight: 1.4,\r\n              }}>{reportData.total_open || 0}</Text>\r\n              <Text style={{\r\n                padding: 12,\r\n                width: '25%',\r\n                fontWeight: 'bold',\r\n                textAlign: 'center',\r\n                color: '#2563eb',\r\n                fontSize: 12,\r\n                lineHeight: 1.4,\r\n              }}>{reportData.total_closed || 0}</Text>\r\n              <Text style={{\r\n                padding: 12,\r\n                width: '25%',\r\n                fontWeight: 'bold',\r\n                textAlign: 'center',\r\n                color: '#2563eb',\r\n                fontSize: 12,\r\n                lineHeight: 1.4,\r\n              }}>{reportData.total_findings || 0}</Text>\r\n            </View>\r\n          </View>\r\n          <Text style={{ fontSize: 12, color: '#64748b', textAlign: 'center', marginTop: 8, lineHeight: 1.4 }}>\r\n            Table 1: Scope of Work\r\n          </Text>\r\n        </View>\r\n      </View>\r\n      <View style={styles.footer} fixed>\r\n        <Text style={styles.footerLeft}>{reportData.document_number || 'Document Number'}</Text>\r\n        <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />\r\n      </View>\r\n      <Text style={{ display: 'none' }} render={({ pageNumber }) => { updateSectionPage('KeyFindings', pageNumber); return ''; }} fixed />\r\n    </Page>\r\n  );\r\n};\r\n\r\nexport default KeyFindingsPage; "], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,UAAU,QAAQ,qBAAqB;AAElE,SAASC,eAAe,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAOxD,MAAMC,eAAe,GAAG,iBAAiB;AAEzC,MAAMC,MAAM,GAAGP,UAAU,CAACQ,MAAM,CAAC;EAC/BC,MAAM,EAAE;IACNC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,MAAM;IACbC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBC,iBAAiB,EAAE;EACrB,CAAC;EACDC,UAAU,EAAE;IACVC,SAAS,EAAE,MAAM;IACjBC,IAAI,EAAE;EACR,CAAC;EACDC,WAAW,EAAE;IACXF,SAAS,EAAE,OAAO;IAClBC,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AAEF,MAAME,eAA+C,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;EACrF,MAAM;IAAEC;EAAkB,CAAC,GAAGvC,eAAe,CAAC,CAAC;EAC/C,oBACEE,OAAA,CAACN,IAAI;IAAC4C,IAAI,EAAC,IAAI;IAACC,EAAE,EAAEhB,SAAU;IAACiB,KAAK,EAAE;MACpC3B,aAAa,EAAE,QAAQ;MACvB4B,eAAe,EAAE,SAAS;MAC1BC,OAAO,EAAE,WAAW;MACpBC,UAAU,EAAE,WAAW;MACvBhC,QAAQ,EAAE;IACZ,CAAE;IAAAiC,QAAA,gBACA5C,OAAA,CAACL,IAAI;MAAC6C,KAAK,EAAE;QAAExB,iBAAiB,EAAE,EAAE;QAAE6B,UAAU,EAAE,EAAE;QAAEhC,aAAa,EAAE,QAAQ;QAAEM,IAAI,EAAE;MAAE,CAAE;MAAAyB,QAAA,eACvF5C,OAAA,CAACL,IAAI;QAAC6C,KAAK,EAAE;UAAErB,IAAI,EAAE;QAAC,CAAE;QAAAyB,QAAA,GAErBtB,UAAU,CAACwB,YAAY,gBACtB9C,OAAA,CAACJ,IAAI;UAAC4C,KAAK,EAAE;YAAE7B,QAAQ,EAAE,EAAE;YAAEoC,UAAU,EAAE,GAAG;YAAEC,YAAY,EAAE,EAAE;YAAEpC,KAAK,EAAE,SAAS;YAAEM,SAAS,EAAE;UAAU,CAAE;UAAA0B,QAAA,EAAEtB,UAAU,CAACwB;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAE1IpD,OAAA,CAAAE,SAAA;UAAA0C,QAAA,gBACE5C,OAAA,CAACJ,IAAI;YAAC4C,KAAK,EAAE;cAAE7B,QAAQ,EAAE,EAAE;cAAEoC,UAAU,EAAE,GAAG;cAAEC,YAAY,EAAE,EAAE;cAAEpC,KAAK,EAAE,SAAS;cAAEM,SAAS,EAAE;YAAU,CAAE;YAAA0B,QAAA,GACrGtB,UAAU,CAAC+B,gBAAgB,IAAIlD,eAAe,EAAE,oCAAkC,eAAAH,OAAA,CAACJ,IAAI;cAAC4C,KAAK,EAAE;gBAAEc,UAAU,EAAE;cAAO,CAAE;cAAAV,QAAA,EAAEtB,UAAU,CAACiC;YAAc;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,oBAAgB,eAAApD,OAAA,CAACJ,IAAI;cAAC4C,KAAK,EAAE;gBAAEc,UAAU,EAAE,MAAM;gBAAE1C,KAAK,EAAE;cAAU,CAAE;cAAAgC,QAAA,EAAE,EAAAnB,qBAAA,GAAAH,UAAU,CAACkC,6BAA6B,cAAA/B,qBAAA,wBAAAC,sBAAA,GAAxCD,qBAAA,CAA0CgC,QAAQ,cAAA/B,sBAAA,uBAAlDA,sBAAA,CAAoDgC,KAAK,KAAI;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,oBAAgB,eAAApD,OAAA,CAACJ,IAAI;cAAC4C,KAAK,EAAE;gBAAEc,UAAU,EAAE,MAAM;gBAAE1C,KAAK,EAAE;cAAU,CAAE;cAAAgC,QAAA,EAAC;YAAiB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,MAAE,eAAApD,OAAA,CAACJ,IAAI;cAAC4C,KAAK,EAAE;gBAAEc,UAAU,EAAE,MAAM;gBAAE1C,KAAK,EAAE;cAAU,CAAE;cAAAgC,QAAA,EAAE,EAAAjB,sBAAA,GAAAL,UAAU,CAACkC,6BAA6B,cAAA7B,sBAAA,wBAAAC,sBAAA,GAAxCD,sBAAA,CAA0CgC,IAAI,cAAA/B,sBAAA,uBAA9CA,sBAAA,CAAgD8B,KAAK,KAAI;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,oBAAgB,eAAApD,OAAA,CAACJ,IAAI;cAAC4C,KAAK,EAAE;gBAAEc,UAAU,EAAE,MAAM;gBAAE1C,KAAK,EAAE;cAAU,CAAE;cAAAgC,QAAA,EAAC;YAAa;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,MAAE,eAAApD,OAAA,CAACJ,IAAI;cAAC4C,KAAK,EAAE;gBAAEc,UAAU,EAAE,MAAM;gBAAE1C,KAAK,EAAE;cAAU,CAAE;cAAAgC,QAAA,EAAE,EAAAf,sBAAA,GAAAP,UAAU,CAACkC,6BAA6B,cAAA3B,sBAAA,wBAAAC,sBAAA,GAAxCD,sBAAA,CAA0C+B,MAAM,cAAA9B,sBAAA,uBAAhDA,sBAAA,CAAkD4B,KAAK,KAAI;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,oBAAgB,eAAApD,OAAA,CAACJ,IAAI;cAAC4C,KAAK,EAAE;gBAAEc,UAAU,EAAE,MAAM;gBAAE1C,KAAK,EAAE;cAAU,CAAE;cAAAgC,QAAA,EAAC;YAAe;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,SAAK,eAAApD,OAAA,CAACJ,IAAI;cAAC4C,KAAK,EAAE;gBAAEc,UAAU,EAAE,MAAM;gBAAE1C,KAAK,EAAE;cAAU,CAAE;cAAAgC,QAAA,EAAE,EAAAb,sBAAA,GAAAT,UAAU,CAACkC,6BAA6B,cAAAzB,sBAAA,wBAAAC,sBAAA,GAAxCD,sBAAA,CAA0C8B,GAAG,cAAA7B,sBAAA,uBAA7CA,sBAAA,CAA+C0B,KAAK,KAAI;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,QAAI,eAAApD,OAAA,CAACJ,IAAI;cAAC4C,KAAK,EAAE;gBAAEc,UAAU,EAAE,MAAM;gBAAE1C,KAAK,EAAE;cAAU,CAAE;cAAAgC,QAAA,EAAC;YAAY;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,wFAAoF,EAAC9B,UAAU,CAACwC,YAAY,EAAC,4EACrnC;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPpD,OAAA,CAACJ,IAAI;YAAC4C,KAAK,EAAE;cAAE7B,QAAQ,EAAE,EAAE;cAAEoC,UAAU,EAAE,GAAG;cAAEC,YAAY,EAAE,EAAE;cAAEpC,KAAK,EAAE,SAAS;cAAEM,SAAS,EAAE;YAAU,CAAE;YAAA0B,QAAA,GACvGtB,UAAU,CAAC+B,gBAAgB,IAAIlD,eAAe,EAAE,0NAClD;UAAA;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,eACP,CACH,eACDpD,OAAA,CAACL,IAAI;UAAC6C,KAAK,EAAE;YACXuB,YAAY,EAAE,EAAE;YAChBC,WAAW,EAAE,CAAC;YACdC,WAAW,EAAE,SAAS;YACtBC,SAAS,EAAE,EAAE;YACblB,YAAY,EAAE,EAAE;YAChBmB,KAAK,EAAE,KAAK;YACZC,SAAS,EAAE,QAAQ;YACnB3B,eAAe,EAAE,MAAM;YACvB4B,QAAQ,EAAE;UACZ,CAAE;UAAAzB,QAAA,gBACA5C,OAAA,CAACL,IAAI;YAAC6C,KAAK,EAAE;cACX3B,aAAa,EAAE,KAAK;cACpB4B,eAAe,EAAE,SAAS;cAC1B6B,mBAAmB,EAAE,EAAE;cACvBC,oBAAoB,EAAE;YACxB,CAAE;YAAA3B,QAAA,gBACA5C,OAAA,CAACJ,IAAI;cAAC4C,KAAK,EAAE;gBACXE,OAAO,EAAE,EAAE;gBACXxB,SAAS,EAAE,MAAM;gBACjBoC,UAAU,EAAE,MAAM;gBAClBa,KAAK,EAAE,KAAK;gBACZvD,KAAK,EAAE,MAAM;gBACbD,QAAQ,EAAE,EAAE;gBACZoC,UAAU,EAAE;cACd,CAAE;cAAAH,QAAA,EAAC;YAAQ;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClBpD,OAAA,CAACJ,IAAI;cAAC4C,KAAK,EAAE;gBACXE,OAAO,EAAE,EAAE;gBACXxB,SAAS,EAAE,QAAQ;gBACnBoC,UAAU,EAAE,MAAM;gBAClBa,KAAK,EAAE,KAAK;gBACZvD,KAAK,EAAE,MAAM;gBACbD,QAAQ,EAAE,EAAE;gBACZoC,UAAU,EAAE;cACd,CAAE;cAAAH,QAAA,EAAC;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACdpD,OAAA,CAACJ,IAAI;cAAC4C,KAAK,EAAE;gBACXE,OAAO,EAAE,EAAE;gBACXxB,SAAS,EAAE,QAAQ;gBACnBoC,UAAU,EAAE,MAAM;gBAClBa,KAAK,EAAE,KAAK;gBACZvD,KAAK,EAAE,MAAM;gBACbD,QAAQ,EAAE,EAAE;gBACZoC,UAAU,EAAE;cACd,CAAE;cAAAH,QAAA,EAAC;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChBpD,OAAA,CAACJ,IAAI;cAAC4C,KAAK,EAAE;gBACXE,OAAO,EAAE,EAAE;gBACXxB,SAAS,EAAE,QAAQ;gBACnBoC,UAAU,EAAE,MAAM;gBAClBa,KAAK,EAAE,KAAK;gBACZvD,KAAK,EAAE,MAAM;gBACbD,QAAQ,EAAE,EAAE;gBACZoC,UAAU,EAAE;cACd,CAAE;cAAAH,QAAA,EAAC;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,EAEN,CACC;YACEoB,KAAK,EAAE,UAAU;YACjB5D,KAAK,EAAE,SAAS;YAChB6D,EAAE,EAAE,SAAS;YACbC,KAAK,EAAE,GAAG;YACVC,MAAM,EAAE,SAAS;YACjBC,SAAS,EAAE,MAAM;YACjBC,IAAI,EAAE,EAAA5C,sBAAA,GAAAX,UAAU,CAACkC,6BAA6B,cAAAvB,sBAAA,uBAAxCA,sBAAA,CAA0CwB,QAAQ,KAAI,CAAC;UAC/D,CAAC,EACD;YACEe,KAAK,EAAE,MAAM;YACb5D,KAAK,EAAE,SAAS;YAChB6D,EAAE,EAAE,SAAS;YACbC,KAAK,EAAE,GAAG;YACVC,MAAM,EAAE,SAAS;YACjBC,SAAS,EAAE,MAAM;YACjBC,IAAI,EAAE,EAAA3C,uBAAA,GAAAZ,UAAU,CAACkC,6BAA6B,cAAAtB,uBAAA,uBAAxCA,uBAAA,CAA0CyB,IAAI,KAAI,CAAC;UAC3D,CAAC,EACD;YACEa,KAAK,EAAE,QAAQ;YACf5D,KAAK,EAAE,SAAS;YAChB6D,EAAE,EAAE,SAAS;YACbC,KAAK,EAAE,GAAG;YACVC,MAAM,EAAE,SAAS;YACjBC,SAAS,EAAE,MAAM;YACjBC,IAAI,EAAE,EAAA1C,uBAAA,GAAAb,UAAU,CAACkC,6BAA6B,cAAArB,uBAAA,uBAAxCA,uBAAA,CAA0CyB,MAAM,KAAI,CAAC;UAC7D,CAAC,EACD;YACEY,KAAK,EAAE,KAAK;YACZ5D,KAAK,EAAE,SAAS;YAChB6D,EAAE,EAAE,SAAS;YACbC,KAAK,EAAE,GAAG;YACVC,MAAM,EAAE,SAAS;YACjBC,SAAS,EAAE,MAAM;YACjBC,IAAI,EAAE,EAAAzC,uBAAA,GAAAd,UAAU,CAACkC,6BAA6B,cAAApB,uBAAA,uBAAxCA,uBAAA,CAA0CyB,GAAG,KAAI,CAAC;UAC1D,CAAC,CACF,CAACiB,GAAG,CAAC,CAACC,GAAG,EAAEC,GAAG,kBACbhF,OAAA,CAACL,IAAI;YAAiB6C,KAAK,EAAE;cAC3B3B,aAAa,EAAE,KAAK;cACpB4B,eAAe,EAAEsC,GAAG,CAACN,EAAE;cACvBQ,cAAc,EAAED,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;cACjCE,cAAc,EAAE;YAClB,CAAE;YAAAtC,QAAA,gBACA5C,OAAA,CAACL,IAAI;cAAC6C,KAAK,EAAE;gBACXE,OAAO,EAAE,EAAE;gBACXyB,KAAK,EAAE,KAAK;gBACZtD,aAAa,EAAE,KAAK;gBACpBE,UAAU,EAAE;cACd,CAAE;cAAA6B,QAAA,gBACA5C,OAAA,CAACL,IAAI;gBAAC6C,KAAK,EAAE;kBACX2C,QAAQ,EAAE,EAAE;kBACZC,SAAS,EAAE,EAAE;kBACb3C,eAAe,EAAEsC,GAAG,CAACJ,MAAM;kBAC3BZ,YAAY,EAAE,EAAE;kBAChBhD,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,QAAQ;kBACxBuE,WAAW,EAAE;gBACf,CAAE;gBAAAzC,QAAA,eACA5C,OAAA,CAACJ,IAAI;kBAAC4C,KAAK,EAAE;oBACX5B,KAAK,EAAEmE,GAAG,CAACH,SAAS;oBACpBtB,UAAU,EAAE,MAAM;oBAClB3C,QAAQ,EAAE,EAAE;oBACZoC,UAAU,EAAE;kBACd,CAAE;kBAAAH,QAAA,EAAEmC,GAAG,CAACL;gBAAK;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACPpD,OAAA,CAACJ,IAAI;gBAAC4C,KAAK,EAAE;kBACXc,UAAU,EAAE,KAAK;kBACjB1C,KAAK,EAAEmE,GAAG,CAACnE,KAAK;kBAChBD,QAAQ,EAAE,EAAE;kBACZoC,UAAU,EAAE;gBACd,CAAE;gBAAAH,QAAA,EAAEmC,GAAG,CAACP;cAAK;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACPpD,OAAA,CAACJ,IAAI;cAAC4C,KAAK,EAAE;gBACXE,OAAO,EAAE,EAAE;gBACXyB,KAAK,EAAE,KAAK;gBACZjD,SAAS,EAAE,QAAQ;gBACnBP,QAAQ,EAAE,EAAE;gBACZoC,UAAU,EAAE;cACd,CAAE;cAAAH,QAAA,EAAEmC,GAAG,CAACF,IAAI,CAACS,IAAI,IAAI;YAAC;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9BpD,OAAA,CAACJ,IAAI;cAAC4C,KAAK,EAAE;gBACXE,OAAO,EAAE,EAAE;gBACXyB,KAAK,EAAE,KAAK;gBACZjD,SAAS,EAAE,QAAQ;gBACnBP,QAAQ,EAAE,EAAE;gBACZoC,UAAU,EAAE;cACd,CAAE;cAAAH,QAAA,EAAEmC,GAAG,CAACF,IAAI,CAACU,MAAM,IAAI;YAAC;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChCpD,OAAA,CAACJ,IAAI;cAAC4C,KAAK,EAAE;gBACXE,OAAO,EAAE,EAAE;gBACXyB,KAAK,EAAE,KAAK;gBACZjD,SAAS,EAAE,QAAQ;gBACnBoC,UAAU,EAAE,MAAM;gBAClB3C,QAAQ,EAAE,EAAE;gBACZoC,UAAU,EAAE;cACd,CAAE;cAAAH,QAAA,EAAEmC,GAAG,CAACF,IAAI,CAACnB,KAAK,IAAI;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAxDtB2B,GAAG,CAACP,KAAK;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyDd,CACP,CAAC,eAEFpD,OAAA,CAACL,IAAI;YAAC6C,KAAK,EAAE;cACX3B,aAAa,EAAE,KAAK;cACpB4B,eAAe,EAAE,SAAS;cAC1BwC,cAAc,EAAE,CAAC;cACjBC,cAAc,EAAE;YAClB,CAAE;YAAAtC,QAAA,gBACA5C,OAAA,CAACJ,IAAI;cAAC4C,KAAK,EAAE;gBACXE,OAAO,EAAE,EAAE;gBACXyB,KAAK,EAAE,KAAK;gBACZb,UAAU,EAAE,MAAM;gBAClB1C,KAAK,EAAE,SAAS;gBAChBD,QAAQ,EAAE,EAAE;gBACZoC,UAAU,EAAE;cACd,CAAE;cAAAH,QAAA,EAAC;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACfpD,OAAA,CAACJ,IAAI;cAAC4C,KAAK,EAAE;gBACXE,OAAO,EAAE,EAAE;gBACXyB,KAAK,EAAE,KAAK;gBACZb,UAAU,EAAE,MAAM;gBAClBpC,SAAS,EAAE,QAAQ;gBACnBN,KAAK,EAAE,SAAS;gBAChBD,QAAQ,EAAE,EAAE;gBACZoC,UAAU,EAAE;cACd,CAAE;cAAAH,QAAA,EAAEtB,UAAU,CAACkE,UAAU,IAAI;YAAC;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtCpD,OAAA,CAACJ,IAAI;cAAC4C,KAAK,EAAE;gBACXE,OAAO,EAAE,EAAE;gBACXyB,KAAK,EAAE,KAAK;gBACZb,UAAU,EAAE,MAAM;gBAClBpC,SAAS,EAAE,QAAQ;gBACnBN,KAAK,EAAE,SAAS;gBAChBD,QAAQ,EAAE,EAAE;gBACZoC,UAAU,EAAE;cACd,CAAE;cAAAH,QAAA,EAAEtB,UAAU,CAACmE,YAAY,IAAI;YAAC;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxCpD,OAAA,CAACJ,IAAI;cAAC4C,KAAK,EAAE;gBACXE,OAAO,EAAE,EAAE;gBACXyB,KAAK,EAAE,KAAK;gBACZb,UAAU,EAAE,MAAM;gBAClBpC,SAAS,EAAE,QAAQ;gBACnBN,KAAK,EAAE,SAAS;gBAChBD,QAAQ,EAAE,EAAE;gBACZoC,UAAU,EAAE;cACd,CAAE;cAAAH,QAAA,EAAEtB,UAAU,CAACiC,cAAc,IAAI;YAAC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACPpD,OAAA,CAACJ,IAAI;UAAC4C,KAAK,EAAE;YAAE7B,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEM,SAAS,EAAE,QAAQ;YAAEgD,SAAS,EAAE,CAAC;YAAEnB,UAAU,EAAE;UAAI,CAAE;UAAAH,QAAA,EAAC;QAErG;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACPpD,OAAA,CAACL,IAAI;MAAC6C,KAAK,EAAEpC,MAAM,CAACE,MAAO;MAACoF,KAAK;MAAA9C,QAAA,gBAC/B5C,OAAA,CAACJ,IAAI;QAAC4C,KAAK,EAAEpC,MAAM,CAACa,UAAW;QAAA2B,QAAA,EAAEtB,UAAU,CAACqE,eAAe,IAAI;MAAiB;QAAA1C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACxFpD,OAAA,CAACJ,IAAI;QAAC4C,KAAK,EAAEpC,MAAM,CAACgB,WAAY;QAACwE,MAAM,EAAEA,CAAC;UAAEC,UAAU;UAAEC;QAAW,CAAC,KAAM,GAAED,UAAW,MAAKC,UAAW;MAAE;QAAA7C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxG,CAAC,eACPpD,OAAA,CAACJ,IAAI;MAAC4C,KAAK,EAAE;QAAEuD,OAAO,EAAE;MAAO,CAAE;MAACH,MAAM,EAAEA,CAAC;QAAEC;MAAW,CAAC,KAAK;QAAExD,iBAAiB,CAAC,aAAa,EAAEwD,UAAU,CAAC;QAAE,OAAO,EAAE;MAAE,CAAE;MAACH,KAAK;IAAA;MAAAzC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChI,CAAC;AAEX,CAAC;AAAC5B,EAAA,CAzOIH,eAA+C;EAAA,QACrBvB,eAAe;AAAA;AAAAkG,EAAA,GADzC3E,eAA+C;AA2OrD,eAAeA,eAAe;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}