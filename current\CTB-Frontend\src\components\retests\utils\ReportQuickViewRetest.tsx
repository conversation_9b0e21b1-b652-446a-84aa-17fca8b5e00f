import { CTBReport } from "../../../utils/api/endpoints/reports/parseReports";
import QuickViewModalHalf, { QuickViewSection } from "../../modal/QuickViewModalHalf";
import Pill from "../../pills/Pill";
import ReportStatusPill from "../../reports/ReportStatusPill";
import parse from "html-react-parser";
import PrimaryButton from "../../buttons/PrimaryButton";
import { useNavigate } from "react-router-dom";
import ReportProgramBanner from "../../reports/ReportProgramBanner";
import useProgram from "../../../utils/hooks/programs/useProgram";

/**
 * A sidebar style preview of the given report (if defined)
 *
 * TODO: Integrate program information
 */
const ReportQuickViewRetest = ({
  report,
  onClose
}: {
  report?: CTBReport;
  onClose: () => void;
}) => {
  const navigate = useNavigate();
  const { program } = useProgram(report?.programId);

  const handleViewFullDetails = () => {
    if (report) {
      navigate(`/dashboard/reports/${report.id}`);
    }
  };

  return (
    <QuickViewModalHalf
      title={report?.title}
      enabled={report !== undefined}
      onClick={onClose}
      className="flex flex-col"
    >
      <section className="h-full overflow-y-auto pt-1 font-thin">
        {report && program && (
          <ReportProgramBanner
            report={report}
            program={program}
            className="mb-3"
          />
        )}

        <div className="flex flex-wrap items-center gap-2">
          {/* {report?.severity && (
            <VulnerabilityFlag
              label={
                report.severity.category +
                " " +
                " Severity"
              }
              className={`text-${getVulnerabilityColour(report.severity)}`}
              flagClassName={`fill-${getVulnerabilityColour(report.severity)}`}
            />
          )} */}

          {report?.category && (
            <Pill
              label={report.category}
              className="bg-ctb-blue-50 px-2 text-xs leading-[150%]"
            />
          )}

          {report !== undefined && (
            <ReportStatusPill
              report={report}
              className="px-2 text-xs leading-[150%]"
            />
          )}
        </div>

        {report?.description && (
          <QuickViewSection>
            <span>{parse(report.description)}</span>
          </QuickViewSection>
        )}

        {report?.instructions && (
          <QuickViewSection title="Validation Steps">
            <span className="font-light leading-[150%]">
              {parse(report.instructions)}
            </span>
          </QuickViewSection>
        )}

        {report?.impact && (
          <QuickViewSection title="Impact">
            <span className="font-light leading-[150%]">
              {parse(report.impact)}
            </span>
          </QuickViewSection>
        )}

        {report?.fix && (
          <QuickViewSection title="Recommended Fix">
            <span className="font-light leading-[150%]">
              {parse(report.fix)}
            </span>
          </QuickViewSection>
        )}
      </section>
      <PrimaryButton
        type="button"
        className="mt-10 w-full"
        onClick={handleViewFullDetails}
      >
        View Full Details
      </PrimaryButton>
    </QuickViewModalHalf>
  );
};

export default ReportQuickViewRetest;
