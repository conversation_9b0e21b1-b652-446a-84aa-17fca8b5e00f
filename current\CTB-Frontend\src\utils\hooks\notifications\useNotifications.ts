import { useEffect } from "react";
import {
  useClearNotificationsMutation,
  useGetNotificationsQuery,
  useMarkNotificationsMutation
} from "../../api/endpoints/notificationsApi";

const useNotifications = () => {
  const { data, isError, isLoading, refetch } = useGetNotificationsQuery();
  const [markNotifications] = useMarkNotificationsMutation();
  const [deleteNotifications] = useClearNotificationsMutation();

  // useEffect(() => {
  //   const id = setInterval(refetch, 10000);
  //   return () => clearInterval(id);
  // }, [refetch]);

  return {
    notifications: data,
    isError,
    isLoading,
    newNotifications: (data?.filter(n => !n.read) || []).length > 0,
    markNotifications,
    deleteAllNotifications: () =>
      deleteNotifications((data || []).map(n => n.id)),
    markAllNotifications: () => markNotifications((data || []).map(n => n.id))
  };
};

export default useNotifications;
