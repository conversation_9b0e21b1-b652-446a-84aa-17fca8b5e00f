import React from 'react';
import { ReportData, DetailedFinding } from '../../types/report.types';
import BaseFindingEditor from './BaseFindingEditor';

interface LowFindingsEditorProps {
  reportData: ReportData;
  onFindingChange: (index: number, field: keyof DetailedFinding, value: string) => void;
  onRemoveFinding: (index: number) => void;
  onAddFinding: (severity: 'Critical' | 'High' | 'Medium' | 'Low') => void;
}

const LowFindingsEditor: React.FC<LowFindingsEditorProps> = (props) => {
  return <BaseFindingEditor {...props} severity="Low" />;
};

export default LowFindingsEditor; 