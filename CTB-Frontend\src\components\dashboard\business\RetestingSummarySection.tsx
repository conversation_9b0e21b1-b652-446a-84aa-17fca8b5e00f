import React from "react";
import { motion, AnimatePresence, Variants, easeInOut } from "framer-motion";
import { format, formatDistanceToNow } from "date-fns";
import { useNavigate } from "react-router-dom";
import useUnifiedBusinessDashboard from "../../../utils/hooks/dashboard/useUnifiedBusinessDashboard";
import { FiAlertTriangle, FiCheck, FiClock, FiXCircle, FiActivity, FiCalendar, FiTrendingUp, FiTrendingDown, FiTarget, FiFileText, FiArrowRight, FiEye } from "react-icons/fi";

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.5,
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 200,
      damping: 20
    }
  }
};

const pulseAnimation = {
  scale: [1, 1.02, 1],
  transition: {
    duration: 2.5,
    repeat: Infinity,
    repeatType: "reverse" as const
  }
};

const progressBarVariants = {
  hidden: { width: 0 },
  visible: (custom: number) => ({
    width: `${custom}%`,
    transition: {
      duration: 0.8,
      ease: "easeOut"
    }
  })
};

const circleVariants: Variants = {
  hidden: { pathLength: 0 },
  visible: (custom: number) => ({
    pathLength: custom / 100,
    transition: {
      duration: 1.5,
      ease: easeInOut
    }
  })
};

// Enhanced hover animation for cards
const cardHoverAnimation = {
  rest: { 
    scale: 1,
    boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
    transition: {
      duration: 0.2,
      ease: "easeOut"
    }
  },
  hover: { 
    scale: 1.02,
    boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
    transition: {
      duration: 0.2,
      ease: "easeOut"
    }
  }
};

const RetestingSummarySection: React.FC = () => {
  const { retestSummary, loading, error } = useUnifiedBusinessDashboard();
  const navigate = useNavigate();

  // Format the date
  const formatDate = (dateString: string | null): string => {
    if (!dateString) return "No recent completions";
    try {
      const date = new Date(dateString);
      return `${format(date, "MMM d, yyyy")} • (${formatDistanceToNow(date, { addSuffix: true })})`;
    } catch (e) {
      return "Invalid date";
    }
  };

  // Calculate the percentage of each status for the bars
  const calculatePercentage = (count: number): number => {
    if (!retestSummary) return 0;
    const total = 
      retestSummary.statusCounts.pending + 
      retestSummary.statusCounts.inProgress + 
      retestSummary.statusCounts.completed;
    
    if (total === 0) return 0;
    return Math.round((count / total) * 100);
  };

  // Get trend icon and color
  const getTrendProperties = (changePercent: number) => {
    if (changePercent > 0) {
      return { 
        icon: <FiTrendingUp className="h-4 w-4" />, 
        color: "text-green-500",
        text: "Improved performance" 
      };
    } else if (changePercent < 0) {
      return { 
        icon: <FiTrendingDown className="h-4 w-4" />, 
        color: "text-red-500",
        text: "Declined performance" 
      };
    }
    return { 
      icon: <FiActivity className="h-4 w-4" />, 
      color: "text-gray-400",
      text: "Unchanged performance"
    };
  };

  // Get descriptive text for duration
  const getDurationDescription = (days: number): string => {
    if (!retestSummary?.averageDuration.days && retestSummary?.averageDuration.days !== 0) return "";
    
    if (days === 0) return "Same-day completion";
    if (days < 3) return "Quick turnaround time";
    if (days < 7) return "Average fix time";
    if (days < 14) return "Complex fix duration";
    return "Extended resolution timeline";
  };

  // Navigate to retest page with specific filter
  const navigateToRetests = (statusFilter: string) => {
    navigate(`/dashboard/retests?status=${encodeURIComponent(statusFilter)}`);
  };

  if (loading) {
    return (
      <motion.div
        className="rounded-xl border border-neutral-200/80 bg-white/95 backdrop-blur-sm p-6 shadow-lg"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="flex items-center mb-5">
          <FiActivity className="text-blue-600 h-5 w-5 mr-2" />
          <div className="h-5 w-40 bg-neutral-200/70 animate-pulse rounded-md"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[0, 1, 2].map((i) => (
            <div 
              key={i} 
              className="bg-white rounded-xl p-5 shadow-md border border-neutral-100/80 h-64"
            >
              <div className="h-4 w-32 bg-neutral-200/70 animate-pulse rounded-md mb-4"></div>
              <div className="h-40 bg-neutral-100/80 animate-pulse rounded-lg"></div>
              <div className="h-4 w-40 bg-neutral-200/70 animate-pulse rounded-md mt-4"></div>
            </div>
          ))}
        </div>
      </motion.div>
    );
  }

  if (error || !retestSummary) {
    return (
      <motion.div
        className="rounded-xl border border-red-200/70 bg-white/95 backdrop-blur-sm p-6 shadow-lg"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="flex items-center text-red-600 gap-2 mb-2">
          <FiAlertTriangle className="h-5 w-5" />
          <h2 className="text-lg font-medium">Retesting Summary Unavailable</h2>
        </div>
        <p className="text-neutral-600 text-sm">
          We couldn't load the retesting data at this time. Please try again later.
        </p>
      </motion.div>
    );
  }

  // Check if there's any retest data at all
  const hasRetestData = 
    retestSummary.statusCounts.pending > 0 || 
    retestSummary.statusCounts.inProgress > 0 || 
    retestSummary.statusCounts.completed > 0;

  // We'll always show the component, even with zero values
  // Only show a complete empty state when there's no retestSummary object at all
  
  // Status panel data
  const statusItems = [
    {
      label: "Pending",
      count: retestSummary.statusCounts.pending || 0,
      color: "bg-yellow-500",
      bgColor: "bg-yellow-100/60",
      percentage: calculatePercentage(retestSummary.statusCounts.pending || 0),
      onClick: () => navigateToRetests("Retest Requested"),
      hoverColor: "hover:bg-yellow-50",
      icon: <FiEye className="h-3.5 w-3.5 text-yellow-500" />
    },
    {
      label: "In Progress",
      count: retestSummary.statusCounts.inProgress || 0,
      color: "bg-blue-500",
      bgColor: "bg-blue-100/60",
      percentage: calculatePercentage(retestSummary.statusCounts.inProgress || 0),
      onClick: () => navigateToRetests("Retest In Process"),
      hoverColor: "hover:bg-blue-50",
      icon: <FiEye className="h-3.5 w-3.5 text-blue-500" />
    },
    {
      label: "Completed",
      count: retestSummary.statusCounts.completed || 0,
      color: "bg-emerald-500",
      bgColor: "bg-emerald-100/60",
      percentage: calculatePercentage(retestSummary.statusCounts.completed || 0),
      onClick: () => navigateToRetests("Fix Verified"),
      hoverColor: "hover:bg-emerald-50",
      icon: <FiEye className="h-3.5 w-3.5 text-emerald-500" />
    }
  ];

  // Pass rate trend properties
  const passRateTrend = getTrendProperties(retestSummary.passRate.changePercent);

  // Duration trend properties
  const durationTrend = getTrendProperties(retestSummary.averageDuration.changePercent * -1); // Inverse for duration (negative is good)

  return (
    <motion.div 
      className="rounded-xl border border-neutral-200/80 bg-white/95 backdrop-blur-sm p-6 shadow-lg"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div className="flex items-center gap-2 mb-6" variants={itemVariants}>
        <FiActivity className="text-blue-600 h-5 w-5" />
        <h2 className="text-lg font-medium text-neutral-800">Retesting Status</h2>
      </motion.div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* 1️⃣ Retest Status (Left Card) */}
        <motion.div 
          className="bg-white rounded-xl p-5 shadow-md border border-neutral-100/80 hover:shadow-lg transition-all"
          variants={itemVariants}
          initial="hidden"
          animate="visible"
          whileHover={{ 
            scale: 1.02, 
            boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)" 
          }}
          transition={{ duration: 0.2, ease: "easeOut" }}
        >
          <h3 className="text-sm font-medium text-neutral-700 mb-5 flex items-center">
            <FiClock className="mr-2 h-4 w-4 text-yellow-500" />
            Retest Status
          </h3>
          
          <div className="space-y-5 mt-2">
            {statusItems.map((item) => (
              <AnimatePresence key={item.label} mode="wait">
                <div 
                  className={`relative cursor-pointer p-2 -m-2 rounded-lg transition-all duration-200 ${item.hoverColor} group`}
                  onClick={item.onClick}
                >
                  <div className="flex justify-between mb-2 items-center">
                    <span className="text-sm font-medium text-neutral-700 flex items-center gap-1.5">
                      {item.label}
                      <span className="hidden group-hover:inline-flex items-center text-xs text-neutral-500 gap-0.5">
                        <FiArrowRight className="h-3 w-3" /> Click to view
                      </span>
                    </span>
                    <motion.span 
                      className="text-sm font-semibold pr-6"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.6, delay: 0.3 }}
                    >{item.count}</motion.span>
                  </div>
                  <div className={`w-full h-3 ${item.bgColor} rounded-full overflow-hidden group-hover:ring-1 group-hover:ring-neutral-200`}>
                    <motion.div
                      className={`h-full ${item.color} rounded-full shadow-inner`}
                      custom={item.percentage}
                      variants={progressBarVariants}
                      initial="hidden"
                      animate="visible"
                    />
                  </div>
                  
                  {/* View icon that appears on hover - repositioned */}
                  <div className="absolute right-1 top-1/2 -translate-y-6 opacity-0 group-hover:opacity-100 transition-opacity">
                    {item.icon}
                  </div>
                </div>
              </AnimatePresence>
            ))}
            
            {/* Show Fix Failed progress bar only when there are failed retests */}
            {retestSummary.resultsSummary.failed > 0 && (
              <AnimatePresence mode="wait">
                <motion.div 
                  className="relative cursor-pointer p-2 -m-2 rounded-lg transition-all duration-200 hover:bg-red-50 group"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  onClick={() => navigateToRetests("Fix Failed")}
                >
                  <div className="flex justify-between mb-2 items-center">
                    <span className="text-sm font-medium text-red-600 flex items-center gap-1.5">
                      <FiXCircle className="h-3 w-3 mr-1" />
                      Fix Failed
                      <span className="hidden group-hover:inline-flex items-center text-xs text-red-500 gap-0.5">
                        <FiArrowRight className="h-3 w-3" /> Click to view
                      </span>
                    </span>
                    <motion.span 
                      className="text-sm font-semibold text-red-600 pr-6"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.6, delay: 0.3 }}
                    >{retestSummary.resultsSummary.failed}</motion.span>
                  </div>
                  <div className="w-full h-3 bg-red-100/60 rounded-full overflow-hidden group-hover:ring-1 group-hover:ring-red-200">
                    <motion.div
                      className="h-full bg-red-500 rounded-full shadow-inner"
                      custom={100}
                      variants={progressBarVariants}
                      initial="hidden"
                      animate="visible"
                    />
                  </div>
                  
                  {/* View icon that appears on hover - repositioned */}
                  <div className="absolute right-1 top-1/2 -translate-y-6 opacity-0 group-hover:opacity-100 transition-opacity">
                    <FiEye className="h-3.5 w-3.5 text-red-500" />
                  </div>
                </motion.div>
              </AnimatePresence>
            )}
          </div>
        </motion.div>
        
        {/* 2️⃣ Pass Rate (Middle Card – Highlighted) */}
        <motion.div 
          className="bg-white rounded-xl p-5 shadow-md border border-neutral-100/80 relative overflow-hidden"
          variants={itemVariants}
          initial="hidden"
          animate="visible"
          whileHover={{ 
            scale: 1.02, 
            boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)" 
          }}
          transition={{ duration: 0.2, ease: "easeOut" }}
        >
          {/* Subtle background glow effect */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50/40 to-transparent pointer-events-none" />
          
          <h3 className="text-sm font-medium text-neutral-700 mb-5 flex items-center relative z-10">
            <FiTarget className="mr-2 h-4 w-4 text-blue-500" />
            Pass Rate
          </h3>
          
          {/* Pass Rate section - always show donut even with 0% */}
          <div className="flex flex-col items-center justify-center h-[220px] relative z-10">
            <div className="relative w-48 h-48">
              <svg className="w-full h-full" viewBox="0 0 100 100">
                {/* Background circle */}
                <circle 
                  cx="50" 
                  cy="50" 
                  r="40" 
                  fill="none" 
                  stroke="#E5E7EB" 
                  strokeWidth="8"
                />
                {/* Progress circle */}
                <motion.circle 
                  cx="50" 
                  cy="50" 
                  r="40" 
                  fill="none" 
                  stroke={retestSummary.passRate.percentage >= 70 ? "#10B981" : "#F59E0B"} 
                  strokeWidth="8"
                  strokeLinecap="round"
                  strokeDasharray="251.2"
                  strokeDashoffset="0"
                  custom={retestSummary.passRate.percentage || 0}
                  variants={circleVariants}
                  initial="hidden"
                  animate="visible"
                  transform="rotate(-90 50 50)"
                />
                {/* Inner glow */}
                <motion.circle 
                  cx="50" 
                  cy="50" 
                  r="40" 
                  fill="none" 
                  stroke={retestSummary.passRate.percentage >= 70 ? "#10B98120" : "#F59E0B20"} 
                  strokeWidth="12"
                  custom={retestSummary.passRate.percentage || 0}
                  variants={circleVariants}
                  initial="hidden"
                  animate="visible"
                  transform="rotate(-90 50 50)"
                  filter="blur(4px)"
                />
                <text
                  x="50%"
                  y="45%"
                  dy=".3em"
                  textAnchor="middle"
                  className="text-lg font-bold"
                  fill="#1F2937"
                >
                  {retestSummary.passRate.percentage || 0}%
                </text>
                <text
                  x="50%"
                  y="62%"
                  dy=".3em"
                  textAnchor="middle"
                  className="text-[10px]"
                  fill="#6B7280"
                >
                  {retestSummary.passRate.completed || 0} Completed
                </text>
              </svg>
            </div>
          </div>
        </motion.div>
        
        {/* 3️⃣ Results Summary (Right Card) */}
        <motion.div 
          className="bg-white rounded-xl p-5 shadow-md border border-neutral-100/80 hover:shadow-lg transition-all"
          variants={itemVariants}
          initial="hidden"
          animate="visible"
          whileHover={{ 
            scale: 1.02, 
            boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)" 
          }}
          transition={{ duration: 0.2, ease: "easeOut" }}
        >
          <h3 className="text-sm font-medium text-neutral-700 mb-5 flex items-center">
            <FiFileText className="mr-2 h-4 w-4 text-neutral-500" />
            Results Summary
          </h3>
          
          {/* 
            Action Needed count includes:
            1. Failed retests (which stay in "In Progress" status)
            2. Retests with special statuses like "Reopen Retest" or "Request Further Action"
            These are cases where the business needs to take some action to move the retest forward.
          */}
          <div className="grid grid-cols-3 gap-2 mb-6">
            {/* Always show passed section, even with zero */}
            <motion.div 
              className={`bg-green-50 p-3 pb-8 rounded-lg border border-green-100 flex flex-col items-center justify-start shadow-sm group hover:bg-green-100/60 transition-colors duration-200 ${
                retestSummary.resultsSummary.passed === 0 ? 'opacity-50' : ''
              } cursor-pointer relative overflow-hidden`}
              whileHover={{ scale: retestSummary.resultsSummary.passed > 0 ? 1.03 : 1, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.05)" }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1, transition: { delay: 0.0 } }}
              onClick={() => navigateToRetests("Fix Verified")}
            >
              <FiCheck className="h-5 w-5 text-green-500 mb-1" />
              <span className="text-lg font-semibold text-green-700">{retestSummary.resultsSummary.passed}</span>
              <span className="text-xs text-green-600 mb-2">Passed</span>
              
              {/* Click overlay hint at bottom with fixed positioning */}
              <div className="absolute bottom-1 left-0 right-0 flex justify-center opacity-0 group-hover:opacity-100 transition-all duration-200">
                <span className="text-[10px] bg-white/80 text-green-700 px-2 py-0.5 rounded-sm font-medium flex items-center">
                  <FiArrowRight className="h-2.5 w-2.5 mr-1" /> View all
                </span>
              </div>
            </motion.div>
            
            {/* Always show failed section, even with zero */}
            <motion.div 
              className={`bg-red-50 p-3 pb-8 rounded-lg border border-red-100 flex flex-col items-center justify-start shadow-sm group hover:bg-red-100/60 transition-colors duration-200 ${
                retestSummary.resultsSummary.failed === 0 ? 'opacity-50' : ''
              } cursor-pointer relative overflow-hidden`}
              whileHover={{ scale: retestSummary.resultsSummary.failed > 0 ? 1.03 : 1, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.05)" }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1, transition: { delay: 0.1 } }}
              onClick={() => navigateToRetests("Fix Failed")}
            >
              <FiXCircle className="h-5 w-5 text-red-500 mb-1" />
              <span className="text-lg font-semibold text-red-700">{retestSummary.resultsSummary.failed}</span>
              <span className="text-xs text-red-600 mb-2">Failed</span>
              
              {/* Click overlay hint at bottom with fixed positioning */}
              <div className="absolute bottom-1 left-0 right-0 flex justify-center opacity-0 group-hover:opacity-100 transition-all duration-200">
                <span className="text-[10px] bg-white/80 text-red-700 px-2 py-0.5 rounded-sm font-medium flex items-center">
                  <FiArrowRight className="h-2.5 w-2.5 mr-1" /> View all
                </span>
              </div>
            </motion.div>
            
            {/* Always show in progress section, renamed from action required */}
            <motion.div 
              className={`bg-yellow-50 p-3 pb-8 rounded-lg border border-yellow-100 flex flex-col items-center justify-start shadow-sm group hover:bg-yellow-100/60 transition-colors duration-200 ${
                retestSummary.statusCounts.inProgress === 0 ? 'opacity-50' : ''
              } cursor-pointer relative overflow-hidden`}
              whileHover={{ scale: retestSummary.statusCounts.inProgress > 0 ? 1.03 : 1, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.05)" }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1, transition: { delay: 0.2 } }}
              onClick={() => navigateToRetests("Retest In Process")}
            >
              <FiAlertTriangle className="h-5 w-5 text-yellow-500 mb-1" />
              <span className="text-lg font-semibold text-yellow-700">
                {retestSummary.statusCounts.inProgress}
              </span>
              <span className="text-xs text-yellow-600 mb-2">In Progress</span>
              
              {/* Click overlay hint at bottom with fixed positioning */}
              <div className="absolute bottom-1 left-0 right-0 flex justify-center opacity-0 group-hover:opacity-100 transition-all duration-200">
                <span className="text-[10px] bg-white/80 text-yellow-700 px-2 py-0.5 rounded-sm font-medium flex items-center">
                  <FiArrowRight className="h-2.5 w-2.5 mr-1" /> View all
                </span>
              </div>
            </motion.div>
          </div>
          
          <motion.div 
            className="space-y-4 text-sm pl-1"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            {retestSummary.latestCompletion && (
              <div className="flex items-start">
                <FiCalendar className="h-4 w-4 text-neutral-500 mt-0.5 mr-2.5" />
                <div>
                  <span className="text-neutral-700 font-medium">Latest Completion</span>
                  <p className="text-neutral-600">{formatDate(retestSummary.latestCompletion)}</p>
                </div>
              </div>
            )}
            
            {(retestSummary.averageDuration.days !== undefined) && (
              <div className="flex items-start">
                <FiClock className="h-4 w-4 text-neutral-500 mt-0.5 mr-2.5" />
                <div>
                  <div className="flex items-center gap-2">
                    <span className="text-neutral-700 font-medium">Avg. Duration</span>
                 
                   
                  </div>
                  <p className="text-neutral-600 flex items-center flex-wrap">
                    <span className="mr-1">{retestSummary.averageDuration.days} day{retestSummary.averageDuration.days !== 1 ? 's' : ''}</span>
                    <span className="text-xs text-neutral-500 mt-0.5">
                      ({getDurationDescription(retestSummary.averageDuration.days)})
                    </span>
                  </p>
                </div>
              </div>
            )}
          </motion.div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default RetestingSummarySection; 