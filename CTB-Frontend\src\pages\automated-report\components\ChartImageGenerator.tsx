import React, { useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';

type ChartImageGeneratorProps = {
  type: 'pie' | 'bar';
  data: any;
  options?: any;
  onImageReady: (base64: string) => void;
};

const ChartImageGenerator: React.FC<ChartImageGeneratorProps> = ({ type, data, options, onImageReady }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const ctx = canvasRef.current?.getContext('2d');
    if (!ctx) return;

    const chart = new Chart(ctx, {
      type,
      data,
      options: {
        ...options,
        animation: false,
        responsive: false,
        plugins: { legend: { display: false } },
        devicePixelRatio: 1,
      },
    });

    setTimeout(() => {
      if (canvasRef.current) {
        const base64Png = canvasRef.current.toDataURL('image/png');
        const base64Jpeg = canvasRef.current.toDataURL('image/jpeg', 0.92);
        console.log('Generated chart base64 PNG:', base64Png);
        console.log('Generated chart base64 JPEG:', base64Jpeg);
        onImageReady(base64Png);
      }
      chart.destroy();
    }, 300);

    return () => {
      chart.destroy();
    };
    // eslint-disable-next-line
  }, [type, JSON.stringify(data), JSON.stringify(options)]);

  return (
    <canvas ref={canvasRef} width={180} height={180} style={{ display: 'none' }} />
  );
};

export default ChartImageGenerator; 