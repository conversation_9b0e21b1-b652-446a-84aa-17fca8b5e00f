import { ReportQueryFilters } from "./reports/reports";
import {
  CTBReport,
  CTBReportAPIResponse,
  ReportRejectReason,
  parseReport
} from "./reports/parseReports";
import { BaseFilters } from "../../../components/filters/FilterToolBar";
import { api, createURLSearchParams } from "../api";

type PaginatedReportData = {
  reports: CTBReport[];
  results: number;
};

export type ReportFilters = {
  severity?: string[];
  status?: string[];
  type?: string;
  page?: number;
} & BaseFilters;

const reportsURL = "/v2/reports";

/**
 * The Reports API slice definition.
 *
 * Each endpoint is defined here, and is then available via the
 * generated hooks. Data fetched using these endpoints is cached
 * for any subsequent queries
 */
export const reportsApi = api.injectEndpoints({
  endpoints: builder => ({
    /**
     * Retrieve the list of paginated reports available to
     * the current user from the backend
     */
    getReports: builder.query<PaginatedReportData, ReportFilters>({
      query: filters => {
        const params = createURLSearchParams<ReportQueryFilters>({
          ...filters,
          severity: filters.severity ? filters.severity.join(",") : undefined,
          status: filters.status ? filters.status.join(",") : undefined,
          page: filters.page ? filters.page.toString() : undefined,
          pageCount: filters.limit.toString()
        });

        return {
          url: reportsURL + "?" + params.toString(),
          method: "get"
        };
      },
      transformResponse: (response: {
        data: CTBReportAPIResponse[];
        results: number;
      }) => ({
        reports: response.data.map(report => parseReport(report)),
        results: response.results || 0
      }),
      providesTags: ["Reports"]
    }),
    /**
     * Retrieve a single report from the backend
     */
    getReport: builder.query<CTBReport, number>({
      query: id => ({ url: reportsURL + "/" + id, method: "get" }),
      transformResponse: (response: CTBReportAPIResponse[]) =>
        parseReport(response[0]),
      providesTags: (_, __, id) => [
        {
          type: "Report",
          id
        }
      ]
    }),
    /**
     * Makes a request to create/update a report on the backend.
     * Only works for researchers
     */
    updateReport: builder.mutation<CTBReport, { data: FormData }>({
      query: ({ data }) => ({
        url: reportsURL,
        method: "post",
        data
      }),
      transformResponse: (response: CTBReportAPIResponse) =>
        parseReport(response),
      invalidatesTags: (report?: CTBReport) => [
        {
          type: "Report",
          id: report?.id
        },
        "Reports"
      ]
    }),
    /**
     * Submits the given report
     */
    submitReport: builder.mutation<void, number>({
      query: id => ({ url: reportsURL + "/" + id + "/submit", method: "post" }),
      invalidatesTags: (_, __, id) => [
        {
          type: "Report",
          id
        },
        "Reports"
      ]
    }),

    deleteReport: builder.mutation<void, number>({
      query: id => ({
        url: `/v2/reports/${id}`,
        method: "DELETE"
      })
    }),
    /**
     * Updates the given reports status (as a business or admin)
     */
    updateReportState: builder.mutation<
      void,
      {
        id: number;
        state:
          | "approved"
          | "rejected"
          | "closed"
          | "request info"
          | "acceptRisk"
          | "nonActionableIssue"
          | "requestFix"
          | "out of scope"
          // | "QA Disagrees"
          | "QA Modified";
        rejectReason?: ReportRejectReason;
      }
    >({
      query: ({ id, state, rejectReason }) => ({
        url: reportsURL + "/" + id + "/state",
        method: "post",
        data: {
          state,
          reason: rejectReason
        }
      }),
      invalidatesTags: (_, __, { id }) => [
        {
          type: "Report",
          id
        },
        "Reports"
      ]
    }),
    generateCertificate: builder.mutation<void, number>({
      query: id => ({
        url: reportsURL + "/certificate",
        method: "post",
        data: {
          id
        }
      }),
      invalidatesTags: (_, __, id) => [
        {
          type: "Report",
          id
        }
      ]
    }),
    getReportRecommendations: builder.query<
      { report_id: number; report_title: string; severity_category: string }[],
      number
    >({
      query: id => ({
        url: `/v2/reports/${id}/recommendations`,
        method: "get"
      }),
      transformResponse: (response: { recommendations: any[] }) =>
        response.recommendations || []
    })
  })
});

export const {
  useGetReportsQuery,
  useGetReportQuery,
  useUpdateReportMutation,
  useSubmitReportMutation,
  useUpdateReportStateMutation,
  useDeleteReportMutation,
  useGenerateCertificateMutation,
  useGetReportRecommendationsQuery
} = reportsApi;
