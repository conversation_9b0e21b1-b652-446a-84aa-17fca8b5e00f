import React, { useState, useRef, useEffect } from "react";
import { Bar } from "react-chartjs-2";
import { FaCaretDown } from "react-icons/fa6";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface Program {
  programId: number;
  programTitle: string;
  retestCounts: {
    completed: number;
    inProgress: number;
  };
}

interface RetestBarChartProps {
  programsWithRetestCounts: Program[];
  title: string;
}

const RetestBarChart: React.FC<RetestBarChartProps> = ({
  programsWithRetestCounts,
  title,
}) => {
  const [selectedPrograms, setSelectedPrograms] = useState<Program[]>([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Sort programs by descending program ID and select the top 5 by default
  useEffect(() => {
    const sortedPrograms = [...programsWithRetestCounts].sort(
      (a, b) => b.programId - a.programId
    );
    setSelectedPrograms(sortedPrograms.slice(0, 5));
  }, [programsWithRetestCounts]);

  const filteredPrograms = programsWithRetestCounts.filter((program) =>
    program.programTitle.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleProgramSelect = (program: Program) => {
    setSelectedPrograms((prev) => {
      if (prev.find((p) => p.programId === program.programId)) {
        return prev.filter((p) => p.programId !== program.programId);
      }
      if (prev.length >= 5) return prev;
      return [...prev, program];
    });
  };

  const displayData =
    selectedPrograms.length > 0
      ? selectedPrograms
      : programsWithRetestCounts.slice(0, 5);

  const chartData = {
    labels: displayData.map((program) => program.programTitle),
    datasets: [
      {
        label: "Completed",
        data: displayData.map((program) => program.retestCounts.completed),
        backgroundColor: "#344BFD",
        borderColor: "#344BFD",
        borderWidth: 1,
        borderRadius: 20,
        barThickness: 35,
      },
      {
        label: "In Progress",
        data: displayData.map((program) => program.retestCounts.inProgress),
        backgroundColor: "#80C1FE",
        borderColor: "#80C1FE",
        borderWidth: 1,
        borderRadius: 20,
        barThickness: 35,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
        labels: {
          font: {
            size: 14,
            family: "'Inter', sans-serif",
            weight: "600",
          },
          padding: 20,
          usePointStyle: true,
          pointStyle: "circle",
        },
      },
      title: {
        display: false,
        text: title,
        font: {
          size: 20,
          family: "'Inter', sans-serif",
          weight: "700",
        },
        padding: 10,
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        title: {
          display: true,
          text: "Programs",
          font: {
            size: 16,
            family: "'Inter', sans-serif",
            weight: "600",
          },
          color: "#333",
          padding: 10,
        },
        ticks: {
          font: {
            family: "'Inter', sans-serif",
          },
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: "rgba(0, 0, 0, 0.05)",
        },
        title: {
          display: true,
          text: "No. of Retests",
          font: {
            size: 16,
            family: "'Inter', sans-serif",
            weight: "600",
          },
          color: "#333",
          padding: 10,
        },
        ticks: {
          font: {
            family: "'Inter', sans-serif",
          },
        },
      },
    },
  };

  return (
    <div className="flex flex-col rounded-xl bg-white shadow-lg">

      <div className="bg-gradient-to-r from-blue-700 to-blue-600 p-4 rounded-t-xl">
        <h2 className="text-xl font-semibold text-white">{title}</h2>
      </div>

      <div className="relative mb-4" ref={dropdownRef}>
        <div className="flex items-center justify-between  bg-ctb-grey-150 p-3 shadow-sm focus:ring-2 focus:ring-blue-500">
          {/* Selected Programs Section */}
          <div className="flex flex-wrap items-center gap-2 mt-1">
            {selectedPrograms.length > 0 ? (
              selectedPrograms.map((program) => (
                <span
                  key={program.programId}
                  className="flex items-center rounded-full border border-ctb-green-600 bg-white px-3 py-1 text-sm font-medium  text-ctb-green-600"
                >
                  {program.programTitle}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleProgramSelect(program);
                    }}
                    className="ml-2 text-ctb-green-600 hover:text-blue-700"
                  >
                    ×
                  </button>
                </span>
              ))
            ) : (
              <span className="text-gray-500">Select up to 5 programs</span>
            )}
          </div>

          {/* Search Program Button */}

          <button
            className="flex items-center rounded border border-gray-300 bg-white px-5 py-2 text-sm text-gray-700 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 whitespace-nowrap"
            onClick={() => setIsDropdownOpen((prevState) => !prevState)}
          >
            Select Programs
            <FaCaretDown className="ml-2" />
          </button>


        </div>


        {isDropdownOpen && (
          <div className="absolute z-10 mt-2 w-[95%] rounded-lg border bg-white shadow-lg">
            <div className="border-b p-2">
              <input
                type="text"
                placeholder="Search programs..."
                className="w-full rounded-md border px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                autoFocus
              />
            </div>
            <div className="max-h-60 overflow-y-auto">
              {filteredPrograms.length > 0 ? (
                filteredPrograms.map((program) => (
                  <div
                    key={program.programId}
                    className={`flex cursor-pointer items-center px-4 py-2 hover:bg-gray-50 ${selectedPrograms.find(
                      (p) => p.programId === program.programId
                    )
                      ? "bg-indigo-50"
                      : ""
                      } ${selectedPrograms.length >= 5 &&
                        !selectedPrograms.find(
                          (p) => p.programId === program.programId
                        )
                        ? "cursor-not-allowed opacity-50"
                        : ""
                      }`}
                    onClick={() => handleProgramSelect(program)}
                  >
                    <input
                      type="checkbox"
                      checked={
                        !!selectedPrograms.find(
                          (p) => p.programId === program.programId
                        )
                      }
                      onChange={() => { }}
                      className="mr-3 h-4 w-4 rounded text-indigo-600"
                    />
                    {program.programTitle}
                  </div>
                ))
              ) : (
                <div className="px-4 py-2 text-gray-500">No programs found</div>
              )}
            </div>
          </div>
        )}
      </div>

      <div className="-mt-2 h-[400px]">
        <Bar data={chartData} options={options} />
      </div>
    </div>
  );
};

export default RetestBarChart;
