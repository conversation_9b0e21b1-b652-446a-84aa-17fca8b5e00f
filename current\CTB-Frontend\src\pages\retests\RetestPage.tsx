import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { FaArrowLeft } from "react-icons/fa"; // Importing react-icons
import useUserCredentials from "../../utils/hooks/user/useUserCredentials";
import useRetestLogs from "../../utils/hooks/retests/useRetestLogs";
import useReportDetails from "../../utils/hooks/retests/useReportDetails";
import usePageTitle from "../../utils/hooks/usePageTitle";
import LoadingSpinner from "../../components/retests/utils/LoadingSpinner";
import { NoDataAvailable } from "../../components/retests/utils/NoDataAvailable";
import { RetestHeader } from "../../components/retests/LogsPage/RetestHeader";
import { CommentForm } from "../../components/retests/LogsPage/CommentForm";
import { LogList } from "../../components/retests/LogsPage/LogList";
import { UserRole } from "../../utils/api/endpoints/user/credentials";
import useRetestStatusRetestId from "../../utils/hooks/retests/useRetestStatusRetestId";
import useRetestDetailsRightSection from "../../utils/hooks/retests/useRetestDetailsRight";
import RetestDetailsRightSection from "../../components/retests/LogsPage/RetestDetailsRightSection";

const RetestPage: React.FC = () => {
  const { retest_id } = useParams<{ retest_id?: string }>();
  const navigate = useNavigate(); // Hook for navigation
  const { role } = useUserCredentials();
  const [refetch, setRefetch] = useState<boolean>(false);

  const roleString = role
    ? role === UserRole.RESEARCHER
      ? "RESEARCHER"
      : role === UserRole.BUSINESS
      ? "BUSINESS"
      : role === UserRole.ADMIN
      ? "ADMIN"
      : ""
    : "";

  const { logs, loading: logsLoading } = useRetestLogs(retest_id, refetch);
  const {
    reportDetails,
    loading: detailsLoading,
    error: detailsError
  } = useReportDetails(retest_id);
  const {
    status,
    createdAt,
    loading: statusLoading,
    error: statusError
  } = useRetestStatusRetestId(retest_id);

  const {
    details: retestDetails,
    loading: retestDetailsLoading,
    error: retestDetailsError
  } = useRetestDetailsRightSection(retest_id || "");

  usePageTitle(`${reportDetails?.title} - Retest`);

  // Loading or Error Handling
  if (logsLoading || detailsLoading || statusLoading || retestDetailsLoading)
    return <LoadingSpinner />;
  if (detailsError || statusError || retestDetailsError)
    return <p>Failed to fetch data...</p>;
  if (!logs || logs.length === 0) return <NoDataAvailable />;

  return (
    <div className="montserrat-font container flex min-h-screen max-w-full flex-col gap-4 bg-[#f8f8fc] px-4 py-4">
      {/* Return to Retests */}
      <button
        className="flex items-center gap-2 text-blue-800 font-semibold hover:underline"
        onClick={() => navigate("/dashboard/retests")}
      >
        <FaArrowLeft className="text-lg" /> {/* Back arrow icon */}
        <span>Return to Retests</span>
      </button>

      <RetestHeader
        reportName={reportDetails?.title}
        programName={reportDetails?.program_creator?.displayName}
        severityScore={reportDetails?.severity?.score}
        severityCategory={reportDetails?.severity?.category}
        retestStatus={status}
        retestDate={createdAt}
      />

      <div className="flex w-full gap-4">
        <div className="h-[500px] w-[70%] flex-grow overflow-y-auto">
          <LogList logs={logs} />
        </div>

        <RetestDetailsRightSection
          reportTitle={retestDetails?.report_title}
          researcherUsername={retestDetails?.researcher_username}
          researcherPfp={retestDetails?.researcher_pfp}
          businessPfp={retestDetails?.business_pfp}
          targetName={retestDetails?.target_name}
          reportId={retestDetails?.report_id}
        />
      </div>

      {retest_id && (
        <div className="sticky bottom-0 w-[67%] bg-white">
          <CommentForm
            retest_id={retest_id}
            roleString={roleString}
            role={role}
            onCommentSubmit={() => setRefetch(!refetch)}
            currentStatus={status}
          />
        </div>
      )}
    </div>
  );
};

export default RetestPage;
