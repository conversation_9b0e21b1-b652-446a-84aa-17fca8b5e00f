export interface ReportsInfo {
    program: {
      id: number;
      name: string;
      total_reports: number;
    };
    summary: {
      reports_received_90_days: number; 
      reports_resolved: number;
      assets_in_scope: number;
      report_status_counts: Record<string, number>;
    };
    reports: {
      id: number;
      title: string;
      status: string;
      category: string;
      severity: string;
      submitted_date: string;
    }[];
  }
  
    
  export interface TargetsInfo {
      program_id: number;
      total_targets: number;
      targets: { targetName: string; targetType: string }[];
    }
    
    export const parseScopeStats = (data: any): ScopeStatsInfo | null => {
      console.log("Raw API response:", data);
     
      if (!data || typeof data.program_id !== "number" || !Array.isArray(data.scopeStats)) {
        console.error("Invalid data structure received:", data);
        return null;
      }
    
      return {
        program: {
          id: data.program_id ?? 0,
          name: `Program ${data.program_id}`,  
          total_scopes: data.scopeStats.length,
        },
        scopes: data.scopeStats.map((scope: any) => ({
          name: scope.scope ?? "Unknown",  
          reportCount: Number(scope.reportCount) || 0,
          lastSubmitted: scope.lastSubmitted ? new Date(scope.lastSubmitted).toLocaleDateString() : "N/A",
         })),
      };
    };
    
    
    export type ScopeStatsInfo = {
      program: {
        id: number;
        name: string;
        total_scopes: number;
      };
      scopes: {
        name: string;
        reportCount: number;
        lastSubmitted: string;
      }[];
    };
    
  
    