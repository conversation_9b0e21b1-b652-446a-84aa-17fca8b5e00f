const CircleCodeIcon = ({ className, color = "#0B45DB" }: { className?: string; color?: string }) => (
    <svg width="33" height="33" viewBox="0 0 33 33" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
      <path
        d="M16.5 30.9375C8.53875 30.9375 2.0625 24.4612 2.0625 16.5H4.125C4.125 23.3238 9.67622 28.875 16.5 28.875C23.3238 28.875 28.875 23.3238 28.875 16.5H30.9375C30.9375 24.4612 24.4613 30.9375 16.5 30.9375Z"
        fill={color}
      />
      <path
        d="M16.5 26.8125C10.8137 26.8125 6.1875 22.1863 6.1875 16.5H8.25C8.25 21.0488 11.9512 24.75 16.5 24.75C21.0488 24.75 24.75 21.0488 24.75 16.5H26.8125C26.8125 22.1863 22.1863 26.8125 16.5 26.8125Z"
        fill={color}
      />
      <path
        d="M16.5 20.6251C14.2251 20.6251 12.375 18.775 12.375 16.5001C12.375 14.2251 14.2251 12.3751 16.5 12.3751C18.7749 12.3751 20.625 14.2251 20.625 16.5001C20.625 18.775 18.7749 20.6251 16.5 20.6251ZM16.5 14.4376C15.3625 14.4376 14.4375 15.3626 14.4375 16.5001C14.4375 17.6375 15.3625 18.5626 16.5 18.5626C17.6375 18.5626 18.5625 17.6375 18.5625 16.5001C18.5625 15.3626 17.6375 14.4376 16.5 14.4376ZM16.5 2.06257C14.0082 2.06045 11.5584 2.70422 9.38958 3.93106C7.22073 5.1579 5.4069 6.92593 4.125 9.06269V4.12507H2.0625V12.3751H10.3125V10.3126H5.78738C7.97981 6.51241 12.0326 4.12507 16.5 4.12507C19.8052 4.12507 22.9134 5.41207 25.2502 7.74991L26.7094 6.29069C25.372 4.94549 23.781 3.87909 22.0285 3.1533C20.276 2.42751 18.3969 2.05677 16.5 2.06257Z"
        fill={color}
      />
    </svg>
  );
  
  export default CircleCodeIcon;
  