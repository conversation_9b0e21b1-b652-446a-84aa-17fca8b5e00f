import React, { useState, use<PERSON>emo, Fragment } from "react";
import { motion, AnimatePresence, LayoutGroup } from "framer-motion";
import { format } from "date-fns";
import { Link } from "react-router-dom";
import * as Switch from "@radix-ui/react-switch";
import useUnifiedBusinessDashboard from "../../../utils/hooks/dashboard/useUnifiedBusinessDashboard";
import BugLifecycleProgress from "./BugLifecycleProgress";
import { BusinessReviewIssue } from "../../../utils/hooks/dashboard/useBusinessReviewIssues";
import { FiChevronDown, FiChevronRight, FiEye, FiClock, FiTag, FiAlertTriangle, FiGrid, FiBriefcase, FiFilter, FiRefreshCw, FiLink } from "react-icons/fi";

// Animation variants for container
const containerVariants = {
  hidden: { opacity: 0 },
  visible: { 
    opacity: 1,
    transition: { 
      duration: 0.4,
      when: "beforeChildren",
      staggerChildren: 0.05,
      ease: "easeOut"
    }
  }
};

// Animation variants for section headers
const headerVariants = {
  hidden: { opacity: 0, y: -12 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { 
      type: "spring", 
      duration: 0.5,
      bounce: 0.1
    }
  }
};

// Animation variants for table rows
const rowVariants = {
  hidden: { opacity: 0, y: 8 },
  visible: (i: number) => ({ 
    opacity: 1, 
    y: 0,
    transition: { 
      type: "spring",
      delay: i * 0.03,
      duration: 0.3,
      bounce: 0.1
    }
  }),
  hover: {
    backgroundColor: "rgba(243, 244, 246, 0.7)",
    y: -2,
    boxShadow: "0 2px 4px rgba(0,0,0,0.03)",
    transition: { 
      duration: 0.2,
      ease: [0.25, 0.1, 0.25, 1.0],
      y: {
        type: "spring",
        stiffness: 300,
        damping: 20
      }
    }
  },
  tap: {
    scale: 0.995,
    transition: { duration: 0.1 }
  }
};

// Animation variants for dropdown menu
const dropdownVariants = {
  hidden: { 
    opacity: 0, 
    scale: 0.95,
    y: -5
  },
  visible: { 
    opacity: 1, 
    scale: 1,
    y: 0,
    transition: { 
      type: "spring", 
      stiffness: 350, 
      damping: 25, 
      mass: 0.5,
      duration: 0.2
    }
  },
  exit: { 
    opacity: 0, 
    scale: 0.95,
    y: -5,
    transition: { 
      duration: 0.1,
      ease: "easeOut"
    }
  }
};

interface FilterState {
  showBusinessReviewOnly: boolean;
  selectedProgram: string | null;
  selectedAsset: string | null;
}

const SeverityBadge = ({ level, color }: { level: string; color: string }) => (
  <motion.span
    className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium"
    style={{ 
      backgroundColor: `${color}15`,
      color: color,
      border: `1px solid ${color}30`
    }}
    whileHover={{ scale: 1.02, boxShadow: `0 1px 2px 0 ${color}20` }}
    transition={{ duration: 0.15 }}
  >
    <span className="relative flex h-2 w-2 mr-1.5">
      <span className="animate-ping absolute inline-flex h-full w-full rounded-full opacity-50" style={{ backgroundColor: color }}></span>
      <span className="relative inline-flex rounded-full h-2 w-2" style={{ backgroundColor: color }}></span>
    </span>
    {level}
  </motion.span>
);

const AssetBadge = ({ scope }: { scope: string }) => (
  <div className="flex items-center w-full">
    <div className={`bg-neutral-100/90 rounded-full px-2.5 py-0.5 text-xs font-medium text-neutral-700 ring-1 ring-inset ring-neutral-200/80 inline-flex items-center ${scope && scope.length > 20 ? 'w-full max-w-full' : 'w-auto'}`}>
      <FiLink className="h-3 w-3 mr-1 text-neutral-500 flex-shrink-0" />
      <span className="truncate" title={scope}>{scope && scope.length > 35 ? `${scope.substring(0, 35)}...` : scope || "Not specified"}</span>
    </div>
  </div>
);

const StatusBadge = ({ status }: { status: string }) => (
  <span className="inline-flex items-center justify-center rounded-full bg-blue-100/90 px-2.5 py-0.5 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-300/70 w-[100px] min-w-[100px]">
    <span className="truncate text-center" title={status}>{status}</span>
  </span>
);

const DaysBadge = ({ days, isUrgent }: { days: number; isUrgent: boolean }) => (
  <motion.span 
    className={`inline-flex items-center font-mono font-semibold text-xs px-2 sm:px-2.5 py-1 rounded-full ${
      isUrgent 
        ? "bg-red-100/90 text-red-700 ring-1 ring-red-300/70" 
        : "bg-amber-100/90 text-amber-800 ring-1 ring-amber-300/70"
    }`}
    {...(isUrgent && {
      initial: { scale: 1 },
      animate: { scale: [1, 1.03, 1] },
      transition: { repeat: Infinity, repeatType: "mirror", duration: 2, ease: "easeInOut" }
    })}
  >
    <FiClock className="h-3 w-3 mr-0.5 sm:mr-1.5" />
    <span className="hidden xs:inline">{days}</span>
    <span className="inline xs:hidden">{days}</span>
  </motion.span>
);

// Table header component
const TableHeader: React.FC = () => (
  <thead className="bg-neutral-50/90 backdrop-blur-sm sticky top-0 z-10 shadow-sm">
    <tr>
      <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-neutral-700 min-w-[120px] w-auto">Title</th>
      <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-neutral-700 w-auto">Severity</th>
      <th scope="col" className="hidden md:table-cell px-4 py-3.5 text-left text-sm font-semibold text-neutral-700 w-auto">Asset</th>
      <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-neutral-700 w-auto">Program</th>
      <th scope="col" className="px-4 py-3.5 text-center text-sm font-semibold text-neutral-700 whitespace-nowrap w-auto">Days</th>
      <th scope="col" className="px-4 py-3.5 text-center text-sm font-semibold text-neutral-700 w-[120px]">Status</th>
    </tr>
  </thead>
);

const ActionableReportsSection: React.FC = () => {
  const { businessReviewReports, loading, error, refetch, businessReviewMeta } = useUnifiedBusinessDashboard();
  const issues = businessReviewReports || [];
  const meta = businessReviewMeta || { totalCount: 0, hasMore: false };
  
  const [expandedReportId, setExpandedReportId] = useState<number | null>(null);
  const [filters, setFilters] = useState<FilterState>({
    showBusinessReviewOnly: false,
    selectedProgram: null,
    selectedAsset: null
  });
  const [isProgramDropdownOpen, setIsProgramDropdownOpen] = useState(false);
  const [isAssetDropdownOpen, setIsAssetDropdownOpen] = useState(false);

  const programOptions = useMemo(() => {
    const programs = new Set<string>();
    issues.forEach(issue => {
      if (issue.program_name) {
        programs.add(issue.program_name);
      }
    });
    return Array.from(programs).sort();
  }, [issues]);

  const assetOptions = useMemo(() => {
    const assets = new Set<string>();
    issues.forEach(issue => {
      if (issue.scope) {
        assets.add(issue.scope);
      }
    });
    return Array.from(assets).sort();
  }, [issues]);

  const filteredIssues = useMemo(() => {
    return issues.filter(issue => {
      if (filters.showBusinessReviewOnly && !issue.is_business_review) return false;
      if (filters.selectedProgram && issue.program_name !== filters.selectedProgram) return false;
      if (filters.selectedAsset && issue.scope !== filters.selectedAsset) return false;
      return true;
    });
  }, [issues, filters]);

  const businessReviewIssues = useMemo(() => 
    filteredIssues.filter(issue => issue.is_business_review),
    [filteredIssues]
  );
  
  const otherIssues = useMemo(() => 
    filteredIssues.filter(issue => !issue.is_business_review),
    [filteredIssues]
  );

  const formatDate = (dateString: string): string => {
    try {
      return format(new Date(dateString), "MMM d, yyyy");
    } catch (error) {
      return "Invalid date";
    }
  };

  const handleProgramChange = (program: string | null) => {
    setFilters(prev => ({ ...prev, selectedProgram: program }));
    setIsProgramDropdownOpen(false);
  };

  const handleAssetChange = (asset: string | null) => {
    setFilters(prev => ({ ...prev, selectedAsset: asset }));
    setIsAssetDropdownOpen(false);
  };

  const handleBusinessReviewToggle = (checked: boolean) => {
    setFilters(prev => ({ ...prev, showBusinessReviewOnly: checked }));
  };

  const toggleExpanded = (reportId: number) => {
    setExpandedReportId(expandedReportId === reportId ? null : reportId);
  };

  const renderReportRow = (issue: BusinessReviewIssue, index: number, isBusinessReviewSection: boolean) => {
    const isExpanded = expandedReportId === issue.report_id;
    const isUrgent = issue.days_open > 30;
    const isEvenRow = index % 2 === 0;
    
    return (
      <Fragment key={issue.report_id}>
        <motion.tr
          custom={index}
          variants={rowVariants}
          whileHover="hover"
          whileTap="tap"
          onClick={() => toggleExpanded(issue.report_id)}
          className={`cursor-pointer transition-all duration-200 outline-none ${
            isBusinessReviewSection 
              ? (isExpanded ? "bg-blue-50/80" : (isEvenRow ? "bg-blue-50/30" : "bg-white")) 
              : (isExpanded ? "bg-neutral-100/80" : (isEvenRow ? "bg-neutral-50/40" : "bg-white"))
          } border-b border-neutral-200/70`}
          style={{ willChange: 'transform, background-color' }}
          role="button"
          tabIndex={0}
          aria-label={`${issue.title} - Click to ${isExpanded ? 'hide' : 'view'} details`}
          aria-expanded={isExpanded}
          onKeyDown={(e: React.KeyboardEvent) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              toggleExpanded(issue.report_id);
            }
          }}
        >          
          <td className="px-4 py-3 text-sm md:text-base font-medium">
            <div className="relative group">
              <span className="line-clamp-1 text-neutral-800 group-hover:text-blue-600 transition-colors duration-200" title={issue.title}>
                {issue.title}
              </span>
              <div className="absolute right-0 top-0 h-full w-12 bg-gradient-to-l from-white to-transparent pointer-events-none"></div>
            </div>
            <div className="text-xs text-neutral-500 mt-1 hidden sm:block">ID: #{issue.report_id}</div>
          </td>
          
          <td className="px-4 py-3 text-sm">
            <SeverityBadge level={issue.severity_category} color={issue.severity_color} />
          </td>
          
          <td className="hidden md:table-cell px-4 py-3 text-sm">
            {issue.scope && (
              <div className={`${issue.scope.length < 10 ? 'w-auto' : issue.scope.length < 20 ? 'w-auto max-w-[150px]' : 'w-full max-w-[250px]'}`}>
                <AssetBadge scope={issue.scope} />
              </div>
            )}
          </td>
          
          <td className="px-4 py-3 text-sm text-neutral-600 max-w-[180px]">
            <div className="overflow-hidden">
              <span className="inline-flex items-center gap-1 w-full">
                <FiBriefcase className="h-3.5 w-3.5 flex-shrink-0 text-neutral-500" />
                <span className="truncate block" title={issue.program_name}>{issue.program_name}</span>
              </span>
            </div>
          </td>
          
          <td className="px-4 py-3 text-sm text-center">
            <DaysBadge days={issue.days_open} isUrgent={isUrgent} />
          </td>
          
          <td className="px-4 py-3 text-center">
            <div className="flex items-center justify-center gap-2">
              <div className="block">
                <StatusBadge status={issue.status_label} />
              </div>
              <motion.div 
                className="text-neutral-400 flex-shrink-0"
                initial={{ opacity: 0, x: -4 }}
                whileHover={{ scale: 1.1 }}
                animate={{ 
                  opacity: isExpanded ? 1 : 0.6,
                  x: 0,
                  rotate: isExpanded ? 90 : 0
                }}
                transition={{
                  duration: 0.2,
                  ease: [0.25, 0.1, 0.25, 1.0],
                  rotate: {
                    type: "spring",
                    stiffness: 200,
                    damping: 24
                  }
                }}
              >
                <FiChevronRight className="h-5 w-5" />
              </motion.div>
            </div>
          </td>
        </motion.tr>
        
        <AnimatePresence initial={false}>
          {isExpanded && (
            <motion.tr 
              className={`${
                isBusinessReviewSection ? "bg-blue-50/60" : "bg-neutral-50/60"
              } border-b border-neutral-200/70`}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ 
                duration: 0.2, 
                ease: "easeOut",
                opacity: { duration: 0.25, ease: [0.25, 0.1, 0.25, 1.0] }
              }}
              layout
            >
              <td colSpan={6} className="p-0">
                <motion.div
                  className="px-6 py-5 border-t border-neutral-200/40 overflow-hidden"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ 
                    opacity: 1, 
                    y: 0,
                    transition: {
                      duration: 0.25,
                      ease: [0.25, 0.1, 0.25, 1.0],
                      opacity: { duration: 0.15 },
                      y: { duration: 0.2 }
                    }
                  }}
                  exit={{ 
                    opacity: 0, 
                    y: -5,
                    transition: {
                      duration: 0.15,
                      ease: "easeInOut"
                    }
                  }}
                >
                  <div className="flex flex-col gap-6">
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.05, duration: 0.2 }}
                    >
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="text-sm font-medium text-neutral-700 flex items-center gap-2">
                          <FiGrid className="h-4 w-4 text-neutral-500" />
                          Report Progress Timeline
                        </h4>
                      </div>
                      <BugLifecycleProgress lifecycle={issue.lifecycle} expanded={true} />
                    </motion.div>
                    
                    <motion.div
                      className="flex justify-center"
                      initial={{ opacity: 0, y: 5 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1, duration: 0.2 }}
                    >
                      <motion.div
                        whileHover={{ 
                          scale: 1.03, 
                          boxShadow: "0 5px 15px rgba(59, 130, 246, 0.3)"
                        }}
                        whileTap={{ scale: 0.97 }}
                        style={{ backgroundPosition: "right center" }}
                        transition={{ duration: 0.3 }}
                      >
                        <Link
                          to={`/dashboard/reports/${issue.report_id}`}
                          onClick={(e) => e.stopPropagation()}
                          className="flex items-center gap-2 px-6 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-lg shadow-md font-medium"
                          title="View complete report details"
                        >
                          <motion.span
                            whileHover={{ x: -2 }}
                            transition={{ duration: 0.2 }}
                          >
                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                              <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                            </svg>
                          </motion.span>
                          View Full Report
                        </Link>
                      </motion.div>
                    </motion.div>
                  </div>
                </motion.div>
              </td>
            </motion.tr>
          )}
        </AnimatePresence>
      </Fragment>
    );
  };
  
  const renderTableSection = (title: string, issuesToRender: BusinessReviewIssue[], isBusinessReviewSection: boolean) => {
    if (!loading && !error && issuesToRender.length === 0) {
      return null; // Don't render section if no issues
    }

    return (
      <div className="mb-8">
        <motion.h3 
          className="text-lg font-medium text-neutral-900 mb-3 px-1 flex items-center"
          variants={headerVariants}
        >
          {title}
          <span className={`ml-2 inline-flex items-center justify-center w-6 h-6 rounded-full text-xs ${
            isBusinessReviewSection ? "bg-blue-100 text-blue-800" : "bg-neutral-100 text-neutral-700"
          }`}>
            {issuesToRender.length}
          </span>
        </motion.h3>
        
        <div className="rounded-lg border border-neutral-200/80 shadow-sm">
          <div className="block w-full overflow-hidden -webkit-overflow-scrolling-touch">
            <LayoutGroup>
              <table className="w-full divide-y divide-neutral-200/80">
                <TableHeader />
                <tbody className="bg-white divide-y divide-neutral-200/60">
                  {issuesToRender.map((issue, index) => renderReportRow(issue, index, isBusinessReviewSection))}
                </tbody>
              </table>
            </LayoutGroup>
          </div>
        </div>
      </div>
    );
  };

  return (
    <motion.div
      className="relative rounded-xl border border-neutral-200/80 bg-white/95 backdrop-blur-sm p-6 shadow-lg"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      style={{ willChange: 'opacity' }}
    >
      {/* Header section with filters */}
      <motion.div 
        className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4"
        variants={headerVariants}
      >
        <div>
          <h2 className="text-xl font-semibold text-neutral-900 mb-1">
            Actionable Reports
          </h2>
          <p className="text-sm text-neutral-600">
            Issues requiring your attention. Click on a row to view progress and details.
          </p>
        </div>
        
        <div className="flex flex-wrap items-center gap-3 md:flex-nowrap whitespace-nowrap">
          <div className="flex items-center mr-1 bg-white/80 border border-neutral-200/60 rounded-lg px-3 py-1.5 shadow-sm">
            <label 
              htmlFor="business-review-toggle" 
              className="pr-3 text-sm font-medium text-neutral-700 cursor-pointer flex items-center"
            >
              <FiAlertTriangle className="h-3.5 w-3.5 text-amber-500 mr-1.5" />
              Business Review Only
            </label>
            <Switch.Root 
              id="business-review-toggle"
              checked={filters.showBusinessReviewOnly} 
              onCheckedChange={handleBusinessReviewToggle}
              className={`w-10 h-5 rounded-full relative transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 ${
                filters.showBusinessReviewOnly ? "bg-blue-600" : "bg-neutral-300"
              }`}
            >
              <Switch.Thumb 
                className={`block w-4 h-4 bg-white rounded-full transform transition-transform shadow-sm ${
                  filters.showBusinessReviewOnly ? "translate-x-5" : "translate-x-0.5"
                }`}
              />
            </Switch.Root>
          </div>
          
          {programOptions.length > 1 && (
            <div className="relative">
              <button 
                className="flex items-center gap-2 rounded-lg border border-neutral-200/60 bg-white/80 px-3 py-1.5 text-sm font-medium text-neutral-700 shadow-sm hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-all"
                onClick={() => setIsProgramDropdownOpen(prev => !prev)}
                aria-expanded={isProgramDropdownOpen}
                aria-haspopup="true"
              >
                <FiBriefcase className="h-3.5 w-3.5 text-neutral-500" />
                <span className="truncate max-w-[150px]">
                  {filters.selectedProgram || "All Programs"}
                </span>
                <FiChevronDown className={`h-4 w-4 text-neutral-500 transition-transform duration-150 ${isProgramDropdownOpen ? 'rotate-180' : ''}`} />
              </button>
              
              <AnimatePresence>
                {isProgramDropdownOpen && (
                  <motion.div
                    className="absolute right-0 mt-2 z-50 min-w-[180px] rounded-lg border border-neutral-200/80 bg-white/95 backdrop-blur-lg p-1.5 shadow-lg overflow-hidden"
                    variants={dropdownVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                  >
                    <div 
                      className={`relative flex cursor-pointer select-none items-center rounded-md px-2 py-1.5 text-sm outline-none transition-colors hover:bg-neutral-100/90 ${
                        filters.selectedProgram === null ? "bg-blue-50/90 text-blue-700 font-medium" : "text-neutral-700"
                      }`}
                      onClick={() => handleProgramChange(null)}
                    >
                      {filters.selectedProgram === null && <span className="mr-2">✓</span>}
                      All Programs
                    </div>
                    <div className="my-1 h-px bg-neutral-200" />
                    {programOptions.map((program) => (
                      <div 
                        key={program}
                        className={`relative flex cursor-pointer select-none items-center rounded-md px-2 py-1.5 text-sm outline-none transition-colors hover:bg-neutral-100/90 ${
                          filters.selectedProgram === program ? "bg-blue-50/90 text-blue-700 font-medium" : "text-neutral-700"
                        }`}
                        onClick={() => handleProgramChange(program)}
                      >
                        {filters.selectedProgram === program && <span className="mr-2">✓</span>}
                        {program}
                      </div>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          )}

          {assetOptions.length > 1 && (
            <div className="relative">
              <button 
                className="flex items-center gap-2 rounded-lg border border-neutral-200/60 bg-white/80 px-3 py-1.5 text-sm font-medium text-neutral-700 shadow-sm hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-all"
                onClick={() => setIsAssetDropdownOpen(prev => !prev)}
                aria-expanded={isAssetDropdownOpen}
                aria-haspopup="true"
              >
                <FiTag className="h-3.5 w-3.5 text-neutral-500" />
                <span className="truncate max-w-[150px]">
                  {filters.selectedAsset || "All Assets"}
                </span>
                <FiChevronDown className={`h-4 w-4 text-neutral-500 transition-transform duration-150 ${isAssetDropdownOpen ? 'rotate-180' : ''}`} />
              </button>
              
              <AnimatePresence>
                {isAssetDropdownOpen && (
                  <motion.div
                    className="absolute right-0 mt-2 z-50 min-w-[180px] rounded-lg border border-neutral-200/80 bg-white/95 backdrop-blur-lg p-1.5 shadow-lg overflow-hidden"
                    variants={dropdownVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                  >
                    <div 
                      className={`relative flex cursor-pointer select-none items-center rounded-md px-2 py-1.5 text-sm outline-none transition-colors hover:bg-neutral-100/90 ${
                        filters.selectedAsset === null ? "bg-blue-50/90 text-blue-700 font-medium" : "text-neutral-700"
                      }`}
                      onClick={() => handleAssetChange(null)}
                    >
                      {filters.selectedAsset === null && <span className="mr-2">✓</span>}
                      All Assets
                    </div>
                    <div className="my-1 h-px bg-neutral-200" />
                    {assetOptions.map((asset) => (
                      <div 
                        key={asset}
                        className={`relative flex cursor-pointer select-none items-center rounded-md px-2 py-1.5 text-sm outline-none transition-colors hover:bg-neutral-100/90 ${
                          filters.selectedAsset === asset ? "bg-blue-50/90 text-blue-700 font-medium" : "text-neutral-700"
                        }`}
                        onClick={() => handleAssetChange(asset)}
                      >
                        {filters.selectedAsset === asset && <span className="mr-2">✓</span>}
                        {asset}
                      </div>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          )}

          <button 
            onClick={(e) => { e.preventDefault(); refetch(); }}
            className="inline-flex items-center gap-1.5 rounded-lg border border-neutral-200/60 bg-white/80 px-3 py-1.5 text-sm font-medium text-neutral-700 shadow-sm hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-all"
            aria-label="Refresh actionable reports"
          >
            <FiRefreshCw className="h-3.5 w-3.5 text-neutral-500" />
            Refresh
          </button>
        </div>
      </motion.div>

      {loading && (
        <div className="flex flex-col items-center justify-center py-20">
          <motion.div 
            className="w-16 h-16 border-t-4 border-b-4 border-blue-600 rounded-full"
            animate={{ rotate: 360 }}
            transition={{ repeat: Infinity, duration: 1, ease: "linear" }}
            style={{ willChange: 'transform' }}
          />
          <p className="mt-4 text-sm font-medium text-neutral-500">Loading actionable reports...</p>
        </div>
      )}

      {error && !loading && (
        <motion.div 
          className="bg-red-50/80 backdrop-blur-sm border border-red-200/70 rounded-xl p-5 mb-6"
          initial={{ opacity: 0, y: -15 }} 
          animate={{ opacity: 1, y: 0 }}
          transition={{ type: "spring", damping: 12 }}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3 flex-1 md:flex md:justify-between">
              <p className="text-sm font-medium text-red-800">Failed to load reports</p>
              <div className="mt-3 flex md:mt-0 md:ml-6">
                <button 
                  onClick={refetch} 
                  className="rounded-md bg-red-100/80 px-3 py-2 text-sm font-medium text-red-800 hover:bg-red-200 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50"
                >
                  Retry
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {!loading && !error && filteredIssues.length === 0 && (
         <motion.div 
          className="flex flex-col items-center justify-center py-20 text-center"
          variants={headerVariants}
        >
          <div className="rounded-full bg-neutral-100 p-4 shadow-inner">
            <svg className="h-12 w-12 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="mt-6 text-lg font-medium text-neutral-900">No actionable reports found</h3>
          <p className="mt-2 text-sm text-neutral-500 max-w-md mx-auto">
            {filters.showBusinessReviewOnly 
              ? "There are no business review issues at this time."
              : "There are no actionable reports that match the current filters."}
          </p>
          {(filters.showBusinessReviewOnly || filters.selectedProgram || filters.selectedAsset) && (
            <button
              onClick={() => setFilters({ showBusinessReviewOnly: false, selectedProgram: null, selectedAsset: null })}
              className="mt-6 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <FiFilter className="h-4 w-4 mr-1.5" />
              Clear filters
            </button>
          )}
        </motion.div>
      )}

      {renderTableSection("Business Review Issues", businessReviewIssues, true)}
      {!filters.showBusinessReviewOnly && renderTableSection("Other Actionable Issues", otherIssues, false)}

      {!loading && !error && filteredIssues.length > 0 && meta && (
        <div className="mt-10 text-center">
          <motion.p 
            className="text-sm text-neutral-700"
            initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.3 }}
          >
            Showing <b>{filteredIssues.length}</b> of <b>{meta.totalCount}</b> actionable reports.
            
          </motion.p>
          
          <motion.div
            className="mt-5"
            initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.4 }}
          >
            <Link
              to="/dashboard/reports"
              className="inline-flex items-center px-4 py-2 border border-neutral-300/80 rounded-md shadow-sm text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all"
            >
              View All Reports
              <svg className="ml-2 -mr-1 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </Link>
          </motion.div>
        </div>
      )}
    </motion.div>
  );
};

export default ActionableReportsSection; 