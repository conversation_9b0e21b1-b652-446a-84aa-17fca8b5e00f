<!DOCTYPE html>
<html>
<head>
    <title>Test List Spacing in PDF</title>
</head>
<body>
    <h1>Test List Spacing Scenarios</h1>
    
    <!-- Scenario 1: Simple unordered list -->
    <div id="simple-ul">
        <h2>Simple Unordered List</h2>
        <p>This is text before the list.</p>
        <ul>
            <li>First item</li>
            <li>Second item with longer text that might wrap to multiple lines</li>
            <li>Third item</li>
            <li>Fourth item with even longer text that definitely should wrap to multiple lines to test how the layout handles longer content</li>
        </ul>
        <p>This is text after the list.</p>
    </div>
    
    <!-- Scenario 2: Simple ordered list -->
    <div id="simple-ol">
        <h2>Simple Ordered List</h2>
        <p>Text before ordered list.</p>
        <ol>
            <li>First numbered item</li>
            <li>Second numbered item</li>
            <li>Third numbered item with longer text</li>
            <li>Fourth numbered item</li>
            <li>Fifth numbered item</li>
            <li>Sixth numbered item</li>
            <li>Seventh numbered item</li>
            <li>Eighth numbered item</li>
            <li>Ninth numbered item</li>
            <li>Tenth numbered item to test double digits</li>
            <li>Eleventh numbered item</li>
            <li>Twelfth numbered item</li>
        </ol>
        <p>Text after ordered list.</p>
    </div>
    
    <!-- Scenario 3: Nested lists -->
    <div id="nested-lists">
        <h2>Nested Lists</h2>
        <p>Text before nested lists.</p>
        <ul>
            <li>First level item 1</li>
            <li>First level item 2
                <ul>
                    <li>Second level item 1</li>
                    <li>Second level item 2
                        <ul>
                            <li>Third level item 1</li>
                            <li>Third level item 2</li>
                        </ul>
                    </li>
                    <li>Second level item 3</li>
                </ul>
            </li>
            <li>First level item 3</li>
        </ul>
        <p>Text after nested lists.</p>
    </div>
    
    <!-- Scenario 4: Mixed content with lists -->
    <div id="mixed-content">
        <h2>Mixed Content with Lists</h2>
        <p>This is a paragraph before the list.</p>
        <ul>
            <li>List item 1</li>
            <li>List item 2</li>
        </ul>
        <p>Paragraph between lists.</p>
        <ol>
            <li>Ordered item 1</li>
            <li>Ordered item 2</li>
        </ol>
        <blockquote>This is a blockquote after the lists.</blockquote>
        <ul>
            <li>Another unordered list item</li>
            <li>With multiple items</li>
        </ul>
        <p>Final paragraph.</p>
    </div>
    
    <!-- Scenario 5: Lists with very long content -->
    <div id="long-content">
        <h2>Lists with Long Content</h2>
        <ul>
            <li>This is a very long list item that contains a lot of text and should test how the PDF rendering handles text wrapping within list items when the content exceeds the available width of the container.</li>
            <li>Another long item: Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.</li>
            <li>Short item</li>
            <li>Yet another extremely long list item that goes on and on and on to test the boundaries of the layout system and ensure that content doesn't overflow outside the parent container boundaries in the PDF output.</li>
        </ul>
    </div>
</body>
</html>
