import React, { useRef, useEffect } from 'react';
import { ReportData } from '../types/report.types';
import { getDefaultFieldContent, HtmlSectionField } from '../utils/defaultContent';

interface HtmlEditorProps {
  field: keyof ReportData;
  title: string;
  reportData: ReportData;
  onHtmlChange: (field: keyof ReportData, value: string) => void;
}

const HtmlEditor: React.FC<HtmlEditorProps> = ({ field, title, reportData, onHtmlChange }) => {
  const htmlContent = (reportData[field] as string) || getDefaultFieldContent(field as HtmlSectionField, reportData);
  const divRef = useRef<HTMLDivElement>(null);
  const lastFieldRef = useRef<keyof ReportData | null>(null);

  // Only set innerHTML when the field changes (not on every keystroke)
  useEffect(() => {
    if (divRef.current && lastFieldRef.current !== field) {
      divRef.current.innerHTML = htmlContent;
      lastFieldRef.current = field;
    }
  }, [field, htmlContent]);

  // Optionally, update if the value changes from outside (e.g., reset)
  useEffect(() => {
    if (divRef.current && divRef.current.innerHTML !== htmlContent) {
      divRef.current.innerHTML = htmlContent;
    }
  }, [htmlContent]);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
      </div>
      <div
        ref={divRef}
        className="prose max-w-none min-h-[200px] p-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        contentEditable
        onBlur={(e) => onHtmlChange(field, e.currentTarget.innerHTML)}
        onInput={(e) => onHtmlChange(field, e.currentTarget.innerHTML)}
        suppressContentEditableWarning
      />
    </div>
  );
};

export default HtmlEditor; 