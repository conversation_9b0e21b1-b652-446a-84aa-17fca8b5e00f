// SectionRenderer.tsx - Renders the correct section editor based on activeSection
import React from 'react';
import CoverPageEditor from './components/editors/CoverPageEditor';
import DocumentReferenceEditor from './components/editors/DocumentReferenceEditor';
import ExecutiveSummaryEditor from './components/editors/ExecutiveSummaryEditor';
import DisclaimerEditor from './components/editors/DisclaimerEditor';
import MethodologyEditor from './components/editors/MethodologyEditor';
import RecommendationsEditor from './components/editors/RecommendationsEditor';
import ConclusionEditor from './components/editors/ConclusionEditor';
import FindingsEditor from './components/editors/FindingsEditor';
import TargetDetailsEditor from './components/editors/TargetDetailsEditor';
import ProjectObjectivesEditor from './components/editors/ProjectObjectivesEditor';
import ScopeEditor from './components/editors/ScopeEditor';
import FindingsSummaryEditor from './components/editors/FindingsSummaryEditor';
import VulnerabilityRatingsEditor from './components/editors/VulnerabilityRatingsEditor';
import CriticalFindingsEditor from './components/editors/CriticalFindingsEditor';
import HighFindingsEditor from './components/editors/HighFindingsEditor';
import MediumFindingsEditor from './components/editors/MediumFindingsEditor';
import LowFindingsEditor from './components/editors/LowFindingsEditor';
import KeyFindingsEditor from './components/editors/KeyFindingsEditor';
import BrandingEditor from './components/editors/BrandingEditor';
import MethodologySelectorEditor from './components/editors/MethodologySelectorEditor';
import { ReportData, DetailedFinding, Report } from './types/report.types';

interface SectionRendererProps {
  activeSection: string;
  currentData: any;
  sectionData: any;
  handleInputChange: (field: string, value: string) => void;
  handleHtmlChange: (field: string, value: string) => void;
  handleDataChange: (field: string, value: any) => void;
  handleFindingChange: (index: number, field: string | keyof any, value: string, severity: string) => void;
  handleRemoveFinding: (index: number, severity: string) => void;
  handleAddFinding: (severity: string) => void;
  handleTargetDetailsChange: (index: number, field: string, value: string) => void;
  handleAddTarget: () => void;
  handleRemoveTarget: (index: number) => void;
  handleTableChange: (field: string, value: number) => void;
  handleKeyFindingChange: (index: number, field: string, value: string) => void;
  handleAddKeyFinding: () => void;
  handleRemoveKeyFinding: (index: number) => void;
  handleRecommendationsChange: (recommendations: { title: string; description: string }[]) => void;
}

const SectionRenderer: React.FC<SectionRendererProps> = (props) => {
  const {
    activeSection,
    currentData,
    handleInputChange,
    handleHtmlChange,
    handleDataChange,
    handleFindingChange,
    handleRemoveFinding,
    handleAddFinding,
    handleTargetDetailsChange,
    handleAddTarget,
    handleRemoveTarget,
    handleTableChange,
    handleKeyFindingChange,
    handleAddKeyFinding,
    handleRemoveKeyFinding,
    handleRecommendationsChange
  } = props;

  switch (activeSection) {
    case 'branding':
      return <BrandingEditor reportData={currentData} onInputChange={handleInputChange} />;
    case 'cover':
      return <CoverPageEditor reportData={currentData} onInputChange={handleInputChange} />;
    case 'disclaimer':
      return <DisclaimerEditor reportData={currentData} onHtmlChange={handleHtmlChange} />;
    case 'document_reference':
      return <DocumentReferenceEditor reportData={currentData} onInputChange={handleInputChange} />;
    case 'executive_summary':
      return <ExecutiveSummaryEditor reportData={currentData} onHtmlChange={handleHtmlChange} onTableChange={handleTableChange} />;
    case 'scope':
      return <ScopeEditor reportData={currentData} onHtmlChange={handleHtmlChange} onTargetDetailsChange={handleTargetDetailsChange} onAddTarget={handleAddTarget} onRemoveTarget={handleRemoveTarget} />;
    case 'project_objectives':
      return <ProjectObjectivesEditor reportData={currentData} onHtmlChange={handleHtmlChange} />;
    case 'findings_summary':
      return <FindingsSummaryEditor reportData={currentData} onTableChange={handleTableChange} />;
    case 'vulnerability_ratings':
      return <VulnerabilityRatingsEditor reportData={currentData} onDataChange={handleDataChange} />;
    case 'critical_findings':
      return <CriticalFindingsEditor reportData={currentData} onFindingChange={(i, f, v) => handleFindingChange(i, f, v, 'Critical')} onRemoveFinding={i => handleRemoveFinding(i, 'Critical')} onAddFinding={handleAddFinding} />;
    case 'high_findings':
      return <HighFindingsEditor reportData={currentData} onFindingChange={(i, f, v) => handleFindingChange(i, f, v, 'High')} onRemoveFinding={i => handleRemoveFinding(i, 'High')} onAddFinding={handleAddFinding} />;
    case 'medium_findings':
      return <MediumFindingsEditor reportData={currentData} onFindingChange={(i, f, v) => handleFindingChange(i, f, v, 'Medium')} onRemoveFinding={i => handleRemoveFinding(i, 'Medium')} onAddFinding={handleAddFinding} />;
    case 'low_findings':
      return <LowFindingsEditor reportData={currentData} onFindingChange={(i, f, v) => handleFindingChange(i, f, v, 'Low')} onRemoveFinding={i => handleRemoveFinding(i, 'Low')} onAddFinding={handleAddFinding} />;
    case 'methodology':
      return <MethodologyEditor reportData={currentData} onHtmlChange={handleHtmlChange} />;
    case 'methodology_selector':
      return (
        <MethodologySelectorEditor
          value={currentData.methodology || {}}
          onChange={v => handleDataChange('methodology', v)}
        />
      );
    case 'findings':
      return <FindingsEditor reportData={currentData} onHtmlChange={handleHtmlChange} onDataChange={handleDataChange} onTableChange={handleTableChange} />;
    case 'recommendations':
      const mergedData = { ...currentData, ...props.sectionData };
      return <RecommendationsEditor reportData={mergedData} onRecommendationsChange={handleRecommendationsChange} />;
    case 'conclusion':
      return <ConclusionEditor reportData={currentData} onHtmlChange={handleHtmlChange} />;
    case 'key_findings':
      return <KeyFindingsEditor reportData={currentData} onFindingChange={handleKeyFindingChange} onAddFinding={handleAddKeyFinding} onRemoveFinding={handleRemoveKeyFinding} />;
    default:
      return null;
  }
};

export default SectionRenderer; 