import React from 'react';
import { Page, View, Text, Image, StyleSheet } from '@react-pdf/renderer';

const DEFAULT_LOGO = 'https://i.postimg.cc/QVPV9d0x/Full-name-logo-blue.png';

interface DetailedFindingsCoverPageProps {
  documentNumber?: string;
  branding_logo?: string;
  sectionId?: string;
}

const DetailedFindingsCoverPage: React.FC<DetailedFindingsCoverPageProps> = ({ documentNumber, branding_logo, sectionId }) => (
  <Page size="A4" id={sectionId} style={{
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: '20mm 15mm',
    fontFamily: 'Helvetica',
    fontSize: 10,
    justifyContent: 'center',
    alignItems: 'center',
  }}>
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <View style={{ backgroundColor: '#dbeafe', borderRadius: 16, width: '75%', padding: 48, alignItems: 'center', textAlign: 'center' }}>
        <Image
          src={branding_logo ? branding_logo : DEFAULT_LOGO}
          style={{ width: 96, height: 96, objectFit: 'contain', marginBottom: 32 }}
        />
        <Text style={{ fontSize: 26, fontWeight: 'bold', color: '#2563eb', marginBottom: 32 }}>
          DETAILED FINDINGS
        </Text>
      </View>
    </View>
    <View style={styles.footer} fixed>
      <Text style={styles.footerLeft}>{documentNumber || 'Document Number'}</Text>
      <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
    </View>
  </Page>
);

const styles = StyleSheet.create({
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    fontSize: 10,
    color: 'grey',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  footerLeft: {
    textAlign: 'left',
    flex: 1,
  },
  footerRight: {
    textAlign: 'right',
    flex: 1,
  },
});

export default DetailedFindingsCoverPage; 