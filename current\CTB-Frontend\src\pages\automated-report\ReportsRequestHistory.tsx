import { useState, useEffect } from "react";
import FilterBar from "../../components/automated-report/FilterBar";
import ReportCard from "../../components/automated-report/ReportCard";
import Pagination from "../../components/retests/utils/Pagination";
import { getBusinessReports } from "../../utils/api/endpoints/automated-reports/automated-reports";
import { useNavigate, useLocation } from "react-router-dom";
import { UserRole } from "../../utils/api/endpoints/user/credentials";
import useUserCredentials from "../../utils/hooks/user/useUserCredentials";

interface Report {
  id: string;
  title: string;
  company_name: string;
  program_titles: { [key: string]: string };
  created_at: string;
  updated_at: string;
}

interface ReportVersion {
  id: string;
  version_number: number;
  status: string;
  summary: string;
  pdf_url: string;
  approved_at: string | null;
  created_at: string;
  updated_at: string;
  approver: { id: string; name: string; email: string } | null;
  report: Report;
  business_owner?: {
    name: string;
    email: string;
  };
}

export default function PendingReports() {
  const [pendingVersions, setPendingVersions] = useState<ReportVersion[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [programOptions, setProgramOptions] = useState<string[]>([]);
  const [notification, setNotification] = useState<{
    show: boolean;
    message: string;
    type: "success" | "error";
  }>({ show: false, message: "", type: "success" });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [versionsPerPage] = useState(9);

  // Filter states
  const [selectedProgram, setSelectedProgram] = useState("");
  const [selectedVersion, setSelectedVersion] = useState("");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const { role } = useUserCredentials();

  const navigate = useNavigate();
  const location = useLocation();

  const getProgramTitles = (programTitlesObj: {
    [key: string]: string;
  }): string[] => {
    return programTitlesObj ? Object.values(programTitlesObj) : [];
  };

  const showNotification = (message: string, type: "success" | "error") => {
    setNotification({ show: true, message, type });
    setTimeout(() => {
      setNotification(prev => ({ ...prev, show: false }));
    }, 3000);
  };

  const fetchReports = async () => {
    setLoading(true);
    setError("");
    try {
      const response = await getBusinessReports();

      if (response.status === "success") {
        const versions = response.data.pending_reports || [];
        setPendingVersions(versions);
        setCurrentPage(1);

        // Extract unique program names
        const allPrograms: string[] = [];
        versions.forEach((version: ReportVersion) => {
          if (version.report.program_titles) {
            const programs = Object.values(version.report.program_titles);
            programs.forEach(program => {
              if (program && !allPrograms.includes(program)) {
                allPrograms.push(program);
              }
            });
          }
        });
        setProgramOptions(allPrograms);
      } else {
        setError(response.message || "Failed to fetch reports");
        showNotification("Failed to fetch reports", "error");
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      showNotification(`Error: ${errorMessage}`, "error");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReports();
  }, [location.pathname]);

  // Handle search filter
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    setCurrentPage(1);
  };

  // Handle program filter
  const handleProgramFilter = (program: string) => {
    setSelectedProgram(program);
    setCurrentPage(1);
  };

  // Handle version filter
  const handleVersionFilter = (version: string) => {
    setSelectedVersion(version);
    setCurrentPage(1);
  };

  // Handle sort order change
  const handleSortChange = (order: "asc" | "desc") => {
    setSortOrder(order);
    setCurrentPage(1);
  };

  // Handle clear all filters
  const handleClearFilters = () => {
    setSearchQuery("");
    setSelectedProgram("");
    setSelectedVersion("");
    setSortOrder("desc");
    setCurrentPage(1);
  };

  // Apply all filters to versions
  const filteredVersions = pendingVersions.filter(version => {
    const report = version.report;
    const programTitles = getProgramTitles(report.program_titles);
    const searchLower = searchQuery.toLowerCase();

    // Search across all card data
    const matchesSearch =
      searchQuery === "" ||
      report.company_name.toLowerCase().includes(searchLower) ||
      report.title.toLowerCase().includes(searchLower) ||
      programTitles.join(", ").toLowerCase().includes(searchLower) ||
      new Date(version.created_at)
        .toLocaleDateString()
        .toLowerCase()
        .includes(searchLower) ||
      `version ${version.version_number}`.toLowerCase().includes(searchLower) ||
      version.status.toLowerCase().includes(searchLower) ||
      (version.business_owner?.name?.toLowerCase().includes(searchLower) ??
        false) ||
      (version.business_owner?.email?.toLowerCase().includes(searchLower) ??
        false);

    // Program filter
    const matchesProgram =
      selectedProgram === "" ||
      (report.program_titles &&
        Object.values(report.program_titles).some(
          program => program === selectedProgram
        ));

    // Version filter
    let matchesVersion = true;
    if (selectedVersion !== "") {
      if (selectedVersion === "4+") {
        matchesVersion = version.version_number >= 4;
      } else {
        const versionNumber = parseInt(selectedVersion, 10);
        matchesVersion = version.version_number === versionNumber;
      }
    }

    return matchesSearch && matchesProgram && matchesVersion;
  });

  // Sort the filtered versions by date
  const sortedVersions = [...filteredVersions].sort((a, b) => {
    const dateA = new Date(a.created_at).getTime();
    const dateB = new Date(b.created_at).getTime();
    return sortOrder === "asc" ? dateA - dateB : dateB - dateA;
  });

  // Calculate pagination
  const totalPages = Math.ceil(sortedVersions.length / versionsPerPage);
  const indexOfLastVersion = currentPage * versionsPerPage;
  const indexOfFirstVersion = indexOfLastVersion - versionsPerPage;
  const currentVersions = sortedVersions.slice(
    indexOfFirstVersion,
    indexOfLastVersion
  );

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
    window.scrollTo(0, 0);
  };

  return (
    <div className="mx-auto w-full max-w-screen-2xl px-6 py-6">
      {notification.show && (
        <div
          className={`fixed right-4 top-4 z-50 rounded-md p-4 shadow-lg ${
            notification.type === "success"
              ? "border border-green-300 bg-green-100 text-green-800"
              : "border border-red-300 bg-red-100 text-red-800"
          }`}
        >
          <div className="flex items-center">
            {notification.type === "success" ? (
              <svg
                className="mr-2 h-5 w-5"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
            ) : (
              <svg
                className="mr-2 h-5 w-5"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            )}
            <span>{notification.message}</span>
          </div>
        </div>
      )}

      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-2xl font-bold">Pentest Reports</h2>
          <div>
            <span className="text-md inline-block rounded-full bg-gradient-to-r from-blue-500 to-blue-800 px-3 py-1 font-semibold text-white shadow-lg transition-all  hover:shadow-xl">
              Beta
            </span>
          </div>
        </div>
      </div>

      <div className="pb-6">
        <hr />
      </div>

      <FilterBar
        searchValue={searchQuery}
        onSearchChange={handleSearchChange}
        programOptions={programOptions}
        onProgramFilter={handleProgramFilter}
        onVersionFilter={handleVersionFilter}
        onSortChange={handleSortChange}
        onClearFilters={handleClearFilters}
      />

      {loading ? (
        <div className="p-8 text-center">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-500 border-r-transparent"></div>
          <p className="mt-4">Loading reports...</p>
        </div>
      ) : error ? (
        <div className="p-8 text-center text-red-500">
          <p>Error: {error}</p>
          <button
            onClick={fetchReports}
            className="mt-4 rounded bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600"
          >
            Try Again
          </button>
        </div>
      ) : sortedVersions.length === 0 ? (
        <div className="py-12 text-center text-gray-500">
          <p className="mb-4">No reports found</p>
          {pendingVersions.length > 0 ? (
            <p className="text-sm">
              No reports match your current filters. Try adjusting your search
              criteria.
            </p>
          ) : (
            <p className="text-sm">
              Click the "Generate a pentest report" button to create your first
              pentest report.
            </p>
          )}
        </div>
      ) : (
        <>
          <div className="mb-4 text-sm text-gray-500">
            Showing {indexOfFirstVersion + 1}-
            {Math.min(indexOfLastVersion, sortedVersions.length)} of{" "}
            {sortedVersions.length} versions
          </div>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {currentVersions.map(version => {
              const report = version.report;
              const programTitles = getProgramTitles(report.program_titles);

              return (
                <ReportCard
                  key={version.id}
                  companyName={report.company_name}
                  title={report.title}
                  description={`Programs: ${
                    programTitles.length > 0
                      ? programTitles.join(", ")
                      : "None specified"
                  }`}
                  date={new Date(version.created_at).toLocaleDateString()}
                  versionNumber={`Version ${version.version_number}`}
                  status={version.status}
                  pdfUrl={version.pdf_url}
                  isAdmin={
                    role
                      ? [
                          UserRole.ADMIN,
                          UserRole.SUB_ADMIN,
                          UserRole.ADMIN_MANAGER
                        ].includes(role)
                      : false
                  }
                  businessOwner={version.business_owner}
                />
              );
            })}
          </div>

          {totalPages > 1 && (
            <div className="mt-6">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          )}

          {sortedVersions.length === 0 && pendingVersions.length > 0 && (
            <div className="mt-8 text-center text-gray-500">
              <p>No reports match your current filters.</p>
              <button
                onClick={handleClearFilters}
                className="mt-2 text-blue-500 hover:underline"
              >
                Clear all filters
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
