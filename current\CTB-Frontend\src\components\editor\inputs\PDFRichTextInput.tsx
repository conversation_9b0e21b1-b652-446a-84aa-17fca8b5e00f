import React from 'react';
import JoditEditor from 'jodit-react';

interface PDFRichTextInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

const PDFRichTextInput: React.FC<PDFRichTextInputProps> = ({
  value,
  onChange,
  placeholder
}) => {
  const config = {
    readonly: false,
    placeholder: placeholder || 'Start typing...',
    height: 400,
    toolbar: true,
    spellcheck: true,
    language: 'en',
    toolbarButtonSize: 'medium',
    toolbarAdaptive: false,
    showCharsCounter: true,
    showWordsCounter: true,
    showXPathInStatusbar: false,
    askBeforePasteHTML: true,
    askBeforePasteFromWord: true,
    defaultActionOnPaste: 'insert_clear_html',
    buttons: [
      'source', '|',
      'bold', 'italic', 'underline', 'strikethrough', '|',
      'font', 'fontsize', 'brush', 'paragraph', '|',
      'image', 'table', 'link', '|',
      'align', '|',
      'ul', 'ol', '|',
      'symbol', 'fullsize', 'print', 'about'
    ],
    uploader: {
      insertImageAsBase64URI: true
    },
    removeButtons: [],
    zIndex: 0,
    maxHeight: 500,
    maxWidth: '100%',
    minHeight: 300
  };

  return (
    <div className="pdf-rich-text-editor">
      <JoditEditor
        value={value}
        //@ts-ignore
        config={config}
        onBlur={newContent => onChange(newContent)}
        onChange={newContent => onChange(newContent)}
      />
    </div>
  );
};

export default PDFRichTextInput; 