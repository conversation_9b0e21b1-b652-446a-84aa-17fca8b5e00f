{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\jira-old\\\\CTB-Frontend\\\\src\\\\pages\\\\automated-report\\\\components\\\\sections\\\\CoverPage.tsx\";\nimport React from 'react';\nimport { Page, View, Text, Image } from '@react-pdf/renderer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FONT_SIZES = {\n  title: 20,\n  company: 16,\n  meta: 12\n};\nconst LINE_HEIGHT = 1.4;\nconst DEFAULT_LOGO = 'https://i.postimg.cc/QVPV9d0x/Full-name-logo-blue.png';\nconst DEFAULT_COMPANY = 'Capture The Bug Ltd.';\nconst CoverPage = ({\n  reportData\n}) => /*#__PURE__*/_jsxDEV(Page, {\n  size: \"A4\",\n  style: {\n    flexDirection: 'column',\n    justifyContent: 'space-between',\n    minHeight: '100%',\n    padding: '20mm 15mm',\n    position: 'relative',\n    fontFamily: 'Helvetica',\n    backgroundColor: '#ffffff'\n  },\n  children: [/*#__PURE__*/_jsxDEV(View, {\n    style: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      zIndex: -1\n    },\n    children: /*#__PURE__*/_jsxDEV(Image, {\n      src: \"https://i.postimg.cc/fLy3KCcw/VAPT-Cover-Page.png\",\n      style: {\n        width: '100%',\n        height: '100%',\n        objectFit: 'cover'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(View, {\n    style: {\n      paddingTop: 32,\n      paddingLeft: 24,\n      marginBottom: 0\n    },\n    children: /*#__PURE__*/_jsxDEV(Image, {\n      src: reportData.branding_logo || DEFAULT_LOGO,\n      style: {\n        width: 256,\n        height: 64,\n        objectFit: 'contain',\n        objectPosition: 'left'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(View, {\n    style: {\n      flex: 1,\n      justifyContent: 'flex-start',\n      alignItems: 'flex-start',\n      paddingLeft: 32,\n      paddingRight: 32,\n      marginTop: 60\n    },\n    children: /*#__PURE__*/_jsxDEV(View, {\n      style: {\n        alignItems: 'flex-start',\n        maxWidth: '85%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          fontSize: FONT_SIZES.title,\n          fontWeight: 700,\n          marginBottom: 24,\n          color: '#2563eb',\n          textAlign: 'left',\n          lineHeight: LINE_HEIGHT,\n          maxWidth: '90%'\n        },\n        children: reportData.report_title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          fontSize: FONT_SIZES.company,\n          fontWeight: 700,\n          marginBottom: 16,\n          color: '#2563eb',\n          textAlign: 'left',\n          textTransform: 'uppercase',\n          letterSpacing: 1,\n          lineHeight: LINE_HEIGHT\n        },\n        children: reportData.program_name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          fontSize: FONT_SIZES.meta,\n          fontWeight: 400,\n          color: '#2563eb',\n          textAlign: 'left',\n          marginBottom: 0,\n          lineHeight: LINE_HEIGHT\n        },\n        children: [\"Version \", reportData.version_number]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(View, {\n    style: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-end',\n      marginTop: 80,\n      paddingHorizontal: 32,\n      paddingBottom: 32\n    },\n    children: [/*#__PURE__*/_jsxDEV(View, {\n      style: {\n        flexDirection: 'column'\n      },\n      children: /*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          color: '#2563eb',\n          fontSize: FONT_SIZES.meta,\n          fontWeight: 400,\n          marginBottom: 0,\n          marginTop: 32,\n          lineHeight: LINE_HEIGHT\n        },\n        children: [\"Date: \", reportData.current_date]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(View, {\n      style: {\n        alignItems: 'flex-end'\n      },\n      children: /*#__PURE__*/_jsxDEV(Image, {\n        src: reportData.branding_logo || DEFAULT_LOGO,\n        style: {\n          width: 160,\n          height: 40,\n          marginBottom: 0,\n          objectFit: 'contain',\n          objectPosition: 'center',\n          opacity: 0.4\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 21,\n  columnNumber: 3\n}, this);\n_c = CoverPage;\nexport default CoverPage;\nvar _c;\n$RefreshReg$(_c, \"CoverPage\");", "map": {"version": 3, "names": ["React", "Page", "View", "Text", "Image", "jsxDEV", "_jsxDEV", "FONT_SIZES", "title", "company", "meta", "LINE_HEIGHT", "DEFAULT_LOGO", "DEFAULT_COMPANY", "CoverPage", "reportData", "size", "style", "flexDirection", "justifyContent", "minHeight", "padding", "position", "fontFamily", "backgroundColor", "children", "top", "left", "right", "bottom", "zIndex", "src", "width", "height", "objectFit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "paddingTop", "paddingLeft", "marginBottom", "branding_logo", "objectPosition", "flex", "alignItems", "paddingRight", "marginTop", "max<PERSON><PERSON><PERSON>", "fontSize", "fontWeight", "color", "textAlign", "lineHeight", "report_title", "textTransform", "letterSpacing", "program_name", "version_number", "paddingHorizontal", "paddingBottom", "current_date", "opacity", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/jira-old/CTB-Frontend/src/pages/automated-report/components/sections/CoverPage.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Page, View, Text, Image } from '@react-pdf/renderer';\r\nimport { ReportData } from '../../types/report.types';\r\n\r\ninterface CoverPageProps {\r\n  reportData: ReportData;\r\n}\r\n\r\nconst FONT_SIZES = {\r\n  title: 20,\r\n  company: 16,\r\n  meta: 12,\r\n};\r\n\r\nconst LINE_HEIGHT = 1.4;\r\n\r\nconst DEFAULT_LOGO = 'https://i.postimg.cc/QVPV9d0x/Full-name-logo-blue.png';\r\nconst DEFAULT_COMPANY = 'Capture The Bug Ltd.';\r\n\r\nconst CoverPage: React.FC<CoverPageProps> = ({ reportData }) => (\r\n  <Page size=\"A4\" style={{\r\n    flexDirection: 'column',\r\n    justifyContent: 'space-between',\r\n    minHeight: '100%',\r\n    padding: '20mm 15mm',\r\n    position: 'relative',\r\n    fontFamily: 'Helvetica',\r\n    backgroundColor: '#ffffff',\r\n  }}>\r\n    {/* Background Image as a separate element */}\r\n    <View style={{\r\n      position: 'absolute',\r\n      top: 0,\r\n      left: 0,\r\n      right: 0,\r\n      bottom: 0,\r\n      zIndex: -1,\r\n    }}>\r\n      <Image\r\n        src=\"https://i.postimg.cc/fLy3KCcw/VAPT-Cover-Page.png\"\r\n        style={{\r\n          width: '100%',\r\n          height: '100%',\r\n          objectFit: 'cover',\r\n        }}\r\n      />\r\n    </View>\r\n    {/* Top Logo */}\r\n    <View style={{\r\n      paddingTop: 32,\r\n      paddingLeft: 24,\r\n      marginBottom: 0,\r\n    }}>\r\n      <Image\r\n        src={reportData.branding_logo || DEFAULT_LOGO}\r\n        style={{\r\n          width: 256,\r\n          height: 64,\r\n          objectFit: 'contain',\r\n          objectPosition: 'left',\r\n        }}\r\n      />\r\n    </View>\r\n    {/* Middle Content */}\r\n    <View style={{\r\n      flex: 1,\r\n      justifyContent: 'flex-start',\r\n      alignItems: 'flex-start',\r\n      paddingLeft: 32,\r\n      paddingRight: 32,\r\n      marginTop: 60,\r\n    }}>\r\n      <View style={{\r\n        alignItems: 'flex-start',\r\n        maxWidth: '85%',\r\n      }}>\r\n        <Text style={{\r\n          fontSize: FONT_SIZES.title,\r\n          fontWeight: 700,\r\n          marginBottom: 24,\r\n          color: '#2563eb',\r\n          textAlign: 'left',\r\n          lineHeight: LINE_HEIGHT,\r\n          maxWidth: '90%',\r\n        }}>\r\n          {reportData.report_title}\r\n        </Text>\r\n        <Text style={{\r\n          fontSize: FONT_SIZES.company,\r\n          fontWeight: 700,\r\n          marginBottom: 16,\r\n          color: '#2563eb',\r\n          textAlign: 'left',\r\n          textTransform: 'uppercase',\r\n          letterSpacing: 1,\r\n          lineHeight: LINE_HEIGHT,\r\n        }}>\r\n          {reportData.program_name}\r\n        </Text>\r\n        <Text style={{\r\n          fontSize: FONT_SIZES.meta,\r\n          fontWeight: 400,\r\n          color: '#2563eb',\r\n          textAlign: 'left',\r\n          marginBottom: 0,\r\n          lineHeight: LINE_HEIGHT,\r\n        }}>\r\n          Version {reportData.version_number}\r\n        </Text>\r\n      </View>\r\n    </View>\r\n    {/* Bottom Section */}\r\n    <View style={{\r\n      flexDirection: 'row',\r\n      justifyContent: 'space-between',\r\n      alignItems: 'flex-end',\r\n      marginTop: 80,\r\n      paddingHorizontal: 32,\r\n      paddingBottom: 32,\r\n    }}>\r\n      <View style={{\r\n        flexDirection: 'column',\r\n      }}>\r\n        <Text style={{\r\n          color: '#2563eb',\r\n          fontSize: FONT_SIZES.meta,\r\n          fontWeight: 400,\r\n          marginBottom: 0,\r\n          marginTop: 32,\r\n          lineHeight: LINE_HEIGHT,\r\n        }}>\r\n          Date: {reportData.current_date}\r\n        </Text>\r\n      </View>\r\n      <View style={{\r\n        alignItems: 'flex-end',\r\n      }}>\r\n        <Image\r\n          src={reportData.branding_logo || DEFAULT_LOGO}\r\n          style={{\r\n            width: 160,\r\n            height: 40,\r\n            marginBottom: 0,\r\n            objectFit: 'contain',\r\n            objectPosition: 'center',\r\n            opacity: 0.4,\r\n          }}\r\n        />\r\n      </View>\r\n    </View>\r\n  </Page>\r\n);\r\n\r\nexport default CoverPage; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAO9D,MAAMC,UAAU,GAAG;EACjBC,KAAK,EAAE,EAAE;EACTC,OAAO,EAAE,EAAE;EACXC,IAAI,EAAE;AACR,CAAC;AAED,MAAMC,WAAW,GAAG,GAAG;AAEvB,MAAMC,YAAY,GAAG,uDAAuD;AAC5E,MAAMC,eAAe,GAAG,sBAAsB;AAE9C,MAAMC,SAAmC,GAAGA,CAAC;EAAEC;AAAW,CAAC,kBACzDT,OAAA,CAACL,IAAI;EAACe,IAAI,EAAC,IAAI;EAACC,KAAK,EAAE;IACrBC,aAAa,EAAE,QAAQ;IACvBC,cAAc,EAAE,eAAe;IAC/BC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,WAAW;IACpBC,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,WAAW;IACvBC,eAAe,EAAE;EACnB,CAAE;EAAAC,QAAA,gBAEAnB,OAAA,CAACJ,IAAI;IAACe,KAAK,EAAE;MACXK,QAAQ,EAAE,UAAU;MACpBI,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;IACX,CAAE;IAAAL,QAAA,eACAnB,OAAA,CAACF,KAAK;MACJ2B,GAAG,EAAC,mDAAmD;MACvDd,KAAK,EAAE;QACLe,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,SAAS,EAAE;MACb;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC,eAEPhC,OAAA,CAACJ,IAAI;IAACe,KAAK,EAAE;MACXsB,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE;IAChB,CAAE;IAAAhB,QAAA,eACAnB,OAAA,CAACF,KAAK;MACJ2B,GAAG,EAAEhB,UAAU,CAAC2B,aAAa,IAAI9B,YAAa;MAC9CK,KAAK,EAAE;QACLe,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,EAAE;QACVC,SAAS,EAAE,SAAS;QACpBS,cAAc,EAAE;MAClB;IAAE;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC,eAEPhC,OAAA,CAACJ,IAAI;IAACe,KAAK,EAAE;MACX2B,IAAI,EAAE,CAAC;MACPzB,cAAc,EAAE,YAAY;MAC5B0B,UAAU,EAAE,YAAY;MACxBL,WAAW,EAAE,EAAE;MACfM,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAE;IAAAtB,QAAA,eACAnB,OAAA,CAACJ,IAAI;MAACe,KAAK,EAAE;QACX4B,UAAU,EAAE,YAAY;QACxBG,QAAQ,EAAE;MACZ,CAAE;MAAAvB,QAAA,gBACAnB,OAAA,CAACH,IAAI;QAACc,KAAK,EAAE;UACXgC,QAAQ,EAAE1C,UAAU,CAACC,KAAK;UAC1B0C,UAAU,EAAE,GAAG;UACfT,YAAY,EAAE,EAAE;UAChBU,KAAK,EAAE,SAAS;UAChBC,SAAS,EAAE,MAAM;UACjBC,UAAU,EAAE1C,WAAW;UACvBqC,QAAQ,EAAE;QACZ,CAAE;QAAAvB,QAAA,EACCV,UAAU,CAACuC;MAAY;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eACPhC,OAAA,CAACH,IAAI;QAACc,KAAK,EAAE;UACXgC,QAAQ,EAAE1C,UAAU,CAACE,OAAO;UAC5ByC,UAAU,EAAE,GAAG;UACfT,YAAY,EAAE,EAAE;UAChBU,KAAK,EAAE,SAAS;UAChBC,SAAS,EAAE,MAAM;UACjBG,aAAa,EAAE,WAAW;UAC1BC,aAAa,EAAE,CAAC;UAChBH,UAAU,EAAE1C;QACd,CAAE;QAAAc,QAAA,EACCV,UAAU,CAAC0C;MAAY;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eACPhC,OAAA,CAACH,IAAI;QAACc,KAAK,EAAE;UACXgC,QAAQ,EAAE1C,UAAU,CAACG,IAAI;UACzBwC,UAAU,EAAE,GAAG;UACfC,KAAK,EAAE,SAAS;UAChBC,SAAS,EAAE,MAAM;UACjBX,YAAY,EAAE,CAAC;UACfY,UAAU,EAAE1C;QACd,CAAE;QAAAc,QAAA,GAAC,UACO,EAACV,UAAU,CAAC2C,cAAc;MAAA;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC,eAEPhC,OAAA,CAACJ,IAAI;IAACe,KAAK,EAAE;MACXC,aAAa,EAAE,KAAK;MACpBC,cAAc,EAAE,eAAe;MAC/B0B,UAAU,EAAE,UAAU;MACtBE,SAAS,EAAE,EAAE;MACbY,iBAAiB,EAAE,EAAE;MACrBC,aAAa,EAAE;IACjB,CAAE;IAAAnC,QAAA,gBACAnB,OAAA,CAACJ,IAAI;MAACe,KAAK,EAAE;QACXC,aAAa,EAAE;MACjB,CAAE;MAAAO,QAAA,eACAnB,OAAA,CAACH,IAAI;QAACc,KAAK,EAAE;UACXkC,KAAK,EAAE,SAAS;UAChBF,QAAQ,EAAE1C,UAAU,CAACG,IAAI;UACzBwC,UAAU,EAAE,GAAG;UACfT,YAAY,EAAE,CAAC;UACfM,SAAS,EAAE,EAAE;UACbM,UAAU,EAAE1C;QACd,CAAE;QAAAc,QAAA,GAAC,QACK,EAACV,UAAU,CAAC8C,YAAY;MAAA;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACPhC,OAAA,CAACJ,IAAI;MAACe,KAAK,EAAE;QACX4B,UAAU,EAAE;MACd,CAAE;MAAApB,QAAA,eACAnB,OAAA,CAACF,KAAK;QACJ2B,GAAG,EAAEhB,UAAU,CAAC2B,aAAa,IAAI9B,YAAa;QAC9CK,KAAK,EAAE;UACLe,KAAK,EAAE,GAAG;UACVC,MAAM,EAAE,EAAE;UACVQ,YAAY,EAAE,CAAC;UACfP,SAAS,EAAE,SAAS;UACpBS,cAAc,EAAE,QAAQ;UACxBmB,OAAO,EAAE;QACX;MAAE;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACP;AAACyB,EAAA,GApIIjD,SAAmC;AAsIzC,eAAeA,SAAS;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}