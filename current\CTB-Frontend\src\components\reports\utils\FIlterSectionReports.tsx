import React, { useState, useMemo, useRef, useEffect } from "react";
import { CTBReport } from "../../../utils/api/endpoints/reports/parseReports";
import { BiSortAlt2, Bi<PERSON>ilter } from "react-icons/bi";
import {
  FaAngleDown,
  FaSearch,
  FaTimes,
  FaFilter,
  FaProjectDiagram,
  FaClipboardList,
  FaExclamationTriangle,
  FaUserShield,
  FaSort,
  FaSortAmountDown,
  FaSortAmountUp,
  FaColumns
} from "react-icons/fa";
import { IoMdClose } from "react-icons/io";
import useUserCredentials from "../../../utils/hooks/user/useUserCredentials";
import { UserRole } from "../../../utils/api/endpoints/user/credentials";
import { cropSentence } from "../../../utils/formatText";
import OutlineButton from "../../buttons/OutlineButton";
import { useNavigate } from "react-router-dom";
import { FiColumns } from "react-icons/fi";
import { FiChevronDown } from "react-icons/fi";

// Define modal types as literal types
const MODAL_TYPES = {
  STATUS: "status",
  PENTESTER: "pentester",
  NONE: "none"
} as const;

// Create a type from the literal values
type ModalType = (typeof MODAL_TYPES)[keyof typeof MODAL_TYPES];

// Define the modal state type
interface ModalState {
  type: ModalType;
}

// Type guard functions
const isStatusModal = (
  state: ModalState
): state is ModalState & { type: typeof MODAL_TYPES.STATUS } => {
  return state.type === MODAL_TYPES.STATUS;
};

const isPentesterModal = (
  state: ModalState
): state is ModalState & { type: typeof MODAL_TYPES.PENTESTER } => {
  return state.type === MODAL_TYPES.PENTESTER;
};

const isNoneModal = (
  state: ModalState
): state is ModalState & { type: typeof MODAL_TYPES.NONE } => {
  return state.type === MODAL_TYPES.NONE;
};

// Add ColumnVisibility type
type ColumnVisibility = {
  createdAt: boolean;
  pentester: boolean;
  action: boolean;
};

// Update the filters state types
interface FilterState {
  programs: string[];
  states: string[];
  severityCategories: string[];
  pentesters: string[];
}

interface FilterSectionReportsProps {
  reports: CTBReport[];
  programNames: { id: number; name: string }[];
  vulnerabilityTypes: string[];
  reportStates: string[];
  severityCategories: string[];
  pentesters: string[];
  filters: {
    searchQuery: string;
    program: string;
    reportState: string;
    severityCategory: string;
    vulnerabilityType: string;
    sortOrder: string;
    pentesterUsername: string;
  };
  onFilter: (filters: {
    searchQuery: string;
    program: string;
    reportState: string;
    severityCategory: string;
    vulnerabilityType: string;
    sortOrder: string;
    pentesterUsername: string;
  }) => void;
  columnVisibility: ColumnVisibility;
  onColumnVisibilityChange: (visibility: ColumnVisibility) => void;
}

const FilterSectionReports: React.FC<FilterSectionReportsProps> = ({
  reports,
  programNames,
  vulnerabilityTypes,
  reportStates,
  severityCategories,
  pentesters,
  filters,
  onFilter,
  columnVisibility,
  onColumnVisibilityChange
}) => {
  // Initialize state with NONE type
  const [activeModal, setActiveModal] = useState<ModalState>({
    type: MODAL_TYPES.NONE
  });
  const [searchQuery, setSearchQuery] = useState(filters.searchQuery);
  const [programId, setProgramId] = useState(filters.program);
  const [programName, setProgramName] = useState("");
  const [reportState, setReportState] = useState<string[]>(
    filters.reportState ? filters.reportState.split(",") : []
  );
  const [severityCategory, setSeverityCategory] = useState(
    filters.severityCategory
  );
  const [vulnerabilityType, setVulnerabilityType] = useState(
    filters.vulnerabilityType
  );
  const [sortOrder, setSortOrder] = useState(filters.sortOrder);
  const [pentesterUsername, setPentesterUsername] = useState(
    filters.pentesterUsername
  );
  const [pentesterSearchQuery, setPentesterSearchQuery] = useState("");
  const [tempProgramId, setTempProgramId] = useState(filters.program);
  const [tempProgramName, setTempProgramName] = useState(
    programNames.find(p => p.id.toString() === filters.program)?.name || ""
  );
  const [tempReportState, setTempReportState] = useState<string[]>(
    filters.reportState ? filters.reportState.split(",") : []
  );
  const [isProgramsOpen, setIsProgramsOpen] = useState(false);
  const [tempPentesterUsername, setTempPentesterUsername] = useState(
    filters.pentesterUsername
  );
  const [isStatesOpen, setIsStatesOpen] = useState(false);
  const [programSearchQuery, setProgramSearchQuery] = useState("");
  const { role } = useUserCredentials();
  const [isColumnMenuOpen, setIsColumnMenuOpen] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState<FilterState>({
    programs: filters.program ? [filters.program] : [],
    states: filters.reportState ? filters.reportState.split(",") : [],
    severityCategories: filters.severityCategory
      ? [filters.severityCategory]
      : [],
    pentesters: filters.pentesterUsername ? [filters.pentesterUsername] : []
  });

  const statusModalRef = useRef<HTMLDivElement>(null);
  const substatusModalRef = useRef<HTMLDivElement>(null);
  const pentesterModalRef = useRef<HTMLDivElement>(null);
  const togglePrograms = () => setIsProgramsOpen(!isProgramsOpen);
  const toggleStates = () => setIsStatesOpen(!isStatesOpen);

  // Use the filter options from the API
  const uniqueReportStates = reportStates;
  const uniqueSeverityCategories = severityCategories;
  const uniquePentesters = pentesters;

  const filteredPentesters = useMemo(() => {
    return uniquePentesters.filter(
      username =>
        username &&
        username.toLowerCase().includes(pentesterSearchQuery.toLowerCase())
    );
  }, [uniquePentesters, pentesterSearchQuery]);

  const [openSection, setOpenSection] = useState<string | null>(null);

  // Add refs for all dropdowns
  const programsDropdownRef = useRef<HTMLDivElement>(null);
  const statesDropdownRef = useRef<HTMLDivElement>(null);
  const severityDropdownRef = useRef<HTMLDivElement>(null);
  const pentestersDropdownRef = useRef<HTMLDivElement>(null);
  const columnsDropdownRef = useRef<HTMLDivElement>(null);
  const buttonRefs = useRef<{ [key: string]: HTMLButtonElement | null }>({});

  // Handle click outside for all dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Handle status modal
      if (
        statusModalRef.current &&
        !statusModalRef.current.contains(event.target as Node)
      ) {
        setActiveModal({ type: MODAL_TYPES.NONE });
      }

      // Handle all other dropdowns
      const dropdownRefs = {
        programs: programsDropdownRef,
        states: statesDropdownRef,
        severity: severityDropdownRef,
        pentesters: pentestersDropdownRef,
        columns: columnsDropdownRef
      };

      const clickedButton = Object.entries(buttonRefs.current).find(
        ([_, button]) => button?.contains(event.target as Node)
      );

      if (!clickedButton) {
        Object.entries(dropdownRefs).forEach(([key, ref]) => {
          if (ref.current && !ref.current.contains(event.target as Node)) {
            if (key === "columns") {
              setIsColumnMenuOpen(false);
            } else {
              setOpenSection(prev => (prev === key ? null : prev));
            }
          }
        });
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Sync temporary states with applied filters
  useEffect(() => {
    setTempProgramId(filters.program);
    setTempProgramName(
      programNames.find(p => p.id.toString() === filters.program)?.name || ""
    );
    setTempReportState(
      filters.reportState ? filters.reportState.split(",") : []
    );
    setTempPentesterUsername(filters.pentesterUsername);
  }, [filters, programNames]);

  const toggleModal = (
    modalType: typeof MODAL_TYPES.STATUS | typeof MODAL_TYPES.PENTESTER
  ) => {
    setActiveModal(
      activeModal.type === modalType
        ? { type: MODAL_TYPES.NONE }
        : { type: modalType }
    );
    if (modalType === MODAL_TYPES.PENTESTER) {
      setPentesterSearchQuery("");
    }
  };

  // Update filter handlers to support multi-select
  const handleProgramToggle = (programName: string) => {
    const selectedProgram = programNames.find(p => p.name === programName);
    const programId = selectedProgram ? selectedProgram.id.toString() : "";

    setSelectedFilters(prev => {
      const newPrograms = prev.programs.includes(programId)
        ? prev.programs.filter(id => id !== programId)
        : [...prev.programs, programId];

      const newFilters = { ...prev, programs: newPrograms };

      // Apply filters immediately
      onFilter({
        searchQuery,
        program: newPrograms.join(","),
        reportState: prev.states.join(","),
        severityCategory: prev.severityCategories.join(","),
        vulnerabilityType,
        sortOrder,
        pentesterUsername: prev.pentesters.join(",")
      });

      return newFilters;
    });
  };

  const handleStateToggle = (state: string) => {
    setSelectedFilters(prev => {
      const newStates = prev.states.includes(state)
        ? prev.states.filter(s => s !== state)
        : [...prev.states, state];

      const newFilters = { ...prev, states: newStates };

      // Apply filters immediately
      onFilter({
        searchQuery,
        program: prev.programs.join(","),
        reportState: newStates.join(","),
        severityCategory: prev.severityCategories.join(","),
        vulnerabilityType,
        sortOrder,
        pentesterUsername: prev.pentesters.join(",")
      });

      return newFilters;
    });
  };

  const handleSeverityToggle = (category: string) => {
    setSelectedFilters(prev => {
      const newCategories = prev.severityCategories.includes(category)
        ? prev.severityCategories.filter(c => c !== category)
        : [...prev.severityCategories, category];

      const newFilters = { ...prev, severityCategories: newCategories };

      // Apply filters immediately
      onFilter({
        searchQuery,
        program: prev.programs.join(","),
        reportState: prev.states.join(","),
        severityCategory: newCategories.join(","),
        vulnerabilityType,
        sortOrder,
        pentesterUsername: prev.pentesters.join(",")
      });

      return newFilters;
    });
  };

  const handlePentesterToggle = (username: string) => {
    setSelectedFilters(prev => {
      const newPentesters = prev.pentesters.includes(username)
        ? prev.pentesters.filter(p => p !== username)
        : [...prev.pentesters, username];

      const newFilters = { ...prev, pentesters: newPentesters };

      // Apply filters immediately
      onFilter({
        searchQuery,
        program: prev.programs.join(","),
        reportState: prev.states.join(","),
        severityCategory: prev.severityCategories.join(","),
        vulnerabilityType,
        sortOrder,
        pentesterUsername: newPentesters.join(",")
      });

      return newFilters;
    });
  };

  // Update apply filter function
  const handleApplyFilter = () => {
    onFilter({
      searchQuery,
      program: selectedFilters.programs.join(","),
      reportState: selectedFilters.states.join(","),
      severityCategory: selectedFilters.severityCategories.join(","),
      vulnerabilityType,
      sortOrder,
      pentesterUsername: selectedFilters.pentesters.join(",")
    });
    setActiveModal({ type: MODAL_TYPES.NONE });
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newSearchQuery = event.target.value;
    setSearchQuery(newSearchQuery);
    onFilter({
      searchQuery: newSearchQuery,
      program: selectedFilters.programs.join(","),
      reportState: selectedFilters.states.join(","),
      severityCategory: selectedFilters.severityCategories.join(","),
      vulnerabilityType,
      sortOrder,
      pentesterUsername: selectedFilters.pentesters.join(",")
    });
  };

  const handlePentesterChange = (username: string) => {
    setTempPentesterUsername(username);
  };

  const handleSeverityCategoryChange = (
    event: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const newSeverityCategory = event.target.value;
    setSeverityCategory(newSeverityCategory);
    onFilter({
      searchQuery,
      program: programId,
      reportState: reportState.join(","),
      severityCategory: newSeverityCategory,
      vulnerabilityType,
      sortOrder,
      pentesterUsername
    });
  };

  const filteredPrograms = useMemo(() => {
    return programNames.filter(program =>
      program.name.toLowerCase().includes(programSearchQuery.toLowerCase())
    );
  }, [programNames, programSearchQuery]);

  const handleSortOrderChange = () => {
    const newSortOrder = sortOrder === "asc" ? "desc" : "asc";
    setSortOrder(newSortOrder);
    onFilter({
      searchQuery,
      program: programId,
      reportState: reportState.join(","),
      severityCategory,
      vulnerabilityType,
      sortOrder: newSortOrder,
      pentesterUsername
    });
  };

  // Update clear filters function
  const handleClearAll = () => {
    setSelectedFilters({
      programs: [],
      states: [],
      severityCategories: [],
      pentesters: []
    });
    setSearchQuery("");
    setVulnerabilityType("");
    setSortOrder("desc");
    setActiveModal({ type: MODAL_TYPES.NONE });

    onFilter({
      searchQuery: "",
      program: "",
      reportState: "",
      severityCategory: "",
      vulnerabilityType: "",
      sortOrder: "desc",
      pentesterUsername: ""
    });
  };

  const navigate = useNavigate();

  const handleClearSelected = () => {
    setTempProgramId("");
    setTempProgramName("");
    setTempReportState([]);
    setProgramId("");
    setProgramName("");
    setReportState([]);

    onFilter({
      searchQuery,
      program: "",
      reportState: "",
      severityCategory,
      vulnerabilityType,
      sortOrder,
      pentesterUsername
    });
  };

  const handleColumnVisibilityChange = (column: keyof ColumnVisibility) => {
    const newVisibility = { ...columnVisibility };
    const currentVisibleCount =
      Object.values(columnVisibility).filter(Boolean).length;

    // If trying to show a column
    if (!columnVisibility[column]) {
      // Only allow if we have less than 2 visible columns
      if (currentVisibleCount < 2) {
        newVisibility[column] = true;
        onColumnVisibilityChange(newVisibility);
      }
    } else {
      // If trying to hide a column, just hide it
      newVisibility[column] = false;
      onColumnVisibilityChange(newVisibility);
    }
  };

  // Update toggleSection to handle dropdown state
  const toggleSection = (sectionName: string) => {
    setOpenSection(prev => {
      // If clicking the same section, close it
      if (prev === sectionName) return null;
      // Otherwise, open the new section (closing any other open section)
      return sectionName;
    });
  };

  // Helper to get dropdown z-index based on which one is open
  const getDropdownZIndex = (sectionName: string) => {
    return openSection === sectionName ? "z-50" : "z-40";
  };

  // Update the dropdown buttons to use refs
  const setButtonRef = (name: string, element: HTMLButtonElement | null) => {
    buttonRefs.current[name] = element;
  };

  // Update the status modal content
  const renderStatusModal = () => {
    if (!isStatusModal(activeModal)) return null;

    return (
      <>
        <span className="ml-1 inline-flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-600">
          {Object.values(selectedFilters).flat().filter(Boolean).length}
        </span>
        <div
          ref={statusModalRef}
          className="absolute right-0 top-full z-20 mt-2 w-[420px] rounded-lg border border-gray-200 bg-white shadow-lg"
        >
          {/* Header */}
          <div className="border-b border-gray-200 p-3">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-900">Filters</h3>
              <div className="flex items-center gap-2">
                <button
                  onClick={handleClearAll}
                  className="rounded-md px-2 py-1 text-xs text-gray-600 hover:bg-gray-100"
                >
                  Clear All
                </button>
                <button
                  onClick={() => setActiveModal({ type: MODAL_TYPES.NONE })}
                  className="rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                >
                  <IoMdClose className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Filter Content */}
          <div className="max-h-[calc(100vh-50px)] overflow-y-auto p-3">
            {/* Search Bar - Always visible at the top */}
            <div className="mb-3">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search reports..."
                  value={searchQuery}
                  onChange={handleSearch}
                  className="block w-full rounded-md border border-gray-300 bg-white py-1.5 pl-8 pr-3 text-sm placeholder-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
                <FaSearch className="absolute left-2.5 top-1/2 h-3.5 w-3.5 -translate-y-1/2 transform text-gray-400" />
              </div>
            </div>

            {/* Filter Sections */}
            <div className="space-y-2">
              {/* Programs Section */}
              <div className="rounded-md border border-gray-200 bg-white">
                <button
                  ref={el => setButtonRef("programs", el)}
                  onClick={() => toggleSection("programs")}
                  className="flex w-full items-center justify-between border-b border-gray-100 px-3 py-2 hover:bg-gray-50"
                >
                  <div className="flex items-center gap-2">
                    <h4 className="text-xs font-medium text-gray-700">
                      Programs
                    </h4>
                    {selectedFilters.programs.length > 0 && (
                      <span className="inline-flex h-4 w-4 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-600">
                        {selectedFilters.programs.length}
                      </span>
                    )}
                  </div>
                  <FaAngleDown
                    className={`h-3 w-3 transform text-gray-400 transition-transform duration-200 ${
                      openSection === "programs" ? "rotate-180" : ""
                    }`}
                  />
                </button>
                {openSection === "programs" && (
                  <div className="p-2">
                    <div className="relative mb-2">
                      <input
                        type="text"
                        placeholder="Search programs..."
                        value={programSearchQuery}
                        onChange={e => setProgramSearchQuery(e.target.value)}
                        className="block w-full rounded-md border border-gray-300 bg-white py-1 pl-7 pr-2 text-xs placeholder-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      />
                      <FaSearch className="absolute left-2 top-1/2 h-3 w-3 -translate-y-1/2 transform text-gray-400" />
                    </div>
                    <div className="max-h-48 space-y-0.5 overflow-y-auto">
                      {filteredPrograms.map(program => (
                        <label
                          key={program.id}
                          className="flex items-center gap-2 rounded px-2 py-1 text-xs hover:bg-gray-50"
                        >
                          <input
                            type="checkbox"
                            checked={selectedFilters.programs.includes(
                              program.id.toString()
                            )}
                            onChange={() => handleProgramToggle(program.name)}
                            className="h-3 w-3 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <span>{cropSentence(program.name, 24)}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* States Section */}
              <div className="rounded-md border border-gray-200 bg-white">
                <button
                  ref={el => setButtonRef("states", el)}
                  onClick={() => toggleSection("states")}
                  className="flex w-full items-center justify-between border-b border-gray-100 px-3 py-2 hover:bg-gray-50"
                >
                  <div className="flex items-center gap-2">
                    <h4 className="text-xs font-medium text-gray-700">
                      States
                    </h4>
                    {selectedFilters.states.length > 0 && (
                      <span className="inline-flex h-4 w-4 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-600">
                        {selectedFilters.states.length}
                      </span>
                    )}
                  </div>
                  <FaAngleDown
                    className={`h-3 w-3 transform text-gray-400 transition-transform duration-200 ${
                      openSection === "states" ? "rotate-180" : ""
                    }`}
                  />
                </button>
                {openSection === "states" && (
                  <div
                    ref={statesDropdownRef}
                    className={`absolute left-0 top-full ${getDropdownZIndex(
                      "states"
                    )} mt-1 w-48 rounded-lg border border-gray-200 bg-white shadow-lg`}
                  >
                    <div className="p-2">
                      <div className="max-h-48 space-y-0.5 overflow-y-auto">
                        {uniqueReportStates.map(state => (
                          <label
                            key={state}
                            className="flex items-center gap-2 rounded px-2 py-1 text-xs hover:bg-gray-50"
                          >
                            <input
                              type="checkbox"
                              checked={selectedFilters.states.includes(
                                state || ""
                              )}
                              onChange={() => handleStateToggle(state || "")}
                              className="h-3 w-3 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <span>{state}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Severity Section */}
              <div className="rounded-md border border-gray-200 bg-white">
                <button
                  ref={el => setButtonRef("severity", el)}
                  onClick={() => toggleSection("severity")}
                  className="flex w-full items-center justify-between border-b border-gray-100 px-3 py-2 hover:bg-gray-50"
                >
                  <div className="flex items-center gap-2">
                    <h4 className="text-xs font-medium text-gray-700">
                      Severity
                    </h4>
                    {selectedFilters.severityCategories.length > 0 && (
                      <span className="inline-flex h-4 w-4 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-600">
                        {selectedFilters.severityCategories.length}
                      </span>
                    )}
                  </div>
                  <FaAngleDown
                    className={`h-3 w-3 transform text-gray-400 transition-transform duration-200 ${
                      openSection === "severity" ? "rotate-180" : ""
                    }`}
                  />
                </button>
                {openSection === "severity" && (
                  <div
                    ref={severityDropdownRef}
                    className={`absolute left-0 top-full ${getDropdownZIndex(
                      "severity"
                    )} mt-1 w-48 rounded-lg border border-gray-200 bg-white shadow-lg`}
                  >
                    <div className="p-2">
                      <div className="max-h-48 space-y-0.5 overflow-y-auto">
                        {uniqueSeverityCategories.map(category => (
                          <label
                            key={category}
                            className="flex items-center gap-2 rounded px-2 py-1 text-xs hover:bg-gray-50"
                          >
                            <input
                              type="checkbox"
                              checked={selectedFilters.severityCategories.includes(
                                category || ""
                              )}
                              onChange={() =>
                                handleSeverityToggle(category || "")
                              }
                              className="h-3 w-3 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <span>{category}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Pentester Section */}
              {role === UserRole.ADMIN && (
                <div className="rounded-md border border-gray-200 bg-white">
                  <button
                    ref={el => setButtonRef("pentesters", el)}
                    onClick={() => toggleSection("pentesters")}
                    className="flex w-full items-center justify-between border-b border-gray-100 px-3 py-2 hover:bg-gray-50"
                  >
                    <div className="flex items-center gap-2">
                      <h4 className="text-xs font-medium text-gray-700">
                        Pentesters
                      </h4>
                      {selectedFilters.pentesters.length > 0 && (
                        <span className="inline-flex h-4 w-4 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-600">
                          {selectedFilters.pentesters.length}
                        </span>
                      )}
                    </div>
                    <FaAngleDown
                      className={`h-3 w-3 transform text-gray-400 transition-transform duration-200 ${
                        openSection === "pentesters" ? "rotate-180" : ""
                      }`}
                    />
                  </button>
                  {openSection === "pentesters" && (
                    <div
                      ref={pentestersDropdownRef}
                      className={`absolute left-0 top-full ${getDropdownZIndex(
                        "pentesters"
                      )} mt-1 w-64 rounded-lg border border-gray-200 bg-white shadow-lg`}
                    >
                      <div className="p-2">
                        <div className="relative mb-2">
                          <input
                            type="text"
                            placeholder="Search pentesters..."
                            value={pentesterSearchQuery}
                            onChange={e =>
                              setPentesterSearchQuery(e.target.value)
                            }
                            className="block w-full rounded-md border border-gray-200 bg-white py-1.5 pl-8 pr-2 text-sm placeholder-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                          />
                          <FaSearch className="absolute left-2.5 top-1/2 h-3.5 w-3.5 -translate-y-1/2 transform text-gray-400" />
                        </div>
                        <div className="max-h-48 space-y-0.5 overflow-y-auto">
                          {filteredPentesters.map(username => (
                            <label
                              key={username}
                              className="flex items-center gap-2 rounded px-2 py-1.5 text-sm hover:bg-gray-50"
                            >
                              <input
                                type="checkbox"
                                checked={selectedFilters.pentesters.includes(
                                  username
                                )}
                                onChange={() => handlePentesterToggle(username)}
                                className="h-3.5 w-3.5 rounded border-gray-200 text-blue-600 focus:ring-blue-500"
                              />
                              <span className="truncate">
                                {cropSentence(username, 23)}
                              </span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Footer with Apply Button */}
          <div className="border-t border-gray-200 bg-gray-50 p-3">
            <button
              onClick={handleApplyFilter}
              className="w-full rounded-md bg-blue-600 px-3 py-1.5 text-xs font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Apply Filters
            </button>
          </div>
        </div>
      </>
    );
  };

  // Update the active filters display to be more compact
  const renderActiveFilters = () => {
    const hasActiveFilters = Object.values(selectedFilters).some(
      filters => filters.length > 0
    );
    if (!hasActiveFilters) return null;

    return (
      <div className="border-b border-gray-200  px-4 py-2">
        <div className="flex flex-wrap items-center gap-1.5">
          <span className="text-xs font-medium text-gray-500">
            Active Filters:
          </span>
          {selectedFilters.programs.map(programId => {
            const program = programNames.find(
              p => p.id.toString() === programId
            );
            return (
              program && (
                <div
                  key={programId}
                  className="inline-flex items-center gap-1 rounded-full bg-blue-100 px-2 py-0.5 text-xs text-blue-700"
                >
                  <span>Program: {cropSentence(program.name, 20)}</span>
                  <button
                    onClick={() => handleProgramToggle(program.name)}
                    className="ml-0.5 rounded-full p-0.5 hover:bg-blue-200"
                  >
                    <FaTimes className="h-2.5 w-2.5" />
                  </button>
                </div>
              )
            );
          })}
          {selectedFilters.states.map(state => (
            <div
              key={state}
              className="inline-flex items-center gap-1 rounded-full bg-blue-100 px-2 py-0.5 text-xs text-blue-700"
            >
              <span>State: {state}</span>
              <button
                onClick={() => handleStateToggle(state)}
                className="ml-0.5 rounded-full p-0.5 hover:bg-blue-200"
              >
                <FaTimes className="h-2.5 w-2.5" />
              </button>
            </div>
          ))}
          {selectedFilters.severityCategories.map(category => (
            <div
              key={category}
              className="inline-flex items-center gap-1 rounded-full bg-blue-100 px-2 py-0.5 text-xs text-blue-700"
            >
              <span>Severity: {category}</span>
              <button
                onClick={() => handleSeverityToggle(category)}
                className="ml-0.5 rounded-full p-0.5 hover:bg-blue-200"
              >
                <FaTimes className="h-2.5 w-2.5" />
              </button>
            </div>
          ))}
          {selectedFilters.pentesters.map(username => (
            <div
              key={username}
              className="inline-flex items-center gap-1 rounded-full bg-blue-100 px-2 py-0.5 text-xs text-blue-700"
            >
              <span>Pentester: {cropSentence(username, 20)}</span>
              <button
                onClick={() => handlePentesterToggle(username)}
                className="ml-0.5 rounded-full p-0.5 hover:bg-blue-200"
              >
                <FaTimes className="h-2.5 w-2.5" />
              </button>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="sticky top-0 z-10 border-b border-gray-200 bg-white">
      {/* Header Section */}
      <div className="px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="text-2xl font-semibold text-gray-900">Reports</h1>
            {Object.values(selectedFilters).some(
              filters => filters.length > 0
            ) && (
              <span className="inline-flex items-center gap-1 rounded-full bg-blue-50 px-2 py-0.5 text-xs font-medium text-blue-700">
                <FaFilter className="h-3 w-3" />
                {Object.values(selectedFilters).flat().length} active filters
              </span>
            )}
          </div>
          {role === UserRole.RESEARCHER && (
            <a
              href="/dashboard/reports/new/blank"
              className="inline-flex items-center gap-1.5 rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white shadow-sm transition-colors hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <svg
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4v16m8-8H4"
                />
              </svg>
              Create New Report
            </a>
          )}
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="bg-gray-50/50 px-4 py-2.5">
        <div className="flex w-full items-center gap-2">
          {/* Search Input */}
          <div className="relative w-64">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-2.5">
              <FaSearch className="h-3.5 w-3.5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search reports..."
              value={searchQuery}
              onChange={handleSearch}
              className="block w-full rounded-md border border-gray-200 bg-white py-1.5 pl-8 pr-3 text-sm placeholder-gray-500 shadow-sm transition-colors focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>

          {/* Filter Sections */}
          <div className="flex flex-1 items-center justify-between gap-2">
            {/* Left side filters */}
            <div className="flex items-center gap-1.5">
              {/* Programs Filter */}
              <div className="relative">
                <button
                  ref={el => setButtonRef("programs", el)}
                  onClick={() => toggleSection("programs")}
                  className={`inline-flex min-w-[110px] items-center justify-between gap-1.5 rounded-md border px-2.5 py-1.5 text-sm font-medium shadow-sm transition-all ${
                    selectedFilters.programs.length > 0
                      ? "border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100"
                      : "border-gray-200 bg-white text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  <div className="flex items-center gap-1.5">
                    <FaProjectDiagram className="h-3.5 w-3.5" />
                    <span>Program</span>
                  </div>
                  <div className="flex items-center gap-1">
                    {selectedFilters.programs.length > 0 && (
                      <span className="inline-flex h-4 w-4 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-600">
                        {selectedFilters.programs.length}
                      </span>
                    )}
                    <FaAngleDown
                      className={`h-3 w-3 transform transition-transform duration-200 ${
                        openSection === "programs" ? "rotate-180" : ""
                      }`}
                    />
                  </div>
                </button>
                {openSection === "programs" && (
                  <div
                    ref={programsDropdownRef}
                    className={`absolute left-0 top-full ${getDropdownZIndex(
                      "programs"
                    )} mt-1 w-64 rounded-md border border-gray-200 bg-white shadow-lg ring-1 ring-black/5`}
                  >
                    <div className="p-2">
                      <div className="relative mb-2">
                        <input
                          type="text"
                          placeholder="Search programs..."
                          value={programSearchQuery}
                          onChange={e => setProgramSearchQuery(e.target.value)}
                          className="block w-full rounded-md border border-gray-200 bg-white py-1.5 pl-8 pr-2 text-sm placeholder-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                        <FaSearch className="absolute left-2.5 top-1/2 h-3.5 w-3.5 -translate-y-1/2 transform text-gray-400" />
                      </div>
                      <div className="max-h-48 space-y-0.5 overflow-y-auto">
                        {filteredPrograms.map(program => (
                          <label
                            key={program.id}
                            className="flex items-center gap-2 rounded px-2 py-1.5 text-sm hover:bg-gray-50"
                          >
                            <input
                              type="checkbox"
                              checked={selectedFilters.programs.includes(
                                program.id.toString()
                              )}
                              onChange={() => handleProgramToggle(program.name)}
                              className="h-3.5 w-3.5 rounded border-gray-200 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="truncate">
                              {cropSentence(program.name, 24)}
                            </span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Report Status Filter */}
              <div className="relative">
                <button
                  ref={el => setButtonRef("states", el)}
                  onClick={() => toggleSection("states")}
                  className={`inline-flex min-w-[100px] items-center justify-between gap-1.5 rounded-md border px-2.5 py-1.5 text-sm font-medium shadow-sm transition-all ${
                    selectedFilters.states.length > 0
                      ? "border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100"
                      : "border-gray-200 bg-white text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  <div className="flex items-center gap-1.5">
                    <FaClipboardList className="h-3.5 w-3.5" />
                    <span>Status</span>
                  </div>
                  <div className="flex items-center gap-1">
                    {selectedFilters.states.length > 0 && (
                      <span className="inline-flex h-4 w-4 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-600">
                        {selectedFilters.states.length}
                      </span>
                    )}
                    <FaAngleDown
                      className={`h-3 w-3 transform transition-transform duration-200 ${
                        openSection === "states" ? "rotate-180" : ""
                      }`}
                    />
                  </div>
                </button>
                {openSection === "states" && (
                  <div
                    ref={statesDropdownRef}
                    className={`absolute left-0 top-full ${getDropdownZIndex(
                      "states"
                    )} mt-1 w-56 rounded-md border border-gray-200 bg-white shadow-lg ring-1 ring-black/5`}
                  >
                    <div className="p-2">
                      <div className="max-h-48 space-y-0.5 overflow-y-auto">
                        {uniqueReportStates.map(state => (
                          <label
                            key={state}
                            className="flex items-center gap-2 rounded px-2 py-1.5 text-sm hover:bg-gray-50"
                          >
                            <input
                              type="checkbox"
                              checked={selectedFilters.states.includes(
                                state || ""
                              )}
                              onChange={() => handleStateToggle(state || "")}
                              className="h-3.5 w-3.5 rounded border-gray-200 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="truncate">{state}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Vulnerability Severity Filter */}
              <div className="relative">
                <button
                  ref={el => setButtonRef("severity", el)}
                  onClick={() => toggleSection("severity")}
                  className={`inline-flex min-w-[110px] items-center justify-between gap-1.5 rounded-md border px-2.5 py-1.5 text-sm font-medium shadow-sm transition-all ${
                    selectedFilters.severityCategories.length > 0
                      ? "border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100"
                      : "border-gray-200 bg-white text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  <div className="flex items-center gap-1.5">
                    <FaExclamationTriangle className="h-3.5 w-3.5" />
                    <span>Severity</span>
                  </div>
                  <div className="flex items-center gap-1">
                    {selectedFilters.severityCategories.length > 0 && (
                      <span className="inline-flex h-4 w-4 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-600">
                        {selectedFilters.severityCategories.length}
                      </span>
                    )}
                    <FaAngleDown
                      className={`h-3 w-3 transform transition-transform duration-200 ${
                        openSection === "severity" ? "rotate-180" : ""
                      }`}
                    />
                  </div>
                </button>
                {openSection === "severity" && (
                  <div
                    ref={severityDropdownRef}
                    className={`absolute left-0 top-full ${getDropdownZIndex(
                      "severity"
                    )} mt-1 w-56 rounded-md border border-gray-200 bg-white shadow-lg ring-1 ring-black/5`}
                  >
                    <div className="p-2">
                      <div className="max-h-48 space-y-0.5 overflow-y-auto">
                        {uniqueSeverityCategories.map(category => (
                          <label
                            key={category}
                            className="flex items-center gap-2 rounded px-2 py-1.5 text-sm hover:bg-gray-50"
                          >
                            <input
                              type="checkbox"
                              checked={selectedFilters.severityCategories.includes(
                                category
                              )}
                              onChange={() => handleSeverityToggle(category)}
                              className="h-3.5 w-3.5 rounded border-gray-200 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="truncate">{category}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
              {/* Pentester Filter - Only for Admin */}
              {(role === UserRole.ADMIN ||
                role === UserRole.SUB_ADMIN ||
                role === UserRole.QA) && (
                <div className="relative">
                  <button
                    ref={el => setButtonRef("pentesters", el)}
                    onClick={() => toggleSection("pentesters")}
                    className={`inline-flex min-w-[110px] items-center justify-between gap-1.5 rounded-md border px-2.5 py-1.5 text-sm font-medium shadow-sm transition-all ${
                      selectedFilters.pentesters.length > 0
                        ? "border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100"
                        : "border-gray-200 bg-white text-gray-700 hover:bg-gray-50"
                    }`}
                  >
                    <div className="flex items-center gap-1.5">
                      <FaUserShield className="h-3.5 w-3.5" />
                      <span>Pentester</span>
                    </div>
                    <div className="flex items-center gap-1">
                      {selectedFilters.pentesters.length > 0 && (
                        <span className="inline-flex h-4 w-4 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-600">
                          {selectedFilters.pentesters.length}
                        </span>
                      )}
                      <FaAngleDown
                        className={`h-3 w-3 transform transition-transform duration-200 ${
                          openSection === "pentesters" ? "rotate-180" : ""
                        }`}
                      />
                    </div>
                  </button>
                  {openSection === "pentesters" && (
                    <div
                      ref={pentestersDropdownRef}
                      className={`absolute right-0 ${getDropdownZIndex(
                        "pentesters"
                      )} mt-1 w-64 rounded-md border border-gray-200 bg-white shadow-lg ring-1 ring-black/5`}
                    >
                      <div className="p-2">
                        <div className="relative mb-2">
                          <input
                            type="text"
                            placeholder="Search pentesters..."
                            value={pentesterSearchQuery}
                            onChange={e =>
                              setPentesterSearchQuery(e.target.value)
                            }
                            className="block w-full rounded-md border border-gray-200 bg-white py-1.5 pl-8 pr-2 text-sm placeholder-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                          />
                          <FaSearch className="absolute left-2.5 top-1/2 h-3.5 w-3.5 -translate-y-1/2 transform text-gray-400" />
                        </div>
                        <div className="max-h-48 space-y-0.5 overflow-y-auto">
                          {filteredPentesters.map(username => (
                            <label
                              key={username}
                              className="flex items-center gap-2 rounded px-2 py-1.5 text-sm hover:bg-gray-50"
                            >
                              <input
                                type="checkbox"
                                checked={selectedFilters.pentesters.includes(
                                  username
                                )}
                                onChange={() => handlePentesterToggle(username)}
                                className="h-3.5 w-3.5 rounded border-gray-200 text-blue-600 focus:ring-blue-500"
                              />
                              <span className="truncate">
                                {cropSentence(username, 23)}
                              </span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
              {/* Sort Button */}
              <button
                onClick={handleSortOrderChange}
                className="inline-flex min-w-[90px] items-center justify-between gap-1.5 rounded-md border border-gray-200 bg-white px-2.5 py-1.5 text-sm font-medium text-gray-700 shadow-sm transition-all hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <div className="flex items-center gap-1.5">
                  <span>By Date</span>
                </div>
                {sortOrder === "asc" ? (
                  <FaSortAmountUp className="h-3.5 w-3.5 text-gray-600" />
                ) : (
                  <FaSortAmountDown className="h-3.5 w-3.5 text-gray-600" />
                )}
              </button>
            </div>

            {/* Right side actions */}
            <div className="flex items-center gap-1.5">
              {/* Clear Filters Button */}
              {Object.values(selectedFilters).some(
                filters => filters.length > 0
              ) && (
                <button
                  onClick={handleClearAll}
                  className="inline-flex min-w-[90px] items-center justify-between gap-1.5 rounded-md bg-red-50 px-2.5 py-1.5 text-sm font-medium text-red-700 shadow-sm transition-all hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                >
                  <div className="flex items-center gap-1.5">
                    <FaTimes className="h-3.5 w-3.5" />
                    <span>Clear</span>
                  </div>
                </button>
              )}

              {/* Column Visibility Dropdown */}
              {role !== UserRole.RESEARCHER ? (
                <div className="relative">
                  <button
                    ref={el => setButtonRef("columns", el)}
                    onClick={() => {
                      setIsColumnMenuOpen(!isColumnMenuOpen);
                      setOpenSection(null);
                    }}
                    className={`inline-flex min-w-[110px] items-center justify-between gap-1.5 rounded-md border px-2.5 py-1.5 text-sm font-medium shadow-sm transition-all ${
                      Object.values(columnVisibility).filter(Boolean).length > 0
                        ? "border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100"
                        : "border-gray-200 bg-white text-gray-700 hover:bg-gray-50"
                    }`}
                  >
                    <div className="flex items-center gap-1.5">
                      <FaColumns className="h-3.5 w-3.5" />
                      <span>Filter Columns</span>
                    </div>
                    <div className="flex items-center gap-1">
                      {Object.values(columnVisibility).filter(Boolean).length >
                        0 && (
                        <span className="inline-flex h-4 w-4 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-600">
                          {
                            Object.values(columnVisibility).filter(Boolean)
                              .length
                          }
                        </span>
                      )}
                      <FaAngleDown
                        className={`h-3 w-3 transform transition-transform duration-200 ${
                          isColumnMenuOpen ? "rotate-180" : ""
                        }`}
                      />
                    </div>
                  </button>
                  {isColumnMenuOpen && (
                    <div
                      ref={columnsDropdownRef}
                      className={`absolute right-0 ${getDropdownZIndex(
                        "columns"
                      )} mt-1 w-64 rounded-md border border-gray-200 bg-white shadow-lg ring-1 ring-black/5`}
                    >
                      <div className="border-b border-gray-100 px-3 py-2">
                        <h3 className="text-sm font-medium text-gray-900">
                          Visible Columns
                        </h3>
                        <p className="mt-0.5 text-xs text-gray-500">
                          Select up to 2 columns to display in the table
                        </p>
                      </div>
                      <div className="p-2">
                        <div className="space-y-1">
                          <label className="flex items-center gap-2 rounded px-2 py-1.5 hover:bg-gray-50">
                            <input
                              type="checkbox"
                              checked={columnVisibility.createdAt}
                              onChange={() =>
                                handleColumnVisibilityChange("createdAt")
                              }
                              className="h-3.5 w-3.5 rounded border-gray-200 text-blue-600 focus:ring-blue-500"
                            />
                            <div className="flex flex-col">
                              <span className="text-sm font-medium text-gray-700">
                                Created At
                              </span>
                              <span className="text-xs text-gray-500">
                                Show report creation date
                              </span>
                            </div>
                          </label>
                          <label className="flex items-center gap-2 rounded px-2 py-1.5 hover:bg-gray-50">
                            <input
                              type="checkbox"
                              checked={columnVisibility.pentester}
                              onChange={() =>
                                handleColumnVisibilityChange("pentester")
                              }
                              className="h-3.5 w-3.5 rounded border-gray-200 text-blue-600 focus:ring-blue-500"
                            />
                            <div className="flex flex-col">
                              <span className="text-sm font-medium text-gray-700">
                                Pentester
                              </span>
                              <span className="text-xs text-gray-500">
                                Show who created the report
                              </span>
                            </div>
                          </label>
                          <label className="flex items-center gap-2 rounded px-2 py-1.5 hover:bg-gray-50">
                            <input
                              type="checkbox"
                              checked={columnVisibility.action}
                              onChange={() =>
                                handleColumnVisibilityChange("action")
                              }
                              className="h-3.5 w-3.5 rounded border-gray-200 text-blue-600 focus:ring-blue-500"
                            />
                            <div className="flex flex-col">
                              <span className="text-sm font-medium text-gray-700">
                                Actions
                              </span>
                              <span className="text-xs text-gray-500">
                                Show status change actions
                              </span>
                            </div>
                          </label>
                        </div>
                      </div>
                      <div className="border-t border-gray-100 bg-gray-50 px-3 py-2">
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">
                            {
                              Object.values(columnVisibility).filter(Boolean)
                                .length
                            }
                            /2 columns selected
                          </span>
                          <button
                            onClick={() => {
                              onColumnVisibilityChange({
                                createdAt: false,
                                pentester: false,
                                action: false
                              });
                            }}
                            className="text-xs font-medium text-gray-600 hover:text-gray-900"
                          >
                            Reset
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ) : null}
            </div>
          </div>
        </div>
      </div>

      {/* Active Filters */}
      {renderActiveFilters()}
    </div>
  );
};

export default FilterSectionReports;
