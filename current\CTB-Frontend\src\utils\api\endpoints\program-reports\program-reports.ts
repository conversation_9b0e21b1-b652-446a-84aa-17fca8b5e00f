import axios from "../../axios";

const URL = "/v2/program-reports";

// Get all program reports with pagination and filters
export const getProgramReports = async (params?: {
  page?: number;
  limit?: number;
  status?: string;
  userId?: number;
  companyName?: string;
  search?: string;
}) => {
  const response = await axios.get(URL, { params });
  return response.data;
};

// Get a specific program report by ID
export const getProgramReportById = async (id: string) => {
  const response = await axios.get(`${URL}/${id}`);
  return response.data;
};

// Create a new program report
export const createProgramReport = async (reportData: any) => {
  const response = await axios.post(URL, reportData);
  return response.data;
};

// Update a program report
export const updateProgramReport = async (id: string, reportData: any) => {
  const response = await axios.put(`${URL}/${id}`, reportData);
  return response.data;
};

// Update report status
export const updateReportStatus = async (id: string, status: string) => {
  const response = await axios.patch(`${URL}/${id}/status`, { status });
  return response.data;
};

// Delete a program report
export const deleteProgramReport = async (id: string) => {
  const response = await axios.delete(`${URL}/${id}`);
  return response.data;
};

// Get reports by automated report ID
export const getReportsByAutomatedId = async (automatedReportId: string) => {
  const response = await axios.get(`${URL}/automated/${automatedReportId}`);
  return response.data;
};

// Get report statistics
export const getReportStatistics = async () => {
  const response = await axios.get(`${URL}/statistics`);
  return response.data;
};

// Create report from automated report data
export const createFromAutomatedReport = async (automatedReportId: string, reportData: any) => {
  const response = await axios.post(`${URL}/from-automated/${automatedReportId}`, reportData);
  return response.data;
};

// Get reports that need review
export const getReportsForReview = async () => {
  const response = await axios.get(`${URL}/for-review`);
  return response.data;
};

// Bulk update report statuses
export const bulkUpdateReportStatus = async (reportIds: string[], status: string) => {
  const response = await axios.patch(`${URL}/bulk-status`, { reportIds, status });
  return response.data;
};

// Get program report preview
export const getProgramReportPreview = async (id: string, reportData: any) => {
  const response = await axios.post(
    `${URL}/${id}/preview`,
    { reportData },
    {
      headers: { 'Accept': 'text/html' },
      responseType: 'text'
    }
  );
  return response.data;
};

// Fetch change log for a program report
export const getProgramReportChangeLog = async (id: string) => {
  const response = await axios.get(`/v2/program-reports/${id}/change-log`);
  return response.data;
}; 