2025-08-05T05:53:56.677Z  [dashboardMetrics] info: Optimised module loaded 
2025-08-05T05:54:15.821Z  [dashboardMetrics] info: Optimised module loaded 
2025-08-05T05:54:36.476Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-08-05T05:54:36.679Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-08-05T05:54:36.684Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-08-05T05:54:36.685Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-08-05T05:54:36.685Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-08-05T05:54:36.686Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-08-05T05:54:36.687Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.687Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-08-05T05:54:36.688Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.689Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.689Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-08-05T05:54:36.690Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.690Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-08-05T05:54:36.691Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.709Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.719Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-08-05T05:54:36.720Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-08-05T05:54:36.724Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-08-05T05:54:36.756Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-08-05T05:54:36.760Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-08-05T05:54:36.760Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-08-05T05:54:36.761Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-08-05T05:54:36.761Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-08-05T05:54:36.761Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.762Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-08-05T05:54:36.762Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.762Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.763Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-08-05T05:54:36.763Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.763Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-08-05T05:54:36.763Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.766Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.770Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-08-05T05:54:36.771Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-08-05T05:54:36.789Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-08-05T05:54:36.794Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-08-05T05:54:36.794Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-08-05T05:54:36.795Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-08-05T05:54:36.795Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-08-05T05:54:36.795Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.796Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-08-05T05:54:36.796Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.796Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.797Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-08-05T05:54:36.797Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.797Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-08-05T05:54:36.797Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.799Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.803Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-08-05T05:54:36.803Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-08-05T05:54:36.823Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-08-05T05:54:36.827Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-08-05T05:54:36.827Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-08-05T05:54:36.828Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-08-05T05:54:36.828Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-08-05T05:54:36.828Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.829Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-08-05T05:54:36.829Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.829Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.830Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-08-05T05:54:36.830Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.830Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-08-05T05:54:36.830Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.832Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.836Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-08-05T05:54:36.837Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-08-05T05:54:36.854Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-08-05T05:54:36.860Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-08-05T05:54:36.860Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-08-05T05:54:36.860Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-08-05T05:54:36.861Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-08-05T05:54:36.861Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.861Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-08-05T05:54:36.861Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.862Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.862Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-08-05T05:54:36.862Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.863Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-08-05T05:54:36.863Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.865Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.869Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-08-05T05:54:36.869Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-08-05T05:54:36.888Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-08-05T05:54:36.892Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-08-05T05:54:36.893Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-08-05T05:54:36.893Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-08-05T05:54:36.893Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-08-05T05:54:36.894Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.894Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-08-05T05:54:36.894Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.895Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.895Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-08-05T05:54:36.895Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.896Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-08-05T05:54:36.896Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.897Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.901Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-08-05T05:54:36.901Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-08-05T05:54:36.917Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-08-05T05:54:36.921Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-08-05T05:54:36.921Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-08-05T05:54:36.921Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-08-05T05:54:36.922Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-08-05T05:54:36.922Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.922Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-08-05T05:54:36.923Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.923Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.923Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-08-05T05:54:36.924Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.924Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-08-05T05:54:36.924Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.926Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-08-05T05:54:36.929Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-08-05T05:54:36.929Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-08-05T05:54:36.946Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-08-05T05:54:38.623Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-08-05T05:54:38.633Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 2 
2025-08-05T05:54:43.062Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-08-05T05:54:43.066Z  [automated-report.controller | checkProgramAutomatedReport] info: Checking automated report for program 4 by user 2 
2025-08-05T05:54:43.071Z  [automated-report.controller | checkProgramAutomatedReport] info: Check completed for program 4. Approved: false, Pending: false 
2025-08-05T05:54:43.073Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 2 
2025-08-05T05:54:43.077Z  [automated-report.controller | checkProgramAutomatedReport] info: Checking automated report for program 4 by user 2 
2025-08-05T05:54:43.079Z  [automated-report.controller | checkProgramAutomatedReport] info: Check completed for program 4. Approved: false, Pending: false 
2025-08-05T05:54:43.110Z  [undefined] info: Fetching program details for program ID: 4, User ID: 2 
2025-08-05T05:54:43.126Z  [undefined] info: Fetching program details for program ID: 4, User ID: 2 
2025-08-05T05:54:43.140Z  [undefined] info: Fetching program details for program ID: 4, User ID: 2 
2025-08-05T05:54:43.149Z  [undefined] info: Fetching program details for program ID: 4, User ID: 2 
2025-08-05T05:55:05.200Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-08-05T05:55:05.274Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-08-05T05:55:05.285Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-08-05T05:55:07.241Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-08-05T05:55:07.248Z  [program.controller | getPrograms] info: Successfully got 5 programs for user with id 3 
2025-08-05T05:55:10.484Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-08-05T05:55:10.490Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-08-05T05:55:10.522Z  [undefined] info: Fetching program details for program ID: 4, User ID: 3 
2025-08-05T05:55:10.532Z  [undefined] info: Fetching program details for program ID: 4, User ID: 3 
2025-08-05T05:55:10.545Z  [undefined] info: Fetching program details for program ID: 4, User ID: 3 
2025-08-05T05:55:10.555Z  [undefined] info: Fetching program details for program ID: 4, User ID: 3 
2025-08-05T06:13:25.378Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-08-05T06:13:25.409Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-08-05T06:13:59.934Z  [dashboardMetrics] info: Optimised module loaded 
