import moment from "moment";
import { DATE_FORMAT } from "../../../..";

// Define the types for the API response
export type BusinessDashboardAPIResponse = {
  username: string;
  group1: {
    totalReports: number;
    totalResolvedReports: number;
    pentestHours: number;
    numberOfPrograms: number;
  };
  group2: {
    overallSeverityCategories: {
      severity: string;
      count: number;
    }[];
    programSeverityCategories: {
      programId: number;
      programName: string;
      severities: {
        severity: string;
        count: number;
      }[];
    }[];
  };
  group3: {
    timeSeriesReports: {
      date: string | null;
      count: number;
    }[];
  };
  group5: {
    overallCategories: {
      category: string;
      count: number;
    }[];
    programCategories: {
      programId: number;
      programName: string;
      categories: {
        category: string;
        count: number;
      }[];
    }[];
  };
  group6: {
    programDetails: {
      programId: number;
      programName: string;
      programType: string | null;
      programPfp: string | null;
      programStartDate: string | null;
      programEndDate: string | null;
    }[];
  };
  group7: {
    recentReports: {
      reportId: number;
      reportTitle: string;
      submissionDate: string;
      severityCategory: string;
      state: string;
    }[];
  };
  group8: {
    recentRetests: {
      retestId: number;
      reportTitle: string;
      status: string;
      latestComment: string;
    }[];
  };
  group9: {
    researchers: {
      researcherUsername: string;
      researcherPfp: string;
    }[];
  };
};

// Define the types for the parsed response
export type ParsedBusinessDashboard = {
  username: string;
  totalReports: number;
  totalResolvedReports: number;
  pentestHours: number;
  numberOfPrograms: number;
  overallSeverityCategories: {
    severity: string;
    count: number;
  }[];
  programSeverityCategories: {
    programId: number;
    programName: string;
    severities: {
      severity: string;
      count: number;
    }[];
  }[];
  timeSeriesReports: {
    date: string | null;
    count: number;
  }[];
  overallCategories: {
    category: string;
    count: number;
  }[];
  programCategories: {
    programId: number;
    programName: string;
    categories: {
      category: string;
      count: number;
    }[];
  }[];
  programDetails: {
    programId: number;
    programName: string;
    programType: string | null;
    programPfp: string | null;
    programStartDate: string | null;
    programEndDate: string | null;
  }[];
  recentReports: {
    reportId: number;
    reportTitle: string;
    submissionDate: string;
    severityCategory: string;
    state: string;
  }[];
  recentRetests: {
    retestId: number;
    reportTitle: string;
    status: string;
    latestComment: string;
  }[];
  researchers: {
    researcherUsername: string;
    researcherPfp: string;
  }[];
};

/**
 * Parse the business dashboard API response into a structured format
 */
export const parseBusinessDashboard = (
  response: BusinessDashboardAPIResponse
): ParsedBusinessDashboard => {
  return {
    username: response.username,
    totalReports: response.group1.totalReports,
    totalResolvedReports: response.group1.totalResolvedReports,
    pentestHours: response.group1.pentestHours,
    numberOfPrograms: response.group1.numberOfPrograms,
    overallSeverityCategories: response.group2.overallSeverityCategories,
    programSeverityCategories: response.group2.programSeverityCategories,
    timeSeriesReports: response.group3.timeSeriesReports,
    overallCategories: response.group5.overallCategories,
    programCategories: response.group5.programCategories,
    programDetails: response.group6.programDetails,
    recentReports: response.group7.recentReports.map(report => ({
      ...report,
      submissionDate: report.submissionDate
        ? moment(report.submissionDate).format(DATE_FORMAT)
        : ""
    })),
    recentRetests: response.group8.recentRetests,
    researchers: response.group9.researchers
  };
};
