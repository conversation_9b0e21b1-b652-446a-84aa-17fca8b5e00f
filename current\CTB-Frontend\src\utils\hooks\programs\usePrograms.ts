import { useState } from "react";
import { ProgramType } from "../../api/endpoints/programs/parsePrograms";
import { BaseFilters } from "../../../components/filters/FilterToolBar";
import { useGetProgramsQuery } from "../../api/endpoints/programsApi";

export type ProgramFilters = {
  activated?: ("Active" | "Inactive")[];
  deleted?: string[];
  private?: ("Public" | "Private")[];
  type?: ProgramType;
  page?: number;
} & BaseFilters;

const usePrograms = () => {
  const [filters, setFilters] = useState<ProgramFilters>({
    limit: 9
  });
  const { data, isError, isLoading } = useGetProgramsQuery(filters);

  const setPage = (page: number) =>
    setFilters(filters => ({
      ...filters,
      page
    }));

  return {
    programs: data?.programs || [],
    count: data?.results || 0,
    filters,
    isError,
    isLoading,
    setPage,
    setFilters
  };
};

export default usePrograms;
