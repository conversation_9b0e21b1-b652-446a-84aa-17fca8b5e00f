import { useState, useEffect } from "react";
import { IoInformationCircleOutline } from "react-icons/io5";
import { ProgramType } from "../../../utils/api/endpoints/programs/parsePrograms";

interface ProgramInformationProps {
  expanded: boolean;
  toggleExpanded: () => void;
  program: {
    private?: boolean;
    type?: string;
    testing_type?: string;
    environment_type?: string;
    compliance_type?: string;
    expected_end_date?: string;
  } | null;
  formatDates: (dateString: string | undefined) => string;
}

const ProgramInformation: React.FC<ProgramInformationProps> = ({ expanded, toggleExpanded, program, formatDates }) => {
  const [highlight, setHighlight] = useState(false);

  const handleToggle = () => {
    toggleExpanded();
    setHighlight(true);

     
    setTimeout(() => {
      setHighlight(false);
    }, 1500);
  };

  return (
    <div className="flex flex-col pb-4">
      <div className="flex items-center mt-3  group relative">
        <button
          className={`rounded-full hover:bg-gray-100 transition-colors relative ${expanded ? "" : "ms-2.5 -mt-6"}`}
          onClick={handleToggle}
        >
          <IoInformationCircleOutline className="text-ctb-blue-600 text-2xl cursor-pointer ms-0.5" />
          <span
          className={`absolute right-7 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 whitespace-nowrap transition-opacity duration-200 pointer-events-none group-hover:opacity-100 ${expanded ? "hidden" : ""}`}
        >
            Program Information
          </span>
        </button> 
        <h2
          className={`text-sm font-semibold text-blue-600 capitalize transition-all duration-[300ms] mt-0.5 ${
            expanded
              ? `opacity-100  px-2 py-1 rounded transition-all duration-500  ${
                  highlight ? "border-b-2 border-blue-600    text-lg" : "  "
                }`
              : "opacity-0 hidden"
          }`}
        >
          Program Information
        </h2>
      </div>

      <ul
        className={`mt-2 text-gray-800 text-[12px] space-y-3 transition-opacity duration-[600ms] ml-2 ${
          expanded ? "opacity-100" : "opacity-0 hidden"
        }`}
      >
        <li className="flex justify-between gap-12 mt-2">
          <span>Access</span>
          <span className="mr-4">{program?.private ? "Private" : "Public"}</span>
        </li>
        <li className="flex justify-between gap-12 mt-2">
          <span>Type</span>
          <span className="mr-4">{program?.type === "PTAAS" ? "PTaaS" : program?.type || "Not specified"}</span>
        </li>
        {program?.type === ProgramType.PTAAS && (
          <ul className="mt-4">
            <li className="flex justify-between gap-12 mt-2">
              <span>Testing</span>
              <span className="mr-4">{program?.testing_type || "Not specified"}</span>
            </li>
            <li className="flex justify-between gap-12 mt-2">
              <span>Environment</span>
              <span className="mr-4">{program?.environment_type || "Not specified"}</span>
            </li>
            <li className="flex justify-between gap-12 mt-2">
              <span>Compliance</span>
              <span className="mr-4">{program?.compliance_type || "Not specified"}</span>
            </li>
            <li className="flex justify-between gap-12 mt-2">
              <span className="whitespace-nowrap">Program End Date</span>
              <span className="whitespace-nowrap mr-4">
                {program?.expected_end_date ? formatDates(program?.expected_end_date) : "N/A"}
              </span>
            </li>
          </ul>
        )}
      </ul>
    </div>
  );
};

export default ProgramInformation;
