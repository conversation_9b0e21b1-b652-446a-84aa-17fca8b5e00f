import { useState, useEffect } from "react";
import { BsClockHistory } from "react-icons/bs";
import { IoCloudUploadOutline } from "react-icons/io5";
import { HiDotsVertical, HiOutlineChartPie } from "react-icons/hi";
import { useNavigate, useParams } from "react-router-dom";
import useProgram from "../../../utils/hooks/programs/useProgram";
import CircleChevron from "../../../assets/icons/CircleChevron";
import PrimaryButton from "../../buttons/PrimaryButton";
import { UserRole } from "../../../utils/api/endpoints/user/credentials";
import useUserCredentials from "../../../utils/hooks/user/useUserCredentials";
import DOMPurify from "dompurify";
import InlineContainer from "../../common/InlineContainer";
import OutlineButton from "../../buttons/OutlineButton";
import { getPDFSummary } from "../../../utils/api/endpoints/business/generateSummaryReport";
import toast from "react-hot-toast";
import { CTBProgram, ProgramType } from "../../../utils/api/endpoints/programs/parsePrograms";
import DeleteIcon from "../../../assets/icons/DeleteIcon";
import PieChartIcon from "../../../assets/icons/PieChartIcon";
import EditIcon from "../../../assets/icons/EditIcon";
import ErrorMessage from "../../forms/errors/ErrorMessage";
import ProgramStats from "../ProgramStats";
import { getProgramOverview } from "../../../utils/api/endpoints/programs/programFullview";
import { fetchReportsInfo } from "../../../utils/api/endpoints/reports/getReport";
import ProgramActions from "./ActionButtons";
import { FaUserCircle } from "react-icons/fa";  
import InfoIcon from "../../../assets/icons/InfoIcon";
import { IoInformationCircleOutline } from "react-icons/io5";
import ProgramInformation from "./ProgramInfoSidebar";
import RecentUpdates from "./RecentUpdatesRightbar";
import RecentUpdatesRightbar from "./RecentUpdatesRightbar";


const formatProgramType = (type: ProgramType): string => {
  return type === ProgramType.PTAAS ? "PTaaS" : type;
};

const Rightbar = () => {
  const [expanded, setExpanded] = useState(false);
  const navigate = useNavigate();
  const { id } = useParams();
  const { role } = useUserCredentials();
  const [showStats, setShowStats] = useState(false);
  const { program, isLoading, deleteProgram } = useProgram(
    id !== undefined ? parseInt(id) : undefined
  );

  const toggleExpanded = () => setExpanded((prev) => !prev);

  interface UserDetails {
    username: string;
    profilePicture: string | null;
    about: string | null;
    displayName: string | null;
    role?: string;
    description?: string;

  }

  interface ProgramOverviewResponse {
    user: UserDetails;
  }

  interface ReportsSummary {
    reports_received_90_days: number;
    reports_resolved: number;
    assets_in_scope: number;
  }

  const [userDetails, setUserDetails] = useState<UserDetails | null>(null);
  const [loadingOverview, setLoadingOverview] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [reportSummary, setReportSummary] = useState<ReportsSummary | null>(null);
  const [loadingReports, setLoadingReports] = useState(true);
  const [showFullDescription, setShowFullDescription] = useState(false);


  useEffect(() => {
    if (id) {
      const fetchProgramOverview = async () => {
        try {
          const data: ProgramOverviewResponse = await getProgramOverview(parseInt(id));
          setUserDetails(data.user);
        } catch (err) {
          setError("Failed to fetch program overview.");
        } finally {
          setLoadingOverview(false);
        }
      };
      fetchProgramOverview();
    }
  }, [id]);

  useEffect(() => {
    if (id && role !== UserRole.BUSINESS_MANAGER && role !== UserRole.ADMIN_MANAGER) {
      const fetchReportsSummary = async () => {
        try {
          setLoadingReports(true);
          const response = await fetchReportsInfo(parseInt(id));
          setReportSummary(response?.summary || null);
        } catch (error) {
          console.warn("Skipping report fetch due to 401.");
        } finally {
          setLoadingReports(false);
        }
      };
      fetchReportsSummary();
    } else {
      console.warn("Skipping fetchReportsInfo API  ");
      setLoadingReports(false);
    }
  }, [id, role]);


  const maxDescriptionLength = 190;
  const rawDescription = userDetails?.about || userDetails?.description || "No description available";

  const truncatedDescription = rawDescription.length > maxDescriptionLength
    ? `${rawDescription.slice(0, maxDescriptionLength)} `
    : rawDescription;

  const handleToggleDescription = () => {
    setShowFullDescription((prev) => !prev);
  };

  const [highlightActions, setHighlightActions] = useState(false);

  const handleHighlightActions = () => {
    setHighlightActions(true);  
    setTimeout(() => {
      setHighlightActions(false);  
      setTimeout(() => {
        setHighlightActions(true);  
        setTimeout(() => {
          setHighlightActions(false);  
        }, 500);
      }, 200);
    }, 500);
  };
  
  async function handleGenerateInsights() {
    try {
      if (program?.type === ProgramType.PTAAS) {
        await getPDFSummary(program?.id);
        toast.success("Insights generated successfully!");
      } else {
        toast.error("Insights are available only for PTAAS programs.");
      }
    } catch (error) {
      toast.error(
        "An error occurred while generating insights. Please try again."
      );
    }
  }

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "Unknown Date";
    const date = new Date(dateString);
    return date.toLocaleString('default', { month: 'long', year: 'numeric' });
  };

  const formatDates = (dateString: string | undefined) => {
    if (!dateString) return "Unknown Date";
    const date = new Date(dateString);
    return date.toLocaleString('default', { day: 'numeric', month: 'short', year: 'numeric' });
  };


  return (
    <><div
      className={`relative flex flex-col bg-white shadow-md  py-4 px-3  -me-3 h-[100vh]  transition-all duration-300 ml-3 ${expanded ? "w-64 h-full  space-y-3" : "w-20  space-y-6"}`}
    >

      <div className="flex items-center transition-all duration-300 space-x-2">
        <div
          className={`rounded-full overflow-hidden border border-gray-300 transition-all duration-300 ${expanded ? "w-16 h-16" : "ms-2 w-10 h-10"}`}
        >
          {userDetails?.profilePicture ? (
            <img
              src={userDetails.profilePicture}
              alt="Profile"
              className="w-full h-full object-cover" />
          ) : (
            <FaUserCircle className="text-gray-500  w-full h-full object-cover" />
          )}
        </div>
        <div className={`transition-opacity duration-300 ${expanded ? "opacity-100" : "opacity-0 hidden"}`}>
          <h2 className="text-sm font-semibold text-gray-900 capitalize">
            {userDetails?.displayName?.trim() || userDetails?.username || "Unknown"}
          </h2>
        </div>
      </div>

      <p className={`text-[11px] text-gray-600 transition-opacity duration-300 ${expanded ? "opacity-100" : "opacity-0 hidden"}`}>
        <span
          dangerouslySetInnerHTML={{
            __html: DOMPurify.sanitize(showFullDescription ? rawDescription : truncatedDescription),
          }} />
        {rawDescription.length > maxDescriptionLength && (
          <span
            className="text-blue-600 cursor-pointer"
            onClick={handleToggleDescription}
          >
            {showFullDescription ? " Show Less" : " [...]"}
          </span>
        )}
      </p>
      <p className={`text-[11px] text-gray-700 ${expanded ? "opacity-100" : "opacity-0 hidden"}`}>
        <strong>{(program?.type)}</strong> program launched in {formatDate(program?.createdAt)}
      </p>


      <div className="flex  flex-col ">
        <div className="relative group">
        <button
      className={`p-1 rounded-full hover:bg-gray-100 transition-colors ${expanded ? "opacity-0 hidden" : "ms-1.5 opacity-100"}`}
      onClick={() => {
        toggleExpanded();
        handleHighlightActions();  
      }}
    >
      <HiDotsVertical className="text-ctb-blue-600 text-xl cursor-pointer ms-1" />
    </button>
          <span
          className={`absolute right-12 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 whitespace-nowrap transition-opacity duration-200 pointer-events-none group-hover:opacity-100 ${expanded ? "hidden" : ""}`}
        >
           View Statistics
          </span>
        </div>

        <div className={` ${expanded ? "opacity-100" : "opacity-0 hidden"}`}>
          {(role === UserRole.BUSINESS ||
            role === UserRole.ADMIN_MANAGER ||
            role === UserRole.QA ||
            role === UserRole.SUB_ADMIN ||
            role === UserRole.BUSINESS_ADMINISTRATOR ||
            role === UserRole.BUSINESS_MANAGER ||
            role === UserRole.ADMIN)
            && (
              <></>
            )}
          {role === UserRole.RESEARCHER && (
            <PrimaryButton
              className="w-full bg-ctb-blue-600 mt-3 mb-3 text-white"
              onClick={() =>
                program && navigate(`/dashboard/reports/new/${program.id}`)
              }
            >
              Report a Bug!
            </PrimaryButton>
          )}
        </div>
      </div>


      <div className="flex  flex-col   ">
      <RecentUpdatesRightbar
       expanded={expanded}
       toggleExpanded={toggleExpanded}
       loadingReports={loadingReports}
       reportSummary={reportSummary} />
      </div>

      <div
      className={`flex flex-col transition-all duration-500 rounded-md ${
        expanded ? "opacity-100" : "opacity-0 hidden"
      } ${
        highlightActions
          ? "bg-ctb-blue-600 shadow-lg text-white"
          : ""
      }`}
    >
      <ProgramActions highlight={highlightActions} />
    </div>
      <div className="flex flex-col pb-4">
 <ProgramInformation 
          expanded={expanded}
          toggleExpanded={toggleExpanded}
          formatDates={formatDates}
           program={program ?? null}/>
</div>
      <button className={`absolute top-[48%] transform -translate-y-1/2 transition-all duration-300 ${expanded ? "-left-5" : "right-5"}`} onClick={toggleExpanded}>
        <CircleChevron className={`text-gray-500   transition-transform ${expanded ? "rotate-180" : ""}`} />
      </button>
    </div>
    </>
  );
};

export default Rightbar;
