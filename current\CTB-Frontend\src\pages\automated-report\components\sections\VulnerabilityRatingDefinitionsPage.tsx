import React from 'react';
import { Page, View, Text, StyleSheet } from '@react-pdf/renderer';
import { ReportData } from '../../types/report.types';
import { useSectionPages } from '../SectionPageContext';

interface VulnerabilityRatingDefinitionsPageProps {
  reportData: ReportData;
  sectionId?: string;
}

const styles = StyleSheet.create({
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    fontSize: 10,
    color: 'grey',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  footerLeft: {
    textAlign: 'left',
    flex: 1,
  },
  footerRight: {
    textAlign: 'right',
    flex: 1,
  },
});

const VulnerabilityRatingDefinitionsPage: React.FC<VulnerabilityRatingDefinitionsPageProps> = ({ reportData, sectionId }) => {
  const { updateSectionPage } = useSectionPages();
  return (
    <Page size="A4" id={sectionId} style={{
      flexDirection: 'column',
      backgroundColor: '#ffffff',
      padding: '20mm 15mm',
      fontFamily: 'Helvetica',
      fontSize: 12,
    }}>
      <View style={{ flex: 1 }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#2563eb', marginBottom: 16, lineHeight: 1.4 }}>
          VULNERABILITY RATING DEFINITIONS
        </Text>
        <Text style={{ fontSize: 12, marginBottom: 14, color: '#374151', lineHeight: 1.4 }}>
          This table provides the summary of the vulnerabilities that were identified during the assessment:
        </Text>
        {/* Vulnerability Ratings Table */}
        <View
          style={{
            borderRadius: 16,
            marginBottom: 24,
            backgroundColor: '#f8fafc',
            width: '100%',
            alignSelf: 'center',
            overflow: 'hidden',
          }}
        >
          {/* Table Header */}
          <View
            style={{
              flexDirection: 'row',
              backgroundColor: '#1e293b',
              borderBottomWidth: 2,
              borderBottomColor: '#2563eb',
            }}
          >
            <Text
              style={{
                padding: 10,
                fontWeight: 'bold',
                fontSize: 12,
                lineHeight: 1.4,
                width: '40%',
                color: '#fff',
                letterSpacing: 1,
                textTransform: 'uppercase',
              }}
            >
              Vulnerability Level
            </Text>
            <Text
              style={{
                padding: 10,
                fontWeight: 'bold',
                fontSize: 12,
                lineHeight: 1.4,
                width: '60%',
                color: '#fff',
                letterSpacing: 2,
                textTransform: 'uppercase',
              }}
            >
              Description
            </Text>
          </View>
          {/* Table Rows */}
          {[
            {
              label: 'Critical',
              color: '#dc2626',
              bg: '#fef2f2',
              icon: '!',
              description:
                'Exploitation is straightforward and usually results in a system-level compromise. It is advised to form a plan of action and patch it immediately.',
            },
            {
              label: 'High',
              color: '#ea580c',
              bg: '#fff7ed',
              icon: '!',
              description:
                'Exploitation is more difficult but could cause elevated privileges and potentially a loss of data or downtime. It is advised to form a plan of action and patch it as soon as possible. ',
            },
            {
              label: 'Medium',
              color: '#eab308',
              bg: '#fef9c3',
              icon: '!',
              description:
                'Vulnerabilities exist but are not exploitable or require extra steps such as social engineering.  It is advised to form a plan of action and patch after high-priority issues have been resolved. ',
            },
            {
              label: 'Low',
              color: '#16a34a',
              bg: '#dcfce7',
              icon: '!',
              description:
                "Vulnerabilities are non-exploitable but would reduce an organization’s attack surface. It is advised to form a plan of action and patch during the next maintenance window. ",
            },
          ].map((row, idx) => (
            <View
              key={row.label}
              style={{
                flexDirection: 'row',
                backgroundColor: row.bg,
                borderLeftWidth: 6,
                borderLeftColor: row.color,
                alignItems: 'center',
                borderBottomWidth: idx < 3 ? 1 : 0,
                borderBottomColor: '#e5e7eb',
              }}
            >
              <View
                style={{
                  padding: 18,
                  width: '40%',
                  flexDirection: 'row',
                  alignItems: 'center',
                }}
              >
                <View
                  style={{
                    backgroundColor: row.color,
                    borderRadius: 20,
                    paddingHorizontal: 16,
                    paddingVertical: 8,
                    marginRight: 12,
                    flexDirection: 'row',
                    alignItems: 'center',
                    borderWidth: 1.5,
                    borderColor: '#fff',
                  }}
                >
                  <Text
                    style={{
                      color: '#fff',
                      fontWeight: 'bold',
                      fontSize: 12,
                      marginRight: 8,
                      lineHeight: 1.4,
                    }}
                  >
                    {row.icon}
                  </Text>
                  <Text
                    style={{
                      color: '#fff',
                      fontWeight: 'bold',
                      fontSize: 12,
                      letterSpacing: 1,
                      lineHeight: 1.4,
                    }}
                  >
                    {row.label}
                  </Text>
                </View>
              </View>
              <Text
                style={{
                  padding: 18,
                  width: '60%',
                  color: '#334155',
                  fontSize: 12,
                  fontWeight: '500',
                  lineHeight: 1.4,
                }}
              >
                {row.description}
              </Text>
            </View>
          ))}
        </View>
        <Text
          style={{
            fontSize: 12,
            color: '#64748b',
            textAlign: 'center',
            marginTop: 12,
            letterSpacing: 1,
            lineHeight: 1.4,
          }}
        >
          Table 3: Vulnerability Rating Definitions
        </Text>
      </View>
      <View style={styles.footer} fixed>
        <Text style={styles.footerLeft}>{reportData.document_number || 'Document Number'}</Text>
        <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
      </View>
      <Text style={{ display: 'none' }} render={({ pageNumber }) => { updateSectionPage('VulnerabilityRatingDefinitions', pageNumber); return ''; }} fixed />
    </Page>
  );
};

export default VulnerabilityRatingDefinitionsPage; 