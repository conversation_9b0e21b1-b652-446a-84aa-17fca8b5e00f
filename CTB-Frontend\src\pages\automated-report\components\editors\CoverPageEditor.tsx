import React from 'react';
import { ReportData } from '../../types/report.types';

interface CoverPageEditorProps {
  reportData: ReportData;
  onInputChange: (field: string, value: string) => void;
}

type CoverPageField =
  | 'report_title'
  | 'company_name'
  | 'program_name'
  | 'document_number'
  | 'version_number'
  | 'current_date'
  | 'revision_date';

interface FormField {
  id: CoverPageField;
  label: string;
  type: 'text' | 'date';
  placeholder?: string;
  required?: boolean;
}

const formFields: FormField[] = [
  { id: 'report_title', label: 'Report Title', type: 'text', placeholder: 'Enter report title', required: true },
  { id: 'program_name', label: 'Program Name', type: 'text', placeholder: 'Enter program name for cover page' },
  { id: 'company_name', label: 'Company Name', type: 'text', placeholder: 'Enter company name', required: true },
  { id: 'document_number', label: 'Document Number', type: 'text', placeholder: 'Enter document number' },
  { id: 'version_number', label: 'Version Number', type: 'text', placeholder: 'Enter version number' },
  { id: 'current_date', label: 'Current Date', type: 'date', required: true },
  { id: 'revision_date', label: 'Revision Date', type: 'date' },
];

const CoverPageEditor: React.FC<CoverPageEditorProps> = ({ reportData, onInputChange }) => {
  const handleInputChange = (field: CoverPageField, value: string) => {
    onInputChange(field, value);
  };

  const renderField = (field: FormField) => {
    const labelClasses = "block text-xs font-medium text-gray-700 mb-0.5";
    const fieldValue = (reportData[field.id] as string) || '';
    return (
      <div key={field.id} className="flex flex-col mb-2">
        <label htmlFor={field.id} className={labelClasses}>
          {field.label}
          {field.required && <span className="text-red-500 ml-0.5">*</span>}
        </label>
        <input
          id={field.id}
          type={field.type}
          value={fieldValue}
          onChange={(e) => handleInputChange(field.id, e.target.value)}
          className="w-full px-2.5 py-1.5 text-sm bg-white border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200 placeholder:text-slate-400"
          placeholder={field.placeholder}
          required={field.required}
          aria-required={field.required}
        />
      </div>
    );
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="bg-white rounded-xl shadow-lg border border-slate-100 p-6 mb-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-1 h-8 bg-gradient-to-b from-blue-500 to-blue-700 rounded-full" />
          <div>
            <h3 className="text-base font-bold text-blue-900 tracking-tight">Cover Page</h3>
            <p className="text-xs text-blue-700/80 font-medium mt-0.5">Edit cover page details</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-2">
          {/* First column: Report Title, Program Name, Company Name, Document Number */}
          <div className="flex flex-col gap-2">
            {renderField(formFields[0])} {/* Report Title */}
            {renderField(formFields[1])} {/* Program Name */}
            {renderField(formFields[2])} {/* Company Name */}
            {renderField(formFields[3])} {/* Document Number */}
          </div>
          {/* Second column: Version Number, Dates side by side */}
          <div className="flex flex-col gap-2">
            {renderField(formFields[4])} {/* Version Number */}
            <div className="grid grid-cols-2 gap-2">
              {renderField(formFields[5])} {/* Current Date */}
              {renderField(formFields[6])} {/* Revision Date */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CoverPageEditor; 