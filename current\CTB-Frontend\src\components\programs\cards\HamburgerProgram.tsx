import React, { useState } from "react";
import { FaAngleDoubleLeft } from "react-icons/fa";
import HamburgerIcon from "../../../assets/icons/Hamburger";
import { UserRole } from "../../../utils/api/endpoints/user/credentials"; // Ensure correct import

interface SidebarProps {
  activeSection: string;
  setActiveSection: (section: string) => void;
  role: UserRole; 
}

const Sidebar: React.FC<SidebarProps> = ({ activeSection, setActiveSection, role }) => {
  const [expanded, setExpanded] = useState(false);
 
  console.log("Current UserRole: ", role);

  const sections = [
    "Overview",
    "Attachments",
    "Targets",
    ...(UserRole[role] !== "BUSINESS_MANAGER" && UserRole[role] !== "ADMIN_MANAGER"
      ? ["Recent Activity", "Reports"]
      : []),
    ...(UserRole[role] === "ADMIN"  
      ? ["Management"]
      : []),
    "Terms & Conditions",
  ];
  
  return (
    <div
      className={`bg-white shadow-md transition-all relative -ms-3 ${
        expanded ? "w-50 p-3 me-3 h-[50vh]" : "me-14"
      }`}
    >
      <button
        className={`absolute top-4 ${
          expanded
            ? "right-3 text-blue-600 text-2xl bg-transparent"
            : "bg-white p-3 rounded-full shadow-lg"
        }`}
        onClick={() => setExpanded(!expanded)}
      >
        {!expanded ? <HamburgerIcon className="w-6 h-6" /> : <FaAngleDoubleLeft className="w-4 h-4"/>}
      </button>

      {expanded && <h2 className="text-lg font-bold mb-4 mt-3">On This Page</h2>}

      {expanded && (
        <ul className="space-y-3">
          {sections.map((section) => (
            <li key={section}>
              <button
                onClick={() => setActiveSection(section)}
                className={`block w-full text-left px-4 py-1 rounded-lg transition ${
                  activeSection === section
                    ? "bg-blue-500 text-white"
                    : "text-black hover:bg-gray-100"
                }`}
              >
                {section}
              </button>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default Sidebar;
