const ResolvedIcon = ({ className, color = "ctb-blue-500" }: { className?: string; color?: string }) => (
    <svg
      width="45"
      height="45"
      viewBox="0 0 55 55"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* Background circle */}
      <circle cx="27.5" cy="27.5" r="27.5" fill="none" stroke={color} strokeWidth="1" />
  
      {/* Browser-like shape */}
      <rect x="15" y="15" width="25" height="18" rx="2" fill="none" stroke={color} strokeWidth="1" />
      <rect x="15" y="15" width="25" height="4" fill={color} />
      <circle cx="19" cy="17" r="1.5" fill="white" />
      <circle cx="22" cy="17" r="1.5" fill="white" />
  
      {/* Code brackets */}
      <polyline points="23,23 20,27 23,31" stroke={color} strokeWidth="1.5" fill="none" />
      <polyline points="32,23 35,27 32,31" stroke={color} strokeWidth="1.5" fill="none" />
    </svg>
  );
  
  export default ResolvedIcon;
  