<html>

  <head>
    <meta charset="UTF-8" />
    <title>{{reportData.report_title}}</title>
    <script src="https://cdn.tailwindcss.com?v=3.3.0"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
      @page { size: A4; margin: 0; } body { margin: 0; padding: 0; font-family:
      Arial, sans-serif; font-size: 12pt; line-height: 1.5; color: #333;
      background: #f0f0f0; -webkit-print-color-adjust: exact !important;
      print-color-adjust: exact !important; } .page { width: 210mm; height:
      297mm; margin: 0 auto 10mm; background: white; box-sizing: border-box;
      position: relative; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      page-break-after: always; padding: 20mm; overflow: hidden; }
      .page:last-child { margin-bottom: 0; page-break-after: avoid; } /* Cover
      Page Styles */ .cover-page { background-image:
      url('https://i.postimg.cc/Bjf6p7rr/image.jpg'); background-size: cover;
      background-position: center; background-repeat: no-repeat; display: flex;
      flex-direction: column; justify-content: space-between; min-height: 297mm;
      padding: 40mm 20mm; } .cover-header { text-align: left; margin-bottom:
      40px; } .company-logo { max-width: 200px; margin-bottom: 30px; }
      .company-name { font-size: 28pt; font-weight: bold; margin-bottom: 20px;
      color: #0a2242; text-transform: uppercase; letter-spacing: 2px; }
      .report-title { font-size: 24pt; font-weight: bold; margin-bottom: 30px;
      color: #333; line-height: 1.3; } .document-info { background: #0a2242;
      color: white; padding: 20px; border-radius: 8px; margin: 40px 0;
      text-align: center; } .document-info p { margin: 10px 0; font-size: 14pt;
      } .document-info strong { color: #ffd700; } .cover-footer { text-align:
      center; margin-top: 40px; padding-top: 20px; border-top: 2px solid
      #0a2242; } .cover-footer p { margin: 5px 0; color: #666; }
      .confidential-stamp { position: absolute; top: 50%; right: 50%; transform:
      translate(50%, -50%) rotate(-45deg); font-size: 48pt; color: rgba(220, 53,
      69, 0.1); font-weight: bold; text-transform: uppercase; white-space:
      nowrap; pointer-events: none; } /* Content Pages Styles */ .content-page {
      padding: 30mm 20mm; } .section { margin-bottom: 30px; page-break-inside:
      avoid; } .section-title { font-size: 16pt; font-weight: bold; color:
      #0a2242; margin-bottom: 20px; border-bottom: 2px solid #0a2242;
      padding-bottom: 10px; } .section-content { font-size: 11pt; line-height:
      1.6; margin-bottom: 40px; } .executive-summary { background-color:
      #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 30px; }
      .findings-table { width: 100%; border-collapse: collapse; margin: 15px 0;
      page-break-inside: avoid; } .findings-table th, .findings-table td {
      border: 1px solid #ddd; padding: 12px; text-align: left; } .findings-table
      th { background-color: #0a2242; color: white; } .findings-table
      tr:nth-child(even) { background-color: #f8f9fa; } .severity-high { color:
      #dc3545; font-weight: bold; } .severity-medium { color: #ffc107;
      font-weight: bold; } .severity-low { color: #28a745; font-weight: bold; }
      .severity-summary { margin: 20px 0; padding: 15px; background-color:
      #f8f9fa; border-radius: 5px; page-break-inside: avoid; } .severity-count {
      display: inline-block; margin-right: 20px; padding: 5px 10px;
      border-radius: 3px; } .target-list { list-style-type: none; padding: 0;
      page-break-inside: avoid; } .target-list li { margin-bottom: 10px;
      padding: 10px; background-color: #f8f9fa; border-radius: 3px; } /*
      Print-specific styles */ @media print { body { background: none; margin:
      0; padding: 0; -webkit-print-color-adjust: exact !important;
      print-color-adjust: exact !important; } .page { margin: 0; box-shadow:
      none; page-break-after: always; padding: 20mm; background: white; height:
      297mm; overflow: hidden; } .page:last-child { page-break-after: avoid; }
      .page-break { page-break-before: always; } .no-break { page-break-inside:
      avoid; break-inside: avoid; } .confidential-stamp { display: none; } /*
      Ensure images are properly rendered */ img { max-width: 100%; height:
      auto; -webkit-print-color-adjust: exact !important; print-color-adjust:
      exact !important; break-inside: avoid; page-break-inside: avoid;
      max-height: 250mm; object-fit: contain; } /* Ensure tables don't break
      across pages */ table { page-break-inside: avoid; break-inside: avoid; }
      /* Ensure headings stay with their content */ h1, h2, h3, h4, h5, h6 {
      page-break-after: avoid; } /* Ensure lists don't break across pages */ ul,
      ol { page-break-inside: avoid; break-inside: avoid; } /* Ensure paragraphs
      don't break across pages */ p { orphans: 3; widows: 3; } /* Ensure
      background colors and images are printed */ * {
      -webkit-print-color-adjust: exact !important; print-color-adjust: exact
      !important; } /* Ensure charts are visible */ canvas {
      -webkit-print-color-adjust: exact !important; print-color-adjust: exact
      !important; break-inside: avoid; page-break-inside: avoid; } } /* Editable
      content styles */ [contenteditable="true"] { border: 1px dashed
      transparent; padding: 2px; transition: all 0.2s ease; }
      [contenteditable="true"]:hover { border-color: #0a2242; background-color:
      rgba(10, 34, 66, 0.05); } [contenteditable="true"]:focus { outline: none;
      border-color: #0a2242; background-color: rgba(10, 34, 66, 0.1); }
      .editable-table td { position: relative; } .editable-table
      td[contenteditable="true"] { min-width: 100px; } .editable-table
      td[contenteditable="true"]:hover { background-color: rgba(10, 34, 66,
      0.05); } .editable-table td[contenteditable="true"]:focus { outline: none;
      background-color: rgba(10, 34, 66, 0.1); } /* Added for overflow handling
      */ .no-break { break-inside: avoid !important; page-break-inside: avoid
      !important; } .page-break { break-after: page !important;
      page-break-after: always !important; } img, table, .finding-card {
      break-inside: avoid !important; page-break-inside: avoid !important; }
    </style>

  </head>

  <body>
    <!-- Cover Page -->
    <div
      class="page cover-page"
      style="background-image: url('https://i.postimg.cc/Bjf6p7rr/image.jpg'); background-size: cover; background-position: center; background-repeat: no-repeat;"
    >
      <!-- Top Logo -->
      <div class="pt-12 pl-8">
        <div class="w-64 h-16 flex items-center justify-center">
          <img
            src="https://i.postimg.cc/QVPV9d0x/Full-name-logo-blue.png"
            alt="Logo"
            class="w-64 h-auto no-break"
          />
        </div>
      </div>

      <!-- Middle Content -->
      <div class="flex-grow flex items-left">
        <div class="text-blue-600 space-y-10 pl-10">
          <h2
            class="text-3xl font-light w-[80%] flex-wrap mb-10 mt-12"
            contenteditable="true"
            data-field="report_title"
            oninput="handleInputChange('report_title', this.textContent)"
          >{{reportData.report_title}}</h2>
          <h1
            class="text-4xl font-bold mb-6"
            contenteditable="true"
            data-field="company_name"
            oninput="handleInputChange('company_name', this.textContent)"
          >{{reportData.company_name}}</h1>
          <h3
            class="text-xl font-light"
            contenteditable="true"
            data-field="version_number"
            oninput="handleInputChange('version_number', this.textContent)"
          >Version {{reportData.version_number}}</h3>
        </div>
      </div>

      <!-- Bottom Section -->
      <div class="w-full px-10 pb-12 mt-48">
        <div class="flex justify-between items-center">
          <!-- Document Info -->
          <div class="text-blue-600 space-y-4">
            <p
              class="text-xl"
              contenteditable="true"
              data-field="current_date"
            >Date: {{reportData.current_date}}</p>
          </div>

          <!-- Watermark Logo -->
          <div class="opacity-40">
            <img
              src="https://i.postimg.cc/hhhftd85/blue-bug.png"
              alt="Watermark"
              class="w-40 h-auto no-break"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Disclaimer Page -->
    <div class="page content-page">
      <div class="px-12 pt-24 flex flex-col h-full">
        <div class="flex-grow mt-16">
          <h1 class="text-3xl font-bold text-blue-600 mb-[20px]">DISCLAIMER</h1>

          <div
            class="space-y-8 text-base leading-relaxed"
            contenteditable="true"
            data-field="disclaimer"
          >
            {{#if reportData.disclaimer}}
              {{{reportData.disclaimer}}}
            {{else}}
              <p>
                Capture The Bug Ltd. has prepared this document exclusively for
                {{reportData.company_name}}. Copying, or modification of this
                document is strictly prohibited without Capture The Bug Ltd.'s
                written consent, except for specific purposes when such
                permission is granted. This document is confidential and
                proprietary material of Capture The Bug Ltd. and must always be
                treated as such, not to be disclosed to third parties without
                prior consent.
              </p>

              <p>
                The information within is provided 'as-is,' without any
                warranty. Vulnerability assessments offer a snapshot of security
                at a specific moment and are not exhaustive risk assessments.
                New vulnerabilities may emerge post-assessment. Thus, this
                report is a guide, not a definitive risk analysis.
              </p>

              <p>
                Capture The Bug Ltd. assumes no liability for any changes,
                omissions, or errors in this document. Capture The Bug Ltd.
                shall not be liable for any damages, financial or otherwise,
                arising out of the use or misuse of this report by any current
                employee of
                {{reportData.company_name}}
                or any member of the general public.
              </p>
            {{/if}}
          </div>
        </div>
      </div>
    </div>

    <!-- Document Reference Page -->
    <div class="page content-page">

      <div class="flex flex-col h-full">
        <!-- Main Content -->
        <div class="flex-grow mt-6">
          <h1 class="text-2xl font-bold text-blue-600 mb-4">DOCUMENT REFERENCE</h1>

          <!-- Document Information -->
          <div class="mb-6">
            <div class="flex items-center mb-2">
              <svg
                class="w-6 h-6 text-blue-600 mr-2"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <span class="font-bold text-blue-600">DOCUMENT INFORMATION</span>
            </div>

            <!-- Document Information Table -->
            <div
              class="rounded-lg overflow-hidden border border-gray-200 no-break"
            >
              <table class="w-full no-break">
                <tbody>
                  <tr class="border-b">
                    <td
                      class="px-4 py-3 font-semibold bg-blue-50 w-2/5"
                    >DOCUMENT TITLE</td>
                    <td
                      class="px-4 py-3"
                      contenteditable="true"
                      data-field="report_title"
                    >{{reportData.report_title}}
                    </td>
                  </tr>
                  <tr class="border-b">
                    <td class="px-4 py-3 font-semibold bg-blue-50">DOCUMENT
                      NUMBER</td>
                    <td
                      class="px-4 py-3"
                      contenteditable="true"
                      data-field="document_number"
                    >
                      {{reportData.document_number}}</td>
                  </tr>
                  <tr class="border-b">
                    <td
                      class="px-4 py-3 font-semibold bg-blue-50"
                    >CLASSIFICATION</td>
                    <td class="px-4 py-3">
                      <div
                        class="bg-red-100 text-red-600 rounded-full px-4 py-1 inline-flex items-center w-fit"
                      >
                        <span
                          class="h-2 w-2 rounded-full bg-red-500 mr-2"
                        ></span>
                        <span>Confidential</span>
                      </div>
                    </td>
                  </tr>
                  <tr class="border-b">
                    <td class="px-4 py-3 font-semibold bg-blue-50">REFERENCE
                      FILE</td>
                    <td class="px-4 py-3">
                      {{#if (gte reportData.version_number 2)}}
                        V{{subtract reportData.version_number 1}}
                      {{else}}
                        NONE
                      {{/if}}
                    </td>
                  </tr>
                  <tr>
                    <td class="px-4 py-3 font-semibold bg-blue-50">REVISION DATE</td>
                    <td
                      class="px-4 py-3"
                      contenteditable="true"
                      data-field="revision_date"
                    >{{reportData.revision_date}}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- Publish Date and Test Performed By -->
          <div class="flex mb-6 gap-4">
            <div class="w-1/2">
              <div class="flex items-center mb-2">
                <svg
                  class="w-6 h-6 text-blue-600 mr-2"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
                <span class="font-bold text-blue-600">PUBLISH DATE</span>
              </div>
              <div
                class="bg-blue-50 p-6 rounded-lg flex items-center justify-center flex-col h-24"
              >
                <div class="mb-2">
                  <svg
                    class="w-8 h-8 text-blue-600"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                  >
                    <circle cx="12" cy="12" r="10" stroke-width="2" />
                    <path
                      d="M12 6v6l4 2"
                      stroke-width="2"
                      stroke-linecap="round"
                    />
                  </svg>
                </div>
                <div
                  class="font-bold text-center"
                  contenteditable="true"
                  data-field="current_date"
                >
                  {{reportData.current_date}}</div>
              </div>
            </div>

            <div class="w-1/2">
              <div class="flex items-center mb-2">
                <svg
                  class="w-6 h-6 text-blue-600 mr-2"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
                <span class="font-bold text-blue-600">TEST PERFORMED BY</span>
              </div>
              <div class="bg-blue-50 p-6 rounded-lg flex items-center h-24">
                <div>
                  <div
                    class="font-bold"
                    contenteditable="true"
                    data-field="test_lead"
                  >{{reportData.test_lead}}</div>
                  <div
                    class="bg-blue-100 text-blue-600 mt-1 px-3 py-1 rounded-full text-xs inline-flex items-center"
                  >
                    <span
                      class="h-1.5 w-1.5 bg-green-500 rounded-full mr-1"
                    ></span>
                    Lead
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Contribution -->
          <div class="mb-6">
            <div class="flex items-center mb-2">
              <svg
                class="w-6 h-6 text-blue-600 mr-2"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                />
              </svg>
              <span class="font-bold text-blue-600">CONTRIBUTION</span>
            </div>

            <div
              class="rounded-lg overflow-hidden border border-gray-200 no-break"
            >
              <table class="w-full no-break">
                <thead>
                  <tr class="bg-blue-50">
                    <th class="px-4 py-3 text-left font-bold">ROLE</th>
                    <th class="px-4 py-3 text-left font-bold">NAME</th>
                    <th class="px-4 py-3 text-left font-bold">DESIGNATION</th>
                    <th class="px-4 py-3 text-left font-bold">DATE</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="border-b">
                    <td class="px-4 py-3 font-semibold">PREPARED BY</td>
                    <td
                      class="px-4 py-3 text-blue-700"
                      contenteditable="true"
                      data-field="prepared_by"
                    >
                      {{reportData.prepared_by}}</td>
                    <td
                      class="px-4 py-3 font-semibold text-yellow-800"
                    >PENTESTER</td>
                    <td class="px-4 py-3">
                      <span
                        class="bg-blue-100 px-3 py-1 rounded-md"
                      >{{reportData.current_date}}</span>
                    </td>
                  </tr>
                  <tr class="border-b">
                    <td class="px-4 py-3 font-semibold">REVIEWED BY</td>
                    <td
                      class="px-4 py-3 text-blue-700"
                      contenteditable="true"
                      data-field="reviewed_by"
                    >
                      {{reportData.reviewed_by}}</td>
                    <td class="px-4 py-3 font-semibold text-yellow-800">DY
                      MANAGER</td>
                    <td class="px-4 py-3">
                      <span
                        class="bg-blue-100 px-3 py-1 rounded-md"
                      >{{reportData.current_date}}</span>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-4 py-3 font-semibold">APPROVED BY</td>
                    <td
                      class="px-4 py-3 text-blue-700"
                      contenteditable="true"
                      data-field="approved_by"
                    >
                      {{reportData.approved_by}}</td>
                    <td
                      class="px-4 py-3 font-semibold text-yellow-800"
                    >MANAGER</td>
                    <td class="px-4 py-3">
                      <span
                        class="bg-blue-100 px-3 py-1 rounded-md"
                      >{{reportData.current_date}}</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- Version Control -->
          <div>
            <div class="flex items-center mb-2">
              <svg
                class="w-6 h-6 text-blue-600 mr-2"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
              <span class="font-bold text-blue-600">VERSION CONTROL</span>
            </div>

            <div
              class="rounded-lg overflow-hidden border border-gray-200 no-break"
            >
              <table class="w-full no-break">
                <thead>
                  <tr class="bg-blue-50">
                    <th class="px-4 py-3 text-left font-bold">VERSION</th>
                    <th class="px-4 py-3 text-left font-bold">DATE</th>
                    <th class="px-4 py-3 text-left font-bold">AUTHOR</th>
                    <th class="px-4 py-3 text-left font-bold">DESCRIPTION</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="px-4 py-3">V {{reportData.version_number}}</td>
                    <td class="px-4 py-3">{{reportData.current_date}}</td>
                    <td class="px-4 py-3">Ankita Dhakar</td>
                    <td
                      class="px-4 py-3"
                      contenteditable="true"
                      data-field="version_description"
                    >
                      {{#if reportData.version_description}}
                        {{reportData.version_description}}
                      {{else}}
                        {{#if (gt reportData.version_number 1)}}
                          Version
                          {{reportData.version_number}}
                          update
                        {{else}}
                          Initial Report
                        {{/if}}
                      {{/if}}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Executive Summary Page -->
    <div class="page content-page">

      <div class="px-12 pt-24 flex flex-col h-full">
        <div class="flex-grow mt-6">
          <h1 class="text-2xl font-bold text-blue-600 mb-4">EXECUTIVE SUMMARY</h1>

          <div
            class="space-y-8 text-base leading-relaxed"
            contenteditable="true"
            data-field="executive_summary"
          >
            {{#if (isNotEmpty reportData.executive_summary)}}
              {{{reportData.executive_summary}}}
            {{else}}
              <p>
                Capture The Bug is a modern Penetration Testing as a Service
                (PTaaS) platform designed to support fast-moving engineering
                teams and security-conscious enterprises. Organizations across
                New Zealand, Australia, and North America-including public
                companies and high-growth SaaS teams-trust Capture The Bug to
                secure their CI/CD pipelines, meet compliance goals, and reduce
                time-to-remediation without slowing development velocity.
                Capture The Bug is HQ in New Zealand and proudly works with
                customers across regulated and innovation-driven sectors.
              </p>

              <p>
                {{reportData.company_name}}
                entrusted Capture The Bug to conduct a comprehensive
                Vulnerability Assessment and Penetration Test (VAPT). This
                assessment evaluated the application's security posture from a
                gray-box perspective to focus on its resilience against common
                attack patterns and identify vulnerabilities in its internal and
                external interfaces.
              </p>

            {{/if}}
          </div>

          <div class="flex justify-between mt-12">
            <div
              class="w-[48%] border border-gray-200 rounded-lg p-6 bg-gray-50"
            >
              <h3 class="text-xl font-bold text-center mb-4">VULNERABILITY<br
                />DISTRIBUTION</h3>
              <div class="h-64 flex justify-center">
                <canvas id="pieChart" width="280" height="280"></canvas>
              </div>
            </div>

            <div
              class="w-[48%] border border-gray-200 rounded-lg p-6 bg-gray-50"
            >
              <h3 class="text-xl font-bold text-center mb-4">FINDINGS BY<br
                />SEVERITY</h3>
              <div class="h-64 flex justify-center">
                <canvas id="barChart" width="280" height="280"></canvas>
              </div>
            </div>
          </div>
          <!-- Add this script block HERE -->
          <script>
            // Get severity data directly from Handlebars const severityCounts =
            { Critical: { { reportData.severity_counts.Critical } }, High: { {
            reportData.severity_counts.High } }, Medium: { {
            reportData.severity_counts.Medium } }, Low: { {
            reportData.severity_counts.Low } } }; function initializeCharts() {
            const criticalTotal = severityCounts.Critical; const highTotal =
            severityCounts.High; const mediumTotal = severityCounts.Medium;
            const lowTotal = severityCounts.Low; const total = criticalTotal +
            highTotal + mediumTotal + lowTotal || 1; const criticalPercentage =
            ((criticalTotal / total) * 100).toFixed(1); const highPercentage =
            ((highTotal / total) * 100).toFixed(1); const mediumPercentage =
            ((mediumTotal / total) * 100).toFixed(1); const lowPercentage =
            ((lowTotal / total) * 100).toFixed(1); // Pie Chart const pieCtx =
            document.getElementById('pieChart'); if (pieCtx) { new
            Chart(pieCtx.getContext('2d'), { type: 'doughnut', data: { labels: [
            `Critical: ${criticalPercentage}%`, `High: ${highPercentage}%`,
            `Medium: ${mediumPercentage}%`, `Low: ${lowPercentage}%`], datasets:
            [{ data: [criticalTotal, highTotal, mediumTotal, lowTotal],
            backgroundColor: ['#8b0000', '#ff4500', '#ffd700', '#32cd32'],
            borderWidth: 1 }] }, options: { responsive: true,
            maintainAspectRatio: false, cutout: '40%', plugins: { legend: {
            position: 'bottom', labels: { usePointStyle: true, font: { size: 10
            } } }, tooltip: { callbacks: { label: function (context) { return
            `${context.label}: ${context.raw} finding(s)`; } } } } } }); } //
            Bar Chart const barCtx = document.getElementById('barChart'); if
            (barCtx) { new Chart(barCtx.getContext('2d'), { type: 'bar', data: {
            labels: ['Critical', 'High', 'Medium', 'Low'], datasets: [{ label:
            'Number of Findings', data: [criticalTotal, highTotal, mediumTotal,
            lowTotal], backgroundColor: ['#8b0000', '#ff4500', '#ffd700',
            '#32cd32'], borderWidth: 1 }] }, options: { responsive: true,
            maintainAspectRatio: false, scales: { y: { beginAtZero: true, ticks:
            { precision: 0 } } }, plugins: { legend: { display: false } } } });
            } } // Initialize after elements are rendered
            setTimeout(initializeCharts, 100);
          </script>
        </div>
      </div>
    </div>

    <!-- Key Findings Page -->
    <div class="page content-page">

      <div class="px-12 pt-24 flex flex-col h-full">
        <div class="flex-grow mt-16">
          <div
            class="space-y-8 text-base leading-relaxed"
            contenteditable="true"
            data-field="key_findings"
          >
            {{#if reportData.key_findings}}
              {{{reportData.key_findings}}}
            {{else}}
              <p>
                Capture The Bug's thorough assessment identified
                <strong>{{reportData.total_findings}}</strong>
                findings, with
                <span
                  class="text-red-700 font-bold"
                >{{reportData.open_close_counts_by_severity.Critical.Total}}</span>
                categorized as
                <span class="text-red-700 font-bold">Critical Severity</span>,
                <span
                  class="text-red-500 font-bold"
                >{{reportData.open_close_counts_by_severity.High.Total}}</span>
                categorized as
                <span class="text-red-500 font-bold">High Severity</span>,
                <span
                  class="text-yellow-500 font-bold"
                >{{reportData.open_close_counts_by_severity.Medium.Total}}</span>
                categorized as
                <span class="text-yellow-500 font-bold">Medium Severity</span>
                and
                <span
                  class="text-green-600 font-bold"
                >{{reportData.open_close_counts_by_severity.Low.Total}}</span>
                as
                <span class="text-green-600 font-bold">Low Severity</span>.
                During the assessment, all critical and high vulnerabilities
                were reported to the
                {{reportData.company_name}}
                team, and the client addressed the reported vulnerabilities
                concurrently.
              </p>

              <p>
                The Capture The Bug team then conducted a retest to confirm the
                closure of the patched vulnerabilities. These findings along
                with status are detailed in the key finding's sections of this
                report. Prompt action is advised to improve the application's
                security posture.
              </p>
            {{/if}}
          </div>

          <div class="mt-12">
            <div
              class="rounded-lg overflow-hidden border border-gray-200 no-break"
            >
              <table class="w-full no-break">
                <thead>
                  <tr class="bg-blue-50 text-left">
                    <th class="px-4 py-6 font-bold">Severity</th>
                    <th class="px-4 py-6 font-bold">Open</th>
                    <th class="px-4 py-6 font-bold">Closed</th>
                    <th class="px-4 py-6 font-bold">Total</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="border-t">
                    <td class="px-4 py-3 flex items-center">
                      <div
                        class="w-6 h-6 bg-red-700 rounded-full flex items-center justify-center mr-2"
                      >
                        <span class="text-white font-bold text-xs">!</span>
                      </div>
                      <span class="font-semibold text-red-700">Critical</span>
                    </td>
                    <td
                      class="px-4 py-3"
                      contenteditable="true"
                      data-field="open_close_counts_by_severity.Critical.Open"
                    >
                      {{reportData.open_close_counts_by_severity.Critical.Open}}</td>
                    <td
                      class="px-4 py-3"
                      contenteditable="true"
                      data-field="open_close_counts_by_severity.Critical.Closed"
                    >
                      {{reportData.open_close_counts_by_severity.Critical.Closed}}</td>
                    <td
                      class="px-4 py-3 font-bold"
                      contenteditable="true"
                      data-field="open_close_counts_by_severity.Critical.Total"
                    >
                      {{reportData.open_close_counts_by_severity.Critical.Total}}</td>
                  </tr>
                  <tr class="border-t">
                    <td class="px-4 py-3 flex items-center">
                      <div
                        class="w-6 h-6 border-2 border-red-500 rounded-md flex items-center justify-center mr-2"
                      >
                        <span class="text-red-500 font-bold text-xs">!</span>
                      </div>
                      <span class="font-semibold text-red-500">High</span>
                    </td>
                    <td
                      class="px-4 py-3"
                      contenteditable="true"
                      data-field="open_close_counts_by_severity.High.Open"
                    >
                      {{reportData.open_close_counts_by_severity.High.Open}}</td>
                    <td
                      class="px-4 py-3"
                      contenteditable="true"
                      data-field="open_close_counts_by_severity.High.Closed"
                    >
                      {{reportData.open_close_counts_by_severity.High.Closed}}</td>
                    <td
                      class="px-4 py-3 font-bold"
                      contenteditable="true"
                      data-field="open_close_counts_by_severity.High.Total"
                    >
                      {{reportData.open_close_counts_by_severity.High.Total}}</td>
                  </tr>
                  <tr class="border-t">
                    <td class="px-4 py-3 flex items-center">
                      <div
                        class="w-6 h-6 text-yellow-500 border-2 border-yellow-500 rounded-md flex items-center justify-center mr-2"
                      >
                        <span class="text-yellow-500 font-bold text-xs">!</span>
                      </div>
                      <span class="font-semibold text-yellow-500">Medium</span>
                    </td>
                    <td
                      class="px-4 py-3"
                      contenteditable="true"
                      data-field="open_close_counts_by_severity.Medium.Open"
                    >
                      {{reportData.open_close_counts_by_severity.Medium.Open}}</td>
                    <td
                      class="px-4 py-3"
                      contenteditable="true"
                      data-field="open_close_counts_by_severity.Medium.Closed"
                    >
                      {{reportData.open_close_counts_by_severity.Medium.Closed}}</td>
                    <td
                      class="px-4 py-3 font-bold"
                      contenteditable="true"
                      data-field="open_close_counts_by_severity.Medium.Total"
                    >
                      {{reportData.open_close_counts_by_severity.Medium.Total}}</td>
                  </tr>
                  <tr class="border-t">
                    <td class="px-4 py-3 flex items-center">
                      <div
                        class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center mr-2"
                      >
                        <span class="text-white font-bold text-xs">i</span>
                      </div>
                      <span class="font-semibold text-green-600">Low</span>
                    </td>
                    <td
                      class="px-4 py-3"
                      contenteditable="true"
                      data-field="open_close_counts_by_severity.Low.Open"
                    >
                      {{reportData.open_close_counts_by_severity.Low.Open}}</td>
                    <td
                      class="px-4 py-3"
                      contenteditable="true"
                      data-field="open_close_counts_by_severity.Low.Closed"
                    >
                      {{reportData.open_close_counts_by_severity.Low.Closed}}</td>
                    <td
                      class="px-4 py-3 font-bold"
                      contenteditable="true"
                      data-field="open_close_counts_by_severity.Low.Total"
                    >
                      {{reportData.open_close_counts_by_severity.Low.Total}}</td>
                  </tr>
                  <tr class="border-t bg-blue-50">
                    <td class="px-4 py-6 font-bold">TOTAL</td>
                    <td
                      class="px-4 py-6 font-bold"
                      contenteditable="true"
                      data-field="total_open"
                    >
                      {{reportData.total_open}}</td>
                    <td
                      class="px-4 py-6 font-bold"
                      contenteditable="true"
                      data-field="total_closed"
                    >
                      {{reportData.total_closed}}</td>
                    <td
                      class="px-4 py-6 font-bold"
                      contenteditable="true"
                      data-field="total_findings"
                    >
                      {{reportData.total_findings}}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Scope Page -->
    <div class="page content-page">

      <div class="px-12 pt-24 flex flex-col h-full">
        <div class="flex-grow mt-6">
          <h1 class="text-2xl font-bold text-blue-500 mb-4">SCOPE</h1>

          <div
            class="space-y-6 text-base leading-relaxed"
            contenteditable="true"
            data-field="scope"
          >
            {{#if reportData.scope}}
              {{{reportData.scope}}}
            {{else}}
              <p class="mb-4">
                The scope of the assessment was limited to performing
                Vulnerability Assessment and Penetration Testing on the
                {{reportData.company_name}}
                systems mentioned below:
              </p>

              <div
                class="rounded-lg overflow-hidden border border-gray-200 no-break"
              >
                <table class="w-full no-break">
                  <thead>
                    <tr class="bg-blue-50 text-left border">
                      <th class="px-6 py-3 font-bold w-24 border-r-2">SL No.</th>
                      <th class="px-6 py-3 font-bold">Application Type</th>
                      <th class="px-6 py-3 font-bold">Application URL/Target</th>
                    </tr>
                  </thead>
                  <tbody>
                    {{#each reportData.target_details}}
                      <tr class="border-t">
                        <td
                          class="px-6 py-4 font-semibold border-r-2"
                        >{{padStart (add @index 1) 2 "0"}}</td>
                        <td class="px-6 py-4">{{this.type}}</td>
                        <td class="px-6 py-4">
                          {{#if (startsWith this.url "http")}}
                            <a
                              href="{{this.url}}"
                              class="text-blue-600 flex items-center"
                            >
                              {{this.url}}
                              <svg
                                class="w-4 h-4 ml-1"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  stroke-width="2"
                                  d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                                ></path>
                              </svg>
                            </a>
                          {{else}}
                            <span>{{this.url}}</span>
                          {{/if}}
                        </td>
                      </tr>
                    {{/each}}
                  </tbody>
                </table>
              </div>

              <div class="text-center mt-4 text-gray-600">
                <p>Table 1: Scope of Work</p>
              </div>
            {{/if}}
          </div>
        </div>
      </div>
    </div>

    <!-- Project Objectives Page -->
    <div class="page content-page">

      <div class="px-12 pt-24 flex flex-col h-full">
        <div class="flex-grow mt-6">
          <h1 class="text-2xl font-bold text-blue-500 mb-4">PROJECT OBJECTIVES</h1>

          <div
            class="space-y-8 text-base leading-relaxed"
            contenteditable="true"
            data-field="project_objectives"
          >
            {{#if reportData.project_objectives}}
              {{{reportData.project_objectives}}}
            {{else}}
              <p>
                The objective of this assessment was to validate the overall
                security posture of
                {{#each reportData.target_details}}
                  {{this.type}}{{#unless @last}}, {{/unless}}
                {{/each}}
                from a
                {{#each reportData.program_details}}
                  {{this.testing_type}}{{#unless @last}}, {{/unless}}
                {{/each}}
                perspective. It included determining the application's ability
                to resist common attack patterns and identifying vulnerable
                areas in the internal or external interfaces that might be
                exploited by a malicious user.
              </p>

              {{#if reportData.project_objectives}}
                <div
                  class="pl-4 border-l-4 border-blue-500 bg-blue-50 p-4 rounded-r-lg"
                >
                  <h3 class="font-bold text-blue-700 mb-2">Specific Objectives:</h3>
                  <ul class="list-disc pl-6 space-y-2">
                    {{#each reportData.project_objectives}}
                      <li>{{this}}</li>
                    {{/each}}
                  </ul>
                </div>
              {{/if}}

              <h2 class="text-xl font-bold text-blue-500 mb-2">ASSUMPTIONS</h2>
              <p>
                These vulnerabilities are based on the current version of the
                {{#each reportData.target_details}}
                  {{this.type}}{{#unless @last}}, {{/unless}}
                {{/each}}
                which was assessed on
                {{reportData.assessment_date}}.
              </p>

              {{#if reportData.assumptions}}
                <div class="pl-4 border-l-4 border-gray-300 p-4 rounded-r-lg">
                  <ul class="list-disc pl-6 space-y-2">
                    {{#each reportData.assumptions}}
                      <li>{{this}}</li>
                    {{/each}}
                  </ul>
                </div>
              {{/if}}
            {{/if}}
          </div>
        </div>
      </div>
    </div>

    <!-- Summary of Findings Page -->
    <div class="page content-page">
      <div class="flex-grow mt-6">
        <h1 class="text-2xl font-bold text-blue-500 mb-4">SUMMARY OF FINDINGS</h1>

        <p class="mb-3 text-l">
          This table provides the summary of the vulnerabilities that were
          identified during the assessment of
          {{reportData.company_name}}'s systems:
        </p>

        <div
          class="rounded-lg overflow-hidden border border-gray-200 mb-4 w-[130mm] mx-auto"
        >
          <table class="w-full">
            <tbody>
              <tr class="bg-blue-50 border-b">
                <td class="px-6 py-4 font-bold" colspan="2">Total Findings</td>
                <td
                  class="px-6 py-4 font-bold text-center"
                >{{reportData.total_findings}}</td>
              </tr>
              <tr class="bg-red-100 border-b">
                <td class="px-6 py-4 w-12">
                  <div
                    class="w-8 h-8 bg-red-700 rounded-full flex items-center justify-center"
                  >
                    <span class="text-white font-bold text-xs">!</span>
                  </div>
                </td>
                <td class="py-4 font-semibold text-red-700">Critical</td>
                <td
                  class="px-6 py-4 font-bold text-center"
                >{{reportData.open_close_counts_by_severity.Critical.Total}}
                </td>
              </tr>
              <tr class="bg-red-50 border-b">
                <td class="px-6 py-4 w-12">
                  <div
                    class="w-8 h-8 border-2 border-red-500 rounded-md flex items-center justify-center"
                  >
                    <span class="text-red-500 font-bold text-xs">!</span>
                  </div>
                </td>
                <td class="py-4 font-semibold text-red-500">High</td>
                <td
                  class="px-6 py-4 font-bold text-center"
                >{{reportData.open_close_counts_by_severity.High.Total}}</td>
              </tr>
              <tr class="bg-yellow-50 border-b">
                <td class="px-6 py-4 w-12">
                  <div
                    class="w-8 h-8 text-yellow-500 border-2 border-yellow-500 rounded-md flex items-center justify-center"
                  >
                    <span class="text-yellow-500 font-bold text-xs">!</span>
                  </div>
                </td>
                <td class="py-4 font-semibold text-yellow-500">Medium</td>
                <td
                  class="px-6 py-4 font-bold text-center"
                >{{reportData.open_close_counts_by_severity.Medium.Total}}</td>
              </tr>
              <tr class="bg-green-50">
                <td class="px-6 py-4 w-12">
                  <div
                    class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center"
                  >
                    <span class="text-white font-bold text-xs">!</span>
                  </div>
                </td>
                <td class="py-4 font-semibold text-green-600">Low</td>
                <td
                  class="px-6 py-4 font-bold text-center"
                >{{reportData.open_close_counts_by_severity.Low.Total}}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="text-center text-gray-600 mb-4">
          <p>Table 2: Summary of Findings</p>
        </div>

        <p class="mb-3">
          The following charts illustrate the distribution of vulnerabilities by
          severity and status:
        </p>

        <div class="flex justify-around mb-6">
          <!-- Severity Distribution using Chart.js -->
          <div class="w-[45%] border border-gray-200 rounded-lg p-4">
            <h3 class="text-center text-lg font-semibold mb-2">Vulnerability
              Distribution by Severity</h3>
            <div class="flex flex-col items-center">
              <div class="h-60 w-full">
                <canvas id="severityChart"></canvas>
              </div>
            </div>
          </div>

          <!-- Status Distribution using Chart.js -->
          <div class="w-[45%] border border-gray-200 rounded-lg p-4">
            <h3 class="text-center text-lg font-semibold mb-2">Vulnerability
              Status</h3>
            <div class="flex flex-col items-center">
              <div class="h-60 w-full">
                <canvas id="statusChart"></canvas>
              </div>
            </div>
          </div>
        </div>

        <div class="text-center text-gray-600 mt-2">
          <p>Figure 1: Visualization of Vulnerabilities by Severity and Status</p>
        </div>
      </div>
    </div>

    <!-- Vulnerability Ratings Page -->
    <div class="page content-page">
      <div class="px-12 pt-24 flex flex-col h-full">
        <div class="flex-grow mt-6">
          <h1 class="text-2xl font-bold text-blue-600 mb-4">VULNERABILITY RATING
            DEFINITIONS</h1>

          <p class="mb-6">
            This table provides the summary of the vulnerabilities that were
            identified during the assessment:
          </p>

          <div class="rounded-lg overflow-hidden border border-gray-200 mb-8">
            <table class="w-full">
              <thead>
                <tr class="bg-gray-50 text-left">
                  <th class="px-6 py-3 font-bold text-lg">Vulnerability Level</th>
                  <th class="px-6 py-3 font-bold text-lg">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr class="bg-red-100 border-t">
                  <td class="px-6 py-5 w-1/3">
                    <div class="flex items-center">
                      <div
                        class="w-8 h-8 bg-red-700 rounded-full flex items-center justify-center mr-3"
                      >
                        <span class="text-white font-bold text-xs">!</span>
                      </div>
                      <span
                        class="font-semibold text-red-700 text-lg"
                      >Critical</span>
                    </div>
                  </td>
                  <td class="px-6 py-5">
                    <p
                      class="text-gray-800"
                      contenteditable="true"
                      data-field="vulnerability_ratings.critical"
                    >
                      Exploitation is straightforward and usually results in a
                      system-level compromise. It is advised to form a plan of
                      action and patch it immediately.
                    </p>
                  </td>
                </tr>
                <tr class="bg-red-50 border-t">
                  <td class="px-6 py-5">
                    <div class="flex items-center">
                      <div
                        class="w-8 h-8 border-2 border-red-500 rounded-md flex items-center justify-center mr-3"
                      >
                        <span class="text-red-500 font-bold text-xs">!</span>
                      </div>
                      <span
                        class="font-semibold text-red-500 text-lg"
                      >High</span>
                    </div>
                  </td>
                  <td class="px-6 py-5">
                    <p
                      class="text-gray-800"
                      contenteditable="true"
                      data-field="vulnerability_ratings.high"
                    >
                      Exploitation is more difficult but could cause elevated
                      privileges and potentially a loss of data or downtime. It
                      is advised to form a plan of action and patch it as soon
                      as possible.
                    </p>
                  </td>
                </tr>
                <tr class="bg-yellow-50 border-t">
                  <td class="px-6 py-5">
                    <div class="flex items-center">
                      <div
                        class="w-8 h-8 border-2 border-yellow-500 text-yellow-500 rounded-md flex items-center justify-center mr-3"
                      >
                        <span class="text-yellow-500 font-bold text-xs">!</span>
                      </div>
                      <span
                        class="font-semibold text-yellow-500 text-lg"
                      >Medium</span>
                    </div>
                  </td>
                  <td class="px-6 py-5">
                    <p
                      class="text-gray-800"
                      contenteditable="true"
                      data-field="vulnerability_ratings.medium"
                    >
                      Vulnerabilities exist but are not exploitable or require
                      extra steps such as social engineering. It is advised to
                      form a plan of action and patch after high-priority issues
                      have been resolved.
                    </p>
                  </td>
                </tr>
                <tr class="bg-green-50 border-t">
                  <td class="px-6 py-5">
                    <div class="flex items-center">
                      <div
                        class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center mr-3"
                      >
                        <span class="text-white font-bold text-xs">i</span>
                      </div>
                      <span
                        class="font-semibold text-green-600 text-lg"
                      >Low</span>
                    </div>
                  </td>
                  <td class="px-6 py-5">
                    <p
                      class="text-gray-800"
                      contenteditable="true"
                      data-field="vulnerability_ratings.low"
                    >
                      Vulnerabilities are non-exploitable but would reduce an
                      organization's attack surface. It is advised to form a
                      plan of action and patch during the next maintenance
                      window.
                    </p>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="text-center text-gray-600 mt-4">
            <p>Table 3: Vulnerability Rating Definitions</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Key Findings Section -->
    <div class="page content-page">
      <div class="px-12 pt-24 flex flex-col h-full">
        <div class="flex-grow mt-6">
          <h1 class="text-3xl font-bold text-blue-500 mb-4">KEY FINDINGS</h1>

          <div class="space-y-6 text-base leading-relaxed">
            <p class="mb-4">
              The table below contains a list of the vulnerabilities identified
              during this assessment:
            </p>

            <div
              class="rounded-lg overflow-hidden border border-gray-600 no-break"
            >
              <table class="w-full border-collapse border-gray-600 no-break">
                <thead>
                  <tr class="bg-blue-50 text-left border-b border-gray-400">
                    <th
                      class="px-4 py-3 font-bold w-12 border-r-2 border-gray-400 text-center"
                    >SL No.</th>
                    <th
                      class="px-4 py-3 font-bold w-16 border-r-2 border-gray-400"
                    >Abbreviation</th>
                    <th
                      class="px-4 py-3 font-bold border-r-2 border-gray-400"
                    >Finding Title</th>
                    <th
                      class="px-4 py-3 font-bold w-24 border-r-2 border-gray-400"
                    >Severity</th>
                    <th class="px-4 py-3 font-bold w-24">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {{#with reportData.reports_list as |reportsList|}}
                    {{#each reportsList}}
                      <tr class="border-t border-gray-400">
                        <td
                          class="px-4 py-4 font-semibold border-r-2 border-gray-400 text-center"
                        >
                          {{padStart (add @index 1) 2 "0"}}
                        </td>
                        <td
                          class="px-4 py-4 font-semibold border-r-2 border-gray-400"
                        >
                          {{this.abbreviation}}
                        </td>
                        <td
                          class="px-4 py-4 border-r-2 border-gray-400"
                        >{{this.title}}</td>
                        <td
                          class="px-4 py-4 font-medium border-r-2 border-gray-400"
                        >
                          <div class="flex items-center">
                            {{#if (eq this.severity_category "Critical")}}
                              <span
                                class="w-3 h-3 rounded-full bg-red-500 mr-2"
                              ></span>
                            {{else if (eq this.severity_category "High")}}
                              <span
                                class="w-3 h-3 rounded-full bg-orange-500 mr-2"
                              ></span>
                            {{else if (eq this.severity_category "Medium")}}
                              <span
                                class="w-3 h-3 rounded-full bg-yellow-500 mr-2"
                              ></span>
                            {{else if (eq this.severity_category "Low")}}
                              <span
                                class="w-3 h-3 rounded-full bg-green-500 mr-2"
                              ></span>
                            {{/if}}
                            {{this.severity_category}}
                          </div>
                        </td>
                        <td class="px-4 py-4">
                          {{#if (eq this.status "Open")}}
                            <span
                              class="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full"
                            >Open</span>
                          {{else if (eq this.status "Closed")}}
                            <span
                              class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full"
                            >Closed</span>
                          {{else}}
                            <span
                              class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full"
                            >{{this.status}}</span>
                          {{/if}}
                        </td>
                      </tr>
                    {{/each}}
                  {{/with}}
                </tbody>
              </table>
            </div>

            <div class="text-center mt-4 text-gray-600">
              <p>Table 4: Summary of Key Findings</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Detailed Findings Cover Page -->
    <div class="page content-page">
      <div class="flex flex-col h-full">
        <div class="flex-grow mt-6">
          <div class="flex-grow mt-6 flex items-center justify-center">
            <div
              class="bg-blue-100 rounded-lg w-3/4 p-16 flex flex-col items-center text-center"
            >
              <div class="mb-12">
                <img
                  src="https://i.postimg.cc/hhhftd85/blue-bug.png"
                  alt="blue-bug"
                  class="w-24 no-break"
                />
              </div>

              <h1 class="text-5xl font-bold text-blue-600 mb-8">DETAILED
                FINDINGS</h1>

              <div
                class="bg-white rounded-full p-2 border border-blue-300 inline-flex items-center justify-center mt-8"
              >
                <svg
                  class="w-6 h-6 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M14 5l7 7m0 0l-7 7m7-7H3"
                  >
                  </path>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Critical Findings -->
    <div class="page content-page">
      <div class="px-12 pt-24 flex flex-col h-full">
        <div class="flex-grow mb-[10px]">
          <div class="bg-red-50 rounded-lg p-4 mb-8">
            <h1
              class="text-2xl font-bold text-red-600"
              contenteditable="true"
              data-field="critical_findings_title"
            >
              CRITICAL SEVERITY FINDINGS</h1>
          </div>

          {{#if (eq reportData.open_close_counts_by_severity.Critical.Total 0)}}
            <div class="text-center py-12">
              <p
                class="text-gray-600"
                contenteditable="true"
                data-field="critical_findings_empty"
              >No critical severity findings were identified during this
                assessment.</p>
            </div>
          {{else}}
            {{#with reportData.detailed_findings as |findingsList|}}
              {{#each findingsList}}
                {{#if (eq (lowercase severity_category) "critical")}}
                  <div
                    class="finding-card bg-white rounded-lg border border-gray-200 shadow-sm p-8 mb-10 no-break"
                  >
                    <div class="flex items-center mb-6">
                      <div
                        class="bg-red-700 text-white font-bold rounded px-3 py-1 mr-4"
                        contenteditable="true"
                        data-field="detailed_findings.{{@index}}.abbreviation"
                      >
                        {{abbreviation}}{{#unless
                          abbreviation
                        }}{{getSeverityAbbreviation
                            severity_category
                          }}{{countBySeverity
                            findingsList
                            severity_category
                            @index
                          }}{{/unless}}
                      </div>
                      <h2
                        class="text-xl font-bold"
                        contenteditable="true"
                        data-field="detailed_findings.{{@index}}.title"
                      >
                        {{title}}</h2>
                    </div>

                    <div class="space-y-6">
                      {{#if scope}}
                        <div>
                          <h3 class="text-lg font-bold mb-2">I. Vulnerable
                            URL/Component</h3>
                          <ul class="list-disc pl-8">
                            <li
                              contenteditable="true"
                              data-field="detailed_findings.{{@index}}.scope"
                            >{{{scope}}}</li>
                          </ul>
                        </div>
                      {{/if}}

                      {{#if description}}
                        <div>
                          <h3 class="text-lg font-bold mb-2">II. Description</h3>
                          <p
                            class="text-base leading-relaxed"
                            contenteditable="true"
                            data-field="detailed_findings.{{@index}}.description"
                          >{{{description}}}</p>
                        </div>
                      {{/if}}

                      {{#if instructions}}
                        <div>
                          <h3 class="text-lg font-bold mb-2">III. Steps to
                            Reproduce</h3>
                          <p
                            class="text-base leading-relaxed whitespace-pre-line"
                            contenteditable="true"
                            data-field="detailed_findings.{{@index}}.instructions"
                          >{{{instructions}}}</p>
                        </div>
                      {{/if}}

                      {{#if impact}}
                        <div>
                          <h3 class="text-lg font-bold mb-2">IV. Impact</h3>
                          <p
                            class="text-base leading-relaxed"
                            contenteditable="true"
                            data-field="detailed_findings.{{@index}}.impact"
                          >{{{impact}}}</p>
                        </div>
                      {{/if}}

                      {{#if fix}}
                        <div>
                          <h3 class="text-lg font-bold mb-2">V. Remediation</h3>
                          <p
                            class="text-base leading-relaxed"
                            contenteditable="true"
                            data-field="detailed_findings.{{@index}}.fix"
                          >
                            {{{fix}}}</p>
                        </div>
                      {{/if}}

                      <div>
                        <h3 class="text-lg font-bold mb-2">VI. Submission Date</h3>
                        <p
                          class="text-base leading-relaxed"
                          contenteditable="true"
                          data-field="detailed_findings.{{@index}}.submitted_date"
                        >
                          {{formatDate submitted_date}}
                        </p>
                      </div>
                    </div>
                  </div>
                {{/if}}
              {{/each}}
            {{/with}}
          {{/if}}
        </div>
      </div>
    </div>

    <!-- High Findings -->
    <div class="page content-page">
      <div class="px-12 pt-24 flex flex-col h-full">
        <div class="flex-grow mb-12">
          <div class="bg-red-50 rounded-lg p-4 mb-8">
            <h1 class="text-2xl font-bold text-red-600">HIGH SEVERITY FINDINGS</h1>
          </div>

          {{#if (eq reportData.open_close_counts_by_severity.High.Total 0)}}
            <div class="text-center py-12">
              <p class="text-gray-600">No high severity findings were identified
                during this assessment.</p>
            </div>
          {{else}}
            {{#with reportData.detailed_findings as |findingsList|}}
              {{#each findingsList}}
                {{#if (eq (lowercase severity_category) "high")}}
                  <div
                    class="finding-card bg-white rounded-lg border border-gray-200 shadow-sm p-8 mb-10 no-break"
                  >
                    <div class="flex items-center mb-6">
                      <div
                        class="bg-red-500 text-white font-bold rounded px-3 py-1 mr-4"
                      >
                        {{abbreviation}}{{#unless
                          abbreviation
                        }}{{getSeverityAbbreviation
                            severity_category
                          }}{{countBySeverity
                            findingsList
                            severity_category
                            @index
                          }}{{/unless}}
                      </div>
                      <h2 class="text-xl font-bold">{{title}}</h2>
                    </div>

                    <div class="space-y-6">
                      {{#if scope}}
                        <div>
                          <h3 class="text-lg font-bold mb-2">I. Vulnerable
                            URL/Component</h3>
                          <ul class="list-disc pl-8">
                            <li>{{{scope}}}</li>
                          </ul>
                        </div>
                      {{/if}}

                      {{#if description}}
                        <div>
                          <h3 class="text-lg font-bold mb-2">II. Description</h3>
                          <p
                            class="text-base leading-relaxed"
                          >{{{description}}}</p>
                        </div>
                      {{/if}}

                      {{#if instructions}}
                        <div>
                          <h3 class="text-lg font-bold mb-2">III. Steps to
                            Reproduce</h3>
                          <p
                            class="text-base leading-relaxed whitespace-pre-line"
                          >{{{instructions}}}</p>
                        </div>
                      {{/if}}

                      {{#if impact}}
                        <div>
                          <h3 class="text-lg font-bold mb-2">IV. Impact</h3>
                          <p class="text-base leading-relaxed">{{{impact}}}</p>
                        </div>
                      {{/if}}

                      {{#if fix}}
                        <div>
                          <h3 class="text-lg font-bold mb-2">V. Remediation</h3>
                          <p class="text-base leading-relaxed">{{{fix}}}</p>
                        </div>
                      {{/if}}

                      <div>
                        <h3 class="text-lg font-bold mb-2">VI. Submission Date</h3>
                        <p class="text-base leading-relaxed">
                          {{formatDate submitted_date}}
                        </p>
                      </div>
                    </div>
                  </div>
                {{/if}}
              {{/each}}
            {{/with}}
          {{/if}}
        </div>
      </div>
    </div>

    <!-- Medium Findings -->
    <div class="page content-page">
      <div class="px-12 pt-24 flex flex-col h-full">
        <div class="flex-grow mb-[100px] pt-24">
          <div class="bg-orange-50 rounded-lg p-4 mb-8">
            <h1 class="text-2xl font-bold text-orange-600">MEDIUM SEVERITY
              FINDINGS</h1>
          </div>

          {{#if (eq reportData.open_close_counts_by_severity.Medium.Total 0)}}
            <div class="text-center py-12">
              <p class="text-gray-600">No medium severity findings were
                identified during this assessment.</p>
            </div>
          {{else}}
            {{#with reportData.detailed_findings as |findingsList|}}
              {{#each findingsList}}
                {{#if (eq (lowercase severity_category) "medium")}}
                  <div
                    class="finding-card bg-white rounded-lg border border-gray-200 shadow-sm p-8 mb-12 no-break"
                  >
                    <div class="flex items-center mb-6">
                      <div
                        class="bg-orange-500 text-white font-bold rounded px-3 py-1 mr-4"
                      >
                        {{abbreviation}}{{#unless
                          abbreviation
                        }}{{getSeverityAbbreviation
                            severity_category
                          }}{{countBySeverity
                            findingsList
                            severity_category
                            @index
                          }}{{/unless}}
                      </div>
                      <h2 class="text-xl font-bold">{{title}}</h2>
                    </div>

                    <div class="space-y-6">
                      {{#if scope}}
                        <div>
                          <h3 class="text-lg font-bold mb-2">I. Vulnerable
                            URL/Component</h3>
                          <ul class="list-disc pl-8">
                            <li>{{{scope}}}</li>
                          </ul>
                        </div>
                      {{/if}}

                      {{#if description}}
                        <div>
                          <h3 class="text-lg font-bold mb-2">II. Description</h3>
                          <p
                            class="text-base leading-relaxed"
                          >{{{description}}}</p>
                        </div>
                      {{/if}}

                      {{#if instructions}}
                        <div>
                          <h3 class="text-lg font-bold mb-2">III. Steps to
                            Reproduce</h3>
                          <p
                            class="text-base leading-relaxed whitespace-pre-line"
                          >{{{instructions}}}</p>
                        </div>
                      {{/if}}

                      {{#if impact}}
                        <div>
                          <h3 class="text-lg font-bold mb-2">IV. Impact</h3>
                          <p class="text-base leading-relaxed">{{{impact}}}</p>
                        </div>
                      {{/if}}

                      {{#if fix}}
                        <div>
                          <h3 class="text-lg font-bold mb-2">V. Remediation</h3>
                          <p class="text-base leading-relaxed">{{{fix}}}</p>
                        </div>
                      {{/if}}

                      <div>
                        <h3 class="text-lg font-bold mb-2">VI. Submission Date</h3>
                        <p class="text-base leading-relaxed">
                          {{formatDate submitted_date}}
                        </p>
                      </div>
                    </div>
                  </div>
                {{/if}}
              {{/each}}
            {{/with}}
          {{/if}}
        </div>
      </div>
    </div>

    <!-- Low Findings -->
    <div class="page content-page">
      <div class="px-12 pt-24 flex flex-col h-full">
        <div class="flex-grow mb-[10px]">
          <div class="bg-blue-50 rounded-lg p-4 mb-8">
            <h1 class="text-2xl font-bold text-blue-600">LOW SEVERITY FINDINGS</h1>
          </div>

          {{#if (eq reportData.open_close_counts_by_severity.Low.Total 0)}}
            <div class="text-center py-12">
              <p class="text-gray-600">No low severity findings were identified
                during this assessment.</p>
            </div>
          {{else}}
            {{#with reportData.detailed_findings as |findingsList|}}
              {{#each findingsList}}
                {{#if (eq (lowercase severity_category) "low")}}
                  <div
                    class="finding-card bg-white rounded-lg border border-gray-200 shadow-sm p-8 mb-10 no-break"
                  >
                    <div class="flex items-center mb-6">
                      <div
                        class="bg-blue-500 text-white font-bold rounded px-3 py-1 mr-4"
                      >
                        {{abbreviation}}{{#unless
                          abbreviation
                        }}{{getSeverityAbbreviation
                            severity_category
                          }}{{countBySeverity
                            findingsList
                            severity_category
                            @index
                          }}{{/unless}}
                      </div>
                      <h2 class="text-xl font-bold">{{title}}</h2>
                    </div>

                    <div class="space-y-6">
                      {{#if scope}}
                        <div>
                          <h3 class="text-lg font-bold mb-2">I. Vulnerable
                            URL/Component</h3>
                          <ul class="list-disc pl-8">
                            <li>{{{scope}}}</li>
                          </ul>
                        </div>
                      {{/if}}

                      {{#if description}}
                        <div>
                          <h3 class="text-lg font-bold mb-2">II. Description</h3>
                          <p
                            class="text-base leading-relaxed"
                          >{{{description}}}</p>
                        </div>
                      {{/if}}

                      {{#if instructions}}
                        <div>
                          <h3 class="text-lg font-bold mb-2">III. Steps to
                            Reproduce</h3>
                          <p
                            class="text-base leading-relaxed whitespace-pre-line"
                          >{{{instructions}}}</p>
                        </div>
                      {{/if}}

                      {{#if impact}}
                        <div>
                          <h3 class="text-lg font-bold mb-2">IV. Impact</h3>
                          <p class="text-base leading-relaxed">{{{impact}}}</p>
                        </div>
                      {{/if}}

                      {{#if fix}}
                        <div>
                          <h3 class="text-lg font-bold mb-2">V. Remediation</h3>
                          <p class="text-base leading-relaxed">{{{fix}}}</p>
                        </div>
                      {{/if}}

                      <div>
                        <h3 class="text-lg font-bold mb-2">VI. Submission Date</h3>
                        <p class="text-base leading-relaxed">
                          {{formatDate submitted_date}}
                        </p>
                      </div>
                    </div>
                  </div>
                {{/if}}
              {{/each}}
            {{/with}}
          {{/if}}
        </div>
      </div>
    </div>

  </body>

</html>