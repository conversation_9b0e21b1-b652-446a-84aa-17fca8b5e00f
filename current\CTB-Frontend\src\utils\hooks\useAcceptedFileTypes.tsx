import { useMemo } from "react";

/**
 * Provides function that can test whether a given file
 * should be accepted by the accept string.
 *
 * An empty or undefined string will accept all files.
 *
 * MDN: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/file#unique_file_type_specifiers
 */
const useAcceptedFileTypes = (accept?: string) => {
  // Convert given accept string into an array of accepted types
  const acceptedFiles = useMemo(
    () => (accept || "").replaceAll(" ", "").split(","),
    [accept]
  );

  // Create array of accepted file extensions
  const acceptedFileExtensions = useMemo(
    () => acceptedFiles.filter(s => s.startsWith(".")),
    [acceptedFiles]
  );

  // Generate regex string to match accepted file types
  const acceptedFilesRegex = useMemo(() => {
    const patterns = acceptedFiles.filter(s => !s.startsWith("."));

    return patterns.length > 0
      ? new RegExp(patterns.join("|").replace("*", ".*"))
      : new RegExp("(?!)");
  }, [acceptedFiles]);

  /**
   * Returns true if a given file is accepted
   * by the accept string
   */
  const isAccepted = (file: File) => {
    if (accept !== undefined) {
      return (
        acceptedFilesRegex.test(file.type) ||
        !!acceptedFileExtensions.find(ex => file.name.endsWith(ex))
      );
    } else {
      return true;
    }
  };

  return isAccepted;
};

export default useAcceptedFileTypes;
