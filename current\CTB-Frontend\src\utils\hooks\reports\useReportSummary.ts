import { useEffect, useState } from "react";
import { CTBReport } from "../../api/endpoints/reports/parseReports";
import { getReportSummary } from "../../api/endpoints/reports/reports";

/**
 * Provides access to a summary of all the
 * report available to the current user.
 */
export type ReportStatusSummary = {
  numReceived: number | undefined;
  draft: number | undefined;
  review: number;
  triage_approved: number;
  triage_rejected: number;
  business_approved: number;
  business_rejected: number;
  closed: number;
};

export type ReportSeveritySummary = {
  low: number;
  medium: number;
  high: number;
  critical: number;
};

const useReportSummary = () => {
  const [severityCounts, setSeverityCounts] = useState<ReportSeveritySummary>();
  const [summary, setSummary] = useState<ReportStatusSummary>();
  const [reports, setReports] = useState<CTBReport[]>([]);

  const refreshSummary = () => {
    getReportSummary().then(res => {
      setReports(res.latest);
      setSummary(res.status);
      setSeverityCounts(res.severity);
    });
  };

  useEffect(() => refreshSummary(), []);

  return {
    reports,
    summary,
    severityCounts
  };
};

export default useReportSummary;
