import axios from "../../axios";
import { UserRole } from "./credentials";

const URL = "/v2/rbac";

export const requestChangeUserRole = async (details: {
  invitation_id: number;
  new_role: UserRole;
}) => {
  const response = await axios.post(URL + "/changeUserRole", details, {
    headers: {
      "Content-Type": "application/json"
    }
  });
  return response.data;
};

export const getActivityLogs = async () => {
  const response = await axios.get(URL + "/getRbacLogs", {
    headers: {
      "Content-Type": "application/json"
    }
  });

  return response.data;
};
