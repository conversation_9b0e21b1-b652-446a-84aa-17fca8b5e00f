<!-- Executive summary component --> 
<div class="a4-page">
  <div class="absolute top-0 w-full text-center pt-8 pb-3 text-gray-700 text-xs font-medium tracking-wide">
      <p>© 2024 Capture The Bug Ltd. | All Rights Reserved</p>
      <p class="mt-1 font-semibold text-gray-700">CONFIDENTIAL</p>
  </div>
  <div class="px-12 pt-24 flex flex-col h-full">
      <div class="flex-grow mt-6">
          <h1 class="text-2xl font-bold text-blue-600 mb-4">EXECUTIVE SUMMARY</h1>
          
          <div class="space-y-8 text-base leading-relaxed">
              <p>
                  Capture The Bug is a modern Penetration Testing as a Service (PTaaS) platform designed to support fast-moving engineering teams and security-conscious enterprises. Organizations across New Zealand, Australia, and North America-including public companies and high-growth SaaS teams-trust Capture The Bug to secure their CI/CD pipelines, meet compliance goals, and reduce time-to-remediation without slowing development velocity. Capture The Bug is HQ in New Zealand and proudly works with customers across regulated and innovation-driven sectors.
              </p>
              
              <p>
                  <%= reportData.company_name %> entrusted Capture The Bug to conduct
                  a comprehensive Vulnerability Assessment and Penetration Test
                  (VAPT). This assessment evaluated the application's security
                  posture from a gray-box perspective to focus on its resilience
                  against common attack patterns and identify vulnerabilities in its
                  internal and external interfaces.
              </p>
          </div>
          
          <div class="flex justify-between mt-12">
              <div class="w-[48%] border border-gray-200 rounded-lg p-6 bg-gray-50">
                  <h3 class="text-xl font-bold text-center mb-4">VULNERABILITY<br>DISTRIBUTION</h3>
                  <div class="h-64 flex justify-center">
                      <canvas id="pieChart" width="280" height="280"></canvas>
                  </div>
              </div>
              
              <div class="w-[48%] border border-gray-200 rounded-lg p-6 bg-gray-50">
                  <h3 class="text-xl font-bold text-center mb-4">FINDINGS BY<br>SEVERITY</h3>
                  <div class="h-64 flex justify-center">
                      <canvas id="barChart" width="280" height="280"></canvas>
                  </div>
              </div>
          </div>
      </div>
  </div>
  <div class="pt-8 flex flex-col h-full">
     <!--remove footer-->
  </div>
</div>

<!-- Key findings component --> 
<div class="a4-page">
  <div class="absolute top-0 w-full text-center pt-8 pb-3 text-gray-700 text-xs font-medium tracking-wide">
      <p>© 2024 Capture The Bug Ltd. | All Rights Reserved</p>
      <p class="mt-1 font-semibold text-gray-700">CONFIDENTIAL</p>
  </div>
  <div class="px-12 pt-24 flex flex-col h-full">
      <div class="flex-grow mt-16">
          <div class="space-y-8 text-base leading-relaxed">
              <% 
              const severityData = reportData.open_close_counts_by_severity || {};
              const critical = severityData.Critical || {Open: 0, Closed: 0, Total: 0};
              const high = severityData.High || {Open: 0, Closed: 0, Total: 0};
              const medium = severityData.Medium || {Open: 0, Closed: 0, Total: 0};
              const low = severityData.Low || {Open: 0, Closed: 0, Total: 0};
              
              const totalOpen = critical.Open + high.Open + medium.Open + low.Open;
              const totalClosed = critical.Closed + high.Closed + medium.Closed + low.Closed;
              const totalFindings = critical.Total + high.Total + medium.Total + low.Total;
              %>
              <p>
                 Capture The Bug's thorough assessment identified <strong><%= totalFindings %></strong> findings, with
                  <span class="text-red-700 font-bold"><%= critical.Total %></span> categorized as <span class="text-red-700 font-bold">Critical Severity</span>,
                  <span class="text-red-500 font-bold"><%= high.Total %></span> categorized as <span class="text-red-500 font-bold">High Severity</span>,
                  <span class="text-yellow-500 font-bold"><%= medium.Total %></span> categorized as <span class="text-yellow-500 font-bold">Medium Severity</span>
                  and <span class="text-green-600 font-bold"><%= low.Total %></span> as <span class="text-green-600 font-bold">Low Severity</span>.
                  During the assessment, all critical and high vulnerabilities were reported to the <%= reportData.company_name %> team,
                  and the client addressed the reported vulnerabilities concurrently.
              </p>
              
              <p>
                  The Capture The Bug team then conducted a retest to confirm the closure of the patched vulnerabilities.
                  These findings along with status are detailed in the key finding's sections of this report.
                  Prompt action is advised to improve the application's security posture.
              </p>
          </div>
          
          <div class="mt-12">
              <div class="rounded-lg overflow-hidden border border-gray-200">
                  <table class="w-full">
                      <thead>
                          <tr class="bg-blue-50 text-left">
                              <th class="px-4 py-6 font-bold">Severity</th>
                              <th class="px-4 py-6 font-bold">Open</th>
                              <th class="px-4 py-6 font-bold">Closed</th>
                              <th class="px-4 py-6 font-bold">Total</th>
                          </tr>
                      </thead>
                      <tbody>
                          <tr class="border-t">
                              <td class="px-4 py-3 flex items-center">
                                  <div class="w-6 h-6 bg-red-700 rounded-full flex items-center justify-center mr-2">
                                      <span class="text-white font-bold text-xs">!</span>
                                  </div>
                                  <span class="font-semibold text-red-700">Critical</span>
                              </td>
                              <td class="px-4 py-3"><%= critical.Open %></td>
                              <td class="px-4 py-3"><%= critical.Closed %></td>
                              <td class="px-4 py-3 font-bold"><%= critical.Total %></td>
                          </tr>
                          <tr class="border-t">
                              <td class="px-4 py-3 flex items-center">
                                  <div class="w-6 h-6 border-2 border-red-500 rounded-md flex items-center justify-center mr-2">
                                      <span class="text-red-500 font-bold text-xs">!</span>
                                  </div>
                                  <span class="font-semibold text-red-500">High</span>
                              </td>
                              <td class="px-4 py-3"><%= high.Open %></td>
                              <td class="px-4 py-3"><%= high.Closed %></td>
                              <td class="px-4 py-3 font-bold"><%= high.Total %></td>
                          </tr>
                          <tr class="border-t">
                              <td class="px-4 py-3 flex items-center">
                                  <div class="w-6 h-6 text-yellow-500 border-2 border-yellow-500 rounded-md flex items-center justify-center mr-2">
                                      <span class="text-yellow-500 font-bold text-xs">!</span>
                                  </div>
                                  <span class="font-semibold text-yellow-500">Medium</span>
                              </td>
                              <td class="px-4 py-3"><%= medium.Open %></td>
                              <td class="px-4 py-3"><%= medium.Closed %></td>
                              <td class="px-4 py-3 font-bold"><%= medium.Total %></td>
                          </tr>
                          <tr class="border-t">
                              <td class="px-4 py-3 flex items-center">
                                  <div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center mr-2">
                                      <span class="text-white font-bold text-xs">i</span>
                                  </div>
                                  <span class="font-semibold text-green-600">Low</span>
                              </td>
                              <td class="px-4 py-3"><%= low.Open %></td>
                              <td class="px-4 py-3"><%= low.Closed %></td>
                              <td class="px-4 py-3 font-bold"><%= low.Total %></td>
                          </tr>
                          <tr class="border-t bg-blue-50">
                              <td class="px-4 py-6 font-bold">TOTAL</td>
                              <td class="px-4 py-6 font-bold"><%= totalOpen %></td>
                              <td class="px-4 py-6 font-bold"><%= totalClosed %></td>
                              <td class="px-4 py-6 font-bold"><%= totalFindings %></td>
                          </tr>
                      </tbody>
                  </table>
              </div>
          </div>
      </div>
  </div>
  <div class="pt-8 flex flex-col h-full">
     <!--remove footer-->
  </div>
</div>

<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- Initialize charts -->
<script>
// Store the values as global variables so they can be accessed by the chart initialization
var criticalTotal = <%= (reportData.open_close_counts_by_severity && reportData.open_close_counts_by_severity.Critical) ? reportData.open_close_counts_by_severity.Critical.Total : 0 %>;
var highTotal = <%= (reportData.open_close_counts_by_severity && reportData.open_close_counts_by_severity.High) ? reportData.open_close_counts_by_severity.High.Total : 0 %>;
var mediumTotal = <%= (reportData.open_close_counts_by_severity && reportData.open_close_counts_by_severity.Medium) ? reportData.open_close_counts_by_severity.Medium.Total : 0 %>;
var lowTotal = <%= (reportData.open_close_counts_by_severity && reportData.open_close_counts_by_severity.Low) ? reportData.open_close_counts_by_severity.Low.Total : 0 %>;

document.addEventListener('DOMContentLoaded', function() {
    // Calculate total and percentages
    var total = criticalTotal + highTotal + mediumTotal + lowTotal || 1; // Avoid division by zero
    
    var criticalPercentage = ((criticalTotal / total) * 100).toFixed(1);
    var highPercentage = ((highTotal / total) * 100).toFixed(1);
    var mediumPercentage = ((mediumTotal / total) * 100).toFixed(1);
    var lowPercentage = ((lowTotal / total) * 100).toFixed(1);
    
    // Pie Chart
    const pieCtx = document.getElementById('pieChart').getContext('2d');
    new Chart(pieCtx, {
        type: 'doughnut',
        data: {
            labels: [
                `Critical: ${criticalPercentage}%`,
                `High: ${highPercentage}%`,
                `Medium: ${mediumPercentage}%`,
                `Low: ${lowPercentage}%`
            ],
            datasets: [{
                data: [criticalTotal, highTotal, mediumTotal, lowTotal],
                backgroundColor: [
                    '#8b0000', '#ff4500', '#ffd700', '#32cd32'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '40%',
            plugins: {
                legend: {
                    position: 'bottom', 
                    labels: {
                        usePointStyle: true, 
                        font: {size: 10}
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.raw} finding(s)`;
                        }
                    }
                }
            }
        }
    });
    
    // Bar Chart
    const barCtx = document.getElementById('barChart').getContext('2d');
    new Chart(barCtx, {
        type: 'bar',
        data: {
            labels: ['Critical', 'High', 'Medium', 'Low'],
            datasets: [{
                label: 'Number of Findings',
                data: [criticalTotal, highTotal, mediumTotal, lowTotal],
                backgroundColor: [
                    '#8b0000', '#ff4500', '#ffd700', '#32cd32'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true, 
                    ticks: {precision: 0}
                }
            },
            plugins: {
                legend: {display: false}
            }
        }
    });
});
</script>
