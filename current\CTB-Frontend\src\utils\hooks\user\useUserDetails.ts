import { useEffect } from "react";
import { useStoreDispatch, useStoreSelector } from "../hooks";
import useUserCredentials from "./useUserCredentials";
import {
  LoginStatus,
  SetUserDetails,
  changeAuthentication,
  fetchUserDetails,
  updateUserDetails
} from "../../store/reducer/slices/userReducer";
import { LoadingState } from "../../store/reducer/reducer";
import toast from "react-hot-toast";
import { PaymentMethod } from "../../../components/settings/pages/researcher/PaymentMethodSettings";
import { useSetPaymentDetailsMutation } from "../../api/endpoints/paymentsApi";
import { checkIfValidLink } from '../../sanitizers'

/**
 * Provide access to the user details
 * stored in the global state
 */
const useUserDetails = () => {
  const dispatch = useStoreDispatch();
  const { loginStatus } = useUserCredentials();
  const [setPaymentDetailsMutation] = useSetPaymentDetailsMutation();

  // Setup selectors for user details state
  const loadingStatus = useStoreSelector(state => state.user.details.status);
  const details = useStoreSelector(state => state.user.details);

  /**
   * Enable/disable the two factor authentication on
   * this account
   */
  const setOtpEnabled = (enabled: boolean) => {
    if (details.otpEnabled !== enabled) {
      dispatch(changeAuthentication(enabled)).then(() => {
        toast.success("Updated 2FA!");
      });
    } else {
      toast("Changes already applied", {
        icon: "👍"
      });
    }
  };

  const setDetails = async (
    details: { username: string } & SetUserDetails,
    notify = true
  ) => {
    const loader = toast.loading("Saving...");
    try {
      await dispatch(updateUserDetails(details));
      await dispatch(fetchUserDetails());
      toast.remove(loader);

      if (notify) toast.success("Updated user details!");
    } catch {
      toast.remove(loader);
      toast.error("Failed to update details...");
    }
  };

  const setPaymentDetails = (method: PaymentMethod, details: unknown) => {
    setPaymentDetailsMutation({
      mode: method,
      details
    }).then(res => {
      if ("error" in res) {
        toast.error("Failed to update payment details...");
      } else {
        toast.success("Updated payment details!");
      }
    });
  };

  /**
   * Adds a link to the current users profile
   */
  const addLink = (link: string) => {
    if (!checkIfValidLink(link)) {
      toast.error('Please enter a valid link')
      return false
    }
    if (details.username) {
      setDetails(
        {
          username: details.username,
          links: [...(details.links || []), link]
        },
        false
      );
    }
  };

  /**
   * Removes a link from the current users profile
   */
  const removeLink = (index: number) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          links: details.links?.filter((_, i) => i !== index)
        },
        false
      );
    }
  };

  /**
   * Adds a skill to the current users profile
   */
  const addSkill = (skill: string) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          skills: [...(details.skills || []), skill]
        },
        false
      );
    }
  };

  /**
   * Removes a skill from the current users profile
   */
  const removeSkill = (index: number) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          skills: details.skills?.filter((_, i) => i !== index)
        },
        false
      );
    }
  };

  /**
 * Adds a language to the current users profile
 */
  const addLang = (language: string) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          languages: [...(details.languages || []), language]
        },
        false
      );
    }
  };

  /**
   * Removes a language from the current users profile
   */
  const removeLang = (index: number) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          languages: details.languages?.filter((_, i) => i !== index)
        },
        false
      );
    }
  };

  /**
   * Adds a hobby to the current users profile
   */
  const addHobby = (hobby: string) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          hobbies: [...(details.hobbies || []), hobby]
        },
        false
      );
    }
  };

  /**
   * Removes a hobby from the current users profile
   */
  const removeHobby = (index: number) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          hobbies: details.hobbies?.filter((_, i) => i !== index)
        },
        false
      );
    }
  };

  /**
* Adds an achievement to the current users profile
*/
  const addAchievement = (achievement: string) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          achievements: [...(details.achievements || []), achievement]
        },
        false
      );
    }
  };

  /**
   * Removes an achievement from the current users profile
   */
  const removeAchievement = (index: number) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          achievements: details.achievements?.filter((_, i) => i !== index)
        },
        false
      );
    }
  };

  /**
* Adds an education to the current users profile
*/
  const addEducation = (education: string) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          education: [...(details.education || []), education]
        },
        false
      );
    }
  };

  /**
   * Removes an education from the current users profile
   */
  const removeEducation = (index: number) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          education: details.education?.filter((_, i) => i !== index)
        },
        false
      );
    }
  };

  /**
* Adds an Work Experience to the current users profile
*/
  const addWorkExperience = (work_experience: string) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          work_experience: [...(details.work_experience || []), work_experience]
        },
        false
      );
    }
  };

  /**
   * Removes an Work Experience from the current users profile
   */
  const removeWorkExperience = (index: number) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          work_experience: details.work_experience?.filter((_, i) => i !== index)
        },
        false
      );
    }
  };

  const addToolsUsed = (tools_used: string) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          tools_used: [...(details.tools_used || []), tools_used]
        },
        false
      );
    }
  };

  /**
   * Removes an Work Experience from the current users profile
   */
  const removeToolsUsed = (index: number) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          tools_used: details.tools_used?.filter((_, i) => i !== index)
        },
        false
      );
    }
  };

  const addCommunityEngagement = (community_engagement: string) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          community_engagement: [...(details.community_engagement || []), community_engagement]
        },
        false
      );
    }
  };

  /**
   * Removes an Work Experience from the current users profile
   */
  const removeCommunityEngagement = (index: number) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          community_engagement: details.community_engagement?.filter((_, i) => i !== index)
        },
        false
      );
    }
  };

  const addTestimonials = (testimonials: string) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          testimonials: [...(details.testimonials || []), testimonials]
        },
        false
      );
    }
  };

  /**
   * Removes an Work Experience from the current users profile
   */
  const removeTestimonials = (index: number) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          testimonials: details.testimonials?.filter((_, i) => i !== index)
        },
        false
      );
    }
  };

  const addPublications = (publications: string) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          publications: [...(details.publications || []), publications]
        },
        false
      );
    }
  };

  /**
   * Removes an Work Experience from the current users profile
   */
  const removePublications = (index: number) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          publications: details.publications?.filter((_, i) => i !== index)
        },
        false
      );
    }
  };

  const addSecurityClearance = (security_clearance: string) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          security_clearance: [...(details.security_clearance || []), security_clearance]
        },
        false
      );
    }
  };

  /**
   * Removes an Work Experience from the current users profile
   */
  const removeSecurityClearance = (index: number) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          security_clearance: details.security_clearance?.filter((_, i) => i !== index)
        },
        false
      );
    }
  };

  const setAvailability = (availability: boolean) => {
    if (details.username) {
      setDetails(
        {
          username: details.username,
          availability: availability
        },
        false
      );
    }
  }


  // Retrieve the list of user details if none exist
  useEffect(() => {
    if (
      loginStatus === LoginStatus.SIGNED_IN &&
      loadingStatus === LoadingState.IDLE
    ) {
      dispatch(fetchUserDetails());
    }
  }, [dispatch, loadingStatus, loginStatus]);

  return {
    user: details,
    ...details,
    setOtpEnabled,
    setDetails,
    setPaymentDetails,
    addLink,
    removeLink,
    addSkill,
    removeSkill,
    addLang,
    removeLang,
    addHobby,
    removeHobby,
    addAchievement,
    removeAchievement,
    addEducation,
    removeEducation,
    addWorkExperience,
    removeWorkExperience,
    addToolsUsed,
    removeToolsUsed,
    addCommunityEngagement,
    removeCommunityEngagement,
    addTestimonials,
    removeTestimonials,
    addPublications, removePublications,
    addSecurityClearance, removeSecurityClearance,
    setAvailability
  };
};

export default useUserDetails; 
