{"ast": null, "code": "export const DEFAULT_REPORT_HTML_CONTENT = {\n  disclaimer: `<p>Capture The Bug Ltd. has prepared this document exclusively for <strong>{company_name}</strong>. Copying, or modification of this document is strictly prohibited without Capture The Bug Ltd.'s written consent, except for specific purposes when such permission is granted. This document is confidential and proprietary material of Capture The Bug Ltd. and must always be treated as such, not to be disclosed to third parties without prior consent.</p>\n<p>The information within is provided 'as-is,' without any warranty. Vulnerability assessments offer a snapshot of security at a specific moment and are not exhaustive risk assessments. New vulnerabilities may emerge post-assessment. Thus, this report is a guide, not a definitive risk analysis.</p>\n<p>Capture The Bug Ltd. assumes no liability for any changes, omissions, or errors in this document. Capture The Bug Ltd. shall not be liable for any damages, financial or otherwise, arising out of the use or misuse of this report by any current employee of <strong>{company_name}</strong> or any member of the general public.</p>`,\n  executive_summary: `<p>Capture The Bug is a modern Penetration Testing as a Service (PTaaS) platform designed to support fast-moving engineering teams and security-conscious enterprises. Organizations across New Zealand, Australia, and North America-including public companies and high-growth SaaS teams-trust Capture The Bug to secure their CI/CD pipelines, meet compliance goals, and reduce time-to-remediation without slowing development velocity. Capture The Bug is HQ in New Zealand and proudly works with customers across regulated and innovation-driven sectors.</p>\n<p><strong>{company_name}</strong> entrusted Capture The Bug to conduct a {testing_type}. This assessment evaluated the application's security posture from a gray-box perspective to focus on its resilience against common attack patterns and identify vulnerabilities in its internal and external interfaces.</p>`,\n  key_findings: `<p>Capture The Bug's thorough assessment identified <strong>{total_findings}</strong> findings, with <strong style='color:#8b0000'>{critical_total}</strong> categorized as <strong style='color:#8b0000'>Critical Severity</strong>, <strong style='color:#ff4500'>{high_total}</strong> categorized as <strong style='color:#ff4500'>High Severity</strong>, <strong style='color:#ffd700'>{medium_total}</strong> categorized as <strong style='color:#ffd700'>Medium Severity</strong> and <strong style='color:#32cd32'>{low_total}</strong> as <strong style='color:#32cd32'>Low Severity</strong>. During the assessment, all critical and high vulnerabilities were reported to the <strong>{company_name}</strong> team, and the client addressed the reported vulnerabilities concurrently.</p>\n<p>Capture The Bug team has documented the identified vulnerabilities along with their current statuses in the key findings section of this report. Prompt action is advised to improve the application's overall security posture.</p>\n`,\n  scope: `<p>The scope of the assessment was limited to performing Vulnerability Assessment and Penetration Testing on the <strong>{company_name}</strong> systems mentioned below:</p>`,\n  project_objectives: `<p>The objective of this assessment was to validate the overall security posture of the in-scope systems from a security perspective. It included determining the application's ability to resist common attack patterns and identifying vulnerable areas in the internal or external interfaces that might be exploited by a malicious user.</p>`\n};", "map": {"version": 3, "names": ["DEFAULT_REPORT_HTML_CONTENT", "disclaimer", "executive_summary", "key_findings", "scope", "project_objectives"], "sources": ["C:/Users/<USER>/Desktop/jira-old/CTB-Frontend/src/pages/automated-report/types.ts"], "sourcesContent": ["export interface DetailedFinding {\r\n  abbreviation: string;\r\n  title: string;\r\n  severity_category: 'Critical' | 'High' | 'Medium' | 'Low';\r\n  status: 'Open' | 'Closed' | 'In Progress';\r\n  scope: string;\r\n  description: string;\r\n  instructions: string;\r\n  impact: string;\r\n  fix: string;\r\n  submitted_date: string;\r\n}\r\n\r\nexport interface ProgramDetail {\r\n  testing_type: string;\r\n  description: string;\r\n}\r\n\r\nexport interface TargetDetail {\r\n  type: string;\r\n  url: string;\r\n}\r\n\r\nexport interface Finding {\r\n  id: string;\r\n  title: string;\r\n  severity: string;\r\n  status: string;\r\n  description: string;\r\n  impact: string;\r\n  remediation: string;\r\n}\r\n\r\nexport interface Report {\r\n  abbreviation: string;\r\n  title: string;\r\n  severity_category: string;\r\n  status: string;\r\n}\r\n\r\nexport interface SeverityCounts {\r\n  Open: number;\r\n  Closed: number;\r\n  Total: number;\r\n}\r\n\r\nexport const DEFAULT_REPORT_HTML_CONTENT = {\r\n  disclaimer: `<p>Capture The Bug Ltd. has prepared this document exclusively for <strong>{company_name}</strong>. Copying, or modification of this document is strictly prohibited without Capture The Bug Ltd.'s written consent, except for specific purposes when such permission is granted. This document is confidential and proprietary material of Capture The Bug Ltd. and must always be treated as such, not to be disclosed to third parties without prior consent.</p>\r\n<p>The information within is provided 'as-is,' without any warranty. Vulnerability assessments offer a snapshot of security at a specific moment and are not exhaustive risk assessments. New vulnerabilities may emerge post-assessment. Thus, this report is a guide, not a definitive risk analysis.</p>\r\n<p>Capture The Bug Ltd. assumes no liability for any changes, omissions, or errors in this document. Capture The Bug Ltd. shall not be liable for any damages, financial or otherwise, arising out of the use or misuse of this report by any current employee of <strong>{company_name}</strong> or any member of the general public.</p>`,\r\n\r\n  executive_summary: `<p>Capture The Bug is a modern Penetration Testing as a Service (PTaaS) platform designed to support fast-moving engineering teams and security-conscious enterprises. Organizations across New Zealand, Australia, and North America-including public companies and high-growth SaaS teams-trust Capture The Bug to secure their CI/CD pipelines, meet compliance goals, and reduce time-to-remediation without slowing development velocity. Capture The Bug is HQ in New Zealand and proudly works with customers across regulated and innovation-driven sectors.</p>\r\n<p><strong>{company_name}</strong> entrusted Capture The Bug to conduct a {testing_type}. This assessment evaluated the application's security posture from a gray-box perspective to focus on its resilience against common attack patterns and identify vulnerabilities in its internal and external interfaces.</p>`,\r\n\r\n  key_findings: `<p>Capture The Bug's thorough assessment identified <strong>{total_findings}</strong> findings, with <strong style='color:#8b0000'>{critical_total}</strong> categorized as <strong style='color:#8b0000'>Critical Severity</strong>, <strong style='color:#ff4500'>{high_total}</strong> categorized as <strong style='color:#ff4500'>High Severity</strong>, <strong style='color:#ffd700'>{medium_total}</strong> categorized as <strong style='color:#ffd700'>Medium Severity</strong> and <strong style='color:#32cd32'>{low_total}</strong> as <strong style='color:#32cd32'>Low Severity</strong>. During the assessment, all critical and high vulnerabilities were reported to the <strong>{company_name}</strong> team, and the client addressed the reported vulnerabilities concurrently.</p>\r\n<p>Capture The Bug team has documented the identified vulnerabilities along with their current statuses in the key findings section of this report. Prompt action is advised to improve the application's overall security posture.</p>\r\n`,\r\n\r\n  scope: `<p>The scope of the assessment was limited to performing Vulnerability Assessment and Penetration Testing on the <strong>{company_name}</strong> systems mentioned below:</p>`,\r\n\r\n  project_objectives: `<p>The objective of this assessment was to validate the overall security posture of the in-scope systems from a security perspective. It included determining the application's ability to resist common attack patterns and identifying vulnerable areas in the internal or external interfaces that might be exploited by a malicious user.</p>`\r\n};\r\n\r\nexport interface PentestReport {\r\n  id: string;\r\n  title: string;\r\n  company_name: string;\r\n  document_number: string;\r\n  status: 'draft' | 'qa_review' | 'admin_review' | 'approved' | 'rejected';\r\n  created_at: string;\r\n  updated_at: string;\r\n  current_version: number;\r\n  total_versions: number;\r\n  user: {\r\n    id: number;\r\n    name: string;\r\n    email: string;\r\n  } | null;\r\n  total_findings: number;\r\n  critical_count: number;\r\n  high_count: number;\r\n  medium_count: number;\r\n  low_count: number;\r\n  program_names?: Record<number, string>;\r\n  program_ids?: number[];\r\n}\r\n\r\nexport interface PentestReportsResponse {\r\n  status: string;\r\n  data: {\r\n    reports: PentestReport[];\r\n    pagination: {\r\n      current_page: number;\r\n      total_pages: number;\r\n      total_count: number;\r\n      limit: number;\r\n    };\r\n  };\r\n}\r\n\r\nexport interface PentestReportsFilters {\r\n  page?: number;\r\n  limit?: number;\r\n  status?: string;\r\n  company_name?: string;\r\n  date_from?: string;\r\n  date_to?: string;\r\n  sort_by?: string;\r\n  sort_order?: 'ASC' | 'DESC';\r\n} "], "mappings": "AA8CA,OAAO,MAAMA,2BAA2B,GAAG;EACzCC,UAAU,EAAG;AACf;AACA,2UAA2U;EAEzUC,iBAAiB,EAAG;AACtB,uTAAuT;EAErTC,YAAY,EAAG;AACjB;AACA,CAAC;EAECC,KAAK,EAAG,+KAA8K;EAEtLC,kBAAkB,EAAG;AACvB,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}