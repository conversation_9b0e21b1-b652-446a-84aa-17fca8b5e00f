import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { format } from "date-fns";
import { 
  FiClock, FiFileText, FiTag, FiEye, FiArrowRight, 
  FiRefreshCw, FiChevronDown, FiChevronUp 
} from "react-icons/fi";
import { Link, useNavigate } from "react-router-dom";
import { RetestAction } from "../../../utils/hooks/dashboard/useUnifiedBusinessDashboard";

interface RetestActionsTableProps {
  retestActions: RetestAction[];
  onRefresh?: () => void;
}

const RetestActionsTable: React.FC<RetestActionsTableProps> = ({ 
  retestActions, 
  onRefresh 
}) => {
  const navigate = useNavigate();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [expandedRow, setExpandedRow] = useState<number | null>(null);
  
  // Limit to 8 retests maximum for display
  const limitedRetests = retestActions.slice(0, 8);
  const hasMoreRetests = retestActions.length > 8;

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        when: "beforeChildren",
        staggerChildren: 0.07,
        delayChildren: 0.1
      }
    }
  };
  
  const rowVariants = {
    hidden: { opacity: 0, y: 15 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 220,
        damping: 20
      }
    }
  };

  const refreshIconVariants = {
    initial: { rotate: 0 },
    rotate: { 
      rotate: 360,
      transition: { 
        duration: 0.7, 
        ease: "easeInOut" 
      } 
    }
  };

  // Function to handle refresh
  const handleRefresh = () => {
    setIsRefreshing(true);
    if (onRefresh) {
      onRefresh();
    }
    
    // Reset after animation completes
    setTimeout(() => {
      setIsRefreshing(false);
    }, 700);
  };

  // Toggle mobile row expansion
  const toggleRowExpansion = (id: number) => {
    setExpandedRow(expandedRow === id ? null : id);
  };

  if (!retestActions || retestActions.length === 0) {
    return (
      <motion.div 
        className="p-8 text-center bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-neutral-200/60"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
      >
        <div className="flex flex-col items-center justify-center">
          <div className="rounded-full bg-neutral-100 p-4 shadow-inner mb-3">
            <FiClock className="h-8 w-8 text-neutral-400" />
          </div>
          <h3 className="text-lg font-medium text-neutral-800 mb-1">No retest actions yet</h3>
          <p className="text-neutral-500 max-w-sm">
            Retest actions will appear here when reports move to retest status.
          </p>
        </div>
      </motion.div>
    );
  }

  const formatDate = (dateString: string | null): string => {
    if (!dateString) return "N/A";
    try {
      return format(new Date(dateString), "MMM d, yyyy");
    } catch (e) {
      return "Invalid date";
    }
  };

  // Function to render days taken with proper handling for null values
  const renderDaysTaken = (daysTaken: number | null): JSX.Element => {
    if (daysTaken === null) {
      return (
        <span className="text-neutral-500 italic font-light">
          In progress
        </span>
      );
    }
    
    return (
      <div className="flex items-center">
        <FiClock className="mr-1.5 h-4 w-4 text-neutral-500" />
        <span>{daysTaken} day{daysTaken !== 1 ? 's' : ''}</span>
      </div>
    );
  };

  // Function to render severity badge
  const renderSeverityBadge = (severity: string, isInProgress: boolean): JSX.Element => {
    let bgColor = "bg-neutral-100";
    let textColor = "text-neutral-700";
    let glowColor = "rgba(0, 0, 0, 0.1)";
    
    switch (severity.toLowerCase()) {
      case "critical":
        bgColor = "bg-red-100/90";
        textColor = "text-red-700";
        glowColor = "rgba(220, 38, 38, 0.2)";
        break;
      case "high":
        bgColor = "bg-orange-100/90";
        textColor = "text-orange-700";
        glowColor = "rgba(234, 88, 12, 0.2)";
        break;
      case "medium":
        bgColor = "bg-yellow-100/90";
        textColor = "text-yellow-700";
        glowColor = "rgba(202, 138, 4, 0.2)";
        break;
      case "low":
        bgColor = "bg-green-100/90";
        textColor = "text-green-700";
        glowColor = "rgba(22, 163, 74, 0.2)";
        break;
    }
    
    return (
      <div className="flex items-center">
        {isInProgress ? (
          <motion.span 
            className={`px-2.5 py-1 rounded-full text-xs font-medium ${bgColor} ${textColor}`}
            style={{ boxShadow: `0 0 8px ${glowColor}` }}
            animate={{ scale: [1, 1.03, 1] }}
            transition={{ 
              repeat: Infinity, 
              duration: 2,
              repeatType: "mirror"
            }}
          >
            {severity}
          </motion.span>
        ) : (
          <span 
            className={`px-2.5 py-1 rounded-full text-xs font-medium ${bgColor} ${textColor}`}
            style={{ boxShadow: `0 0 4px ${glowColor}` }}
          >
            {severity}
          </span>
        )}
      </div>
    );
  };

  // Function to render status badge
  const renderStatusBadge = (status: string): JSX.Element => {
    let bgColor = "bg-neutral-100/90";
    let textColor = "text-neutral-700";
    let isInProgress = false;
    
    if (status.toLowerCase().includes("verified") || status.toLowerCase().includes("approved")) {
      bgColor = "bg-green-100/90";
      textColor = "text-green-700";
    } else if (status.toLowerCase().includes("failed")) {
      bgColor = "bg-red-100/90";
      textColor = "text-red-700";
    } else if (status.toLowerCase().includes("requested")) {
      bgColor = "bg-blue-100/90";
      textColor = "text-blue-700";
    } else if (status.toLowerCase().includes("progress")) {
      bgColor = "bg-yellow-100/90";
      textColor = "text-yellow-700";
      isInProgress = true;
    }
    
    return (
      <div className="flex items-center">
        {isInProgress ? (
          <motion.div 
            className={`px-2.5 py-1 rounded-full text-xs font-medium ${bgColor} ${textColor} relative overflow-hidden`}
            style={{ boxShadow: "0 0 6px rgba(250, 204, 21, 0.4)" }}
          >
            {/* Shimmer effect for in progress status */}
            <motion.div 
              className="absolute inset-0 bg-white/20"
              initial={{ x: "-100%" }}
              animate={{ x: "100%" }}
              transition={{ 
                repeat: Infinity, 
                duration: 1.5, 
                ease: "linear" 
              }}
            />
            <span className="relative z-10">{status}</span>
          </motion.div>
        ) : (
          <span className={`px-2.5 py-1 rounded-full text-xs font-medium ${bgColor} ${textColor}`}>
            {status}
          </span>
        )}
      </div>
    );
  };

  // Handle view retest click
  const handleViewRetest = (retestId: number, reportId: number) => {
    navigate(`/dashboard/retests/${retestId}`);
  };

  // Is the current view likely mobile
  const isMobileView = () => {
    return window.innerWidth < 768;
  };

  return (
    <motion.div 
      className="overflow-hidden rounded-2xl border border-neutral-200/60 shadow-lg bg-white/80 backdrop-blur-sm hover:shadow-xl transition-shadow duration-300"
      style={{ 
        boxShadow: "0 8px 32px rgba(0, 0, 0, 0.07)",
        background: "rgba(255, 255, 255, 0.85)",
        backdropFilter: "blur(12px)"
      }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="flex justify-between items-center px-6 py-4 border-b border-neutral-200/70">
        <h2 className="text-lg font-semibold text-neutral-800 flex items-center">
          <div className="w-8 h-8 rounded-full bg-blue-100/70 flex items-center justify-center mr-2">
            <FiClock className="w-4 h-4 text-blue-600" />
          </div>
          Recent Retest Actions
        </h2>
        
        <motion.button
          className="rounded-full p-2 bg-neutral-100 hover:bg-neutral-200 text-neutral-600 transition-colors"
          variants={refreshIconVariants}
          initial="initial"
          animate={isRefreshing ? "rotate" : "initial"}
          whileTap="rotate"
          onClick={handleRefresh}
          disabled={isRefreshing}
          aria-label="Refresh retest actions"
        >
          <FiRefreshCw className="w-4 h-4" />
        </motion.button>
      </div>

      {/* Table for desktop */}
      <div className="hidden md:block overflow-hidden">
        <div className="overflow-x-auto">
          <motion.table 
            className="min-w-full divide-y divide-neutral-200"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <thead className="bg-neutral-50/80 backdrop-blur-sm">
              <tr>
                <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-neutral-700 w-[30%]">Report Title</th>
                <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-neutral-700">Severity</th>
                <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-neutral-700">Asset</th>
                <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-neutral-700">Status</th>
                <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-neutral-700">Updated</th>
                <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-neutral-700">Days Taken</th>
                <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-neutral-700 text-center">Action</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-neutral-200 bg-white/60">
              {limitedRetests.map((action, index) => (
                <motion.tr 
                  key={action.retest_id} 
                  className="hover:bg-blue-50/40 group relative"
                  variants={rowVariants}
                  whileHover={{
                    backgroundColor: "rgba(239, 246, 255, 0.6)",
                    boxShadow: "0 0 15px rgba(59, 130, 246, 0.1)",
                    transition: { duration: 0.2 }
                  }}
                  aria-current={action.status.toLowerCase().includes("progress") ? "true" : undefined}
                >
                  <td className="px-4 py-3 text-sm text-neutral-800">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 mr-2">
                        <div className="h-7 w-7 rounded-full bg-blue-50 flex items-center justify-center">
                          <FiFileText className="h-3.5 w-3.5 text-blue-600" />
                        </div>
                      </div>
                      <div className="truncate max-w-[250px]">
                        <span 
                          className="font-medium hover:text-blue-600 transition-colors cursor-pointer" 
                          title={action.title}
                          onClick={(e: React.MouseEvent) => {
                            e.stopPropagation();
                            handleViewRetest(action.retest_id, action.report_id);
                          }}
                        >
                          {action.title}
                        </span>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-sm whitespace-nowrap">
                    {renderSeverityBadge(
                      action.severity_category, 
                      action.status.toLowerCase().includes("progress")
                    )}
                  </td>
                  <td className="px-4 py-3 text-sm">
                    {action.scope ? (
                      <div className="flex items-center">
                        <div className="h-6 w-6 rounded-full bg-neutral-100 flex items-center justify-center mr-1.5">
                          <FiTag className="h-3 w-3 text-neutral-600" />
                        </div>
                        <span className="truncate max-w-[120px]" title={action.scope}>
                          {action.scope}
                        </span>
                      </div>
                    ) : (
                      <span className="text-neutral-400">Not specified</span>
                    )}
                  </td>
                  <td className="px-4 py-3 text-sm whitespace-nowrap">
                    {renderStatusBadge(action.status)}
                  </td>
                  <td className="px-4 py-3 text-sm text-neutral-600 whitespace-nowrap">
                    {formatDate(action.updated_date)}
                  </td>
                  <td className="px-4 py-3 text-sm text-neutral-600 whitespace-nowrap">
                    {renderDaysTaken(action.days_taken)}
                  </td>
                  <td className="px-4 py-3 text-sm text-center relative">
                    <motion.button 
                      onClick={(e: React.MouseEvent) => {
                        e.stopPropagation();
                        handleViewRetest(action.retest_id, action.report_id);
                      }}
                      className="inline-flex items-center justify-center rounded-md bg-blue-600 px-3 py-1.5 text-xs font-medium text-white hover:bg-blue-700 transition-colors"
                      whileHover={{ 
                        scale: 1.05,
                        boxShadow: "0 4px 12px rgba(37, 99, 235, 0.3)"
                      }}
                      whileTap={{ scale: 0.95 }}
                      aria-label={`View retest details for ${action.title}`}
                    >
                      <FiEye className="mr-1.5 h-3.5 w-3.5" />
                      View
                    </motion.button>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </motion.table>
        </div>
      </div>
      
      {/* Mobile view with accordion */}
      <div className="md:hidden">
        <motion.div 
          className="divide-y divide-neutral-200 bg-white/60"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {limitedRetests.map((action, index) => (
            <motion.div 
              key={action.retest_id}
              className="relative"
              variants={rowVariants}
            >
              <div 
                className={`flex items-center justify-between px-4 py-3 ${
                  expandedRow === action.retest_id ? 'bg-blue-50/60' : ''
                }`}
                onClick={() => toggleRowExpansion(action.retest_id)}
              >
                <div className="flex-grow truncate pr-10">
                  <div className="flex items-center">
                    <div className="h-7 w-7 rounded-full bg-blue-50 flex items-center justify-center mr-2">
                      <FiFileText className="h-3.5 w-3.5 text-blue-600" />
                    </div>
                    <div className="truncate">
                      <span className="font-medium text-neutral-800">{action.title}</span>
                    </div>
                  </div>
                </div>
                <div className="flex-shrink-0 flex items-center gap-2">
                  {renderSeverityBadge(
                    action.severity_category, 
                    action.status.toLowerCase().includes("progress")
                  )}
                  {expandedRow === action.retest_id ? (
                    <FiChevronUp className="h-5 w-5 text-neutral-500" />
                  ) : (
                    <FiChevronDown className="h-5 w-5 text-neutral-500" />
                  )}
                </div>
              </div>
              
              <AnimatePresence>
                {expandedRow === action.retest_id && (
                  <motion.div 
                    className="px-4 py-3 bg-neutral-50/70 border-t border-neutral-100"
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                  >
                    <div className="space-y-2.5">
                      <div className="flex justify-between">
                        <div className="text-sm font-medium text-neutral-500">Asset:</div>
                        <div className="text-sm text-right">
                          {action.scope ? (
                            <div className="flex items-center">
                              <FiTag className="h-3.5 w-3.5 text-neutral-500 mr-1.5" />
                              <span className="truncate max-w-[160px]">{action.scope}</span>
                            </div>
                          ) : (
                            <span className="text-neutral-400">Not specified</span>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex justify-between">
                        <div className="text-sm font-medium text-neutral-500">Status:</div>
                        <div className="text-sm">{renderStatusBadge(action.status)}</div>
                      </div>
                      
                      <div className="flex justify-between">
                        <div className="text-sm font-medium text-neutral-500">Updated:</div>
                        <div className="text-sm text-neutral-700">{formatDate(action.updated_date)}</div>
                      </div>
                      
                      <div className="flex justify-between">
                        <div className="text-sm font-medium text-neutral-500">Days Taken:</div>
                        <div className="text-sm text-neutral-700">{renderDaysTaken(action.days_taken)}</div>
                      </div>
                      
                      <div className="pt-1.5">
                        <motion.button 
                          onClick={(e: React.MouseEvent) => {
                            e.stopPropagation();
                            handleViewRetest(action.retest_id, action.report_id);
                          }}
                          className="w-full inline-flex items-center justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-medium text-white hover:bg-blue-700 transition-colors"
                          whileHover={{ 
                            scale: 1.02,
                            boxShadow: "0 4px 12px rgba(37, 99, 235, 0.3)"
                          }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <FiEye className="mr-1.5 h-4 w-4" />
                          View Retest
                        </motion.button>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </motion.div>
      </div>
      
      {/* Footer with "View More" button */}
      {hasMoreRetests && (
        <div className="bg-neutral-50/80 px-4 py-3 border-t border-neutral-200 flex justify-center">
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Link 
              to="/dashboard/retests" 
              className="inline-flex items-center px-4 py-2 border border-neutral-300/80 rounded-md shadow-sm text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all"
            >
              View All Retests
              <FiArrowRight className="ml-2 -mr-0.5 h-4 w-4" />
            </Link>
          </motion.div>
        </div>
      )}
    </motion.div>
  );
};

export default RetestActionsTable; 