// EditorLayout.tsx - Presentational component for main editor UI in PDFEditor
import React from 'react';
import EditorNavigation from './components/EditorNavigation';
import CoverPageEditor from './components/editors/CoverPageEditor';
import DocumentReferenceEditor from './components/editors/DocumentReferenceEditor';
import ExecutiveSummaryEditor from './components/editors/ExecutiveSummaryEditor';
import DisclaimerEditor from './components/editors/DisclaimerEditor';
import MethodologyEditor from './components/editors/MethodologyEditor';
import RecommendationsEditor from './components/editors/RecommendationsEditor';
import ConclusionEditor from './components/editors/ConclusionEditor';
import FindingsEditor from './components/editors/FindingsEditor';
import TargetDetailsEditor from './components/editors/TargetDetailsEditor';
import ProjectObjectivesEditor from './components/editors/ProjectObjectivesEditor';
import ScopeEditor from './components/editors/ScopeEditor';
import FindingsSummaryEditor from './components/editors/FindingsSummaryEditor';
import VulnerabilityRatingsEditor from './components/editors/VulnerabilityRatingsEditor';
import CriticalFindingsEditor from './components/editors/CriticalFindingsEditor';
import HighFindingsEditor from './components/editors/HighFindingsEditor';
import MediumFindingsEditor from './components/editors/MediumFindingsEditor';
import LowFindingsEditor from './components/editors/LowFindingsEditor';
import KeyFindingsEditor from './components/editors/KeyFindingsEditor';
import PDFDownloader from './components/PDFDownloader';
import PreviewSection from './components/PreviewSection';
import ReportTemplate from './components/ReportTemplate';
import ChangeLogSidebar from './components/ChangeLogSidebar';
import SectionRenderer from './SectionRenderer';
import { ReportData } from './types/report.types';

interface EditorLayoutProps {
  currentData: any;
  reportData: any;
  editorWidth: string;
  dragging: React.MutableRefObject<boolean>;
  setEditorWidth: (width: string) => void;
  activeSection: string;
  setActiveSection: (section: string) => void;
  openChangeLog: () => void;
  handleSave: () => void;
  saveLoading: boolean;
  approveLoading: boolean;
  rejectLoading: boolean;
  canApprove: boolean;
  handleApprove: () => void;
  canReject: boolean;
  handleReject: () => void;
  reportDataStatus: string | undefined;
  handleInputChange: (field: string, value: string) => void;
  handleHtmlChange: (field: string, value: string) => void;
  handleDataChange: (field: string, value: any) => void;
  handleFindingChange: (index: number, field: string | keyof any, value: string, severity: string) => void;
  handleRemoveFinding: (index: number, severity: string) => void;
  handleAddFinding: (severity: string) => void;
  handleTargetDetailsChange: (index: number, field: string, value: string) => void;
  handleAddTarget: () => void;
  handleRemoveTarget: (index: number) => void;
  handleTableChange: (field: string, value: number) => void;
  handleKeyFindingChange: (index: number, field: string, value: string) => void;
  handleAddKeyFinding: () => void;
  handleRemoveKeyFinding: (index: number) => void;
  handleRecommendationsChange: (recommendations: { title: string; description: string }[]) => void;
  saveSectionChanges: () => void;
  previewRef: React.RefObject<HTMLDivElement>;
  openFullView: () => void;
  previewMode: string;
  setPreviewMode: (mode: string) => void;
  previewHtml: string;
  isFullViewOpen: boolean;
  closeFullView: () => void;
  notification: any;
  hideNotification: () => void;
  isChangeLogOpen: boolean;
  closeChangeLog: () => void;
  changeLog: any[];
  changeLogLoading: boolean;
  previewLoading: boolean;
  sectionData: any;
  children?: React.ReactNode;
}

const EditorLayout: React.FC<EditorLayoutProps> = (props) => {
  // Destructure all props for clarity
  const {
    currentData,
    reportData,
    editorWidth,
    dragging,
    setEditorWidth,
    activeSection,
    setActiveSection,
    openChangeLog,
    handleSave,
    saveLoading,
    approveLoading,
    rejectLoading,
    canApprove,
    handleApprove,
    canReject,
    handleReject,
    reportDataStatus,
    handleInputChange,
    handleHtmlChange,
    handleDataChange,
    handleFindingChange,
    handleRemoveFinding,
    handleAddFinding,
    handleTargetDetailsChange,
    handleAddTarget,
    handleRemoveTarget,
    handleTableChange,
    handleKeyFindingChange,
    handleAddKeyFinding,
    handleRemoveKeyFinding,
    handleRecommendationsChange,
    saveSectionChanges,
    previewRef,
    openFullView,
    previewMode,
    setPreviewMode,
    previewHtml,
    isFullViewOpen,
    closeFullView,
    notification,
    hideNotification,
    isChangeLogOpen,
    closeChangeLog,
    changeLog,
    changeLogLoading,
    previewLoading,
    sectionData,
    children
  } = props;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="flex h-screen overflow-hidden select-none">
        {/* Editor Section - Premium Glassy Redesign */}
        <div
          className="ctb-editor-pane flex flex-col h-full overflow-hidden bg-white/60 backdrop-blur-xl border-r border-gray-200 shadow-2xl relative z-10"
          style={{ width: editorWidth, minWidth: 0, maxWidth: '100%', transition: dragging.current ? 'none' : 'width 0.2s' }}
        >
          {/* Professional Blue Header */}
          <div className="sticky top-0 z-30 w-full bg-blue-700 px-6 py-3 flex flex-col">
            <div className="flex items-center justify-between">
              <h2 className="text-white text-base font-bold flex items-center gap-2">
                <span className="inline-block w-2 h-2 rounded-full bg-white/80 animate-pulse"></span>
                Edit Report
              </h2>
              <div className="flex items-center gap-2">
                <button
                  onClick={openChangeLog}
                  className="px-2 py-1 min-w-[70px] rounded text-xs font-semibold bg-white text-blue-700 border border-blue-200 hover:bg-blue-50 transition-all duration-200 flex items-center gap-1"
                >
                  <svg className="w-3.5 h-3.5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                  Edit Logs
                </button>
                <button 
                  onClick={handleSave}
                  className={`px-2 py-1 min-w-[55px] rounded text-xs font-bold bg-white text-blue-700 border border-blue-200 hover:bg-blue-50 transition-all duration-200 flex items-center gap-1 ${
                    saveLoading
                      ? 'opacity-60 cursor-not-allowed' : ''
                  }`}
                  disabled={saveLoading}
                >
                  <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" /></svg>
                  {saveLoading ? 'Saving...' : 'Save'}
                </button>
                {canApprove && (
                  <button 
                    onClick={handleApprove}
                    className={`px-2 py-1 min-w-[70px] rounded text-xs font-bold bg-white text-green-700 border border-green-200 hover:bg-green-50 transition-all duration-200 flex items-center gap-1 ${
                      approveLoading
                        ? 'opacity-60 cursor-not-allowed' : ''
                    }`}
                    disabled={approveLoading}
                  >
                    <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" /></svg>
                    {approveLoading ? 'Approving...' : 'Approve'}
                  </button>
                )}
                {canReject && (
                  <button 
                    onClick={handleReject}
                    className={`px-2 py-1 min-w-[70px] rounded text-xs font-bold bg-white text-red-700 border border-red-200 hover:bg-red-50 transition-all duration-200 flex items-center gap-1 ${
                      rejectLoading
                        ? 'opacity-60 cursor-not-allowed' : ''
                    }`}
                    disabled={rejectLoading}
                  >
                    <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" /></svg>
                    {rejectLoading ? 'Rejecting...' : 'Reject'}
                  </button>
                )}
              </div>
            </div>
            {reportData && (
              <div className="mt-2 flex items-center gap-2">
                <span className={`px-3 py-1 text-xs font-bold rounded-full bg-white shadow-sm ${
                  reportDataStatus === 'draft' ? 'text-slate-700' :
                  reportDataStatus === 'qa_review' ? 'text-amber-700' :
                  reportDataStatus === 'admin_review' ? 'text-blue-700' :
                  reportDataStatus === 'approved' ? 'text-green-700' :
                  reportDataStatus === 'rejected' ? 'text-red-700' :
                  reportDataStatus === 'business_review' ? 'text-blue-800' :
                  reportDataStatus === 'business_requested_changes' ? 'text-red-800' :
                  reportDataStatus === 'changes_added' ? 'text-amber-800' :
                  reportDataStatus === 'report_updated' ? 'text-blue-900' :
                  reportDataStatus === 'business_approved' ? 'text-green-900' :
                  'text-red-700'
                }`}>
                  {(() => {
                    switch (reportDataStatus) {
                      case 'draft': return 'Draft';
                      case 'qa_review': return 'QA Review';
                      case 'admin_review': return 'Admin Review';
                      case 'approved': return 'Approved';
                      case 'rejected': return 'Rejected';
                      case 'business_review': return 'Business Review';
                      case 'business_requested_changes': return 'Business Requested Changes';
                      case 'changes_added': return 'Changes Added (QA)';
                      case 'report_updated': return 'Report Updated (Admin)';
                      case 'business_approved': return 'Business Approved';
                      default: return reportDataStatus?.toUpperCase() || 'UNKNOWN';
                    }
                  })()}
                </span>
              </div>
            )}
          </div>
          {/* Navigation */}
          <div className="border-b border-transparent bg-transparent">
            <EditorNavigation
              activeSection={activeSection}
              onSectionChange={setActiveSection}
            />
          </div>
          {/* Editor Content */}
          <div className="flex-1 overflow-y-auto p-6 bg-transparent">
            <div className="max-w-3xl mx-auto">
              <SectionRenderer
                activeSection={activeSection}
                currentData={currentData}
                sectionData={props.sectionData}
                handleInputChange={handleInputChange}
                handleHtmlChange={handleHtmlChange}
                handleDataChange={handleDataChange}
                handleFindingChange={handleFindingChange}
                handleRemoveFinding={handleRemoveFinding}
                handleAddFinding={handleAddFinding}
                handleTargetDetailsChange={handleTargetDetailsChange}
                handleAddTarget={handleAddTarget}
                handleRemoveTarget={handleRemoveTarget}
                handleTableChange={handleTableChange}
                handleKeyFindingChange={handleKeyFindingChange}
                handleAddKeyFinding={handleAddKeyFinding}
                handleRemoveKeyFinding={handleRemoveKeyFinding}
                handleRecommendationsChange={handleRecommendationsChange}
              />
            </div>
          </div>
        </div>
        {/* Draggable Divider */}
        <div
          className="group flex-shrink-0 w-2 cursor-col-resize relative z-20"
          style={{ userSelect: 'none' }}
          onMouseDown={() => { dragging.current = true; }}
        >
          <div className="absolute left-1/2 top-0 -translate-x-1/2 h-full w-1 rounded bg-gradient-to-b from-blue-200 via-blue-400 to-blue-200 shadow-lg group-hover:from-blue-300 group-hover:to-blue-500 group-hover:shadow-xl transition-all duration-200" />
        </div>
        {/* Preview Section */}
        <div className="flex-1 h-full overflow-hidden bg-gradient-to-br from-blue-50/80 to-blue-100/60">
          <div className="h-full flex flex-col">
            <div className="flex flex-col items-end">
              <div className="flex gap-3">
                {reportData && (
                  <PDFDownloader 
                    reportData={currentData} 
                    fileName={`${reportData.company_name}-security-report.pdf`}
                  />
                )}
              </div>
            </div>
            <div className="flex-1 overflow-y-auto">
              {(() => {
                const mergedData = { ...currentData, ...props.sectionData };
                return (
                  <PreviewSection 
                    reportData={mergedData}
                    onFullView={openFullView}
                    initialPreviewMode={['full', 'technical'].includes(previewMode) ? previewMode as 'full' | 'technical' : 'full'}
                    onPreviewModeChange={setPreviewMode}
                    previewHtml={previewHtml}
                  >
                    <div ref={previewRef} className="report-template-print">
                      <ReportTemplate reportData={mergedData} />
                    </div>
                  </PreviewSection>
                );
              })()}
            </div>
          </div>
        </div>
      </div>
      <ChangeLogSidebar
        isOpen={isChangeLogOpen}
        onClose={closeChangeLog}
        changeLog={changeLog}
        loading={changeLogLoading}
      />
      {children}
    </div>
  );
};

export default EditorLayout; 