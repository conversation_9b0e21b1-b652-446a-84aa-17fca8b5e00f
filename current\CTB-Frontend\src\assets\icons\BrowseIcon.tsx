const BrowseIcon = ({ className, color = "#3056D3" }: { className?: string; color?: string }) => (
    <svg
      width="40"
      height="40"
      viewBox="0 0 45 45"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* Cloud shape */}
      <path
        d="M32 18C31.4 13.7 27.8 10.5 23.5 10.5C19.8 10.5 16.6 12.9 15.4 16C11.5 16.2 8.5 19.2 8.5 23C8.5 26.9 11.6 30 15.5 30H32C35.3 30 38 27.3 38 24C38 20.9 35.4 18.3 32 18Z"
        fill="none"
        stroke={color}
        strokeWidth="3"
      />
      
      {/* Upload arrow */}
      <path
        d="M22 27V18"
        stroke={color}
        strokeWidth="3"
        strokeLinecap="round"
      />
      <polyline
        points="18,22 22,18 26,22"
        stroke={color}
        strokeWidth="3"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
      />
    </svg>
  );
  
  export default BrowseIcon;
  