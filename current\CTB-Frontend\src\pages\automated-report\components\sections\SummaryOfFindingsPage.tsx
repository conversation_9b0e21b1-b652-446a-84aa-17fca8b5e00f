import React from 'react';
import { Page, View, Text, Image, StyleSheet } from '@react-pdf/renderer';
import { ReportData } from '../../types/report.types';
import { useSectionPages } from '../SectionPageContext';

interface SummaryOfFindingsPageProps {
  reportData: ReportData;
  pieChartImage?: string;
  barChartImage?: string;
  sectionId?: string;
}

const styles = StyleSheet.create({
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    fontSize: 10,
    color: 'grey',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  footerLeft: {
    textAlign: 'left',
    flex: 1,
  },
  footerRight: {
    textAlign: 'right',
    flex: 1,
  },
});

const SummaryOfFindingsPage: React.FC<SummaryOfFindingsPageProps> = ({ reportData, pieChartImage, barChartImage, sectionId }) => {
  const { updateSectionPage } = useSectionPages();
  return (
    <Page size="A4" id={sectionId} style={{
      flexDirection: 'column',
      backgroundColor: '#ffffff',
      padding: '20mm 15mm',
      fontFamily: 'Helvetica',
      fontSize: 10,
    }}>
      <View style={{ flex: 1}}>
        <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#2563eb', marginBottom: 16 }}>
          SUMMARY OF FINDINGS
        </Text>
        <Text style={{ fontSize: 13, marginBottom: 12, color: '#374151' }}>
          This table provides the summary of the vulnerabilities that were identified during the assessment of {reportData.company_name}'s systems:
        </Text>
        {/* Summary Table */}
        <View style={{
          borderRadius: 14,
          overflow: 'hidden',
          marginBottom: 20,
          backgroundColor: '#f8fafc',
          width: '90%',
          alignSelf: 'center',
        }}>
          {/* Table Header */}
          <View style={{ flexDirection: 'row', backgroundColor: '#2563eb', borderTopLeftRadius: 14, borderTopRightRadius: 14 }}>
            <Text style={{ padding: 14, fontWeight: 'bold', width: '10%', color: '#fff', fontSize: 13, letterSpacing: 1, textAlign: 'center' }}> </Text>
            <Text style={{ padding: 14, fontWeight: 'bold', width: '56%', color: '#fff', fontSize: 13, letterSpacing: 1 }}>Severity</Text>
            <Text style={{ padding: 14, fontWeight: 'bold', width: '34%', color: '#fff', fontSize: 13, letterSpacing: 1, textAlign: 'center' }}>Total</Text>
          </View>
          {/* Total Findings Row */}
          <View style={{ flexDirection: 'row', backgroundColor: '#e0e7ef', borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>
            <Text style={{ padding: 14, width: '10%' }}></Text>
            <Text style={{ padding: 14, width: '56%', fontWeight: 'bold', color: '#2563eb', fontSize: 13 }}>Total Findings</Text>
            <Text style={{ padding: 14, width: '34%', fontWeight: 'bold', textAlign: 'center', color: '#2563eb', fontSize: 13 }}>{reportData.total_findings}</Text>
          </View>
          {/* Critical Row */}
          <View style={{ flexDirection: 'row', backgroundColor: '#fef2f2', borderBottomWidth: 1, borderBottomColor: '#e5e7eb', alignItems: 'center' }}>
            <View style={{ padding: 12, width: '10%', alignItems: 'center', justifyContent: 'center' }}>
              <View style={{ minWidth: 24, minHeight: 24, backgroundColor: '#B91C1C', borderRadius: 12, alignItems: 'center', justifyContent: 'center' }}>
                <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 12 }}>!</Text>
              </View>
            </View>
            <Text style={{ padding: 12, width: '56%', fontWeight: '600', color: '#B91C1C', fontSize: 12 }}>Critical</Text>
            <Text style={{ padding: 12, width: '34%', fontWeight: 'bold', textAlign: 'center', fontSize: 12 }}>{reportData.open_close_counts_by_severity?.Critical?.Total || 0}</Text>
          </View>
          {/* High Row */}
          <View style={{ flexDirection: 'row', backgroundColor: '#fff7ed', borderBottomWidth: 1, borderBottomColor: '#e5e7eb', alignItems: 'center' }}>
            <View style={{ padding: 12, width: '10%', alignItems: 'center', justifyContent: 'center' }}>
              <View style={{ minWidth: 24, minHeight: 24, backgroundColor: '#F59E42', borderRadius: 12, alignItems: 'center', justifyContent: 'center' }}>
                <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 12 }}>!</Text>
              </View>
            </View>
            <Text style={{ padding: 12, width: '56%', fontWeight: '600', color: '#F59E42', fontSize: 12 }}>High</Text>
            <Text style={{ padding: 12, width: '34%', fontWeight: 'bold', textAlign: 'center', fontSize: 12 }}>{reportData.open_close_counts_by_severity?.High?.Total || 0}</Text>
          </View>
          {/* Medium Row */}
          <View style={{ flexDirection: 'row', backgroundColor: '#fef9c3', borderBottomWidth: 1, borderBottomColor: '#e5e7eb', alignItems: 'center' }}>
            <View style={{ padding: 12, width: '10%', alignItems: 'center', justifyContent: 'center' }}>
              <View style={{ minWidth: 24, minHeight: 24, backgroundColor: '#FACC15', borderRadius: 12, alignItems: 'center', justifyContent: 'center' }}>
                <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 12 }}>!</Text>
              </View>
            </View>
            <Text style={{ padding: 12, width: '56%', fontWeight: '600', color: '#FACC15', fontSize: 12 }}>Medium</Text>
            <Text style={{ padding: 12, width: '34%', fontWeight: 'bold', textAlign: 'center', fontSize: 12 }}>{reportData.open_close_counts_by_severity?.Medium?.Total || 0}</Text>
          </View>
          {/* Low Row */}
          <View style={{ flexDirection: 'row', backgroundColor: '#dcfce7', alignItems: 'center' }}>
            <View style={{ padding: 12, width: '10%', alignItems: 'center', justifyContent: 'center' }}>
              <View style={{ minWidth: 24, minHeight: 24, backgroundColor: '#22C55E', borderRadius: 12, alignItems: 'center', justifyContent: 'center' }}>
                <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 12 }}>i</Text>
              </View>
            </View>
            <Text style={{ padding: 12, width: '56%', fontWeight: '600', color: '#22C55E', fontSize: 12 }}>Low</Text>
            <Text style={{ padding: 12, width: '34%', fontWeight: 'bold', textAlign: 'center', fontSize: 12 }}>{reportData.open_close_counts_by_severity?.Low?.Total || 0}</Text>
          </View>
        </View>
        <Text style={{ fontSize: 12, color: '#64748b', textAlign: 'center', marginBottom: 16 }}>Table 2: Summary of Findings</Text>
        <Text style={{ fontSize: 13, marginBottom: 8, color: '#374151' }}>
          The following charts illustrate the distribution of vulnerabilities by severity and status:
        </Text>
        {/* Centered Pie Chart Only */}
        <View style={{ flexDirection: 'row', justifyContent: 'center', marginBottom: 16 }}>
          <View style={{ width: '48%', borderWidth: 1, borderColor: '#e5e7eb', borderRadius: 8, padding: 20, backgroundColor: '#f9fafb', alignItems: 'center' }}>
            <Text style={{ fontSize: 14, fontWeight: 'bold', textAlign: 'center', marginBottom: 8 }}>
              Vulnerability Distribution by Severity
            </Text>
            {pieChartImage ? (
              <Image src={pieChartImage} style={{ width: 160, height: 160, objectFit: 'contain', marginBottom: 8 }} />
            ) : (
              <View style={{ width: 160, height: 160, backgroundColor: '#e5e7eb', alignItems: 'center', justifyContent: 'center', borderRadius: 90 }}>
                <Text style={{ color: '#64748b', fontSize: 12 }}>No Chart</Text>
              </View>
            )}
          </View>
        </View>
        {/*
        // Bar chart commented out as per request
        <View style={{ width: '48%', borderWidth: 1, borderColor: '#e5e7eb', borderRadius: 8, padding: 20, backgroundColor: '#f9fafb', alignItems: 'center' }}>
          <Text style={{ fontSize: 14, fontWeight: 'bold', textAlign: 'center', marginBottom: 8 }}>
            Vulnerability Status
          </Text>
          {barChartImage ? (
            <Image src={barChartImage} style={{ width: 160, height: 160, objectFit: 'contain', marginBottom: 8 }} />
          ) : (
            <View style={{ width: 160, height: 160, backgroundColor: '#e5e7eb', alignItems: 'center', justifyContent: 'center', borderRadius: 12 }}>
              <Text style={{ color: '#64748b', fontSize: 12 }}>No Chart</Text>
            </View>
          )}
        </View>
        */}
        <Text style={{ fontSize: 12, color: '#64748b', textAlign: 'center', marginTop: 8 }}>
          Figure 1: Visualization of Vulnerabilities by Severity and Status
        </Text>
        <Text style={{ display: 'none' }} render={({ pageNumber }) => ''} fixed />
      </View>
      <View style={styles.footer} fixed>
        <Text style={styles.footerLeft}>{reportData.document_number || 'Document Number'}</Text>
        <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
      </View>
    </Page>
  );
};

export default SummaryOfFindingsPage; 