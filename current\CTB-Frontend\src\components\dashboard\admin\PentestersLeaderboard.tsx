import React, { useMemo } from "react";
import { FaUsers, FaExclamationTriangle } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { LiaMedalSolid } from "react-icons/lia";

type Pentester = {
  userId: number;
  username: string;
  email: string;
  pfp: string | null; // Profile picture URL
  severityCounts: Array<{
    severity: string;
    count: number;
  }>;
  totalReportsCreated: number;
  triageRejectedCount: number;
  programs: Array<{
    programId: number;
    programTitle: string;
  }>;
};

type PentestersLeaderboardProps = {
  pentesters: Pentester[];
};

const PentestersLeaderboard: React.FC<PentestersLeaderboardProps> = ({
  pentesters,
}) => {
  // Hook for navigation
  const navigate = useNavigate();

  // Function to calculate credit points
  const calculateCredits = (severityCounts: Pentester["severityCounts"]) => {
    const points = {
      LOW: 5,
      MEDIUM: 10,
      HIGH: 20,
      CRITICAL: 40,
    };

    return severityCounts.reduce(
      (sum, { severity, count }) =>
        sum + (points[severity as keyof typeof points] || 0) * count,
      0
    );
  };

  const sortedPentesters = useMemo(() => {
    return [...pentesters]
      .map((pentester) => ({
        ...pentester,
        totalCredits: calculateCredits(pentester.severityCounts),
      }))
      .sort((a, b) => b.totalCredits - a.totalCredits);
  }, [pentesters]);

  return (
    <div className="w-full overflow-hidden rounded-xl border border-gray-100 bg-white shadow-lg">
      <div className="sticky top-0 z-10 flex items-center justify-between gap-28 bg-blue-700 p-6">
        <div className="flex items-center space-x-4">
          <LiaMedalSolid className="h-8 w-8 text-yellow-400" />
          <h2 className="text-lg font-bold text-white">Pentester Leaderboard</h2>
        </div>
        <div className="text-sm font-medium text-white">
          Total Pentesters: {sortedPentesters.length}
        </div>
      </div>

      <div
        className="scrollbar-thin scrollbar-track-gray-100 scrollbar-thumb-gray-300 overflow-y-auto"
        style={{
          maxHeight: "calc(4 * 85px + 20px)",
          scrollbarWidth: "thin",
          scrollbarColor: "#D1D5DB #F3F4F6",
        }}
      >
        <table className="w-full">
          <thead className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50">
            <tr>
              <th className="px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider text-gray-600">
                Pentester
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider text-gray-600">
                Severity Counts
              </th>
              <th className="px-6 py-4 text-center text-xs font-semibold uppercase tracking-wider text-gray-600">
                Approved
              </th>
              <th className="px-6 py-4 text-center text-xs font-semibold uppercase tracking-wider text-gray-600">
                Rejected
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider text-gray-600">
                Programs
              </th>
              <th className="px-6 py-4 text-center text-xs font-semibold uppercase tracking-wider text-gray-600">
                Score
              </th>
            </tr>
          </thead>
          <tbody>
            {sortedPentesters.length === 0 ? (
              <tr>
                <td colSpan={6} className="py-8 text-center text-gray-500">
                  <div className="flex flex-col items-center space-y-4">
                    <FaUsers className="h-12 w-12 text-gray-300" />
                    <p className="text-lg">No pentesters found</p>
                  </div>
                </td>
              </tr>
            ) : (
              sortedPentesters.map((pentester) => (
                <tr
                  key={pentester.userId}
                  className="group border-b border-gray-100 transition-colors duration-200 last:border-b-0 hover:bg-blue-50"
                >
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-4">
                      {pentester.pfp ? (
                        <img
                          src={pentester.pfp}
                          alt={pentester.username}
                          className="h-10 w-10 rounded-full object-cover"
                        />
                      ) : (
                        <div className="h-10 w-10 rounded-full bg-gray-300" />
                      )}
                      <div className="flex flex-col">
                        <span className="text-sm font-bold text-gray-900 transition-colors group-hover:text-blue-600">
                          @{pentester.username}
                        </span>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="grid grid-cols-2 gap-2 text-xs font-medium lowercase text-gray-700">
                      {pentester.severityCounts
                        .filter((severityCount) => severityCount.count > 0)
                        .map((severityCount) => (
                          <div
                            key={severityCount.severity}
                            className="flex items-center space-x-2"
                          >
                            <FaExclamationTriangle
                              className={`h-4 w-4 ${severityCount.severity === "CRITICAL"
                                  ? "text-red-500"
                                  : severityCount.severity === "HIGH"
                                    ? "text-orange-500"
                                    : severityCount.severity === "MEDIUM"
                                      ? "text-yellow-500"
                                      : "text-green-500"
                                }`}
                            />
                            <span>
                              {severityCount.severity}: {severityCount.count}
                            </span>
                          </div>
                        ))}
                    </div>
                  </td>

                  <td className="px-6 py-4 text-center">
                    <div className="text-sm font-medium text-gray-900">
                      {pentester.totalReportsCreated}
                    </div>
                  </td>
                  <td className="px-6 py-4 text-center">
                    <div className="text-sm font-medium text-gray-900">
                      {pentester.triageRejectedCount}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <ul className="space-y-1 text-xs font-medium text-gray-500">
                      {pentester.programs
                        .sort((a, b) => b.programId - a.programId)
                        .slice(0, 3)
                        .map((program) => (
                          <li key={program.programId}>{program.programTitle}</li>
                        ))}
                      {pentester.programs.length > 3 && (
                        <li className="text-gray-400">
                          + {pentester.programs.length - 3} more
                        </li>
                      )}
                    </ul>
                  </td>
                  <td className="px-6 py-4 text-center">
                    <div className="text-sm font-medium text-gray-900">
                      {pentester.totalCredits} {/* Display Total Score */}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default PentestersLeaderboard;
