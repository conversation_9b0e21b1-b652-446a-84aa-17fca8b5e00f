import { useState, useEffect } from "react";

interface FilterBarProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  programOptions?: string[];
  onProgramFilter?: (program: string) => void;
  onVersionFilter?: (version: string) => void;
  onSortChange?: (sortOrder: "asc" | "desc") => void;
  onClearFilters?: () => void;
}

export default function FilterBar({
  searchValue,
  onSearchChange,
  programOptions = [],
  onProgramFilter,
  onVersionFilter,
  onSortChange,
  onClearFilters
}: FilterBarProps) {
  const [selectedProgram, setSelectedProgram] = useState<string>("");
  const [selectedVersion, setSelectedVersion] = useState<string>("");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc"); // Default to newest first

  // Version options
  const versionOptions = [
    { value: "", label: "All Versions" },
    { value: "1", label: "Version 1" },
    { value: "2", label: "Version 2" },
    { value: "3", label: "Version 3" },
    { value: "4+", label: "Version 4+" }
  ];

  // Handle program filter change
  const handleProgramChange = (program: string) => {
    setSelectedProgram(program);
    if (onProgramFilter) {
      onProgramFilter(program);
    }
  };

  // Handle version filter change
  const handleVersionChange = (version: string) => {
    setSelectedVersion(version);
    if (onVersionFilter) {
      onVersionFilter(version);
    }
  };

  // Handle sort order change
  const handleSortChange = (order: "asc" | "desc") => {
    setSortOrder(order);
    if (onSortChange) {
      onSortChange(order);
    }
  };

  // Clear all filters
  const handleClearFilters = () => {
    setSelectedProgram("");
    setSelectedVersion("");
    setSortOrder("desc");
    onSearchChange("");
    if (onProgramFilter) onProgramFilter("");
    if (onVersionFilter) onVersionFilter("");
    if (onSortChange) onSortChange("desc");
    if (onClearFilters) onClearFilters();
  };

  // Check if any filter is applied
  const isAnyFilterApplied = () => {
    return (
      selectedProgram !== "" ||
      selectedVersion !== "" ||
      sortOrder !== "desc" ||
      searchValue !== ""
    );
  };

  return (
    <div className="mb-6 w-full rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
      <div className="flex items-center justify-between gap-2">
        {/* Search input */}
        <div className="w-1/3 min-w-[200px]">
          <div className="relative">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <svg
                className="h-5 w-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                ></path>
              </svg>
            </div>
            <input
              type="text"
              placeholder="Search reports..."
              className="w-full rounded-md border border-gray-300 py-2 pl-10 pr-4 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              value={searchValue}
              onChange={e => onSearchChange(e.target.value)}
            />
          </div>
        </div>

        {/* Program filter */}
        {programOptions.length > 0 && (
          <div className="min-w-[180px]">
            <select
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              value={selectedProgram}
              onChange={e => handleProgramChange(e.target.value)}
            >
              <option value="">All Programs</option>
              {programOptions.map((program, index) => (
                <option key={index} value={program}>
                  {program}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Version filter */}
        <div className="min-w-[150px]">
          <select
            className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            value={selectedVersion}
            onChange={e => handleVersionChange(e.target.value)}
          >
            {versionOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Sort order toggle */}
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600">Sort:</span>
          <div className="flex rounded-md border border-gray-300">
            <button
              type="button"
              onClick={() => handleSortChange("desc")}
              className={`px-3 py-2 text-sm ${
                sortOrder === "desc"
                  ? "bg-blue-50 font-medium text-blue-600"
                  : "bg-white text-gray-700"
              }`}
            >
              Newest
            </button>
            <button
              type="button"
              onClick={() => handleSortChange("asc")}
              className={`px-3 py-2 text-sm ${
                sortOrder === "asc"
                  ? "bg-blue-50 font-medium text-blue-600"
                  : "bg-white text-gray-700"
              }`}
            >
              Oldest
            </button>
          </div>
        </div>

        {/* Clear filters button - only show when filters are applied */}
        {isAnyFilterApplied() && (
          <button
            type="button"
            onClick={handleClearFilters}
            className="rounded-md bg-red-50 px-3 py-2 text-sm font-medium text-red-600 hover:bg-red-100"
          >
            Clear Filters
          </button>
        )}
      </div>

      {/* Active filters display */}
      {isAnyFilterApplied() && (
        <div className="mt-3 flex flex-wrap items-center gap-2">
          <span className="text-xs font-medium text-gray-500">
            Active filters:
          </span>

          {searchValue && (
            <span className="rounded-full bg-blue-100 px-3 py-1 text-xs text-blue-800">
              Search: {searchValue}
            </span>
          )}

          {selectedProgram && (
            <span className="rounded-full bg-blue-100 px-3 py-1 text-xs text-blue-800">
              Program: {selectedProgram}
            </span>
          )}

          {selectedVersion && (
            <span className="rounded-full bg-blue-100 px-3 py-1 text-xs text-blue-800">
              Version: {selectedVersion}
            </span>
          )}

          {sortOrder !== "desc" && (
            <span className="rounded-full bg-blue-100 px-3 py-1 text-xs text-blue-800">
              Sort: Oldest First
            </span>
          )}
        </div>
      )}
    </div>
  );
}
