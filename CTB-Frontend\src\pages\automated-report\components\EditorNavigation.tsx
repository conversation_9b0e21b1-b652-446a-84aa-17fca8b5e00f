import React, { useRef, useEffect, useState } from 'react';

interface EditorNavigationProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

const sections = [
  { id: 'branding', label: 'Branding', icon: '🏷️' },
  { id: 'cover', label: 'Cover Page', icon: '📄' },
  { id: 'disclaimer', label: 'Disclaimer', icon: '🔍' },
  { id: 'document_reference', label: 'Document Reference', icon: '📋' },
  { id: 'executive_summary', label: 'Executive Summary', icon: '📝' },
  { id: 'recommendations', label: 'Recommendations', icon: '💡' },
  { id: 'scope', label: 'Scope', icon: '🎯' },
  { id: 'project_objectives', label: 'Project Objectives', icon: '🎯' },
  { id: 'findings_summary', label: 'Findings Summary', icon: '📊' },
  { id: 'vulnerability_ratings', label: 'Vulnerability Ratings', icon: '🔍' },
  { id: 'key_findings', label: 'Key Findings', icon: '🔑' },
  { id: 'critical_findings', label: 'Critical Findings', icon: '⚠️' },
  { id: 'high_findings', label: 'High Findings', icon: '🔴' },
  { id: 'medium_findings', label: 'Medium Findings', icon: '🟡' },
  { id: 'low_findings', label: 'Low Findings', icon: '🟢' },
  { id: 'methodology_selector', label: 'Methodology Selection', icon: '🧩' },
];

const EditorNavigation: React.FC<EditorNavigationProps> = ({ activeSection, onSectionChange }) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const activeButtonRef = useRef<HTMLButtonElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);

  // Auto-scroll to active section
  useEffect(() => {
    if (activeButtonRef.current && scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const button = activeButtonRef.current;
      const containerWidth = container.offsetWidth;
      const buttonLeft = button.offsetLeft;
      const buttonWidth = button.offsetWidth;
      const scrollLeft = buttonLeft - (containerWidth / 2) + (buttonWidth / 2);
      container.scrollTo({
        left: scrollLeft,
        behavior: 'smooth'
      });
    }
  }, [activeSection]);

  // Update scroll arrow state
  const updateScrollButtons = () => {
    const container = scrollContainerRef.current;
    if (!container) return;
    setCanScrollLeft(container.scrollLeft > 0);
    setCanScrollRight(container.scrollLeft + container.offsetWidth < container.scrollWidth - 1);
  };

  useEffect(() => {
    updateScrollButtons();
    const container = scrollContainerRef.current;
    if (!container) return;
    container.addEventListener('scroll', updateScrollButtons);
    window.addEventListener('resize', updateScrollButtons);
    return () => {
      container.removeEventListener('scroll', updateScrollButtons);
      window.removeEventListener('resize', updateScrollButtons);
    };
  }, []);

  // Handle wheel events for horizontal scrolling
  const handleWheel = (e: React.WheelEvent) => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollLeft += e.deltaY;
    }
  };

  // Arrow scroll handlers
  const scrollBy = (amount: number) => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: amount, behavior: 'smooth' });
    }
  };

  return (
    <div className="sticky top-0 z-10 w-full px-1 py-1 bg-blue-800 border-b border-blue-900 flex items-center">
      {/* Left Arrow */}
      <button
        aria-label="Scroll left"
        onClick={() => scrollBy(-120)}
        disabled={!canScrollLeft}
        className={`mr-1 p-1 rounded-full bg-blue-900 text-white transition-opacity duration-150 ${canScrollLeft ? 'opacity-100 hover:bg-blue-700' : 'opacity-40 cursor-not-allowed'}`}
        style={{ minWidth: 32 }}
      >
        {'\u25C0'}
      </button>
      {/* Scrollable nav */}
      <div 
        ref={scrollContainerRef}
        onWheel={handleWheel}
        className="relative max-w-full overflow-x-auto scrollbar-hide transition-all duration-300 flex-1"
        style={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
        }}
      >
        <style>{`.scrollbar-hide::-webkit-scrollbar{display:none}`}</style>
        <div className="flex space-x-1 p-0.5 min-w-max">
          {sections.map((section) => (
            <button
              key={section.id}
              ref={section.id === activeSection ? activeButtonRef : null}
              onClick={() => onSectionChange(section.id)}
              className={`group flex items-center px-2.5 py-1.5 rounded-md transition-all duration-150 whitespace-nowrap text-xs font-semibold border border-transparent focus:outline-none focus:ring-2 focus:ring-blue-300 ${
                activeSection === section.id
                  ? 'bg-blue-600 text-white border-blue-400 shadow-sm'
                  : 'bg-blue-900/60 text-blue-100 hover:bg-blue-700/80 hover:text-white border-blue-800'
              }`}
              style={{boxShadow: activeSection === section.id ? '0 2px 8px 0 rgba(59,130,246,0.10)' : undefined}}
            >
              <span className={`mr-1 text-base transition-transform duration-150 ${
                activeSection === section.id ? 'scale-105' : 'group-hover:scale-105'
              }`}>
                {section.icon}
              </span>
              <span className={`font-semibold ${
                activeSection === section.id ? 'text-white' : 'text-blue-100'
              }`}>
                {section.label}
              </span>
              {activeSection === section.id && (
                <span className="ml-1 w-1.5 h-1.5 bg-white/80 rounded-full animate-pulse border border-blue-300" />
              )}
            </button>
          ))}
        </div>
      </div>
      {/* Right Arrow */}
      <button
        aria-label="Scroll right"
        onClick={() => scrollBy(120)}
        disabled={!canScrollRight}
        className={`ml-1 p-1 rounded-full bg-blue-900 text-white transition-opacity duration-150 ${canScrollRight ? 'opacity-100 hover:bg-blue-700' : 'opacity-40 cursor-not-allowed'}`}
        style={{ minWidth: 32 }}
      >
        {'\u25B6'}
      </button>
    </div>
  );
};

export default EditorNavigation; 