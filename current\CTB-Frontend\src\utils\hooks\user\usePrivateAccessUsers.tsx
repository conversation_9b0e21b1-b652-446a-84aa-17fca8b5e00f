import { useCallback, useEffect, useMemo, useState } from "react";
import useProgram from "../programs/useProgram";
import toast from "react-hot-toast";
import { User, getUsers } from "../../api/endpoints/usersApi";

/**
 * A hook to provide access to managing private access users
 * on a particular program
 */
const usePrivateAccessUsers = (programId?: number) => {
  const [privateAccessUsers, setUsers] = useState<User[]>([]);
  const [nextPage, setNextPage] = useState(0);
  const [count, setCount] = useState(1);
  const [isLoading, setIsLoading] = useState(false);

  const { program } = useProgram(programId);
  const userIds = useMemo(() => program?.privateAccessUsers || [], [program]);
  const moreUsers = useMemo(
    () =>
      privateAccessUsers.length < count &&
      program?.privateAccessUsers &&
      program.privateAccessUsers.length > 0,
    [count, privateAccessUsers.length, program]
  );

  /**
   * Load the next chunk of users and append to the array of users
   */
  const loadNextPage = useCallback(() => {
    if (moreUsers) {
      setIsLoading(true);

      getUsers({
        page: nextPage,
        limit: 10,
        ids: userIds
      })
        .then(data => {
          setUsers(users => [...users, ...data.users]);
          setCount(data.count);
          setNextPage(page => page + 1);
        })
        .catch(() => toast.error("Failed to load users"))
        .finally(() => setIsLoading(false));
    }
  }, [moreUsers, nextPage, userIds]);

  /**
   * Reload the private access users when the list of users changes in the program
   */
  useEffect(() => {
    setUsers([]);
    setNextPage(0);
  }, [program?.privateAccessUsers]);

  /**
   * Preload the first page of users
   */
  useEffect(() => {
    if (moreUsers && privateAccessUsers.length === 0) {
      loadNextPage();
    }
  }, [loadNextPage, moreUsers, privateAccessUsers.length]);

  return {
    privateAccessUsers,
    isLoading,
    moreUsers,
    loadNextPage
  };
};

export default usePrivateAccessUsers;
