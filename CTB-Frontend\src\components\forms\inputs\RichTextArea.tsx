import React, { useMemo, useCallback } from "react";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "jodit-react";
import { Controller, useFormContext } from "react-hook-form";
import { InputBaseParams } from "../Form";
import { Jodit } from "jodit";
import "jodit/es2021/jodit.min.css";

// Unique class prefix to prevent global style leaks
const EDITOR_CLASS_PREFIX = "jodit-custom-editor";

const RichTextArea = ({
  name,
  rules,
  placeholder,
  readOnly = false
}: InputBaseParams & {
  placeholder?: string;
  readOnly?: boolean;
}) => {
  const { control } = useFormContext();

  // Styles for editor display (these will be converted to inline styles when saving)
  const editorStyles = useMemo(
    () => `
      .${EDITOR_CLASS_PREFIX} h1 { font-size: 2em; font-weight: bold; margin: 0.67em 0; }
      .${EDITOR_CLASS_PREFIX} h2 { font-size: 1.5em; font-weight: bold; margin: 0.75em 0; }
      .${EDITOR_CLASS_PREFIX} h3 { font-size: 1.17em; font-weight: bold; margin: 0.83em 0; }
      .${EDITOR_CLASS_PREFIX} h4 { font-size: 1em; font-weight: bold; margin: 1.12em 0; }
      .${EDITOR_CLASS_PREFIX} ul {list-style-type: disc; margin: 0; padding: 0 12px 8px;}
      .${EDITOR_CLASS_PREFIX} ul ul {list-style-type: circle; padding-left: 16px;}
      .${EDITOR_CLASS_PREFIX} ul ul ul {list-style-type: square; padding-left: 16px;}
      .${EDITOR_CLASS_PREFIX} ol {list-style-type: decimal; padding-left: 20px; margin-top: 1em; margin-bottom: 1em;}
      .${EDITOR_CLASS_PREFIX} ol ol {margin-top: 0; margin-bottom: 0; padding-left: 16px;}
      .${EDITOR_CLASS_PREFIX} td {border: solid 1px;}
      .${EDITOR_CLASS_PREFIX} table {border-collapse: collapse; width: 100%;}
      .${EDITOR_CLASS_PREFIX} th {border: solid 1px; padding: 8px; background-color: #f2f2f2;}
      .${EDITOR_CLASS_PREFIX} tr {border-bottom: solid 1px #ddd;}
    
      /* Enhanced styling for code blocks */
      .${EDITOR_CLASS_PREFIX} pre {
        display: inline-block;
        min-width: 200px;
        max-width: 100%;
        width: auto;
        background: linear-gradient(145deg, #1e293b, #0f172a);
        color: #e2e8f0;
        font-family: 'Fira Code', 'Consolas', monospace;
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
        overflow-x: auto;
        position: relative;
        border-left: 4px solid #3b82f6;
      }
    
      /* Code block header with language indicator */
      .${EDITOR_CLASS_PREFIX} pre::before {
        content: "code";
        position: absolute;
        top: 0;
        right: 10px;
        background-color: #3b82f6;
        color: white;
        padding: 2px 8px;
        font-size: 12px;
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
        font-family: sans-serif;
        opacity: 0.8;
      }
    
      /* Line numbers effect */
      .${EDITOR_CLASS_PREFIX} pre {
        counter-reset: line;
        padding-left: 3.8em;
        line-height: 1.6;
      }
    
      .${EDITOR_CLASS_PREFIX} pre code {
        counter-increment: line;
        position: relative;
        display: block;
        background-color: transparent;
        padding: 0;
        border-radius: 0;
        font-size: 0.9em;
      }
    
      .${EDITOR_CLASS_PREFIX} pre code::before {
        content: counter(line);
        position: absolute;
        left: -3em;
        width: 2.5em;
        text-align: right;
        color: #64748b;
        padding-right: 0.5em;
        border-right: 1px solid #475569;
        user-select: none;
      }
    
      /* Inline code styling */
      .${EDITOR_CLASS_PREFIX} code:not(pre code) {
        font-family: 'Fira Code', 'Consolas', monospace;
        background: rgba(59, 130, 246, 0.1);
        color: #3b82f6;
        padding: 0.2em 0.4em;
        border-radius: 3px;
        font-size: 0.9em;
        border: 1px solid rgba(59, 130, 246, 0.2);
      }
    
      .${EDITOR_CLASS_PREFIX} blockquote {border-left: 5px solid #eee; padding: 10px 20px; margin: 0 0 20px;}
    `,
    []
  );

  const config = useMemo(
    () => ({
      readonly: readOnly,
      toolbar: true,
      spellcheck: true,
      language: "en",
      theme: "default",
      toolbarButtonSize: "small" as const,
      toolbarSticky: true,
      toolbarAdaptive: true,
      showCharsCounter: false,
      showWordsCounter: false,
      showXPathInStatusbar: false,
      enterMode: "p",
      defaultMode: 1,
      indentMargin: 10,
      allowTabNavigation: true,
      tabNavigation: true,
      askBeforePasteHTML: true,
      askBeforePasteFromWord: true,
      buttons: [
        "bold",
        "italic",
        "underline",
        "strikethrough",
        "|",
        "outdent",
        "indent",
        "|",
        "font",
        "fontsize",
        "brush",
        "formatblock",
        "|",
        "image",
        "table",
        "link",
        "|",
        "align",
        "undo",
        "redo",
        "|",
        "find",
        "source",
        "fullsize",
        "preview"
      ],
      uploader: {
        insertImageAsBase64URI: true,
        imagesExtensions: ["jpg", "png", "jpeg", "gif", "webp"],
        processImageBeforeUpload: (img: File) => img
      },
      cleanHTML: {
        allowTags: {
          p: true,
          br: true,
          strong: true,
          b: true,
          em: true,
          i: true,
          u: true,
          strike: true,
          s: true,
          ul: true,
          ol: true,
          li: true,
          a: true,
          img: true,
          table: true,
          tbody: true,
          tr: true,
          td: true,
          th: true,
          h1: true,
          h2: true,
          h3: true,
          h4: true,
          div: true,
          span: true,
          pre: true,
          code: true,
          blockquote: true
        },
        denyTags: {
          script: true,
          iframe: true,
          form: true,
          input: true,
          style: true
        },
        fullyQualifiedLinks: true,
        removeEmptyAttributes: true,
        safeJavaScriptLink: true
      },
      // Updated events to scope styles properly and enhance content formatting
      events: {
        afterInit: (editor: Jodit) => {
          // Add the class to the editor container
          editor.editor.classList.add(EDITOR_CLASS_PREFIX);

          // Create style element but only scope it to the editor
          const styleTag = editor.createInside.element("style");
          styleTag.innerHTML = editorStyles;
          editor.container.appendChild(styleTag);

          // Set placeholder if provided
          if (placeholder) {
            editor.editor.setAttribute("data-placeholder", placeholder);
          }

          // Add custom keyboard shortcut for code block
          editor.events.on("keydown", (event: KeyboardEvent) => {
            // Ctrl+Shift+C or Cmd+Shift+C for code block
            if (
              (event.ctrlKey || event.metaKey) &&
              event.shiftKey &&
              event.key === "c"
            ) {
              event.preventDefault();
              const codeBlock = `<pre><code>// Your code here</code></pre>`;
              editor.selection.insertHTML(codeBlock);
              return false;
            }
            return true;
          });
        },
        afterPreview: (editor: Jodit) => {
          setTimeout(() => {
            // Find the preview dialog
            const dialog = document.querySelector(".jodit-dialog__content");
            if (dialog) {
              // Add our class to the preview content
              const previewContent = dialog.querySelector(
                ".jodit-preview-content"
              );
              if (previewContent) {
                previewContent.classList.add(EDITOR_CLASS_PREFIX);

                // Format code blocks in preview
                formatCodeBlocks(previewContent as HTMLElement);
              }

              // Add scoped styles only if not already added
              if (
                !dialog.querySelector(
                  `style[data-${EDITOR_CLASS_PREFIX}-style]`
                )
              ) {
                const style = document.createElement("style");
                style.setAttribute(`data-${EDITOR_CLASS_PREFIX}-style`, "true");
                style.innerHTML = editorStyles;
                dialog.appendChild(style);
              }
            }
          }, 0);
        },
        // Format content when inserted
        change: (newValue: string, oldValue: string) => {
          if (newValue && newValue !== oldValue) {
            // This is a light formatting to handle changes during editing
            // The full formatting will happen on blur
          }
        }
      },
      height: 350,
      minHeight: 200,
      maxHeight: 800,
      width: "auto",
      // Add class to wrapper for better CSS scoping
      className: EDITOR_CLASS_PREFIX + "-wrapper"
    }),
    [readOnly, placeholder, editorStyles]
  );

  // Helper function to format code blocks
  const formatCodeBlocks = (container: HTMLElement) => {
    container.querySelectorAll("pre").forEach(pre => {
      // Set display to inline-block for auto-width
      pre.style.display = "inline-block";
      pre.style.minWidth = "200px";
      pre.style.maxWidth = "100%";
      pre.style.width = "auto";
      pre.style.background = "linear-gradient(145deg, #1e293b, #0f172a)";
      pre.style.color = "#e2e8f0";
      pre.style.fontFamily = "'Fira Code', 'Consolas', monospace";
      pre.style.padding = "1rem";
      pre.style.margin = "1rem 0";
      pre.style.borderRadius = "8px";
      pre.style.boxShadow =
        "0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08)";
      pre.style.overflowX = "auto";
      pre.style.position = "relative";
      pre.style.borderLeft = "4px solid #3b82f6";
      pre.style.counterReset = "line";
      pre.style.paddingLeft = "3.8em";
      pre.style.lineHeight = "1.6";

      // Process each code line
      const code = pre.querySelector("code");
      if (code) {
        code.style.backgroundColor = "transparent";
        code.style.padding = "0";
        code.style.borderRadius = "0";
        code.style.fontSize = "0.9em";

        // Split code into lines and recreate with line numbers
        if (!code.hasAttribute("data-processed")) {
          const content = code.innerHTML;
          const lines = content.split("\n");
          let newContent = "";

          lines.forEach(line => {
            newContent += `<span class="code-line">${line}</span>\n`;
          });

          code.innerHTML = newContent;
          code.setAttribute("data-processed", "true");
        }
      }
    });

    // Style inline code elements (not inside pre)
    container.querySelectorAll("code:not(pre code)").forEach(code => {
      const codeElement = code as HTMLElement;
      codeElement.style.fontFamily = "'Fira Code', 'Consolas', monospace";
      codeElement.style.background = "rgba(59, 130, 246, 0.1)";
      codeElement.style.color = "#3b82f6";
      codeElement.style.padding = "0.2em 0.4em";
      codeElement.style.borderRadius = "3px";
      codeElement.style.fontSize = "0.9em";
      codeElement.style.border = "1px solid rgba(59, 130, 246, 0.2)";
    });
  };

  const processContent = useCallback((content: string) => {
    if (!content || content === "<p><br></p>" || content.trim() === "")
      return "";

    let processed = content.replace(/\t/g, "  ");

    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = processed;

    // Apply inline styles to ensure formatting is preserved when rendered elsewhere
    // Headings
    tempDiv.querySelectorAll("h1").forEach(h1 => {
      h1.style.fontSize = "2em";
      h1.style.fontWeight = "bold";
      h1.style.margin = "0.67em 0";
    });

    tempDiv.querySelectorAll("h2").forEach(h2 => {
      h2.style.fontSize = "1.5em";
      h2.style.fontWeight = "bold";
      h2.style.margin = "0.75em 0";
    });

    tempDiv.querySelectorAll("h3").forEach(h3 => {
      h3.style.fontSize = "1.17em";
      h3.style.fontWeight = "bold";
      h3.style.margin = "0.83em 0";
    });

    tempDiv.querySelectorAll("h4").forEach(h4 => {
      h4.style.fontSize = "1em";
      h4.style.fontWeight = "bold";
      h4.style.margin = "1.12em 0";
    });

    // Lists - Use minimal padding for better PDF rendering
    tempDiv.querySelectorAll("ul").forEach(ul => {
      ul.style.listStyleType = "disc";
      ul.style.margin = "0";
      ul.style.padding = "0 12px 8px"; // Reduced from 20px to 12px
    });

    tempDiv.querySelectorAll("ul ul").forEach(ulul => {
      (ulul as HTMLElement).style.listStyleType = "circle";
      (ulul as HTMLElement).style.paddingLeft = "16px"; // Nested lists get less padding
    });

    tempDiv.querySelectorAll("ul ul ul").forEach(ululul => {
      (ululul as HTMLElement).style.listStyleType = "square";
      (ululul as HTMLElement).style.paddingLeft = "16px";
    });

    tempDiv.querySelectorAll("ol").forEach(ol => {
      ol.style.listStyleType = "decimal";
      ol.style.paddingLeft = "20px"; // Reduced from 40px to 20px
      ol.style.marginTop = "1em";
      ol.style.marginBottom = "1em";
    });

    tempDiv.querySelectorAll("ol ol").forEach(olol => {
      (olol as HTMLElement).style.marginTop = "0";
      (olol as HTMLElement).style.marginBottom = "0";
      (olol as HTMLElement).style.paddingLeft = "16px"; // Nested ordered lists
    });

    // Tables
    tempDiv.querySelectorAll("td").forEach(td => {
      td.style.border = "solid 1px";
    });

    // Additional elements that may need inline styling
    tempDiv.querySelectorAll("table").forEach(table => {
      table.style.borderCollapse = "collapse";
      table.style.width = "100%";
    });

    tempDiv.querySelectorAll("th").forEach(th => {
      th.style.border = "solid 1px";
      th.style.padding = "8px";
      th.style.backgroundColor = "#f2f2f2";
    });

    tempDiv.querySelectorAll("tr").forEach(tr => {
      tr.style.borderBottom = "solid 1px #ddd";
    });

    // Format code blocks with our enhanced styling
    tempDiv.querySelectorAll("pre").forEach(pre => {
      pre.style.display = "inline-block";
      pre.style.minWidth = "200px";
      pre.style.maxWidth = "100%";
      pre.style.width = "auto";
      pre.style.background = "linear-gradient(145deg, #1e293b, #0f172a)";
      pre.style.color = "#e2e8f0";
      pre.style.fontFamily = "'Fira Code', 'Consolas', monospace";
      pre.style.padding = "1rem";
      pre.style.margin = "1rem 0";
      pre.style.borderRadius = "8px";
      pre.style.boxShadow =
        "0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08)";
      pre.style.overflowX = "auto";
      pre.style.position = "relative";
      pre.style.borderLeft = "4px solid #3b82f6";

      // Try to detect language from content to add language tag
      const code = pre.querySelector("code");
      if (code) {
        const codeContent = code.textContent || "";
        let language = "code";

        // Basic language detection
        if (
          codeContent.includes("function") ||
          codeContent.includes("const ") ||
          codeContent.includes("let ")
        ) {
          language = "javascript";
        } else if (
          codeContent.includes("def ") ||
          (codeContent.includes("import ") && codeContent.includes(":"))
        ) {
          language = "python";
        } else if (
          codeContent.includes("class ") &&
          codeContent.includes("{")
        ) {
          language = "java";
        } else if (codeContent.includes("#include")) {
          language = "c++";
        }

        // Add pseudo-element with content via data attribute
        pre.setAttribute("data-language", language);

        // Apply inline code styling
        code.style.backgroundColor = "transparent";
        code.style.padding = "0";
        code.style.borderRadius = "0";
      }
    });

    tempDiv.querySelectorAll("code:not(pre code)").forEach(code => {
      const codeElement = code as HTMLElement;
      codeElement.style.fontFamily = "'Fira Code', 'Consolas', monospace";
      codeElement.style.background = "rgba(59, 130, 246, 0.1)";
      codeElement.style.color = "#3b82f6";
      codeElement.style.padding = "0.2em 0.4em";
      codeElement.style.borderRadius = "3px";
      codeElement.style.fontSize = "0.9em";
      codeElement.style.border = "1px solid rgba(59, 130, 246, 0.2)";
    });

    tempDiv.querySelectorAll("blockquote").forEach(blockquote => {
      blockquote.style.borderLeft = "5px solid #eee";
      blockquote.style.padding = "10px 20px";
      blockquote.style.margin = "0 0 20px";
    });

    return tempDiv.innerHTML;
  }, []);

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({
        field: { value, onChange, onBlur },
        fieldState: { error }
      }) => (
        <div className={`jodit-wrapper ${EDITOR_CLASS_PREFIX}-container`}>
          {/* Scoped styles to this component only */}
          <style>{`
              .${EDITOR_CLASS_PREFIX}-container .jodit-container {
                border-color: ${error ? "#ff3860" : "#dbdbdb"};
              }
              .${EDITOR_CLASS_PREFIX}-container .jodit-error-message {
                color: #ff3860;
                font-size: 0.85rem;
                margin-top: 4px;
              }
            
              /* Code block styling in the editor */
              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg pre {
                display: inline-block;
                min-width: 200px;
                max-width: 100%;
                width: auto;
                background: linear-gradient(145deg, #1e293b, #0f172a);
                color: #e2e8f0;
                font-family: 'Fira Code', 'Consolas', monospace;
                padding: 1rem;
                margin: 1rem 0;
                border-radius: 8px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
                overflow-x: auto;
                position: relative;
                border-left: 4px solid #3b82f6;
              }
            
              /* Language indicator */
              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg pre[data-language]::before {
                content: attr(data-language);
                position: absolute;
                top: 0;
                right: 10px;
                background-color: #3b82f6;
                color: white;
                padding: 2px 8px;
                font-size: 12px;
                border-bottom-left-radius: 4px;
                border-bottom-right-radius: 4px;
                font-family: sans-serif;
                opacity: 0.8;
              }
            
              /* Line numbers effect */
              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg pre {
                counter-reset: line;
                padding-left: 3.8em;
                line-height: 1.6;
              }
            
              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg pre code {
                counter-increment: line;
              }
            
              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg pre code .code-line {
                position: relative;
                display: block;
              }
            
              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg pre code .code-line::before {
                content: counter(line);
                position: absolute;
                left: -2.8em;
                width: 2em;
                text-align: right;
                color: #64748b;
                user-select: none;
              }
            
              /* Inline code styling */
              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg code:not(pre code) {
                font-family: 'Fira Code', 'Consolas', monospace;
                background: rgba(59, 130, 246, 0.1);
                color: #3b82f6;
                padding: 0.2em 0.4em;
                border-radius: 3px;
                font-size: 0.9em;
                border: 1px solid rgba(59, 130, 246, 0.2);
              }
            
              /* Custom code block button styling */
              .jodit-toolbar-button_insertCodeBlock button {
                position: relative;
              }
            
              /* Font preloading (optional) */
              @font-face {
                font-family: 'Fira Code';
                src: url('https://cdnjs.cloudflare.com/ajax/libs/firacode/6.2.0/woff2/FiraCode-Regular.woff2') format('woff2');
                font-style: normal;
                font-weight: 400;
                font-display: swap;
              }
            `}</style>

          <JoditEditor
            value={value || ""}
            //@ts-ignore
            config={config}
            onChange={content => {
              onChange(content);
            }}
            onBlur={() => {
              if (value) {
                const cleaned = processContent(value);
                onChange(cleaned);
              }
              onBlur();
            }}
            className={error ? "jodit-container-error" : ""}
            ref={editor => {
              if (editor?.container) {
                const textarea = editor.container.querySelector("textarea");
                if (textarea) {
                  textarea.setAttribute("data-prevent-scroll", "true");
                }
              }
            }}
          />
          {error && <p className="jodit-error-message">{error.message}</p>}
        </div>
      )}
    />
  );
};

export default RichTextArea;
