// components/InputField.jsx
import React from "react";

interface InputFieldProps {
  label: string;
  required?: boolean;
  placeholder?: string;
  value: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const InputField: React.FC<InputFieldProps> = ({
  label,
  required,
  placeholder,
  value,
  onChange
}) => (
  <div className="mb-6">
    <label className="mb-1 block font-semibold text-gray-700">
      {label} {required && <span className="text-red-500">*</span>}
    </label>
    <input
      type="text"
      placeholder={placeholder}
      required={required}
      value={value}
      onChange={onChange}
      className="w-full rounded border border-gray-300 px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
    />
  </div>
);

export default InputField;
