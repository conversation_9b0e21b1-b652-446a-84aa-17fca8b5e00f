import { useState, useEffect } from "react";
import { getBusinessDashboardDetails } from "../../api/endpoints/dashboard/businessDashboard";
import {
  parseBusinessDashboard,
  ParsedBusinessDashboard
} from "../../api/endpoints/dashboard/businessDashboardParser";

const useBusinessDashboardDetails = () => {
  const [details, setDetails] = useState<ParsedBusinessDashboard | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDetails = async () => {
      try {
        const response = await getBusinessDashboardDetails();
        const parsedDetails = parseBusinessDashboard(response);
        setDetails(parsedDetails);
      } catch (err) {
        if (err instanceof Error) {
          setError(
            err.message ||
              "An error occurred while fetching business dashboard details."
          );
        } else {
          setError(
            "An error occurred while fetching business dashboard details."
          );
        }
      } finally {
        setLoading(false);
      }
    };

    fetchDetails();
  }, []);

  return { details, loading, error };
};

export default useBusinessDashboardDetails;
