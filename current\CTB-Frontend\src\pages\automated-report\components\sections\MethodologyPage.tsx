import React from 'react';
import { Page, View, Text, StyleSheet } from '@react-pdf/renderer';

const accentColor = '#2563eb';
const cardBg = '#f8fafc';
const dividerColor = '#e5e7eb';

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: '20mm 15mm',
    fontFamily: 'Helvetica',
    fontSize: 12,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: accentColor,
    letterSpacing: 1.2,
    marginBottom: 6,
    textAlign: 'left',
  },
  divider: {
    height: 2,
    backgroundColor: dividerColor,
    marginBottom: 18,
    opacity: 0.7,
    borderRadius: 1,
  },
  intro: {
    fontSize: 13,
    color: '#374151',
    marginBottom: 28,
    lineHeight: 1.6,
    textAlign: 'justify',
    paddingHorizontal: 8,
  },
  phaseBlock: {
    backgroundColor: cardBg,
    borderRadius: 12,
    padding: 18,
    marginBottom: 28,
    borderWidth: 1,
    borderColor: dividerColor,
  },
  phaseName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: accentColor,
    marginBottom: 8,
    letterSpacing: 0.5,
  },
  phaseDesc: {
    fontSize: 13,
    color: '#374151',
    marginBottom: 10,
    lineHeight: 1.5,
    textAlign: 'justify',
  },
  phaseTechniques: {
    fontSize: 13,
    color: '#374151',
    fontWeight: 'bold',
    marginBottom: 4,
    marginTop: 2,
  },
  bulletList: {
    marginLeft: 12,
    marginBottom: 0,
    marginTop: 0,
  },
  bulletItem: {
    fontSize: 12,
    color: '#374151',
    marginBottom: 4,
    lineHeight: 1.5,
    paddingLeft: 4,
  },
  phaseSummary: {
    fontSize: 12,
    color: '#374151',
    marginTop: 10,
    textAlign: 'justify',
    fontStyle: 'italic',
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    fontSize: 10,
    color: 'grey',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  footerLeft: {
    textAlign: 'left',
    flex: 1,
  },
  footerRight: {
    textAlign: 'right',
    flex: 1,
  },
});

const phases = [
  {
    name: 'Pre-Engagement Analysis',
    description: `This foundational phase involves gathering critical background information and aligning with the client to ensure the engagement is well-defined and expectations are clear. We focus on understanding the application’s purpose, architecture, and business context to tailor our testing approach effectively.`,
    techniques: [
      `Review the application’s documentation, such as architecture diagrams, API specifications, and user guides, to understand its functionality and data flows.`,
      `Conduct stakeholder interviews to identify critical assets (e.g., payment systems, user data) and business logic that requires special attention.`,
      `Analyze the application’s deployment environment (e.g., cloud-based, on-premises) to identify potential external dependencies like CDNs or third-party APIs.`,
      `Assess previous security reports or audits (if provided) to identify recurring issues or known vulnerabilities.`,
      `Establish success criteria with the client, such as identifying a specific number of critical vulnerabilities or achieving a particular level of access (e.g., admin privileges).`,
    ],
    summary: `This phase ensures a strategic approach, aligning testing with the client’s priorities and business risks.`,
  },
  {
    name: 'Planning and Scoping',
    description: `We define the scope, objectives, and rules of engagement to ensure testing aligns with client requirements and avoids unintended disruptions. This phase sets the foundation for a controlled and compliant testing process.`,
    techniques: [
      `Identify target URLs, API endpoints (e.g., /api/v1/), and IP ranges to be tested, ensuring all in-scope assets are documented.`,
      `Define testing constraints, such as avoiding DoS attacks, excluding production databases, or limiting testing hours to off-peak times.`,
      `Establish communication protocols with the client, including points of contact for escalations and regular status updates.`,
      `Review legal agreements, such as NDAs, authorization letters, or terms of service, to ensure compliance with regulatory requirements (e.g., GDPR for EU clients).`,
      `Set up a testing environment with tools like Burp Suite, Postman, sqlmap, and OWASP ZAP, ensuring they are configured for the target’s technology stack.`,
    ],
    summary: `This phase ensures a clear scope and prepares the team for effective, compliant testing.`,
  },
  {
    name: 'Reconnaissance',
    description: `We gather intelligence to map the attack surface of the web application and API, focusing on identifying all accessible components, technologies, and potential vulnerabilities. This phase combines passive and active techniques to build a comprehensive profile of the target.`,
    techniques: [
      `Enumerate subdomains using tools like Amass, Sublist3r, and DNSdumpster to uncover hidden or forgotten assets, such as staging environments (e.g., staging.example.com) or development servers (e.g., dev.example.com).`,
      `Discover API endpoints by crawling the application with Burp Suite’s Spider and analyzing OpenAPI/Swagger documentation, JavaScript files, or network traffic for undocumented APIs.`,
      `Fingerprint technologies with WhatWeb and Wappalyzer to identify frameworks (e.g., Django, Laravel), servers (e.g., Apache, Nginx), and libraries, cross-referencing versions with CVE databases for known vulnerabilities.`,
      `Check for exposed metadata in HTML comments, robots.txt, sitemaps, or error pages, looking for sensitive information like developer notes, API keys, or internal paths.`,
      `Analyze JavaScript files with LinkFinder to extract hardcoded API keys, endpoints, or credentials, and review client-side code for potential logic vulnerabilities (e.g., client-side validation bypasses).`,
      `Identify misconfigured CORS policies that allow unauthorized domains to access resources, using automated tools like CORS-Scanner and manual testing with Burp Suite.`,
    ],
    summary: `This phase builds a detailed map of the attack surface, identifying entry points for targeted testing while minimizing detection risk.`,
  },
  {
    name: 'Unauthenticated Testing',
    description: `We simulate an external attacker with no credentials, targeting vulnerabilities that do not require authentication. The goal is to identify weaknesses that could allow unauthorized access, data leakage, or an initial foothold into the system, focusing on common attack vectors.`,
    techniques: [
      `Test for injection vulnerabilities, including SQL injection, XSS (reflected, stored, DOM-based), command injection, and XML injection, in forms, URL parameters, and API inputs using sqlmap for automated testing and Burp Suite’s Repeater for manual payload injection (e.g., <script>alert(1)</script> for XSS).`,
      `Identify exposed resources such as admin panels (/admin, /dashboard), API endpoints (/api/v1/users), or file directories that lack authentication, using tools like dirb and gobuster to enumerate hidden paths.`,
      `Attempt Insecure Direct Object Reference (IDOR) attacks by manipulating parameters (e.g., changing “user_id=123” to “user_id=124”) to access unauthorized data, focusing on both web and API endpoints with predictable identifiers.`,
      `Check for weak rate-limiting on login forms, password reset endpoints, or API routes, attempting brute-force attacks with Hydra or Burp Intruder to enumerate accounts or overwhelm the system.`,
      `Analyze error messages, API responses, and HTTP headers for sensitive data leakage, such as database errors revealing table names, API keys in JSON responses, or stack traces exposing internal paths.`,
      `Test for Server-Side Request Forgery (SSRF) by injecting URLs into API parameters to access internal systems (e.g., *************** for AWS metadata) or external services, potentially bypassing network restrictions.`,
      `Assess file upload functionality for vulnerabilities, such as uploading malicious files (e.g., a PHP web shell) or exploiting file inclusion (LFI/RFI) by injecting paths like ../../etc/passwd.`,
    ],
    summary: `This phase uncovers vulnerabilities exploitable by anonymous attackers, providing a foundation for deeper testing.`,
  },
  {
    name: 'Authenticated Testing',
    description: `We simulate a malicious user with valid credentials, focusing on privilege escalation, access control bypasses, and deeper system compromise. This phase tests the application’s ability to enforce proper authorization and session management across different user roles.`,
    techniques: [
      `Test for vertical privilege escalation by attempting to access admin-level functions as a standard user, such as modifying API parameters (e.g., “role=user” to “role=admin”) or accessing admin endpoints (e.g., /api/admin/users).`,
      `Exploit IDOR vulnerabilities to access another user’s data by changing identifiers in API requests (e.g., “account_id=1001” to “account_id=1002”) or web forms, focusing on horizontal privilege escalation.`,
      `Assess JSON Web Token (JWT) validation for weaknesses, such as accepting tampered tokens by changing the algorithm to “none” or exploiting weak secrets, using tools like jwt_tool to decode and manipulate tokens.`,
      `Test multiple user roles (e.g., guest, user, admin) to identify inconsistencies in access control enforcement, ensuring that lower-privileged users cannot access restricted functionality or data.`,
      `Check session management for vulnerabilities, including session fixation (forcing a known session ID), improper session expiration after logout, and weak token generation (e.g., predictable session IDs), using Burp Suite’s Sequencer to analyze token entropy.`,
      `Evaluate authentication mechanisms, such as password reset flows for weak token generation, OAuth/OpenID Connect misconfigurations (e.g., redirect URI manipulation), and MFA bypasses through session token reuse.`,
      `Test for Cross-Site Request Forgery (CSRF) by crafting malicious requests to perform actions (e.g., changing a user’s email) without proper CSRF token validation, focusing on state-changing POST requests.`,
    ],
    summary: `This phase ensures that authenticated users cannot abuse their access to compromise the system, focusing on privilege escalation and access control vulnerabilities.`,
  },
  {
    name: 'Logic and Workflow Testing',
    description: `We examine the application’s business logic, API workflows, and data handling to identify vulnerabilities that arise from improper implementation of processes or assumptions. This phase focuses on exploiting flaws that may not be immediately obvious but can lead to significant security issues.`,
    techniques: [
      `Test for race conditions in API calls, such as simultaneous fund withdrawals or account updates, using Burp Turbo Intruder to send concurrent requests and exploit timing issues (e.g., double-spending by withdrawing funds twice before balance updates).`,
      `Attempt to bypass critical workflow steps, such as skipping payment verification by altering API request sequences (e.g., jumping from /api/cart to /api/order/complete without /api/payment), or modifying client-side validation logic.`,
      `Manipulate API parameters to exploit logic flaws, such as changing “quantity=1” to “quantity=-1” to trigger refunds, or altering “price=100” to “price=0” to bypass payment, focusing on hidden or undocumented parameters.`,
      `Flood API endpoints with requests to test rate-limiting and throttling mechanisms, using tools like ffuf to send high-volume requests and monitor for DoS conditions or error responses that might reveal sensitive information.`,
      `Check for improper input validation and sanitization by injecting malformed data, such as invalid JSON payloads, oversized strings, or null values, to cause errors, crashes, or unexpected behavior (e.g., bypassing validation checks).`,
      `Assess business rule enforcement, such as testing whether discounts can be applied multiple times by replaying API requests, or if account statuses can be manipulated (e.g., “verified=false” to “verified=true”) to gain unauthorized access.`,
      `Evaluate the application’s handling of edge cases, such as negative values, zero values, or maximum integer limits, to identify vulnerabilities like integer overflows or logic bypasses in payment or inventory systems.`,
    ],
    summary: `This phase uncovers logic-based vulnerabilities that could allow attackers to manipulate the application’s functionality or bypass security controls.`,
  },
  {
    name: 'Configuration and Environment Testing',
    description: `We assess the application’s configuration and hosting environment for security weaknesses that could be exploited. This phase focuses on identifying misconfigurations in the application, server, and API setup that may expose vulnerabilities or weaken defenses.`,
    techniques: [
      `Check for weak TLS configurations using testssl.sh, identifying outdated versions (e.g., TLS 1.0), weak cipher suites (e.g., RC4), or expired certificates that could allow MITM attacks or downgrade attacks.`,
      `Review HTTP security headers with tools like Security Headers, ensuring headers like Content-Security-Policy (CSP), HTTP Strict Transport Security (HSTS), and X-Frame-Options are properly implemented to prevent attacks like XSS or clickjacking.`,
      `Identify misconfigured Cross-Origin Resource Sharing (CORS) policies that allow unauthorized domains to access resources, using CORS-Scanner and manual testing to verify allowed origins, methods, and headers.`,
      `Check for exposed debugging endpoints (e.g., /debug, /api/status) or verbose error messages that reveal sensitive information like stack traces, database details, or internal IP addresses.`,
      `Assess API rate-limiting and throttling mechanisms by sending excessive requests to determine if they can be bypassed, potentially leading to DoS or brute-force attacks, and verify if the API returns appropriate HTTP 429 responses.`,
      `Evaluate server configurations for vulnerabilities, such as directory listing enabled on Apache/Nginx, outdated server software (e.g., Apache 2.4.29 with known CVEs), or unnecessary services running (e.g., FTP on a web server).`,
      `Test for cloud-specific misconfigurations if applicable, such as exposed S3 buckets, misconfigured IAM roles in AWS, or public-facing Azure blobs, using tools like awscli or Azure PowerShell for enumeration.`,
    ],
    summary: `This phase ensures the application’s configuration and environment are secure, mitigating risks from misconfigurations that could be exploited.`,
  },
  {
    name: 'Exploitation',
    description: `We exploit identified vulnerabilities to assess their real-world impact, focusing on demonstrating the severity of issues and their potential consequences for the application and its users. This phase involves chaining vulnerabilities to maximize impact and testing the application’s resilience to attacks.`,
    techniques: [
      `Exploit SQL injection vulnerabilities to extract sensitive data (e.g., user tables, admin credentials) or execute commands, using sqlmap to automate extraction and Burp Suite for manual exploitation with payloads like ' OR 1=1 --.`,
      `Use XSS vulnerabilities to steal session cookies, perform actions on behalf of users (e.g., <script>fetch('/api/transfer?amount=1000')</script>), or deliver malicious payloads, testing both reflected and stored XSS scenarios.`,
      `Leverage misconfigured API endpoints to escalate access, such as accessing admin APIs with a standard user token (e.g., /api/admin/users with a stolen JWT), or exploiting excessive data exposure to retrieve sensitive user data.`,
      `Extract sensitive data through vulnerabilities like SSRF (e.g., accessing internal APIs at ********) or XML External Entity (XXE) injection (e.g., parsing XML to read /etc/passwd), testing the effectiveness of data protection controls.`,
      `Combine vulnerabilities to achieve greater impact, such as using XSS to steal a session token, then exploiting an IDOR vulnerability to escalate privileges, and finally exfiltrating data via SSRF, documenting the entire attack chain.`,
      `Test for Denial of Service (DoS) by exploiting resource-intensive API calls (e.g., recursive queries, large file uploads) or unhandled exceptions (e.g., null pointer dereference), using tools like Slowloris to simulate DoS attacks.`,
      `Assess the application’s logging and monitoring capabilities by performing exploits and checking if they are detected, such as looking for alerts in the client’s SIEM or log files for unauthorized API access attempts.`,
    ],
    summary: `This phase demonstrates the severity of vulnerabilities with proof-of-concept exploits, highlighting the potential impact on the application and its users.`,
  },
  {
    name: 'Persistence and Post-Exploitation Analysis',
    description: `We evaluate the application’s resilience to persistent threats and analyze the impact of a successful breach, focusing on establishing persistence, exfiltrating data, and assessing detection capabilities. This phase simulates a long-term attacker presence and tests the application’s ability to mitigate such threats.`,
    techniques: [
      `Establish persistence by uploading a web shell (e.g., a PHP script like <?php system($_GET['cmd']); ?>) through a file upload vulnerability, or modifying server-side scripts to include a backdoor that allows repeated access.`,
      `Exfiltrate sensitive data, such as user credentials or API keys, by encoding data in HTTP requests (e.g., Base64 in query parameters) or using out-of-band techniques like DNS tunneling to evade detection.`,
      `Test persistence mechanisms by creating cron jobs (on Linux servers) or scheduled tasks (on Windows servers) to execute malicious scripts, ensuring the attacker can regain access after a server reboot.`,
      `Assess the application’s logging and monitoring by performing actions like data exfiltration or privilege escalation, then reviewing logs to identify blind spots (e.g., unlogged API calls, missing audit trails).`,
      `Simulate attacker cleanup by deleting logs, disabling audit policies, or planting false evidence (e.g., fake user activity) to mislead forensic investigations, testing the application’s forensic readiness.`,
      `Evaluate the impact of a breach, such as financial loss from fraudulent transactions, data breaches exposing PII, or reputational damage from defacement, providing a detailed impact assessment for the client.`,
      `Test the application’s incident response by triggering alerts (e.g., brute-force attempts) and observing the client’s response time, communication, and mitigation efforts, providing feedback on their security operations.`,
    ],
    summary: `This phase evaluates the application’s ability to detect, mitigate, and recover from persistent threats, providing insights into long-term security risks.`,
  },
  {
    name: 'Reporting and Remediation Support',
    description: `We compile a comprehensive report detailing all findings, their impact, and remediation steps, ensuring the client can address vulnerabilities effectively. This phase also includes support for remediation and retesting to verify that fixes are successful.`,
    techniques: [
      `Document each vulnerability with detailed evidence, including screenshots of exploits, HTTP requests/responses, affected endpoints, and proof-of-concept code (e.g., a Python script for SSRF exploitation), assigning severity using CVSS scoring.`,
      `Explain the real-world impact of each issue, such as unauthorized access to user data, financial fraud through logic flaws, or denial of service causing downtime, with scenarios illustrating potential attacker actions.`,
      `Provide actionable remediation steps, such as implementing parameterized queries for SQL injection, enforcing rate-limiting on APIs, adding security headers like CSP, or updating outdated libraries to patched versions.`,
      `Include an executive summary for non-technical stakeholders, summarizing key risks, the overall security posture, and prioritized remediation actions, ensuring clarity for decision-makers.`,
      `Offer retesting support by revisiting exploited vulnerabilities after remediation, providing a follow-up report comparing pre- and post-remediation results to confirm that issues are resolved.`,
      `Conduct a knowledge transfer session with the client’s team, explaining findings, demonstrating exploitation techniques (if requested), and providing guidance on secure development practices (e.g., OWASP API Security Top 10).`,
      `Provide a technical appendix for developers, including detailed exploit steps, code snippets, and references to standards like OWASP ASVS for implementing secure coding practices.`,
    ],
    summary: `This phase ensures that findings are actionable, stakeholders are informed, and the application’s security is improved through effective remediation and knowledge sharing.`,
  },
];

interface MethodologyPageProps {
  documentNumber?: string;
  registerSectionPage?: (section: string, page: number) => void;
  sectionId?: string;
}

const MethodologyPage: React.FC<MethodologyPageProps> = ({ documentNumber, registerSectionPage, sectionId }) => (
  <Page size="A4" id={sectionId} style={styles.page}>
    {/* Register section page for TOC */}
    {registerSectionPage && (
      <Text style={{ display: 'none', fontSize: 1 }} render={({ pageNumber }) => {
        registerSectionPage('Methodology', pageNumber);
        return '';
      }} />
    )}
    <Text style={styles.sectionTitle}>Web + API Penetration Testing Methodology</Text>
    <View style={styles.divider} />
    <Text style={styles.intro}>
      Our Web and API penetration testing methodology is structured into detailed phases to systematically identify, exploit, and assess vulnerabilities in web applications and APIs. Each phase is designed to build on the previous one, ensuring a thorough evaluation of the target’s security posture.
    </Text>
    {phases.map((phase) => (
      <View key={phase.name} style={styles.phaseBlock}>
        <Text style={styles.phaseName}>{phase.name}</Text>
        <Text style={styles.phaseDesc}><Text style={{ fontWeight: 'bold' }}>Description:</Text> {phase.description}</Text>
        <Text style={styles.phaseTechniques}>Techniques:</Text>
        <View style={styles.bulletList}>
          {phase.techniques.map((tech, i) => (
            <Text key={i} style={styles.bulletItem}>• {tech}</Text>
          ))}
        </View>
        {phase.summary && <Text style={styles.phaseSummary}>{phase.summary}</Text>}
      </View>
    ))}
    <View style={styles.footer} fixed>
      <Text style={styles.footerLeft}>{documentNumber || 'Document Number'}</Text>
      <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
    </View>
  </Page>
);

export default MethodologyPage; 