import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { ReportsInfo } from "../../../utils/api/endpoints/reports/parseTarget";
import { fetchReportsInfo } from "../../../utils/api/endpoints/reports/getReport";
import Pagination from "../../../components/retests/utils/Pagination";
import { parseCategoryString } from "../../../utils/categoryUtils";

const ReportTable: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [reportData, setReportData] = useState<ReportsInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [expandedTitleId, setExpandedTitleId] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const reportsPerPage = 8;

  useEffect(() => {
    const fetchReports = async () => {
      if (!id) return;
      try {
        setLoading(true);
        const data = await fetchReportsInfo(Number(id));

        if (data === null) {
          console.warn(
            "Skipping report data fetch due to user role restrictions."
          );
          setReportData(null);
        } else {
          setReportData(data);
        }
      } catch (error) {
        console.error("Error fetching reports:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchReports();
  }, [id]);

  if (loading)
    return <p className="text-center text-gray-500">Loading reports...</p>;

  const indexOfLastReport = currentPage * reportsPerPage;
  const indexOfFirstReport = indexOfLastReport - reportsPerPage;
  const currentReports =
    reportData?.reports.slice(indexOfFirstReport, indexOfLastReport) || [];
  const totalPages = reportData
    ? Math.ceil(reportData.reports.length / reportsPerPage)
    : 0;

  return (
    <div className="container mx-auto">
      <div className="overflow-hidden rounded-md border border-gray-200 shadow-md">
        <div className="max-h-[30rem] overflow-y-auto">
          <table className="min-w-full bg-white">
            <thead className="sticky top-0 z-10 bg-blue-700 text-white">
              <tr>
                <th className="w-1/5 px-6 py-4 text-left">Report Name</th>
                <th className="w-1/5 px-6 py-4 text-left">Report Category</th>
                <th className="w-1/5 px-6 py-4 text-left">Severity</th>
                <th className="w-1/5 px-6 py-4 text-left">Status</th>
                <th className="w-1/5 px-6 py-4 text-left">Date</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan={5} className="py-4 text-center text-gray-500">
                    Loading reports...
                  </td>
                </tr>
              ) : currentReports.length === 0 ? (
                <tr>
                  <td colSpan={5} className="py-4 text-center text-gray-500">
                    No reports generated yet.
                  </td>
                </tr>
              ) : (
                currentReports.map(report => {
                  const words = report.title.split(" ");
                  const truncatedTitle =
                    words.length > 2
                      ? words.slice(0, 2).join(" ") + "..."
                      : report.title;
                  const isExpanded = expandedTitleId === report.id;

                  return (
                    <tr
                      key={report.id}
                      className="cursor-pointer border-b hover:bg-gray-100"
                      onClick={() =>
                        navigate(`/dashboard/reports/${report.id}`)
                      }
                    >
                      <td className="w-3/10 px-6 py-4">
                        <span
                          className="relative cursor-pointer whitespace-nowrap text-[16px] font-semibold"
                          onClick={e => {
                            e.stopPropagation();
                          }}
                          title={report.title}
                        >
                          {isExpanded ? report.title : truncatedTitle}
                        </span>
                        <br />
                        <span className="text-[10px] text-sm text-gray-500">
                          #CTB{report.id}{" "}
                        </span>
                      </td>
                      <td className="w-3/10 px-6 py-4 text-[14px]">
                        {parseCategoryString(report.category)}
                      </td>
                      <td className="w-2/10 px-6 py-4">
                        <span
                          className={`inline-flex items-center gap-2 rounded px-4 py-2 text-[12px] font-semibold capitalize text-black
                          ${
                            report.severity.toLowerCase() === "low"
                              ? "bg-green-500 bg-opacity-40"
                              : report.severity.toLowerCase() === "medium"
                              ? "bg-yellow-300 bg-opacity-40"
                              : report.severity.toLowerCase() === "high"
                              ? "bg-orange-300 bg-opacity-40"
                              : report.severity.toLowerCase() === "critical"
                              ? "bg-red-500 bg-opacity-40 text-white"
                              : "bg-gray-400 bg-opacity-40"
                          }`}
                        >
                          <span
                            className={`h-2 w-2 rounded-full ${
                              report.severity.toLowerCase() === "low"
                                ? "bg-green-700"
                                : report.severity.toLowerCase() === "medium"
                                ? "bg-yellow-500"
                                : report.severity.toLowerCase() === "high"
                                ? "bg-orange-500"
                                : report.severity.toLowerCase() === "critical"
                                ? "bg-red-400"
                                : "bg-gray-600"
                            }`}
                          ></span>
                          {report.severity.charAt(0).toUpperCase() +
                            report.severity.slice(1).toLowerCase()}
                        </span>
                      </td>
                      <td className="w-2/10 py-4">
                        <span className="rounded bg-gray-500 px-4 py-2 text-[12px] font-normal capitalize text-white">
                          {report.status}
                        </span>
                      </td>
                      <td className="w-1/10 px-6 py-4 text-[14px]">
                        {new Date(report.submitted_date).toLocaleDateString()}
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>

        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
        />
      </div>
    </div>
  );
};

export default ReportTable;
