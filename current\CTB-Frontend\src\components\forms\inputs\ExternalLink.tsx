import toast from "react-hot-toast";
import { NotificationMethods } from "../../../utils/api/endpoints/programs/parsePrograms";
import OutlineButton from "../../buttons/OutlineButton";
import { ChangeEvent, useState } from "react";

const LinkToggle = ({
  title,
  initialValue,
  programId,
  methodOpted,
  optMethod,
  onInput,
  onInsert,
  onRemove,
  onIntegrate,
  className
}: {
  title: string;
  initialValue: any;
  programId: string;
  methodOpted: boolean;
  optMethod?: NotificationMethods;
  onInput: (inputValue: string) => void;
  onInsert: (slackChannelLink: string, programId: string) => void;
  onRemove: (programId: string) => void;
  onIntegrate: (optMethod: NotificationMethods, programId: string) => void;
  className:string ; 
}) => {
  const [value, setvalue] = useState(initialValue);

  const changeEventhandler = (e: ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setvalue(newValue);
    onInput(newValue);
  };

  const handleInsert = () => {
    onInsert(value, programId);
  };

  const handleRemove = () => {
    onRemove(programId);
    setvalue("");
  };

  const handleIntegrate = () => {
    if (optMethod) {
      onIntegrate(optMethod, programId);
    } else {
      toast.error("could not read method (internal error)");
    }
  };
  return (
    <article className={"grid "}>
      <span className="flex flex-col w-full  items-start gap-4">
      <input
        type="text"
        className="w-full h-20 p-4 bg-gray-100 text-black font-normal text-lg rounded-md resize-none border border-transparent focus:border-ctb-grey-300 focus:outline-none placeholder-opacity-45 placeholder:text-base"
        placeholder={title}
        value={value}
          onInput={changeEventhandler}
        />
        <div className="flex justify-end items-center w-full space-x-4 mt-2">
 
    <>
      <button
        onClick={handleInsert}
        className="px-5 h-[45px] bg-blue-600 text-white font-medium rounded-xl hover:bg-blue-700 transition"
      >
        Insert
      </button>
      <button
        onClick={handleRemove}
        className="px-5 h-[45px] border border-blue-600 text-blue-600 font-medium rounded-xl hover:bg-blue-100 transition"
      >
        Remove
      </button>
    </>
 
 </div>
      </span>
    </article>
  );
};

export default LinkToggle;
