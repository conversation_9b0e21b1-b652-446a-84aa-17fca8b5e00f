import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import useProgram from "../../../utils/hooks/programs/useProgram";
import BusinessProfileCard from "../../../components/programs/cards/BusinessProfileCard";
import ProgramInfoCard from "../../../components/programs/cards/ProgramInfoCard";
import useUserCredentials from "../../../utils/hooks/user/useUserCredentials";
import ManageProgram from "../../../components/programs/ManageProgram";
import InlineContainer from "../../../components/common/InlineContainer";
import OutlineButton from "../../../components/buttons/OutlineButton";
import DeleteIcon from "../../../assets/icons/DeleteIcon";
import EditIcon from "../../../assets/icons/EditIcon";
import PieChartIcon from "../../../assets/icons/PieChartIcon";

const Management: React.FC = () => {
  const navigate = useNavigate();
  const params = useParams(); 

  const programId = params.programId || params.id;
  
 
  const { role } = useUserCredentials();
  const programIdNumber = programId ? parseInt(programId, 10) : undefined;
  const { program, isLoading, deleteProgram: deleteProgramFromHook } = useProgram(programIdNumber);

  useEffect(() => {
    console.log("Extracted programId from URL:", programId);
  }, [programId]);

  
  if (!programId) {
    return <p className="text-red-500">Program ID is missing in the URL.</p>;
  }

  if (isLoading) {
    return <p>Loading program details...</p>;
  }

  if (!program) {
    return <p className="text-red-500">Program not found.</p>;
  }

  return (
    <div className="flex gap-4  ">
      <div className="w-full">
        <ManageProgram program={program} />
      </div>
    </div>
  );
};

export default Management;
