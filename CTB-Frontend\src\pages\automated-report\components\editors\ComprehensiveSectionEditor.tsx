import React, { useState, useCallback } from 'react';
import { ReportData, DetailedFinding, Report } from '../../types/report.types';
import HtmlEditor from '../HtmlEditor';
import MethodologySelectorEditor from './MethodologySelectorEditor';

interface ComprehensiveSectionEditorProps {
  reportData: ReportData;
  activeSection: string;
  onInputChange: (field: string, value: string) => void;
  onHtmlChange: (field: string, value: string) => void;
  onDataChange: (field: string, value: any) => void;
  onTableChange: (field: string, value: number) => void;
  onFindingChange: (index: number, field: keyof DetailedFinding, value: string, severity?: 'Critical' | 'High' | 'Medium' | 'Low') => void;
  onRemoveFinding: (index: number, severity?: 'Critical' | 'High' | 'Medium' | 'Low') => void;
  onAddFinding: (severity?: 'Critical' | 'High' | 'Medium' | 'Low') => void;
  onTargetDetailsChange: (index: number, field: 'type' | 'url', value: string) => void;
  onAddTarget: () => void;
  onRemoveTarget: (index: number) => void;
  onKeyFindingChange: (index: number, field: string, value: string) => void;
  onAddKeyFinding: () => void;
  onRemoveKeyFinding: (index: number) => void;
}

type SeverityCategory = 'Critical' | 'High' | 'Medium' | 'Low';
type FindingStatus = 'Open' | 'Closed' | 'In Progress';

const ComprehensiveSectionEditor: React.FC<ComprehensiveSectionEditorProps> = ({
  reportData,
  activeSection,
  onInputChange,
  onHtmlChange,
  onDataChange,
  onTableChange,
  onFindingChange,
  onRemoveFinding,
  onAddFinding,
  onTargetDetailsChange,
  onAddTarget,
  onRemoveTarget,
  onKeyFindingChange,
  onAddKeyFinding,
  onRemoveKeyFinding
}) => {
  const severityColors = {
    Critical: 'bg-red-50 border-red-200 text-red-700',
    High: 'bg-orange-50 border-orange-200 text-orange-700',
    Medium: 'bg-yellow-50 border-yellow-200 text-yellow-700',
    Low: 'bg-blue-50 border-blue-200 text-blue-700'
  };

  const statusColors = {
    Open: 'bg-red-100 text-red-800',
    Closed: 'bg-green-100 text-green-800',
    'In Progress': 'bg-yellow-100 text-yellow-800'
  };

  // Cover Page Fields
  const coverPageFields = [
    { id: 'report_title', label: 'Report Title', type: 'text', placeholder: 'Enter report title', required: true },
    { id: 'company_name', label: 'Company Name', type: 'text', placeholder: 'Enter company name', required: true },
    { id: 'document_number', label: 'Document Number', type: 'text', placeholder: 'Enter document number' },
    { id: 'version_number', label: 'Version Number', type: 'text', placeholder: 'Enter version number' },
    { id: 'current_date', label: 'Current Date', type: 'date', required: true },
    { id: 'revision_date', label: 'Revision Date', type: 'date' },
    { id: 'date_of_request', label: 'Date of Request', type: 'date' },
    { id: 'prepared_by', label: 'Prepared By', type: 'text', placeholder: 'Enter preparer name' },
    { id: 'reviewed_by', label: 'Reviewed By', type: 'text', placeholder: 'Enter reviewer name' },
    { id: 'approved_by', label: 'Approved By', type: 'text', placeholder: 'Enter approver name' },
    { id: 'test_lead', label: 'Test Lead', type: 'text', placeholder: 'Enter test lead name' },
    { id: 'version_description', label: 'Version Description', type: 'text', placeholder: 'Enter version description' }
  ];

  const renderCoverPageSection = () => (
    <div className="space-y-4">
      {coverPageFields.map(field => (
        <div key={field.id} className="flex flex-col">
          <label className="block text-xs font-medium text-gray-700 mb-1">
            {field.label}
            {field.required && <span className="text-red-500 ml-0.5">*</span>}
          </label>
          <input
            type={field.type}
            value={(reportData[field.id as keyof ReportData] as string) || ''}
            onChange={(e) => onInputChange(field.id, e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            placeholder={field.placeholder}
            required={field.required}
          />
        </div>
      ))}
    </div>
  );

  const renderTargetDetailsSection = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h4 className="text-sm font-semibold text-gray-800">Target Details</h4>
        <button
          onClick={onAddTarget}
          className="px-3 py-1.5 bg-blue-600 text-white text-xs rounded-md hover:bg-blue-700 transition-colors duration-200"
        >
          Add Target
        </button>
      </div>
      
      {reportData.target_details?.map((target, index) => (
        <div key={index} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
          <div className="flex justify-between items-center mb-3">
            <h5 className="text-sm font-medium text-gray-700">Target {index + 1}</h5>
            <button
              onClick={() => onRemoveTarget(index)}
              className="text-red-600 hover:text-red-800 text-sm"
            >
              Remove
            </button>
          </div>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">Type</label>
              <input
                type="text"
                value={target.type}
                onChange={(e) => onTargetDetailsChange(index, 'type', e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="e.g., Web, API, Mobile"
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">URL</label>
              <input
                type="text"
                value={target.url}
                onChange={(e) => onTargetDetailsChange(index, 'url', e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="e.g., https://example.com"
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const renderSeverityCountsSection = () => (
    <div className="space-y-4">
      <h4 className="text-sm font-semibold text-gray-800">Severity Counts</h4>
      <div className="grid grid-cols-2 gap-4">
        {Object.entries(reportData.open_close_counts_by_severity).map(([severity, counts]) => (
          <div key={severity} className="bg-gray-50 rounded-lg p-3 border border-gray-200">
            <div className="flex items-center gap-2 mb-3">
              <div className={`w-2 h-2 rounded-full ${
                severity === 'Critical' ? 'bg-red-500' :
                severity === 'High' ? 'bg-orange-500' :
                severity === 'Medium' ? 'bg-yellow-500' :
                'bg-blue-500'
              }`} />
              <h5 className="font-semibold text-sm text-gray-800">{severity}</h5>
            </div>
            <div className="space-y-2">
              {Object.entries(counts).map(([type, value]) => (
                <div key={type} className="flex items-center gap-2">
                  <span className="text-xs text-gray-600 min-w-[60px]">{type}:</span>
                  <div className="flex-1 flex items-center bg-white border border-gray-200 rounded-md overflow-hidden">
                    <button
                      onClick={() => onTableChange(`open_close_counts_by_severity.${severity}.${type}`, Math.max(0, value - 1))}
                      className="w-6 h-6 flex items-center justify-center hover:bg-gray-50 active:bg-gray-100 border-r border-gray-200 text-gray-600"
                    >
                      −
                    </button>
                    <input
                      type="number"
                      value={value}
                      onChange={(e) => onTableChange(`open_close_counts_by_severity.${severity}.${type}`, parseInt(e.target.value) || 0)}
                      className="w-12 text-center focus:outline-none focus:ring-1 focus:ring-blue-500"
                      min="0"
                    />
                    <button
                      onClick={() => onTableChange(`open_close_counts_by_severity.${severity}.${type}`, value + 1)}
                      className="w-6 h-6 flex items-center justify-center hover:bg-gray-50 active:bg-gray-100 border-l border-gray-200 text-gray-600"
                    >
                      +
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
      
      {/* Total Counts */}
      <div className="grid grid-cols-3 gap-4 mt-4">
        {[
          { label: 'Total Open', value: reportData.total_open, field: 'total_open', color: 'bg-blue-50 border-blue-200' },
          { label: 'Total Closed', value: reportData.total_closed, field: 'total_closed', color: 'bg-green-50 border-green-200' },
          { label: 'Total Findings', value: reportData.total_findings, field: 'total_findings', color: 'bg-purple-50 border-purple-200' }
        ].map(({ label, value, field, color }) => (
          <div key={field} className={`${color} rounded-lg p-3 border`}>
            <h5 className="font-semibold text-sm text-gray-800 mb-2">{label}</h5>
            <div className="flex items-center bg-white border border-gray-200 rounded-md overflow-hidden">
              <button
                onClick={() => onTableChange(field, Math.max(0, value - 1))}
                className="w-6 h-6 flex items-center justify-center hover:bg-gray-50 active:bg-gray-100 border-r border-gray-200 text-gray-600"
              >
                −
              </button>
              <input
                type="number"
                value={value}
                onChange={(e) => onTableChange(field, parseInt(e.target.value) || 0)}
                className="flex-1 text-center focus:outline-none focus:ring-1 focus:ring-blue-500"
                min="0"
              />
              <button
                onClick={() => onTableChange(field, value + 1)}
                className="w-6 h-6 flex items-center justify-center hover:bg-gray-50 active:bg-gray-100 border-l border-gray-200 text-gray-600"
              >
                +
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderVulnerabilityRatingsSection = () => (
    <div className="space-y-4">
      <h4 className="text-sm font-semibold text-gray-800">Vulnerability Ratings</h4>
      <div className="grid grid-cols-2 gap-4">
        {['critical', 'high', 'medium', 'low'].map(severity => (
          <div key={severity} className="bg-gray-50 rounded-lg p-3 border border-gray-200">
            <label className="block text-xs font-medium text-gray-700 mb-2 capitalize">{severity}</label>
            <textarea
              value={reportData.vulnerability_ratings?.[severity as keyof typeof reportData.vulnerability_ratings] || ''}
              onChange={(e) => onDataChange(`vulnerability_ratings.${severity}`, e.target.value)}
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              rows={3}
              placeholder={`Enter ${severity} vulnerability rating description`}
            />
          </div>
        ))}
      </div>
    </div>
  );

  const renderKeyFindingsSection = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h4 className="text-sm font-semibold text-gray-800">Key Findings</h4>
        <button
          onClick={onAddKeyFinding}
          className="px-3 py-1.5 bg-blue-600 text-white text-xs rounded-md hover:bg-blue-700 transition-colors duration-200"
        >
          Add Finding
        </button>
      </div>
      
      {reportData.reports_list?.map((finding, index) => (
        <div key={index} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
          <div className="flex justify-between items-center mb-3">
            <h5 className="text-sm font-medium text-gray-700">Finding {index + 1}</h5>
            <button
              onClick={() => onRemoveKeyFinding(index)}
              className="text-red-600 hover:text-red-800 text-sm"
            >
              Remove
            </button>
          </div>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">Abbreviation</label>
              <input
                type="text"
                value={finding.abbreviation}
                onChange={(e) => onKeyFindingChange(index, 'abbreviation', e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="e.g., F1"
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">Title</label>
              <input
                type="text"
                value={finding.title}
                onChange={(e) => onKeyFindingChange(index, 'title', e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Enter finding title"
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">Severity</label>
              <select
                value={finding.severity_category}
                onChange={(e) => onKeyFindingChange(index, 'severity_category', e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white"
              >
                {['Critical', 'High', 'Medium', 'Low'].map(severity => (
                  <option key={severity} value={severity}>{severity}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">Status</label>
              <select
                value={finding.status}
                onChange={(e) => onKeyFindingChange(index, 'status', e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white"
              >
                {['Open', 'Closed', 'In Progress'].map(status => (
                  <option key={status} value={status}>{status}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const renderDetailedFindingsSection = () => (
    <div className="space-y-6">
      {(['Critical', 'High', 'Medium', 'Low'] as SeverityCategory[]).map(severity => {
        const findings = reportData.detailed_findings?.filter(f => f.severity_category === severity) || [];
        
        return (
          <div key={severity} className="bg-white rounded-lg border border-gray-200">
            <div className="bg-gradient-to-r from-blue-50 to-blue-100 px-4 py-3 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-semibold text-gray-800">{severity} Findings</h4>
                  <p className="text-xs text-gray-600">Manage {severity.toLowerCase()} severity findings</p>
                </div>
                <button
                  onClick={() => onAddFinding(severity)}
                  className="px-3 py-1.5 bg-blue-600 text-white text-xs rounded-md hover:bg-blue-700 transition-colors duration-200"
                >
                  Add Finding
                </button>
              </div>
            </div>
            
            <div className="p-4">
              {findings.length === 0 ? (
                <div className="text-center py-8 bg-gray-50 rounded-lg border border-dashed border-gray-200">
                  <p className="text-sm text-gray-600">No {severity.toLowerCase()} findings added yet</p>
                  <button
                    onClick={() => onAddFinding(severity)}
                    className="mt-2 text-blue-600 hover:text-blue-700 text-sm font-medium"
                  >
                    Add your first finding
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  {findings.map((finding, index) => (
                    <div key={index} className={`rounded-lg p-4 border ${severityColors[severity]}`}>
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${
                            severity === 'Critical' ? 'bg-red-500' :
                            severity === 'High' ? 'bg-orange-500' :
                            severity === 'Medium' ? 'bg-yellow-500' :
                            'bg-blue-500'
                          }`} />
                          <h5 className="font-medium text-sm">Finding {index + 1}</h5>
                        </div>
                        <button
                          onClick={() => onRemoveFinding(index, severity)}
                          className="text-gray-400 hover:text-red-600 transition-colors duration-200"
                        >
                          Remove
                        </button>
                      </div>
                      
                      <div className="space-y-3">
                        {[
                          { field: 'abbreviation', label: 'Abbreviation', type: 'text', placeholder: 'e.g., F1, F2' },
                          { field: 'title', label: 'Title', type: 'text', placeholder: 'Enter finding title' },
                          { field: 'scope', label: 'Scope', type: 'text', placeholder: 'Enter vulnerable URL/component' },
                          { field: 'description', label: 'Description', type: 'textarea', placeholder: 'Enter finding description' },
                          { field: 'instructions', label: 'Steps to Reproduce', type: 'textarea', placeholder: 'Enter steps to reproduce' },
                          { field: 'impact', label: 'Impact', type: 'textarea', placeholder: 'Enter impact of the finding' },
                          { field: 'fix', label: 'Remediation', type: 'textarea', placeholder: 'Enter remediation steps' }
                        ].map(({ field, label, type, placeholder }) => (
                          <div key={field}>
                            <label className="block text-xs font-medium text-gray-700 mb-1">{label}</label>
                            {type === 'textarea' ? (
                              <textarea
                                value={finding[field as keyof DetailedFinding] as string}
                                onChange={(e) => onFindingChange(index, field as keyof DetailedFinding, e.target.value, severity)}
                                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                                rows={3}
                                placeholder={placeholder}
                              />
                            ) : (
                              <input
                                type={type}
                                value={finding[field as keyof DetailedFinding] as string}
                                onChange={(e) => onFindingChange(index, field as keyof DetailedFinding, e.target.value, severity)}
                                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                                placeholder={placeholder}
                              />
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );

  const renderSection = () => {
    switch (activeSection) {
      case 'cover':
        return renderCoverPageSection();
      case 'html_content':
        return renderHtmlContentSection();
      case 'target_details':
        return renderTargetDetailsSection();
      case 'severity_counts':
        return renderSeverityCountsSection();
      case 'vulnerability_ratings':
        return renderVulnerabilityRatingsSection();
      case 'key_findings':
        return renderKeyFindingsSection();
      case 'detailed_findings':
        return renderDetailedFindingsSection();
      case 'methodology_selector':
        return <MethodologySelectorEditor value={reportData.methodology || {}} onChange={v => onDataChange('methodology', v)} />;
      default:
        return <div className="text-center py-8 text-gray-500">Select a section to edit</div>;
    }
  };

  const getSectionTitle = () => {
    const sectionMap = {
      cover: 'Cover Page',
      html_content: 'HTML Content',
      target_details: 'Target Details',
      severity_counts: 'Severity Counts',
      vulnerability_ratings: 'Vulnerability Ratings',
      key_findings: 'Key Findings',
      detailed_findings: 'Detailed Findings',
      methodology_selector: 'Methodology Selection',
    };
    return sectionMap[activeSection as keyof typeof sectionMap] || 'Section Editor';
  };

  const renderHtmlContentSection = () => (
    <div className="space-y-6">
      {[
        { id: 'executive_summary', label: 'Executive Summary' },
        { id: 'key_findings', label: 'Key Findings' },
        { id: 'scope', label: 'Scope' },
        { id: 'project_objectives', label: 'Project Objectives' },
        { id: 'methodology', label: 'Methodology' },
        { id: 'findings', label: 'Findings' },
        { id: 'recommendations', label: 'Recommendations' },
        { id: 'conclusion', label: 'Conclusion' },
        { id: 'disclaimer', label: 'Disclaimer' }
      ].map(field => (
        <div key={field.id} className="bg-white rounded-lg border border-gray-200 p-4">
          <h4 className="text-sm font-semibold text-gray-800 mb-3">{field.label}</h4>
          <HtmlEditor
            field={field.id as keyof ReportData}
            title={field.label}
            reportData={reportData}
            onHtmlChange={onHtmlChange}
          />
        </div>
      ))}
    </div>
  );

  return (
    <div className="h-full overflow-y-auto">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 shadow-md">
          <h2 className="text-xl font-bold text-white">{getSectionTitle()}</h2>
          <p className="text-blue-100 text-sm mt-1">Edit {getSectionTitle().toLowerCase()} content</p>
        </div>
        
        <div className="p-6">
          {renderSection()}
        </div>
      </div>
    </div>
  );
};

export default ComprehensiveSectionEditor; 