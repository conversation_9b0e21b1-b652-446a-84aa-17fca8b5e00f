import { useState } from "react";
import { updateRetestStatus } from "../../../utils/api/endpoints/retests/retests";

const useCommentForm = (
  retest_id: string | undefined,
  role: string,
  refetchLogs: () => void
) => {
  const [comment, setComment] = useState<string>("");
  const [selectedStatus, setSelectedStatus] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [pocFile, setPocFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const handleCommentSubmit = async (actionTaken: string, comment: string) => {
    if (!retest_id) {
      setError("Retest ID is undefined");
      return;
    }

    try {
      if (!selectedStatus || !actionTaken || !comment) {
        setError("All fields are required");
        return;
      }

      setIsSubmitting(true);

      const response = await updateRetestStatus(
        retest_id,
        selectedStatus,
        actionTaken,
        comment,
        pocFile || undefined
      );

      // Reset the form
      setComment("");
      setSelectedStatus("");
      setPocFile(null);
      setError(null);
      setIsSubmitting(false);

      // Trigger a re-fetch of logs
      refetchLogs();
    } catch (error: any) {
      console.error("Error submitting comment:", error);
      setError(error.message || "An error occurred");
      setIsSubmitting(false);
    }
  };

  return {
    comment,
    setComment,
    selectedStatus,
    setSelectedStatus,
    pocFile,
    setPocFile,
    handleCommentSubmit,
    error,
    setError,
    isSubmitting
  };
};

export default useCommentForm;
