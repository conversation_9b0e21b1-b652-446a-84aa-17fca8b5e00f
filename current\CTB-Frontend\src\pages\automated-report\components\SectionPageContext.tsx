import React, { createContext, useContext, useState } from 'react';

interface SectionPageContextType {
  sectionPages: Record<string, number>;
  updateSectionPage: (section: string, pageNumber: number) => void;
}

const SectionPageContext = createContext<SectionPageContextType | undefined>(undefined);

export const useSectionPages = () => {
  const ctx = useContext(SectionPageContext);
  if (!ctx) throw new Error('useSectionPages must be used within a SectionPageProvider');
  return ctx;
};

export const SectionPageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [sectionPages, setSectionPages] = useState<Record<string, number>>({});
  const updateSectionPage = (section: string, pageNumber: number) => {
    setSectionPages(prev => {
      if (prev[section] === pageNumber) return prev;
      return { ...prev, [section]: pageNumber };
    });
  };
  return (
    <SectionPageContext.Provider value={{ sectionPages, updateSectionPage }}>
      {children}
    </SectionPageContext.Provider>
  );
}; 