import axios from "../../axios";

const URL = "/v2/automated-report";

// Get all pentest reports for admin with filtering and pagination
export const getPentestReports = async (filters: any) => {
  const queryParams = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams.append(key, value.toString());
    }
  });

  const response = await axios.get(`${URL}/admin/pentest-reports?${queryParams.toString()}`);
  return response.data;
};

// Get program reports for a specific report
export const getProgramReports = async (reportId: string, reportData?: any) => {
  const response = await axios.post(`${URL}/get-program-reports/${reportId}`, {
    reportData
  });
  return response.data;
};

// Create automated report
export const createAutomatedReport = async (data: {
  company_name: string;
  program_ids: number[];
}) => {
  const response = await axios.post(`${URL}/create`, data);
  return response.data;
};

// Get user programs
export const getUserPrograms = async (filters?: any) => {
  const response = await axios.post(`${URL}/getUserPrograms`, filters || {});
  return response.data;
};

// Get pending automated report versions
export const getPendingAutomatedReportVersions = async (filters?: any) => {
  const response = await axios.post(`${URL}/pending-versions`, filters || {});
  return response.data;
};

// Check pending company report
export const checkPendingCompanyReport = async (data: {
  program_ids: number[];
}) => {
  const response = await axios.get(`${URL}/check-pending-report`, {
    params: data
  });
  return response.data;
};

// Get business reports
export const getBusinessReports = async (filters?: any) => {
  const response = await axios.get(`${URL}/business-reports`, {
    params: filters
  });
  return response.data;
};

// Preview template
export const previewTemplate = async (reportId: string, reportData?: any) => {
  const response = await axios.post(`${URL}/preview-template/${reportId}`, {
    reportData
  });
  return response.data;
}; 