// StatusFormatter.tsx
import React from "react";

interface StatusFormatterProps {
  status: string;
}

const StatusFormatter: React.FC<StatusFormatterProps> = ({ status }) => {
  const getPastTenseStatus = (status: string): string => {
    const statusMap: { [key: string]: string } = {
      "QA Review In Process": "QA Review In Process",
      "Fix Approved": "Fix Approved",
      "Fix Rejected": "Fix Rejected",
      "Findings Rejected": "Findings Rejected",
      "Need Information": "Need Information",
      "Retest In Process": "Retest In Process",
      "Fix Verified": "Fix Verified",
      "Fix Failed": "Fix Failed",
      "Fix in Progress": "Fix in Progress",
      "Risk Accepted and Closed": "Risk Accepted and Closed",
      "Close Retest": "Retest Closed",
      "Reopen Retest": "Retest Reopened",
      "Request Further Action": "Further Action Requested",
      chat: "Chat"
    };
    return statusMap[status] || status;
  };

  return <span>{getPastTenseStatus(status)}</span>;
};

export default StatusFormatter;
