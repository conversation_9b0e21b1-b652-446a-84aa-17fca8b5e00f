import { <PERSON> } from "react-router-dom";
import { CTBProgram } from "../../utils/api/endpoints/programs/parsePrograms";
import { CTBReport } from "../../utils/api/endpoints/reports/parseReports";
import DualProfileQuickLook from "../profile/DualProfileQuickLook";

/**
 * Displays a large banner with the details for the program
 * the given report is reporting on.
 */
const ReportProgramBanner = ({
  report,
  program,
  large,
  className
}: {
  report: CTBReport;
  program: CTBProgram;
  large?: boolean;
  className?: string;
}) => {
  return (
    <article className={"flex w-full flex-col gap-4 font-light " + className}>
      <DualProfileQuickLook
        title={
          <p>
            By{" "}
            <span className="text-ctb-blue-200">
              {report.creator?.displayName}
            </span>{" "}
            for:{" "}
            <Link
              to={`/dashboard/programs/${program.id}`}
              className="text-ctb-blue-200 hover:underline"
            >
              {program.title}
            </Link>
          </p>
        }
        subtitle={
          large ? (
            <p>
              with{" "}
              <span className="text-ctb-blue-400">
                {program.creator?.displayName}
              </span>
            </p>
          ) : (
            <p className="text-ctb-blue-50">{report.submittedDate}</p>
          )
        }
        leftImageSrc={report.creator?.pfp}
        rightImageSrc={program.profilePicture || program.creator?.pfp}
        large={large}
      />

      {report.scope && (
        <div className="flex items-center gap-2">
          <p className="text-ctb-blue">Targeting:</p>

          {/* TODO: Use icon from profile page when merged */}
          <p className="w-fit rounded-lg bg-ctb-grey-200 px-2 py-1 text-sm leading-[150%]">
            {report?.scope}
          </p>
        </div>
      )}
    </article>
  );
};

export default ReportProgramBanner;
