import React from 'react';
import { ReportData } from '../../types/report.types';

export interface DocumentReferenceEditorProps {
  reportData: ReportData;
  onInputChange: (field: string, value: string) => void;
}

const DocumentReferenceEditor: React.FC<DocumentReferenceEditorProps> = ({ reportData, onInputChange }) => {
  return (
    <div className="h-full overflow-y-auto">
      <div className="bg-white rounded-xl shadow-lg border border-slate-100 p-6 mb-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-1 h-8 bg-gradient-to-b from-blue-500 to-blue-700 rounded-full" />
          <div>
            <h3 className="text-base font-bold text-blue-900 tracking-tight">Document Reference</h3>
            <p className="text-xs text-blue-700/80 font-medium mt-0.5">Edit document reference details</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-2">
          {/* First column: Report Title, Company Name, Document Number, Prepared By, Reviewed By */}
          <div className="flex flex-col gap-2">
        <div>
              <label className="block text-xs font-medium text-gray-700 mb-0.5">Report Title</label>
          <input
                className="w-full px-2.5 py-1.5 text-sm bg-white border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200 placeholder:text-slate-400"
            value={reportData.report_title}
            onChange={e => onInputChange("report_title", e.target.value)}
                placeholder="Report Title"
          />
        </div>
        <div>
              <label className="block text-xs font-medium text-gray-700 mb-0.5">Company Name</label>
          <input
                className="w-full px-2.5 py-1.5 text-sm bg-white border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200 placeholder:text-slate-400"
            value={reportData.company_name}
            onChange={e => onInputChange("company_name", e.target.value)}
                placeholder="Company Name"
          />
        </div>
        <div>
              <label className="block text-xs font-medium text-gray-700 mb-0.5">Document Number</label>
          <input
                className="w-full px-2.5 py-1.5 text-sm bg-white border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200 placeholder:text-slate-400"
            value={reportData.document_number}
            onChange={e => onInputChange("document_number", e.target.value)}
                placeholder="Document Number"
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-0.5">Prepared By</label>
              <input
                className="w-full px-2.5 py-1.5 text-sm bg-white border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200 placeholder:text-slate-400"
                value={reportData.prepared_by}
                onChange={e => onInputChange("prepared_by", e.target.value)}
                placeholder="Prepared By"
          />
        </div>
        <div>
              <label className="block text-xs font-medium text-gray-700 mb-0.5">Reviewed By</label>
              <input
                className="w-full px-2.5 py-1.5 text-sm bg-white border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200 placeholder:text-slate-400"
                value={reportData.reviewed_by}
                onChange={e => onInputChange("reviewed_by", e.target.value)}
                placeholder="Reviewed By"
              />
            </div>
          </div>
          {/* Second column: Version Number, Dates side by side, Approved By, Test Lead, Version Description */}
          <div className="flex flex-col gap-2">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-0.5">Version Number</label>
          <input
                className="w-full px-2.5 py-1.5 text-sm bg-white border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200 placeholder:text-slate-400"
            value={reportData.version_number}
            onChange={e => onInputChange("version_number", e.target.value)}
                placeholder="Version Number"
          />
        </div>
            <div className="grid grid-cols-2 gap-2">
        <div>
                <label className="block text-xs font-medium text-gray-700 mb-0.5">Current Date</label>
          <input
                  className="w-full px-2.5 py-1.5 text-sm bg-white border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200 placeholder:text-slate-400"
            value={reportData.current_date}
            onChange={e => onInputChange("current_date", e.target.value)}
            type="date"
          />
        </div>
        <div>
                <label className="block text-xs font-medium text-gray-700 mb-0.5">Revision Date</label>
          <input
                  className="w-full px-2.5 py-1.5 text-sm bg-white border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200 placeholder:text-slate-400"
            value={reportData.revision_date}
            onChange={e => onInputChange("revision_date", e.target.value)}
            type="date"
          />
        </div>
        </div>
        <div>
              <label className="block text-xs font-medium text-gray-700 mb-0.5">Approved By</label>
          <input
                className="w-full px-2.5 py-1.5 text-sm bg-white border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200 placeholder:text-slate-400"
            value={reportData.approved_by}
            onChange={e => onInputChange("approved_by", e.target.value)}
                placeholder="Approved By"
          />
        </div>
        <div>
              <label className="block text-xs font-medium text-gray-700 mb-0.5">Test Lead</label>
          <input
                className="w-full px-2.5 py-1.5 text-sm bg-white border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200 placeholder:text-slate-400"
            value={reportData.test_lead}
            onChange={e => onInputChange("test_lead", e.target.value)}
                placeholder="Test Lead"
          />
        </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-0.5">Version Description</label>
          <input
                className="w-full px-2.5 py-1.5 text-sm bg-white border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200 placeholder:text-slate-400"
            value={reportData.version_description}
            onChange={e => onInputChange("version_description", e.target.value)}
                placeholder="Version Description"
          />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentReferenceEditor; 