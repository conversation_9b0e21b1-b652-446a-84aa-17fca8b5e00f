{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\jira-old\\\\CTB-Frontend\\\\src\\\\components\\\\forms\\\\inputs\\\\RichTextArea.tsx\",\n  _s = $RefreshSig$();\nimport React, { useMemo, useCallback } from \"react\";\nimport JoditEditor from \"jodit-react\";\nimport { Controller, useFormContext } from \"react-hook-form\";\nimport \"jodit/es2021/jodit.min.css\";\n\n// Unique class prefix to prevent global style leaks\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EDITOR_CLASS_PREFIX = \"jodit-custom-editor\";\nconst RichTextArea = ({\n  name,\n  rules,\n  placeholder,\n  readOnly = false\n}) => {\n  _s();\n  const {\n    control\n  } = useFormContext();\n\n  // Styles for editor display (these will be converted to inline styles when saving)\n  const editorStyles = useMemo(() => `\n      .${EDITOR_CLASS_PREFIX} h1 { font-size: 2em; font-weight: bold; margin: 0.67em 0; }\n      .${EDITOR_CLASS_PREFIX} h2 { font-size: 1.5em; font-weight: bold; margin: 0.75em 0; }\n      .${EDITOR_CLASS_PREFIX} h3 { font-size: 1.17em; font-weight: bold; margin: 0.83em 0; }\n      .${EDITOR_CLASS_PREFIX} h4 { font-size: 1em; font-weight: bold; margin: 1.12em 0; }\n      .${EDITOR_CLASS_PREFIX} ul {list-style-type: disc; margin: 0; padding: 0 12px 8px;}\n      .${EDITOR_CLASS_PREFIX} ul ul {list-style-type: circle; padding-left: 16px;}\n      .${EDITOR_CLASS_PREFIX} ul ul ul {list-style-type: square; padding-left: 16px;}\n      .${EDITOR_CLASS_PREFIX} ol {list-style-type: decimal; padding-left: 20px; margin-top: 1em; margin-bottom: 1em;}\n      .${EDITOR_CLASS_PREFIX} ol ol {margin-top: 0; margin-bottom: 0; padding-left: 16px;}\n      .${EDITOR_CLASS_PREFIX} td {border: solid 1px;}\n      .${EDITOR_CLASS_PREFIX} table {border-collapse: collapse; width: 100%;}\n      .${EDITOR_CLASS_PREFIX} th {border: solid 1px; padding: 8px; background-color: #f2f2f2;}\n      .${EDITOR_CLASS_PREFIX} tr {border-bottom: solid 1px #ddd;}\n    \n      /* Enhanced styling for code blocks */\n      .${EDITOR_CLASS_PREFIX} pre {\n        display: inline-block;\n        min-width: 200px;\n        max-width: 100%;\n        width: auto;\n        background: linear-gradient(145deg, #1e293b, #0f172a);\n        color: #e2e8f0;\n        font-family: 'Fira Code', 'Consolas', monospace;\n        padding: 1rem;\n        margin: 1rem 0;\n        border-radius: 8px;\n        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);\n        overflow-x: auto;\n        position: relative;\n        border-left: 4px solid #3b82f6;\n      }\n    \n      /* Code block header with language indicator */\n      .${EDITOR_CLASS_PREFIX} pre::before {\n        content: \"code\";\n        position: absolute;\n        top: 0;\n        right: 10px;\n        background-color: #3b82f6;\n        color: white;\n        padding: 2px 8px;\n        font-size: 12px;\n        border-bottom-left-radius: 4px;\n        border-bottom-right-radius: 4px;\n        font-family: sans-serif;\n        opacity: 0.8;\n      }\n    \n      /* Line numbers effect */\n      .${EDITOR_CLASS_PREFIX} pre {\n        counter-reset: line;\n        padding-left: 3.8em;\n        line-height: 1.6;\n      }\n    \n      .${EDITOR_CLASS_PREFIX} pre code {\n        counter-increment: line;\n        position: relative;\n        display: block;\n        background-color: transparent;\n        padding: 0;\n        border-radius: 0;\n        font-size: 0.9em;\n      }\n    \n      .${EDITOR_CLASS_PREFIX} pre code::before {\n        content: counter(line);\n        position: absolute;\n        left: -3em;\n        width: 2.5em;\n        text-align: right;\n        color: #64748b;\n        padding-right: 0.5em;\n        border-right: 1px solid #475569;\n        user-select: none;\n      }\n    \n      /* Inline code styling */\n      .${EDITOR_CLASS_PREFIX} code:not(pre code) {\n        font-family: 'Fira Code', 'Consolas', monospace;\n        background: rgba(59, 130, 246, 0.1);\n        color: #3b82f6;\n        padding: 0.2em 0.4em;\n        border-radius: 3px;\n        font-size: 0.9em;\n        border: 1px solid rgba(59, 130, 246, 0.2);\n      }\n    \n      .${EDITOR_CLASS_PREFIX} blockquote {border-left: 5px solid #eee; padding: 10px 20px; margin: 0 0 20px;}\n    `, []);\n  const config = useMemo(() => ({\n    readonly: readOnly,\n    toolbar: true,\n    spellcheck: true,\n    language: \"en\",\n    theme: \"default\",\n    toolbarButtonSize: \"small\",\n    toolbarSticky: true,\n    toolbarAdaptive: true,\n    showCharsCounter: false,\n    showWordsCounter: false,\n    showXPathInStatusbar: false,\n    enterMode: \"p\",\n    defaultMode: 1,\n    indentMargin: 10,\n    allowTabNavigation: true,\n    tabNavigation: true,\n    askBeforePasteHTML: true,\n    askBeforePasteFromWord: true,\n    buttons: [\"bold\", \"italic\", \"underline\", \"strikethrough\", \"|\", \"outdent\", \"indent\", \"|\", \"font\", \"fontsize\", \"brush\", \"formatblock\", \"|\", \"image\", \"table\", \"link\", \"|\", \"align\", \"undo\", \"redo\", \"|\", \"find\", \"source\", \"fullsize\", \"preview\"],\n    uploader: {\n      insertImageAsBase64URI: true,\n      imagesExtensions: [\"jpg\", \"png\", \"jpeg\", \"gif\", \"webp\"],\n      processImageBeforeUpload: img => img\n    },\n    cleanHTML: {\n      allowTags: {\n        p: true,\n        br: true,\n        strong: true,\n        b: true,\n        em: true,\n        i: true,\n        u: true,\n        strike: true,\n        s: true,\n        ul: true,\n        ol: true,\n        li: true,\n        a: true,\n        img: true,\n        table: true,\n        tbody: true,\n        tr: true,\n        td: true,\n        th: true,\n        h1: true,\n        h2: true,\n        h3: true,\n        h4: true,\n        div: true,\n        span: true,\n        pre: true,\n        code: true,\n        blockquote: true\n      },\n      denyTags: {\n        script: true,\n        iframe: true,\n        form: true,\n        input: true,\n        style: true\n      },\n      fullyQualifiedLinks: true,\n      removeEmptyAttributes: true,\n      safeJavaScriptLink: true\n    },\n    // Updated events to scope styles properly and enhance content formatting\n    events: {\n      afterInit: editor => {\n        // Add the class to the editor container\n        editor.editor.classList.add(EDITOR_CLASS_PREFIX);\n\n        // Create style element but only scope it to the editor\n        const styleTag = editor.createInside.element(\"style\");\n        styleTag.innerHTML = editorStyles;\n        editor.container.appendChild(styleTag);\n\n        // Set placeholder if provided\n        if (placeholder) {\n          editor.editor.setAttribute(\"data-placeholder\", placeholder);\n        }\n\n        // Add custom keyboard shortcut for code block\n        editor.events.on(\"keydown\", event => {\n          // Ctrl+Shift+C or Cmd+Shift+C for code block\n          if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === \"c\") {\n            event.preventDefault();\n            const codeBlock = `<pre><code>// Your code here</code></pre>`;\n            editor.selection.insertHTML(codeBlock);\n            return false;\n          }\n          return true;\n        });\n      },\n      afterPreview: editor => {\n        setTimeout(() => {\n          // Find the preview dialog\n          const dialog = document.querySelector(\".jodit-dialog__content\");\n          if (dialog) {\n            // Add our class to the preview content\n            const previewContent = dialog.querySelector(\".jodit-preview-content\");\n            if (previewContent) {\n              previewContent.classList.add(EDITOR_CLASS_PREFIX);\n\n              // Format code blocks in preview\n              formatCodeBlocks(previewContent);\n            }\n\n            // Add scoped styles only if not already added\n            if (!dialog.querySelector(`style[data-${EDITOR_CLASS_PREFIX}-style]`)) {\n              const style = document.createElement(\"style\");\n              style.setAttribute(`data-${EDITOR_CLASS_PREFIX}-style`, \"true\");\n              style.innerHTML = editorStyles;\n              dialog.appendChild(style);\n            }\n          }\n        }, 0);\n      },\n      // Format content when inserted\n      change: (newValue, oldValue) => {\n        if (newValue && newValue !== oldValue) {\n          // This is a light formatting to handle changes during editing\n          // The full formatting will happen on blur\n        }\n      }\n    },\n    height: 350,\n    minHeight: 200,\n    maxHeight: 800,\n    width: \"auto\",\n    // Add class to wrapper for better CSS scoping\n    className: EDITOR_CLASS_PREFIX + \"-wrapper\"\n  }), [readOnly, placeholder, editorStyles]);\n\n  // Helper function to format code blocks\n  const formatCodeBlocks = container => {\n    container.querySelectorAll(\"pre\").forEach(pre => {\n      // Set display to inline-block for auto-width\n      pre.style.display = \"inline-block\";\n      pre.style.minWidth = \"200px\";\n      pre.style.maxWidth = \"100%\";\n      pre.style.width = \"auto\";\n      pre.style.background = \"linear-gradient(145deg, #1e293b, #0f172a)\";\n      pre.style.color = \"#e2e8f0\";\n      pre.style.fontFamily = \"'Fira Code', 'Consolas', monospace\";\n      pre.style.padding = \"1rem\";\n      pre.style.margin = \"1rem 0\";\n      pre.style.borderRadius = \"8px\";\n      pre.style.boxShadow = \"0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08)\";\n      pre.style.overflowX = \"auto\";\n      pre.style.position = \"relative\";\n      pre.style.borderLeft = \"4px solid #3b82f6\";\n      pre.style.counterReset = \"line\";\n      pre.style.paddingLeft = \"3.8em\";\n      pre.style.lineHeight = \"1.6\";\n\n      // Process each code line\n      const code = pre.querySelector(\"code\");\n      if (code) {\n        code.style.backgroundColor = \"transparent\";\n        code.style.padding = \"0\";\n        code.style.borderRadius = \"0\";\n        code.style.fontSize = \"0.9em\";\n\n        // Split code into lines and recreate with line numbers\n        if (!code.hasAttribute(\"data-processed\")) {\n          const content = code.innerHTML;\n          const lines = content.split(\"\\n\");\n          let newContent = \"\";\n          lines.forEach(line => {\n            newContent += `<span class=\"code-line\">${line}</span>\\n`;\n          });\n          code.innerHTML = newContent;\n          code.setAttribute(\"data-processed\", \"true\");\n        }\n      }\n    });\n\n    // Style inline code elements (not inside pre)\n    container.querySelectorAll(\"code:not(pre code)\").forEach(code => {\n      const codeElement = code;\n      codeElement.style.fontFamily = \"'Fira Code', 'Consolas', monospace\";\n      codeElement.style.background = \"rgba(59, 130, 246, 0.1)\";\n      codeElement.style.color = \"#3b82f6\";\n      codeElement.style.padding = \"0.2em 0.4em\";\n      codeElement.style.borderRadius = \"3px\";\n      codeElement.style.fontSize = \"0.9em\";\n      codeElement.style.border = \"1px solid rgba(59, 130, 246, 0.2)\";\n    });\n  };\n  const processContent = useCallback(content => {\n    if (!content || content === \"<p><br></p>\" || content.trim() === \"\") return \"\";\n    let processed = content.replace(/\\t/g, \"  \");\n    const tempDiv = document.createElement(\"div\");\n    tempDiv.innerHTML = processed;\n\n    // Apply inline styles to ensure formatting is preserved when rendered elsewhere\n    // Headings\n    tempDiv.querySelectorAll(\"h1\").forEach(h1 => {\n      h1.style.fontSize = \"2em\";\n      h1.style.fontWeight = \"bold\";\n      h1.style.margin = \"0.67em 0\";\n    });\n    tempDiv.querySelectorAll(\"h2\").forEach(h2 => {\n      h2.style.fontSize = \"1.5em\";\n      h2.style.fontWeight = \"bold\";\n      h2.style.margin = \"0.75em 0\";\n    });\n    tempDiv.querySelectorAll(\"h3\").forEach(h3 => {\n      h3.style.fontSize = \"1.17em\";\n      h3.style.fontWeight = \"bold\";\n      h3.style.margin = \"0.83em 0\";\n    });\n    tempDiv.querySelectorAll(\"h4\").forEach(h4 => {\n      h4.style.fontSize = \"1em\";\n      h4.style.fontWeight = \"bold\";\n      h4.style.margin = \"1.12em 0\";\n    });\n\n    // Lists - Use minimal padding for better PDF rendering\n    tempDiv.querySelectorAll(\"ul\").forEach(ul => {\n      ul.style.listStyleType = \"disc\";\n      ul.style.margin = \"0\";\n      ul.style.padding = \"0 12px 8px\"; // Reduced from 20px to 12px\n    });\n    tempDiv.querySelectorAll(\"ul ul\").forEach(ulul => {\n      ulul.style.listStyleType = \"circle\";\n      ulul.style.paddingLeft = \"16px\"; // Nested lists get less padding\n    });\n    tempDiv.querySelectorAll(\"ul ul ul\").forEach(ululul => {\n      ululul.style.listStyleType = \"square\";\n      ululul.style.paddingLeft = \"16px\";\n    });\n    tempDiv.querySelectorAll(\"ol\").forEach(ol => {\n      ol.style.listStyleType = \"decimal\";\n      ol.style.paddingLeft = \"20px\"; // Reduced from 40px to 20px\n      ol.style.marginTop = \"1em\";\n      ol.style.marginBottom = \"1em\";\n    });\n    tempDiv.querySelectorAll(\"ol ol\").forEach(olol => {\n      olol.style.marginTop = \"0\";\n      olol.style.marginBottom = \"0\";\n      olol.style.paddingLeft = \"16px\"; // Nested ordered lists\n    });\n\n    // Tables\n    tempDiv.querySelectorAll(\"td\").forEach(td => {\n      td.style.border = \"solid 1px\";\n    });\n\n    // Additional elements that may need inline styling\n    tempDiv.querySelectorAll(\"table\").forEach(table => {\n      table.style.borderCollapse = \"collapse\";\n      table.style.width = \"100%\";\n    });\n    tempDiv.querySelectorAll(\"th\").forEach(th => {\n      th.style.border = \"solid 1px\";\n      th.style.padding = \"8px\";\n      th.style.backgroundColor = \"#f2f2f2\";\n    });\n    tempDiv.querySelectorAll(\"tr\").forEach(tr => {\n      tr.style.borderBottom = \"solid 1px #ddd\";\n    });\n\n    // Format code blocks with our enhanced styling\n    tempDiv.querySelectorAll(\"pre\").forEach(pre => {\n      pre.style.display = \"inline-block\";\n      pre.style.minWidth = \"200px\";\n      pre.style.maxWidth = \"100%\";\n      pre.style.width = \"auto\";\n      pre.style.background = \"linear-gradient(145deg, #1e293b, #0f172a)\";\n      pre.style.color = \"#e2e8f0\";\n      pre.style.fontFamily = \"'Fira Code', 'Consolas', monospace\";\n      pre.style.padding = \"1rem\";\n      pre.style.margin = \"1rem 0\";\n      pre.style.borderRadius = \"8px\";\n      pre.style.boxShadow = \"0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08)\";\n      pre.style.overflowX = \"auto\";\n      pre.style.position = \"relative\";\n      pre.style.borderLeft = \"4px solid #3b82f6\";\n\n      // Try to detect language from content to add language tag\n      const code = pre.querySelector(\"code\");\n      if (code) {\n        const codeContent = code.textContent || \"\";\n        let language = \"code\";\n\n        // Basic language detection\n        if (codeContent.includes(\"function\") || codeContent.includes(\"const \") || codeContent.includes(\"let \")) {\n          language = \"javascript\";\n        } else if (codeContent.includes(\"def \") || codeContent.includes(\"import \") && codeContent.includes(\":\")) {\n          language = \"python\";\n        } else if (codeContent.includes(\"class \") && codeContent.includes(\"{\")) {\n          language = \"java\";\n        } else if (codeContent.includes(\"#include\")) {\n          language = \"c++\";\n        }\n\n        // Add pseudo-element with content via data attribute\n        pre.setAttribute(\"data-language\", language);\n\n        // Apply inline code styling\n        code.style.backgroundColor = \"transparent\";\n        code.style.padding = \"0\";\n        code.style.borderRadius = \"0\";\n      }\n    });\n    tempDiv.querySelectorAll(\"code:not(pre code)\").forEach(code => {\n      const codeElement = code;\n      codeElement.style.fontFamily = \"'Fira Code', 'Consolas', monospace\";\n      codeElement.style.background = \"rgba(59, 130, 246, 0.1)\";\n      codeElement.style.color = \"#3b82f6\";\n      codeElement.style.padding = \"0.2em 0.4em\";\n      codeElement.style.borderRadius = \"3px\";\n      codeElement.style.fontSize = \"0.9em\";\n      codeElement.style.border = \"1px solid rgba(59, 130, 246, 0.2)\";\n    });\n    tempDiv.querySelectorAll(\"blockquote\").forEach(blockquote => {\n      blockquote.style.borderLeft = \"5px solid #eee\";\n      blockquote.style.padding = \"10px 20px\";\n      blockquote.style.margin = \"0 0 20px\";\n    });\n    return tempDiv.innerHTML;\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Controller, {\n    name: name,\n    control: control,\n    rules: rules,\n    render: ({\n      field: {\n        value,\n        onChange,\n        onBlur\n      },\n      fieldState: {\n        error\n      }\n    }) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `jodit-wrapper ${EDITOR_CLASS_PREFIX}-container`,\n      children: [/*#__PURE__*/_jsxDEV(\"style\", {\n        children: `\n              .${EDITOR_CLASS_PREFIX}-container .jodit-container {\n                border-color: ${error ? \"#ff3860\" : \"#dbdbdb\"};\n              }\n              .${EDITOR_CLASS_PREFIX}-container .jodit-error-message {\n                color: #ff3860;\n                font-size: 0.85rem;\n                margin-top: 4px;\n              }\n            \n              /* Code block styling in the editor */\n              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg pre {\n                display: inline-block;\n                min-width: 200px;\n                max-width: 100%;\n                width: auto;\n                background: linear-gradient(145deg, #1e293b, #0f172a);\n                color: #e2e8f0;\n                font-family: 'Fira Code', 'Consolas', monospace;\n                padding: 1rem;\n                margin: 1rem 0;\n                border-radius: 8px;\n                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);\n                overflow-x: auto;\n                position: relative;\n                border-left: 4px solid #3b82f6;\n              }\n            \n              /* Language indicator */\n              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg pre[data-language]::before {\n                content: attr(data-language);\n                position: absolute;\n                top: 0;\n                right: 10px;\n                background-color: #3b82f6;\n                color: white;\n                padding: 2px 8px;\n                font-size: 12px;\n                border-bottom-left-radius: 4px;\n                border-bottom-right-radius: 4px;\n                font-family: sans-serif;\n                opacity: 0.8;\n              }\n            \n              /* Line numbers effect */\n              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg pre {\n                counter-reset: line;\n                padding-left: 3.8em;\n                line-height: 1.6;\n              }\n            \n              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg pre code {\n                counter-increment: line;\n              }\n            \n              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg pre code .code-line {\n                position: relative;\n                display: block;\n              }\n            \n              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg pre code .code-line::before {\n                content: counter(line);\n                position: absolute;\n                left: -2.8em;\n                width: 2em;\n                text-align: right;\n                color: #64748b;\n                user-select: none;\n              }\n            \n              /* Inline code styling */\n              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg code:not(pre code) {\n                font-family: 'Fira Code', 'Consolas', monospace;\n                background: rgba(59, 130, 246, 0.1);\n                color: #3b82f6;\n                padding: 0.2em 0.4em;\n                border-radius: 3px;\n                font-size: 0.9em;\n                border: 1px solid rgba(59, 130, 246, 0.2);\n              }\n            \n              /* Custom code block button styling */\n              .jodit-toolbar-button_insertCodeBlock button {\n                position: relative;\n              }\n            \n              /* Font preloading (optional) */\n              @font-face {\n                font-family: 'Fira Code';\n                src: url('https://cdnjs.cloudflare.com/ajax/libs/firacode/6.2.0/woff2/FiraCode-Regular.woff2') format('woff2');\n                font-style: normal;\n                font-weight: 400;\n                font-display: swap;\n              }\n            `\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(JoditEditor, {\n        value: value || \"\"\n        //@ts-ignore\n        ,\n        config: config,\n        onChange: content => {\n          onChange(content);\n        },\n        onBlur: () => {\n          if (value) {\n            const cleaned = processContent(value);\n            onChange(cleaned);\n          }\n          onBlur();\n        },\n        className: error ? \"jodit-container-error\" : \"\",\n        ref: editor => {\n          if (editor !== null && editor !== void 0 && editor.container) {\n            const textarea = editor.container.querySelector(\"textarea\");\n            if (textarea) {\n              textarea.setAttribute(\"data-prevent-scroll\", \"true\");\n            }\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 620,\n        columnNumber: 11\n      }, this), error && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"jodit-error-message\",\n        children: error.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 644,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 522,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 514,\n    columnNumber: 5\n  }, this);\n};\n_s(RichTextArea, \"s6/ZpAkS0PEN5AopB6YEvmdyO2k=\", false, function () {\n  return [useFormContext];\n});\n_c = RichTextArea;\nexport default RichTextArea;\nvar _c;\n$RefreshReg$(_c, \"RichTextArea\");", "map": {"version": 3, "names": ["React", "useMemo", "useCallback", "JoditEditor", "Controller", "useFormContext", "jsxDEV", "_jsxDEV", "EDITOR_CLASS_PREFIX", "RichTextArea", "name", "rules", "placeholder", "readOnly", "_s", "control", "editor<PERSON><PERSON><PERSON>", "config", "readonly", "toolbar", "spellcheck", "language", "theme", "toolbarButtonSize", "toolbarSticky", "toolbarAdaptive", "showCharsCounter", "showWordsCounter", "showXPathInStatusbar", "enterMode", "defaultMode", "in<PERSON><PERSON><PERSON><PERSON>", "allowTabNavigation", "tabNavigation", "askBeforePasteHTML", "askBeforePasteFromWord", "buttons", "uploader", "insertImageAsBase64URI", "imagesExtensions", "processImageBeforeUpload", "img", "cleanHTML", "allowTags", "p", "br", "strong", "b", "em", "i", "u", "strike", "s", "ul", "ol", "li", "a", "table", "tbody", "tr", "td", "th", "h1", "h2", "h3", "h4", "div", "span", "pre", "code", "blockquote", "denyTags", "script", "iframe", "form", "input", "style", "fullyQualifiedLinks", "removeEmptyAttributes", "safeJavaScriptLink", "events", "afterInit", "editor", "classList", "add", "styleTag", "createInside", "element", "innerHTML", "container", "append<PERSON><PERSON><PERSON>", "setAttribute", "on", "event", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "key", "preventDefault", "codeBlock", "selection", "insertHTML", "afterPreview", "setTimeout", "dialog", "document", "querySelector", "previewContent", "formatCodeBlocks", "createElement", "change", "newValue", "oldValue", "height", "minHeight", "maxHeight", "width", "className", "querySelectorAll", "for<PERSON>ach", "display", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "background", "color", "fontFamily", "padding", "margin", "borderRadius", "boxShadow", "overflowX", "position", "borderLeft", "counterReset", "paddingLeft", "lineHeight", "backgroundColor", "fontSize", "hasAttribute", "content", "lines", "split", "newContent", "line", "codeElement", "border", "processContent", "trim", "processed", "replace", "tempDiv", "fontWeight", "listStyleType", "ulul", "ululul", "marginTop", "marginBottom", "olol", "borderCollapse", "borderBottom", "codeContent", "textContent", "includes", "render", "field", "value", "onChange", "onBlur", "fieldState", "error", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "cleaned", "ref", "textarea", "message", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/jira-old/CTB-Frontend/src/components/forms/inputs/RichTextArea.tsx"], "sourcesContent": ["import React, { useMemo, useCallback } from \"react\";\r\nimport <PERSON><PERSON><PERSON><PERSON><PERSON> from \"jodit-react\";\r\nimport { Controller, useFormContext } from \"react-hook-form\";\r\nimport { InputBaseParams } from \"../Form\";\r\nimport { Jodit } from \"jodit\";\r\nimport \"jodit/es2021/jodit.min.css\";\r\n\r\n// Unique class prefix to prevent global style leaks\r\nconst EDITOR_CLASS_PREFIX = \"jodit-custom-editor\";\r\n\r\nconst RichTextArea = ({\r\n  name,\r\n  rules,\r\n  placeholder,\r\n  readOnly = false\r\n}: InputBaseParams & {\r\n  placeholder?: string;\r\n  readOnly?: boolean;\r\n}) => {\r\n  const { control } = useFormContext();\r\n\r\n  // Styles for editor display (these will be converted to inline styles when saving)\r\n  const editorStyles = useMemo(\r\n    () => `\r\n      .${EDITOR_CLASS_PREFIX} h1 { font-size: 2em; font-weight: bold; margin: 0.67em 0; }\r\n      .${EDITOR_CLASS_PREFIX} h2 { font-size: 1.5em; font-weight: bold; margin: 0.75em 0; }\r\n      .${EDITOR_CLASS_PREFIX} h3 { font-size: 1.17em; font-weight: bold; margin: 0.83em 0; }\r\n      .${EDITOR_CLASS_PREFIX} h4 { font-size: 1em; font-weight: bold; margin: 1.12em 0; }\r\n      .${EDITOR_CLASS_PREFIX} ul {list-style-type: disc; margin: 0; padding: 0 12px 8px;}\r\n      .${EDITOR_CLASS_PREFIX} ul ul {list-style-type: circle; padding-left: 16px;}\r\n      .${EDITOR_CLASS_PREFIX} ul ul ul {list-style-type: square; padding-left: 16px;}\r\n      .${EDITOR_CLASS_PREFIX} ol {list-style-type: decimal; padding-left: 20px; margin-top: 1em; margin-bottom: 1em;}\r\n      .${EDITOR_CLASS_PREFIX} ol ol {margin-top: 0; margin-bottom: 0; padding-left: 16px;}\r\n      .${EDITOR_CLASS_PREFIX} td {border: solid 1px;}\r\n      .${EDITOR_CLASS_PREFIX} table {border-collapse: collapse; width: 100%;}\r\n      .${EDITOR_CLASS_PREFIX} th {border: solid 1px; padding: 8px; background-color: #f2f2f2;}\r\n      .${EDITOR_CLASS_PREFIX} tr {border-bottom: solid 1px #ddd;}\r\n    \r\n      /* Enhanced styling for code blocks */\r\n      .${EDITOR_CLASS_PREFIX} pre {\r\n        display: inline-block;\r\n        min-width: 200px;\r\n        max-width: 100%;\r\n        width: auto;\r\n        background: linear-gradient(145deg, #1e293b, #0f172a);\r\n        color: #e2e8f0;\r\n        font-family: 'Fira Code', 'Consolas', monospace;\r\n        padding: 1rem;\r\n        margin: 1rem 0;\r\n        border-radius: 8px;\r\n        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);\r\n        overflow-x: auto;\r\n        position: relative;\r\n        border-left: 4px solid #3b82f6;\r\n      }\r\n    \r\n      /* Code block header with language indicator */\r\n      .${EDITOR_CLASS_PREFIX} pre::before {\r\n        content: \"code\";\r\n        position: absolute;\r\n        top: 0;\r\n        right: 10px;\r\n        background-color: #3b82f6;\r\n        color: white;\r\n        padding: 2px 8px;\r\n        font-size: 12px;\r\n        border-bottom-left-radius: 4px;\r\n        border-bottom-right-radius: 4px;\r\n        font-family: sans-serif;\r\n        opacity: 0.8;\r\n      }\r\n    \r\n      /* Line numbers effect */\r\n      .${EDITOR_CLASS_PREFIX} pre {\r\n        counter-reset: line;\r\n        padding-left: 3.8em;\r\n        line-height: 1.6;\r\n      }\r\n    \r\n      .${EDITOR_CLASS_PREFIX} pre code {\r\n        counter-increment: line;\r\n        position: relative;\r\n        display: block;\r\n        background-color: transparent;\r\n        padding: 0;\r\n        border-radius: 0;\r\n        font-size: 0.9em;\r\n      }\r\n    \r\n      .${EDITOR_CLASS_PREFIX} pre code::before {\r\n        content: counter(line);\r\n        position: absolute;\r\n        left: -3em;\r\n        width: 2.5em;\r\n        text-align: right;\r\n        color: #64748b;\r\n        padding-right: 0.5em;\r\n        border-right: 1px solid #475569;\r\n        user-select: none;\r\n      }\r\n    \r\n      /* Inline code styling */\r\n      .${EDITOR_CLASS_PREFIX} code:not(pre code) {\r\n        font-family: 'Fira Code', 'Consolas', monospace;\r\n        background: rgba(59, 130, 246, 0.1);\r\n        color: #3b82f6;\r\n        padding: 0.2em 0.4em;\r\n        border-radius: 3px;\r\n        font-size: 0.9em;\r\n        border: 1px solid rgba(59, 130, 246, 0.2);\r\n      }\r\n    \r\n      .${EDITOR_CLASS_PREFIX} blockquote {border-left: 5px solid #eee; padding: 10px 20px; margin: 0 0 20px;}\r\n    `,\r\n    []\r\n  );\r\n\r\n  const config = useMemo(\r\n    () => ({\r\n      readonly: readOnly,\r\n      toolbar: true,\r\n      spellcheck: true,\r\n      language: \"en\",\r\n      theme: \"default\",\r\n      toolbarButtonSize: \"small\" as const,\r\n      toolbarSticky: true,\r\n      toolbarAdaptive: true,\r\n      showCharsCounter: false,\r\n      showWordsCounter: false,\r\n      showXPathInStatusbar: false,\r\n      enterMode: \"p\",\r\n      defaultMode: 1,\r\n      indentMargin: 10,\r\n      allowTabNavigation: true,\r\n      tabNavigation: true,\r\n      askBeforePasteHTML: true,\r\n      askBeforePasteFromWord: true,\r\n      buttons: [\r\n        \"bold\",\r\n        \"italic\",\r\n        \"underline\",\r\n        \"strikethrough\",\r\n        \"|\",\r\n        \"outdent\",\r\n        \"indent\",\r\n        \"|\",\r\n        \"font\",\r\n        \"fontsize\",\r\n        \"brush\",\r\n        \"formatblock\",\r\n        \"|\",\r\n        \"image\",\r\n        \"table\",\r\n        \"link\",\r\n        \"|\",\r\n        \"align\",\r\n        \"undo\",\r\n        \"redo\",\r\n        \"|\",\r\n        \"find\",\r\n        \"source\",\r\n        \"fullsize\",\r\n        \"preview\"\r\n      ],\r\n      uploader: {\r\n        insertImageAsBase64URI: true,\r\n        imagesExtensions: [\"jpg\", \"png\", \"jpeg\", \"gif\", \"webp\"],\r\n        processImageBeforeUpload: (img: File) => img\r\n      },\r\n      cleanHTML: {\r\n        allowTags: {\r\n          p: true,\r\n          br: true,\r\n          strong: true,\r\n          b: true,\r\n          em: true,\r\n          i: true,\r\n          u: true,\r\n          strike: true,\r\n          s: true,\r\n          ul: true,\r\n          ol: true,\r\n          li: true,\r\n          a: true,\r\n          img: true,\r\n          table: true,\r\n          tbody: true,\r\n          tr: true,\r\n          td: true,\r\n          th: true,\r\n          h1: true,\r\n          h2: true,\r\n          h3: true,\r\n          h4: true,\r\n          div: true,\r\n          span: true,\r\n          pre: true,\r\n          code: true,\r\n          blockquote: true\r\n        },\r\n        denyTags: {\r\n          script: true,\r\n          iframe: true,\r\n          form: true,\r\n          input: true,\r\n          style: true\r\n        },\r\n        fullyQualifiedLinks: true,\r\n        removeEmptyAttributes: true,\r\n        safeJavaScriptLink: true\r\n      },\r\n      // Updated events to scope styles properly and enhance content formatting\r\n      events: {\r\n        afterInit: (editor: Jodit) => {\r\n          // Add the class to the editor container\r\n          editor.editor.classList.add(EDITOR_CLASS_PREFIX);\r\n\r\n          // Create style element but only scope it to the editor\r\n          const styleTag = editor.createInside.element(\"style\");\r\n          styleTag.innerHTML = editorStyles;\r\n          editor.container.appendChild(styleTag);\r\n\r\n          // Set placeholder if provided\r\n          if (placeholder) {\r\n            editor.editor.setAttribute(\"data-placeholder\", placeholder);\r\n          }\r\n\r\n          // Add custom keyboard shortcut for code block\r\n          editor.events.on(\"keydown\", (event: KeyboardEvent) => {\r\n            // Ctrl+Shift+C or Cmd+Shift+C for code block\r\n            if (\r\n              (event.ctrlKey || event.metaKey) &&\r\n              event.shiftKey &&\r\n              event.key === \"c\"\r\n            ) {\r\n              event.preventDefault();\r\n              const codeBlock = `<pre><code>// Your code here</code></pre>`;\r\n              editor.selection.insertHTML(codeBlock);\r\n              return false;\r\n            }\r\n            return true;\r\n          });\r\n        },\r\n        afterPreview: (editor: Jodit) => {\r\n          setTimeout(() => {\r\n            // Find the preview dialog\r\n            const dialog = document.querySelector(\".jodit-dialog__content\");\r\n            if (dialog) {\r\n              // Add our class to the preview content\r\n              const previewContent = dialog.querySelector(\r\n                \".jodit-preview-content\"\r\n              );\r\n              if (previewContent) {\r\n                previewContent.classList.add(EDITOR_CLASS_PREFIX);\r\n\r\n                // Format code blocks in preview\r\n                formatCodeBlocks(previewContent as HTMLElement);\r\n              }\r\n\r\n              // Add scoped styles only if not already added\r\n              if (\r\n                !dialog.querySelector(\r\n                  `style[data-${EDITOR_CLASS_PREFIX}-style]`\r\n                )\r\n              ) {\r\n                const style = document.createElement(\"style\");\r\n                style.setAttribute(`data-${EDITOR_CLASS_PREFIX}-style`, \"true\");\r\n                style.innerHTML = editorStyles;\r\n                dialog.appendChild(style);\r\n              }\r\n            }\r\n          }, 0);\r\n        },\r\n        // Format content when inserted\r\n        change: (newValue: string, oldValue: string) => {\r\n          if (newValue && newValue !== oldValue) {\r\n            // This is a light formatting to handle changes during editing\r\n            // The full formatting will happen on blur\r\n          }\r\n        }\r\n      },\r\n      height: 350,\r\n      minHeight: 200,\r\n      maxHeight: 800,\r\n      width: \"auto\",\r\n      // Add class to wrapper for better CSS scoping\r\n      className: EDITOR_CLASS_PREFIX + \"-wrapper\"\r\n    }),\r\n    [readOnly, placeholder, editorStyles]\r\n  );\r\n\r\n  // Helper function to format code blocks\r\n  const formatCodeBlocks = (container: HTMLElement) => {\r\n    container.querySelectorAll(\"pre\").forEach(pre => {\r\n      // Set display to inline-block for auto-width\r\n      pre.style.display = \"inline-block\";\r\n      pre.style.minWidth = \"200px\";\r\n      pre.style.maxWidth = \"100%\";\r\n      pre.style.width = \"auto\";\r\n      pre.style.background = \"linear-gradient(145deg, #1e293b, #0f172a)\";\r\n      pre.style.color = \"#e2e8f0\";\r\n      pre.style.fontFamily = \"'Fira Code', 'Consolas', monospace\";\r\n      pre.style.padding = \"1rem\";\r\n      pre.style.margin = \"1rem 0\";\r\n      pre.style.borderRadius = \"8px\";\r\n      pre.style.boxShadow =\r\n        \"0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08)\";\r\n      pre.style.overflowX = \"auto\";\r\n      pre.style.position = \"relative\";\r\n      pre.style.borderLeft = \"4px solid #3b82f6\";\r\n      pre.style.counterReset = \"line\";\r\n      pre.style.paddingLeft = \"3.8em\";\r\n      pre.style.lineHeight = \"1.6\";\r\n\r\n      // Process each code line\r\n      const code = pre.querySelector(\"code\");\r\n      if (code) {\r\n        code.style.backgroundColor = \"transparent\";\r\n        code.style.padding = \"0\";\r\n        code.style.borderRadius = \"0\";\r\n        code.style.fontSize = \"0.9em\";\r\n\r\n        // Split code into lines and recreate with line numbers\r\n        if (!code.hasAttribute(\"data-processed\")) {\r\n          const content = code.innerHTML;\r\n          const lines = content.split(\"\\n\");\r\n          let newContent = \"\";\r\n\r\n          lines.forEach(line => {\r\n            newContent += `<span class=\"code-line\">${line}</span>\\n`;\r\n          });\r\n\r\n          code.innerHTML = newContent;\r\n          code.setAttribute(\"data-processed\", \"true\");\r\n        }\r\n      }\r\n    });\r\n\r\n    // Style inline code elements (not inside pre)\r\n    container.querySelectorAll(\"code:not(pre code)\").forEach(code => {\r\n      const codeElement = code as HTMLElement;\r\n      codeElement.style.fontFamily = \"'Fira Code', 'Consolas', monospace\";\r\n      codeElement.style.background = \"rgba(59, 130, 246, 0.1)\";\r\n      codeElement.style.color = \"#3b82f6\";\r\n      codeElement.style.padding = \"0.2em 0.4em\";\r\n      codeElement.style.borderRadius = \"3px\";\r\n      codeElement.style.fontSize = \"0.9em\";\r\n      codeElement.style.border = \"1px solid rgba(59, 130, 246, 0.2)\";\r\n    });\r\n  };\r\n\r\n  const processContent = useCallback((content: string) => {\r\n    if (!content || content === \"<p><br></p>\" || content.trim() === \"\")\r\n      return \"\";\r\n\r\n    let processed = content.replace(/\\t/g, \"  \");\r\n\r\n    const tempDiv = document.createElement(\"div\");\r\n    tempDiv.innerHTML = processed;\r\n\r\n    // Apply inline styles to ensure formatting is preserved when rendered elsewhere\r\n    // Headings\r\n    tempDiv.querySelectorAll(\"h1\").forEach(h1 => {\r\n      h1.style.fontSize = \"2em\";\r\n      h1.style.fontWeight = \"bold\";\r\n      h1.style.margin = \"0.67em 0\";\r\n    });\r\n\r\n    tempDiv.querySelectorAll(\"h2\").forEach(h2 => {\r\n      h2.style.fontSize = \"1.5em\";\r\n      h2.style.fontWeight = \"bold\";\r\n      h2.style.margin = \"0.75em 0\";\r\n    });\r\n\r\n    tempDiv.querySelectorAll(\"h3\").forEach(h3 => {\r\n      h3.style.fontSize = \"1.17em\";\r\n      h3.style.fontWeight = \"bold\";\r\n      h3.style.margin = \"0.83em 0\";\r\n    });\r\n\r\n    tempDiv.querySelectorAll(\"h4\").forEach(h4 => {\r\n      h4.style.fontSize = \"1em\";\r\n      h4.style.fontWeight = \"bold\";\r\n      h4.style.margin = \"1.12em 0\";\r\n    });\r\n\r\n    // Lists - Use minimal padding for better PDF rendering\r\n    tempDiv.querySelectorAll(\"ul\").forEach(ul => {\r\n      ul.style.listStyleType = \"disc\";\r\n      ul.style.margin = \"0\";\r\n      ul.style.padding = \"0 12px 8px\"; // Reduced from 20px to 12px\r\n    });\r\n\r\n    tempDiv.querySelectorAll(\"ul ul\").forEach(ulul => {\r\n      (ulul as HTMLElement).style.listStyleType = \"circle\";\r\n      (ulul as HTMLElement).style.paddingLeft = \"16px\"; // Nested lists get less padding\r\n    });\r\n\r\n    tempDiv.querySelectorAll(\"ul ul ul\").forEach(ululul => {\r\n      (ululul as HTMLElement).style.listStyleType = \"square\";\r\n      (ululul as HTMLElement).style.paddingLeft = \"16px\";\r\n    });\r\n\r\n    tempDiv.querySelectorAll(\"ol\").forEach(ol => {\r\n      ol.style.listStyleType = \"decimal\";\r\n      ol.style.paddingLeft = \"20px\"; // Reduced from 40px to 20px\r\n      ol.style.marginTop = \"1em\";\r\n      ol.style.marginBottom = \"1em\";\r\n    });\r\n\r\n    tempDiv.querySelectorAll(\"ol ol\").forEach(olol => {\r\n      (olol as HTMLElement).style.marginTop = \"0\";\r\n      (olol as HTMLElement).style.marginBottom = \"0\";\r\n      (olol as HTMLElement).style.paddingLeft = \"16px\"; // Nested ordered lists\r\n    });\r\n\r\n    // Tables\r\n    tempDiv.querySelectorAll(\"td\").forEach(td => {\r\n      td.style.border = \"solid 1px\";\r\n    });\r\n\r\n    // Additional elements that may need inline styling\r\n    tempDiv.querySelectorAll(\"table\").forEach(table => {\r\n      table.style.borderCollapse = \"collapse\";\r\n      table.style.width = \"100%\";\r\n    });\r\n\r\n    tempDiv.querySelectorAll(\"th\").forEach(th => {\r\n      th.style.border = \"solid 1px\";\r\n      th.style.padding = \"8px\";\r\n      th.style.backgroundColor = \"#f2f2f2\";\r\n    });\r\n\r\n    tempDiv.querySelectorAll(\"tr\").forEach(tr => {\r\n      tr.style.borderBottom = \"solid 1px #ddd\";\r\n    });\r\n\r\n    // Format code blocks with our enhanced styling\r\n    tempDiv.querySelectorAll(\"pre\").forEach(pre => {\r\n      pre.style.display = \"inline-block\";\r\n      pre.style.minWidth = \"200px\";\r\n      pre.style.maxWidth = \"100%\";\r\n      pre.style.width = \"auto\";\r\n      pre.style.background = \"linear-gradient(145deg, #1e293b, #0f172a)\";\r\n      pre.style.color = \"#e2e8f0\";\r\n      pre.style.fontFamily = \"'Fira Code', 'Consolas', monospace\";\r\n      pre.style.padding = \"1rem\";\r\n      pre.style.margin = \"1rem 0\";\r\n      pre.style.borderRadius = \"8px\";\r\n      pre.style.boxShadow =\r\n        \"0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08)\";\r\n      pre.style.overflowX = \"auto\";\r\n      pre.style.position = \"relative\";\r\n      pre.style.borderLeft = \"4px solid #3b82f6\";\r\n\r\n      // Try to detect language from content to add language tag\r\n      const code = pre.querySelector(\"code\");\r\n      if (code) {\r\n        const codeContent = code.textContent || \"\";\r\n        let language = \"code\";\r\n\r\n        // Basic language detection\r\n        if (\r\n          codeContent.includes(\"function\") ||\r\n          codeContent.includes(\"const \") ||\r\n          codeContent.includes(\"let \")\r\n        ) {\r\n          language = \"javascript\";\r\n        } else if (\r\n          codeContent.includes(\"def \") ||\r\n          (codeContent.includes(\"import \") && codeContent.includes(\":\"))\r\n        ) {\r\n          language = \"python\";\r\n        } else if (\r\n          codeContent.includes(\"class \") &&\r\n          codeContent.includes(\"{\")\r\n        ) {\r\n          language = \"java\";\r\n        } else if (codeContent.includes(\"#include\")) {\r\n          language = \"c++\";\r\n        }\r\n\r\n        // Add pseudo-element with content via data attribute\r\n        pre.setAttribute(\"data-language\", language);\r\n\r\n        // Apply inline code styling\r\n        code.style.backgroundColor = \"transparent\";\r\n        code.style.padding = \"0\";\r\n        code.style.borderRadius = \"0\";\r\n      }\r\n    });\r\n\r\n    tempDiv.querySelectorAll(\"code:not(pre code)\").forEach(code => {\r\n      const codeElement = code as HTMLElement;\r\n      codeElement.style.fontFamily = \"'Fira Code', 'Consolas', monospace\";\r\n      codeElement.style.background = \"rgba(59, 130, 246, 0.1)\";\r\n      codeElement.style.color = \"#3b82f6\";\r\n      codeElement.style.padding = \"0.2em 0.4em\";\r\n      codeElement.style.borderRadius = \"3px\";\r\n      codeElement.style.fontSize = \"0.9em\";\r\n      codeElement.style.border = \"1px solid rgba(59, 130, 246, 0.2)\";\r\n    });\r\n\r\n    tempDiv.querySelectorAll(\"blockquote\").forEach(blockquote => {\r\n      blockquote.style.borderLeft = \"5px solid #eee\";\r\n      blockquote.style.padding = \"10px 20px\";\r\n      blockquote.style.margin = \"0 0 20px\";\r\n    });\r\n\r\n    return tempDiv.innerHTML;\r\n  }, []);\r\n\r\n  return (\r\n    <Controller\r\n      name={name}\r\n      control={control}\r\n      rules={rules}\r\n      render={({\r\n        field: { value, onChange, onBlur },\r\n        fieldState: { error }\r\n      }) => (\r\n        <div className={`jodit-wrapper ${EDITOR_CLASS_PREFIX}-container`}>\r\n          {/* Scoped styles to this component only */}\r\n          <style>{`\r\n              .${EDITOR_CLASS_PREFIX}-container .jodit-container {\r\n                border-color: ${error ? \"#ff3860\" : \"#dbdbdb\"};\r\n              }\r\n              .${EDITOR_CLASS_PREFIX}-container .jodit-error-message {\r\n                color: #ff3860;\r\n                font-size: 0.85rem;\r\n                margin-top: 4px;\r\n              }\r\n            \r\n              /* Code block styling in the editor */\r\n              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg pre {\r\n                display: inline-block;\r\n                min-width: 200px;\r\n                max-width: 100%;\r\n                width: auto;\r\n                background: linear-gradient(145deg, #1e293b, #0f172a);\r\n                color: #e2e8f0;\r\n                font-family: 'Fira Code', 'Consolas', monospace;\r\n                padding: 1rem;\r\n                margin: 1rem 0;\r\n                border-radius: 8px;\r\n                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);\r\n                overflow-x: auto;\r\n                position: relative;\r\n                border-left: 4px solid #3b82f6;\r\n              }\r\n            \r\n              /* Language indicator */\r\n              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg pre[data-language]::before {\r\n                content: attr(data-language);\r\n                position: absolute;\r\n                top: 0;\r\n                right: 10px;\r\n                background-color: #3b82f6;\r\n                color: white;\r\n                padding: 2px 8px;\r\n                font-size: 12px;\r\n                border-bottom-left-radius: 4px;\r\n                border-bottom-right-radius: 4px;\r\n                font-family: sans-serif;\r\n                opacity: 0.8;\r\n              }\r\n            \r\n              /* Line numbers effect */\r\n              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg pre {\r\n                counter-reset: line;\r\n                padding-left: 3.8em;\r\n                line-height: 1.6;\r\n              }\r\n            \r\n              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg pre code {\r\n                counter-increment: line;\r\n              }\r\n            \r\n              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg pre code .code-line {\r\n                position: relative;\r\n                display: block;\r\n              }\r\n            \r\n              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg pre code .code-line::before {\r\n                content: counter(line);\r\n                position: absolute;\r\n                left: -2.8em;\r\n                width: 2em;\r\n                text-align: right;\r\n                color: #64748b;\r\n                user-select: none;\r\n              }\r\n            \r\n              /* Inline code styling */\r\n              .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg code:not(pre code) {\r\n                font-family: 'Fira Code', 'Consolas', monospace;\r\n                background: rgba(59, 130, 246, 0.1);\r\n                color: #3b82f6;\r\n                padding: 0.2em 0.4em;\r\n                border-radius: 3px;\r\n                font-size: 0.9em;\r\n                border: 1px solid rgba(59, 130, 246, 0.2);\r\n              }\r\n            \r\n              /* Custom code block button styling */\r\n              .jodit-toolbar-button_insertCodeBlock button {\r\n                position: relative;\r\n              }\r\n            \r\n              /* Font preloading (optional) */\r\n              @font-face {\r\n                font-family: 'Fira Code';\r\n                src: url('https://cdnjs.cloudflare.com/ajax/libs/firacode/6.2.0/woff2/FiraCode-Regular.woff2') format('woff2');\r\n                font-style: normal;\r\n                font-weight: 400;\r\n                font-display: swap;\r\n              }\r\n            `}</style>\r\n\r\n          <JoditEditor\r\n            value={value || \"\"}\r\n            //@ts-ignore\r\n            config={config}\r\n            onChange={content => {\r\n              onChange(content);\r\n            }}\r\n            onBlur={() => {\r\n              if (value) {\r\n                const cleaned = processContent(value);\r\n                onChange(cleaned);\r\n              }\r\n              onBlur();\r\n            }}\r\n            className={error ? \"jodit-container-error\" : \"\"}\r\n            ref={editor => {\r\n              if (editor?.container) {\r\n                const textarea = editor.container.querySelector(\"textarea\");\r\n                if (textarea) {\r\n                  textarea.setAttribute(\"data-prevent-scroll\", \"true\");\r\n                }\r\n              }\r\n            }}\r\n          />\r\n          {error && <p className=\"jodit-error-message\">{error.message}</p>}\r\n        </div>\r\n      )}\r\n    />\r\n  );\r\n};\r\n\r\nexport default RichTextArea;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AACnD,OAAOC,WAAW,MAAM,aAAa;AACrC,SAASC,UAAU,EAAEC,cAAc,QAAQ,iBAAiB;AAG5D,OAAO,4BAA4B;;AAEnC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,mBAAmB,GAAG,qBAAqB;AAEjD,MAAMC,YAAY,GAAGA,CAAC;EACpBC,IAAI;EACJC,KAAK;EACLC,WAAW;EACXC,QAAQ,GAAG;AAIb,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC;EAAQ,CAAC,GAAGV,cAAc,CAAC,CAAC;;EAEpC;EACA,MAAMW,YAAY,GAAGf,OAAO,CAC1B,MAAO;AACX,SAASO,mBAAoB;AAC7B,SAASA,mBAAoB;AAC7B,SAASA,mBAAoB;AAC7B,SAASA,mBAAoB;AAC7B,SAASA,mBAAoB;AAC7B,SAASA,mBAAoB;AAC7B,SAASA,mBAAoB;AAC7B,SAASA,mBAAoB;AAC7B,SAASA,mBAAoB;AAC7B,SAASA,mBAAoB;AAC7B,SAASA,mBAAoB;AAC7B,SAASA,mBAAoB;AAC7B,SAASA,mBAAoB;AAC7B;AACA;AACA,SAASA,mBAAoB;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,mBAAoB;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,mBAAoB;AAC7B;AACA;AACA;AACA;AACA;AACA,SAASA,mBAAoB;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,mBAAoB;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,mBAAoB;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,mBAAoB;AAC7B,KAAK,EACD,EACF,CAAC;EAED,MAAMS,MAAM,GAAGhB,OAAO,CACpB,OAAO;IACLiB,QAAQ,EAAEL,QAAQ;IAClBM,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE,SAAS;IAChBC,iBAAiB,EAAE,OAAgB;IACnCC,aAAa,EAAE,IAAI;IACnBC,eAAe,EAAE,IAAI;IACrBC,gBAAgB,EAAE,KAAK;IACvBC,gBAAgB,EAAE,KAAK;IACvBC,oBAAoB,EAAE,KAAK;IAC3BC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,IAAI;IACxBC,aAAa,EAAE,IAAI;IACnBC,kBAAkB,EAAE,IAAI;IACxBC,sBAAsB,EAAE,IAAI;IAC5BC,OAAO,EAAE,CACP,MAAM,EACN,QAAQ,EACR,WAAW,EACX,eAAe,EACf,GAAG,EACH,SAAS,EACT,QAAQ,EACR,GAAG,EACH,MAAM,EACN,UAAU,EACV,OAAO,EACP,aAAa,EACb,GAAG,EACH,OAAO,EACP,OAAO,EACP,MAAM,EACN,GAAG,EACH,OAAO,EACP,MAAM,EACN,MAAM,EACN,GAAG,EACH,MAAM,EACN,QAAQ,EACR,UAAU,EACV,SAAS,CACV;IACDC,QAAQ,EAAE;MACRC,sBAAsB,EAAE,IAAI;MAC5BC,gBAAgB,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;MACvDC,wBAAwB,EAAGC,GAAS,IAAKA;IAC3C,CAAC;IACDC,SAAS,EAAE;MACTC,SAAS,EAAE;QACTC,CAAC,EAAE,IAAI;QACPC,EAAE,EAAE,IAAI;QACRC,MAAM,EAAE,IAAI;QACZC,CAAC,EAAE,IAAI;QACPC,EAAE,EAAE,IAAI;QACRC,CAAC,EAAE,IAAI;QACPC,CAAC,EAAE,IAAI;QACPC,MAAM,EAAE,IAAI;QACZC,CAAC,EAAE,IAAI;QACPC,EAAE,EAAE,IAAI;QACRC,EAAE,EAAE,IAAI;QACRC,EAAE,EAAE,IAAI;QACRC,CAAC,EAAE,IAAI;QACPf,GAAG,EAAE,IAAI;QACTgB,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE,IAAI;QACXC,EAAE,EAAE,IAAI;QACRC,EAAE,EAAE,IAAI;QACRC,EAAE,EAAE,IAAI;QACRC,EAAE,EAAE,IAAI;QACRC,EAAE,EAAE,IAAI;QACRC,EAAE,EAAE,IAAI;QACRC,EAAE,EAAE,IAAI;QACRC,GAAG,EAAE,IAAI;QACTC,IAAI,EAAE,IAAI;QACVC,GAAG,EAAE,IAAI;QACTC,IAAI,EAAE,IAAI;QACVC,UAAU,EAAE;MACd,CAAC;MACDC,QAAQ,EAAE;QACRC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,IAAI;QACZC,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE;MACT,CAAC;MACDC,mBAAmB,EAAE,IAAI;MACzBC,qBAAqB,EAAE,IAAI;MAC3BC,kBAAkB,EAAE;IACtB,CAAC;IACD;IACAC,MAAM,EAAE;MACNC,SAAS,EAAGC,MAAa,IAAK;QAC5B;QACAA,MAAM,CAACA,MAAM,CAACC,SAAS,CAACC,GAAG,CAAC5E,mBAAmB,CAAC;;QAEhD;QACA,MAAM6E,QAAQ,GAAGH,MAAM,CAACI,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QACrDF,QAAQ,CAACG,SAAS,GAAGxE,YAAY;QACjCkE,MAAM,CAACO,SAAS,CAACC,WAAW,CAACL,QAAQ,CAAC;;QAEtC;QACA,IAAIzE,WAAW,EAAE;UACfsE,MAAM,CAACA,MAAM,CAACS,YAAY,CAAC,kBAAkB,EAAE/E,WAAW,CAAC;QAC7D;;QAEA;QACAsE,MAAM,CAACF,MAAM,CAACY,EAAE,CAAC,SAAS,EAAGC,KAAoB,IAAK;UACpD;UACA,IACE,CAACA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,OAAO,KAC/BF,KAAK,CAACG,QAAQ,IACdH,KAAK,CAACI,GAAG,KAAK,GAAG,EACjB;YACAJ,KAAK,CAACK,cAAc,CAAC,CAAC;YACtB,MAAMC,SAAS,GAAI,2CAA0C;YAC7DjB,MAAM,CAACkB,SAAS,CAACC,UAAU,CAACF,SAAS,CAAC;YACtC,OAAO,KAAK;UACd;UACA,OAAO,IAAI;QACb,CAAC,CAAC;MACJ,CAAC;MACDG,YAAY,EAAGpB,MAAa,IAAK;QAC/BqB,UAAU,CAAC,MAAM;UACf;UACA,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,wBAAwB,CAAC;UAC/D,IAAIF,MAAM,EAAE;YACV;YACA,MAAMG,cAAc,GAAGH,MAAM,CAACE,aAAa,CACzC,wBACF,CAAC;YACD,IAAIC,cAAc,EAAE;cAClBA,cAAc,CAACxB,SAAS,CAACC,GAAG,CAAC5E,mBAAmB,CAAC;;cAEjD;cACAoG,gBAAgB,CAACD,cAA6B,CAAC;YACjD;;YAEA;YACA,IACE,CAACH,MAAM,CAACE,aAAa,CAClB,cAAalG,mBAAoB,SACpC,CAAC,EACD;cACA,MAAMoE,KAAK,GAAG6B,QAAQ,CAACI,aAAa,CAAC,OAAO,CAAC;cAC7CjC,KAAK,CAACe,YAAY,CAAE,QAAOnF,mBAAoB,QAAO,EAAE,MAAM,CAAC;cAC/DoE,KAAK,CAACY,SAAS,GAAGxE,YAAY;cAC9BwF,MAAM,CAACd,WAAW,CAACd,KAAK,CAAC;YAC3B;UACF;QACF,CAAC,EAAE,CAAC,CAAC;MACP,CAAC;MACD;MACAkC,MAAM,EAAEA,CAACC,QAAgB,EAAEC,QAAgB,KAAK;QAC9C,IAAID,QAAQ,IAAIA,QAAQ,KAAKC,QAAQ,EAAE;UACrC;UACA;QAAA;MAEJ;IACF,CAAC;IACDC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,GAAG;IACdC,SAAS,EAAE,GAAG;IACdC,KAAK,EAAE,MAAM;IACb;IACAC,SAAS,EAAE7G,mBAAmB,GAAG;EACnC,CAAC,CAAC,EACF,CAACK,QAAQ,EAAED,WAAW,EAAEI,YAAY,CACtC,CAAC;;EAED;EACA,MAAM4F,gBAAgB,GAAInB,SAAsB,IAAK;IACnDA,SAAS,CAAC6B,gBAAgB,CAAC,KAAK,CAAC,CAACC,OAAO,CAACnD,GAAG,IAAI;MAC/C;MACAA,GAAG,CAACQ,KAAK,CAAC4C,OAAO,GAAG,cAAc;MAClCpD,GAAG,CAACQ,KAAK,CAAC6C,QAAQ,GAAG,OAAO;MAC5BrD,GAAG,CAACQ,KAAK,CAAC8C,QAAQ,GAAG,MAAM;MAC3BtD,GAAG,CAACQ,KAAK,CAACwC,KAAK,GAAG,MAAM;MACxBhD,GAAG,CAACQ,KAAK,CAAC+C,UAAU,GAAG,2CAA2C;MAClEvD,GAAG,CAACQ,KAAK,CAACgD,KAAK,GAAG,SAAS;MAC3BxD,GAAG,CAACQ,KAAK,CAACiD,UAAU,GAAG,oCAAoC;MAC3DzD,GAAG,CAACQ,KAAK,CAACkD,OAAO,GAAG,MAAM;MAC1B1D,GAAG,CAACQ,KAAK,CAACmD,MAAM,GAAG,QAAQ;MAC3B3D,GAAG,CAACQ,KAAK,CAACoD,YAAY,GAAG,KAAK;MAC9B5D,GAAG,CAACQ,KAAK,CAACqD,SAAS,GACjB,6DAA6D;MAC/D7D,GAAG,CAACQ,KAAK,CAACsD,SAAS,GAAG,MAAM;MAC5B9D,GAAG,CAACQ,KAAK,CAACuD,QAAQ,GAAG,UAAU;MAC/B/D,GAAG,CAACQ,KAAK,CAACwD,UAAU,GAAG,mBAAmB;MAC1ChE,GAAG,CAACQ,KAAK,CAACyD,YAAY,GAAG,MAAM;MAC/BjE,GAAG,CAACQ,KAAK,CAAC0D,WAAW,GAAG,OAAO;MAC/BlE,GAAG,CAACQ,KAAK,CAAC2D,UAAU,GAAG,KAAK;;MAE5B;MACA,MAAMlE,IAAI,GAAGD,GAAG,CAACsC,aAAa,CAAC,MAAM,CAAC;MACtC,IAAIrC,IAAI,EAAE;QACRA,IAAI,CAACO,KAAK,CAAC4D,eAAe,GAAG,aAAa;QAC1CnE,IAAI,CAACO,KAAK,CAACkD,OAAO,GAAG,GAAG;QACxBzD,IAAI,CAACO,KAAK,CAACoD,YAAY,GAAG,GAAG;QAC7B3D,IAAI,CAACO,KAAK,CAAC6D,QAAQ,GAAG,OAAO;;QAE7B;QACA,IAAI,CAACpE,IAAI,CAACqE,YAAY,CAAC,gBAAgB,CAAC,EAAE;UACxC,MAAMC,OAAO,GAAGtE,IAAI,CAACmB,SAAS;UAC9B,MAAMoD,KAAK,GAAGD,OAAO,CAACE,KAAK,CAAC,IAAI,CAAC;UACjC,IAAIC,UAAU,GAAG,EAAE;UAEnBF,KAAK,CAACrB,OAAO,CAACwB,IAAI,IAAI;YACpBD,UAAU,IAAK,2BAA0BC,IAAK,WAAU;UAC1D,CAAC,CAAC;UAEF1E,IAAI,CAACmB,SAAS,GAAGsD,UAAU;UAC3BzE,IAAI,CAACsB,YAAY,CAAC,gBAAgB,EAAE,MAAM,CAAC;QAC7C;MACF;IACF,CAAC,CAAC;;IAEF;IACAF,SAAS,CAAC6B,gBAAgB,CAAC,oBAAoB,CAAC,CAACC,OAAO,CAAClD,IAAI,IAAI;MAC/D,MAAM2E,WAAW,GAAG3E,IAAmB;MACvC2E,WAAW,CAACpE,KAAK,CAACiD,UAAU,GAAG,oCAAoC;MACnEmB,WAAW,CAACpE,KAAK,CAAC+C,UAAU,GAAG,yBAAyB;MACxDqB,WAAW,CAACpE,KAAK,CAACgD,KAAK,GAAG,SAAS;MACnCoB,WAAW,CAACpE,KAAK,CAACkD,OAAO,GAAG,aAAa;MACzCkB,WAAW,CAACpE,KAAK,CAACoD,YAAY,GAAG,KAAK;MACtCgB,WAAW,CAACpE,KAAK,CAAC6D,QAAQ,GAAG,OAAO;MACpCO,WAAW,CAACpE,KAAK,CAACqE,MAAM,GAAG,mCAAmC;IAChE,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAGhJ,WAAW,CAAEyI,OAAe,IAAK;IACtD,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,aAAa,IAAIA,OAAO,CAACQ,IAAI,CAAC,CAAC,KAAK,EAAE,EAChE,OAAO,EAAE;IAEX,IAAIC,SAAS,GAAGT,OAAO,CAACU,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;IAE5C,MAAMC,OAAO,GAAG7C,QAAQ,CAACI,aAAa,CAAC,KAAK,CAAC;IAC7CyC,OAAO,CAAC9D,SAAS,GAAG4D,SAAS;;IAE7B;IACA;IACAE,OAAO,CAAChC,gBAAgB,CAAC,IAAI,CAAC,CAACC,OAAO,CAACzD,EAAE,IAAI;MAC3CA,EAAE,CAACc,KAAK,CAAC6D,QAAQ,GAAG,KAAK;MACzB3E,EAAE,CAACc,KAAK,CAAC2E,UAAU,GAAG,MAAM;MAC5BzF,EAAE,CAACc,KAAK,CAACmD,MAAM,GAAG,UAAU;IAC9B,CAAC,CAAC;IAEFuB,OAAO,CAAChC,gBAAgB,CAAC,IAAI,CAAC,CAACC,OAAO,CAACxD,EAAE,IAAI;MAC3CA,EAAE,CAACa,KAAK,CAAC6D,QAAQ,GAAG,OAAO;MAC3B1E,EAAE,CAACa,KAAK,CAAC2E,UAAU,GAAG,MAAM;MAC5BxF,EAAE,CAACa,KAAK,CAACmD,MAAM,GAAG,UAAU;IAC9B,CAAC,CAAC;IAEFuB,OAAO,CAAChC,gBAAgB,CAAC,IAAI,CAAC,CAACC,OAAO,CAACvD,EAAE,IAAI;MAC3CA,EAAE,CAACY,KAAK,CAAC6D,QAAQ,GAAG,QAAQ;MAC5BzE,EAAE,CAACY,KAAK,CAAC2E,UAAU,GAAG,MAAM;MAC5BvF,EAAE,CAACY,KAAK,CAACmD,MAAM,GAAG,UAAU;IAC9B,CAAC,CAAC;IAEFuB,OAAO,CAAChC,gBAAgB,CAAC,IAAI,CAAC,CAACC,OAAO,CAACtD,EAAE,IAAI;MAC3CA,EAAE,CAACW,KAAK,CAAC6D,QAAQ,GAAG,KAAK;MACzBxE,EAAE,CAACW,KAAK,CAAC2E,UAAU,GAAG,MAAM;MAC5BtF,EAAE,CAACW,KAAK,CAACmD,MAAM,GAAG,UAAU;IAC9B,CAAC,CAAC;;IAEF;IACAuB,OAAO,CAAChC,gBAAgB,CAAC,IAAI,CAAC,CAACC,OAAO,CAAClE,EAAE,IAAI;MAC3CA,EAAE,CAACuB,KAAK,CAAC4E,aAAa,GAAG,MAAM;MAC/BnG,EAAE,CAACuB,KAAK,CAACmD,MAAM,GAAG,GAAG;MACrB1E,EAAE,CAACuB,KAAK,CAACkD,OAAO,GAAG,YAAY,CAAC,CAAC;IACnC,CAAC,CAAC;IAEFwB,OAAO,CAAChC,gBAAgB,CAAC,OAAO,CAAC,CAACC,OAAO,CAACkC,IAAI,IAAI;MAC/CA,IAAI,CAAiB7E,KAAK,CAAC4E,aAAa,GAAG,QAAQ;MACnDC,IAAI,CAAiB7E,KAAK,CAAC0D,WAAW,GAAG,MAAM,CAAC,CAAC;IACpD,CAAC,CAAC;IAEFgB,OAAO,CAAChC,gBAAgB,CAAC,UAAU,CAAC,CAACC,OAAO,CAACmC,MAAM,IAAI;MACpDA,MAAM,CAAiB9E,KAAK,CAAC4E,aAAa,GAAG,QAAQ;MACrDE,MAAM,CAAiB9E,KAAK,CAAC0D,WAAW,GAAG,MAAM;IACpD,CAAC,CAAC;IAEFgB,OAAO,CAAChC,gBAAgB,CAAC,IAAI,CAAC,CAACC,OAAO,CAACjE,EAAE,IAAI;MAC3CA,EAAE,CAACsB,KAAK,CAAC4E,aAAa,GAAG,SAAS;MAClClG,EAAE,CAACsB,KAAK,CAAC0D,WAAW,GAAG,MAAM,CAAC,CAAC;MAC/BhF,EAAE,CAACsB,KAAK,CAAC+E,SAAS,GAAG,KAAK;MAC1BrG,EAAE,CAACsB,KAAK,CAACgF,YAAY,GAAG,KAAK;IAC/B,CAAC,CAAC;IAEFN,OAAO,CAAChC,gBAAgB,CAAC,OAAO,CAAC,CAACC,OAAO,CAACsC,IAAI,IAAI;MAC/CA,IAAI,CAAiBjF,KAAK,CAAC+E,SAAS,GAAG,GAAG;MAC1CE,IAAI,CAAiBjF,KAAK,CAACgF,YAAY,GAAG,GAAG;MAC7CC,IAAI,CAAiBjF,KAAK,CAAC0D,WAAW,GAAG,MAAM,CAAC,CAAC;IACpD,CAAC,CAAC;;IAEF;IACAgB,OAAO,CAAChC,gBAAgB,CAAC,IAAI,CAAC,CAACC,OAAO,CAAC3D,EAAE,IAAI;MAC3CA,EAAE,CAACgB,KAAK,CAACqE,MAAM,GAAG,WAAW;IAC/B,CAAC,CAAC;;IAEF;IACAK,OAAO,CAAChC,gBAAgB,CAAC,OAAO,CAAC,CAACC,OAAO,CAAC9D,KAAK,IAAI;MACjDA,KAAK,CAACmB,KAAK,CAACkF,cAAc,GAAG,UAAU;MACvCrG,KAAK,CAACmB,KAAK,CAACwC,KAAK,GAAG,MAAM;IAC5B,CAAC,CAAC;IAEFkC,OAAO,CAAChC,gBAAgB,CAAC,IAAI,CAAC,CAACC,OAAO,CAAC1D,EAAE,IAAI;MAC3CA,EAAE,CAACe,KAAK,CAACqE,MAAM,GAAG,WAAW;MAC7BpF,EAAE,CAACe,KAAK,CAACkD,OAAO,GAAG,KAAK;MACxBjE,EAAE,CAACe,KAAK,CAAC4D,eAAe,GAAG,SAAS;IACtC,CAAC,CAAC;IAEFc,OAAO,CAAChC,gBAAgB,CAAC,IAAI,CAAC,CAACC,OAAO,CAAC5D,EAAE,IAAI;MAC3CA,EAAE,CAACiB,KAAK,CAACmF,YAAY,GAAG,gBAAgB;IAC1C,CAAC,CAAC;;IAEF;IACAT,OAAO,CAAChC,gBAAgB,CAAC,KAAK,CAAC,CAACC,OAAO,CAACnD,GAAG,IAAI;MAC7CA,GAAG,CAACQ,KAAK,CAAC4C,OAAO,GAAG,cAAc;MAClCpD,GAAG,CAACQ,KAAK,CAAC6C,QAAQ,GAAG,OAAO;MAC5BrD,GAAG,CAACQ,KAAK,CAAC8C,QAAQ,GAAG,MAAM;MAC3BtD,GAAG,CAACQ,KAAK,CAACwC,KAAK,GAAG,MAAM;MACxBhD,GAAG,CAACQ,KAAK,CAAC+C,UAAU,GAAG,2CAA2C;MAClEvD,GAAG,CAACQ,KAAK,CAACgD,KAAK,GAAG,SAAS;MAC3BxD,GAAG,CAACQ,KAAK,CAACiD,UAAU,GAAG,oCAAoC;MAC3DzD,GAAG,CAACQ,KAAK,CAACkD,OAAO,GAAG,MAAM;MAC1B1D,GAAG,CAACQ,KAAK,CAACmD,MAAM,GAAG,QAAQ;MAC3B3D,GAAG,CAACQ,KAAK,CAACoD,YAAY,GAAG,KAAK;MAC9B5D,GAAG,CAACQ,KAAK,CAACqD,SAAS,GACjB,6DAA6D;MAC/D7D,GAAG,CAACQ,KAAK,CAACsD,SAAS,GAAG,MAAM;MAC5B9D,GAAG,CAACQ,KAAK,CAACuD,QAAQ,GAAG,UAAU;MAC/B/D,GAAG,CAACQ,KAAK,CAACwD,UAAU,GAAG,mBAAmB;;MAE1C;MACA,MAAM/D,IAAI,GAAGD,GAAG,CAACsC,aAAa,CAAC,MAAM,CAAC;MACtC,IAAIrC,IAAI,EAAE;QACR,MAAM2F,WAAW,GAAG3F,IAAI,CAAC4F,WAAW,IAAI,EAAE;QAC1C,IAAI5I,QAAQ,GAAG,MAAM;;QAErB;QACA,IACE2I,WAAW,CAACE,QAAQ,CAAC,UAAU,CAAC,IAChCF,WAAW,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAC9BF,WAAW,CAACE,QAAQ,CAAC,MAAM,CAAC,EAC5B;UACA7I,QAAQ,GAAG,YAAY;QACzB,CAAC,MAAM,IACL2I,WAAW,CAACE,QAAQ,CAAC,MAAM,CAAC,IAC3BF,WAAW,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,WAAW,CAACE,QAAQ,CAAC,GAAG,CAAE,EAC9D;UACA7I,QAAQ,GAAG,QAAQ;QACrB,CAAC,MAAM,IACL2I,WAAW,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAC9BF,WAAW,CAACE,QAAQ,CAAC,GAAG,CAAC,EACzB;UACA7I,QAAQ,GAAG,MAAM;QACnB,CAAC,MAAM,IAAI2I,WAAW,CAACE,QAAQ,CAAC,UAAU,CAAC,EAAE;UAC3C7I,QAAQ,GAAG,KAAK;QAClB;;QAEA;QACA+C,GAAG,CAACuB,YAAY,CAAC,eAAe,EAAEtE,QAAQ,CAAC;;QAE3C;QACAgD,IAAI,CAACO,KAAK,CAAC4D,eAAe,GAAG,aAAa;QAC1CnE,IAAI,CAACO,KAAK,CAACkD,OAAO,GAAG,GAAG;QACxBzD,IAAI,CAACO,KAAK,CAACoD,YAAY,GAAG,GAAG;MAC/B;IACF,CAAC,CAAC;IAEFsB,OAAO,CAAChC,gBAAgB,CAAC,oBAAoB,CAAC,CAACC,OAAO,CAAClD,IAAI,IAAI;MAC7D,MAAM2E,WAAW,GAAG3E,IAAmB;MACvC2E,WAAW,CAACpE,KAAK,CAACiD,UAAU,GAAG,oCAAoC;MACnEmB,WAAW,CAACpE,KAAK,CAAC+C,UAAU,GAAG,yBAAyB;MACxDqB,WAAW,CAACpE,KAAK,CAACgD,KAAK,GAAG,SAAS;MACnCoB,WAAW,CAACpE,KAAK,CAACkD,OAAO,GAAG,aAAa;MACzCkB,WAAW,CAACpE,KAAK,CAACoD,YAAY,GAAG,KAAK;MACtCgB,WAAW,CAACpE,KAAK,CAAC6D,QAAQ,GAAG,OAAO;MACpCO,WAAW,CAACpE,KAAK,CAACqE,MAAM,GAAG,mCAAmC;IAChE,CAAC,CAAC;IAEFK,OAAO,CAAChC,gBAAgB,CAAC,YAAY,CAAC,CAACC,OAAO,CAACjD,UAAU,IAAI;MAC3DA,UAAU,CAACM,KAAK,CAACwD,UAAU,GAAG,gBAAgB;MAC9C9D,UAAU,CAACM,KAAK,CAACkD,OAAO,GAAG,WAAW;MACtCxD,UAAU,CAACM,KAAK,CAACmD,MAAM,GAAG,UAAU;IACtC,CAAC,CAAC;IAEF,OAAOuB,OAAO,CAAC9D,SAAS;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEjF,OAAA,CAACH,UAAU;IACTM,IAAI,EAAEA,IAAK;IACXK,OAAO,EAAEA,OAAQ;IACjBJ,KAAK,EAAEA,KAAM;IACbwJ,MAAM,EAAEA,CAAC;MACPC,KAAK,EAAE;QAAEC,KAAK;QAAEC,QAAQ;QAAEC;MAAO,CAAC;MAClCC,UAAU,EAAE;QAAEC;MAAM;IACtB,CAAC,kBACClK,OAAA;MAAK8G,SAAS,EAAG,iBAAgB7G,mBAAoB,YAAY;MAAAkK,QAAA,gBAE/DnK,OAAA;QAAAmK,QAAA,EAAS;AACnB,iBAAiBlK,mBAAoB;AACrC,gCAAgCiK,KAAK,GAAG,SAAS,GAAG,SAAU;AAC9D;AACA,iBAAiBjK,mBAAoB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBA,mBAAoB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBA,mBAAoB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBA,mBAAoB;AACrC;AACA;AACA;AACA;AACA;AACA,iBAAiBA,mBAAoB;AACrC;AACA;AACA;AACA,iBAAiBA,mBAAoB;AACrC;AACA;AACA;AACA;AACA,iBAAiBA,mBAAoB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBA,mBAAoB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MAAa;QAAAmK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAEZvK,OAAA,CAACJ,WAAW;QACVkK,KAAK,EAAEA,KAAK,IAAI;QAChB;QAAA;QACApJ,MAAM,EAAEA,MAAO;QACfqJ,QAAQ,EAAE3B,OAAO,IAAI;UACnB2B,QAAQ,CAAC3B,OAAO,CAAC;QACnB,CAAE;QACF4B,MAAM,EAAEA,CAAA,KAAM;UACZ,IAAIF,KAAK,EAAE;YACT,MAAMU,OAAO,GAAG7B,cAAc,CAACmB,KAAK,CAAC;YACrCC,QAAQ,CAACS,OAAO,CAAC;UACnB;UACAR,MAAM,CAAC,CAAC;QACV,CAAE;QACFlD,SAAS,EAAEoD,KAAK,GAAG,uBAAuB,GAAG,EAAG;QAChDO,GAAG,EAAE9F,MAAM,IAAI;UACb,IAAIA,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEO,SAAS,EAAE;YACrB,MAAMwF,QAAQ,GAAG/F,MAAM,CAACO,SAAS,CAACiB,aAAa,CAAC,UAAU,CAAC;YAC3D,IAAIuE,QAAQ,EAAE;cACZA,QAAQ,CAACtF,YAAY,CAAC,qBAAqB,EAAE,MAAM,CAAC;YACtD;UACF;QACF;MAAE;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACDL,KAAK,iBAAIlK,OAAA;QAAG8G,SAAS,EAAC,qBAAqB;QAAAqD,QAAA,EAAED,KAAK,CAACS;MAAO;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D;EACL;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEN,CAAC;AAAChK,EAAA,CA9nBIL,YAAY;EAAA,QASIJ,cAAc;AAAA;AAAA8K,EAAA,GAT9B1K,YAAY;AAgoBlB,eAAeA,YAAY;AAAC,IAAA0K,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}