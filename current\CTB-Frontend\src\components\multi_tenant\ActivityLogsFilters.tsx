import React from "react";
import { UserRole } from "../../utils/api/endpoints/user/credentials";
import SearchBar from "./SearchBar";
import { formatRole } from "../../utils/hooks/multi-tenant/invitation";
import { FaSortAmountDownAlt, FaSortAmountUpAlt } from "react-icons/fa";

interface ActivityLogsFiltersProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  filterRole: string;
  setFilterRole: (role: string) => void;
  filterModule: string;
  setFilterModule: (module: string) => void;
  sortOrder: "newest" | "oldest";
  setSortOrder: (order: "newest" | "oldest") => void;
  availableRoles: UserRole[];
  availableModules: string[];
}

const ActivityLogsFilters: React.FC<ActivityLogsFiltersProps> = ({
  searchQuery,
  setSearchQuery,
  filterRole,
  setFilterRole,
  filterModule,
  setFilterModule,
  sortOrder,
  setSortOrder,
  availableRoles,
  availableModules
}) => {
  return (
    <div className="mb-6 flex items-center gap-4">
      {/* Search Bar with increased width */}
      <div className="w-full max-w-md">
        <SearchBar
          value={searchQuery}
          onChange={setSearchQuery}
          placeholder="email/username/action"
        />
      </div>

      {/* Role Dropdown */}
      <select
        className="w-full rounded-md border border-gray-300 p-2 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
        value={filterRole}
        onChange={e => setFilterRole(e.target.value)}
      >
        <option value="all">All Roles</option>
        {availableRoles.map(role => (
          <option key={role} value={UserRole[role]}>
            {formatRole(role)}
          </option>
        ))}
      </select>

      {/* Module Dropdown */}
      <select
        className="w-full rounded-md border border-gray-300 p-2 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
        value={filterModule}
        onChange={e => setFilterModule(e.target.value)}
      >
        <option value="all">All Modules</option>
        {availableModules.map(module => (
          <option key={module} value={module}>
            {module}
          </option>
        ))}
      </select>

      {/* Sort Button with reduced width */}
      <button
        onClick={() =>
          setSortOrder(sortOrder === "newest" ? "oldest" : "newest")
        }
        className="inline-flex w-fit items-center gap-1 rounded-md bg-blue-600 px-3 py-2 text-sm font-medium text-white shadow-sm transition hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-1"
      >
        {sortOrder === "newest" ? (
          <FaSortAmountDownAlt className="h-4 w-4" />
        ) : (
          <FaSortAmountUpAlt className="h-4 w-4" />
        )}
        <span>Date</span>
      </button>
    </div>
  );
};

export default ActivityLogsFilters;
