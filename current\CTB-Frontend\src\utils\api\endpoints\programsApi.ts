import {
  CTBProgram,
  ProgramAPIResponse,
  ProgramTarget,
  ProgramType,
  parseProgramDetails
} from "./programs/parsePrograms";
import { ProgramFilters } from "../../hooks/programs/usePrograms";
import { api, createURLSearchParams } from "../api";

type PaginatedProgramData = {
  programs: CTBProgram[];
  results: number;
};

export type ProgramQueryFilters = {
  activated?: string;
  deleted?: string;
  private?: string;
  type?: string;
  limit?: string;
  page?: string;
  search?: string;
};

export type ProgramUpdateFields = {
  title?: string;
  description?: string;
  scope?: string;
  outOfScope?: string;
  targets?: ProgramTarget[];
  type?: ProgramType;
  triaged?: boolean;
  payoutRange?: number[];
  knownVulnerabilities?: string;
  other?: string;
  vpn?: string;
  termsOfService?: string;
  credentials?: string;
  rewardPolicy?: string;
  isDelete?: boolean;
  private?: boolean;
};

const programsUrl = "/v2/programs";
const publicProgramUrl = "/public-program";

/**
 * The Programs API slice definition.
 *
 * Each endpoint is defined here, and is then available via the
 * generated hooks. Data fetched using these endpoints is cached
 * for any subsequent queries
 */
export const programsApi = api.injectEndpoints({
  endpoints: builder => ({
    /**
     * Saves program details like test lead, prepared by, etc.
     */
    saveProgramDetails: builder.mutation<
      any, // Replace with proper response type if known
      {
        programId: number;
        details: {
          test_lead: string;
          prepared_by: string;
          reviewed_by: string;
          approved_by: string;
        };
      }
    >({
      query: ({ programId, details }) => ({
        url: `${programsUrl}/${programId}/details`,
        method: "post",
        data: details
      }),
      invalidatesTags: (_, __, { programId }) => [
        {
          type: "Program",
          id: programId
        }
      ]
    }),
    /**
     * Retrieve the list of paginated programs available to
     * the current user from the backend
     */
    getPrograms: builder.query<PaginatedProgramData, ProgramFilters>({
      query: filters => {
        const params = createURLSearchParams<ProgramQueryFilters>({
          activated: filters.activated?.map(a => a === "Active").join(","),
          deleted: filters.deleted?.join(","),
          private: filters.private?.map(p => p === "Private").join(","),
          type: filters.type,
          limit: filters.limit?.toString(),
          page: filters.page?.toString(),
          search: filters.search
        });

        return {
          url: programsUrl + "?" + params.toString(),
          method: "get"
        };
      },
      transformResponse: (response: {
        programs: ProgramAPIResponse[];
        count: number;
      }) => ({
        programs: response.programs
          .filter(program => program !== undefined && program !== null)
          .map(program => parseProgramDetails(program)),
        results: response.count
      }),
      providesTags: ["Programs"]
    }),
    /**
     * Retreive a single program from the backend
     */
    getProgram: builder.query<CTBProgram, number>({
      query: id => ({ url: programsUrl + "?id=" + id, method: "get" }),
      transformResponse: (response: {
        programs: ProgramAPIResponse[];
        count: number;
      }) => parseProgramDetails(response.programs[0]),
      providesTags: (_, __, id) => [
        {
          type: "Program",
          id
        }
      ]
    }),
    /**
     * Retreive a single program from the backend for public page
     */
    getPublicProgram: builder.query<CTBProgram, number>({
      query: id => ({ url: publicProgramUrl + "?id=" + id, method: "get" }),
      transformResponse: (response: ProgramAPIResponse) =>
        parseProgramDetails(response),
      providesTags: (_, __, id) => [
        {
          type: "Program",
          id
        }
      ]
    }),
    /**

     * Updates the list of private access users for the given program
     */
    setPrivateAccessUsers: builder.mutation<
      void,
      {
        id: number;
        users: number[];
      }
    >({
      query: ({ id, users }) => ({
        url: programsUrl + "/" + id,
        method: "post",
        data: {
          private_users: users
        }
      }),
      invalidatesTags: (_, __, { id }) => [
        {
          type: "Program",
          id
        },
        "Programs"
      ]
    }),
    /**
     * Activates/deactivates the given program
     */
    postProgramActivation: builder.mutation<
      void,
      {
        id: number;
        isActivated: boolean;
      }
    >({
      query: ({ id, isActivated }) => ({
        url: programsUrl + "/" + id,
        method: "post",
        data: {
          activated: isActivated
        }
      }),
      invalidatesTags: (_, __, { id }) => [
        {
          type: "Program",
          id
        },
        "Programs"
      ]
    }),
    /**
     * Deletes the given program
     */
    deleteProgram: builder.mutation<void, number>({
      query: id => ({ url: programsUrl + "?id=" + id, method: "delete" }),
      invalidatesTags: (_, __, id) => [
        {
          type: "Program",
          id
        },
        "Programs"
      ]
    }),
    /**
     * Updates the given program, creating a new one if necessary
     */
    updateProgram: builder.mutation<number, { id?: number; data: FormData }>({
      query: ({ id, data }) => ({
        url: programsUrl + (id === undefined ? "" : "/" + id),
        method: "post",
        data
      }),
      transformResponse: (response: { id: number }) => response.id,
      invalidatesTags: (_, __, { id }) => [
        {
          type: "Program",
          id
        },
        "Programs"
      ]
    }),

    // Add attachments handling in the mutation
    updateProgramWithAttachments: builder.mutation<
      number,
      { id?: number; data: FormData }
    >({
      query: ({ id, data }) => ({
        url: programsUrl + (id === undefined ? "" : "/" + id),
        method: "post",
        data
      }),
      transformResponse: (response: { id: number }) => response.id,
      invalidatesTags: (_, __, { id }) => [
        {
          type: "Program",
          id
        },
        "Programs"
      ]
    })
  })
});

export const {
  useGetProgramsQuery,
  useGetProgramQuery,
  useGetPublicProgramQuery,
  useSetPrivateAccessUsersMutation,
  usePostProgramActivationMutation,
  useDeleteProgramMutation,
  useUpdateProgramMutation,
  useUpdateProgramWithAttachmentsMutation,
  useSaveProgramDetailsMutation
} = programsApi;
