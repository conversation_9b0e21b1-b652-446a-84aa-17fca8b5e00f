import { Controller, useFormContext } from "react-hook-form";
import EditorInput from "./EditorInput";
import { InputBaseParams } from "../../forms/Form";
import { CropOptions } from "../../file_upload/useImageCrop";
import AttachmentsInput from "../../forms/inputs/AttachmentsInput";
import React from "react";
import { FiPaperclip } from "react-icons/fi";

const ProgramEditorLogo = ({
  name,
  inputTitle: title,
  details,
  placeholder,
  note,
  accept,
  multiple,
  cropImage,
  defaultLinks = []
}: InputBaseParams & {
  inputTitle: string;
  details: string;
  placeholder?: string;
  accept?: string;
  multiple?: boolean;
  note?: string;
  cropImage?: CropOptions;
  defaultLinks?: string[] | string;
}) => {
  const { control, setError, clearErrors } = useFormContext();

  let normalizedDefaultLinks: string[] = [];

  try {
    if (Array.isArray(defaultLinks)) {
      normalizedDefaultLinks = defaultLinks;
    } else if (typeof defaultLinks === "string") {
      const parsed = JSON.parse(defaultLinks);
      if (Array.isArray(parsed)) {
        normalizedDefaultLinks = parsed;
      } else {
        normalizedDefaultLinks = [defaultLinks];
      }
    }
  } catch {
    normalizedDefaultLinks = [];
  }

  return (
    <EditorInput name={name} inputTitle={title} details={details}>
     {normalizedDefaultLinks && normalizedDefaultLinks.length > 0 && (
        <div className="mb-6">
          <h3 className="text-base font-semibold text-gray-700 mb-3 flex items-center gap-2">
            📁 Existing Program Logo
          </h3>
          <ul className="grid gap-4">
            {normalizedDefaultLinks.map((url, index) => {
              if (typeof url !== "string" || !url) return null;

              const fileName = decodeURIComponent(
                url.split("pfps/")[1] || url.split("/").pop() || `Attachment-${index + 1}`
              );
              const extension = fileName.split(".").pop()?.toLowerCase() || "";
              const isImage = ["png", "jpg", "jpeg", "webp", "gif"].includes(extension);

              return (
                <li
                  key={index}
                  className="group flex flex-col md:flex-row items-center justify-between bg-white border border-gray-200 shadow-sm rounded-xl px-4 py-3 hover:bg-blue-50 transition-all gap-4"
                >
                  <div className="flex items-center gap-4 w-full overflow-hidden">
                    <div className="bg-blue-100 text-blue-700 p-2 rounded-full shrink-0">
                      <FiPaperclip size={18} />
                    </div>

                    <div className="flex flex-col overflow-hidden w-full">
                      <span className="text-sm font-medium text-gray-800 truncate">
                        {fileName}
                      </span>
                      <span className="text-xs text-gray-500 uppercase">{extension}</span>
                    </div>
                  </div>

                  <div className="flex flex-col md:flex-row items-center gap-4 mt-2 md:mt-0">
                    {isImage && (
                        <a
                        href={url}
                        target="_blank"
                        rel="noopener noreferrer"
                        download
                        className="text-sm font-medium text-blue-600 group-hover:underline hover:scale-105 transition-transform"
                      >
                      <img
                        src={url}
                        alt={`Preview of ${fileName}`}
                        className="h-16 rounded-lg object-contain border"
                      />
                       </a>
                    )}
                   
                  </div>
                </li>
              );
            })}
          </ul>
        </div>
      )}

     
      <Controller
        name={name}
        control={control}
        render={({
          field: { value, onChange, onBlur },
          fieldState: { error }
        }) => (
          <AttachmentsInput
            accept={accept}
            multiple={multiple}
            placeholder={placeholder}
            value={value}
            note={note}
            error={error !== undefined}
            onChange={value => {
              clearErrors(name);
              onChange(value && value.length > 0 ? value : undefined);
            }}
            onBlur={onBlur}
            setError={message => setError(name, { message })}
            cropImage={cropImage}
          />
        )}
      />
    </EditorInput>
  );
};

export default ProgramEditorLogo;
