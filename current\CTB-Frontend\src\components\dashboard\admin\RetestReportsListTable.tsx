import React from "react";
import { FaCheckCircle, FaTimesCircle } from "react-icons/fa";
import { cropSentence } from "../../../utils/formatText";

type RetestReport = {
  retestId: number;
  reportTitle: string;
  status: string;
  latestComment?: string;
};

type RetestReportsListTableProps = {
  retestReportsList: RetestReport[];
};

const RetestReportsListTable: React.FC<RetestReportsListTableProps> = ({
  retestReportsList
}) => {
  return (
    <div className="w-full overflow-hidden rounded-xl border border-gray-100 bg-white shadow-lg">
      <div className="sticky top-0 z-10 flex items-center justify-between bg-blue-700 p-6">
        <div className="flex items-center space-x-2">
          <FaCheckCircle className="h-4 w-4 text-green-400" />
          <h2 className="text-sm font-bold text-white">Actionable Retests</h2>
        </div>
        <div className="text-sm font-medium text-white">
          Total Retests: {retestReportsList.length}
        </div>
      </div>

      <div
        className="scrollbar-thin scrollbar-track-gray-100 scrollbar-thumb-gray-300 w-full overflow-y-auto"
        style={{
          maxHeight: "calc(4 * 85px + 20px)",
          scrollbarWidth: "thin",
          scrollbarColor: "#D1D5DB #F3F4F6"
        }}
      >
        <table className="w-full">
          <thead className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50">
            <tr>
              <th className="px-6 py-4 text-center text-xs font-semibold uppercase tracking-wider text-gray-600">
                Title
              </th>
              <th className="px-6 py-4 text-center text-xs font-semibold uppercase tracking-wider text-gray-600">
                Status
              </th>
            </tr>
          </thead>
          <tbody>
            {retestReportsList.length === 0 ? (
              <tr>
                <td colSpan={2} className="py-8 text-center text-gray-500">
                  <div className="flex flex-col items-center space-y-4">
                    <FaTimesCircle className="h-12 w-12 text-gray-300" />
                    <p className="text-lg">No retest reports found</p>
                  </div>
                </td>
              </tr>
            ) : (
              [...retestReportsList]
                .sort((a, b) => b.retestId - a.retestId)
                .map((retest) => (
                  <tr
                    key={retest.retestId}
                    className="group border-b border-gray-100 transition-colors duration-200 last:border-b-0 hover:bg-blue-50"
                  >
                    <td className="px-6 py-4 text-center">
                      <a
                        href={`/dashboard/retests/${retest.retestId}`}
                        className="block text-sm font-medium text-gray-900 transition-colors group-hover:text-blue-600"
                      >
                        {cropSentence(retest.reportTitle, 35)}
                      </a>
                    </td>
                    <td className="px-6 py-4 text-center">
                      <div className="flex items-center justify-center space-x-2">
                        {retest.status === "Fix Verified" ? (
                          <FaCheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <FaTimesCircle className="h-4 w-4 text-red-500" />
                        )}
                        <span className="text-sm text-gray-700">{retest.status}</span>
                      </div>
                    </td>
                  </tr>
                ))
            )}
          </tbody>
        </table>

      </div>
    </div>
  );
};

export default RetestReportsListTable;
