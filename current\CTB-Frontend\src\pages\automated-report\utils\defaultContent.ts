import { ReportData } from '../types/report.types';
import { ProgramDetail, DEFAULT_REPORT_HTML_CONTENT } from '../types';

// Only allow keys that exist in DEFAULT_REPORT_HTML_CONTENT
export type HtmlSectionField = keyof typeof DEFAULT_REPORT_HTML_CONTENT;

export function getDefaultFieldContent(field: HtmlSectionField, reportData: ReportData | null): string {
  if (DEFAULT_REPORT_HTML_CONTENT[field]) {
    if (field === 'disclaimer' && reportData?.company_name) {
      return DEFAULT_REPORT_HTML_CONTENT.disclaimer.replace('...', reportData.company_name);
    }
    return DEFAULT_REPORT_HTML_CONTENT[field];
  }
      return '';
  }

export const getDefaultContent = () => ({
  report_title: '',
  company_name: '',
  document_number: '',
  version_number: '',
  current_date: new Date().toISOString().split('T')[0],
  revision_date: '',
  prepared_by: '',
  reviewed_by: '',
  approved_by: '',
  test_lead: '',
  version_description: '',
  executive_summary: '',
  scope: '',
  methodology: { web: true, network: false, mobile: false },
  objectives: 'The objective of this assessment was to validate the overall security posture of the in-scope systems from a security perspective. It included determining the application\'s ability to resist common attack patterns and identifying vulnerable areas in the internal or external interfaces that might be exploited by a malicious user.',
  findings: '',
  recommendations: '',
  conclusion: '',
  disclaimer: '',
  target_details: [],
  reports_list: [],
  open_close_counts_by_severity: {
    Critical: { Open: 0, Closed: 0, Total: 0 },
    High: { Open: 0, Closed: 0, Total: 0 },
    Medium: { Open: 0, Closed: 0, Total: 0 },
    Low: { Open: 0, Closed: 0, Total: 0 }
  },
  total_open: 0,
  total_closed: 0,
  total_findings: 0,
  date_of_request: '',
  vulnerability_ratings: {
    critical: 'Exploitation is straightforward and usually results in a system-level compromise. It is advised to form a plan of action and patch it immediately.',
    high: 'Exploitation is more difficult but could cause elevated privileges and potentially a loss of data or downtime. It is advised to form a plan of action and patch as soon as possible.',
    medium: 'Vulnerabilities exist but are not exploitable or require extra steps such as social engineering. It is advised to form a plan of action and patch after high-priority issues have been resolved.',
    low: 'Vulnerabilities are non-exploitable but would reduce an organization\'s attack surface. It is advised to form a plan of action and patch during the next maintenance window.'
  }
}); 