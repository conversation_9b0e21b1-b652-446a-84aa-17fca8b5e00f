import React from 'react';
import { Image, Text, View } from '@react-pdf/renderer';

// ============= Types =============
export type PdfElement =
  | { type: 'text'; content: string; style?: 'bold' | 'italic' }
  | { type: 'heading'; level: number; content: string }
  | { type: 'image'; src: string | null; alt?: string; width?: string | number; height?: string | number }
  | { type: 'paragraph'; children: PdfElement[] }
  | { type: 'list'; ordered: boolean; items: string[] }
  | { type: 'break' };

// ============= IMAGE PROCESSING UTILITIES =============
class ImageProcessor {
  private cache: Map<string, string | null>;
  private processingQueue: Map<string, Promise<string | null>>;

  constructor() {
    this.cache = new Map();
    this.processingQueue = new Map();
  }

  async processImage(imageUrl: string, options: { width?: number; height?: number } = {}): Promise<string | null> {
    if (!imageUrl) return null;
    if (this.cache.has(imageUrl)) {
      return this.cache.get(imageUrl) ?? null;
    }
    if (this.processingQueue.has(imageUrl)) {
      return this.processingQueue.get(imageUrl) ?? null;
    }
    const processingPromise = this._processImageInternal(imageUrl, options);
    this.processingQueue.set(imageUrl, processingPromise);
    try {
      const result = await processingPromise;
      this.cache.set(imageUrl, result);
      return result;
    } finally {
      this.processingQueue.delete(imageUrl);
    }
  }

  async _processImageInternal(imageUrl: string, options: { width?: number; height?: number }): Promise<string | null> {
    if (imageUrl.startsWith('data:')) {
      return imageUrl;
    }
    const canvasResult = await this._tryCanvasMethod(imageUrl);
    if (canvasResult) return canvasResult;
    const proxyResult = await this._tryProxyMethod(imageUrl);
    if (proxyResult) return proxyResult;
    return this._generatePlaceholder(imageUrl, options);
  }

  async _tryCanvasMethod(imageUrl: string): Promise<string | null> {
    return new Promise((resolve) => {
      if (typeof window === 'undefined') return resolve(null);
      const img = new window.Image();
      const timeoutId = setTimeout(() => resolve(null), 5000);
      img.onload = () => {
        clearTimeout(timeoutId);
        try {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          if (!ctx) return resolve(null);
          const maxWidth = 800;
          const maxHeight = 600;
          let width = img.width;
          let height = img.height;
          if (width > maxWidth || height > maxHeight) {
            const ratio = Math.min(maxWidth / width, maxHeight / height);
            width *= ratio;
            height *= ratio;
          }
          canvas.width = width;
          canvas.height = height;
          ctx.drawImage(img, 0, 0, width, height);
          const dataURL = canvas.toDataURL('image/jpeg', 0.8);
          resolve(dataURL);
        } catch (error) {
          resolve(null);
        }
      };
      img.onerror = () => {
        clearTimeout(timeoutId);
        resolve(null);
      };
      img.crossOrigin = 'anonymous';
      img.src = imageUrl;
    });
  }

  async _tryProxyMethod(imageUrl: string): Promise<string | null> {
    try {
      const proxyUrl = `https://api.allorigins.win/raw?url=${encodeURIComponent(imageUrl)}`;
      const response = await fetch(proxyUrl, { method: 'GET' });
      if (!response.ok) return null;
      const blob = await response.blob();
      if (blob.size === 0) return null;
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result as string);
        reader.onerror = () => resolve(null);
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      return null;
    }
  }

  _generatePlaceholder(originalUrl: string, options: { width?: number; height?: number } = {}): string | null {
    if (typeof window === 'undefined') return null;
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return null;
    const width = options.width || 300;
    const height = options.height || 200;
    canvas.width = width;
    canvas.height = height;
    const gradient = ctx.createLinearGradient(0, 0, width, height);
    gradient.addColorStop(0, '#f8f9fa');
    gradient.addColorStop(1, '#e9ecef');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
    ctx.strokeStyle = '#dee2e6';
    ctx.lineWidth = 2;
    ctx.strokeRect(1, 1, width - 2, height - 2);
    ctx.fillStyle = '#6c757d';
    ctx.font = '24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('🖼️', width / 2, height / 2 - 20);
    ctx.font = '12px Arial';
    ctx.fillStyle = '#495057';
    ctx.fillText('Image not available', width / 2, height / 2 + 10);
    try {
      const domain = new URL(originalUrl).hostname;
      ctx.font = '10px Arial';
      ctx.fillStyle = '#6c757d';
      ctx.fillText(domain, width / 2, height / 2 + 25);
    } catch {}
    return canvas.toDataURL('image/png');
  }

  clearCache(): void {
    this.cache.clear();
  }
}

const imageProcessor = new ImageProcessor();

// ============= HTML PARSING =============
export async function parseHtmlToElements(htmlString: string): Promise<PdfElement[]> {
  if (!htmlString) return [];
  if (typeof window === 'undefined' || typeof DOMParser === 'undefined') {
    // SSR fallback: strip tags, return as text
    return [{ type: 'text', content: htmlString.replace(/<[^>]+>/g, '') }];
  }
  const parser = new window.DOMParser();
  const doc = parser.parseFromString(htmlString, 'text/html');
  const elements: PdfElement[] = [];

  const processNodeAsync = async (node: Node, container: PdfElement[] = elements): Promise<void> => {
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent?.trim() ?? '';
      if (text) {
        container.push({ type: 'text', content: text });
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      const el = node as HTMLElement;
      switch (el.tagName.toLowerCase()) {
        case 'img': {
          const src = el.getAttribute('src');
          const width = el.getAttribute('width');
          const height = el.getAttribute('height');
          const processedSrc = await imageProcessor.processImage(src ?? '', {
            width: width ? parseInt(width) : undefined,
            height: height ? parseInt(height) : undefined
          });
          container.push({
            type: 'image',
            src: processedSrc,
            alt: el.getAttribute('alt') || '',
            width: width ?? undefined,
            height: height ?? undefined,
          });
          break;
        }
        case 'p': {
          const paragraph: PdfElement = { type: 'paragraph', children: [] };
          for (const child of Array.from(el.childNodes)) {
            await processNodeAsync(child, paragraph.children);
          }
          if (paragraph.children.length > 0) {
            container.push(paragraph);
          }
          break;
        }
        case 'h1':
        case 'h2':
        case 'h3':
        case 'h4':
        case 'h5':
        case 'h6':
          container.push({
            type: 'heading',
            level: parseInt(el.tagName.substring(1)),
            content: el.textContent?.trim() ?? ''
          });
          break;
        case 'ul':
        case 'ol': {
          const list: PdfElement = {
            type: 'list',
            ordered: el.tagName.toLowerCase() === 'ol',
            items: []
          };
          for (const li of Array.from(el.querySelectorAll('li'))) {
            list.items.push(li.textContent?.trim() ?? '');
          }
          if (list.items.length > 0) {
            container.push(list);
          }
          break;
        }
        case 'br':
          container.push({ type: 'break' });
          break;
        default:
          for (const child of Array.from(el.childNodes)) {
            await processNodeAsync(child, container);
          }
          break;
      }
    }
  };

  for (const child of Array.from(doc.body.childNodes)) {
    await processNodeAsync(child);
  }
  return elements;
}

// ============= PDF RENDERER =============
// renderPdfElements returns an array of React elements for use directly in PDF rendering. Do not wrap its output in <Text>.
// If you need a plain text fallback, use renderPdfElementsPlainText.
export function renderPdfElements(elements: PdfElement[]): (JSX.Element | null)[] {
  return elements.map((element: PdfElement, index: number): JSX.Element | null => {
    if (element.type === 'text') {
      return (
        <Text key={index}>{element.content}</Text>
      );
    } else if (element.type === 'heading') {
      const fontSize = element.level === 1 ? 24 : element.level === 2 ? 20 : 16;
      const fontWeight = 'bold' as const;
      const marginBottom = element.level === 1 ? 16 : element.level === 2 ? 12 : 10;
      const marginTop = element.level === 1 ? 20 : element.level === 2 ? 16 : 12;
      return (
        <Text key={index} style={{ fontSize, fontWeight, marginBottom, marginTop }}>{element.content}</Text>
      );
    } else if (element.type === 'image') {
      if (!element.src) return null;
      return (
        <Image
          key={index}
          style={{
            maxWidth: 400,
            maxHeight: 200,
            marginTop: 20,
            marginBottom: 20,
            width: typeof element.width === 'number' ? element.width : (element.width ? parseInt(element.width as string) : undefined),
            height: typeof element.height === 'number' ? element.height : (element.height ? parseInt(element.height as string) : undefined)
          }}
          src={element.src}
        />
      );
    } else if (element.type === 'paragraph') {
      // Check if paragraph contains only an image
      const hasOnlyImage = element.children.length === 1 && element.children[0].type === 'image';
      return (
        <View key={index} style={{ marginBottom: hasOnlyImage ? -80 : 4 }}>
          {renderPdfElements(element.children)}
        </View>
      );
    } else if (element.type === 'list') {
      return (
        <View key={index} style={{ marginBottom: 12, paddingLeft: 20 }}>
          {element.items.map((item: string, itemIndex: number) => (
            <View key={itemIndex} style={{ flexDirection: 'row', marginBottom: 4 }}>
              <Text style={{ width: 15, marginRight: 5 }}>
                {element.ordered ? `${itemIndex + 1}.` : '•'}
              </Text>
              <Text>{item}</Text>
            </View>
          ))}
        </View>
      );
    } else if (element.type === 'break') {
      return (
        <Text key={index}>{''}</Text>
      );
    } else {
      return null;
    }
  });
}

export function renderPdfElementsPlainText(input: string | string[]): JSX.Element {
  return <Text>{Array.isArray(input) ? input.join('\n') : input}</Text>;
}

export { imageProcessor };