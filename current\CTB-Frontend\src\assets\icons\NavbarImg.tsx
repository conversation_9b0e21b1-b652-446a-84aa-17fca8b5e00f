const NavbarImg = ({ className }: { className?: string }) => (
    <svg
      width="170"
      height="150"
      viewBox="0 0 40 45"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      className={className}
    >
      <rect width="50" height="45" fill="url(#pattern0)" />
      <defs>
        <pattern
          id="pattern0"
          patternUnits="userSpaceOnUse"
          width="45"
          height="45"
        >
          <image
            id="image0_1_257"
            width="45"
            height="45"
            xlinkHref="https://i.ibb.co/N2fBxxf/Screenshot-2024-12-11-104319.png"   
          />
        </pattern>
      </defs>
    </svg>
  );
  
  export default NavbarImg;
  