"use strict";

module.exports = {
  async up(queryInterface, Sequelize) {
    // Populate program_name for existing automated_reports
    // This query will set program_name to the title of the first program in program_ids array
    await queryInterface.sequelize.query(`
      UPDATE automated_reports ar
      SET program_name = (
        SELECT p.program_title 
        FROM programs p 
        WHERE p.program_id = JSON_EXTRACT(ar.program_ids, '$[0]')
        LIMIT 1
      )
      WHERE ar.program_name IS NULL 
      AND JSON_LENGTH(ar.program_ids) > 0;
    `);

    // Populate program_name for existing program_reports
    // This query will get the program name from the associated automated_report
    await queryInterface.sequelize.query(`
      UPDATE program_reports pr
      JOIN automated_reports ar ON pr.automated_report_id = ar.id
      SET pr.program_name = ar.program_name
      WHERE pr.program_name IS NULL 
      AND ar.program_name IS NOT NULL;
    `);

    // For program_reports that still don't have program_name,
    // try to get it directly from the first program in the automated_report
    await queryInterface.sequelize.query(`
      UPDATE program_reports pr
      JOIN automated_reports ar ON pr.automated_report_id = ar.id
      SET pr.program_name = (
        SELECT p.program_title 
        FROM programs p 
        WHERE p.program_id = JSON_EXTRACT(ar.program_ids, '$[0]')
        LIMIT 1
      )
      WHERE pr.program_name IS NULL 
      AND JSON_LENGTH(ar.program_ids) > 0;
    `);
  },

  async down(queryInterface, Sequelize) {
    // Clear program_name fields (optional - could be left as is)
    await queryInterface.sequelize.query(`
      UPDATE automated_reports SET program_name = NULL;
    `);

    await queryInterface.sequelize.query(`
      UPDATE program_reports SET program_name = NULL;
    `);
  }
};
