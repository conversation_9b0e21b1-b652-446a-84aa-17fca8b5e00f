import React from 'react';
import { Page, View, Text, StyleSheet } from '@react-pdf/renderer';
import { ReportData } from '../../types/report.types';
import { useSectionPages } from '../SectionPageContext';

interface KeyFindingsPageProps {
  reportData: ReportData;
  sectionId?: string;
}

const DEFAULT_COMPANY = "Capture The Bug";

const styles = StyleSheet.create({
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    fontSize: 10,
    color: 'grey',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  footerLeft: {
    textAlign: 'left',
    flex: 1,
  },
  footerRight: {
    textAlign: 'right',
    flex: 1,
  },
});

const KeyFindingsPage: React.FC<KeyFindingsPageProps> = ({ reportData, sectionId }) => {
  const { updateSectionPage } = useSectionPages();
  return (
    <Page size="A4" id={sectionId} style={{
      flexDirection: 'column',
      backgroundColor: '#ffffff',
      padding: '20mm 15mm',
      fontFamily: 'Helvetica',
      fontSize: 12,
    }}>
      <View style={{ paddingHorizontal: 14, paddingTop: 10, flexDirection: 'column', flex: 1 }}>
        <View style={{ flex: 1}}>
          {/* No title here, just two paragraphs */}
          {reportData.key_findings ? (
            <Text style={{ fontSize: 12, lineHeight: 1.4, marginBottom: 18, color: '#374151', textAlign: 'justify' }}>{reportData.key_findings}</Text>
          ) : (
            <>
              <Text style={{ fontSize: 12, lineHeight: 1.4, marginBottom: 10, color: '#374151', textAlign: 'justify' }}>
                {(reportData.branding_company || DEFAULT_COMPANY)}'s thorough assessment identified <Text style={{ fontWeight: 'bold' }}>{reportData.total_findings}</Text> findings, with <Text style={{ fontWeight: 'bold', color: '#8b0000' }}>{reportData.open_close_counts_by_severity?.Critical?.Total || 0}</Text> categorized as <Text style={{ fontWeight: 'bold', color: '#8b0000' }}>Critical Severity</Text>, <Text style={{ fontWeight: 'bold', color: '#ff4500' }}>{reportData.open_close_counts_by_severity?.High?.Total || 0}</Text> categorized as <Text style={{ fontWeight: 'bold', color: '#ff4500' }}>High Severity</Text>, <Text style={{ fontWeight: 'bold', color: '#ffd700' }}>{reportData.open_close_counts_by_severity?.Medium?.Total || 0}</Text> categorized as <Text style={{ fontWeight: 'bold', color: '#ffd700' }}>Medium Severity</Text> and <Text style={{ fontWeight: 'bold', color: '#32cd32' }}>{reportData.open_close_counts_by_severity?.Low?.Total || 0}</Text> as <Text style={{ fontWeight: 'bold', color: '#32cd32' }}>Low Severity</Text>. During the assessment, all critical and high vulnerabilities were reported to the {reportData.company_name} team, and the client addressed the reported vulnerabilities concurrently.
              </Text>
              <Text style={{ fontSize: 12, lineHeight: 1.4, marginBottom: 18, color: '#374151', textAlign: 'justify' }}>
              {(reportData.branding_company || DEFAULT_COMPANY)} team has documented the identified vulnerabilities along with their current statuses in the key findings section of this report. Prompt action is recommended to strengthen the application's overall security posture.
              </Text>
            </>
          )}
          <View style={{
            borderRadius: 10,
            borderWidth: 1,
            borderColor: '#d1d5db',
            marginTop: 32,
            marginBottom: 16,
            width: '90%',
            alignSelf: 'center',
            backgroundColor: '#fff',
            overflow: 'hidden',
          }}>
            <View style={{
              flexDirection: 'row',
              backgroundColor: '#2563eb',
              borderTopLeftRadius: 10,
              borderTopRightRadius: 10,
            }}>
              <Text style={{
                padding: 14,
                textAlign: 'left',
                fontWeight: 'bold',
                width: '25%',
                color: '#fff',
                fontSize: 12,
                lineHeight: 1.4,
              }}>Severity</Text>
              <Text style={{
                padding: 14,
                textAlign: 'center',
                fontWeight: 'bold',
                width: '25%',
                color: '#fff',
                fontSize: 12,
                lineHeight: 1.4,
              }}>Open</Text>
              <Text style={{
                padding: 14,
                textAlign: 'center',
                fontWeight: 'bold',
                width: '25%',
                color: '#fff',
                fontSize: 12,
                lineHeight: 1.4,
              }}>Closed</Text>
              <Text style={{
                padding: 14,
                textAlign: 'center',
                fontWeight: 'bold',
                width: '25%',
                color: '#fff',
                fontSize: 12,
                lineHeight: 1.4,
              }}>Total</Text>
            </View>
            {/* Table Rows */}
            {[
              {
                label: 'Critical',
                color: '#dc2626',
                bg: '#fef2f2',
                value: '!',
                pillBg: '#dc2626',
                pillColor: '#fff',
                data: reportData.open_close_counts_by_severity?.Critical || {},
              },
              {
                label: 'High',
                color: '#ea580c',
                bg: '#fff7ed',
                value: 'H',
                pillBg: '#ea580c',
                pillColor: '#fff',
                data: reportData.open_close_counts_by_severity?.High || {},
              },
              {
                label: 'Medium',
                color: '#eab308',
                bg: '#fef9c3',
                value: 'M',
                pillBg: '#eab308',
                pillColor: '#fff',
                data: reportData.open_close_counts_by_severity?.Medium || {},
              },
              {
                label: 'Low',
                color: '#16a34a',
                bg: '#dcfce7',
                value: 'L',
                pillBg: '#16a34a',
                pillColor: '#fff',
                data: reportData.open_close_counts_by_severity?.Low || {},
              },
            ].map((row, idx) => (
              <View key={row.label} style={{
                flexDirection: 'row',
                backgroundColor: row.bg,
                borderTopWidth: idx === 0 ? 0 : 1,
                borderTopColor: '#e5e7eb',
              }}>
                <View style={{
                  padding: 12,
                  width: '25%',
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                  <View style={{
                    minWidth: 22,
                    minHeight: 22,
                    backgroundColor: row.pillBg,
                    borderRadius: 11,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: 8,
                  }}>
                    <Text style={{
                      color: row.pillColor,
                      fontWeight: 'bold',
                      fontSize: 10,
                      lineHeight: 1.4,
                    }}>{row.value}</Text>
                  </View>
                  <Text style={{
                    fontWeight: '600',
                    color: row.color,
                    fontSize: 12,
                    lineHeight: 1.4,
                  }}>{row.label}</Text>
                </View>
                <Text style={{
                  padding: 12,
                  width: '25%',
                  textAlign: 'center',
                  fontSize: 12,
                  lineHeight: 1.4,
                }}>{row.data.Open || 0}</Text>
                <Text style={{
                  padding: 12,
                  width: '25%',
                  textAlign: 'center',
                  fontSize: 12,
                  lineHeight: 1.4,
                }}>{row.data.Closed || 0}</Text>
                <Text style={{
                  padding: 12,
                  width: '25%',
                  textAlign: 'center',
                  fontWeight: 'bold',
                  fontSize: 12,
                  lineHeight: 1.4,
                }}>{row.data.Total || 0}</Text>
              </View>
            ))}
            {/* Total Row */}
            <View style={{
              flexDirection: 'row',
              backgroundColor: '#dbeafe',
              borderTopWidth: 1,
              borderTopColor: '#93c5fd',
            }}>
              <Text style={{
                padding: 12,
                width: '25%',
                fontWeight: 'bold',
                color: '#2563eb',
                fontSize: 12,
                lineHeight: 1.4,
              }}>TOTAL</Text>
              <Text style={{
                padding: 12,
                width: '25%',
                fontWeight: 'bold',
                textAlign: 'center',
                color: '#2563eb',
                fontSize: 12,
                lineHeight: 1.4,
              }}>{reportData.total_open || 0}</Text>
              <Text style={{
                padding: 12,
                width: '25%',
                fontWeight: 'bold',
                textAlign: 'center',
                color: '#2563eb',
                fontSize: 12,
                lineHeight: 1.4,
              }}>{reportData.total_closed || 0}</Text>
              <Text style={{
                padding: 12,
                width: '25%',
                fontWeight: 'bold',
                textAlign: 'center',
                color: '#2563eb',
                fontSize: 12,
                lineHeight: 1.4,
              }}>{reportData.total_findings || 0}</Text>
            </View>
          </View>
          <Text style={{ fontSize: 12, color: '#64748b', textAlign: 'center', marginTop: 8, lineHeight: 1.4 }}>
            Table 1: Scope of Work
          </Text>
        </View>
      </View>
      <View style={styles.footer} fixed>
        <Text style={styles.footerLeft}>{reportData.document_number || 'Document Number'}</Text>
        <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
      </View>
      <Text style={{ display: 'none' }} render={({ pageNumber }) => { updateSectionPage('KeyFindings', pageNumber); return ''; }} fixed />
    </Page>
  );
};

export default KeyFindingsPage; 