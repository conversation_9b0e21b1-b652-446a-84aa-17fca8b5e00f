const BrowserCodeIcon = ({ className, color = "#3056D3" }: { className?: string; color?: string }) => (
    <svg
      width="40"
      height="40"
      viewBox="0 0 45 45"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* Rounded rectangle (browser window) */}
      <rect x="8" y="8" width="29" height="25" rx="3" fill="none" stroke={color} strokeWidth="3" />
  
      {/* Top bar of browser */}
      <rect x="8" y="8" width="29" height="8" fill={color} />
  
      {/* Circles in the top bar */}
      <circle cx="12" cy="11.5" r="1.5" fill="white" />
      <circle cx="16" cy="11.5" r="1.5" fill="white" />
  
    {/* Code brackets */}
      <polyline points="18,22 15,24.5 18,27" stroke={color} strokeWidth="2" fill="none" />
      <polyline points="26,22 29,24.5 26,27" stroke={color} strokeWidth="2" fill="none" />
    </svg>
  );
  
  export default BrowserCodeIcon;
  