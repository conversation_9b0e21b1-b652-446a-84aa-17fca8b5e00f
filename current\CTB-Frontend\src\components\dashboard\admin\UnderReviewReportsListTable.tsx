import React from "react";
import { FaExclamationTriangle } from "react-icons/fa";
import { cropSentence } from "../../../utils/formatText";

type UnderReviewReport = {
  reportId: number;
  reportName: string;
  severityCategory: string;
  submittedDate: string;
};

type UnderReviewReportsListTableProps = {
  underReviewReportsList: UnderReviewReport[];
};

const UnderReviewReportsListTable: React.FC<
  UnderReviewReportsListTableProps
> = ({ underReviewReportsList }) => {
  return (
    <div className="w-full overflow-hidden rounded-xl border border-gray-100 bg-white shadow-lg">
      <div className="sticky top-0 z-10 flex items-center justify-between bg-blue-700 p-6">
        <div className="flex items-center space-x-2">
          <FaExclamationTriangle className="h-4 w-4 text-yellow-400" />
          <h2 className="text-sm font-bold text-white">Actionable Reports</h2>
        </div>
        <div className="text-sm font-medium text-white">
          Total Reports: {underReviewReportsList.length}
        </div>
      </div>

      <div
        className="scrollbar-thin scrollbar-track-gray-100 scrollbar-thumb-gray-300 w-full overflow-y-auto"
        style={{
          maxHeight: "calc(4 * 85px + 20px)",
          scrollbarWidth: "thin",
          scrollbarColor: "#D1D5DB #F3F4F6"
        }}
      >
        <table className="w-full">
          <thead className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50">
            <tr>
              <th className="px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider text-gray-600">
                Title
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider text-gray-600">
                Severity
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider text-gray-600">
                Date
              </th>
            </tr>
          </thead>
          <tbody>
            {underReviewReportsList.length === 0 ? (
              <tr>
                <td colSpan={3} className="py-8 text-center text-gray-500">
                  <div className="flex flex-col items-center space-y-4">
                    <FaExclamationTriangle className="h-12 w-12 text-gray-300" />
                    <p className="text-lg">No reports under review found</p>
                  </div>
                </td>
              </tr>
            ) : [...underReviewReportsList]
            .sort((a, b) => b.reportId - a.reportId)  
            .map(report => ((
                <tr
                  key={report.reportId}
                  className="group border-b border-gray-100 transition-colors duration-200 last:border-b-0 hover:bg-blue-50"
                >
                  <td className="px-6 py-4">
                    <a
                      href={`/dashboard/reports/${report.reportId}`}
                      className="block text-sm font-medium text-gray-900 transition-colors group-hover:text-blue-600"
                    >
                      {cropSentence(report.reportName,30)}
                    </a>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2">
                      <FaExclamationTriangle
                        className={`h-4 w-4 ${
                          report.severityCategory === "CRITICAL"
                            ? "text-red-500"
                            : report.severityCategory === "HIGH"
                            ? "text-orange-500"
                            : report.severityCategory === "MEDIUM"
                            ? "text-yellow-500"
                            : "text-green-500"
                        }`}
                      />
                      <span className="text-sm text-gray-700">
                        {report.severityCategory}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {new Date(report.submittedDate).toLocaleDateString()}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default UnderReviewReportsListTable;
