import React from "react";
import { Link } from "react-router-dom";
import { cropSentence } from "../../../utils/formatText";

type ProgramDetail = {
  programId: number;
  programName: string;
  programType: string | null;
  programPfp: string | null;
  programStartDate: string | null;
  programEndDate: string | null;
};

type RecentProgramDetailsTableProps = {
  programDetails: ProgramDetail[];
};

const RecentProgramDetailsTable: React.FC<RecentProgramDetailsTableProps> = ({
  programDetails
}) => {
  const calculateProgress = (
    startDate: string | null,
    endDate: string | null
  ): number => {
    if (!startDate || !endDate) return 0;

    const start = new Date(startDate).getTime();
    const end = new Date(endDate).getTime();
    const now = new Date().getTime();

    const totalDuration = end - start;
    const elapsedDuration = now - start;

    const progress = (elapsedDuration / totalDuration) * 100;
    return Math.min(100, Math.max(0, progress));
  };

  const formatDate = (dateString: string | null): string => {
    if (!dateString) return "TBD";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit"
      });
    } catch {
      return "TBD";
    }
  };

  return (
    <div className="w-full overflow-x-auto shadow-sm">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-100">
          <tr>
            {["Name", "Type", "End Date", "Progress"].map(header => (
              <th
                key={header}
                className="px-6 py-3 text-left text-xs font-semibold uppercase tracking-wider text-gray-800"
              >
                {header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 bg-white">
          {programDetails.length === 0 ? (
            <tr>
              <td
                colSpan={4}
                className="px-6 py-4 text-center text-sm text-gray-500"
              >
                No programs found
              </td>
            </tr>
          ) : (
            programDetails.map(program => (
              <tr
                key={program.programId}
                className="cursor-pointer transition-colors duration-150 ease-in-out hover:bg-gray-50"
              >
                <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                  <Link
                    to={`/dashboard/programs/${program.programId}`}
                    className="block h-full w-full font-medium hover:text-ctb-blue-400"
                  >
                    {cropSentence(program.programName)}
                  </Link>
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-600">
                  {program.programType}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-600">
                  {formatDate(program.programEndDate)}
                </td>
                <td className="whitespace-nowrap px-6 py-4">
                  {program.programType !== "Bug Bounty" ? (
                    <div className="h-2 w-full overflow-hidden rounded-full bg-gray-200">
                      <div
                        className="h-full rounded-full bg-blue-500 transition-all duration-300 ease-in-out"
                        style={{
                          width: `${calculateProgress(
                            program.programStartDate,
                            program.programEndDate
                          )}%`
                        }}
                      />
                    </div>
                  ) : (
                    <div className="text-sm font-medium text-gray-600">TBD</div>
                  )}
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
};

export default RecentProgramDetailsTable;
