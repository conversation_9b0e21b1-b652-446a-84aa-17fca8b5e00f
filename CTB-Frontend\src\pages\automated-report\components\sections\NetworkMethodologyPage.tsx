import React from 'react';
import { Page, View, Text, StyleSheet } from '@react-pdf/renderer';

const accentColor = '#2563eb';
const cardBg = '#f8fafc';
const dividerColor = '#e5e7eb';

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: '20mm 15mm',
    fontFamily: 'Helvetica',
    fontSize: 12,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: accentColor,
    letterSpacing: 1.2,
    marginBottom: 6,
    textAlign: 'left',
  },
  divider: {
    height: 2,
    backgroundColor: dividerColor,
    marginBottom: 18,
    opacity: 0.7,
    borderRadius: 1,
  },
  intro: {
    fontSize: 13,
    color: '#374151',
    marginBottom: 28,
    lineHeight: 1.6,
    textAlign: 'justify',
    paddingHorizontal: 8,
  },
  phaseBlock: {
    backgroundColor: cardBg,
    borderRadius: 12,
    padding: 18,
    marginBottom: 28,
    borderWidth: 1,
    borderColor: dividerColor,
  },
  phaseName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: accentColor,
    marginBottom: 8,
    letterSpacing: 0.5,
  },
  phaseDesc: {
    fontSize: 13,
    color: '#374151',
    marginBottom: 10,
    lineHeight: 1.5,
    textAlign: 'justify',
  },
  phaseTechniques: {
    fontSize: 13,
    color: '#374151',
    fontWeight: 'bold',
    marginBottom: 4,
    marginTop: 2,
  },
  bulletList: {
    marginLeft: 12,
    marginBottom: 0,
    marginTop: 0,
  },
  bulletItem: {
    fontSize: 12,
    color: '#374151',
    marginBottom: 4,
    lineHeight: 1.5,
    paddingLeft: 4,
  },
  phaseSummary: {
    fontSize: 12,
    color: '#374151',
    marginTop: 10,
    textAlign: 'justify',
    fontStyle: 'italic',
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    fontSize: 10,
    color: 'grey',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  footerLeft: {
    textAlign: 'left',
    flex: 1,
  },
  footerRight: {
    textAlign: 'right',
    flex: 1,
  },
});

const phases = [
  {
    name: 'Pre-Engagement Analysis',
    description: `We gather background information and align with the client to ensure the engagement is well-defined and focused on the network’s critical components. This phase helps tailor our testing to the network’s architecture and business priorities.`,
    techniques: [
      `Review network diagrams, asset inventories, and documentation to understand the network’s topology, critical systems (e.g., domain controllers), and data flows.`,
      `Conduct stakeholder interviews to identify high-value assets (e.g., file servers, databases) and business requirements that may influence testing (e.g., avoiding downtime for production systems).`,
      `Analyze previous security assessments or incident reports (if provided) to identify known vulnerabilities or recurring issues, such as unpatched systems or weak credentials.`,
      `Assess the network’s environment, such as on-premises, cloud-based (e.g., AWS, Azure), or hybrid, to identify external dependencies like VPNs or cloud gateways.`,
      `Establish success criteria with the client, such as gaining domain admin access, accessing a specific server, or identifying a set number of critical vulnerabilities.`,
    ],
    summary: `This phase ensures a strategic approach, aligning testing with the client’s priorities and network risks.`,
  },
  {
    name: 'Planning and Scoping',
    description: `We define the scope, objectives, and rules of engagement to ensure testing aligns with client requirements and avoids disruptions. This phase establishes a controlled and compliant testing framework.`,
    techniques: [
      `Identify target IP ranges, domains, and network segments to be tested, ensuring all in-scope assets are documented (e.g., ***********/24, example.com).`,
      `Define constraints, such as avoiding DoS attacks, excluding critical systems (e.g., production servers), or limiting testing to specific hours to minimize impact.`,
      `Establish communication protocols for reporting issues, escalations, or incidents, including points of contact and regular status updates.`,
      `Review legal agreements, such as NDAs or authorization letters, to ensure compliance with regulations (e.g., PCI DSS for financial networks) and obtain written permission for testing.`,
      `Set up tools like Nmap, Nessus, Metasploit, and Wireshark, ensuring they are configured for the target network’s protocols and infrastructure (e.g., enabling safe checks in Nessus).`,
    ],
    summary: `This phase ensures a clear scope and prepares the team for effective, compliant testing.`,
  },
  {
    name: 'Reconnaissance',
    description: `We map the network to identify live hosts, services, and potential vulnerabilities, using both passive and active techniques to build a comprehensive profile of the network’s attack surface.`,
    techniques: [
      `Discover active IP addresses with Nmap, using ARP scans for local networks and ICMP sweeps for remote networks, identifying devices like servers, workstations, and IoT devices.`,
      `Perform TCP/UDP port scans (e.g., SYN, ACK, UDP scans) with Nmap to identify open ports and services, such as SSH (port 22), RDP (port 3389), or SMB (port 445).`,
      `Enumerate service versions and operating systems using Nmap scripts (e.g., nmap -sV) and banner grabbing, identifying details like Apache 2.4.29 or Windows Server 2016.`,
      `Detect firewalls, IDS/IPS, or load balancers by analyzing packet responses, such as TCP window size, TTL values, or ICMP replies, using techniques like idle scanning to evade detection.`,
      `Map network topology with Zenmap or SolarWinds, identifying segments, gateways, VLANs, and trust relationships between systems (e.g., domain controllers and workstations).`,
      `Check for insecure protocols, such as SNMPv1/2 with default community strings (e.g., “public”), Telnet, or SMBv1, by analyzing service banners or sniffing traffic (if permitted).`,
      `Enumerate wireless networks (if in scope) using airodump-ng to identify SSIDs, encryption types (e.g., WPA2), and rogue access points, focusing on weak PSKs or misconfigured APs.`,
    ],
    summary: `This phase provides a detailed map of the network’s attack surface, identifying entry points for targeted attacks while minimizing detection.`,
  },
  {
    name: 'Vulnerability Assessment',
    description: `We identify vulnerabilities in network services, protocols, and configurations, focusing on weaknesses that could be exploited to gain unauthorized access or disrupt services. This phase combines automated and manual techniques for accuracy.`,
    techniques: [
      `Scan for CVEs and misconfigurations using Nessus and OpenVAS, focusing on outdated software (e.g., MS17-010 for SMB, CVE-2019-0708 for RDP), unpatched systems, or exposed services.`,
      `Manually verify scan results to eliminate false positives, prioritizing high-severity issues like remote code execution (RCE) vulnerabilities or exposed administrative interfaces (e.g., Tomcat manager with default credentials).`,
      `Check for weak protocols, such as SNMPv1/2 with default community strings, FTP without encryption, or SMBv1, which is vulnerable to attacks like EternalBlue, using Nmap scripts (e.g., snmp-brute).`,
      `Identify misconfigurations, such as open ports that shouldn’t be exposed (e.g., port 445 on a public-facing server), weak SSL/TLS configurations (e.g., supporting TLS 1.0), or default credentials on devices like routers or IoT systems.`,
      `Assess patch management by cross-referencing service versions with CVE databases, identifying missing updates (e.g., BlueKeep for RDP), and checking for known exploits in Exploit-DB.`,
      `Test for weak credentials on services like SSH, RDP, or SMB using Hydra or Medusa, performing password spraying with common passwords (e.g., “Password123”) to avoid account lockouts.`,
      `Evaluate network devices (e.g., routers, switches) for vulnerabilities, such as exposed management interfaces (e.g., HTTP, Telnet), outdated firmware, or misconfigured SNMP settings, using tools like snmp-check.`,
    ],
    summary: `This phase pinpoints exploitable weaknesses in the network, providing a clear target list for exploitation.`,
  },
  {
    name: 'Exploitation',
    description: `We exploit identified vulnerabilities to gain unauthorized access to systems or data, simulating real-world attack scenarios to demonstrate the impact of weaknesses and the potential for initial compromise.`,
    techniques: [
      `Brute-force credentials on services like SSH, RDP, or SMB using Hydra or Medusa, focusing on common usernames (e.g., “admin”) and passwords (e.g., “password123”), while monitoring for account lockouts.`,
      `Use Metasploit to exploit known vulnerabilities, such as EternalBlue (MS17-010) for SMB to gain a shell, or BlueKeep (CVE-2019-0708) for RDP to execute arbitrary code, ensuring exploits are tailored to the target’s environment.`,
      `Escalate privileges on compromised hosts by exploiting local vulnerabilities, such as kernel exploits (e.g., Dirty COW for Linux) or misconfigured services (e.g., writable SYSTEM files on Windows), using tools like Meterpreter.`,
      `Target specific services, such as exploiting DNS servers for zone transfers to enumerate internal hosts, or attacking SMB shares with guest access to extract sensitive files (e.g., configuration files, credentials).`,
      `Develop custom payloads with msfvenom to evade detection, such as encoding payloads (e.g., msfvenom -p windows/meterpreter/reverse_tcp -e x86/shikata_ga_nai) or using obfuscated PowerShell scripts for Windows systems.`,
      `Exploit wireless networks (if in scope) by cracking WPA2 PSKs with aircrack-ng, performing deauthentication attacks to capture handshakes, or targeting rogue access points to intercept traffic.`,
      `Test for misconfigured network devices, such as routers with default credentials (e.g., admin/admin on Cisco devices), or switches with VLAN hopping vulnerabilities, using tools like Yersinia for layer 2 attacks.`,
    ],
    summary: `This phase demonstrates how vulnerabilities can be exploited to gain initial access, providing concrete evidence of risk and the potential for compromise.`,
  },
  {
    name: 'Lateral Movement',
    description: `We simulate an attacker moving laterally within the network to access critical systems or data, focusing on privilege escalation and network traversal to assess the network’s resilience to internal threats.`,
    techniques: [
      `Extract credentials from compromised hosts using Mimikatz, harvesting NTLM hashes, Kerberos tickets, or plaintext passwords from memory, and cracking hashes with Hashcat if necessary (e.g., cracking NTLM hashes with a wordlist).`,
      `Perform Pass-the-Hash or Kerberoasting attacks to access other systems, using stolen NTLM hashes to authenticate to Windows hosts, or requesting Kerberos service tickets to crack offline with John the Ripper.`,
      `Access shared resources like SMB shares or NFS mounts with stolen credentials, searching for sensitive data such as configuration files, credentials, or backups, and enumerating share permissions with tools like smbclient.`,
      `Test network segmentation by attempting to pivot to restricted segments, exploiting misconfigured firewall rules, VLAN hopping via switch spoofing (e.g., using Yersinia), or routing protocol vulnerabilities to access isolated systems.`,
      `Target internal services that may be less secure, such as Jenkins servers (port 8080), internal APIs, or databases (e.g., MySQL on port 3306), attempting SQL injection or default credential attacks (e.g., root/root on MySQL).`,
      `Use compromised hosts as pivot points to access other systems, employing Metasploit’s autoroute or SSH tunneling to reach internal networks, and enumerating Active Directory (if applicable) with tools like BloodHound.`,
      `Attempt domain escalation in Active Directory environments by exploiting misconfigured permissions (e.g., generic write on a GPO), abusing Kerberos delegation, or targeting domain controllers to gain domain admin privileges.`,
    ],
    summary: `This phase assesses the network’s ability to prevent lateral movement and protect critical systems from internal threats, highlighting the potential for widespread compromise.`,
  },
  {
    name: 'Persistence Testing',
    description: `We establish persistence to simulate a long-term attacker presence, focusing on maintaining access to the network even after initial detection or mitigation attempts, and testing the network’s ability to detect such threats.`,
    techniques: [
      `Create backdoors using Netcat or Meterpreter, setting up listeners (e.g., nc -lvp 4444) on compromised hosts to allow repeated access, and obfuscating traffic to evade detection.`,
      `Set up scheduled tasks on Windows (e.g., schtasks /create /tn "Backdoor" /tr "malicious.exe") or cron jobs on Linux (e.g., */5 * * * * /bin/malicious.sh) to execute malicious scripts at regular intervals.`,
      `Modify registry keys on Windows (e.g., adding a startup key in HKLM\Software\Microsoft\Windows\CurrentVersion\Run) or init scripts on Linux (e.g., /etc/init.d/malicious) to ensure persistence after reboot.`,
      `Plant malicious services that restart on reboot, such as creating a fake service on Windows (e.g., sc create MaliciousService binpath=malicious.exe) or a systemd service on Linux, to maintain access.`,
      `Test detection by monitoring logs for persistence-related activities, such as new scheduled tasks or unauthorized service creation, and assessing whether the client’s SIEM or monitoring tools generate alerts.`,
      `Hide persistence mechanisms by using legitimate system binaries (e.g., living-off-the-land techniques with PowerShell) or renaming malicious files to blend with normal system processes (e.g., naming a backdoor “svchost.exe”).`,
      `Evaluate the network’s patch management by re-exploiting vulnerabilities post-persistence to check if patches or mitigations have been applied, ensuring persistence isn’t disrupted by updates.`,
    ],
    summary: `This phase evaluates the network’s ability to detect and mitigate persistent threats, providing insights into long-term security risks and detection gaps.`,
  },
  {
    name: 'Post-Exploitation',
    description: `We evaluate the impact of a successful breach, focusing on data exfiltration, system compromise, and detection evasion, to demonstrate the real-world consequences of a network compromise and assess the network’s defensive capabilities.`,
    techniques: [
      `Exfiltrate sensitive data, such as customer records, configuration files, or credentials, using protocols like DNS tunneling (e.g., with dnscat2), HTTPS, or ICMP to test data loss prevention (DLP) controls, ensuring data is compressed and encrypted to evade detection.`,
      `Analyze log generation to identify monitoring blind spots, such as unlogged SSH access, missing audit trails for file access, or disabled logging on critical systems, and provide recommendations for improved visibility.`,
      `Assess the impact of a breach, such as data loss (e.g., exfiltrating a customer database), system downtime (e.g., crashing a server via a DoS exploit), or unauthorized access to critical systems (e.g., domain controllers), with detailed impact scenarios.`,
      `Test for Denial of Service (DoS) vulnerabilities by exploiting resource-intensive services (e.g., flooding an FTP server with connections) or triggering crashes via malformed packets (e.g., using hping3 to send invalid TCP packets), evaluating the network’s resilience.`,
      `Evaluate cryptographic weaknesses, such as outdated TLS versions (e.g., TLS 1.0 on a web server) or weak SSH keys, attempting to exploit them by cracking keys with John the Ripper or performing downgrade attacks to intercept traffic.`,
      `Test network monitoring by performing actions like data exfiltration or privilege escalation, then reviewing the client’s SIEM or IDS/IPS logs to assess detection capabilities, response times, and alert accuracy.`,
      `Assess the network’s incident response by triggering alerts (e.g., brute-force attempts on RDP) and observing the client’s response, including communication, mitigation efforts, and containment strategies, providing feedback for improvement.`,
    ],
    summary: `This phase highlights the real-world consequences of a network breach, identifying gaps in detection, response, and resilience that need to be addressed.`,
  },
  {
    name: 'Cleanup and Evasion Analysis',
    description: `We simulate attacker cleanup and evasion techniques to assess the network’s forensic readiness and ability to detect malicious activity after a breach. This phase focuses on how an attacker might cover their tracks and the network’s ability to preserve evidence for investigation.`,
    techniques: [
      `Delete logs on compromised systems, such as clearing event logs on Windows with wevtutil cl System or removing /var/log/auth.log on Linux, to test log retention and backup policies.`,
      `Disable auditing mechanisms, such as turning off Windows auditing policies or stopping the syslog service on Linux, to prevent future logging of malicious activity, and assess if these changes are detected.`,
      `Plant false evidence to mislead forensic investigations, such as creating fake user accounts, modifying timestamps with touch on Linux, or planting decoy files to distract investigators.`,
      `Use anti-forensic techniques, such as overwriting deleted files with tools like shred on Linux or cipher /w on Windows, to prevent data recovery, and test the network’s ability to preserve evidence.`,
      `Test timestomping by altering file timestamps (e.g., Set-Mtime in PowerShell) to hide malicious activity, ensuring the timeline of events is obfuscated, and evaluate the client’s forensic tools for detection.`,
      `Assess the network’s forensic readiness by reviewing log backups, snapshot capabilities, and chain-of-custody procedures, ensuring evidence can be preserved for legal or investigative purposes.`,
      `Provide recommendations for improving forensic readiness, such as implementing centralized logging, enabling immutable logs, or using tamper-evident logging solutions to detect unauthorized changes.`,
    ],
    summary: `This phase evaluates the network’s ability to detect and preserve evidence of a breach, ensuring effective incident response and forensic investigation capabilities.`,
  },
  {
    name: 'Reporting and Remediation Support',
    description: `We compile a detailed report of all findings, their impact, and remediation steps, ensuring the client can address vulnerabilities effectively. This phase also includes support for remediation and retesting to verify that fixes are successful.`,
    techniques: [
      `Document each vulnerability with detailed evidence, including Nmap scans, exploit output, stolen credentials, and network diagrams illustrating attack paths, assigning severity using CVSS scoring.`,
      `Explain the real-world impact of each issue, such as data breaches exposing customer data, system downtime affecting operations, or domain admin access leading to full network compromise, with scenarios for context.`,
      `Provide actionable remediation steps, such as applying patches (e.g., MS17-010), disabling unused services (e.g., Telnet), implementing network segmentation with firewall rules, or enabling centralized logging.`,
      `Include an executive summary for non-technical stakeholders, summarizing key risks, the overall security posture, and prioritized remediation actions, ensuring clarity for decision-makers.`,
      `Offer retesting support by revisiting exploited vulnerabilities after remediation, providing a follow-up report comparing pre- and post-remediation results to confirm that issues are resolved.`,
      `Conduct a knowledge transfer session with the client’s team, explaining findings, demonstrating exploitation techniques (if requested), and providing guidance on secure network configurations (e.g., NIST SP 800-115 guidelines).`,
      `Provide a technical appendix for network administrators, including detailed exploit steps, mitigation scripts (e.g., PowerShell to disable SMBv1), and references to standards like CIS benchmarks for hardening systems.`,
    ],
    summary: `This phase ensures that findings are actionable, stakeholders are informed, and the network’s security is improved through effective remediation and knowledge sharing.`,
  },
];

interface NetworkMethodologyPageProps {
  documentNumber?: string;
  registerSectionPage?: (section: string, page: number) => void;
  sectionId?: string;
}

const NetworkMethodologyPage: React.FC<NetworkMethodologyPageProps> = ({ documentNumber, registerSectionPage, sectionId }) => (
  <Page size="A4" id={sectionId} style={styles.page}>
    {/* Register section page for TOC */}
    {registerSectionPage && (
      <Text style={{ display: 'none', fontSize: 1 }} render={({ pageNumber }) => {
        registerSectionPage('NetworkMethodology', pageNumber);
        return '';
      }} />
    )}
    <Text style={styles.sectionTitle}>Network Penetration Testing Methodology</Text>
    <View style={styles.divider} />
    <Text style={styles.intro}>
      Our network penetration testing methodology identifies vulnerabilities in network infrastructure through a detailed, phased approach, ensuring a comprehensive evaluation of the network’s security posture.
    </Text>
    {phases.map((phase) => (
      <View key={phase.name} style={styles.phaseBlock}>
        <Text style={styles.phaseName}>{phase.name}</Text>
        <Text style={styles.phaseDesc}><Text style={{ fontWeight: 'bold' }}>Description:</Text> {phase.description}</Text>
        <Text style={styles.phaseTechniques}>Techniques:</Text>
        <View style={styles.bulletList}>
          {phase.techniques.map((tech, i) => (
            <Text key={i} style={styles.bulletItem}>• {tech}</Text>
          ))}
        </View>
        {phase.summary && <Text style={styles.phaseSummary}>{phase.summary}</Text>}
      </View>
    ))}
    <View style={styles.footer} fixed>
      <Text style={styles.footerLeft}>{documentNumber || 'Document Number'}</Text>
      <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
    </View>
  </Page>
);

export default NetworkMethodologyPage; 