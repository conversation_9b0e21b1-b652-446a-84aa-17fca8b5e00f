import { useState, useEffect, useCallback } from "react";
import { getBusinessReviewIssues } from "../../api/endpoints/dashboard/dashboardApi";

// Define interfaces for the business review issues data
export interface BugLifecycleStage {
  key: string;
  label: string;
  isCompleted: boolean;
  isCurrent: boolean;
}

export interface BugLifecycle {
  currentStage: string;
  currentStageLabel: string;
  lifecycleStages: BugLifecycleStage[];
}

export interface BusinessReviewIssue {
  report_id: number;
  title: string;
  severity_category: string;
  severity_color: string;
  submitted_date: string;
  days_open: number;
  scope: string;
  state: string;
  status_label: string;
  program_id: number;
  program_name: string;
  is_business_review: boolean;
  lifecycle: BugLifecycle;
}

export interface BusinessReviewMeta {
  totalCount: number;
  hasMore: boolean;
}

export interface BusinessReviewResponse {
  success: boolean;
  data: BusinessReviewIssue[];
  meta?: BusinessReviewMeta;
}

/**
 * Custom hook to fetch and handle business review issues
 */
const useBusinessReviewIssues = () => {
  const [issues, setIssues] = useState<BusinessReviewIssue[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [meta, setMeta] = useState<BusinessReviewMeta | null>(null);

  // Function to fetch data
  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await getBusinessReviewIssues();
      
      if (!result.success) {
        throw new Error("Failed to fetch business review issues");
      }
      
      setIssues(result.data);
      
      // Set meta information if available
      if (result.meta) {
        setMeta(result.meta);
      } else {
        // Create default meta if not provided
        setMeta({
          totalCount: result.data.length,
          hasMore: false
        });
      }
      
    } catch (err) {
      console.error("Error in useBusinessReviewIssues:", err);
      setError(err instanceof Error ? err.message : "Unknown error occurred");
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch data on component mount
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    issues,
    loading,
    error,
    refetch: fetchData,
    meta
  };
};

export default useBusinessReviewIssues; 