import React from 'react';
import { ReportData } from '../../types/report.types';
import HtmlEditor from '../HtmlEditor';

interface ProjectObjectivesEditorProps {
  reportData: ReportData;
  onHtmlChange: (field: keyof ReportData, value: string) => void;
}

const ProjectObjectivesEditor: React.FC<ProjectObjectivesEditorProps> = ({
  reportData,
  onHtmlChange,
}) => {
  return (
    <div className="h-full overflow-y-auto">
      <div className="bg-white rounded-xl shadow-lg border border-slate-100 p-6 mb-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-1 h-8 bg-gradient-to-b from-blue-500 to-blue-700 rounded-full" />
          <div>
            <h3 className="text-base font-bold text-blue-900 tracking-tight">Project Objectives</h3>
            <p className="text-xs text-blue-700/80 font-medium mt-0.5">Define the goals and objectives of your security assessment</p>
          </div>
        </div>
        <div className="mb-4">
          <label className="block text-xs font-medium text-slate-700 mb-1">Published Date</label>
          <input
            type="date"
            className="border border-slate-300 rounded px-2 py-1 text-xs"
            value={reportData.current_date ? reportData.current_date.split('T')[0] : ''}
            onChange={e => onHtmlChange('current_date', e.target.value)}
          />
        </div>
        <div className="pt-2">
          <div className="text-xs">
            <HtmlEditor
              field={"project_objectives" as keyof ReportData}
              title=""
              reportData={reportData}
              onHtmlChange={onHtmlChange as any}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectObjectivesEditor; 