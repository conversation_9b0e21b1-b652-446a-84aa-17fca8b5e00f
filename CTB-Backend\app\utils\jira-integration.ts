/**
 * Jira API Integration
 * Provides functions for interacting with the Jira API
 */
import axios from "axios";
import { logger } from "../logger/index";

/**
 * Create a Jira issue based on the report data
 * @param jiraConfig The Jira configuration (url, email, api_token, project_key)
 * @param reportData The report data to create an issue for
 * @returns Promise containing the created issue key
 */
export const createJiraIssue = async (
  jiraConfig: {
    url: string;
    email: string;
    api_token: string;
    project_key: string;
  },
  reportData: {
    report_id: number;
    report_title: string;
    category?: string;
    severity_category?: string;
    description?: any;
    scope?: string;
    program_title?: string;
  }
) => {
  try {
    // Validate Jira configuration
    logger.log({
      level: "info",
      label: "Jira Integration | Detailed",
      message: `Starting Jira issue creation for report ${reportData.report_id} - Config check`
    });

    logger.log({
      level: "info",
      label: "Jira Integration | Detailed",
      message: `Jira URL: ${jiraConfig.url ? "Provided" : "Missing"}, Email: ${
        jiraConfig.email ? "Provided" : "Missing"
      }, API Token: ${
        jiraConfig.api_token ? "Provided" : "Missing"
      }, Project Key: ${jiraConfig.project_key ? "Provided" : "Missing"}`
    });

    if (
      !jiraConfig ||
      !jiraConfig.url ||
      !jiraConfig.email ||
      !jiraConfig.api_token ||
      !jiraConfig.project_key
    ) {
      logger.log({
        level: "error",
        label: "Jira Integration",
        message: "Invalid Jira configuration"
      });
      return { success: false, message: "Invalid Jira configuration" };
    }

    // Log attempt to create Jira issue
    logger.log({
      level: "info",
      label: "Jira Integration",
      message: `Attempting to create Jira issue for report ${reportData.report_id} in project ${jiraConfig.project_key}`
    });

    // Standard Jira API URL format - ensure we have the right endpoint
    const apiUrl = `${jiraConfig.url.replace(/\/+$/, "")}/rest/api/2/issue`;

    logger.log({
      level: "info",
      label: "Jira Integration | Detailed",
      message: `Using Jira API URL: ${apiUrl}`
    });

    // Function to strip HTML tags from text
    const stripHtmlTags = html => {
      if (!html) return "";
      // Convert to string if it's an object
      const htmlString =
        typeof html === "object" ? JSON.stringify(html) : String(html);
      // Replace HTML tags with empty string
      return htmlString.replace(/<[^>]*>/g, "");
    };

    // Function to parse and format category
    const formatCategory = category => {
      if (!category) return "Not specified";
      try {
        // If it's already a string, try to parse it as JSON
        let categoryObj;
        if (typeof category === "string") {
          try {
            categoryObj = JSON.parse(category);
          } catch (e) {
            // If it's not valid JSON, return the string as-is
            return category;
          }
        } else {
          // If it's an object, use it directly
          categoryObj = category;
        }

        // Extract relevant fields
        const parts = [];
        if (categoryObj.categoryId)
          parts.push(categoryObj.categoryId.replace(/_/g, " "));
        if (categoryObj.subcategoryId)
          parts.push(categoryObj.subcategoryId.replace(/_/g, " "));
        if (categoryObj.variantId)
          parts.push(categoryObj.variantId.replace(/_/g, " "));

        // Join with proper formatting and title case each part
        return parts
          .map(part =>
            part
              .split(" ")
              .map(word => word.charAt(0).toUpperCase() + word.slice(1))
              .join(" ")
          )
          .join(" > ");
      } catch (error) {
        // If anything goes wrong, return the original
        return typeof category === "object"
          ? JSON.stringify(category)
          : category;
      }
    };

    // Format the description with comprehensive report details
    const descriptionWithDetails = `
*Report ID:* ${reportData.report_id}
*Severity:* ${reportData.severity_category || "Not specified"}
*Category:* ${formatCategory(reportData.category)}
*Target:* ${reportData.scope || "Not specified"}
*Program:* ${reportData.program_title || "Not specified"}
*Report Link:* ${
      process.env.FRONTEND_BASE_URL || "http://localhost:3000"
    }/dashboard/reports/${reportData.report_id}

*Description:*
${stripHtmlTags(reportData.description) || "No description provided."}

---
This issue was automatically created from CTB platform.
`;

    logger.log({
      level: "info",
      label: "Jira Integration | Detailed",
      message: `Formatted description for Jira issue: ${descriptionWithDetails.substring(
        0,
        100
      )}...`
    });

    // Get available issue types for the project
    logger.log({
      level: "info",
      label: "Jira Integration | Detailed",
      message: `Fetching issue types for project ${jiraConfig.project_key}`
    });

    const metaResponse = await axios
      .get(
        `${jiraConfig.url.replace(
          /\/+$/,
          ""
        )}/rest/api/2/issue/createmeta?projectKeys=${
          jiraConfig.project_key
        }&expand=projects.issuetypes`,
        {
          auth: {
            username: jiraConfig.email,
            password: jiraConfig.api_token
          }
        }
      )
      .catch(error => {
        logger.log({
          level: "error",
          label: "Jira Integration | Detailed",
          message: `Error fetching issue types: ${error.message}`
        });

        if (error.response) {
          logger.log({
            level: "error",
            label: "Jira Integration | Detailed",
            message: `Response status: ${
              error.response.status
            }, data: ${JSON.stringify(error.response.data)}`
          });
        }

        throw error;
      });

    logger.log({
      level: "info",
      label: "Jira Integration | Detailed",
      message: `Received metaResponse: ${
        metaResponse ? "Success" : "Undefined"
      }`
    });

    // Extract the first available issue type (usually "Bug" or "Task")
    const project = metaResponse.data.projects[0];

    logger.log({
      level: "info",
      label: "Jira Integration | Detailed",
      message: `Project data: ${
        project ? "Available" : "Missing"
      }, Issue types: ${
        project && project.issuetypes ? project.issuetypes.length : 0
      }`
    });

    if (!project || !project.issuetypes || project.issuetypes.length === 0) {
      throw new Error(
        `No issue types available for project ${jiraConfig.project_key}`
      );
    }
    const issueType = project.issuetypes[0].name;

    logger.log({
      level: "info",
      label: "Jira Integration | Detailed",
      message: `Using issue type: ${issueType}`
    });

    // Create the issue data
    const issueData = {
      fields: {
        project: {
          key: jiraConfig.project_key
        },
        summary: `[CTB Report] ${reportData.report_title}`,
        description: descriptionWithDetails,
        issuetype: {
          name: issueType
        },
        // Add assignee to ensure issues appear on Kanban boards
        // Most boards filter out unassigned issues
        assignee: {
          name: jiraConfig.email.split("@")[0] // Use the email username as assignee
        },
        // Add labels for easy filtering and identification
        labels: ["CTB", "Security", "reports"]
        // Priority field removed - not supported in this Jira project configuration
        // Uncomment if your Jira instance supports setting priority
        // priority: mapSeverityToPriority(reportData.severity_category)
      }
    };

    logger.log({
      level: "info",
      label: "Jira Integration | Detailed",
      message: `Prepared issue data: ${JSON.stringify(issueData)}`
    });

    // Make the API call to create the issue
    logger.log({
      level: "info",
      label: "Jira Integration | Detailed",
      message: `Sending POST request to Jira API at ${apiUrl}`
    });

    const response = await axios
      .post(apiUrl, issueData, {
        auth: {
          username: jiraConfig.email,
          password: jiraConfig.api_token
        },
        headers: {
          "Content-Type": "application/json"
        }
      })
      .catch(error => {
        logger.log({
          level: "error",
          label: "Jira Integration | Detailed",
          message: `Error creating issue: ${error.message}`
        });

        if (error.response) {
          logger.log({
            level: "error",
            label: "Jira Integration | Detailed",
            message: `Response status: ${
              error.response.status
            }, data: ${JSON.stringify(error.response.data)}`
          });
        }

        throw error;
      });

    logger.log({
      level: "info",
      label: "Jira Integration | Detailed",
      message: `Received response from Jira API: ${JSON.stringify(
        response.data
      )}`
    });

    logger.log({
      level: "info",
      label: "Jira Integration",
      message: `Successfully created Jira issue ${response.data.key} for report ${reportData.report_id}`
    });

    return {
      success: true,
      issueKey: response.data.key,
      issueUrl: `${jiraConfig.url.replace(/rest\/api\/2$/, "")}/browse/${
        response.data.key
      }`
    };
  } catch (error) {
    let errorMessage = "Unknown error";
    if (error.response) {
      // Extract meaningful error information from Jira API response
      errorMessage = `Status: ${
        error.response.status
      }, Message: ${JSON.stringify(error.response.data)}`;

      logger.log({
        level: "error",
        label: "Jira Integration | Detailed",
        message: `Error response from Jira: Status ${
          error.response.status
        }, Data: ${JSON.stringify(error.response.data)}`
      });

      // Log specific error types for better debugging
      if (error.response.status === 401) {
        errorMessage = "Authentication error: Invalid email or API token";
      } else if (error.response.status === 404) {
        errorMessage = "Project not found: Check your project key";
      } else if (error.response.status === 400) {
        errorMessage = `Bad request: ${JSON.stringify(error.response.data)}`;
      }
    } else if (error.request) {
      errorMessage =
        "No response received from Jira server. Check your Jira URL.";

      logger.log({
        level: "error",
        label: "Jira Integration | Detailed",
        message: `No response received from Jira server. Request: ${JSON.stringify(
          error.request
        )}`
      });
    } else {
      errorMessage = error.message;

      logger.log({
        level: "error",
        label: "Jira Integration | Detailed",
        message: `General error: ${error.message}, Stack: ${error.stack}`
      });
    }

    logger.log({
      level: "error",
      label: "Jira Integration",
      message: `Failed to create Jira issue: ${errorMessage}`
    });

    return { success: false, message: errorMessage };
  }
};

/**
 * Check webhook connectivity and configuration
 * @param webhookUrl The webhook URL to test
 * @returns Connectivity status and diagnostic information
 */
export const checkWebhookConnectivity = async (webhookUrl: string) => {
  try {
    logger.log({
      level: "info",
      label: "Jira Integration | Webhook Check",
      message: `Testing webhook connectivity to: ${webhookUrl}`
    });

    // Test basic connectivity
    const response = await axios.get(webhookUrl, {
      timeout: 10000, // 10 second timeout
      validateStatus: status => status < 500 // Accept any status < 500 as "reachable"
    });

    logger.log({
      level: "info",
      label: "Jira Integration | Webhook Check",
      message: `Webhook endpoint reachable. Status: ${response.status}`
    });

    return {
      success: true,
      reachable: true,
      status: response.status,
      message: "Webhook endpoint is reachable"
    };
  } catch (error) {
    const errorMessage = error.message || "Unknown error";

    logger.log({
      level: "error",
      label: "Jira Integration | Webhook Check",
      message: `Webhook connectivity test failed: ${errorMessage}`
    });

    // Provide specific error messages based on error type
    let diagnosticMessage = "Webhook endpoint is not reachable";

    if (error.code === "ECONNREFUSED") {
      diagnosticMessage =
        "Connection refused - webhook endpoint may not be running or port may be blocked";
    } else if (error.code === "ENOTFOUND") {
      diagnosticMessage =
        "DNS resolution failed - check webhook URL and network connectivity";
    } else if (error.code === "ETIMEDOUT") {
      diagnosticMessage =
        "Connection timeout - webhook endpoint may be slow or overloaded";
    } else if (error.response) {
      diagnosticMessage = `HTTP error ${error.response.status} - webhook endpoint returned an error`;
    } else if (error.request) {
      diagnosticMessage =
        "No response received - webhook endpoint may be down or unreachable";
    }

    return {
      success: false,
      reachable: false,
      error: errorMessage,
      diagnostic: diagnosticMessage,
      message: diagnosticMessage
    };
  }
};

/**
 * Validate webhook configuration and provide setup guidance
 * @param jiraConfig Jira configuration
 * @param webhookUrl Webhook URL
 * @returns Validation results and setup recommendations
 */
export const validateWebhookConfiguration = async (
  jiraConfig: {
    url: string;
    email: string;
    api_token: string;
    project_key: string;
  },
  webhookUrl: string
) => {
  const results = {
    jiraConnection: false,
    webhookConnectivity: false,
    recommendations: [] as string[]
  };

  try {
    // Test Jira API connectivity
    const jiraResponse = await axios.get(
      `${jiraConfig.url.replace(/\/+$/, "")}/rest/api/2/myself`,
      {
        auth: {
          username: jiraConfig.email,
          password: jiraConfig.api_token
        },
        timeout: 10000
      }
    );

    if (jiraResponse.status === 200) {
      results.jiraConnection = true;
      logger.log({
        level: "info",
        label: "Jira Integration | Config Validation",
        message: "Jira API connection successful"
      });
    }
  } catch (error) {
    results.recommendations.push(
      "Check Jira API credentials and URL configuration"
    );
    logger.log({
      level: "error",
      label: "Jira Integration | Config Validation",
      message: `Jira API connection failed: ${error.message}`
    });
  }

  // Test webhook connectivity
  const webhookCheck = await checkWebhookConnectivity(webhookUrl);
  results.webhookConnectivity = webhookCheck.reachable;

  if (!webhookCheck.reachable) {
    results.recommendations.push(webhookCheck.diagnostic);
    results.recommendations.push(
      "Verify webhook URL is accessible from Jira's servers"
    );
    results.recommendations.push("Check firewall and network configuration");
  }

  // Additional recommendations
  if (!results.jiraConnection) {
    results.recommendations.push("Verify Jira email and API token are correct");
    results.recommendations.push(
      "Ensure Jira instance is accessible from your server"
    );
  }

  if (results.jiraConnection && !results.webhookConnectivity) {
    results.recommendations.push(
      "Jira is accessible but webhook endpoint is not - check webhook URL configuration"
    );
  }

  return results;
};

// Function removed - priority field not used in Jira integration

/**
 * Fetch available project keys from Jira
 * @param config Jira configuration
 * @returns List of available project keys
 */
export const fetchJiraProjects = async (config: {
  url: string;
  email: string;
  api_token: string;
}) => {
  try {
    if (!config || !config.url || !config.email || !config.api_token) {
      return { success: false, message: "Invalid Jira configuration" };
    }

    const response = await axios.get(
      `${config.url.replace(/\/+$/, "")}/rest/api/2/project`,
      {
        auth: {
          username: config.email,
          password: config.api_token
        }
      }
    );

    const projects = response.data.map(project => ({
      key: project.key,
      name: project.name
    }));

    return { success: true, projects };
  } catch (error) {
    let errorMessage = "Unknown error";
    if (error.response) {
      errorMessage = `Status: ${
        error.response.status
      }, Message: ${JSON.stringify(error.response.data)}`;
    } else if (error.request) {
      errorMessage = "No response received from Jira server";
    } else {
      errorMessage = error.message;
    }

    logger.log({
      level: "error",
      label: "Jira Integration",
      message: `Failed to fetch Jira projects: ${errorMessage}`
    });

    return { success: false, message: errorMessage };
  }
};

/**
 * Revert Jira issue to a specific status
 * @param jiraConfig The Jira configuration (url, email, api_token, project_key)
 * @param issueKey The Jira issue key to revert
 * @param targetStatus The target status to revert to (default: "To Do")
 * @returns Promise containing success status and message
 */
export const revertJiraIssueToStatus = async (
  jiraConfig: {
    url: string;
    email: string;
    api_token: string;
    project_key: string;
  },
  issueKey: string,
  targetStatus: string = "To Do"
): Promise<{ success: boolean; message?: string }> => {
  try {
    // Validate configuration
    if (
      !jiraConfig ||
      !jiraConfig.url ||
      !jiraConfig.email ||
      !jiraConfig.api_token
    ) {
      logger.log({
        level: "error",
        label: "Jira Integration | Revert Status",
        message: "Invalid Jira configuration for status revert"
      });
      return {
        success: false,
        message: "Jira configuration not available for status revert"
      };
    }

    logger.log({
      level: "info",
      label: "Jira Integration | Revert Status",
      message: `Attempting to revert Jira issue ${issueKey} to '${targetStatus}' status`
    });

    // Get available transitions for the issue
    const transitionsUrl = `${jiraConfig.url.replace(
      /\/+$/,
      ""
    )}/rest/api/2/issue/${issueKey}/transitions`;

    const transitionsResponse = await axios
      .get(transitionsUrl, {
        auth: {
          username: jiraConfig.email,
          password: jiraConfig.api_token
        }
      })
      .catch(error => {
        logger.log({
          level: "error",
          label: "Jira Integration | Revert Status",
          message: `Failed to get transitions for ${issueKey}: ${error.message}`
        });
        throw error;
      });

    const transitions = transitionsResponse.data.transitions;
    const availableTransitions = transitions.map((t: any) => t.name).join(", ");

    logger.log({
      level: "info",
      label: "Jira Integration | Revert Status",
      message: `Available transitions for ${issueKey}: ${availableTransitions}`
    });

    // Find the transition to target status
    const targetTransition = transitions.find(
      (transition: any) =>
        transition.name === targetStatus || transition.to.name === targetStatus
    );

    if (!targetTransition) {
      logger.log({
        level: "error",
        label: "Jira Integration | Revert Status",
        message: `No transition to '${targetStatus}' found for issue ${issueKey}. Available: ${availableTransitions}`
      });
      return {
        success: false,
        message: `Cannot transition to '${targetStatus}'. Available transitions: ${availableTransitions}`
      };
    }

    logger.log({
      level: "info",
      label: "Jira Integration | Revert Status",
      message: `Found transition to '${targetStatus}' (ID: ${targetTransition.id}) for issue ${issueKey}`
    });

    // Execute the transition
    const transitionUrl = `${jiraConfig.url.replace(
      /\/+$/,
      ""
    )}/rest/api/2/issue/${issueKey}/transitions`;

    await axios.post(
      transitionUrl,
      {
        transition: {
          id: targetTransition.id
        }
      },
      {
        auth: {
          username: jiraConfig.email,
          password: jiraConfig.api_token
        },
        headers: {
          "Content-Type": "application/json"
        }
      }
    );

    logger.log({
      level: "info",
      label: "Jira Integration | Revert Status",
      message: `Successfully reverted Jira issue ${issueKey} to '${targetStatus}' status`
    });

    return {
      success: true,
      message: `Successfully reverted issue ${issueKey} to '${targetStatus}'`
    };
  } catch (error) {
    logger.log({
      level: "error",
      label: "Jira Integration | Revert Status",
      message: `Error reverting Jira issue ${issueKey} to '${targetStatus}': ${error.message}`
    });

    return {
      success: false,
      message: `Failed to revert issue to '${targetStatus}': ${error.message}`
    };
  }
};

/**
 * Revert Jira issue back to "To Do" status when closure conditions are not met
 * @param jiraConfig The Jira configuration (url, email, api_token, project_key)
 * @param issueKey The Jira issue key to revert
 * @returns Promise containing success status and message
 */
export const revertJiraIssueStatus = async (
  jiraConfig: {
    url: string;
    email: string;
    api_token: string;
    project_key: string;
  },
  issueKey: string
): Promise<{ success: boolean; message?: string }> => {
  try {
    // Validate configuration
    if (
      !jiraConfig ||
      !jiraConfig.url ||
      !jiraConfig.email ||
      !jiraConfig.api_token
    ) {
      logger.log({
        level: "error",
        label: "Jira Integration | Revert Status",
        message: "Invalid Jira configuration for status revert"
      });
      return {
        success: false,
        message: "Jira configuration not available for status revert"
      };
    }

    logger.log({
      level: "info",
      label: "Jira Integration | Revert Status",
      message: `Attempting to revert Jira issue ${issueKey} to 'To Do' status`
    });

    // Get available transitions for the issue
    const transitionsUrl = `${jiraConfig.url.replace(
      /\/+$/,
      ""
    )}/rest/api/2/issue/${issueKey}/transitions`;

    const transitionsResponse = await axios
      .get(transitionsUrl, {
        auth: {
          username: jiraConfig.email,
          password: jiraConfig.api_token
        }
      })
      .catch(error => {
        logger.log({
          level: "error",
          label: "Jira Integration | Revert Status",
          message: `Error fetching transitions for ${issueKey}: ${error.message}`
        });
        throw error;
      });

    logger.log({
      level: "info",
      label: "Jira Integration | Revert Status",
      message: `Available transitions for ${issueKey}: ${transitionsResponse.data.transitions
        .map(t => t.to.name)
        .join(", ")}`
    });

    // Find the transition to "To Do" status
    const toDoTransition = transitionsResponse.data.transitions.find(
      (transition: any) =>
        transition.to.name === "To Do" ||
        transition.to.name === "TODO" ||
        transition.to.name === "Open"
    );

    if (!toDoTransition) {
      logger.log({
        level: "warn",
        label: "Jira Integration | Revert Status",
        message: `No transition to 'To Do' status found for issue ${issueKey}`
      });
      return {
        success: false,
        message: "No transition to 'To Do' status found for this issue"
      };
    }

    logger.log({
      level: "info",
      label: "Jira Integration | Revert Status",
      message: `Found transition to '${toDoTransition.to.name}' (ID: ${toDoTransition.id}) for issue ${issueKey}`
    });

    // Execute the transition
    const transitionUrl = `${jiraConfig.url.replace(
      /\/+$/,
      ""
    )}/rest/api/2/issue/${issueKey}/transitions`;

    await axios
      .post(
        transitionUrl,
        {
          transition: {
            id: toDoTransition.id
          },
          fields: {},
          update: {
            comment: [
              {
                add: {
                  body: "Issue reverted to 'To Do' by CTB system. Report closure conditions not met: Report must be in 'awaiting_fix' status and retest status must be 'fix_verified'."
                }
              }
            ]
          }
        },
        {
          auth: {
            username: jiraConfig.email,
            password: jiraConfig.api_token
          },
          headers: {
            "Content-Type": "application/json"
          }
        }
      )
      .catch(error => {
        logger.log({
          level: "error",
          label: "Jira Integration | Revert Status",
          message: `Error executing transition for ${issueKey}: ${error.message}`
        });

        if (error.response) {
          logger.log({
            level: "error",
            label: "Jira Integration | Revert Status",
            message: `Response status: ${
              error.response.status
            }, data: ${JSON.stringify(error.response.data)}`
          });
        }

        throw error;
      });

    logger.log({
      level: "info",
      label: "Jira Integration | Revert Status",
      message: `Successfully reverted Jira issue ${issueKey} to 'To Do' status`
    });

    return { success: true };
  } catch (error) {
    logger.log({
      level: "error",
      label: "Jira Integration | Revert Status",
      message: `Error reverting Jira issue ${issueKey}: ${error.message}`
    });

    return {
      success: false,
      message: error.response?.data?.errorMessages?.[0] || error.message
    };
  }
};
