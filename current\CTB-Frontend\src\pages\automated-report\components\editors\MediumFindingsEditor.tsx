import React from 'react';
import { ReportData, DetailedFinding } from '../../types/report.types';
import BaseFindingEditor from './BaseFindingEditor';

interface MediumFindingsEditorProps {
  reportData: ReportData;
  onFindingChange: (index: number, field: keyof DetailedFinding, value: string) => void;
  onRemoveFinding: (index: number) => void;
  onAddFinding: (severity: 'Critical' | 'High' | 'Medium' | 'Low') => void;
}

const MediumFindingsEditor: React.FC<MediumFindingsEditorProps> = (props) => {
  return <BaseFindingEditor {...props} severity="Medium" />;
};

export default MediumFindingsEditor; 