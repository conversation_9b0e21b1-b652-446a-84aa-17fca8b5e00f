{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\jira-old\\\\CTB-Frontend\\\\src\\\\pages\\\\automated-report\\\\components\\\\sections\\\\ScopePage.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Page, View, Text, StyleSheet } from '@react-pdf/renderer';\nimport { useSectionPages } from '../SectionPageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst styles = StyleSheet.create({\n  footer: {\n    position: 'absolute',\n    bottom: 30,\n    left: 0,\n    right: 0,\n    fontSize: 10,\n    color: 'grey',\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    paddingHorizontal: 24\n  },\n  footerLeft: {\n    textAlign: 'left',\n    flex: 1\n  },\n  footerRight: {\n    textAlign: 'right',\n    flex: 1\n  }\n});\n\n// Utility to strip HTML tags and preserve paragraph breaks\nfunction htmlToParagraphs(html) {\n  if (!html) return [];\n  // Replace <br> and <br/> with \\n\n  let withBreaks = html.replace(/<br\\s*\\/?>(?![^<]*<)/gi, '\\n');\n  // Replace <p> and <div> with \\n (block-level)\n  withBreaks = withBreaks.replace(/<\\/?(p|div)[^>]*>/gi, '\\n');\n  // Remove all other tags\n  const tmp = document.createElement('div');\n  tmp.innerHTML = withBreaks;\n  const text = tmp.textContent || tmp.innerText || '';\n  // Split by double newlines or single newlines, filter empty\n  return text.split(/\\n+/).map(s => s.trim()).filter(Boolean);\n}\nconst ScopePage = ({\n  reportData,\n  sectionId\n}) => {\n  _s();\n  const {\n    updateSectionPage\n  } = useSectionPages();\n  return /*#__PURE__*/_jsxDEV(Page, {\n    size: \"A4\",\n    id: sectionId,\n    style: {\n      flexDirection: 'column',\n      backgroundColor: '#ffffff',\n      padding: '20mm 15mm',\n      fontFamily: 'Helvetica',\n      fontSize: 12\n    },\n    children: [/*#__PURE__*/_jsxDEV(View, {\n      style: {\n        paddingHorizontal: 14,\n        paddingTop: 1,\n        flexDirection: 'column',\n        flex: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(View, {\n        style: {\n          flex: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            fontSize: 16,\n            fontWeight: 'bold',\n            color: '#2563eb',\n            marginBottom: 12,\n            lineHeight: 1.4\n          },\n          children: \"SCOPE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), reportData.scope ? htmlToParagraphs(reportData.scope).map((para, idx) => /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            fontSize: 12,\n            lineHeight: 1.4,\n            marginBottom: 14,\n            color: '#374151',\n            textAlign: 'justify'\n          },\n          children: para\n        }, idx, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 15\n        }, this)) : /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            fontSize: 12,\n            lineHeight: 1.4,\n            marginBottom: 18,\n            color: '#374151',\n            textAlign: 'justify'\n          },\n          children: [\"The scope of the assessment was limited to performing Vulnerability Assessment and Penetration Testing on the  \", reportData.program_name, \" mentioned below:\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(View, {\n          style: {\n            borderRadius: 10,\n            borderWidth: 1,\n            borderColor: '#d1d5db',\n            marginBottom: 16,\n            width: '100%',\n            alignSelf: 'center',\n            backgroundColor: '#fff',\n            overflow: 'hidden'\n          },\n          children: /*#__PURE__*/_jsxDEV(View, {\n            style: {\n              flexDirection: 'column',\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(View, {\n              style: {\n                flexDirection: 'row',\n                backgroundColor: '#2563eb',\n                borderTopLeftRadius: 10,\n                borderTopRightRadius: 10\n              },\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                style: {\n                  padding: 14,\n                  textAlign: 'center',\n                  fontWeight: 'bold',\n                  width: '20%',\n                  color: '#fff',\n                  fontSize: 12,\n                  lineHeight: 1.4\n                },\n                children: \"SL No.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                style: {\n                  padding: 14,\n                  textAlign: 'left',\n                  fontWeight: 'bold',\n                  width: '30%',\n                  color: '#fff',\n                  fontSize: 12,\n                  lineHeight: 1.4\n                },\n                children: \"Asset Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                style: {\n                  padding: 14,\n                  textAlign: 'left',\n                  fontWeight: 'bold',\n                  width: '50%',\n                  color: '#fff',\n                  fontSize: 12,\n                  lineHeight: 1.4\n                },\n                children: \"Asset\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), reportData.target_details && reportData.target_details.map((target, idx) => /*#__PURE__*/_jsxDEV(View, {\n              style: {\n                flexDirection: 'row',\n                backgroundColor: idx % 2 === 0 ? '#f1f5f9' : '#fff',\n                borderTopWidth: idx === 0 ? 0 : 1,\n                borderTopColor: '#e5e7eb'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                style: {\n                  padding: 12,\n                  width: '20%',\n                  textAlign: 'center',\n                  fontSize: 12,\n                  color: '#2563eb',\n                  fontWeight: 'bold',\n                  lineHeight: 1.4\n                },\n                children: String(idx + 1).padStart(2, '0')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                style: {\n                  padding: 12,\n                  width: '30%',\n                  fontWeight: '600',\n                  fontSize: 12,\n                  lineHeight: 1.4\n                },\n                children: target.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                style: {\n                  padding: 12,\n                  width: '50%',\n                  fontSize: 12,\n                  lineHeight: 1.4\n                },\n                children: target.url\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 19\n              }, this)]\n            }, idx, true, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            fontSize: 12,\n            color: '#64748b',\n            textAlign: 'center',\n            marginTop: 8,\n            lineHeight: 1.4\n          },\n          children: \"Table 1: Scope of Work\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(View, {\n      style: styles.footer,\n      fixed: true,\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        style: styles.footerLeft,\n        children: reportData.document_number || 'Document Number'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        style: styles.footerRight,\n        render: ({\n          pageNumber,\n          totalPages\n        }) => `${pageNumber} / ${totalPages}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      style: {\n        display: 'none'\n      },\n      render: ({\n        pageNumber\n      }) => {\n        updateSectionPage('Scope', pageNumber);\n        return '';\n      },\n      fixed: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n};\n_s(ScopePage, \"3rf3HaIqG9R7of1rm8wX9C3kpH0=\", false, function () {\n  return [useSectionPages];\n});\n_c = ScopePage;\nexport default ScopePage;\nvar _c;\n$RefreshReg$(_c, \"ScopePage\");", "map": {"version": 3, "names": ["React", "Page", "View", "Text", "StyleSheet", "useSectionPages", "jsxDEV", "_jsxDEV", "styles", "create", "footer", "position", "bottom", "left", "right", "fontSize", "color", "flexDirection", "justifyContent", "alignItems", "paddingHorizontal", "footerLeft", "textAlign", "flex", "footerRight", "htmlToParagraphs", "html", "withBreaks", "replace", "tmp", "document", "createElement", "innerHTML", "text", "textContent", "innerText", "split", "map", "s", "trim", "filter", "Boolean", "ScopePage", "reportData", "sectionId", "_s", "updateSectionPage", "size", "id", "style", "backgroundColor", "padding", "fontFamily", "children", "paddingTop", "fontWeight", "marginBottom", "lineHeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "scope", "para", "idx", "program_name", "borderRadius", "borderWidth", "borderColor", "width", "alignSelf", "overflow", "borderTopLeftRadius", "borderTopRightRadius", "target_details", "target", "borderTopWidth", "borderTopColor", "String", "padStart", "type", "url", "marginTop", "fixed", "document_number", "render", "pageNumber", "totalPages", "display", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/jira-old/CTB-Frontend/src/pages/automated-report/components/sections/ScopePage.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Page, View, Text, StyleSheet } from '@react-pdf/renderer';\r\nimport { ReportData } from '../../types/report.types';\r\nimport { useSectionPages } from '../SectionPageContext';\r\n\r\ninterface ScopePageProps {\r\n  reportData: ReportData;\r\n  sectionId?: string;\r\n}\r\n\r\nconst styles = StyleSheet.create({\r\n  footer: {\r\n    position: 'absolute',\r\n    bottom: 30,\r\n    left: 0,\r\n    right: 0,\r\n    fontSize: 10,\r\n    color: 'grey',\r\n    flexDirection: 'row',\r\n    justifyContent: 'space-between',\r\n    alignItems: 'center',\r\n    paddingHorizontal: 24,\r\n  },\r\n  footerLeft: {\r\n    textAlign: 'left',\r\n    flex: 1,\r\n  },\r\n  footerRight: {\r\n    textAlign: 'right',\r\n    flex: 1,\r\n  },\r\n});\r\n\r\n// Utility to strip HTML tags and preserve paragraph breaks\r\nfunction htmlToParagraphs(html: string): string[] {\r\n  if (!html) return [];\r\n  // Replace <br> and <br/> with \\n\r\n  let withBreaks = html.replace(/<br\\s*\\/?>(?![^<]*<)/gi, '\\n');\r\n  // Replace <p> and <div> with \\n (block-level)\r\n  withBreaks = withBreaks.replace(/<\\/?(p|div)[^>]*>/gi, '\\n');\r\n  // Remove all other tags\r\n  const tmp = document.createElement('div');\r\n  tmp.innerHTML = withBreaks;\r\n  const text = tmp.textContent || tmp.innerText || '';\r\n  // Split by double newlines or single newlines, filter empty\r\n  return text.split(/\\n+/).map(s => s.trim()).filter(Boolean);\r\n}\r\n\r\nconst ScopePage: React.FC<ScopePageProps> = ({ reportData, sectionId }) => {\r\n  const { updateSectionPage } = useSectionPages();\r\n  return (\r\n    <Page size=\"A4\" id={sectionId} style={{\r\n      flexDirection: 'column',\r\n      backgroundColor: '#ffffff',\r\n      padding: '20mm 15mm',\r\n      fontFamily: 'Helvetica',\r\n      fontSize: 12,\r\n    }}>\r\n      <View style={{ paddingHorizontal: 14, paddingTop: 1, flexDirection: 'column', flex: 1 }}>\r\n        <View style={{ flex: 1 }}>\r\n          <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#2563eb', marginBottom: 12, lineHeight: 1.4 }}>\r\n            SCOPE\r\n          </Text>\r\n          {reportData.scope ? (\r\n            htmlToParagraphs(reportData.scope).map((para, idx) => (\r\n              <Text key={idx} style={{ fontSize: 12, lineHeight: 1.4, marginBottom: 14, color: '#374151', textAlign: 'justify' }}>{para}</Text>\r\n            ))\r\n          ) : (\r\n            <Text style={{ fontSize: 12, lineHeight: 1.4, marginBottom: 18, color: '#374151', textAlign: 'justify' }}>\r\n              The scope of the assessment was limited to performing Vulnerability Assessment and Penetration Testing on the  {reportData.program_name} mentioned below:\r\n            </Text>\r\n          )}\r\n          <View style={{\r\n            borderRadius: 10,\r\n            borderWidth: 1,\r\n            borderColor: '#d1d5db',\r\n            marginBottom: 16,\r\n            width: '100%',\r\n            alignSelf: 'center',\r\n            backgroundColor: '#fff',\r\n            overflow: 'hidden',\r\n          }}>\r\n            <View style={{ flexDirection: 'column', width: '100%' }}>\r\n              {/* Table Header */}\r\n              <View style={{ flexDirection: 'row', backgroundColor: '#2563eb', borderTopLeftRadius: 10, borderTopRightRadius: 10 }}>\r\n                <Text style={{ padding: 14, textAlign: 'center', fontWeight: 'bold', width: '20%', color: '#fff', fontSize: 12, lineHeight: 1.4 }}>SL No.</Text>\r\n                <Text style={{ padding: 14, textAlign: 'left', fontWeight: 'bold', width: '30%', color: '#fff', fontSize: 12, lineHeight: 1.4 }}>Asset Type</Text>\r\n                <Text style={{ padding: 14, textAlign: 'left', fontWeight: 'bold', width: '50%', color: '#fff', fontSize: 12, lineHeight: 1.4 }}>Asset</Text>\r\n              </View>\r\n              {/* Table Rows */}\r\n              {reportData.target_details && reportData.target_details.map((target, idx) => (\r\n                <View key={idx} style={{\r\n                  flexDirection: 'row',\r\n                  backgroundColor: idx % 2 === 0 ? '#f1f5f9' : '#fff',\r\n                  borderTopWidth: idx === 0 ? 0 : 1,\r\n                  borderTopColor: '#e5e7eb',\r\n                }}>\r\n                  <Text style={{ padding: 12, width: '20%', textAlign: 'center', fontSize: 12, color: '#2563eb', fontWeight: 'bold', lineHeight: 1.4 }}>{String(idx + 1).padStart(2, '0')}</Text>\r\n                  <Text style={{ padding: 12, width: '30%', fontWeight: '600', fontSize: 12, lineHeight: 1.4 }}>{target.type}</Text>\r\n                  <Text style={{ padding: 12, width: '50%', fontSize: 12, lineHeight: 1.4 }}>{target.url}</Text>\r\n                </View>\r\n              ))}\r\n            </View>\r\n          </View>\r\n          <Text style={{ fontSize: 12, color: '#64748b', textAlign: 'center', marginTop: 8, lineHeight: 1.4 }}>Table 1: Scope of Work</Text>\r\n        </View>\r\n      </View>\r\n      <View style={styles.footer} fixed>\r\n        <Text style={styles.footerLeft}>{reportData.document_number || 'Document Number'}</Text>\r\n        <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />\r\n      </View>\r\n      <Text style={{ display: 'none' }} render={({ pageNumber }) => { updateSectionPage('Scope', pageNumber); return ''; }} fixed />\r\n    </Page>\r\n  );\r\n};\r\n\r\nexport default ScopePage; "], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,UAAU,QAAQ,qBAAqB;AAElE,SAASC,eAAe,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOxD,MAAMC,MAAM,GAAGJ,UAAU,CAACK,MAAM,CAAC;EAC/BC,MAAM,EAAE;IACNC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,MAAM;IACbC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBC,iBAAiB,EAAE;EACrB,CAAC;EACDC,UAAU,EAAE;IACVC,SAAS,EAAE,MAAM;IACjBC,IAAI,EAAE;EACR,CAAC;EACDC,WAAW,EAAE;IACXF,SAAS,EAAE,OAAO;IAClBC,IAAI,EAAE;EACR;AACF,CAAC,CAAC;;AAEF;AACA,SAASE,gBAAgBA,CAACC,IAAY,EAAY;EAChD,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB;EACA,IAAIC,UAAU,GAAGD,IAAI,CAACE,OAAO,CAAC,wBAAwB,EAAE,IAAI,CAAC;EAC7D;EACAD,UAAU,GAAGA,UAAU,CAACC,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC;EAC5D;EACA,MAAMC,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EACzCF,GAAG,CAACG,SAAS,GAAGL,UAAU;EAC1B,MAAMM,IAAI,GAAGJ,GAAG,CAACK,WAAW,IAAIL,GAAG,CAACM,SAAS,IAAI,EAAE;EACnD;EACA,OAAOF,IAAI,CAACG,KAAK,CAAC,KAAK,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;AAC7D;AAEA,MAAMC,SAAmC,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM;IAAEC;EAAkB,CAAC,GAAGzC,eAAe,CAAC,CAAC;EAC/C,oBACEE,OAAA,CAACN,IAAI;IAAC8C,IAAI,EAAC,IAAI;IAACC,EAAE,EAAEJ,SAAU;IAACK,KAAK,EAAE;MACpChC,aAAa,EAAE,QAAQ;MACvBiC,eAAe,EAAE,SAAS;MAC1BC,OAAO,EAAE,WAAW;MACpBC,UAAU,EAAE,WAAW;MACvBrC,QAAQ,EAAE;IACZ,CAAE;IAAAsC,QAAA,gBACA9C,OAAA,CAACL,IAAI;MAAC+C,KAAK,EAAE;QAAE7B,iBAAiB,EAAE,EAAE;QAAEkC,UAAU,EAAE,CAAC;QAAErC,aAAa,EAAE,QAAQ;QAAEM,IAAI,EAAE;MAAE,CAAE;MAAA8B,QAAA,eACtF9C,OAAA,CAACL,IAAI;QAAC+C,KAAK,EAAE;UAAE1B,IAAI,EAAE;QAAE,CAAE;QAAA8B,QAAA,gBACvB9C,OAAA,CAACJ,IAAI;UAAC8C,KAAK,EAAE;YAAElC,QAAQ,EAAE,EAAE;YAAEwC,UAAU,EAAE,MAAM;YAAEvC,KAAK,EAAE,SAAS;YAAEwC,YAAY,EAAE,EAAE;YAAEC,UAAU,EAAE;UAAI,CAAE;UAAAJ,QAAA,EAAC;QAExG;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACNlB,UAAU,CAACmB,KAAK,GACfrC,gBAAgB,CAACkB,UAAU,CAACmB,KAAK,CAAC,CAACzB,GAAG,CAAC,CAAC0B,IAAI,EAAEC,GAAG,kBAC/CzD,OAAA,CAACJ,IAAI;UAAW8C,KAAK,EAAE;YAAElC,QAAQ,EAAE,EAAE;YAAE0C,UAAU,EAAE,GAAG;YAAED,YAAY,EAAE,EAAE;YAAExC,KAAK,EAAE,SAAS;YAAEM,SAAS,EAAE;UAAU,CAAE;UAAA+B,QAAA,EAAEU;QAAI,GAA9GC,GAAG;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAkH,CACjI,CAAC,gBAEFtD,OAAA,CAACJ,IAAI;UAAC8C,KAAK,EAAE;YAAElC,QAAQ,EAAE,EAAE;YAAE0C,UAAU,EAAE,GAAG;YAAED,YAAY,EAAE,EAAE;YAAExC,KAAK,EAAE,SAAS;YAAEM,SAAS,EAAE;UAAU,CAAE;UAAA+B,QAAA,GAAC,iHACO,EAACV,UAAU,CAACsB,YAAY,EAAC,mBAC1I;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP,eACDtD,OAAA,CAACL,IAAI;UAAC+C,KAAK,EAAE;YACXiB,YAAY,EAAE,EAAE;YAChBC,WAAW,EAAE,CAAC;YACdC,WAAW,EAAE,SAAS;YACtBZ,YAAY,EAAE,EAAE;YAChBa,KAAK,EAAE,MAAM;YACbC,SAAS,EAAE,QAAQ;YACnBpB,eAAe,EAAE,MAAM;YACvBqB,QAAQ,EAAE;UACZ,CAAE;UAAAlB,QAAA,eACA9C,OAAA,CAACL,IAAI;YAAC+C,KAAK,EAAE;cAAEhC,aAAa,EAAE,QAAQ;cAAEoD,KAAK,EAAE;YAAO,CAAE;YAAAhB,QAAA,gBAEtD9C,OAAA,CAACL,IAAI;cAAC+C,KAAK,EAAE;gBAAEhC,aAAa,EAAE,KAAK;gBAAEiC,eAAe,EAAE,SAAS;gBAAEsB,mBAAmB,EAAE,EAAE;gBAAEC,oBAAoB,EAAE;cAAG,CAAE;cAAApB,QAAA,gBACnH9C,OAAA,CAACJ,IAAI;gBAAC8C,KAAK,EAAE;kBAAEE,OAAO,EAAE,EAAE;kBAAE7B,SAAS,EAAE,QAAQ;kBAAEiC,UAAU,EAAE,MAAM;kBAAEc,KAAK,EAAE,KAAK;kBAAErD,KAAK,EAAE,MAAM;kBAAED,QAAQ,EAAE,EAAE;kBAAE0C,UAAU,EAAE;gBAAI,CAAE;gBAAAJ,QAAA,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChJtD,OAAA,CAACJ,IAAI;gBAAC8C,KAAK,EAAE;kBAAEE,OAAO,EAAE,EAAE;kBAAE7B,SAAS,EAAE,MAAM;kBAAEiC,UAAU,EAAE,MAAM;kBAAEc,KAAK,EAAE,KAAK;kBAAErD,KAAK,EAAE,MAAM;kBAAED,QAAQ,EAAE,EAAE;kBAAE0C,UAAU,EAAE;gBAAI,CAAE;gBAAAJ,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClJtD,OAAA,CAACJ,IAAI;gBAAC8C,KAAK,EAAE;kBAAEE,OAAO,EAAE,EAAE;kBAAE7B,SAAS,EAAE,MAAM;kBAAEiC,UAAU,EAAE,MAAM;kBAAEc,KAAK,EAAE,KAAK;kBAAErD,KAAK,EAAE,MAAM;kBAAED,QAAQ,EAAE,EAAE;kBAAE0C,UAAU,EAAE;gBAAI,CAAE;gBAAAJ,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzI,CAAC,EAENlB,UAAU,CAAC+B,cAAc,IAAI/B,UAAU,CAAC+B,cAAc,CAACrC,GAAG,CAAC,CAACsC,MAAM,EAAEX,GAAG,kBACtEzD,OAAA,CAACL,IAAI;cAAW+C,KAAK,EAAE;gBACrBhC,aAAa,EAAE,KAAK;gBACpBiC,eAAe,EAAEc,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,MAAM;gBACnDY,cAAc,EAAEZ,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;gBACjCa,cAAc,EAAE;cAClB,CAAE;cAAAxB,QAAA,gBACA9C,OAAA,CAACJ,IAAI;gBAAC8C,KAAK,EAAE;kBAAEE,OAAO,EAAE,EAAE;kBAAEkB,KAAK,EAAE,KAAK;kBAAE/C,SAAS,EAAE,QAAQ;kBAAEP,QAAQ,EAAE,EAAE;kBAAEC,KAAK,EAAE,SAAS;kBAAEuC,UAAU,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAI,CAAE;gBAAAJ,QAAA,EAAEyB,MAAM,CAACd,GAAG,GAAG,CAAC,CAAC,CAACe,QAAQ,CAAC,CAAC,EAAE,GAAG;cAAC;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/KtD,OAAA,CAACJ,IAAI;gBAAC8C,KAAK,EAAE;kBAAEE,OAAO,EAAE,EAAE;kBAAEkB,KAAK,EAAE,KAAK;kBAAEd,UAAU,EAAE,KAAK;kBAAExC,QAAQ,EAAE,EAAE;kBAAE0C,UAAU,EAAE;gBAAI,CAAE;gBAAAJ,QAAA,EAAEsB,MAAM,CAACK;cAAI;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClHtD,OAAA,CAACJ,IAAI;gBAAC8C,KAAK,EAAE;kBAAEE,OAAO,EAAE,EAAE;kBAAEkB,KAAK,EAAE,KAAK;kBAAEtD,QAAQ,EAAE,EAAE;kBAAE0C,UAAU,EAAE;gBAAI,CAAE;gBAAAJ,QAAA,EAAEsB,MAAM,CAACM;cAAG;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GARrFG,GAAG;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASR,CACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACPtD,OAAA,CAACJ,IAAI;UAAC8C,KAAK,EAAE;YAAElC,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEM,SAAS,EAAE,QAAQ;YAAE4D,SAAS,EAAE,CAAC;YAAEzB,UAAU,EAAE;UAAI,CAAE;UAAAJ,QAAA,EAAC;QAAsB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9H;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACPtD,OAAA,CAACL,IAAI;MAAC+C,KAAK,EAAEzC,MAAM,CAACE,MAAO;MAACyE,KAAK;MAAA9B,QAAA,gBAC/B9C,OAAA,CAACJ,IAAI;QAAC8C,KAAK,EAAEzC,MAAM,CAACa,UAAW;QAAAgC,QAAA,EAAEV,UAAU,CAACyC,eAAe,IAAI;MAAiB;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACxFtD,OAAA,CAACJ,IAAI;QAAC8C,KAAK,EAAEzC,MAAM,CAACgB,WAAY;QAAC6D,MAAM,EAAEA,CAAC;UAAEC,UAAU;UAAEC;QAAW,CAAC,KAAM,GAAED,UAAW,MAAKC,UAAW;MAAE;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxG,CAAC,eACPtD,OAAA,CAACJ,IAAI;MAAC8C,KAAK,EAAE;QAAEuC,OAAO,EAAE;MAAO,CAAE;MAACH,MAAM,EAAEA,CAAC;QAAEC;MAAW,CAAC,KAAK;QAAExC,iBAAiB,CAAC,OAAO,EAAEwC,UAAU,CAAC;QAAE,OAAO,EAAE;MAAE,CAAE;MAACH,KAAK;IAAA;MAAAzB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC1H,CAAC;AAEX,CAAC;AAAChB,EAAA,CAlEIH,SAAmC;EAAA,QACTrC,eAAe;AAAA;AAAAoF,EAAA,GADzC/C,SAAmC;AAoEzC,eAAeA,SAAS;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}