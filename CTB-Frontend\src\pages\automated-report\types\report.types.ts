export interface ReportSection {
  id: string;
  title: string;
  content: string;
}

export interface TargetDetail {
  type: string;
  url: string;
}

export interface ReportListItem {
  abbreviation: string;
  title: string;
  severity_category: string;
  status: string;
}

export interface DetailedFinding {
  abbreviation: string;
  title: string;
  severity_category: 'Critical' | 'High' | 'Medium' | 'Low';
  status: 'Open' | 'Closed' | 'In Progress';
  scope: string;
  description: string;
  instructions: string;
  impact: string;
  fix: string;
  submitted_date: string;
}

export interface Report {
  abbreviation: string;
  title: string;
  severity_category: string;
  status: string;
}

export interface SeverityCounts {
  Open: number;
  Closed: number;
  Total: number;
}

export interface ProgramDetail {
  program_id: number;
  program_title: string;
  testing_type?: string;
  targets: string[];
  expected_start_date?: string;
  test_lead?: string;
  prepared_by?: string;
  reviewed_by?: string;
  approved_by?: string;
}

export interface ReportData {
  report_title: string;
  company_name: string;
  program_name?: string;
  document_number: string;
  version_number: string;
  current_date: string;
  revision_date: string;
  prepared_by: string;
  reviewed_by: string;
  approved_by: string;
  test_lead: string;
  version_description: string;
  executive_summary: string;
  key_findings: string;
  scope: string;
  methodology: {
    web?: boolean;
    network?: boolean;
    mobile?: boolean;
  };
  objectives?: string; // <-- Added for project objectives
  findings: string;
  recommendations: string;
  recommendations_list?: Array<{
    title: string;
    description: string;
  }>;
  conclusion: string;
  program_details: ProgramDetail[];
  target_details: Array<{
    type: string;
    url: string;
  }>;
  reports_list: Array<{
    abbreviation: string;
    title: string;
    severity_category: string;
    status: string;
  }>;
  open_close_counts_by_severity: {
    Critical: {
      Open: number;
      Closed: number;
      Total: number;
    };
    High: {
      Open: number;
      Closed: number;
      Total: number;
    };
    Medium: {
      Open: number;
      Closed: number;
      Total: number;
    };
    Low: {
      Open: number;
      Closed: number;
      Total: number;
    };
  };
  total_open: number;
  total_closed: number;
  total_findings: number;
  date_of_request: string;
  assumptions?: string[];
  disclaimer?: string;
  detailed_findings?: DetailedFinding[];
  branding_logo?: string; // White label logo URL
  branding_company?: string; // White label company name
  status?: ProgramReportStatus;
  automated_report_id?: string;
  vulnerability_ratings?: {
    critical: string;
    high: string;
    medium: string;
    low: string;
  };
}

export type ProgramReportStatus =
  | 'draft'
  | 'qa_review'
  | 'admin_review'
  | 'approved'
  | 'rejected'
  | 'business_review'
  | 'business_requested_changes'
  | 'changes_added'
  | 'report_updated'
  | 'business_approved'; 