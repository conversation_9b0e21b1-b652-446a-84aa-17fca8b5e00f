<!DOCTYPE html>
<html>
<head>
    <title>Test Image Spacing in PDF</title>
</head>
<body>
    <h1>Test Image Spacing Scenarios</h1>
    
    <!-- Scenario 1: Single image -->
    <div id="single-image">
        <h2>Single Image</h2>
        <p>This is text before the image.</p>
        <p><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" width="200" height="100" alt="Test image 1"></p>
        <p>This is text after the image.</p>
    </div>
    
    <!-- Scenario 2: Two consecutive images -->
    <div id="consecutive-images">
        <h2>Two Consecutive Images</h2>
        <p>Text before images.</p>
        <p><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" width="200" height="100" alt="Test image 1"></p>
        <p><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" width="200" height="100" alt="Test image 2"></p>
        <p>Text after images.</p>
    </div>
    
    <!-- Scenario 3: Three consecutive images -->
    <div id="three-images">
        <h2>Three Consecutive Images</h2>
        <p>Text before images.</p>
        <p><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" width="200" height="100" alt="Test image 1"></p>
        <p><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" width="200" height="100" alt="Test image 2"></p>
        <p><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" width="200" height="100" alt="Test image 3"></p>
        <p>Text after images.</p>
    </div>
    
    <!-- Scenario 4: Mixed content with images -->
    <div id="mixed-content">
        <h2>Mixed Content</h2>
        <p>This is a paragraph with text.</p>
        <p><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" width="200" height="100" alt="Test image 1"></p>
        <ul>
            <li>List item 1</li>
            <li>List item 2</li>
        </ul>
        <p><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" width="200" height="100" alt="Test image 2"></p>
        <blockquote>This is a blockquote between images.</blockquote>
        <p><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" width="200" height="100" alt="Test image 3"></p>
        <p>Final paragraph.</p>
    </div>
    
    <!-- Scenario 5: Large images that should be resized -->
    <div id="large-images">
        <h2>Large Images (Should be Auto-resized)</h2>
        <p>These images are specified with large dimensions but should be automatically resized:</p>
        <p><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" width="800" height="600" alt="Large image 1"></p>
        <p><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" width="1000" height="400" alt="Large image 2"></p>
        <p>Text after large images.</p>
    </div>
</body>
</html>
