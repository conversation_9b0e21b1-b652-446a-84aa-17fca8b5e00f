// usePDFEditorLogic.ts - Custom hook for all PDFEditor state, effects, and handlers
import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { getProgramReportById, getProgramReportChangeLog, updateReportStatus, updateProgramReport } from '../../../utils/api/endpoints/program-reports/program-reports';
import axios from '../../../utils/api/axios';
import useUserCredentials from '../../../utils/hooks/user/useUserCredentials';
import { useReactToPrint } from 'react-to-print';
import { UserRole } from '../../../utils/api/endpoints/user/credentials';
import { ReportData, DetailedFinding, Report } from '../types/report.types';
import { ProgramReportStatus } from '../types/report.types';

// Constants
const PREVIEW_UPDATE_DELAY = 500;
const SEVERITY_CATEGORIES = ['Critical', 'High', 'Medium', 'Low'] as const;
const STATUS_TYPES = ['Open', 'Closed', 'Total'] as const;
const MIN_EDITOR_WIDTH = 320;
const MAX_EDITOR_WIDTH = 700;
const DEFAULT_EDITOR_WIDTH = '50%';

// Utility functions (copy from PDFEditor)
const normalizeSeverityCategory = (category: string) => {
  const normalized = category.charAt(0).toUpperCase() + category.slice(1).toLowerCase();
  return normalized as (typeof SEVERITY_CATEGORIES)[number];
};

const getDefaultDisclaimer = (companyName: string) => `
  <p>
    Capture The Bug Ltd. has prepared this document exclusively for ${companyName}.
    Copying, or modification of this document is strictly prohibited without Capture The Bug Ltd.'s written consent, 
    except for specific purposes when such permission is granted. This document is confidential and proprietary material 
    of Capture The Bug Ltd. and must always be treated as such, not to be disclosed to third parties without prior consent.
  </p>
  
  <p>
    The information within is provided 'as-is,' without any warranty. Vulnerability assessments offer a snapshot of 
    security at a specific moment and are not exhaustive risk assessments. New vulnerabilities may emerge post-assessment. 
    Thus, this report is a guide, not a definitive risk analysis.
  </p>
  
  <p>
    Capture The Bug Ltd. assumes no liability for any changes, omissions, or errors in this document. 
    Capture The Bug Ltd. shall not be liable for any damages, financial or otherwise, arising out of the use or misuse 
    of this report by any current employee of ${companyName} or any member of the general public.
  </p>
`;

const getDefaultOpenCloseCounts = () => ({
  Critical: { Open: 0, Closed: 0, Total: 0 },
  High: { Open: 0, Closed: 0, Total: 0 },
  Medium: { Open: 0, Closed: 0, Total: 0 },
  Low: { Open: 0, Closed: 0, Total: 0 }
});

type NotificationState = { type: string; title: string; message: string; isVisible: boolean };

export function usePDFEditorLogic() {
  const { report_id } = useParams<{ report_id: string }>();
  const { role } = useUserCredentials();

  // State
  const [loading, setLoading] = useState(true);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [previewHtml, setPreviewHtml] = useState<string>('');
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [activeSection, setActiveSection] = useState<string>('cover');
  const [sectionData, setSectionData] = useState<Partial<ReportData>>({});
  const [statusUpdateLoading, setStatusUpdateLoading] = useState(false);
  const [notification, setNotification] = useState<NotificationState>({ type: 'info', title: '', message: '', isVisible: false });
  const [isFullViewOpen, setIsFullViewOpen] = useState(false);
  const [isChangeLogOpen, setIsChangeLogOpen] = useState(false);
  const [changeLog, setChangeLog] = useState<any[]>([]);
  const [changeLogLoading, setChangeLogLoading] = useState(false);
  const [previewMode, setPreviewMode] = useState<'full' | 'technical' | undefined>('full');
  const [editorWidth, setEditorWidth] = useState<string>(DEFAULT_EDITOR_WIDTH);
  const dragging = useRef(false);
  const [viewMode, setViewMode] = useState(false);
  const previewRef = useRef<HTMLDivElement>(null);
  const [currentData, setCurrentData] = useState<ReportData | null>(null);
  const [saveLoading, setSaveLoading] = useState(false);
  const [approveLoading, setApproveLoading] = useState(false);
  const [rejectLoading, setRejectLoading] = useState(false);

  // Notification helpers
  const showNotification = useCallback((type: 'success' | 'error' | 'info', title: string, message: string) => {
    setNotification({ type, title, message, isVisible: true });
  }, []);
  const hideNotification = useCallback(() => {
    setNotification((prev: NotificationState) => ({ ...prev, isVisible: false }));
  }, []);

  // Full view modal
  const openFullView = useCallback(() => setIsFullViewOpen(true), []);
  const closeFullView = useCallback(() => setIsFullViewOpen(false), []);

  // Change log
  const openChangeLog = useCallback(async () => {
    if (!report_id) return;
    setIsChangeLogOpen(true);
    setChangeLogLoading(true);
    try {
      const res = await getProgramReportChangeLog(report_id);
      if (res.success) {
        setChangeLog(res.data);
      } else {
        setChangeLog([]);
      }
    } catch (e) {
      setChangeLog([]);
    } finally {
      setChangeLogLoading(false);
    }
  }, [report_id]);
  const closeChangeLog = useCallback(() => setIsChangeLogOpen(false), []);

  // Preview update
  const updatePreview = useCallback(async (data: ReportData) => {
    if (!report_id) return;
    try {
      setPreviewLoading(true);
      const response = await axios.post(
        `/v2/program-reports/${report_id}/preview`,
        { reportData: data },
        { headers: { 'Accept': 'text/html' }, responseType: 'text' }
      );
      setPreviewHtml(response.data);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error updating preview:', error);
    } finally {
      setPreviewLoading(false);
    }
  }, [report_id]);

  const debouncedUpdatePreview = useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    return (data: ReportData) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => updatePreview(data), PREVIEW_UPDATE_DELAY);
    };
  }, [updatePreview]);

  // Data update
  const handleDataUpdate = useCallback((updates: Partial<ReportData>) => {
    if (!reportData) return;
    let updatedData = { ...reportData, ...updates };
    setReportData(updatedData);
    setCurrentData(updatedData); // <-- ensure currentData is always up to date
    setSectionData(prev => ({ ...prev, ...updates }));
    debouncedUpdatePreview(updatedData);
  }, [reportData, debouncedUpdatePreview]);

  // Data fetching
  const fetchReportData = useCallback(async () => {
    if (!report_id) return;
    try {
      setLoading(true);
      const response = await getProgramReportById(report_id);
      if (response.status === 'success') {
        let data = response.data;
        // Robustly handle methodology as stringified JSON or object
        if (typeof data.methodology === 'string') {
          try {
            // Try to parse as JSON
            const parsed = JSON.parse(data.methodology);
            if (typeof parsed === 'object' && parsed !== null) {
              data.methodology = parsed;
            } else {
              // Fallback: old CSV logic
              data.methodology = data.methodology.split(',').reduce((acc: any, key: string) => {
                acc[key.trim()] = true;
                return acc;
              }, {});
            }
          } catch (e) {
            // Fallback: old CSV logic
            data.methodology = data.methodology.split(',').reduce((acc: any, key: string) => {
              acc[key.trim()] = true;
              return acc;
            }, {});
          }
        }
        // Patch: Ensure methodology default is always set if missing or empty
        if (
          !data.methodology ||
          (typeof data.methodology === 'object' &&
            !data.methodology.web &&
            !data.methodology.network &&
            !data.methodology.mobile)
        ) {
          data.methodology = { web: true, network: false, mobile: false };
        }
        setReportData(data);
        await updatePreview(data);
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error fetching report data:', error);
    } finally {
      setLoading(false);
    }
  }, [report_id, updatePreview]);

  useEffect(() => {
    if (report_id) {
      fetchReportData();
    }
  }, [report_id, fetchReportData]);

  useEffect(() => {
    setCurrentData(reportData);
  }, [reportData]);

  useEffect(() => {
    // If reportData is loaded and methodology.web is true, ensure preview is up to date
    if (reportData && reportData.methodology && reportData.methodology.web) {
      updatePreview(reportData);
    }
    // Only run when reportData changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [reportData]);

  useEffect(() => {
    // Patch: Ensure methodology default is always set if missing or empty for new reports (no report_id)
    if (!report_id && reportData) {
      if (
        !reportData.methodology ||
        (typeof reportData.methodology === 'object' &&
          !reportData.methodology.web &&
          !reportData.methodology.network &&
          !reportData.methodology.mobile)
      ) {
        const patched = { ...reportData, methodology: { web: true, network: false, mobile: false } };
        setReportData(patched);
        setCurrentData(patched);
        debouncedUpdatePreview(patched);
      }
    }
  }, [report_id, reportData, debouncedUpdatePreview]);

  // Status update
  const updateReportStatusHandler = useCallback(async (newStatus: ProgramReportStatus) => {
    if (!report_id) return;
    try {
      setStatusUpdateLoading(true);
      const response = await updateReportStatus(report_id, newStatus);
      if (response.success) {
        setReportData(prev => prev ? { ...prev, status: newStatus } : null);
        showNotification('success', 'Report Status Updated', `Report status updated to ${newStatus}`);
      } else {
        showNotification('error', 'Report Status Update Error', 'Failed to update report status. Please try again.');
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error updating report status:', error);
      showNotification('error', 'Report Status Update Error', 'Error updating report status. Please try again.');
    } finally {
      setStatusUpdateLoading(false);
    }
  }, [report_id, showNotification]);

  // Save
  const handleSave = useCallback(async () => {
    if (!reportData) return;
    try {
      setSaveLoading(true);
      const updatedData = { ...reportData, ...sectionData };
      const response = await updateProgramReport(report_id!, updatedData);

      // Use the response data from backend to ensure we have the latest state
      const savedData = response.success ? response.data : updatedData;
      setReportData(savedData);
      setCurrentData(savedData);
      setSectionData({});
      await updatePreview(savedData);
      showNotification('success', 'Report Saved', 'Report saved successfully!');
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error saving report:', error);
      showNotification('error', 'Report Save Error', 'Error saving report. Please try again.');
    } finally {
      setSaveLoading(false);
    }
  }, [report_id, reportData, sectionData, updatePreview, showNotification]);

  // Approve
  const handleApprove = useCallback(async () => {
    if (!reportData) return;
    try {
      setApproveLoading(true);
      // Always save first, even if there are no unsaved changes
      const updatedData = { ...reportData, ...sectionData };
      // Step 1: Save the report data
      const response = await updateProgramReport(report_id!, updatedData);
      const savedData = response.success ? response.data : updatedData;
      setReportData(savedData);
      await updatePreview(savedData);
      setSectionData({});
      // Step 2: Determine the new status based on role and current status
      let newStatus: ProgramReportStatus | null = null;
      let roleStr = '';
      if (typeof role === 'number') {
        roleStr = {
          1: 'RESEARCHER', 2: 'BUSINESS', 3: 'ADMIN', 4: 'QA', 5: 'ADMIN_MANAGER', 6: 'DEVELOPER',
          7: 'BUSINESS_MANAGER',
          8: 'BUSINESS_ADMINISTRATOR',
          9: 'SUB_ADMIN',
        }[role] || '';
      } else if (typeof role === 'string') {
        roleStr = role;
      }
      roleStr = roleStr.toUpperCase();
      const statusStr = (reportData.status || '').toLowerCase();
      if (roleStr === 'QA') {
        if (statusStr === 'draft' || statusStr === 'qa_review') {
          newStatus = 'admin_review';
        } else if (statusStr === 'business_requested_changes') {
          newStatus = 'changes_added';
        }
      } else if ([
        'ADMIN',
        'SUB_ADMIN',
        'ADMIN_MANAGER'
      ].includes(roleStr)) {
        if (statusStr === 'admin_review' || statusStr === 'business_requested_changes') {
          newStatus = 'approved';
        } else if (statusStr === 'changes_added') {
          newStatus = 'report_updated';
        }
      }
      // Step 3: Update the status only after successful save
      if (newStatus) {
        await updateReportStatusHandler(newStatus);
      }
      showNotification('success', 'Report Approved', 'Report saved and approved successfully!');
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error approving report:', error);
      showNotification('error', 'Report Approval Error', 'Error saving or approving report. Please try again.');
    } finally {
      setApproveLoading(false);
    }
  }, [report_id, reportData, sectionData, updatePreview, updateReportStatusHandler, role, showNotification]);

  // Reject
  const handleReject = useCallback(async () => {
    if (!reportData) return;
    try {
      setRejectLoading(true);
      const updatedData = { ...reportData, ...sectionData };
      const response = await updateProgramReport(report_id!, updatedData);
      const savedData = response.success ? response.data : updatedData;
      setReportData(savedData);
      await updatePreview(savedData);
      setSectionData({});
      await updateReportStatusHandler('draft');
      showNotification('success', 'Report Rejected', 'Report rejected and sent back to QA for editing!');
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error rejecting report:', error);
      showNotification('error', 'Report Rejection Error', 'Error rejecting report. Please try again.');
    } finally {
      setRejectLoading(false);
    }
  }, [report_id, reportData, sectionData, updatePreview, updateReportStatusHandler, showNotification]);

  // Permissions
  const canApprove = useMemo(() => {
    if (!reportData) return false;
    let roleStr = '';
    if (typeof role === 'number') {
      roleStr = {
        1: 'RESEARCHER', 2: 'BUSINESS', 3: 'ADMIN', 4: 'QA', 5: 'ADMIN_MANAGER', 6: 'DEVELOPER',
        7: 'BUSINESS_MANAGER',
        8: 'BUSINESS_ADMINISTRATOR',
        9: 'SUB_ADMIN',
      }[role] || '';
    } else if (typeof role === 'string') {
      roleStr = role;
    }
    roleStr = roleStr.toUpperCase();
    const statusStr = (reportData.status || '').toLowerCase();
    if (roleStr === 'QA') {
      return statusStr === 'draft' || statusStr === 'qa_review' || statusStr === 'business_requested_changes';
    }
    if ([
      'ADMIN',
      'SUB_ADMIN',
      'ADMIN_MANAGER'
    ].includes(roleStr)) {
      return statusStr === 'admin_review' || statusStr === 'changes_added' || statusStr === 'business_requested_changes';
    }
    return false;
  }, [role, reportData]);

  const canReject = useMemo(() => {
    if (!reportData) return false;
    let roleStr = '';
    if (typeof role === 'number') {
      roleStr = {
        1: 'RESEARCHER', 2: 'BUSINESS', 3: 'ADMIN', 4: 'QA', 5: 'ADMIN_MANAGER', 6: 'DEVELOPER',
        7: 'BUSINESS_MANAGER',
        8: 'BUSINESS_ADMINISTRATOR',
        9: 'SUB_ADMIN',
      }[role] || '';
    } else if (typeof role === 'string') {
      roleStr = role;
    }
    return [
      'ADMIN',
      'SUB_ADMIN',
      'ADMIN_MANAGER'
    ].includes(roleStr.toUpperCase()) && reportData.status === 'admin_review';
  }, [role, reportData]);

  // Section handlers (pass-through)
  const handleInputChange = useCallback((field: string, value: string) => {
    handleDataUpdate({ [field]: value });
  }, [handleDataUpdate]);
  const handleHtmlChange = useCallback((field: string, value: string) => {
    handleDataUpdate({ [field]: value });
  }, [handleDataUpdate]);
  const handleDataChange = useCallback((field: string, value: any) => {
    handleDataUpdate({ [field]: value });
  }, [handleDataUpdate]);
  const handleFindingChange = useCallback((index: number, field: string | number | symbol, value: string, severity: string) => {
    if (!reportData) return;
    const updatedFindings = [...(reportData.detailed_findings || [])];
    const severityFindings = updatedFindings.filter(f => f.severity_category === severity);
    if (index >= 0 && index < severityFindings.length) {
      const findingToUpdate = severityFindings[index];
      const findingIndex = updatedFindings.findIndex(f => f === findingToUpdate);
      if (findingIndex !== -1) {
        updatedFindings[findingIndex] = {
          ...updatedFindings[findingIndex],
          [field]: value
        };
        handleDataUpdate({ detailed_findings: updatedFindings });
      }
    }
  }, [reportData, handleDataUpdate]);
  const handleAddFinding = useCallback((severity: string) => {
    if (!reportData) return;
    const severityAbbreviations: Record<string, string> = { Critical: 'C', High: 'H', Medium: 'M', Low: 'L' };
    // Only count findings of this severity
    const findingsOfThisSeverity = (reportData.detailed_findings || []).filter(
      f => (f.severity_category || '').toLowerCase() === severity.toLowerCase()
    );
    const newCount = findingsOfThisSeverity.length + 1;
    const abbreviation = `${severityAbbreviations[severity]}${newCount}`;
    const newFinding: DetailedFinding = {
      abbreviation: abbreviation,
      title: '',
      severity_category: severity as 'Critical' | 'High' | 'Medium' | 'Low',
      status: 'Open',
      scope: '',
      description: '',
      instructions: '',
      impact: '',
      fix: '',
      submitted_date: new Date().toISOString()
    };
    const updatedFindings = [...(reportData.detailed_findings || []), newFinding];
    handleDataUpdate({ detailed_findings: updatedFindings });
  }, [reportData, handleDataUpdate]);
  const handleRemoveFinding = useCallback((index: number, severity: string) => {
    if (!reportData) return;
    const updatedFindings = [...(reportData.detailed_findings || [])];
    const severityFindings = updatedFindings.filter(f => f.severity_category === severity);
    if (index >= 0 && index < severityFindings.length) {
      const findingToRemove = severityFindings[index];
      const findingIndex = updatedFindings.findIndex(f => f === findingToRemove);
      if (findingIndex !== -1) {
        updatedFindings.splice(findingIndex, 1);
        handleDataUpdate({ detailed_findings: updatedFindings });
      }
    }
  }, [reportData, handleDataUpdate]);
  const handleTargetDetailsChange = useCallback((index: number, field: string, value: string) => {
    if (!reportData) return;
    const newTargets = (reportData.target_details || []).map((target, i) => i === index ? { ...target, [field]: value } : target);
    handleDataUpdate({ target_details: newTargets });
  }, [reportData, handleDataUpdate]);
  const handleAddTarget = useCallback(() => {
    if (!reportData) return;
    const newTargets = [...(reportData.target_details || []), { type: '', url: '' }];
    handleDataUpdate({ target_details: newTargets });
  }, [reportData, handleDataUpdate]);
  const handleRemoveTarget = useCallback((index: number) => {
    if (!reportData) return;
    const newTargets = (reportData.target_details || []).filter((_, i) => i !== index);
    handleDataUpdate({ target_details: newTargets });
  }, [reportData, handleDataUpdate]);
  const handleTableChange = useCallback((field: string, value: number) => {
    // ...table change logic (copy from PDFEditor)
  }, [reportData, handleDataUpdate]);
  const handleKeyFindingChange = useCallback((index: number, field: string, value: string) => {
    if (!reportData) return;
    const updatedReports = [...(reportData.reports_list || [])];
    updatedReports[index] = { ...updatedReports[index], [field]: value };
    handleDataUpdate({ reports_list: updatedReports });
  }, [reportData, handleDataUpdate]);
  const handleAddKeyFinding = useCallback(() => {
    if (!reportData) return;
    const severityAbbreviations: Record<string, string> = { Critical: 'C', High: 'H', Medium: 'M', Low: 'L' };
    const defaultSeverity = 'Medium' as const;
    const severityCounts: Record<string, number> = { Critical: 0, High: 0, Medium: 0, Low: 0 };
    (reportData.reports_list || []).forEach(finding => {
      if (finding.severity_category && finding.severity_category in severityCounts) {
        severityCounts[finding.severity_category as keyof typeof severityCounts]++;
      }
    });
    const newCount = severityCounts[defaultSeverity] + 1;
    const abbreviation = `${severityAbbreviations[defaultSeverity]}${newCount}`;
    const newFinding = {
      abbreviation: abbreviation,
      title: 'New Finding',
      severity_category: defaultSeverity as 'Critical' | 'High' | 'Medium' | 'Low',
      status: 'Open'
    };
    const updatedReports = [...(reportData.reports_list || []), newFinding];
    handleDataUpdate({ reports_list: updatedReports });
  }, [reportData, handleDataUpdate]);
  const handleRemoveKeyFinding = useCallback((index: number) => {
    if (!reportData) return;
    const updatedReports = reportData.reports_list?.filter((_, i) => i !== index);
    handleDataUpdate({ reports_list: updatedReports });
  }, [reportData, handleDataUpdate]);
  const handleRecommendationsChange = useCallback((recommendations: { title: string; description: string }[]) => {
    setSectionData(prev => ({ ...prev, recommendations_list: recommendations }));
  }, []);
  const saveSectionChanges = useCallback(async () => {
    if (!report_id || !reportData) return;
    try {
      const updatedData = { ...reportData, ...sectionData };
      const response = await updateProgramReport(report_id!, updatedData);
      const savedData = response.success ? response.data : updatedData;
      setReportData(savedData);
      await updatePreview(savedData);
      setSectionData({});
      showNotification('success', 'Section Changes Saved', 'Section changes saved successfully!');
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error saving section changes:', error);
      showNotification('error', 'Section Changes Error', 'Error saving section changes. Please try again.');
    }
  }, [report_id, reportData, sectionData, updatePreview, showNotification]);

  // Print handler
  const handlePrint = useReactToPrint({
    content: (): HTMLDivElement | null => previewRef.current,
    documentTitle: reportData?.report_title || 'Security Report',
    removeAfterPrint: true,
    pageStyle: `
      @page { size: A4; margin: 0; }
      body { margin: 0; padding: 0; }
      .page { page-break-after: always; min-height: 297mm; }
      .page:last-child { page-break-after: avoid; }
      .no-break, img, table, .finding-card { break-inside: avoid !important; page-break-inside: avoid !important; }
      .page-break { break-after: page !important; page-break-after: always !important; }
    `,
  } as any);

  return {
    report_id,
    role,
    loading,
    previewLoading,
    previewHtml,
    reportData,
    activeSection,
    setActiveSection,
    sectionData,
    statusUpdateLoading,
    notification,
    setNotification,
    isFullViewOpen,
    setIsFullViewOpen,
    isChangeLogOpen,
    setIsChangeLogOpen,
    changeLog,
    setChangeLog,
    changeLogLoading,
    setChangeLogLoading,
    previewMode,
    setPreviewMode,
    editorWidth,
    setEditorWidth,
    dragging,
    viewMode,
    setViewMode,
    previewRef,
    handlePrint,
    showNotification,
    hideNotification,
    openFullView,
    closeFullView,
    openChangeLog,
    closeChangeLog,
    updatePreview,
    debouncedUpdatePreview,
    handleDataUpdate,
    fetchReportData,
    updateReportStatus: updateReportStatusHandler,
    handleSave,
    handleApprove,
    handleReject,
    canApprove,
    canReject,
    handleInputChange,
    handleHtmlChange,
    handleDataChange,
    handleFindingChange,
    handleAddFinding,
    handleRemoveFinding,
    handleTargetDetailsChange,
    handleAddTarget,
    handleRemoveTarget,
    handleTableChange,
    handleKeyFindingChange,
    handleAddKeyFinding,
    handleRemoveKeyFinding,
    handleRecommendationsChange,
    saveSectionChanges,
    currentData,
    saveLoading,
    approveLoading,
    rejectLoading,
  };
} 