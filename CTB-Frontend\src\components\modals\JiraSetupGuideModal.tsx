import React, { useState } from "react";
import { FiX, FiInfo, FiArrowRight, FiCheck, FiCopy } from "react-icons/fi";
import { HiOutlineExternalLink } from "react-icons/hi";

interface JiraSetupGuideModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const JiraSetupGuideModal: React.FC<JiraSetupGuideModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [activeTab, setActiveTab] = useState<"basic" | "webhook">("basic");
  const [copiedStep, setCopiedStep] = useState<string | null>(null);

  if (!isOpen) return null;

  const copyToClipboard = (text: string, stepId: string) => {
    navigator.clipboard.writeText(text);
    setCopiedStep(stepId);
    setTimeout(() => setCopiedStep(null), 2000);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <FiInfo className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Jira Integration Setup
              </h2>
              <p className="text-sm text-gray-600">
                Connect your project with Jira for seamless issue tracking
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <FiX className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab("basic")}
            className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === "basic"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
          >
            Basic Setup (Required)
          </button>
          <button
            onClick={() => setActiveTab("webhook")}
            className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === "webhook"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
          >
            Two-Way Sync (Optional)
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-220px)]">
          {activeTab === "basic" && (
            <div className="space-y-6">
              {/* Overview */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-medium text-blue-900 mb-2">What you'll need:</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Your Jira site URL</li>
                  <li>• Your Jira account email</li>
                  <li>• A Jira API token (we'll help you create this)</li>
                  <li>• Your project key</li>
                </ul>
              </div>

              {/* Steps */}
              <div className="space-y-6">
                {/* Step 1 */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                      1
                    </div>
                    <h4 className="font-medium text-gray-900">Find your Jira Site URL</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    This is the web address you use to access your Jira workspace.
                  </p>
                  <div className="bg-gray-50 rounded-lg p-3">
                    <p className="text-sm font-mono text-gray-800">
                      https://yourcompany.atlassian.net
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      Example: If you visit "acme.atlassian.net", your URL is "https://acme.atlassian.net"
                    </p>
                  </div>
                </div>

                {/* Step 2 */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                      2
                    </div>
                    <h4 className="font-medium text-gray-900">Use your Jira Email</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Enter the email address you use to log into Jira.
                  </p>
                  <div className="bg-gray-50 rounded-lg p-3">
                    <p className="text-sm font-mono text-gray-800">
                      <EMAIL>
                    </p>
                  </div>
                </div>

                {/* Step 3 */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                      3
                    </div>
                    <h4 className="font-medium text-gray-900">Create API Token</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Follow these steps to create your API token:
                  </p>
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <div className="w-5 h-5 bg-gray-200 rounded-full flex items-center justify-center text-xs font-medium mt-0.5">
                        a
                      </div>
                      <div className="flex-1">
                        <p className="text-sm text-gray-700">
                          Go to{" "}
                          <a
                            href="https://id.atlassian.com/manage-profile/security/api-tokens"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-700 inline-flex items-center gap-1"
                          >
                            Atlassian Account Settings
                            <HiOutlineExternalLink className="w-3 h-3" />
                          </a>
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-5 h-5 bg-gray-200 rounded-full flex items-center justify-center text-xs font-medium mt-0.5">
                        b
                      </div>
                      <p className="text-sm text-gray-700">Click "Create API token"</p>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-5 h-5 bg-gray-200 rounded-full flex items-center justify-center text-xs font-medium mt-0.5">
                        c
                      </div>
                      <p className="text-sm text-gray-700">Enter a label like "CTB Integration"</p>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-5 h-5 bg-gray-200 rounded-full flex items-center justify-center text-xs font-medium mt-0.5">
                        d
                      </div>
                      <p className="text-sm text-gray-700">Copy the generated token and paste it in the form</p>
                    </div>
                  </div>
                </div>

                {/* Step 4 */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                      4
                    </div>
                    <h4 className="font-medium text-gray-900">Find your Project Key</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Your project key is usually 2-10 uppercase letters.
                  </p>
                  <div className="space-y-3">
                    <div className="bg-gray-50 rounded-lg p-3">
                      <p className="text-sm font-mono text-gray-800 mb-1">
                        Examples: PROJ, DEV, SUPPORT, MBA
                      </p>
                      <p className="text-xs text-gray-500">
                        You can find this in your Jira project URL or project settings
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Success Message */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center gap-2 text-green-800">
                  <FiCheck className="w-4 h-4" />
                  <span className="font-medium">That's it!</span>
                </div>
                <p className="text-sm text-green-700 mt-1">
                  With these details, CTB will create Jira issues automatically when reports are approved.
                </p>
              </div>
            </div>
          )}

          {activeTab === "webhook" && (
            <div className="space-y-6">
              {/* Overview */}
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <h3 className="font-medium text-amber-900 mb-2">Two-Way Sync Benefits:</h3>
                <ul className="text-sm text-amber-800 space-y-1">
                  <li>• Close CTB reports directly from Jira</li>
                  <li>• Automatic validation of business rules</li>
                  <li>• Prevents accidental reopening of closed reports</li>
                </ul>
              </div>

              {/* Webhook Steps */}
              <div className="space-y-6">
                {/* Step 1 */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-6 h-6 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                      1
                    </div>
                    <h4 className="font-medium text-gray-900">Access Jira Settings</h4>
                  </div>
                  <div className="space-y-3">
                    <p className="text-sm text-gray-600">Go to your Jira administration:</p>
                    <div className="bg-gray-50 rounded-lg p-3">
                      <p className="text-sm text-gray-700">
                        Settings ⚙️ → System → WebHooks
                      </p>
                    </div>
                  </div>
                </div>

                {/* Step 2 */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-6 h-6 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                      2
                    </div>
                    <h4 className="font-medium text-gray-900">Create Webhook</h4>
                  </div>
                  <div className="space-y-3">
                    <p className="text-sm text-gray-600">Click "Create a WebHook" and fill in:</p>
                    
                    <div className="space-y-3">
                      <div className="bg-gray-50 rounded-lg p-3">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium text-gray-700">Name:</span>
                          <button
                            onClick={() => copyToClipboard("CTB Report Status Sync", "name")}
                            className="text-xs text-blue-600 hover:text-blue-700 flex items-center gap-1"
                          >
                            <FiCopy className="w-3 h-3" />
                            {copiedStep === "name" ? "Copied!" : "Copy"}
                          </button>
                        </div>
                        <p className="text-sm font-mono text-gray-800">CTB Report Status Sync</p>
                      </div>

                      <div className="bg-gray-50 rounded-lg p-3">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium text-gray-700">URL:</span>
                          <button
                            onClick={() => copyToClipboard("https://app.capturethebug.xyz/api/v2/webhooks/jira-webhook", "url")}
                            className="text-xs text-blue-600 hover:text-blue-700 flex items-center gap-1"
                          >
                            <FiCopy className="w-3 h-3" />
                            {copiedStep === "url" ? "Copied!" : "Copy"}
                          </button>
                        </div>
                        <p className="text-sm font-mono text-gray-800">
                          https://app.capturethebug.xyz/api/v2/webhooks/jira-webhook
                        </p>
                      </div>

                      <div className="bg-gray-50 rounded-lg p-3">
                        <span className="text-sm font-medium text-gray-700">Events:</span>
                        <div className="mt-1 space-y-1">
                          <p className="text-sm text-gray-800">☑️ Issue Updated</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Step 3 */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-6 h-6 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                      3
                    </div>
                    <h4 className="font-medium text-gray-900">Test & Save</h4>
                  </div>
                  <div className="space-y-3">
                    <p className="text-sm text-gray-600">
                      Click "Create" to save your webhook. The two-way sync will be active immediately!
                    </p>
                  </div>
                </div>
              </div>

              {/* Success Message */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center gap-2 text-green-800">
                  <FiCheck className="w-4 h-4" />
                  <span className="font-medium">Two-way sync enabled!</span>
                </div>
                <p className="text-sm text-green-700 mt-1">
                  You can now close CTB reports directly from Jira. The system will automatically validate business rules.
                </p>
              </div>

              {/* Additional Help */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-blue-900">Need More Help?</h4>
                    <p className="text-sm text-blue-700 mt-1">
                      Check out the official Jira documentation for detailed webhook setup.
                    </p>
                  </div>
                  <a
                    href="https://support.atlassian.com/jira-cloud-administration/docs/manage-webhooks/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
                  >
                    Jira Docs
                    <HiOutlineExternalLink className="w-4 h-4" />
                  </a>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center gap-4">
            <p className="text-sm text-gray-600">
              Need help? Contact our support team.
            </p>
            <a
              href="https://support.atlassian.com/jira-cloud-administration/docs/manage-webhooks/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm text-blue-600 hover:text-blue-700 underline flex items-center gap-1"
            >
              Jira Official Docs
              <HiOutlineExternalLink className="w-3 h-3" />
            </a>
          </div>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Got it!
          </button>
        </div>
      </div>
    </div>
  );
};

export default JiraSetupGuideModal;
