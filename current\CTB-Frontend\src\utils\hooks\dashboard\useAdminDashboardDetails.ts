import { useState, useEffect } from "react";
import { getAdminDashboardDetails } from "../../api/endpoints/dashboard/adminDashboard";
import {
  parseAdminDashboard,
  ParsedAdminDashboard
} from "../../api/endpoints/dashboard/adminDashboardParser";

const useAdminDashboardDetails = () => {
  const [details, setDetails] = useState<ParsedAdminDashboard | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDetails = async () => {
      try {
        const response = await getAdminDashboardDetails();
        const parsedDetails = parseAdminDashboard(response);
        setDetails(parsedDetails);
      } catch (err) {
        if (err instanceof Error) {
          setError(
            err.message ||
              "An error occurred while fetching Admin dashboard details."
          );
        } else {
          setError("An error occurred while fetching Admin dashboard details.");
        }
      } finally {
        setLoading(false);
      }
    };

    fetchDetails();
  }, []);

  return { details, loading, error };
};

export default useAdminDashboardDetails;
