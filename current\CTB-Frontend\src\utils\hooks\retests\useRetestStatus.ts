import { useState, useEffect } from "react";
import { getRetestStatusForReport } from "../../../utils/api/endpoints/retests/retests";
import axios from "axios";

const useRetestStatus = (
  reportId: number | undefined,
  dependencies: any[] = []
) => {
  const [retestStatus, setRetestStatus] = useState<string | null>(null);

  useEffect(() => {
    const fetchRetestStatus = async () => {
      if (reportId) {
        try {
          const status = await getRetestStatusForReport(reportId);
          setRetestStatus(status || "Request Retest");
        } catch (error) {
          if (axios.isAxiosError(error) && error.response?.status === 404) {
            setRetestStatus("Request Retest");
          } else {
            console.error("Failed to fetch retest status:", error);
            setRetestStatus("Request Retest");
          }
        }
      } else {
        setRetestStatus("Request Retest");
      }
    };
    fetchRetestStatus();
  }, [reportId, ...dependencies]);

  return retestStatus;
};

export default useRetestStatus;
