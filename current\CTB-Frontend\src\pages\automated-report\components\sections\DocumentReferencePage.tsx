import React from 'react';
import { Page, View, Text, StyleSheet } from '@react-pdf/renderer';
import { ReportData } from '../../types/report.types';
import { useSectionPages } from '../SectionPageContext';

interface DocumentReferencePageProps {
  reportData: ReportData;
  sectionId?: string;
}

const styles = StyleSheet.create({
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    fontSize: 10,
    color: 'grey',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  footerLeft: {
    textAlign: 'left',
    flex: 1,
  },
  footerRight: {
    textAlign: 'right',
    flex: 1,
  },
});

const DocumentReferencePage: React.FC<DocumentReferencePageProps> = ({ reportData, sectionId }) => {
  const { updateSectionPage } = useSectionPages();
  return (
    <Page size="A4" id={sectionId} style={{
      flexDirection: 'column',
      backgroundColor: '#ffffff',
      padding: '20mm 15mm',
      fontFamily: 'Helvetica',
      fontSize: 12,
    }}>
      <View style={{ marginBottom: 16 }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#2563eb', marginBottom: 12, lineHeight: 1.4 }}>
          DOCUMENT REFERENCE
        </Text>
      </View>
      {/* Document Information */}
      <View style={{ marginBottom: 20 }}>
      
        <View style={{ borderRadius: 8, overflow: 'hidden', borderWidth: 1, borderColor: '#e5e7eb', fontSize: 12 }}>
          <View style={{ flexDirection: 'column', width: '100%' }}>
            <View style={{ flexDirection: 'row', borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>
              <Text style={{ padding: 8, fontWeight: 'bold', backgroundColor: '#eff6ff', width: '30%', fontSize: 12, lineHeight: 1.4 }}>
                DOCUMENT TITLE
              </Text>
              <Text style={{ padding: 8, flex: 1, fontSize: 12, lineHeight: 1.4 }}>{reportData.report_title}</Text>
            </View>
            <View style={{ flexDirection: 'row', borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>
              <Text style={{ padding: 8, fontWeight: 'bold', backgroundColor: '#eff6ff', width: '30%', fontSize: 12, lineHeight: 1.4 }}>
                DOCUMENT NUMBER
              </Text>
              <Text style={{ padding: 8, flex: 1, fontSize: 12, lineHeight: 1.4 }}>{reportData.document_number}</Text>
            </View>
            <View style={{ flexDirection: 'row', borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>
              <Text style={{ padding: 8, fontWeight: 'bold', backgroundColor: '#eff6ff', width: '30%', fontSize: 12, lineHeight: 1.4 }}>
                CLASSIFICATION
              </Text>
              <View style={{ padding: 8 }}>
                <View style={{
                  backgroundColor: '#fef2f2',
                  paddingHorizontal: 16,
                  paddingVertical: 4,
                  borderRadius: 12,
                  alignSelf: 'flex-start'
                }}>
                  <Text style={{ color: '#dc2626', fontSize: 10, lineHeight: 1.4 }}>Confidential</Text>
                </View>
              </View>
            </View>
            <View style={{ flexDirection: 'row', borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>
              <Text style={{ padding: 8, fontWeight: 'bold', backgroundColor: '#eff6ff', width: '30%', fontSize: 12, lineHeight: 1.4 }}>
                REFERENCE FILE
              </Text>
              <Text style={{ padding: 8, flex: 1, fontSize: 12, lineHeight: 1.4 }}>{Number(reportData.version_number) >= 2 ? `V${Number(reportData.version_number) - 1}` : 'NONE'}</Text>
            </View>
            <View style={{ flexDirection: 'row' }}>
              <Text style={{ padding: 8, fontWeight: 'bold', backgroundColor: '#eff6ff', width: '30%', fontSize: 12, lineHeight: 1.4 }}>
                REVISION DATE
              </Text>
              <Text style={{ padding: 8, flex: 1, fontSize: 12, lineHeight: 1.4 }}>{reportData.revision_date}</Text>
            </View>
          </View>
        </View>
      </View>
      {/* Publish Date and Test Performed By */}
      <View style={{ flexDirection: 'row', marginBottom: 20, gap: 16 }}>
        {/* Publish Date Card */}
        <View style={{
          width: '50%',
          backgroundColor: '#eff6ff',
          borderRadius: 10,
          padding: 16,
          alignItems: 'center',
          justifyContent: 'center',
          borderWidth: 1,
          borderColor: '#dbeafe',
          minHeight: 100,
        }}>
          {/* Calendar SVG */}
          <View style={{ marginBottom: 8 }}>
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
              <rect x="3" y="5" width="18" height="16" rx="3" fill="#2563eb"/>
              <rect x="3" y="8" width="18" height="13" rx="2" fill="#fff"/>
              <rect x="7" y="12" width="2" height="2" rx="1" fill="#2563eb"/>
              <rect x="11" y="12" width="2" height="2" rx="1" fill="#2563eb"/>
              <rect x="15" y="12" width="2" height="2" rx="1" fill="#2563eb"/>
            </svg>
          </View>
          <Text style={{ fontWeight: 'bold', color: '#2563eb', fontSize: 13, marginBottom: 4, lineHeight: 1.4 }}>
            PUBLISH DATE
          </Text>
          <Text style={{ fontWeight: 'bold', fontSize: 16, color: '#1e293b', lineHeight: 1.4 }}>
            {reportData.current_date}
          </Text>
        </View>
        {/* Test Performed By Card */}
        <View style={{
          width: '50%',
          backgroundColor: '#eff6ff',
          borderRadius: 10,
          padding: 16,
          alignItems: 'center',
          justifyContent: 'center',
          borderWidth: 1,
          borderColor: '#dbeafe',
          minHeight: 100,
        }}>
          {/* User SVG */}
          <View style={{ marginBottom: 8 }}>
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
              <circle cx="12" cy="8" r="4" fill="#2563eb"/>
              <rect x="5" y="15" width="14" height="5" rx="2.5" fill="#2563eb"/>
            </svg>
          </View>
          <Text style={{ fontWeight: 'bold', color: '#2563eb', fontSize: 13, marginBottom: 4, lineHeight: 1.4 }}>
            TEST PERFORMED BY
          </Text>
          <Text style={{ fontWeight: 'bold', fontSize: 16, color: '#1e293b', marginBottom: 4, lineHeight: 1.4 }}>
            {reportData.test_lead}
          </Text>
          <View style={{
            backgroundColor: '#dbeafe',
            paddingHorizontal: 12,
            paddingVertical: 4,
            borderRadius: 12,
            marginTop: 2,
            alignSelf: 'center'
          }}>
            <Text style={{ color: '#2563eb', fontSize: 12, lineHeight: 1.4 }}>Lead</Text>
          </View>
        </View>
      </View>
      {/* Contribution Table */}
      <View style={{ marginBottom: 20 }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 6 }}>
          <Text style={{ fontWeight: 'bold', color: '#2563eb', fontSize: 16, lineHeight: 1.4 }}>
            CONTRIBUTION
          </Text>
        </View>
        <View style={{ borderRadius: 8, overflow: 'hidden', borderWidth: 1, borderColor: '#e5e7eb', fontSize: 12 }}>
          <View style={{ flexDirection: 'column', width: '100%' }}>
            <View style={{ flexDirection: 'row', backgroundColor: '#eff6ff', borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>
              <Text style={{ padding: 8, textAlign: 'left', fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>ROLE</Text>
              <Text style={{ padding: 8, textAlign: 'left', fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>NAME</Text>
              <Text style={{ padding: 8, textAlign: 'left', fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>DESIGNATION</Text>
              <Text style={{ padding: 8, textAlign: 'left', fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>DATE</Text>
            </View>
            <View style={{ flexDirection: 'row', borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>
              <Text style={{ padding: 8, fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>PREPARED BY</Text>
              <Text style={{ padding: 8, color: '#1d4ed8', width: '25%', fontSize: 12, lineHeight: 1.4 }}>{reportData.prepared_by}</Text>
              <Text style={{ padding: 8, fontWeight: 'bold', color: '#92400e', width: '25%', fontSize: 12, lineHeight: 1.4 }}>PENTESTER</Text>
              <View style={{ padding: 8 }}>
                <View style={{ backgroundColor: '#dbeafe', paddingHorizontal: 12, paddingVertical: 4, borderRadius: 6, alignSelf: 'flex-start' }}>
                  <Text style={{ color: '#2563eb', fontSize: 12, lineHeight: 1.4 }}>{reportData.current_date}</Text>
                </View>
              </View>
            </View>
            <View style={{ flexDirection: 'row', borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>
              <Text style={{ padding: 8, fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>REVIEWED BY</Text>
              <Text style={{ padding: 8, color: '#1d4ed8', width: '25%', fontSize: 12, lineHeight: 1.4 }}>{reportData.reviewed_by}</Text>
              <Text style={{ padding: 8, fontWeight: 'bold', color: '#92400e', width: '25%', fontSize: 12, lineHeight: 1.4 }}>DY MANAGER</Text>
              <View style={{ padding: 8 }}>
                <View style={{ backgroundColor: '#dbeafe', paddingHorizontal: 12, paddingVertical: 4, borderRadius: 6, alignSelf: 'flex-start' }}>
                  <Text style={{ color: '#2563eb', fontSize: 12, lineHeight: 1.4 }}>{reportData.current_date}</Text>
                </View>
              </View>
            </View>
            <View style={{ flexDirection: 'row' }}>
              <Text style={{ padding: 8, fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>APPROVED BY</Text>
              <Text style={{ padding: 8, color: '#1d4ed8', width: '25%', fontSize: 12, lineHeight: 1.4 }}>{reportData.approved_by}</Text>
              <Text style={{ padding: 8, fontWeight: 'bold', color: '#92400e', width: '25%', fontSize: 12, lineHeight: 1.4 }}>MANAGER</Text>
              <View style={{ padding: 8 }}>
                <View style={{ backgroundColor: '#dbeafe', paddingHorizontal: 12, paddingVertical: 4, borderRadius: 6, alignSelf: 'flex-start' }}>
                  <Text style={{ color: '#2563eb', fontSize: 12, lineHeight: 1.4 }}>{reportData.current_date}</Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      </View>
      {/* Version Control Table */}
      <View>
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
          <Text style={{ fontWeight: 'bold', color: '#2563eb', fontSize: 16, lineHeight: 1.4 }}>
            VERSION CONTROL
          </Text>
        </View>
        <View style={{ borderRadius: 8, overflow: 'hidden', borderWidth: 1, borderColor: '#e5e7eb', fontSize: 12 }}>
          <View style={{ flexDirection: 'column', width: '100%' }}>
            <View style={{ flexDirection: 'row', backgroundColor: '#eff6ff', borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>
              <Text style={{ padding: 8, textAlign: 'left', fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>VERSION</Text>
              <Text style={{ padding: 8, textAlign: 'left', fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>DATE</Text>
              <Text style={{ padding: 8, textAlign: 'left', fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>AUTHOR</Text>
              <Text style={{ padding: 8, textAlign: 'left', fontWeight: 'bold', width: '25%', fontSize: 12, lineHeight: 1.4 }}>DESCRIPTION</Text>
            </View>
            <View style={{ flexDirection: 'row' }}>
              <Text style={{ padding: 8, width: '25%', fontSize: 12, lineHeight: 1.4 }}>{reportData.version_number}</Text>
              <Text style={{ padding: 8, width: '25%', fontSize: 12, lineHeight: 1.4 }}>{reportData.current_date}</Text>
              <Text style={{ padding: 8, width: '25%', fontSize: 12, lineHeight: 1.4 }}>{reportData.prepared_by}</Text>
              <Text style={{ padding: 8, width: '25%', fontSize: 12, lineHeight: 1.4 }}>{reportData.version_description ? reportData.version_description : (Number(reportData.version_number) > 1 ? `Version ${reportData.version_number} update` : 'Initial Report')}</Text>
            </View>
          </View>
        </View>
      </View>
      <View style={styles.footer} fixed>
        <Text style={styles.footerLeft}>{reportData.document_number || 'Document Number'}</Text>
        <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
      </View>
      <Text style={{ display: 'none' }} render={({ pageNumber }) => { updateSectionPage('DocumentReference', pageNumber); return ''; }} fixed />
    </Page>
  );
};

export default DocumentReferencePage; 