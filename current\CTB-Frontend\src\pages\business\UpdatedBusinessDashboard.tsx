import useBusinessDashboardDetails from "../../utils/hooks/dashboard/useBusinessDashboardDetails";
import SeverityDoughnutChart from "../../components/dashboard/business/SeverityDoughnutChart";
import ReportTimeSeriesLineChart from "../../components/dashboard/business/ReportTimeSeriesLineChart";
import DashboardStats from "../../components/dashboard/business/NumberBoxSection";
import ReportTypeRadarChart from "../../components/dashboard/business/ReportTypeRadarChart";
import RecentProgramDetailsTable from "../../components/dashboard/business/RecentProgramDetailsTable";
import RecentReportsTable from "../../components/dashboard/business/RecentReportsTable";
import RecentRetestsTable from "../../components/dashboard/business/RecentRetestsTable";
import ResearcherTeamList from "../../components/dashboard/business/ResearcherTeamList";
import usePageTitle from "../../utils/hooks/usePageTitle";

const UpdatedBusinessDashboard = () => {
  usePageTitle("Dashboard | Capture The Bug");
  const { details, loading, error } = useBusinessDashboardDetails();

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-slate-100 p-6">
        <div className="text-lg text-gray-600">Loading...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-slate-100 p-6">
        <div className="text-lg text-red-600">Error: {error}</div>
      </div>
    );
  }

  return (
    <div className="montserrat-font min-h-screen bg-slate-100 p-6">
      {details && (
        <>
          <DashboardStats
            totalReports={details.totalReports}
            totalResolvedReports={details.totalResolvedReports}
            // pentestHours={details.pentestHours}
            numberOfPrograms={details.numberOfPrograms}
          />

          <div className="mt-6 grid grid-cols-1 gap-4 md:grid-cols-3">
            <div className="">
              <SeverityDoughnutChart details={details} />
            </div>
            <div className="">
              <ReportTimeSeriesLineChart details={details} />
            </div>
            <div className="">
              <ReportTypeRadarChart details={details} />
            </div>
          </div>

          {/* Tables Row 1 */}
          <div className="mt-8 flex w-full flex-col gap-4 md:flex-row">
            <div className="w-full md:w-[65%]">
              <div className="flex h-[415px] flex-col rounded-lg bg-white shadow-sm">
                <div className="border-b border-gray-200 p-4">
                  <h2 className="text-lg font-semibold text-gray-900">
                    Reports for Action
                  </h2>
                </div>
                <div className="flex-1 overflow-auto">
                  <RecentReportsTable recentReports={details.recentReports} />
                </div>
              </div>
            </div>
            <div className="w-full md:w-[35%]">
              <div className="flex h-[415px] flex-col rounded-lg bg-white shadow-sm">
                {/* <div className="p-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">Recent Reports</h2>
                </div> */}
                <div className="flex-1 overflow-auto">
                  <ResearcherTeamList researchers={details.researchers} />
                </div>
              </div>
            </div>
          </div>

          {/* Tables Row 2 */}
          <div className="mt-8 flex w-full flex-col gap-4 md:flex-row">
            <div className="w-full md:w-[50%]">
              <div className="flex h-[415px] flex-col rounded-lg bg-white shadow-sm">
                <div className="border-b border-gray-200 p-4">
                  <h2 className="text-lg font-semibold text-gray-900">
                    Recent Programs
                  </h2>
                </div>
                <div className="flex-1 overflow-auto">
                  <RecentProgramDetailsTable
                    programDetails={details.programDetails}
                  />
                </div>
              </div>
            </div>

            <div className="w-full md:w-[50%]">
              <div className="flex h-[415px] flex-col rounded-lg bg-white shadow-sm">
                <div className="border-b border-gray-200 p-4">
                  <h2 className="text-lg font-semibold text-gray-900">
                    Recent Retests
                  </h2>
                </div>
                <div className="flex-1 overflow-auto">
                  <RecentRetestsTable recentRetests={details.recentRetests} />
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default UpdatedBusinessDashboard;
