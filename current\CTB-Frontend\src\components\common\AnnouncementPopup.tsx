import React, { useEffect, useState } from 'react';
import styles from '../../assets/stylesheets/AnnouncementPopup.module.scss';
import useUserCredentials from '../../utils/hooks/user/useUserCredentials';
import { UserRole } from '../../utils/api/endpoints/user/credentials';
import { useNavigate } from 'react-router-dom';

// To support future announcements, use a versioned key and launch date
const ANNOUNCEMENTS = [
  {
    key: 'announcement_20250719_dismissed',
    launchDate: new Date('2025-07-19T00:00:00Z'),
    displayDays: 10,
    // You can add more fields here for future announcements
  },
  // Add future announcements here
];

function getActiveAnnouncement() {
  const now = new Date();
  return ANNOUNCEMENTS.find(a => {
    const diff = now.getTime() - a.launchDate.getTime();
    const days = diff / (1000 * 60 * 60 * 24);
    return days >= 0 && days < a.displayDays && !localStorage.getItem(a.key);
  });
}

const AnnouncementPopup: React.FC = () => {
  const { role } = useUserCredentials();
  const navigate = useNavigate();
  const [visible, setVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [activeAnnouncement, setActiveAnnouncement] = useState<typeof ANNOUNCEMENTS[0] | null>(null);

  useEffect(() => {
    const announcement = getActiveAnnouncement();
    if (
      announcement &&
      role === UserRole.BUSINESS
    ) {
      setActiveAnnouncement(announcement);
      setVisible(true);
      setTimeout(() => setIsAnimating(true), 100);
    }
  }, [role]);

  const handleDismiss = () => {
    if (activeAnnouncement) {
      localStorage.setItem(activeAnnouncement.key, 'true');
    }
    setIsAnimating(false);
    setTimeout(() => setVisible(false), 300);
  };

  const handleTryNow = () => {
    navigate('/dashboard/pentest-reports');
    handleDismiss();
  };

  if (!visible) return null;

  return (
    <>
      <div className={styles.overlay} onClick={handleDismiss} />
      <div className={`${styles.announcementPopup} ${isAnimating ? styles.animate : ''}`}>
        <div className={styles.header}>
          <div className={styles.badge}>
            <span className={styles.badgeIcon}>✨</span>
            <span className={styles.badgeText}>NEW FEATURE</span>
          </div>
          <button 
            className={styles.closeBtn}
            onClick={handleDismiss}
            aria-label="Close announcement"
          >
            ×
          </button>
        </div>
        <div className={styles.content}>
          <div className={styles.iconContainer}>
            <div className={styles.mainIcon}>🚀</div>
          </div>
          <h2 className={styles.title}>
          Reporting Made Easy
          </h2>
          <p className={styles.subtitle}>
          You can now generate pentest reports directly from the platform!
          No exports. No delays. Just fast, expert-reviewed reporting.
          </p>
          <div className={styles.features}>
            <div className={styles.feature}>
              <span className={styles.featureIcon}>⚡</span>
              <span>Auto-generated reports</span>
            </div>
            <div className={styles.feature}>
              <span className={styles.featureIcon}>👥</span>
              <span>Reviewed by our pentesters</span>
            </div>
            <div className={styles.feature}>
              <span className={styles.featureIcon}>💬</span>
              <span>Real-time chat for edits</span>
            </div>
          </div>
        </div>
        <div className={styles.actions}>
          <button 
            className={styles.primaryBtn}
            onClick={handleTryNow}
          >
            Request Report
            <span className={styles.btnIcon}>→</span>
          </button>
          <button 
            className={styles.secondaryBtn}
            onClick={handleDismiss}
          >
            Maybe Later
          </button>
        </div>
        {/* <div className={styles.footer}>
          <span className={styles.footerText}>
            Available for Business accounts • Expert    -reviewed quality assurance
          </span>
        </div> */}
      </div>
    </>
  );
};

export default AnnouncementPopup; 