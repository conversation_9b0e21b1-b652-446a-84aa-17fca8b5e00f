import React, { useRef, useEffect, useState } from "react";
import { CTBRetestLog } from "../../../utils/api/endpoints/retests/parseRetests";
import { LogItem } from "./LogItems";

interface LogListProps {
  logs: CTBRetestLog[];
  className?: string;
}

export const LogList: React.FC<LogListProps> = ({
  logs,
  className = "w-full"
}) => {
  const logContainerRef = useRef<HTMLDivElement | null>(null);
  const [lineHeight, setLineHeight] = useState<number>(0);

  useEffect(() => {
    if (logContainerRef.current) {
      // Calculate the distance between the first and last pfp
      const logItems = logContainerRef.current.querySelectorAll(".log-item");
      if (logItems.length > 1) {
        const firstPfp = logItems[0].querySelector(".pfp");
        const lastPfp = logItems[logItems.length - 1].querySelector(".pfp");
        if (firstPfp && lastPfp) {
          const firstPfpRect = firstPfp.getBoundingClientRect();
          const lastPfpRect = lastPfp.getBoundingClientRect();
          const height = lastPfpRect.top - firstPfpRect.top;
          setLineHeight(height);
        }
      }
    }
  }, [logs]);

  return (
    <div
      className={`relative rounded-lg bg-white py-4 ${className}`}
      ref={logContainerRef}
    >
      <h3 className="mb-2 mt-2 pl-6 text-xl font-semibold text-black">
        Comments
      </h3>

      {/* Connection Line */}
      {lineHeight > 0 && (
        <div
          className="z-1 absolute left-[48px] top-[6.5rem] border-[1px] border-ctb-grey-100"
          style={{ height: lineHeight }}
        />
      )}
      <div className="space-y-6">
        {logs
          .slice()
          .reverse()
          .map(log => (
            <LogItem key={log.id} log={log} />
          ))}
      </div>
    </div>
  );
};
