import { useState } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { MdSend } from "react-icons/md";
import {
  PiPencilSimpleSlashDuotone,
  PiPencilSimpleDuotone
} from "react-icons/pi";
import CommentRichTextArea from "./CommentRichTextArea";

// Define types for props and comment structure
type Comment = {
  comment_id: number;
  user_id: number | string;
  username: string;
  pfp?: string;
  comment: string;
  createdAt: string;
  updatedAt: string;
};

type CommentBoxProps = {
  commentList: Comment[];
  reportId?: number | string; // Allow undefined
  userId?: number | string; // Allow undefined
  username?: string; // Allow undefined
  onSubmitComment: (reportId: number, comment: string) => void; // Match addNewComment signature
  onDeleteComment: (commentId: number) => void;
  onEditComment: (commentId: number, comment: string) => void;
};

const CommentBox = ({
  commentList,
  reportId,
  userId,
  username,
  onSubmitComment,
  onDeleteComment,
  onEditComment
}: CommentBoxProps) => {
  const [newComment, setNewComment] = useState<string>("");
  const [editMode, setEditMode] = useState(false);
  const [currentComment, setCurrentComment] = useState<{
    commentId: number;
    comment: string;
    index: number;
  }>({ commentId: 0, comment: "", index: 0 });

  const methods = useForm();

  const handleCommentSubmit = () => {
    if (newComment.trim() === "" || !reportId) return; // Guard against undefined reportId
    onSubmitComment(Number(reportId), newComment); // Convert reportId to number
    setNewComment("");
    methods.reset();
  };

  const handleCommentDelete = (commentId: number) => {
    onDeleteComment(commentId);
  };

  const handleCommentEdit = (comment: Comment, index: number) => {
    setCurrentComment({
      commentId: comment.comment_id,
      comment: comment.comment,
      index
    });
    setEditMode(true);
  };

  const handleEditedCommentSubmit = () => {
    if (currentComment.comment.trim() === "") return;
    onEditComment(currentComment.commentId, currentComment.comment);
    setEditMode(false);
    setCurrentComment({ commentId: 0, comment: "", index: 0 });
    methods.reset();
  };

  return (
    <FormProvider {...methods}>
      <div className="mt-12 rounded-md bg-white shadow-sm">
        <div className="py-6 text-2xl font-semibold">Comments</div>
        {/* Comment List */}
        <div className="max-h-72 max-w-[850px] overflow-y-auto">
          {commentList.length < 1 ? (
            <div className="text-gray-500">No Comments Yet...</div>
          ) : (
            commentList.map((comment, idx) => (
              <div
                key={comment.comment_id || idx}
                className="flex items-start gap-3 rounded-md border-b p-3"
              >
                {/* Profile Picture */}
                <div>
                  {comment.pfp ? (
                    <img
                      src={comment.pfp}
                      alt="Profile"
                      className="h-10 w-10 rounded-full"
                    />
                  ) : (
                    <div className="h-10 w-10 rounded-full bg-gray-300"></div>
                  )}
                </div>
                {/* Content */}
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    {/* Username */}
                    <div className="flex items-center gap-2">
                      <span className="font-bold text-blue-600">
                        {comment.username || "Unknown User"}
                      </span>
                    </div>
                    {/* Timestamp */}
                    <div className="text-xs text-gray-500">
                      {comment.comment.includes("[Deleted]")
                        ? `Deleted At: ${comment.updatedAt}`
                        : `Commented At: ${comment.createdAt}`}
                    </div>
                  </div>
                  {/* Comment */}
                  <div className="mt-2 text-gray-800">
                    {comment.comment.includes("[Deleted]") ? (
                      <span className="italic text-gray-400">
                        {comment.comment}
                      </span>
                    ) : (
                      <div
                        dangerouslySetInnerHTML={{ __html: comment.comment }}
                        className="max-w-[750px] break-words font-medium"
                      />
                    )}
                  </div>
                  {/* Edit/Delete Buttons */}
                  {comment.user_id === userId &&
                    !comment.comment.includes("[Deleted]") && (
                      <div className="mt-2 flex gap-2 text-sm">
                        <button
                          className="text-blue-500 hover:text-blue-700"
                          onClick={() => handleCommentEdit(comment, idx)}
                        >
                          Edit
                        </button>
                        <button
                          className="text-red-500 hover:text-red-700"
                          onClick={() =>
                            handleCommentDelete(comment.comment_id)
                          }
                        >
                          Delete
                        </button>
                      </div>
                    )}
                </div>
              </div>
            ))
          )}
        </div>
        {/* Comment Input */}
        <div className="relative mt-4">
          {editMode ? (
            <div className="flex max-w-[750px] flex-col gap-4">
              <CommentRichTextArea
                name="editComment"
                rules={{ required: "Comment cannot be empty" }}
                value={currentComment.comment}
                onChange={value =>
                  setCurrentComment(prev => ({ ...prev, comment: value }))
                }
              />
              <div className="flex gap-2">
                <button
                  className="rounded-md bg-red-500 p-2 text-white"
                  onClick={() => {
                    setEditMode(false);
                    setCurrentComment({ commentId: 0, comment: "", index: 0 });
                    methods.reset();
                  }}
                >
                  <PiPencilSimpleSlashDuotone />
                </button>
                <button
                  className="rounded-md bg-blue-500 p-2 text-white"
                  onClick={handleEditedCommentSubmit}
                >
                  <PiPencilSimpleDuotone />
                </button>
              </div>
            </div>
          ) : (
            <div className="relative max-w-[750px]">
              <CommentRichTextArea
                name="newComment"
                rules={{ required: "Comment cannot be empty" }}
                value={newComment}
                onChange={value => setNewComment(value)}
              />
              <button
                className="absolute bottom-2 right-2 flex h-10 w-10 items-center justify-center rounded-md bg-blue-500 p-3 text-white hover:bg-blue-600"
                onClick={handleCommentSubmit}
                disabled={!reportId || !userId} // Disable if required props are undefined
              >
                <MdSend />
              </button>
            </div>
          )}
        </div>
      </div>
    </FormProvider>
  );
};

export default CommentBox;
