import axios from "../../axios";

const URL = "/v2/retests";

export const retestRequest = async (reportId: number, comments?: string) => {
  const response = await axios.post(`${URL}/${reportId}`, {
    report_id: reportId,
    comments: comments || null // Include comments if provided
  });
  return response.data;
};

export const getRetestStatus = async (reportId: number) => {
  const response = await axios.get(`${URL}/status/${reportId}`);
  return response.data.status; // Assuming the response contains a 'status' field
};

export const getRetestStatusByRetestId = async (retestId: number) => {
  const response = await axios.get(`${URL}/retest-status/${retestId}`);
  return {
    status: response.data.retest.status,
    createdAt: response.data.retest.created_at
  };
};

export const getRetestsByUser = async () => {
  const response = await axios.get(`${URL}/user`); // Assuming the endpoint is /v2/retests/user
  return response.data; // Return the data containing the retests
};

// New function to get retest details with logs
export const getRetestWithLogs = async (retestId: string) => {
  const response = await axios.get(`${URL}/${retestId}/details`); // Assuming the endpoint is /v2/retests/:retestId/details
  return response.data; // Return the data containing the retest and logs
};

export const getReportDetailsByRetestId = async (retestId: string) => {
  const response = await axios.get(`${URL}/${retestId}/report-details`); // Assuming the endpoint is /v2/retests/:retestId/report-details
  return response.data; // Return the data containing the report details
};

// API function to get a specific retest
export const getRetest = async (retestId: string) => {
  const response = await axios.get(`${URL}/${retestId}`);
  return response.data; // Return the data containing the retest
};

export const updateRetestStatus = async (
  retestId: string,
  status: string,
  action_taken: string,
  comments: string,
  pocFile?: File
) => {
  const formData = new FormData();
  formData.append("status", status);
  formData.append("action_taken", action_taken);
  formData.append("comments", comments);
  if (pocFile) {
    formData.append("poc", pocFile);
  }

  const response = await axios.post(
    `${URL}/update-status/${retestId}`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
  return response.data;
};

export const deleteRetestRequest = async (reportId: string) => {
  const response = await axios.delete(`${URL}/${reportId}/request`);
  return response.data;
};

export const deleteRetestLog = async (logId: number) => {
  const response = await axios.delete(`${URL}/logs/${logId}`);
  return response.data;
};

export const getRetestStatusForReport = async (reportId: number) => {
  const response = await axios.get(`${URL}/status/${reportId}`);
  return response.data.status;
};

export const getRetestDetailsRightSection = async (retestId: string) => {
  const response = await axios.get(`${URL}/retest-details-right/${retestId}`);
  return response.data;
};
