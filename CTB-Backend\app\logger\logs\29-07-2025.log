2025-07-29T05:03:04.005Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T05:03:30.546Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-29T05:03:30.555Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:03:30.559Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:03:30.560Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:03:30.562Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:03:30.563Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.566Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:03:30.567Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.570Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.571Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:03:30.572Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.575Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:03:30.575Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.610Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.633Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:03:30.635Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:03:30.708Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-29T05:03:30.760Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:03:30.774Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:03:30.774Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:03:30.775Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:03:30.776Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:03:30.777Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.778Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:03:30.779Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.779Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.780Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:03:30.781Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.782Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:03:30.783Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.789Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.801Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:03:30.802Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:03:30.863Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:03:30.871Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:03:30.872Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:03:30.873Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:03:30.873Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:03:30.874Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.875Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:03:30.875Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.876Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.876Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:03:30.877Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.878Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:03:30.878Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.883Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.892Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:03:30.893Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:03:30.944Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:03:30.953Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:03:30.954Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:03:30.955Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:03:30.955Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:03:30.956Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.957Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:03:30.957Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.958Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.959Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:03:30.959Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.960Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:03:30.960Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.967Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:30.974Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:03:30.975Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:03:31.017Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:03:31.026Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:03:31.027Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:03:31.028Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:03:31.028Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:03:31.029Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:31.029Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:03:31.030Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:31.031Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:31.032Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:03:31.032Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:31.033Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:03:31.034Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:31.037Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:31.045Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:03:31.047Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:03:31.096Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:03:31.352Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:03:31.353Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:03:31.354Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:03:31.355Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:03:31.355Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:31.356Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:03:31.357Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:31.357Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:31.358Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:03:31.359Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:31.359Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:03:31.360Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:31.364Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:31.370Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:03:31.371Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:03:31.417Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:03:31.427Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:03:31.429Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:03:31.430Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:03:31.431Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:03:31.431Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:31.432Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:03:31.433Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:31.434Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:31.435Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:03:31.435Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:31.436Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:03:31.437Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:31.443Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:03:31.449Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:03:31.450Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:03:31.496Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:19:18.078Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T05:19:45.459Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-29T05:19:45.464Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:19:45.465Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:19:45.466Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:19:45.467Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:19:45.469Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.470Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:19:45.471Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.473Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.474Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:19:45.475Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.476Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:19:45.476Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.502Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:19:45.504Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:19:45.522Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.560Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-29T05:19:45.588Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:19:45.597Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:19:45.598Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:19:45.598Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:19:45.599Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:19:45.599Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.600Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:19:45.600Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.600Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.601Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:19:45.601Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.601Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:19:45.602Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.606Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.611Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:19:45.612Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:19:45.648Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:19:45.655Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:19:45.655Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:19:45.656Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:19:45.656Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:19:45.656Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.656Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:19:45.657Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.657Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.658Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:19:45.658Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.659Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:19:45.659Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.664Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.672Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:19:45.674Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:19:45.709Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:19:45.715Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:19:45.715Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:19:45.716Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:19:45.716Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:19:45.716Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.717Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:19:45.717Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.718Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.718Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:19:45.718Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.718Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:19:45.719Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.722Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.727Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:19:45.727Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:19:45.767Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:19:45.775Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:19:45.776Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:19:45.777Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:19:45.778Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:19:45.779Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.780Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:19:45.780Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.781Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.781Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:19:45.782Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.782Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:19:45.784Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.789Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.801Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:19:45.802Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:19:45.842Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:19:45.989Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:19:45.990Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:19:45.990Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:19:45.991Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:19:45.991Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.992Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:19:45.992Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.994Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.995Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:19:45.995Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.996Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:19:45.996Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:45.999Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:46.005Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:19:46.010Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:19:46.055Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:19:46.067Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:19:46.067Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:19:46.069Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:19:46.072Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:19:46.074Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:46.077Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:19:46.078Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:46.079Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:46.079Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:19:46.079Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:46.080Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:19:46.080Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:46.086Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:19:46.098Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:19:46.099Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:19:46.163Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:22:01.760Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-29T05:22:01.762Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:22:01.762Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:22:01.762Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:22:01.763Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:22:01.763Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.763Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:22:01.763Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.763Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.764Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:22:01.764Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.764Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:22:01.764Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.766Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:22:01.767Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:22:01.774Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.784Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-29T05:22:01.794Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:22:01.798Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:22:01.799Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:22:01.799Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:22:01.800Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:22:01.800Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.801Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:22:01.801Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.801Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.801Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:22:01.802Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.803Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:22:01.803Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.806Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.810Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:22:01.811Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:22:01.824Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:22:01.829Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:22:01.829Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:22:01.830Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:22:01.830Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:22:01.830Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.830Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:22:01.831Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.831Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.831Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:22:01.831Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.831Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:22:01.832Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.833Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.835Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:22:01.836Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:22:01.848Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:22:01.852Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:22:01.852Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:22:01.853Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:22:01.853Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:22:01.853Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.853Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:22:01.854Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.854Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.854Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:22:01.854Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.854Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:22:01.855Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.857Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.860Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:22:01.860Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:22:01.878Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:22:01.883Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:22:01.884Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:22:01.884Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:22:01.884Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:22:01.884Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.885Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:22:01.885Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.885Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.885Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:22:01.885Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.886Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:22:01.886Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.887Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.889Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:22:01.889Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:22:01.903Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:22:01.986Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:22:01.987Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:22:01.987Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:22:01.987Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:22:01.987Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.988Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:22:01.988Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.988Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.988Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:22:01.988Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.989Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:22:01.989Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.990Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:01.993Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:22:01.993Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:22:02.009Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:22:02.013Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:22:02.014Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:22:02.014Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:22:02.014Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:22:02.015Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:02.015Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:22:02.015Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:02.015Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:02.016Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:22:02.016Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:02.016Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:22:02.016Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:02.018Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:02.020Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:22:02.020Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:22:02.033Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:22:03.636Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-29T05:22:03.646Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 2 
2025-07-29T05:22:04.753Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:22:04.753Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:22:04.753Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:22:04.754Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:22:04.754Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.754Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:22:04.755Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.755Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.755Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:22:04.755Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.756Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:22:04.756Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.758Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.761Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:22:04.762Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:22:04.775Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:22:04.780Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:22:04.780Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:22:04.780Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:22:04.781Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:22:04.781Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.781Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:22:04.782Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.782Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.782Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:22:04.782Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.783Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:22:04.783Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.784Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.786Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:22:04.786Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:22:04.800Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:22:04.803Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:22:04.804Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:22:04.804Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:22:04.804Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:22:04.804Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.804Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:22:04.805Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.805Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.805Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:22:04.805Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.805Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:22:04.806Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.807Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.808Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:22:04.808Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:22:04.823Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:22:04.828Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:22:04.828Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:22:04.829Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:22:04.829Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:22:04.829Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.829Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:22:04.829Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.830Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.830Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:22:04.830Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.830Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:22:04.830Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.832Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.834Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:22:04.835Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:22:04.848Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:22:04.852Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:22:04.853Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:22:04.853Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:22:04.853Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:22:04.853Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.854Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:22:04.854Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.854Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.854Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:22:04.854Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.855Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:22:04.855Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.856Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.859Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:22:04.859Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:22:04.881Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:22:04.897Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:22:04.897Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:22:04.897Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:22:04.898Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:22:04.898Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.898Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:22:04.898Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.898Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.899Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:22:04.899Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.899Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:22:04.899Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.900Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.902Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:22:04.903Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:22:04.914Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:22:04.918Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:22:04.919Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:22:04.919Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:22:04.919Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:22:04.919Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.919Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:22:04.919Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.920Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.920Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:22:04.920Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.920Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:22:04.920Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.922Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:22:04.924Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:22:04.924Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:22:04.934Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:22:45.172Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-29T05:22:45.250Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:22:45.256Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T05:22:45.259Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-29T05:22:45.292Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-29T05:22:45.302Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:22:45.313Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:22:45.319Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:22:45.323Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:22:45.327Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-29T05:22:45.335Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:22:45.336Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-29T05:22:45.345Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-29T05:22:47.037Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T05:22:47.039Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T05:22:47.043Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-29T05:22:47.044Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-29T05:22:49.279Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:22:49.281Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T05:22:49.289Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:22:49.290Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-29T05:22:49.345Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T05:22:49.349Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-29T05:22:58.481Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T05:22:58.484Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-29T05:23:33.260Z  [POST | Report] info: <EMAIL> saving report... 
2025-07-29T05:23:33.265Z  [POST | Report] info: Parsed form data succesfully... 
2025-07-29T05:23:33.279Z  [POST | Report] info: Creating new <NAME_EMAIL> 
2025-07-29T05:23:33.302Z  [POST | Report] info: Successfully created new <NAME_EMAIL> 
2025-07-29T05:23:33.303Z  [POST | Report | Jira Integration] info: Jira integration disabled for program 5 
2025-07-29T05:23:33.337Z  [POST | Submit Report | Jira Integration] info: Jira integration enabled for report 17 
2025-07-29T05:23:33.344Z  [POST | Submit Report] info: Submitted report 17 
2025-07-29T05:23:33.347Z  [notifications.controller | sendNotification] info: notification: {"type":7,"payload":"17","message":"jira-finalllll waiting review for program mee"}, userIDs: 3 
2025-07-29T05:23:33.379Z  [GET | Reports] info: Retrieving report 17 for user 1... 
2025-07-29T05:23:33.384Z  [report.controller | getComments] info: User 1 requested comments for report 17 
2025-07-29T05:23:33.384Z  [report.controller | getComments] info: No comments found for report 17 
2025-07-29T05:23:33.400Z  [GET | Report] info: Retrieved report 17 for user 1. 
2025-07-29T05:23:33.401Z  [report.controller | getComments] info: User 1 requested comments for report 17 
2025-07-29T05:23:33.401Z  [report.controller | getComments] info: No comments found for report 17 
2025-07-29T05:23:40.579Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-29T05:23:40.625Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-29T05:23:40.634Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-29T05:23:42.999Z  [automated-report.controller | getAllPentestReports] info: Admin 3 requesting pentest reports with filters: {"page":"1","limit":"12","sort_by":"createdAt","sort_order":"DESC"} 
2025-07-29T05:23:43.013Z  [automated-report.controller | getAllPentestReports] info: Retrieved 0 reports in 14.49ms for role 3 
2025-07-29T05:23:43.019Z  [automated-report.controller | getAllPentestReports] info: Admin 3 requesting pentest reports with filters: {"page":"1","limit":"12","sort_by":"createdAt","sort_order":"DESC"} 
2025-07-29T05:23:43.021Z  [automated-report.controller | getAllPentestReports] info: Retrieved 0 reports in 2.11ms for role 3 
2025-07-29T05:23:44.522Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-29T05:23:44.530Z  [program.controller | getPrograms] info: Successfully got 5 programs for user with id 3 
2025-07-29T05:23:46.430Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-29T05:23:46.432Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-29T05:23:46.436Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-29T05:23:46.437Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-29T05:23:51.887Z  [POST | Report State] info: Updated report 17 state 
2025-07-29T05:23:51.891Z  [notifications.controller | sendNotification] info: notification: {"type":1,"payload":"17","message":"jira-finalllll has been approved by the CTB team"}, userIDs: 1,2 
2025-07-29T05:23:51.933Z  [POST | Report State | Jira Integration] info: Admin approved report 17, checking Jira configuration 
2025-07-29T05:23:51.934Z  [Jira Integration | Detailed] info: Starting Jira issue creation for report 17 - Config check 
2025-07-29T05:23:51.934Z  [Jira Integration | Detailed] info: Jira URL: Provided, Email: Provided, API Token: Provided, Project Key: Provided 
2025-07-29T05:23:51.934Z  [Jira Integration] info: Attempting to create Jira issue for report 17 in project CTB 
2025-07-29T05:23:51.935Z  [Jira Integration | Detailed] info: Using Jira API URL: https://prateekdp2877.atlassian.net/rest/api/2/issue 
2025-07-29T05:23:51.935Z  [Jira Integration | Detailed] info: Formatted description for Jira issue: 
*Report ID:* 17
*Severity:* CRITICAL
*Category:* Application Level Denial Of Service Dos > Critical... 
2025-07-29T05:23:51.935Z  [Jira Integration | Detailed] info: Fetching issue types for project CTB 
2025-07-29T05:23:52.655Z  [Jira Integration | Detailed] info: Received metaResponse: Success 
2025-07-29T05:23:52.655Z  [Jira Integration | Detailed] info: Project data: Available, Issue types: 3 
2025-07-29T05:23:52.655Z  [Jira Integration | Detailed] info: Using issue type: Task 
2025-07-29T05:23:52.656Z  [Jira Integration | Detailed] info: Prepared issue data: {"fields":{"project":{"key":"CTB"},"summary":"[CTB Report] jira-finalllll","description":"\n*Report ID:* 17\n*Severity:* CRITICAL\n*Category:* Application Level Denial Of Service Dos > Critical Impact And Or Easy Difficulty\n*Target:* fgg\n*Program:* mee\n*Report Link:* http://localhost:3000/dashboard/reports/17\n\n*Description:*\nffffff\n\n---\nThis issue was automatically created from CTB platform.\n","issuetype":{"name":"Task"},"assignee":{"name":"prateekdp2877"},"labels":["CTB","Security","reports"]}} 
2025-07-29T05:23:52.656Z  [Jira Integration | Detailed] info: Sending POST request to Jira API at https://prateekdp2877.atlassian.net/rest/api/2/issue 
2025-07-29T05:23:53.550Z  [Jira Integration | Detailed] info: Received response from Jira API: {"id":"10021","key":"CTB-5","self":"https://prateekdp2877.atlassian.net/rest/api/2/issue/10021"} 
2025-07-29T05:23:53.551Z  [Jira Integration] info: Successfully created Jira issue CTB-5 for report 17 
2025-07-29T05:23:53.556Z  [POST | Report State | Jira Integration] info: Successfully created Jira issue CTB-5 for approved report 17 and stored in additional_info 
2025-07-29T05:24:14.208Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-5 
2025-07-29T05:24:14.209Z  [Jira Webhook | Status Change] info: Issue CTB-5: To Do → Done 
2025-07-29T05:24:14.218Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-5 with status Done 
2025-07-29T05:24:14.223Z  [Jira Integration | Report Not Found] warn: No CTB report found for Jira issue CTB-5 in project CTB 
2025-07-29T05:27:07.729Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-5 
2025-07-29T05:27:07.730Z  [Jira Webhook | Status Change] info: Issue CTB-5: Done → To Do 
2025-07-29T05:27:07.730Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-5 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-29T05:27:47.185Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-5 
2025-07-29T05:27:47.185Z  [Jira Webhook | Status Change] info: Issue CTB-5: To Do → Done 
2025-07-29T05:27:47.185Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-5 with status Done 
2025-07-29T05:27:47.190Z  [Jira Integration | Report Not Found] warn: No CTB report found for Jira issue CTB-5 in project CTB 
2025-07-29T05:43:47.753Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T05:44:13.547Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-29T05:44:13.574Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-29T05:44:41.885Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-29T05:44:41.899Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-29T05:44:44.422Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-29T05:44:44.424Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-29T05:44:44.430Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-29T05:44:44.432Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-29T05:44:51.127Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-29T05:44:51.187Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-29T05:44:51.192Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:44:51.193Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:44:51.193Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:44:51.194Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:44:51.194Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.195Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:44:51.196Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.196Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.197Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:44:51.198Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.198Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:44:51.199Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.208Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:44:51.209Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:44:51.211Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.236Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-29T05:44:51.257Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:44:51.263Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:44:51.264Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:44:51.264Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:44:51.264Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:44:51.264Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.265Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:44:51.265Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.265Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.266Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:44:51.266Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.266Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:44:51.266Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.269Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.273Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:44:51.274Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:44:51.293Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:44:51.298Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:44:51.298Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:44:51.298Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:44:51.299Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:44:51.299Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.299Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:44:51.300Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.300Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.300Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:44:51.300Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.301Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:44:51.301Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.304Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.306Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:44:51.306Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:44:51.321Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:44:51.325Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:44:51.326Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:44:51.326Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:44:51.326Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:44:51.326Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.326Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:44:51.327Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.327Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.327Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:44:51.327Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.328Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:44:51.328Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.330Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.333Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:44:51.334Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:44:51.351Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:44:51.355Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:44:51.355Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:44:51.355Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:44:51.356Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:44:51.356Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.356Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:44:51.357Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.357Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.357Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:44:51.357Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.358Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:44:51.358Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.359Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.361Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:44:51.361Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:44:51.382Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:44:51.424Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:44:51.424Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:44:51.424Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:44:51.425Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:44:51.425Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.425Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:44:51.425Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.426Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.426Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:44:51.426Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.426Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:44:51.427Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.428Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.430Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:44:51.430Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:44:51.447Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:44:51.451Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:44:51.452Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:44:51.452Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:44:51.453Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:44:51.453Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.453Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:44:51.454Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.454Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.454Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:44:51.454Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.455Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:44:51.455Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.456Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:44:51.458Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:44:51.458Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:44:51.473Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:44:52.547Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-29T05:44:52.554Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 2 
2025-07-29T05:44:55.564Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-29T05:44:55.565Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-29T05:44:55.571Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 2 
2025-07-29T05:44:55.572Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 2 
2025-07-29T05:45:02.103Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-29T05:45:02.173Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:45:02.175Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T05:45:02.177Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-29T05:45:02.193Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:45:02.194Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-29T05:45:02.211Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:45:02.217Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:45:02.225Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:45:02.232Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-29T05:45:02.234Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:45:02.238Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-29T05:45:02.247Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-29T05:45:04.118Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T05:45:04.119Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T05:45:04.122Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-29T05:45:04.123Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-29T05:45:06.533Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:45:06.535Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T05:45:06.545Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:45:06.546Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-29T05:45:06.601Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T05:45:06.605Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-29T05:45:17.828Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T05:45:17.831Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-29T05:45:48.456Z  [POST | Report] info: <EMAIL> saving report... 
2025-07-29T05:45:48.461Z  [POST | Report] info: Parsed form data succesfully... 
2025-07-29T05:45:48.475Z  [POST | Report] info: Creating new <NAME_EMAIL> 
2025-07-29T05:45:48.488Z  [POST | Report] info: Successfully created new <NAME_EMAIL> 
2025-07-29T05:45:48.488Z  [POST | Report | Jira Integration] info: Jira integration disabled for program 5 
2025-07-29T05:45:48.516Z  [POST | Submit Report | Jira Integration] info: Jira integration enabled for report 18 
2025-07-29T05:45:48.523Z  [POST | Submit Report] info: Submitted report 18 
2025-07-29T05:45:48.526Z  [notifications.controller | sendNotification] info: notification: {"type":7,"payload":"18","message":"jira issue kye check waiting review for program mee"}, userIDs: 3 
2025-07-29T05:45:48.552Z  [GET | Reports] info: Retrieving report 18 for user 1... 
2025-07-29T05:45:48.557Z  [report.controller | getComments] info: User 1 requested comments for report 18 
2025-07-29T05:45:48.557Z  [report.controller | getComments] info: No comments found for report 18 
2025-07-29T05:45:48.571Z  [GET | Report] info: Retrieved report 18 for user 1. 
2025-07-29T05:45:48.573Z  [report.controller | getComments] info: User 1 requested comments for report 18 
2025-07-29T05:45:48.573Z  [report.controller | getComments] info: No comments found for report 18 
2025-07-29T05:45:58.784Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-29T05:45:58.842Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-29T05:45:58.849Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-29T05:46:00.781Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-29T05:46:00.782Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-29T05:46:00.786Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-29T05:46:00.787Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-29T05:46:06.285Z  [POST | Report State] info: Updated report 18 state 
2025-07-29T05:46:06.289Z  [notifications.controller | sendNotification] info: notification: {"type":1,"payload":"18","message":"jira issue kye check has been approved by the CTB team"}, userIDs: 1,2 
2025-07-29T05:46:06.330Z  [POST | Report State | Jira Integration] info: Admin approved report 18, checking Jira configuration 
2025-07-29T05:46:06.331Z  [Jira Integration | Detailed] info: Starting Jira issue creation for report 18 - Config check 
2025-07-29T05:46:06.331Z  [Jira Integration | Detailed] info: Jira URL: Provided, Email: Provided, API Token: Provided, Project Key: Provided 
2025-07-29T05:46:06.332Z  [Jira Integration] info: Attempting to create Jira issue for report 18 in project CTB 
2025-07-29T05:46:06.332Z  [Jira Integration | Detailed] info: Using Jira API URL: https://prateekdp2877.atlassian.net/rest/api/2/issue 
2025-07-29T05:46:06.332Z  [Jira Integration | Detailed] info: Formatted description for Jira issue: 
*Report ID:* 18
*Severity:* MEDIUM
*Category:* Blockchain Infrastructure Misconfiguration > Imprope... 
2025-07-29T05:46:06.332Z  [Jira Integration | Detailed] info: Fetching issue types for project CTB 
2025-07-29T05:46:07.077Z  [Jira Integration | Detailed] info: Received metaResponse: Success 
2025-07-29T05:46:07.078Z  [Jira Integration | Detailed] info: Project data: Available, Issue types: 3 
2025-07-29T05:46:07.078Z  [Jira Integration | Detailed] info: Using issue type: Task 
2025-07-29T05:46:07.078Z  [Jira Integration | Detailed] info: Prepared issue data: {"fields":{"project":{"key":"CTB"},"summary":"[CTB Report] jira issue kye check","description":"\n*Report ID:* 18\n*Severity:* MEDIUM\n*Category:* Blockchain Infrastructure Misconfiguration > Improper Bridge Validation And Verification Logic\n*Target:* fgg\n*Program:* mee\n*Report Link:* http://localhost:3000/dashboard/reports/18\n\n*Description:*\nff\n\n---\nThis issue was automatically created from CTB platform.\n","issuetype":{"name":"Task"},"assignee":{"name":"prateekdp2877"},"labels":["CTB","Security","reports"]}} 
2025-07-29T05:46:07.078Z  [Jira Integration | Detailed] info: Sending POST request to Jira API at https://prateekdp2877.atlassian.net/rest/api/2/issue 
2025-07-29T05:46:07.915Z  [Jira Integration | Detailed] info: Received response from Jira API: {"id":"10022","key":"CTB-6","self":"https://prateekdp2877.atlassian.net/rest/api/2/issue/10022"} 
2025-07-29T05:46:07.915Z  [Jira Integration] info: Successfully created Jira issue CTB-6 for report 18 
2025-07-29T05:46:07.928Z  [POST | Report State | Jira Integration] info: Successfully created Jira issue CTB-6 for approved report 18 and stored in description 
2025-07-29T05:46:46.464Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-6 
2025-07-29T05:46:46.464Z  [Jira Webhook | Status Change] info: Issue CTB-6: To Do → Done 
2025-07-29T05:46:46.465Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-6 with status Done 
2025-07-29T05:46:46.465Z  [Jira Integration | Search] info: Searching for report with Jira issue key CTB-6 in project CTB 
2025-07-29T05:46:46.471Z  [Jira Integration | Validation Result] info: Validation for CTB-6: DENIED - Report cannot be closed. Current status: Business Review. Required status: Awaiting Fix 
2025-07-29T05:46:46.471Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-6 to 'To Do' due to: Report cannot be closed. Current status: Business Review. Required status: Awaiting Fix 
2025-07-29T05:46:46.472Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-6 to 'To Do' status 
2025-07-29T05:46:46.857Z  [Jira Integration | Revert Status] info: Available transitions for CTB-6: To Do, In Progress, Done 
2025-07-29T05:46:46.858Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-6 
2025-07-29T05:46:47.507Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-6 to 'To Do' status 
2025-07-29T05:46:47.891Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-6 
2025-07-29T05:46:47.891Z  [Jira Webhook | Status Change] info: Issue CTB-6: Done → To Do 
2025-07-29T05:46:47.891Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-6 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-29T05:48:56.680Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T05:49:11.867Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T05:49:25.892Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T05:49:34.007Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T05:50:07.690Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-6 
2025-07-29T05:50:07.691Z  [Jira Webhook | Status Change] info: Issue CTB-6: To Do → Done 
2025-07-29T05:50:07.692Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-6 with status Done 
2025-07-29T05:50:07.693Z  [Jira Integration | Search] info: Searching for report with Jira issue key CTB-6 in project CTB 
2025-07-29T05:50:07.709Z  [Jira Integration | Validation Result] info: Validation for CTB-6: DENIED - Report cannot be closed. Current status: Business Review. Required status: Awaiting Fix 
2025-07-29T05:50:07.710Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-6 to 'To Do' due to: Report cannot be closed. Current status: Business Review. Required status: Awaiting Fix 
2025-07-29T05:50:07.711Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-6 to 'To Do' status 
2025-07-29T05:50:08.155Z  [Jira Integration | Revert Status] info: Available transitions for CTB-6: To Do, In Progress, Done 
2025-07-29T05:50:08.156Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-6 
2025-07-29T05:50:08.764Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-6 to 'To Do' status 
2025-07-29T05:50:09.139Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-6 
2025-07-29T05:50:09.139Z  [Jira Webhook | Loop Prevention] info: Ignoring webhook for CTB-6 - too recent after system action (1429ms ago) 
2025-07-29T05:51:26.313Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T05:52:28.053Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T05:52:45.440Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T05:53:01.626Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T05:53:12.176Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T05:54:13.383Z  [Jira Webhook | Request Details] info: Webhook request received 
2025-07-29T05:54:13.384Z  [Jira Webhook | Event Structure] info: Webhook event structure received 
2025-07-29T05:54:13.384Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-5 
2025-07-29T05:54:13.385Z  [Jira Webhook | Status Change] info: Issue CTB-5: Done → To Do 
2025-07-29T05:54:13.385Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-5 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-29T05:54:50.181Z  [Jira Webhook | Request Details] info: Webhook request received 
2025-07-29T05:54:50.182Z  [Jira Webhook | Event Structure] info: Webhook event structure received 
2025-07-29T05:54:50.182Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-5 
2025-07-29T05:54:50.183Z  [Jira Webhook | Status Change] info: Issue CTB-5: To Do → Done 
2025-07-29T05:54:50.184Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-5 with status Done 
2025-07-29T05:54:50.184Z  [Jira Integration | Search] info: Searching for report with Jira issue key CTB-5 in project CTB 
2025-07-29T05:54:50.190Z  [Jira Integration | Report Not Found] warn: No CTB report found for Jira issue CTB-5 in project CTB 
2025-07-29T05:54:50.192Z  [Jira Integration | Debug Info] info: Found 3 reports with Jira issue keys for debugging 
2025-07-29T05:55:27.925Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-29T05:55:28.008Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-29T05:55:28.019Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:55:28.021Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:55:28.022Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:55:28.022Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:55:28.023Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.024Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:55:28.027Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.030Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.031Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:55:28.031Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.032Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:55:28.033Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.043Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.054Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:55:28.055Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:55:28.087Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-29T05:55:28.105Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:55:28.111Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:55:28.111Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:55:28.112Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:55:28.112Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:55:28.112Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.112Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:55:28.113Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.113Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.113Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:55:28.113Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.114Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:55:28.114Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.117Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.120Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:55:28.121Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:55:28.145Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:55:28.150Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:55:28.151Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:55:28.151Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:55:28.151Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:55:28.151Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.152Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:55:28.152Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.152Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.153Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:55:28.153Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.153Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:55:28.153Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.155Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.159Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:55:28.160Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:55:28.179Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:55:28.184Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:55:28.185Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:55:28.185Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:55:28.186Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:55:28.186Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.187Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:55:28.187Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.187Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.187Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:55:28.188Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.188Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:55:28.188Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.190Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.196Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:55:28.196Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:55:28.219Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:55:28.224Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:55:28.224Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:55:28.225Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:55:28.225Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:55:28.225Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.226Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:55:28.226Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.226Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.227Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:55:28.227Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.227Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:55:28.227Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.230Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.233Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:55:28.233Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:55:28.253Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:55:28.299Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:55:28.300Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:55:28.300Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:55:28.300Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:55:28.301Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.301Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:55:28.301Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.302Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.302Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:55:28.302Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.303Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:55:28.303Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.305Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.307Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:55:28.307Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:55:28.332Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:55:28.337Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T05:55:28.337Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T05:55:28.338Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T05:55:28.338Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T05:55:28.338Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.339Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T05:55:28.339Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.339Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.339Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T05:55:28.340Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.340Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T05:55:28.340Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.342Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T05:55:28.346Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T05:55:28.347Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T05:55:28.366Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T05:55:29.787Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-29T05:55:29.789Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-29T05:55:29.793Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 2 
2025-07-29T05:55:29.794Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 2 
2025-07-29T05:55:33.011Z  [GET | Reports] info: Retrieving report 18 for user 2... 
2025-07-29T05:55:33.016Z  [report.controller | getComments] info: User 2 requested comments for report 18 
2025-07-29T05:55:33.016Z  [report.controller | getComments] info: No comments found for report 18 
2025-07-29T05:55:33.036Z  [GET | Report] info: Retrieved report 18 for user 2. 
2025-07-29T05:55:33.038Z  [report.controller | getComments] info: User 2 requested comments for report 18 
2025-07-29T05:55:33.038Z  [report.controller | getComments] info: No comments found for report 18 
2025-07-29T05:58:53.943Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T05:58:58.031Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-29T05:58:58.121Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:58:58.131Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T05:58:58.138Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-29T05:58:58.184Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:58:58.185Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-29T05:58:58.208Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:58:58.218Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:58:58.226Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:58:58.230Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-29T05:58:58.238Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:58:58.241Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-29T05:58:58.258Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-29T05:58:59.601Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T05:58:59.604Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T05:58:59.611Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-29T05:58:59.613Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-29T05:59:02.142Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:59:02.144Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T05:59:02.159Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T05:59:02.161Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-29T05:59:02.249Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T05:59:02.255Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-29T05:59:10.656Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T05:59:10.660Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-29T05:59:44.942Z  [POST | Report] info: <EMAIL> saving report... 
2025-07-29T05:59:44.947Z  [POST | Report] info: Parsed form data succesfully... 
2025-07-29T05:59:44.966Z  [POST | Report] info: Creating new <NAME_EMAIL> 
2025-07-29T05:59:44.978Z  [POST | Report] info: Successfully created new <NAME_EMAIL> 
2025-07-29T05:59:44.978Z  [POST | Report | Jira Integration] info: Jira integration disabled for program 5 
2025-07-29T05:59:45.014Z  [POST | Submit Report | Jira Integration] info: Jira integration enabled for report 19 
2025-07-29T05:59:45.022Z  [POST | Submit Report] info: Submitted report 19 
2025-07-29T05:59:45.024Z  [notifications.controller | sendNotification] info: notification: {"type":7,"payload":"19","message":"additional info check waiting review for program mee"}, userIDs: 3 
2025-07-29T05:59:45.056Z  [GET | Reports] info: Retrieving report 19 for user 1... 
2025-07-29T05:59:45.062Z  [report.controller | getComments] info: User 1 requested comments for report 19 
2025-07-29T05:59:45.062Z  [report.controller | getComments] info: No comments found for report 19 
2025-07-29T05:59:45.081Z  [GET | Report] info: Retrieved report 19 for user 1. 
2025-07-29T05:59:45.084Z  [report.controller | getComments] info: User 1 requested comments for report 19 
2025-07-29T05:59:45.084Z  [report.controller | getComments] info: No comments found for report 19 
2025-07-29T05:59:51.927Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-29T05:59:52.004Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-29T05:59:52.016Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-29T05:59:53.749Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-29T05:59:53.765Z  [program.controller | getPrograms] info: Successfully got 5 programs for user with id 3 
2025-07-29T05:59:57.543Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-29T05:59:57.547Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-29T05:59:57.579Z  [undefined] info: Fetching program details for program ID: 5, User ID: 3 
2025-07-29T05:59:57.595Z  [undefined] info: Fetching program details for program ID: 5, User ID: 3 
2025-07-29T05:59:57.610Z  [undefined] info: Fetching program details for program ID: 5, User ID: 3 
2025-07-29T05:59:57.619Z  [undefined] info: Fetching program details for program ID: 5, User ID: 3 
2025-07-29T06:00:09.881Z  [POST | Report State] info: Updated report 19 state 
2025-07-29T06:00:09.886Z  [notifications.controller | sendNotification] info: notification: {"type":1,"payload":"19","message":"additional info check has been approved by the CTB team"}, userIDs: 1,2 
2025-07-29T06:00:09.930Z  [POST | Report State | Jira Integration] info: Admin approved report 19, checking Jira configuration 
2025-07-29T06:00:09.932Z  [Jira Integration | Detailed] info: Starting Jira issue creation for report 19 - Config check 
2025-07-29T06:00:09.932Z  [Jira Integration | Detailed] info: Jira URL: Provided, Email: Provided, API Token: Provided, Project Key: Provided 
2025-07-29T06:00:09.932Z  [Jira Integration] info: Attempting to create Jira issue for report 19 in project CTB 
2025-07-29T06:00:09.932Z  [Jira Integration | Detailed] info: Using Jira API URL: https://prateekdp2877.atlassian.net/rest/api/2/issue 
2025-07-29T06:00:09.933Z  [Jira Integration | Detailed] info: Formatted description for Jira issue: 
*Report ID:* 19
*Severity:* MEDIUM
*Category:* Application Level Denial Of Service Dos > Critical I... 
2025-07-29T06:00:09.933Z  [Jira Integration | Detailed] info: Fetching issue types for project CTB 
2025-07-29T06:00:10.528Z  [Jira Integration | Detailed] info: Received metaResponse: Success 
2025-07-29T06:00:10.528Z  [Jira Integration | Detailed] info: Project data: Available, Issue types: 3 
2025-07-29T06:00:10.529Z  [Jira Integration | Detailed] info: Using issue type: Task 
2025-07-29T06:00:10.529Z  [Jira Integration | Detailed] info: Prepared issue data: {"fields":{"project":{"key":"CTB"},"summary":"[CTB Report] additional info check","description":"\n*Report ID:* 19\n*Severity:* MEDIUM\n*Category:* Application Level Denial Of Service Dos > Critical Impact And Or Easy Difficulty\n*Target:* fgg\n*Program:* mee\n*Report Link:* http://localhost:3000/dashboard/reports/19\n\n*Description:*\ndddd\n\n---\nThis issue was automatically created from CTB platform.\n","issuetype":{"name":"Task"},"assignee":{"name":"prateekdp2877"},"labels":["CTB","Security","reports"]}} 
2025-07-29T06:00:10.529Z  [Jira Integration | Detailed] info: Sending POST request to Jira API at https://prateekdp2877.atlassian.net/rest/api/2/issue 
2025-07-29T06:00:11.392Z  [Jira Integration | Detailed] info: Received response from Jira API: {"id":"10023","key":"CTB-7","self":"https://prateekdp2877.atlassian.net/rest/api/2/issue/10023"} 
2025-07-29T06:00:11.392Z  [Jira Integration] info: Successfully created Jira issue CTB-7 for report 19 
2025-07-29T06:00:11.397Z  [POST | Report State | Jira Integration] info: Successfully created Jira issue CTB-7 for approved report 19 and stored in description 
2025-07-29T06:02:08.956Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T06:04:55.653Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-29T06:04:55.744Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T06:04:55.753Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T06:04:55.794Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T06:04:55.797Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-29T06:04:55.813Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-29T06:04:55.825Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T06:04:55.846Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T06:04:55.857Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T06:04:55.866Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T06:04:55.872Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-29T06:04:55.878Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-29T06:04:55.893Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-29T06:04:57.396Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T06:04:57.400Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-29T06:04:59.672Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T06:04:59.674Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T06:04:59.686Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T06:04:59.689Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-29T06:04:59.744Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T06:04:59.750Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-29T06:05:06.772Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T06:05:06.775Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-29T06:05:38.092Z  [POST | Report] info: <EMAIL> saving report... 
2025-07-29T06:05:38.097Z  [POST | Report] info: Parsed form data succesfully... 
2025-07-29T06:05:38.113Z  [POST | Report] info: Creating new <NAME_EMAIL> 
2025-07-29T06:05:38.126Z  [POST | Report] info: Successfully created new <NAME_EMAIL> 
2025-07-29T06:05:38.127Z  [POST | Report | Jira Integration] info: Jira integration disabled for program 5 
2025-07-29T06:05:38.165Z  [POST | Submit Report | Jira Integration] info: Jira integration enabled for report 20 
2025-07-29T06:05:38.173Z  [POST | Submit Report] info: Submitted report 20 
2025-07-29T06:05:38.175Z  [notifications.controller | sendNotification] info: notification: {"type":7,"payload":"20","message":"check again waiting review for program mee"}, userIDs: 3 
2025-07-29T06:05:38.210Z  [GET | Reports] info: Retrieving report 20 for user 1... 
2025-07-29T06:05:38.215Z  [report.controller | getComments] info: User 1 requested comments for report 20 
2025-07-29T06:05:38.215Z  [report.controller | getComments] info: No comments found for report 20 
2025-07-29T06:05:38.231Z  [GET | Report] info: Retrieved report 20 for user 1. 
2025-07-29T06:05:38.233Z  [report.controller | getComments] info: User 1 requested comments for report 20 
2025-07-29T06:05:38.233Z  [report.controller | getComments] info: No comments found for report 20 
2025-07-29T06:05:45.692Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-29T06:05:45.768Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-29T06:05:45.782Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-29T06:05:47.588Z  [automated-report.controller | getAllPentestReports] info: Admin 3 requesting pentest reports with filters: {"page":"1","limit":"12","sort_by":"createdAt","sort_order":"DESC"} 
2025-07-29T06:05:47.595Z  [automated-report.controller | getAllPentestReports] info: Retrieved 0 reports in 6.70ms for role 3 
2025-07-29T06:05:47.601Z  [automated-report.controller | getAllPentestReports] info: Admin 3 requesting pentest reports with filters: {"page":"1","limit":"12","sort_by":"createdAt","sort_order":"DESC"} 
2025-07-29T06:05:47.606Z  [automated-report.controller | getAllPentestReports] info: Retrieved 0 reports in 5.05ms for role 3 
2025-07-29T06:05:49.149Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-29T06:05:49.152Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-29T06:05:54.135Z  [POST | Report State] info: Updated report 20 state 
2025-07-29T06:05:54.141Z  [notifications.controller | sendNotification] info: notification: {"type":1,"payload":"20","message":"check again has been approved by the CTB team"}, userIDs: 1,2 
2025-07-29T06:05:54.189Z  [POST | Report State | Jira Integration] info: Admin approved report 20, checking Jira configuration 
2025-07-29T06:05:54.190Z  [Jira Integration | Detailed] info: Starting Jira issue creation for report 20 - Config check 
2025-07-29T06:05:54.191Z  [Jira Integration | Detailed] info: Jira URL: Provided, Email: Provided, API Token: Provided, Project Key: Provided 
2025-07-29T06:05:54.191Z  [Jira Integration] info: Attempting to create Jira issue for report 20 in project CTB 
2025-07-29T06:05:54.191Z  [Jira Integration | Detailed] info: Using Jira API URL: https://prateekdp2877.atlassian.net/rest/api/2/issue 
2025-07-29T06:05:54.192Z  [Jira Integration | Detailed] info: Formatted description for Jira issue: 
*Report ID:* 20
*Severity:* HIGH
*Category:* Automotive Security Misconfiguration > Gnss Gps > Spoo... 
2025-07-29T06:05:54.192Z  [Jira Integration | Detailed] info: Fetching issue types for project CTB 
2025-07-29T06:05:54.747Z  [Jira Integration | Detailed] info: Received metaResponse: Success 
2025-07-29T06:05:54.747Z  [Jira Integration | Detailed] info: Project data: Available, Issue types: 3 
2025-07-29T06:05:54.747Z  [Jira Integration | Detailed] info: Using issue type: Task 
2025-07-29T06:05:54.748Z  [Jira Integration | Detailed] info: Prepared issue data: {"fields":{"project":{"key":"CTB"},"summary":"[CTB Report] check again","description":"\n*Report ID:* 20\n*Severity:* HIGH\n*Category:* Automotive Security Misconfiguration > Gnss Gps > Spoofing\n*Target:* fgg\n*Program:* mee\n*Report Link:* http://localhost:3000/dashboard/reports/20\n\n*Description:*\nxcc\n\n---\nThis issue was automatically created from CTB platform.\n","issuetype":{"name":"Task"},"assignee":{"name":"prateekdp2877"},"labels":["CTB","Security","reports"]}} 
2025-07-29T06:05:54.748Z  [Jira Integration | Detailed] info: Sending POST request to Jira API at https://prateekdp2877.atlassian.net/rest/api/2/issue 
2025-07-29T06:05:55.698Z  [Jira Integration | Detailed] info: Received response from Jira API: {"id":"10024","key":"CTB-8","self":"https://prateekdp2877.atlassian.net/rest/api/2/issue/10024"} 
2025-07-29T06:05:55.699Z  [Jira Integration] info: Successfully created Jira issue CTB-8 for report 20 
2025-07-29T06:05:55.703Z  [POST | Report State | Jira Integration] info: Successfully created Jira issue CTB-8 for approved report 20 and stored in additional_info 
2025-07-29T06:05:56.970Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-29T06:05:56.995Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-29T06:05:57.040Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-29T06:05:57.044Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-29T06:14:20.110Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T06:14:28.253Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-29T06:14:28.274Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-29T06:14:33.973Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-29T06:14:34.039Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T06:14:34.043Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T06:14:34.047Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-29T06:14:34.083Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T06:14:34.085Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-29T06:14:34.107Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T06:14:34.116Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T06:14:34.124Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T06:14:34.127Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-29T06:14:34.131Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T06:14:34.133Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-29T06:14:34.143Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-29T06:14:35.757Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T06:14:35.759Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-29T06:14:38.098Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T06:14:38.100Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T06:14:38.109Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-29T06:14:38.110Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-29T06:14:38.165Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T06:14:38.170Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-29T06:14:48.481Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-29T06:14:48.485Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-29T06:15:25.906Z  [POST | Report] info: <EMAIL> saving report... 
2025-07-29T06:15:25.911Z  [POST | Report] info: Parsed form data succesfully... 
2025-07-29T06:15:25.924Z  [POST | Report] info: Creating new <NAME_EMAIL> 
2025-07-29T06:15:25.935Z  [POST | Report] info: Successfully created new <NAME_EMAIL> 
2025-07-29T06:15:25.936Z  [POST | Report | Jira Integration] info: Jira integration disabled for program 5 
2025-07-29T06:15:25.971Z  [POST | Submit Report | Jira Integration] info: Jira integration enabled for report 21 
2025-07-29T06:15:25.977Z  [POST | Submit Report] info: Submitted report 21 
2025-07-29T06:15:25.979Z  [notifications.controller | sendNotification] info: notification: {"type":7,"payload":"21","message":"finalllll-last waiting review for program mee"}, userIDs: 3 
2025-07-29T06:15:26.027Z  [GET | Reports] info: Retrieving report 21 for user 1... 
2025-07-29T06:15:26.029Z  [report.controller | getComments] info: User 1 requested comments for report 21 
2025-07-29T06:15:26.029Z  [report.controller | getComments] info: No comments found for report 21 
2025-07-29T06:15:26.045Z  [GET | Report] info: Retrieved report 21 for user 1. 
2025-07-29T06:15:26.047Z  [report.controller | getComments] info: User 1 requested comments for report 21 
2025-07-29T06:15:26.047Z  [report.controller | getComments] info: No comments found for report 21 
2025-07-29T06:15:34.559Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-29T06:15:34.604Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-29T06:15:34.620Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-29T06:15:37.681Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-29T06:15:37.684Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-29T06:15:41.997Z  [POST | Report State] info: Updated report 21 state 
2025-07-29T06:15:42.001Z  [notifications.controller | sendNotification] info: notification: {"type":1,"payload":"21","message":"finalllll-last has been approved by the CTB team"}, userIDs: 1,2 
2025-07-29T06:15:42.039Z  [POST | Report State | Jira Integration] info: Admin approved report 21, checking Jira configuration 
2025-07-29T06:15:42.040Z  [Jira Integration | Detailed] info: Starting Jira issue creation for report 21 - Config check 
2025-07-29T06:15:42.041Z  [Jira Integration | Detailed] info: Jira URL: Provided, Email: Provided, API Token: Provided, Project Key: Provided 
2025-07-29T06:15:42.041Z  [Jira Integration] info: Attempting to create Jira issue for report 21 in project CTB 
2025-07-29T06:15:42.041Z  [Jira Integration | Detailed] info: Using Jira API URL: https://prateekdp2877.atlassian.net/rest/api/2/issue 
2025-07-29T06:15:42.041Z  [Jira Integration | Detailed] info: Formatted description for Jira issue: 
*Report ID:* 21
*Severity:* MEDIUM
*Category:* Automotive Security Misconfiguration > Gnss Gps > Sp... 
2025-07-29T06:15:42.042Z  [Jira Integration | Detailed] info: Fetching issue types for project CTB 
2025-07-29T06:15:42.643Z  [Jira Integration | Detailed] info: Received metaResponse: Success 
2025-07-29T06:15:42.643Z  [Jira Integration | Detailed] info: Project data: Available, Issue types: 3 
2025-07-29T06:15:42.644Z  [Jira Integration | Detailed] info: Using issue type: Task 
2025-07-29T06:15:42.644Z  [Jira Integration | Detailed] info: Prepared issue data: {"fields":{"project":{"key":"CTB"},"summary":"[CTB Report] finalllll-last","description":"\n*Report ID:* 21\n*Severity:* MEDIUM\n*Category:* Automotive Security Misconfiguration > Gnss Gps > Spoofing\n*Target:* fgg\n*Program:* mee\n*Report Link:* http://localhost:3000/dashboard/reports/21\n\n*Description:*\nee\n\n---\nThis issue was automatically created from CTB platform.\n","issuetype":{"name":"Task"},"assignee":{"name":"prateekdp2877"},"labels":["CTB","Security","reports"]}} 
2025-07-29T06:15:42.644Z  [Jira Integration | Detailed] info: Sending POST request to Jira API at https://prateekdp2877.atlassian.net/rest/api/2/issue 
2025-07-29T06:15:43.402Z  [Jira Integration | Detailed] info: Received response from Jira API: {"id":"10025","key":"CTB-9","self":"https://prateekdp2877.atlassian.net/rest/api/2/issue/10025"} 
2025-07-29T06:15:43.402Z  [Jira Integration] info: Successfully created Jira issue CTB-9 for report 21 
2025-07-29T06:15:43.407Z  [POST | Report State | Jira Integration] info: Successfully created Jira issue CTB-9 for approved report 21 and stored in jira_issue_key field 
2025-07-29T06:16:12.367Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-9 
2025-07-29T06:16:12.367Z  [Jira Webhook | Status Change] info: Issue CTB-9: To Do → Done 
2025-07-29T06:16:12.368Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-9 with status Done 
2025-07-29T06:16:12.375Z  [Jira Integration | Validation Result] info: Validation for CTB-9: DENIED - Report cannot be closed. Current status: Business Review. Required status: Awaiting Fix 
2025-07-29T06:16:12.376Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-9 to 'To Do' due to: Report cannot be closed. Current status: Business Review. Required status: Awaiting Fix 
2025-07-29T06:16:12.376Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-9 to 'To Do' status 
2025-07-29T06:16:12.749Z  [Jira Integration | Revert Status] info: Available transitions for CTB-9: To Do, In Progress, Done 
2025-07-29T06:16:12.749Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-9 
2025-07-29T06:16:13.372Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-9 to 'To Do' status 
2025-07-29T06:16:13.721Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-9 
2025-07-29T06:16:13.721Z  [Jira Webhook | Status Change] info: Issue CTB-9: Done → To Do 
2025-07-29T06:16:13.721Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-9 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-29T06:18:22.563Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T06:18:25.373Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T06:19:16.228Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-9 
2025-07-29T06:19:16.229Z  [Jira Webhook | Status Change] info: Issue CTB-9: To Do → Done 
2025-07-29T06:19:16.230Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-9 with status Done 
2025-07-29T06:19:16.249Z  [Jira Integration | Validation Result] info: Validation for CTB-9: DENIED - Report cannot be closed. Current status: Business Review. Required status: Awaiting Fix 
2025-07-29T06:19:16.250Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-9 to 'To Do' due to: Report cannot be closed. Current status: Business Review. Required status: Awaiting Fix 
2025-07-29T06:19:16.251Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-9 to 'To Do' status 
2025-07-29T06:19:17.071Z  [Jira Integration | Revert Status] info: Available transitions for CTB-9: To Do, In Progress, Done 
2025-07-29T06:19:17.072Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-9 
2025-07-29T06:19:17.664Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-9 to 'To Do' status 
2025-07-29T06:19:18.031Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-9 
2025-07-29T06:19:18.032Z  [Jira Webhook | Status Change] info: Issue CTB-9: Done → To Do 
2025-07-29T06:19:18.036Z  [Jira Webhook | System Reversion] info: Allowing system reversion of issue CTB-9 from 'Done' to 'To Do' - report not closed 
2025-07-29T06:20:34.775Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T06:20:50.923Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T06:21:17.656Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T06:21:31.175Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T06:25:26.352Z  [Jira Webhook | Request Details] info: Webhook request from ::1, User-Agent: Atlassian Webhook HTTP Client, Content-Type: application/json; charset=UTF-8 
2025-07-29T06:25:26.353Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-9 
2025-07-29T06:25:26.353Z  [Jira Webhook | Status Change] info: Issue CTB-9: Done → To Do 
2025-07-29T06:25:26.372Z  [Jira Webhook | System Reversion] info: Allowing system reversion of issue CTB-9 from 'Done' to 'To Do' - report not closed 
2025-07-29T06:25:39.763Z  [Jira Webhook | Request Details] info: Webhook request from ::1, User-Agent: Atlassian Webhook HTTP Client, Content-Type: application/json; charset=UTF-8 
2025-07-29T06:25:39.763Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-9 
2025-07-29T06:25:39.764Z  [Jira Webhook | Status Change] info: Issue CTB-9: To Do → Done 
2025-07-29T06:25:39.765Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-9 with status Done 
2025-07-29T06:25:39.773Z  [Jira Integration | Validation Result] info: Validation for CTB-9: DENIED - Report cannot be closed. Current status: Business Review. Required status: Awaiting Fix 
2025-07-29T06:25:39.773Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-9 to 'To Do' due to: Report cannot be closed. Current status: Business Review. Required status: Awaiting Fix 
2025-07-29T06:25:39.774Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-9 to 'To Do' status 
2025-07-29T06:25:40.191Z  [Jira Integration | Revert Status] info: Available transitions for CTB-9: To Do, In Progress, Done 
2025-07-29T06:25:40.191Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-9 
2025-07-29T06:25:40.809Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-9 to 'To Do' status 
2025-07-29T06:25:41.134Z  [Jira Webhook | Request Details] info: Webhook request from ::1, User-Agent: Atlassian Webhook HTTP Client, Content-Type: application/json; charset=UTF-8 
2025-07-29T06:25:41.134Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-9 
2025-07-29T06:25:41.134Z  [Jira Webhook | Status Change] info: Issue CTB-9: Done → To Do 
2025-07-29T06:25:41.141Z  [Jira Webhook | System Reversion] info: Allowing system reversion of issue CTB-9 from 'Done' to 'To Do' - report not closed 
2025-07-29T06:26:02.783Z  [Jira Webhook | Request Details] info: Webhook request from ::1, User-Agent: Atlassian Webhook HTTP Client, Content-Type: application/json; charset=UTF-8 
2025-07-29T06:26:02.783Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-9 
2025-07-29T06:26:02.784Z  [Jira Webhook | Status Change] info: Issue CTB-9: To Do → In Progress 
2025-07-29T06:31:07.325Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T07:23:55.734Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T07:24:13.499Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T07:24:31.845Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T07:26:44.964Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T07:28:43.601Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T07:28:51.616Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T07:29:38.111Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-29T07:42:26.397Z  [GET | Reports] info: Retrieving report 19 for user 3... 
2025-07-29T07:42:26.405Z  [report.controller | getComments] info: User 3 requested comments for report 19 
2025-07-29T07:42:26.405Z  [report.controller | getComments] info: No comments found for report 19 
2025-07-29T07:42:26.433Z  [GET | Report] info: Retrieved report 19 for user 3. 
2025-07-29T07:42:26.436Z  [report.controller | getComments] info: User 3 requested comments for report 19 
2025-07-29T07:42:26.436Z  [report.controller | getComments] info: No comments found for report 19 
2025-07-29T07:42:35.319Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-29T07:42:35.411Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-29T07:42:35.417Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T07:42:35.419Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T07:42:35.419Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T07:42:35.419Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T07:42:35.420Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.421Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T07:42:35.421Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.422Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.425Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T07:42:35.426Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.427Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T07:42:35.427Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.442Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T07:42:35.444Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T07:42:35.480Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.504Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-29T07:42:35.516Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T07:42:35.521Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T07:42:35.521Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T07:42:35.522Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T07:42:35.522Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T07:42:35.522Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.523Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T07:42:35.523Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.523Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.523Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T07:42:35.524Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.524Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T07:42:35.524Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.526Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.532Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T07:42:35.533Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T07:42:35.565Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T07:42:35.570Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T07:42:35.570Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T07:42:35.571Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T07:42:35.571Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T07:42:35.571Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.572Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T07:42:35.572Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.572Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.573Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T07:42:35.573Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.574Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T07:42:35.574Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.578Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.582Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T07:42:35.582Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T07:42:35.604Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T07:42:35.609Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T07:42:35.610Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T07:42:35.610Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T07:42:35.610Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T07:42:35.610Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.611Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T07:42:35.611Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.611Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.612Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T07:42:35.612Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.612Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T07:42:35.612Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.614Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.617Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T07:42:35.618Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T07:42:35.643Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T07:42:35.650Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T07:42:35.650Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T07:42:35.651Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T07:42:35.651Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T07:42:35.651Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.651Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T07:42:35.652Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.652Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.652Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T07:42:35.652Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.653Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T07:42:35.653Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.655Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.658Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T07:42:35.659Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T07:42:35.677Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T07:42:35.715Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T07:42:35.716Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T07:42:35.716Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T07:42:35.716Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T07:42:35.717Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.717Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T07:42:35.717Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.717Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.718Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T07:42:35.718Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.718Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T07:42:35.718Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.720Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.723Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T07:42:35.724Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T07:42:35.750Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T07:42:35.755Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T07:42:35.755Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T07:42:35.756Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T07:42:35.756Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T07:42:35.756Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.756Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T07:42:35.757Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.757Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.757Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T07:42:35.757Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.758Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T07:42:35.758Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.760Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:35.767Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T07:42:35.768Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T07:42:35.790Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T07:42:50.791Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-29T07:42:50.795Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T07:42:50.796Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T07:42:50.796Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T07:42:50.796Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T07:42:50.797Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:50.797Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T07:42:50.798Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:50.798Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:50.799Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T07:42:50.799Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:50.800Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T07:42:50.800Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:50.813Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:50.819Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T07:42:50.819Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T07:42:50.893Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-29T07:42:50.911Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T07:42:50.919Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T07:42:50.921Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T07:42:50.921Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T07:42:50.922Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T07:42:50.922Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:50.922Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T07:42:50.922Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:50.923Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:50.923Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T07:42:50.923Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:50.924Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T07:42:50.924Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:50.926Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:50.932Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T07:42:50.933Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T07:42:50.981Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T07:42:50.987Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T07:42:50.987Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T07:42:50.988Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T07:42:50.988Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T07:42:50.989Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:50.990Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T07:42:50.991Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:50.992Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:50.992Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T07:42:50.992Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:50.993Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T07:42:50.993Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:50.997Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.003Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T07:42:51.004Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T07:42:51.042Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T07:42:51.049Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T07:42:51.050Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T07:42:51.051Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T07:42:51.052Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T07:42:51.052Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.052Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T07:42:51.053Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.053Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.053Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T07:42:51.054Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.055Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T07:42:51.055Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.058Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.063Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T07:42:51.064Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T07:42:51.099Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T07:42:51.105Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T07:42:51.108Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T07:42:51.109Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T07:42:51.111Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T07:42:51.111Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.112Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T07:42:51.112Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.113Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.113Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T07:42:51.114Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.115Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T07:42:51.116Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.118Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.133Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T07:42:51.134Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T07:42:51.176Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T07:42:51.241Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T07:42:51.241Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T07:42:51.241Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T07:42:51.242Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T07:42:51.242Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.242Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T07:42:51.243Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.243Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.243Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T07:42:51.243Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.244Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T07:42:51.244Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.247Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.254Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T07:42:51.255Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T07:42:51.291Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-29T07:42:51.300Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-29T07:42:51.300Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-29T07:42:51.300Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-29T07:42:51.301Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-29T07:42:51.301Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.302Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-29T07:42:51.302Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.303Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.303Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-29T07:42:51.304Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.304Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-29T07:42:51.305Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.309Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-29T07:42:51.317Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-29T07:42:51.318Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-29T07:42:51.369Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
