import { useState } from "react";
import {
  TransactionFilters,
  useGetTransactionsQuery
} from "../../api/endpoints/paymentsApi";

/**
 * Provide access to the history of transactions for
 * the current user
 */
const useTransactions = () => {
  const [filters, setFilters] = useState<TransactionFilters>({
    limit: 10
  });
  const { data, isError, isLoading } = useGetTransactionsQuery(filters);

  return {
    transactions: data?.transactions || [],
    count: data?.count || 0,
    filters,
    isError,
    isLoading,
    setPage: (page: number) => {
      setFilters(filters => ({
        ...filters,
        page
      }));
    },
    setFilters
  };
};

export default useTransactions;
