import { useMemo, useState } from "react";
import {
  ProgramTarget,
  TargetType
} from "../../../../utils/api/endpoints/programs/parsePrograms";
import OutlineButton from "../../../buttons/OutlineButton";
import PlusOutlineIcon from "../../../../assets/icons/PlusOutlineIcon";
import TableContainer from "../../../table/TableContainer";
import TableRow from "../../../table/TableRow";
import DeleteIcon from "../../../../assets/icons/DeleteIcon";
import EmptyTableRow from "../../../table/EmptyTableRow";
import { Dropdown } from "../../../forms/inputs/ControlledDropdown";
import EditorAttachmentsInput from "../EditorAttachmentsInput";

const blankTarget: Omit<ProgramTarget, "targetType"> & {
  targetType: TargetType | "";
} = {
  targetName: "",
  targetType: ""
};

const ProgramTargetsEditor = ({
  value,
  onChange
}: {
  value?: ProgramTarget[];
  onChange: (value: ProgramTarget[]) => void;
}) => {
  const [newTarget, setNewTarget] = useState(blankTarget);

  const validNewTarget = useMemo(
    () => newTarget.targetName !== "" && newTarget.targetType !== "",
    [newTarget]
  );

  const handleAddTargets = () => {
    if (!validNewTarget) return;

    const targetNames = newTarget.targetName
      .split(",")
      .map(name => name.trim())
      .filter(name => name !== "");

    const newTargets = targetNames.map(name => ({
      targetName: name,
      targetType: newTarget.targetType as TargetType
    }));

    onChange([...(value || []), ...newTargets]);
    setNewTarget(blankTarget);
  };

   
  return (
    <div className="space-y-4">
      <div className="flex w-full gap-3">
        <input
          type="text"
          placeholder="Enter targets (comma-separated)..."
          value={newTarget.targetName}
          onChange={e =>
            setNewTarget(target => ({
              ...target,
              targetName: e.target.value
            }))
          }
          className="flex-grow rounded w-[70%] border-[1px] px-4 py-4"
        />

        <Dropdown
          placeholder="Select a target type"
          value={newTarget.targetType}
          className="flex flex-grow !bg-ctb-blue-600 !important"
          options={Object.values(TargetType).map(target => ({
            value: target
          }))}
          onChange={value =>
            setNewTarget(target => ({
              ...target,
              targetType: value as TargetType
            }))
          }
        />

        <OutlineButton
          disabled={!validNewTarget}
          className={
            "!rounded " +
            (validNewTarget
              ? "stroke-black"
              : "stroke-ctb-grey-200")
          }
          onClick={handleAddTargets}
        >
          <PlusOutlineIcon className="stroke-inherit" />
        </OutlineButton>
      </div>

      <TableContainer
        columns={[
          { name: "Target", weight: 4 },
          { name: "Type", weight: 2 },
          { name: "", weight: 1 }
        ]}
        className="relative"
      >
        {value === undefined || value.length === 0 ? (
          <EmptyTableRow message="No targets" />
        ) : (
          value.map((target, index) => (
            <div key={index}>
              <TableRow
                className={
                  index !== value.length - 1
                    ? "mx-5 border-b !pl-6"
                    : "mx-5 !pl-6"
                }
              >
                <p>{target.targetName}</p>
                <p>{target.targetType}</p>
                <div className="flex justify-end pr-2">
                  <button
                    type="button"
                    className="stroke-ctb-red-500 transition hover:scale-110"
                    onClick={() =>
                      onChange(
                        value.filter(
                          t =>
                            !(
                              t.targetName === target.targetName &&
                              t.targetType === target.targetType
                            )
                        )
                      )
                    }
                  >
                    <DeleteIcon className="stroke-inherit" />
                  </button>
                </div>
              </TableRow>
            </div>
          ))
        )}
      </TableContainer>

       
    </div>
  );
};

export default ProgramTargetsEditor;
