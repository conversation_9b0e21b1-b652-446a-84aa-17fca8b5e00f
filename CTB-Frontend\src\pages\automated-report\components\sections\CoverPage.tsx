import React from 'react';
import { Page, View, Text, Image } from '@react-pdf/renderer';
import { ReportData } from '../../types/report.types';

interface CoverPageProps {
  reportData: ReportData;
}

const FONT_SIZES = {
  title: 20,
  company: 16,
  meta: 12,
};

const LINE_HEIGHT = 1.4;

const DEFAULT_LOGO = 'https://i.postimg.cc/QVPV9d0x/Full-name-logo-blue.png';
const DEFAULT_COMPANY = 'Capture The Bug Ltd.';

const CoverPage: React.FC<CoverPageProps> = ({ reportData }) => (
  <Page size="A4" style={{
    flexDirection: 'column',
    justifyContent: 'space-between',
    minHeight: '100%',
    padding: '20mm 15mm',
    position: 'relative',
    fontFamily: 'Helvetica',
    backgroundColor: '#ffffff',
  }}>
    {/* Background Image as a separate element */}
    <View style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: -1,
    }}>
      <Image
        src="https://i.postimg.cc/fLy3KCcw/VAPT-Cover-Page.png"
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
        }}
      />
    </View>
    {/* Top Logo */}
    <View style={{
      paddingTop: 32,
      paddingLeft: 24,
      marginBottom: 0,
    }}>
      <Image
        src={reportData.branding_logo || DEFAULT_LOGO}
        style={{
          width: 256,
          height: 64,
          objectFit: 'contain',
          objectPosition: 'left',
        }}
      />
    </View>
    {/* Middle Content */}
    <View style={{
      flex: 1,
      justifyContent: 'flex-start',
      alignItems: 'flex-start',
      paddingLeft: 32,
      paddingRight: 32,
      marginTop: 60,
    }}>
      <View style={{
        alignItems: 'flex-start',
        maxWidth: '85%',
      }}>
        <Text style={{
          fontSize: FONT_SIZES.title,
          fontWeight: 700,
          marginBottom: 24,
          color: '#2563eb',
          textAlign: 'left',
          lineHeight: LINE_HEIGHT,
          maxWidth: '90%',
        }}>
          {reportData.report_title}
        </Text>
        <Text style={{
          fontSize: FONT_SIZES.company,
          fontWeight: 700,
          marginBottom: 16,
          color: '#2563eb',
          textAlign: 'left',
          textTransform: 'uppercase',
          letterSpacing: 1,
          lineHeight: LINE_HEIGHT,
        }}>
          {reportData.program_name}
        </Text>
        <Text style={{
          fontSize: FONT_SIZES.meta,
          fontWeight: 400,
          color: '#2563eb',
          textAlign: 'left',
          marginBottom: 0,
          lineHeight: LINE_HEIGHT,
        }}>
          Version {reportData.version_number}
        </Text>
      </View>
    </View>
    {/* Bottom Section */}
    <View style={{
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-end',
      marginTop: 80,
      paddingHorizontal: 32,
      paddingBottom: 32,
    }}>
      <View style={{
        flexDirection: 'column',
      }}>
        <Text style={{
          color: '#2563eb',
          fontSize: FONT_SIZES.meta,
          fontWeight: 400,
          marginBottom: 0,
          marginTop: 32,
          lineHeight: LINE_HEIGHT,
        }}>
          Date: {reportData.current_date}
        </Text>
      </View>
      <View style={{
        alignItems: 'flex-end',
      }}>
        <Image
          src={reportData.branding_logo || DEFAULT_LOGO}
          style={{
            width: 160,
            height: 40,
            marginBottom: 0,
            objectFit: 'contain',
            objectPosition: 'center',
            opacity: 0.4,
          }}
        />
      </View>
    </View>
  </Page>
);

export default CoverPage; 