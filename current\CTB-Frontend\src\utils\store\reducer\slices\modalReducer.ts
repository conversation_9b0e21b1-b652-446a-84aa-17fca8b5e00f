import { createSlice } from "@reduxjs/toolkit";

export enum ModalType {
  NOTIFICATIONS,
  FILTERS,
  DROPDOWN
}

export type ModalState = {
  selected?: ModalType;
  preventClose: boolean;
  id?: number | string;
};

// Blank initial state - no modal has been selected yet
const initialState: ModalState = {
  preventClose: false
};

const modalReducer = createSlice({
  name: "modal",
  initialState,
  reducers: {
    setModal: (
      state,
      action: {
        payload: {
          selected?: ModalType;
          id?: number | string;
        };
      }
    ) => ({
      ...state,
      selected: action.payload.selected,
      id: action.payload.id
    }),
    setPreventClose: (
      state,
      action: {
        payload: boolean;
      }
    ) => ({
      ...state,
      preventClose: action.payload
    })
  }
});

export const { setModal, setPreventClose } = modalReducer.actions;
export default modalReducer.reducer;
