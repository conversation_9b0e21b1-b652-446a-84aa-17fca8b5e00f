{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\jira-old\\\\CTB-Frontend\\\\src\\\\pages\\\\automated-report\\\\components\\\\editors\\\\BrandingEditor.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DEFAULT_LOGO = 'https://i.postimg.cc/QVPV9d0x/Full-name-logo-blue.png';\nconst DEFAULT_COMPANY = 'Capture The Bug Ltd.';\nconst BrandingEditor = ({\n  reportData,\n  onInputChange\n}) => {\n  const brandingLogo = reportData.branding_logo || DEFAULT_LOGO;\n  const brandingCompany = reportData.branding_company || DEFAULT_COMPANY;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-full overflow-y-auto\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-xl shadow-lg border border-slate-100 p-6 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-1 h-8 bg-gradient-to-b from-blue-500 to-blue-700 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-base font-bold text-blue-900 tracking-tight\",\n            children: \"Branding\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-blue-700/80 font-medium mt-0.5\",\n            children: \"Edit logo and company name for white labeling\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-xs font-medium text-gray-700 mb-0.5\",\n            htmlFor: \"branding_logo\",\n            children: \"Logo URL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"branding_logo\",\n            type: \"text\",\n            value: brandingLogo,\n            onChange: e => onInputChange('branding_logo', e.target.value),\n            className: \"w-full px-2.5 py-1.5 text-sm bg-white border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200 placeholder:text-slate-400\",\n            placeholder: \"Enter logo URL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block text-xs text-gray-500 mb-1\",\n              children: \"Logo Preview:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 border border-slate-200 rounded-md p-2 flex items-center justify-center min-h-[60px]\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: brandingLogo,\n                alt: \"Branding Logo Preview\",\n                className: \"max-h-16 max-w-full object-contain\",\n                onError: e => e.currentTarget.src = DEFAULT_LOGO\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-xs font-medium text-gray-700 mb-0.5\",\n            htmlFor: \"branding_company\",\n            children: \"Company Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"branding_company\",\n            type: \"text\",\n            value: brandingCompany,\n            onChange: e => onInputChange('branding_company', e.target.value),\n            className: \"w-full px-2.5 py-1.5 text-sm bg-white border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200 placeholder:text-slate-400\",\n            placeholder: \"Enter company name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_c = BrandingEditor;\nexport default BrandingEditor;\nvar _c;\n$RefreshReg$(_c, \"BrandingEditor\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "DEFAULT_LOGO", "DEFAULT_COMPANY", "BrandingEditor", "reportData", "onInputChange", "brandingLogo", "branding_logo", "brandingCompany", "branding_company", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "id", "type", "value", "onChange", "e", "target", "placeholder", "src", "alt", "onError", "currentTarget", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/jira-old/CTB-Frontend/src/pages/automated-report/components/editors/BrandingEditor.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { ReportData } from '../../types/report.types';\r\n\r\ninterface BrandingEditorProps {\r\n  reportData: ReportData;\r\n  onInputChange: (field: string, value: string) => void;\r\n}\r\n\r\nconst DEFAULT_LOGO = 'https://i.postimg.cc/QVPV9d0x/Full-name-logo-blue.png';\r\nconst DEFAULT_COMPANY = 'Capture The Bug Ltd.';\r\n\r\nconst BrandingEditor: React.FC<BrandingEditorProps> = ({ reportData, onInputChange }) => {\r\n  const brandingLogo = reportData.branding_logo || DEFAULT_LOGO;\r\n  const brandingCompany = reportData.branding_company || DEFAULT_COMPANY;\r\n\r\n  return (\r\n    <div className=\"h-full overflow-y-auto\">\r\n      <div className=\"bg-white rounded-xl shadow-lg border border-slate-100 p-6 mb-6\">\r\n        <div className=\"flex items-center gap-3 mb-4\">\r\n          <div className=\"w-1 h-8 bg-gradient-to-b from-blue-500 to-blue-700 rounded-full\" />\r\n          <div>\r\n            <h3 className=\"text-base font-bold text-blue-900 tracking-tight\">Branding</h3>\r\n            <p className=\"text-xs text-blue-700/80 font-medium mt-0.5\">Edit logo and company name for white labeling</p>\r\n          </div>\r\n        </div>\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-2\">\r\n          <div className=\"flex flex-col gap-2\">\r\n            <label className=\"block text-xs font-medium text-gray-700 mb-0.5\" htmlFor=\"branding_logo\">\r\n              Logo URL\r\n            </label>\r\n            <input\r\n              id=\"branding_logo\"\r\n              type=\"text\"\r\n              value={brandingLogo}\r\n              onChange={e => onInputChange('branding_logo', e.target.value)}\r\n              className=\"w-full px-2.5 py-1.5 text-sm bg-white border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200 placeholder:text-slate-400\"\r\n              placeholder=\"Enter logo URL\"\r\n            />\r\n            <div className=\"mt-2\">\r\n              <span className=\"block text-xs text-gray-500 mb-1\">Logo Preview:</span>\r\n              <div className=\"bg-gray-50 border border-slate-200 rounded-md p-2 flex items-center justify-center min-h-[60px]\">\r\n                <img\r\n                  src={brandingLogo}\r\n                  alt=\"Branding Logo Preview\"\r\n                  className=\"max-h-16 max-w-full object-contain\"\r\n                  onError={e => (e.currentTarget.src = DEFAULT_LOGO)}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex flex-col gap-2\">\r\n            <label className=\"block text-xs font-medium text-gray-700 mb-0.5\" htmlFor=\"branding_company\">\r\n              Company Name\r\n            </label>\r\n            <input\r\n              id=\"branding_company\"\r\n              type=\"text\"\r\n              value={brandingCompany}\r\n              onChange={e => onInputChange('branding_company', e.target.value)}\r\n              className=\"w-full px-2.5 py-1.5 text-sm bg-white border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200 placeholder:text-slate-400\"\r\n              placeholder=\"Enter company name\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BrandingEditor; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ1B,MAAMC,YAAY,GAAG,uDAAuD;AAC5E,MAAMC,eAAe,GAAG,sBAAsB;AAE9C,MAAMC,cAA6C,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAc,CAAC,KAAK;EACvF,MAAMC,YAAY,GAAGF,UAAU,CAACG,aAAa,IAAIN,YAAY;EAC7D,MAAMO,eAAe,GAAGJ,UAAU,CAACK,gBAAgB,IAAIP,eAAe;EAEtE,oBACEF,OAAA;IAAKU,SAAS,EAAC,wBAAwB;IAAAC,QAAA,eACrCX,OAAA;MAAKU,SAAS,EAAC,gEAAgE;MAAAC,QAAA,gBAC7EX,OAAA;QAAKU,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3CX,OAAA;UAAKU,SAAS,EAAC;QAAiE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnFf,OAAA;UAAAW,QAAA,gBACEX,OAAA;YAAIU,SAAS,EAAC,kDAAkD;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9Ef,OAAA;YAAGU,SAAS,EAAC,6CAA6C;YAAAC,QAAA,EAAC;UAA6C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNf,OAAA;QAAKU,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAC9DX,OAAA;UAAKU,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCX,OAAA;YAAOU,SAAS,EAAC,gDAAgD;YAACM,OAAO,EAAC,eAAe;YAAAL,QAAA,EAAC;UAE1F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRf,OAAA;YACEiB,EAAE,EAAC,eAAe;YAClBC,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEb,YAAa;YACpBc,QAAQ,EAAEC,CAAC,IAAIhB,aAAa,CAAC,eAAe,EAAEgB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC9DT,SAAS,EAAC,2MAA2M;YACrNa,WAAW,EAAC;UAAgB;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACFf,OAAA;YAAKU,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBX,OAAA;cAAMU,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEf,OAAA;cAAKU,SAAS,EAAC,iGAAiG;cAAAC,QAAA,eAC9GX,OAAA;gBACEwB,GAAG,EAAElB,YAAa;gBAClBmB,GAAG,EAAC,uBAAuB;gBAC3Bf,SAAS,EAAC,oCAAoC;gBAC9CgB,OAAO,EAAEL,CAAC,IAAKA,CAAC,CAACM,aAAa,CAACH,GAAG,GAAGvB;cAAc;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNf,OAAA;UAAKU,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCX,OAAA;YAAOU,SAAS,EAAC,gDAAgD;YAACM,OAAO,EAAC,kBAAkB;YAAAL,QAAA,EAAC;UAE7F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRf,OAAA;YACEiB,EAAE,EAAC,kBAAkB;YACrBC,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEX,eAAgB;YACvBY,QAAQ,EAAEC,CAAC,IAAIhB,aAAa,CAAC,kBAAkB,EAAEgB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACjET,SAAS,EAAC,2MAA2M;YACrNa,WAAW,EAAC;UAAoB;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACa,EAAA,GAxDIzB,cAA6C;AA0DnD,eAAeA,cAAc;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}