import { PaymentMethod } from "../../../../components/settings/pages/researcher/PaymentMethodSettings";
import {
  SetUserDetails,
  UserDetails
} from "../../../store/reducer/slices/userReducer";
import axios from "../../axios";

const URL = "/v2/user";

export type PaymentDetailsAPIResponse = {
  mode: string;
  payment_details: string;
  createdAt: string;
  updatedAt: string;
};

export const parsePaymentDetails = (details?: PaymentDetailsAPIResponse) =>
  details
    ? {
        mode:
          details.mode === "paypal"
            ? PaymentMethod.PAYPAL
            : details.mode === "bank_account"
            ? PaymentMethod.BANK_ACCOUNT
            : details.mode === "orbit_remix"
            ? PaymentMethod.ORBIT
            : "",
        details: JSON.parse(details.payment_details)
      }
    : undefined;

export type UserDetailsAPIResponse = {
  data: {
    user: {
      user_id?: number;
      username: string;
      email: string;
      display_name?: string;
      about?: string;
      country?: string;
      links?: string;
      skills?: string;
      pfp?: string;
      banner?: string;
      two_fa_enabled: boolean;
      email_verified: boolean;
      first_time_uxp: boolean;
      languages?: string;
      hobbies?: string;
      education?: string;
      work_experience?: string;
      achievements?: string;
      tools_used?: string;
      community_engagement?: string;
      testimonials?: string;
      publications?: string;
      availability?: boolean;
      security_clearance?: string;
    };
    payment_details?: PaymentDetailsAPIResponse;
  };
};

export const getUserDetails = async () => {
  const response = (await axios.get(URL)) as UserDetailsAPIResponse;
  const paymentDetails = response.data.payment_details;

  return {
    username: response.data.user.username,
    displayName:
      response.data.user.display_name && response.data.user.display_name !== ""
        ? response.data.user.display_name
        : undefined,
    about: response.data.user.about || undefined,
    country: response.data.user.country || undefined,
    links: response.data.user.links
      ? JSON.parse(response.data.user.links)
      : undefined,
    skills: response.data.user.skills
      ? JSON.parse(response.data.user.skills)
      : undefined,
    profilePicture: response.data.user.pfp || undefined,
    bannerPicture: response.data.user.banner || undefined,
    otpEnabled: response.data.user.two_fa_enabled,
    firstTimeUXP: response.data.user.first_time_uxp,
    paymentDetails: parsePaymentDetails(paymentDetails),
    languages: response.data.user.languages
      ? JSON.parse(response.data.user.languages)
      : undefined,
    hobbies: response.data.user.hobbies
      ? JSON.parse(response.data.user.hobbies)
      : undefined,
    education: response.data.user.education
      ? JSON.parse(response.data.user.education)
      : undefined,
    work_experience: response.data.user.work_experience
      ? JSON.parse(response.data.user.work_experience)
      : undefined,
    achievements: response.data.user.achievements
      ? JSON.parse(response.data.user.achievements)
      : undefined,
    community_engagement: response.data.user.community_engagement
      ? JSON.parse(response.data.user.community_engagement)
      : undefined,
    testimonials: response.data.user.testimonials
      ? JSON.parse(response.data.user.testimonials)
      : undefined,
    publications: response.data.user.publications
      ? JSON.parse(response.data.user.publications)
      : undefined,
    tools_used: response.data.user.tools_used
      ? JSON.parse(response.data.user.tools_used)
      : undefined,
    security_clearance: response.data.user.security_clearance
      ? JSON.parse(response.data.user.security_clearance)
      : undefined
  } as UserDetails;
};

/**
 *
 * Same as above but for admin
 */
export const getUserDetailsAdmin = async (id: string) => {
  const response = (await axios.get(
    URL + `?id=${id}`
  )) as UserDetailsAPIResponse;
  const paymentDetails = response.data.payment_details;

  return {
    user_id: response.data.user.user_id,
    username: response.data.user.username,
    displayName:
      response.data.user.display_name && response.data.user.display_name !== ""
        ? response.data.user.display_name
        : undefined,
    about: response.data.user.about || undefined,
    country: response.data.user.country || undefined,
    links: response.data.user.links
      ? JSON.parse(response.data.user.links)
      : undefined,
    skills: response.data.user.skills
      ? JSON.parse(response.data.user.skills)
      : undefined,
    profilePicture: response.data.user.pfp || undefined,
    bannerPicture: response.data.user.banner || undefined,
    otpEnabled: response.data.user.two_fa_enabled,
    firstTimeUXP: response.data.user.first_time_uxp,
    paymentDetails: parsePaymentDetails(paymentDetails),
    languages: response.data.user.languages
      ? JSON.parse(response.data.user.languages)
      : undefined,
    hobbies: response.data.user.hobbies
      ? JSON.parse(response.data.user.hobbies)
      : undefined,
    education: response.data.user.education
      ? JSON.parse(response.data.user.education)
      : undefined,
    work_experience: response.data.user.work_experience
      ? JSON.parse(response.data.user.work_experience)
      : undefined,
    achievements: response.data.user.achievements
      ? JSON.parse(response.data.user.achievements)
      : undefined,
    community_engagement: response.data.user.community_engagement
      ? JSON.parse(response.data.user.community_engagement)
      : undefined,
    testimonials: response.data.user.testimonials
      ? JSON.parse(response.data.user.testimonials)
      : undefined,
    publications: response.data.user.publications
      ? JSON.parse(response.data.user.publications)
      : undefined,
    tools_used: response.data.user.tools_used
      ? JSON.parse(response.data.user.tools_used)
      : undefined,
    security_clearance: response.data.user.security_clearance
      ? JSON.parse(response.data.user.security_clearance)
      : undefined
  } as UserDetails;
};

/**
 * Make a request to the API to change
 * the users password.
 */
export const changePassword = async (
  currentPassword: string,
  newPassword: string
) => {
  await axios.post(URL + "/password", {
    current: currentPassword,
    new: newPassword
  });
};

/**
 * Make a request to the API to update the
 * two factor authentication settings
 */
export const requestAuthenticationChange = async (enabled: boolean) => {
  await axios.post(URL +"/two-factor-auth", {
    two_fa_enabled: enabled
  });

  return enabled;
};

/**
 * Make a request to update the
 * current users details
 */
export const postUserDetails = async (
  details: { username: string } & SetUserDetails
) => {
  // Collate user details into the POST body
  const formData = new FormData();
  formData.append(
    "data",
    JSON.stringify({
      ...details,
      profilePicture: undefined,
      bannerPicture: undefined,
      links: details.links ? JSON.stringify(details.links) : undefined,
      skills: details.skills ? JSON.stringify(details.skills) : undefined,
      languages: details.languages
        ? JSON.stringify(details.languages)
        : undefined,
      hobbies: details.hobbies ? JSON.stringify(details.hobbies) : undefined,
      achievements: details.achievements
        ? JSON.stringify(details.achievements)
        : undefined,
      education: details.education
        ? JSON.stringify(details.education)
        : undefined,
      work_experience: details.work_experience
        ? JSON.stringify(details.work_experience)
        : undefined,
      community_engagement: details.community_engagement
        ? JSON.stringify(details.community_engagement)
        : undefined,
      testimonials: details.testimonials
        ? JSON.stringify(details.testimonials)
        : undefined,
      publications: details.publications
        ? JSON.stringify(details.publications)
        : undefined,
      security_clearance: details.security_clearance
        ? JSON.stringify(details.security_clearance)
        : undefined,
      tools_used: details.tools_used
        ? JSON.stringify(details.tools_used)
        : undefined
    })
  );

  if (details.profilePicture) {
    formData.append("pfp", details.profilePicture);
  }

  if (details.bannerPicture) {
    formData.append("banner", details.bannerPicture);
  }

  // Make the request and return the new profile details
  await axios.post(URL + "/details", formData);
  return {
    ...details,
    profilePicture: undefined,
    bannerPicture: undefined
  };
};
