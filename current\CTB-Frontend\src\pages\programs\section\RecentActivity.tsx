import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import moment from "moment";
import DOMPurify from "dompurify"; 
import { RetestDetailsResponse } from "../../../utils/api/endpoints/retests/parseRetests";
import { FaUserCircle } from "react-icons/fa";
import { getRetestDetails } from "../../../utils/api/endpoints/retests/retestDetails";

const RecentActivity: React.FC = () => {
  const { programId } = useParams();
  const finalProgramId = programId || window.location.pathname.split("/").pop();

  const [data, setData] = useState<RetestDetailsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAll, setShowAll] = useState(false);

  useEffect(() => {
    if (!finalProgramId) {
      setError("Invalid program ID");
      setLoading(false);
      return;
    }

    (async () => {
      try {
        const response = await getRetestDetails(Number(finalProgramId));
        if (response) {
          setData(response);
        } else {
          setData({ reports: [] } as unknown as RetestDetailsResponse);
        }
      } catch (err) {
        console.error("Error fetching retest details:", err);
        setData({ reports: [] } as unknown as RetestDetailsResponse);
      } finally {
        setLoading(false);
      }
    })();
  }, [finalProgramId]);


  if (loading) return <p className="text-gray-500 text-center">Loading...</p>;
  if (error) return <p className="text-red-500">{error}</p>;


  const retestActivities =
    data?.reports?.flatMap((report) =>
      report?.retests?.flatMap((retest) =>
        retest.logs.map((log) => {
          const { actionTaken, newStatus, comments, createdAt } = log;

          let username = "Unknown User";
          let profileImage = "";
          let role = "Unknown Role";

          switch (actionTaken) {
            case "Researcher":
              username =
                typeof retest.researcher === "string"
                  ? retest.researcher
                  : retest.researcher?.username || "Unknown Researcher";
              profileImage =
                typeof retest.researcher !== "string"
                  ? retest.researcher?.pfp || ""
                  : "";
              role = "Researcher";
              break;

            case "Business":
              username =
                typeof retest.programOwner === "string"
                  ? retest.programOwner
                  : retest.programOwner?.username || "Unknown Owner";
              profileImage =
                typeof retest.programOwner !== "string"
                  ? retest.programOwner?.pfp || ""
                  : "";
              role = "Business";
              break;

            case "Admin":
              username = "Admin";
              role = "Admin";
              break;
          }

          return {
            username,
            role,
            profileImage,
            actionTaken,
            newStatus,
            comments: comments || "No comments provided.",
            createdAt,
            retestName: report.reportTitle || "Untitled Retest",
          };
        })
      )
    ) || [];

  const submissionActivities =
    data?.reports?.map((report) => ({
      username:
        typeof report.reportCreator === "string"
          ? report.reportCreator
          : report.reportCreator?.username || "Unknown User",
      role: "Researcher",
      profileImage:
        typeof report.reportCreator !== "string"
          ? report.reportCreator?.pfp || ""
          : "",
      actionTaken: "Submitted Report",
      newStatus: `submitted the report: ${report.reportTitle}`,
      comments: `${report.severityCategory} severity issue has been reported in this report.`,
      createdAt: report.submittedDate,
      retestName: report.reportTitle,
    })) || [];


  const allActivities = [...retestActivities, ...submissionActivities].sort(
    (a, b) => moment(b.createdAt).diff(moment(a.createdAt))
  );

  const displayedActivities = showAll ? allActivities : allActivities.slice(0, 3);

  return (
    <div className="container mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-8">Recent Activity</h2>
      {allActivities.length === 0 ? (
      <p className="text-gray-500 text-center">No recent interactions are available.</p>
    ) : (
      <div className="relative pl-2 space-y-6">
        {displayedActivities.map((activity, index) => (
          <div key={index} className="relative">
            {index !== displayedActivities.length - 1 && (
              <div className="absolute left-5 top-12 h-full w-0.5 bg-gray-300"></div>
            )}
            <div className="flex justify-between items-start">
              <div className="flex items-start space-x-4 mb-4">

                <div className="relative w-10 h-10 flex items-center justify-center rounded-full bg-gray-200 text-gray-500 font-bold text-lg">
                  {activity.profileImage ? (
                    <img
                      src={activity.profileImage}
                      alt={activity.username}
                      className="rounded-full w-full h-full"
                    />
                  ) : (
                    <FaUserCircle className="text-gray-500 text-3xl" />
                  )}
                </div>

                <div>
                  <div className="flex items-center flex-wrap space-x-2 mt-2">
                    <span className="font-semibold text-gray-900">{activity.username}</span>
                    <span
                      className={`px-2 py-1 text-xs text-white rounded-full ${activity.role === "Researcher"
                          ? "bg-yellow-500"
                          : activity.role === "Business"
                            ? "bg-green-500"
                            : activity.role === "Admin"
                              ? "bg-red-500"
                              : "bg-gray-500"
                        }`}
                    >
                      {activity.role}
                    </span>

                    {activity.actionTaken === "Submitted Report" ? (
                      <span className="text-gray-500 font-semibold text-sm">
                        {String(activity.newStatus)}
                      </span>
                    ) : (
                      <div className="flex items-center space-x-2 whitespace-nowrap">
                        <span className="text-gray-500 text-sm">updated status to</span>
                        <span className="font-semibold">{String(activity.newStatus)}</span>
                      </div>
                    )}
                  </div>
                  {activity.actionTaken !== "Submitted Report" ? (
                  <div className="mt-2 flex items-center space-x-2">
                    <span className="text-gray-500 text-sm">for report:</span>
                    <span className="font-semibold text-sm capitalize   ">
                      {String(activity.retestName)}
                    </span>
                  </div>
                  ) : null}
                  

                  <p
                    className="text-gray-700 mt-4"
                    dangerouslySetInnerHTML={{
                      __html: DOMPurify.sanitize(activity.comments.replace(/<\/?p>/g, "")),
                    }}
                  ></p>
                </div>
              </div>

              <p className="text-gray-400 text-[12px] mt-4">
                {moment(activity.createdAt).format("MMM D, YYYY, h:mm A UTC")}
              </p>
            </div>
          </div>
        
        ))}
      </div>
    )}
      {allActivities.length > 3 && (
        <div className="text-center mt-6">
          <button
            onClick={() => setShowAll(!showAll)}
            className="px-2 py-1 text-lg font-semibold text-blue-600 hover:underline"
          >
            {showAll ? "Show Less" : "Show More"}
          </button>
        </div>
      )}
    </div>
  );
};

export default RecentActivity;
