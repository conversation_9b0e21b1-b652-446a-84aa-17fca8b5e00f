import axios from '../../axios';

const CHAT_URL = '/v2/program-reports';

/**
 * For BUSINESS users, id must be the automated_report.id.
 * For ADMIN/QA, id must be the program_report.id.
 */
export const getChatMessages = async (
  id: string,
  chatType: 'admin_business' | 'admin_qa' | 'admin_subadmin',
  userRole: string
) => {
  if (userRole.toUpperCase() === 'BUSINESS' && id && id.includes('-')) {
    // UUIDs are usually program_report.id, not automated_report.id
    // eslint-disable-next-line no-console
    console.warn('BUSINESS user: id should be automated_report.id, not program_report.id');
  }
  const response = await axios.get(`${CHAT_URL}/${id}/chat/${chatType}`);
  return response.data;
};

export const postChatMessage = async (
  id: string,
  chatType: 'admin_business' | 'admin_qa' | 'admin_subadmin',
  message: string,
  userRole: string
) => {
  if (userRole.toUpperCase() === 'BUSINESS' && id && id.includes('-')) {
    // UUIDs are usually program_report.id, not automated_report.id
    // eslint-disable-next-line no-console
    console.warn('BUSINESS user: id should be automated_report.id, not program_report.id');
  }
  const response = await axios.post(`${CHAT_URL}/${id}/chat/${chatType}`, { message });
  return response.data;
}; 