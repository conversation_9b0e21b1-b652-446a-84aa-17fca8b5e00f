// useRoleString.ts - Custom hook for role string detection in PDFEditor
import { UserRole } from '../../../utils/api/endpoints/user/credentials';

export function useRoleString(role: string | number | undefined): string {
  const ROLE_MAP: Record<number, string> = {
    1: 'RESEARCHER',
    2: 'BUSINESS',
    3: 'ADMIN',
    4: 'QA',
    5: 'ADMIN_MANAGER',
    6: 'DEVELOPER',
    7: 'BUSINESS_MANAGER',
    8: 'BUSINESS_ADMINISTRATOR',
    9: 'SUB_ADMIN',
  };
  if (typeof role === 'number') return ROLE_MAP[role] || '';
  if (typeof role === 'string') return role;
  return '';
} 