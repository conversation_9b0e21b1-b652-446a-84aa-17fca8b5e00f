export interface DetailedFinding {
  abbreviation: string;
  title: string;
  severity_category: 'Critical' | 'High' | 'Medium' | 'Low';
  status: 'Open' | 'Closed' | 'In Progress';
  scope: string;
  description: string;
  instructions: string;
  impact: string;
  fix: string;
  submitted_date: string;
}

export interface ProgramDetail {
  testing_type: string;
  description: string;
}

export interface TargetDetail {
  type: string;
  url: string;
}

export interface Finding {
  id: string;
  title: string;
  severity: string;
  status: string;
  description: string;
  impact: string;
  remediation: string;
}

export interface Report {
  abbreviation: string;
  title: string;
  severity_category: string;
  status: string;
}

export interface SeverityCounts {
  Open: number;
  Closed: number;
  Total: number;
}

export const DEFAULT_REPORT_HTML_CONTENT = {
  disclaimer: `<p>Capture The Bug Ltd. has prepared this document exclusively for <strong>{company_name}</strong>. Copying, or modification of this document is strictly prohibited without Capture The Bug Ltd.'s written consent, except for specific purposes when such permission is granted. This document is confidential and proprietary material of Capture The Bug Ltd. and must always be treated as such, not to be disclosed to third parties without prior consent.</p>
<p>The information within is provided 'as-is,' without any warranty. Vulnerability assessments offer a snapshot of security at a specific moment and are not exhaustive risk assessments. New vulnerabilities may emerge post-assessment. Thus, this report is a guide, not a definitive risk analysis.</p>
<p>Capture The Bug Ltd. assumes no liability for any changes, omissions, or errors in this document. Capture The Bug Ltd. shall not be liable for any damages, financial or otherwise, arising out of the use or misuse of this report by any current employee of <strong>{company_name}</strong> or any member of the general public.</p>`,

  executive_summary: `<p>Capture The Bug is a modern Penetration Testing as a Service (PTaaS) platform designed to support fast-moving engineering teams and security-conscious enterprises. Organizations across New Zealand, Australia, and North America-including public companies and high-growth SaaS teams-trust Capture The Bug to secure their CI/CD pipelines, meet compliance goals, and reduce time-to-remediation without slowing development velocity. Capture The Bug is HQ in New Zealand and proudly works with customers across regulated and innovation-driven sectors.</p>
<p><strong>{company_name}</strong> entrusted Capture The Bug to conduct a {testing_type}. This assessment evaluated the application's security posture from a gray-box perspective to focus on its resilience against common attack patterns and identify vulnerabilities in its internal and external interfaces.</p>`,

  key_findings: `<p>Capture The Bug's thorough assessment identified <strong>{total_findings}</strong> findings, with <strong style='color:#8b0000'>{critical_total}</strong> categorized as <strong style='color:#8b0000'>Critical Severity</strong>, <strong style='color:#ff4500'>{high_total}</strong> categorized as <strong style='color:#ff4500'>High Severity</strong>, <strong style='color:#ffd700'>{medium_total}</strong> categorized as <strong style='color:#ffd700'>Medium Severity</strong> and <strong style='color:#32cd32'>{low_total}</strong> as <strong style='color:#32cd32'>Low Severity</strong>. During the assessment, all critical and high vulnerabilities were reported to the <strong>{company_name}</strong> team, and the client addressed the reported vulnerabilities concurrently.</p>
<p>Capture The Bug team has documented the identified vulnerabilities along with their current statuses in the key findings section of this report. Prompt action is advised to improve the application's overall security posture.</p>
`,

  scope: `<p>The scope of the assessment was limited to performing Vulnerability Assessment and Penetration Testing on the <strong>{company_name}</strong> systems mentioned below:</p>`,

  project_objectives: `<p>The objective of this assessment was to validate the overall security posture of the in-scope systems from a security perspective. It included determining the application's ability to resist common attack patterns and identifying vulnerable areas in the internal or external interfaces that might be exploited by a malicious user.</p>`
};

export interface PentestReport {
  id: string;
  title: string;
  company_name: string;
  document_number: string;
  status: 'draft' | 'qa_review' | 'admin_review' | 'approved' | 'rejected';
  created_at: string;
  updated_at: string;
  current_version: number;
  total_versions: number;
  user: {
    id: number;
    name: string;
    email: string;
  } | null;
  total_findings: number;
  critical_count: number;
  high_count: number;
  medium_count: number;
  low_count: number;
  program_names?: Record<number, string>;
  program_ids?: number[];
}

export interface PentestReportsResponse {
  status: string;
  data: {
    reports: PentestReport[];
    pagination: {
      current_page: number;
      total_pages: number;
      total_count: number;
      limit: number;
    };
  };
}

export interface PentestReportsFilters {
  page?: number;
  limit?: number;
  status?: string;
  company_name?: string;
  date_from?: string;
  date_to?: string;
  sort_by?: string;
  sort_order?: 'ASC' | 'DESC';
} 