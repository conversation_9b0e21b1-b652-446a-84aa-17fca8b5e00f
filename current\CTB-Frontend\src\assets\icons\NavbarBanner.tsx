import React from "react";

interface NavbarBannerProps {
  className?: string;
  width?: number;
  height?: number;
}

const NavbarBanner: React.FC<NavbarBannerProps> = ({
  className = "",
  width = 170,
  height = 150,
}) => {
  const imageURL = "https://i.ibb.co/N1yQBN0/Navbar.png";

  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 50 45"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      role="img"
      aria-label="Navbar banner"
    >
      <rect width="50" height="45" fill="url(#navbar-pattern)" />
      <defs>
        <pattern
          id="navbar-pattern"
          patternUnits="userSpaceOnUse"
          width="50"
          height="45"
        >
          <image
            width="50"
            height="45"
            xlinkHref={imageURL}
          />
        </pattern>
      </defs>
    </svg>
  );
};

export default NavbarBanner;
