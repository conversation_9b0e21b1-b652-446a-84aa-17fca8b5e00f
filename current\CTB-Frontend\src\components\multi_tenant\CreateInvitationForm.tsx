import React, { useState } from "react";
import toast from "react-hot-toast";
import { UserRole } from "../../utils/api/endpoints/user/credentials";
import { createInvitation } from "../../utils/api/endpoints/user/invitation";
import { RoleOption } from "../../utils/hooks/multi-tenant/invitation";

interface CreateInvitationFormProps {
  availableRoles: RoleOption[];
  onInvitationCreated: () => void;
}

const CreateInvitationForm: React.FC<CreateInvitationFormProps> = ({
  availableRoles,
  onInvitationCreated
}) => {
  const [email, setEmail] = useState("");
  const [selectedRole, setSelectedRole] = useState<UserRole>(
    availableRoles.length > 0 ? availableRoles[0].value : UserRole.QA
  );
  const [isProcessing, setIsProcessing] = useState(false);

  const handleCreateInvitation = async () => {
    if (!email) {
      toast.error("Email is required");
      return;
    }

    setIsProcessing(true);
    try {
      await createInvitation({ email, role: selectedRole });
      onInvitationCreated();
      setEmail("");
      if (availableRoles.length > 0) {
        setSelectedRole(availableRoles[0].value);
      }
      toast.success("Invitation sent successfully");
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to send invitation");
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="mb-8 rounded-lg bg-gray-50 p-2 py-4">
      <h3 className="mb-4 text-xl font-semibold text-gray-900">
        Create new invitation
      </h3>
      <div className="flex flex-col gap-4 sm:flex-row">
        <input
          type="email"
          placeholder="Enter email address"
          className="flex-1 rounded-md border border-gray-300 p-3 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
          value={email}
          onChange={e => setEmail(e.target.value)}
        />
        <select
          className="w-full rounded-md border border-gray-300 p-3 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 sm:w-1/4"
          value={selectedRole}
          onChange={e => setSelectedRole(Number(e.target.value))}
        >
          {availableRoles.map(({ value, label }) => (
            <option key={value} value={value}>
              {label}
            </option>
          ))}
        </select>
        <button
          onClick={handleCreateInvitation}
          disabled={isProcessing}
          className="w-full rounded-md bg-blue-600 px-6 py-3 text-white shadow-sm transition-colors hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto"
        >
          {isProcessing ? "Sending..." : "Send Invitation"}
        </button>
      </div>
    </div>
  );
};

export default CreateInvitationForm;
