{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\jira-old\\\\CTB-Frontend\\\\src\\\\pages\\\\automated-report\\\\components\\\\sections\\\\SummaryOfFindingsPage.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Page, View, Text, Image, StyleSheet } from '@react-pdf/renderer';\nimport { useSectionPages } from '../SectionPageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst styles = StyleSheet.create({\n  footer: {\n    position: 'absolute',\n    bottom: 30,\n    left: 0,\n    right: 0,\n    fontSize: 10,\n    color: 'grey',\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    paddingHorizontal: 24\n  },\n  footerLeft: {\n    textAlign: 'left',\n    flex: 1\n  },\n  footerRight: {\n    textAlign: 'right',\n    flex: 1\n  }\n});\nconst SummaryOfFindingsPage = ({\n  reportData,\n  pieChartImage,\n  barChartImage,\n  sectionId\n}) => {\n  _s();\n  var _reportData$open_clos, _reportData$open_clos2, _reportData$open_clos3, _reportData$open_clos4, _reportData$open_clos5, _reportData$open_clos6, _reportData$open_clos7, _reportData$open_clos8;\n  const {\n    updateSectionPage\n  } = useSectionPages();\n  return /*#__PURE__*/_jsxDEV(Page, {\n    size: \"A4\",\n    id: sectionId,\n    style: {\n      flexDirection: 'column',\n      backgroundColor: '#ffffff',\n      padding: '20mm 15mm',\n      fontFamily: 'Helvetica',\n      fontSize: 10\n    },\n    children: [/*#__PURE__*/_jsxDEV(View, {\n      style: {\n        flex: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          fontSize: 20,\n          fontWeight: 'bold',\n          color: '#2563eb',\n          marginBottom: 16\n        },\n        children: \"SUMMARY OF FINDINGS\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          fontSize: 13,\n          marginBottom: 12,\n          color: '#374151'\n        },\n        children: [\"This table provides the summary of the vulnerabilities that were identified during the assessment of  \", reportData.program_name, \":\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(View, {\n        style: {\n          borderRadius: 14,\n          overflow: 'hidden',\n          marginBottom: 20,\n          backgroundColor: '#f8fafc',\n          width: '90%',\n          alignSelf: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(View, {\n          style: {\n            flexDirection: 'row',\n            backgroundColor: '#2563eb',\n            borderTopLeftRadius: 14,\n            borderTopRightRadius: 14\n          },\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              padding: 14,\n              fontWeight: 'bold',\n              width: '10%',\n              color: '#fff',\n              fontSize: 13,\n              letterSpacing: 1,\n              textAlign: 'center'\n            },\n            children: \" \"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              padding: 14,\n              fontWeight: 'bold',\n              width: '56%',\n              color: '#fff',\n              fontSize: 13,\n              letterSpacing: 1\n            },\n            children: \"Severity\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              padding: 14,\n              fontWeight: 'bold',\n              width: '34%',\n              color: '#fff',\n              fontSize: 13,\n              letterSpacing: 1,\n              textAlign: 'center'\n            },\n            children: \"Total\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(View, {\n          style: {\n            flexDirection: 'row',\n            backgroundColor: '#e0e7ef',\n            borderBottomWidth: 1,\n            borderBottomColor: '#e5e7eb'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              padding: 14,\n              width: '10%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              padding: 14,\n              width: '56%',\n              fontWeight: 'bold',\n              color: '#2563eb',\n              fontSize: 13\n            },\n            children: \"Total Findings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              padding: 14,\n              width: '34%',\n              fontWeight: 'bold',\n              textAlign: 'center',\n              color: '#2563eb',\n              fontSize: 13\n            },\n            children: reportData.total_findings\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(View, {\n          style: {\n            flexDirection: 'row',\n            backgroundColor: '#fef2f2',\n            borderBottomWidth: 1,\n            borderBottomColor: '#e5e7eb',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(View, {\n            style: {\n              padding: 12,\n              width: '10%',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(View, {\n              style: {\n                minWidth: 24,\n                minHeight: 24,\n                backgroundColor: '#B91C1C',\n                borderRadius: 12,\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                style: {\n                  color: '#fff',\n                  fontWeight: 'bold',\n                  fontSize: 12\n                },\n                children: \"!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              padding: 12,\n              width: '56%',\n              fontWeight: '600',\n              color: '#B91C1C',\n              fontSize: 12\n            },\n            children: \"Critical\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              padding: 12,\n              width: '34%',\n              fontWeight: 'bold',\n              textAlign: 'center',\n              fontSize: 12\n            },\n            children: ((_reportData$open_clos = reportData.open_close_counts_by_severity) === null || _reportData$open_clos === void 0 ? void 0 : (_reportData$open_clos2 = _reportData$open_clos.Critical) === null || _reportData$open_clos2 === void 0 ? void 0 : _reportData$open_clos2.Total) || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(View, {\n          style: {\n            flexDirection: 'row',\n            backgroundColor: '#fff7ed',\n            borderBottomWidth: 1,\n            borderBottomColor: '#e5e7eb',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(View, {\n            style: {\n              padding: 12,\n              width: '10%',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(View, {\n              style: {\n                minWidth: 24,\n                minHeight: 24,\n                backgroundColor: '#F59E42',\n                borderRadius: 12,\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                style: {\n                  color: '#fff',\n                  fontWeight: 'bold',\n                  fontSize: 12\n                },\n                children: \"!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              padding: 12,\n              width: '56%',\n              fontWeight: '600',\n              color: '#F59E42',\n              fontSize: 12\n            },\n            children: \"High\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              padding: 12,\n              width: '34%',\n              fontWeight: 'bold',\n              textAlign: 'center',\n              fontSize: 12\n            },\n            children: ((_reportData$open_clos3 = reportData.open_close_counts_by_severity) === null || _reportData$open_clos3 === void 0 ? void 0 : (_reportData$open_clos4 = _reportData$open_clos3.High) === null || _reportData$open_clos4 === void 0 ? void 0 : _reportData$open_clos4.Total) || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(View, {\n          style: {\n            flexDirection: 'row',\n            backgroundColor: '#fef9c3',\n            borderBottomWidth: 1,\n            borderBottomColor: '#e5e7eb',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(View, {\n            style: {\n              padding: 12,\n              width: '10%',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(View, {\n              style: {\n                minWidth: 24,\n                minHeight: 24,\n                backgroundColor: '#FACC15',\n                borderRadius: 12,\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                style: {\n                  color: '#fff',\n                  fontWeight: 'bold',\n                  fontSize: 12\n                },\n                children: \"!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              padding: 12,\n              width: '56%',\n              fontWeight: '600',\n              color: '#FACC15',\n              fontSize: 12\n            },\n            children: \"Medium\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              padding: 12,\n              width: '34%',\n              fontWeight: 'bold',\n              textAlign: 'center',\n              fontSize: 12\n            },\n            children: ((_reportData$open_clos5 = reportData.open_close_counts_by_severity) === null || _reportData$open_clos5 === void 0 ? void 0 : (_reportData$open_clos6 = _reportData$open_clos5.Medium) === null || _reportData$open_clos6 === void 0 ? void 0 : _reportData$open_clos6.Total) || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(View, {\n          style: {\n            flexDirection: 'row',\n            backgroundColor: '#dcfce7',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(View, {\n            style: {\n              padding: 12,\n              width: '10%',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(View, {\n              style: {\n                minWidth: 24,\n                minHeight: 24,\n                backgroundColor: '#22C55E',\n                borderRadius: 12,\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                style: {\n                  color: '#fff',\n                  fontWeight: 'bold',\n                  fontSize: 12\n                },\n                children: \"i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              padding: 12,\n              width: '56%',\n              fontWeight: '600',\n              color: '#22C55E',\n              fontSize: 12\n            },\n            children: \"Low\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              padding: 12,\n              width: '34%',\n              fontWeight: 'bold',\n              textAlign: 'center',\n              fontSize: 12\n            },\n            children: ((_reportData$open_clos7 = reportData.open_close_counts_by_severity) === null || _reportData$open_clos7 === void 0 ? void 0 : (_reportData$open_clos8 = _reportData$open_clos7.Low) === null || _reportData$open_clos8 === void 0 ? void 0 : _reportData$open_clos8.Total) || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          fontSize: 12,\n          color: '#64748b',\n          textAlign: 'center',\n          marginBottom: 16\n        },\n        children: \"Table 2: Summary of Findings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          fontSize: 13,\n          marginBottom: 8,\n          color: '#374151'\n        },\n        children: \"The following charts illustrate the distribution of vulnerabilities by severity and status:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(View, {\n        style: {\n          flexDirection: 'row',\n          justifyContent: 'center',\n          marginBottom: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(View, {\n          style: {\n            width: '48%',\n            borderWidth: 1,\n            borderColor: '#e5e7eb',\n            borderRadius: 8,\n            padding: 20,\n            backgroundColor: '#f9fafb',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              fontSize: 14,\n              fontWeight: 'bold',\n              textAlign: 'center',\n              marginBottom: 8\n            },\n            children: \"Vulnerability Distribution by Severity\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), pieChartImage ? /*#__PURE__*/_jsxDEV(Image, {\n            src: pieChartImage,\n            style: {\n              width: 160,\n              height: 160,\n              objectFit: 'contain',\n              marginBottom: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(View, {\n            style: {\n              width: 160,\n              height: 160,\n              backgroundColor: '#e5e7eb',\n              alignItems: 'center',\n              justifyContent: 'center',\n              borderRadius: 90\n            },\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                color: '#64748b',\n                fontSize: 12\n              },\n              children: \"No Chart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          fontSize: 12,\n          color: '#64748b',\n          textAlign: 'center',\n          marginTop: 8\n        },\n        children: \"Figure 1: Visualization of Vulnerabilities by Severity and Status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          display: 'none'\n        },\n        render: ({\n          pageNumber\n        }) => '',\n        fixed: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(View, {\n      style: styles.footer,\n      fixed: true,\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        style: styles.footerLeft,\n        children: reportData.document_number || 'Document Number'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        style: styles.footerRight,\n        render: ({\n          pageNumber,\n          totalPages\n        }) => `${pageNumber} / ${totalPages}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(SummaryOfFindingsPage, \"3rf3HaIqG9R7of1rm8wX9C3kpH0=\", false, function () {\n  return [useSectionPages];\n});\n_c = SummaryOfFindingsPage;\nexport default SummaryOfFindingsPage;\nvar _c;\n$RefreshReg$(_c, \"SummaryOfFindingsPage\");", "map": {"version": 3, "names": ["React", "Page", "View", "Text", "Image", "StyleSheet", "useSectionPages", "jsxDEV", "_jsxDEV", "styles", "create", "footer", "position", "bottom", "left", "right", "fontSize", "color", "flexDirection", "justifyContent", "alignItems", "paddingHorizontal", "footerLeft", "textAlign", "flex", "footerRight", "SummaryOfFindingsPage", "reportData", "pieChartImage", "barChartImage", "sectionId", "_s", "_reportData$open_clos", "_reportData$open_clos2", "_reportData$open_clos3", "_reportData$open_clos4", "_reportData$open_clos5", "_reportData$open_clos6", "_reportData$open_clos7", "_reportData$open_clos8", "updateSectionPage", "size", "id", "style", "backgroundColor", "padding", "fontFamily", "children", "fontWeight", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "program_name", "borderRadius", "overflow", "width", "alignSelf", "borderTopLeftRadius", "borderTopRightRadius", "letterSpacing", "borderBottomWidth", "borderBottomColor", "total_findings", "min<PERSON><PERSON><PERSON>", "minHeight", "open_close_counts_by_severity", "Critical", "Total", "High", "Medium", "Low", "borderWidth", "borderColor", "src", "height", "objectFit", "marginTop", "display", "render", "pageNumber", "fixed", "document_number", "totalPages", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/jira-old/CTB-Frontend/src/pages/automated-report/components/sections/SummaryOfFindingsPage.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Page, View, Text, Image, StyleSheet } from '@react-pdf/renderer';\r\nimport { ReportData } from '../../types/report.types';\r\nimport { useSectionPages } from '../SectionPageContext';\r\n\r\ninterface SummaryOfFindingsPageProps {\r\n  reportData: ReportData;\r\n  pieChartImage?: string;\r\n  barChartImage?: string;\r\n  sectionId?: string;\r\n}\r\n\r\nconst styles = StyleSheet.create({\r\n  footer: {\r\n    position: 'absolute',\r\n    bottom: 30,\r\n    left: 0,\r\n    right: 0,\r\n    fontSize: 10,\r\n    color: 'grey',\r\n    flexDirection: 'row',\r\n    justifyContent: 'space-between',\r\n    alignItems: 'center',\r\n    paddingHorizontal: 24,\r\n  },\r\n  footerLeft: {\r\n    textAlign: 'left',\r\n    flex: 1,\r\n  },\r\n  footerRight: {\r\n    textAlign: 'right',\r\n    flex: 1,\r\n  },\r\n});\r\n\r\nconst SummaryOfFindingsPage: React.FC<SummaryOfFindingsPageProps> = ({ reportData, pieChartImage, barChartImage, sectionId }) => {\r\n  const { updateSectionPage } = useSectionPages();\r\n  return (\r\n    <Page size=\"A4\" id={sectionId} style={{\r\n      flexDirection: 'column',\r\n      backgroundColor: '#ffffff',\r\n      padding: '20mm 15mm',\r\n      fontFamily: 'Helvetica',\r\n      fontSize: 10,\r\n    }}>\r\n      <View style={{ flex: 1}}>\r\n        <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#2563eb', marginBottom: 16 }}>\r\n          SUMMARY OF FINDINGS\r\n        </Text>\r\n        <Text style={{ fontSize: 13, marginBottom: 12, color: '#374151' }}>\r\n          This table provides the summary of the vulnerabilities that were identified during the assessment of  {reportData.program_name}:\r\n        </Text>\r\n        {/* Summary Table */}\r\n        <View style={{\r\n          borderRadius: 14,\r\n          overflow: 'hidden',\r\n          marginBottom: 20,\r\n          backgroundColor: '#f8fafc',\r\n          width: '90%',\r\n          alignSelf: 'center',\r\n        }}>\r\n          {/* Table Header */}\r\n          <View style={{ flexDirection: 'row', backgroundColor: '#2563eb', borderTopLeftRadius: 14, borderTopRightRadius: 14 }}>\r\n            <Text style={{ padding: 14, fontWeight: 'bold', width: '10%', color: '#fff', fontSize: 13, letterSpacing: 1, textAlign: 'center' }}> </Text>\r\n            <Text style={{ padding: 14, fontWeight: 'bold', width: '56%', color: '#fff', fontSize: 13, letterSpacing: 1 }}>Severity</Text>\r\n            <Text style={{ padding: 14, fontWeight: 'bold', width: '34%', color: '#fff', fontSize: 13, letterSpacing: 1, textAlign: 'center' }}>Total</Text>\r\n          </View>\r\n          {/* Total Findings Row */}\r\n          <View style={{ flexDirection: 'row', backgroundColor: '#e0e7ef', borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>\r\n            <Text style={{ padding: 14, width: '10%' }}></Text>\r\n            <Text style={{ padding: 14, width: '56%', fontWeight: 'bold', color: '#2563eb', fontSize: 13 }}>Total Findings</Text>\r\n            <Text style={{ padding: 14, width: '34%', fontWeight: 'bold', textAlign: 'center', color: '#2563eb', fontSize: 13 }}>{reportData.total_findings}</Text>\r\n          </View>\r\n          {/* Critical Row */}\r\n          <View style={{ flexDirection: 'row', backgroundColor: '#fef2f2', borderBottomWidth: 1, borderBottomColor: '#e5e7eb', alignItems: 'center' }}>\r\n            <View style={{ padding: 12, width: '10%', alignItems: 'center', justifyContent: 'center' }}>\r\n              <View style={{ minWidth: 24, minHeight: 24, backgroundColor: '#B91C1C', borderRadius: 12, alignItems: 'center', justifyContent: 'center' }}>\r\n                <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 12 }}>!</Text>\r\n              </View>\r\n            </View>\r\n            <Text style={{ padding: 12, width: '56%', fontWeight: '600', color: '#B91C1C', fontSize: 12 }}>Critical</Text>\r\n            <Text style={{ padding: 12, width: '34%', fontWeight: 'bold', textAlign: 'center', fontSize: 12 }}>{reportData.open_close_counts_by_severity?.Critical?.Total || 0}</Text>\r\n          </View>\r\n          {/* High Row */}\r\n          <View style={{ flexDirection: 'row', backgroundColor: '#fff7ed', borderBottomWidth: 1, borderBottomColor: '#e5e7eb', alignItems: 'center' }}>\r\n            <View style={{ padding: 12, width: '10%', alignItems: 'center', justifyContent: 'center' }}>\r\n              <View style={{ minWidth: 24, minHeight: 24, backgroundColor: '#F59E42', borderRadius: 12, alignItems: 'center', justifyContent: 'center' }}>\r\n                <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 12 }}>!</Text>\r\n              </View>\r\n            </View>\r\n            <Text style={{ padding: 12, width: '56%', fontWeight: '600', color: '#F59E42', fontSize: 12 }}>High</Text>\r\n            <Text style={{ padding: 12, width: '34%', fontWeight: 'bold', textAlign: 'center', fontSize: 12 }}>{reportData.open_close_counts_by_severity?.High?.Total || 0}</Text>\r\n          </View>\r\n          {/* Medium Row */}\r\n          <View style={{ flexDirection: 'row', backgroundColor: '#fef9c3', borderBottomWidth: 1, borderBottomColor: '#e5e7eb', alignItems: 'center' }}>\r\n            <View style={{ padding: 12, width: '10%', alignItems: 'center', justifyContent: 'center' }}>\r\n              <View style={{ minWidth: 24, minHeight: 24, backgroundColor: '#FACC15', borderRadius: 12, alignItems: 'center', justifyContent: 'center' }}>\r\n                <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 12 }}>!</Text>\r\n              </View>\r\n            </View>\r\n            <Text style={{ padding: 12, width: '56%', fontWeight: '600', color: '#FACC15', fontSize: 12 }}>Medium</Text>\r\n            <Text style={{ padding: 12, width: '34%', fontWeight: 'bold', textAlign: 'center', fontSize: 12 }}>{reportData.open_close_counts_by_severity?.Medium?.Total || 0}</Text>\r\n          </View>\r\n          {/* Low Row */}\r\n          <View style={{ flexDirection: 'row', backgroundColor: '#dcfce7', alignItems: 'center' }}>\r\n            <View style={{ padding: 12, width: '10%', alignItems: 'center', justifyContent: 'center' }}>\r\n              <View style={{ minWidth: 24, minHeight: 24, backgroundColor: '#22C55E', borderRadius: 12, alignItems: 'center', justifyContent: 'center' }}>\r\n                <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 12 }}>i</Text>\r\n              </View>\r\n            </View>\r\n            <Text style={{ padding: 12, width: '56%', fontWeight: '600', color: '#22C55E', fontSize: 12 }}>Low</Text>\r\n            <Text style={{ padding: 12, width: '34%', fontWeight: 'bold', textAlign: 'center', fontSize: 12 }}>{reportData.open_close_counts_by_severity?.Low?.Total || 0}</Text>\r\n          </View>\r\n        </View>\r\n        <Text style={{ fontSize: 12, color: '#64748b', textAlign: 'center', marginBottom: 16 }}>Table 2: Summary of Findings</Text>\r\n        <Text style={{ fontSize: 13, marginBottom: 8, color: '#374151' }}>\r\n          The following charts illustrate the distribution of vulnerabilities by severity and status:\r\n        </Text>\r\n        {/* Centered Pie Chart Only */}\r\n        <View style={{ flexDirection: 'row', justifyContent: 'center', marginBottom: 16 }}>\r\n          <View style={{ width: '48%', borderWidth: 1, borderColor: '#e5e7eb', borderRadius: 8, padding: 20, backgroundColor: '#f9fafb', alignItems: 'center' }}>\r\n            <Text style={{ fontSize: 14, fontWeight: 'bold', textAlign: 'center', marginBottom: 8 }}>\r\n              Vulnerability Distribution by Severity\r\n            </Text>\r\n            {pieChartImage ? (\r\n              <Image src={pieChartImage} style={{ width: 160, height: 160, objectFit: 'contain', marginBottom: 8 }} />\r\n            ) : (\r\n              <View style={{ width: 160, height: 160, backgroundColor: '#e5e7eb', alignItems: 'center', justifyContent: 'center', borderRadius: 90 }}>\r\n                <Text style={{ color: '#64748b', fontSize: 12 }}>No Chart</Text>\r\n              </View>\r\n            )}\r\n          </View>\r\n        </View>\r\n        {/*\r\n        // Bar chart commented out as per request\r\n        <View style={{ width: '48%', borderWidth: 1, borderColor: '#e5e7eb', borderRadius: 8, padding: 20, backgroundColor: '#f9fafb', alignItems: 'center' }}>\r\n          <Text style={{ fontSize: 14, fontWeight: 'bold', textAlign: 'center', marginBottom: 8 }}>\r\n            Vulnerability Status\r\n          </Text>\r\n          {barChartImage ? (\r\n            <Image src={barChartImage} style={{ width: 160, height: 160, objectFit: 'contain', marginBottom: 8 }} />\r\n          ) : (\r\n            <View style={{ width: 160, height: 160, backgroundColor: '#e5e7eb', alignItems: 'center', justifyContent: 'center', borderRadius: 12 }}>\r\n              <Text style={{ color: '#64748b', fontSize: 12 }}>No Chart</Text>\r\n            </View>\r\n          )}\r\n        </View>\r\n        */}\r\n        <Text style={{ fontSize: 12, color: '#64748b', textAlign: 'center', marginTop: 8 }}>\r\n          Figure 1: Visualization of Vulnerabilities by Severity and Status\r\n        </Text>\r\n        <Text style={{ display: 'none' }} render={({ pageNumber }) => ''} fixed />\r\n      </View>\r\n      <View style={styles.footer} fixed>\r\n        <Text style={styles.footerLeft}>{reportData.document_number || 'Document Number'}</Text>\r\n        <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />\r\n      </View>\r\n    </Page>\r\n  );\r\n};\r\n\r\nexport default SummaryOfFindingsPage; "], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,QAAQ,qBAAqB;AAEzE,SAASC,eAAe,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASxD,MAAMC,MAAM,GAAGJ,UAAU,CAACK,MAAM,CAAC;EAC/BC,MAAM,EAAE;IACNC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,MAAM;IACbC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBC,iBAAiB,EAAE;EACrB,CAAC;EACDC,UAAU,EAAE;IACVC,SAAS,EAAE,MAAM;IACjBC,IAAI,EAAE;EACR,CAAC;EACDC,WAAW,EAAE;IACXF,SAAS,EAAE,OAAO;IAClBC,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AAEF,MAAME,qBAA2D,GAAGA,CAAC;EAAEC,UAAU;EAAEC,aAAa;EAAEC,aAAa;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC/H,MAAM;IAAEC;EAAkB,CAAC,GAAGlC,eAAe,CAAC,CAAC;EAC/C,oBACEE,OAAA,CAACP,IAAI;IAACwC,IAAI,EAAC,IAAI;IAACC,EAAE,EAAEZ,SAAU;IAACa,KAAK,EAAE;MACpCzB,aAAa,EAAE,QAAQ;MACvB0B,eAAe,EAAE,SAAS;MAC1BC,OAAO,EAAE,WAAW;MACpBC,UAAU,EAAE,WAAW;MACvB9B,QAAQ,EAAE;IACZ,CAAE;IAAA+B,QAAA,gBACAvC,OAAA,CAACN,IAAI;MAACyC,KAAK,EAAE;QAAEnB,IAAI,EAAE;MAAC,CAAE;MAAAuB,QAAA,gBACtBvC,OAAA,CAACL,IAAI;QAACwC,KAAK,EAAE;UAAE3B,QAAQ,EAAE,EAAE;UAAEgC,UAAU,EAAE,MAAM;UAAE/B,KAAK,EAAE,SAAS;UAAEgC,YAAY,EAAE;QAAG,CAAE;QAAAF,QAAA,EAAC;MAEvF;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACP7C,OAAA,CAACL,IAAI;QAACwC,KAAK,EAAE;UAAE3B,QAAQ,EAAE,EAAE;UAAEiC,YAAY,EAAE,EAAE;UAAEhC,KAAK,EAAE;QAAU,CAAE;QAAA8B,QAAA,GAAC,wGACqC,EAACpB,UAAU,CAAC2B,YAAY,EAAC,GACjI;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEP7C,OAAA,CAACN,IAAI;QAACyC,KAAK,EAAE;UACXY,YAAY,EAAE,EAAE;UAChBC,QAAQ,EAAE,QAAQ;UAClBP,YAAY,EAAE,EAAE;UAChBL,eAAe,EAAE,SAAS;UAC1Ba,KAAK,EAAE,KAAK;UACZC,SAAS,EAAE;QACb,CAAE;QAAAX,QAAA,gBAEAvC,OAAA,CAACN,IAAI;UAACyC,KAAK,EAAE;YAAEzB,aAAa,EAAE,KAAK;YAAE0B,eAAe,EAAE,SAAS;YAAEe,mBAAmB,EAAE,EAAE;YAAEC,oBAAoB,EAAE;UAAG,CAAE;UAAAb,QAAA,gBACnHvC,OAAA,CAACL,IAAI;YAACwC,KAAK,EAAE;cAAEE,OAAO,EAAE,EAAE;cAAEG,UAAU,EAAE,MAAM;cAAES,KAAK,EAAE,KAAK;cAAExC,KAAK,EAAE,MAAM;cAAED,QAAQ,EAAE,EAAE;cAAE6C,aAAa,EAAE,CAAC;cAAEtC,SAAS,EAAE;YAAS,CAAE;YAAAwB,QAAA,EAAC;UAAC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5I7C,OAAA,CAACL,IAAI;YAACwC,KAAK,EAAE;cAAEE,OAAO,EAAE,EAAE;cAAEG,UAAU,EAAE,MAAM;cAAES,KAAK,EAAE,KAAK;cAAExC,KAAK,EAAE,MAAM;cAAED,QAAQ,EAAE,EAAE;cAAE6C,aAAa,EAAE;YAAE,CAAE;YAAAd,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9H7C,OAAA,CAACL,IAAI;YAACwC,KAAK,EAAE;cAAEE,OAAO,EAAE,EAAE;cAAEG,UAAU,EAAE,MAAM;cAAES,KAAK,EAAE,KAAK;cAAExC,KAAK,EAAE,MAAM;cAAED,QAAQ,EAAE,EAAE;cAAE6C,aAAa,EAAE,CAAC;cAAEtC,SAAS,EAAE;YAAS,CAAE;YAAAwB,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5I,CAAC,eAEP7C,OAAA,CAACN,IAAI;UAACyC,KAAK,EAAE;YAAEzB,aAAa,EAAE,KAAK;YAAE0B,eAAe,EAAE,SAAS;YAAEkB,iBAAiB,EAAE,CAAC;YAAEC,iBAAiB,EAAE;UAAU,CAAE;UAAAhB,QAAA,gBACpHvC,OAAA,CAACL,IAAI;YAACwC,KAAK,EAAE;cAAEE,OAAO,EAAE,EAAE;cAAEY,KAAK,EAAE;YAAM;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnD7C,OAAA,CAACL,IAAI;YAACwC,KAAK,EAAE;cAAEE,OAAO,EAAE,EAAE;cAAEY,KAAK,EAAE,KAAK;cAAET,UAAU,EAAE,MAAM;cAAE/B,KAAK,EAAE,SAAS;cAAED,QAAQ,EAAE;YAAG,CAAE;YAAA+B,QAAA,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrH7C,OAAA,CAACL,IAAI;YAACwC,KAAK,EAAE;cAAEE,OAAO,EAAE,EAAE;cAAEY,KAAK,EAAE,KAAK;cAAET,UAAU,EAAE,MAAM;cAAEzB,SAAS,EAAE,QAAQ;cAAEN,KAAK,EAAE,SAAS;cAAED,QAAQ,EAAE;YAAG,CAAE;YAAA+B,QAAA,EAAEpB,UAAU,CAACqC;UAAc;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnJ,CAAC,eAEP7C,OAAA,CAACN,IAAI;UAACyC,KAAK,EAAE;YAAEzB,aAAa,EAAE,KAAK;YAAE0B,eAAe,EAAE,SAAS;YAAEkB,iBAAiB,EAAE,CAAC;YAAEC,iBAAiB,EAAE,SAAS;YAAE3C,UAAU,EAAE;UAAS,CAAE;UAAA2B,QAAA,gBAC1IvC,OAAA,CAACN,IAAI;YAACyC,KAAK,EAAE;cAAEE,OAAO,EAAE,EAAE;cAAEY,KAAK,EAAE,KAAK;cAAErC,UAAU,EAAE,QAAQ;cAAED,cAAc,EAAE;YAAS,CAAE;YAAA4B,QAAA,eACzFvC,OAAA,CAACN,IAAI;cAACyC,KAAK,EAAE;gBAAEsB,QAAQ,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBAAEtB,eAAe,EAAE,SAAS;gBAAEW,YAAY,EAAE,EAAE;gBAAEnC,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAS,CAAE;cAAA4B,QAAA,eACzIvC,OAAA,CAACL,IAAI;gBAACwC,KAAK,EAAE;kBAAE1B,KAAK,EAAE,MAAM;kBAAE+B,UAAU,EAAE,MAAM;kBAAEhC,QAAQ,EAAE;gBAAG,CAAE;gBAAA+B,QAAA,EAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACP7C,OAAA,CAACL,IAAI;YAACwC,KAAK,EAAE;cAAEE,OAAO,EAAE,EAAE;cAAEY,KAAK,EAAE,KAAK;cAAET,UAAU,EAAE,KAAK;cAAE/B,KAAK,EAAE,SAAS;cAAED,QAAQ,EAAE;YAAG,CAAE;YAAA+B,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9G7C,OAAA,CAACL,IAAI;YAACwC,KAAK,EAAE;cAAEE,OAAO,EAAE,EAAE;cAAEY,KAAK,EAAE,KAAK;cAAET,UAAU,EAAE,MAAM;cAAEzB,SAAS,EAAE,QAAQ;cAAEP,QAAQ,EAAE;YAAG,CAAE;YAAA+B,QAAA,EAAE,EAAAf,qBAAA,GAAAL,UAAU,CAACwC,6BAA6B,cAAAnC,qBAAA,wBAAAC,sBAAA,GAAxCD,qBAAA,CAA0CoC,QAAQ,cAAAnC,sBAAA,uBAAlDA,sBAAA,CAAoDoC,KAAK,KAAI;UAAC;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtK,CAAC,eAEP7C,OAAA,CAACN,IAAI;UAACyC,KAAK,EAAE;YAAEzB,aAAa,EAAE,KAAK;YAAE0B,eAAe,EAAE,SAAS;YAAEkB,iBAAiB,EAAE,CAAC;YAAEC,iBAAiB,EAAE,SAAS;YAAE3C,UAAU,EAAE;UAAS,CAAE;UAAA2B,QAAA,gBAC1IvC,OAAA,CAACN,IAAI;YAACyC,KAAK,EAAE;cAAEE,OAAO,EAAE,EAAE;cAAEY,KAAK,EAAE,KAAK;cAAErC,UAAU,EAAE,QAAQ;cAAED,cAAc,EAAE;YAAS,CAAE;YAAA4B,QAAA,eACzFvC,OAAA,CAACN,IAAI;cAACyC,KAAK,EAAE;gBAAEsB,QAAQ,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBAAEtB,eAAe,EAAE,SAAS;gBAAEW,YAAY,EAAE,EAAE;gBAAEnC,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAS,CAAE;cAAA4B,QAAA,eACzIvC,OAAA,CAACL,IAAI;gBAACwC,KAAK,EAAE;kBAAE1B,KAAK,EAAE,MAAM;kBAAE+B,UAAU,EAAE,MAAM;kBAAEhC,QAAQ,EAAE;gBAAG,CAAE;gBAAA+B,QAAA,EAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACP7C,OAAA,CAACL,IAAI;YAACwC,KAAK,EAAE;cAAEE,OAAO,EAAE,EAAE;cAAEY,KAAK,EAAE,KAAK;cAAET,UAAU,EAAE,KAAK;cAAE/B,KAAK,EAAE,SAAS;cAAED,QAAQ,EAAE;YAAG,CAAE;YAAA+B,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1G7C,OAAA,CAACL,IAAI;YAACwC,KAAK,EAAE;cAAEE,OAAO,EAAE,EAAE;cAAEY,KAAK,EAAE,KAAK;cAAET,UAAU,EAAE,MAAM;cAAEzB,SAAS,EAAE,QAAQ;cAAEP,QAAQ,EAAE;YAAG,CAAE;YAAA+B,QAAA,EAAE,EAAAb,sBAAA,GAAAP,UAAU,CAACwC,6BAA6B,cAAAjC,sBAAA,wBAAAC,sBAAA,GAAxCD,sBAAA,CAA0CoC,IAAI,cAAAnC,sBAAA,uBAA9CA,sBAAA,CAAgDkC,KAAK,KAAI;UAAC;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClK,CAAC,eAEP7C,OAAA,CAACN,IAAI;UAACyC,KAAK,EAAE;YAAEzB,aAAa,EAAE,KAAK;YAAE0B,eAAe,EAAE,SAAS;YAAEkB,iBAAiB,EAAE,CAAC;YAAEC,iBAAiB,EAAE,SAAS;YAAE3C,UAAU,EAAE;UAAS,CAAE;UAAA2B,QAAA,gBAC1IvC,OAAA,CAACN,IAAI;YAACyC,KAAK,EAAE;cAAEE,OAAO,EAAE,EAAE;cAAEY,KAAK,EAAE,KAAK;cAAErC,UAAU,EAAE,QAAQ;cAAED,cAAc,EAAE;YAAS,CAAE;YAAA4B,QAAA,eACzFvC,OAAA,CAACN,IAAI;cAACyC,KAAK,EAAE;gBAAEsB,QAAQ,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBAAEtB,eAAe,EAAE,SAAS;gBAAEW,YAAY,EAAE,EAAE;gBAAEnC,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAS,CAAE;cAAA4B,QAAA,eACzIvC,OAAA,CAACL,IAAI;gBAACwC,KAAK,EAAE;kBAAE1B,KAAK,EAAE,MAAM;kBAAE+B,UAAU,EAAE,MAAM;kBAAEhC,QAAQ,EAAE;gBAAG,CAAE;gBAAA+B,QAAA,EAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACP7C,OAAA,CAACL,IAAI;YAACwC,KAAK,EAAE;cAAEE,OAAO,EAAE,EAAE;cAAEY,KAAK,EAAE,KAAK;cAAET,UAAU,EAAE,KAAK;cAAE/B,KAAK,EAAE,SAAS;cAAED,QAAQ,EAAE;YAAG,CAAE;YAAA+B,QAAA,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5G7C,OAAA,CAACL,IAAI;YAACwC,KAAK,EAAE;cAAEE,OAAO,EAAE,EAAE;cAAEY,KAAK,EAAE,KAAK;cAAET,UAAU,EAAE,MAAM;cAAEzB,SAAS,EAAE,QAAQ;cAAEP,QAAQ,EAAE;YAAG,CAAE;YAAA+B,QAAA,EAAE,EAAAX,sBAAA,GAAAT,UAAU,CAACwC,6BAA6B,cAAA/B,sBAAA,wBAAAC,sBAAA,GAAxCD,sBAAA,CAA0CmC,MAAM,cAAAlC,sBAAA,uBAAhDA,sBAAA,CAAkDgC,KAAK,KAAI;UAAC;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpK,CAAC,eAEP7C,OAAA,CAACN,IAAI;UAACyC,KAAK,EAAE;YAAEzB,aAAa,EAAE,KAAK;YAAE0B,eAAe,EAAE,SAAS;YAAExB,UAAU,EAAE;UAAS,CAAE;UAAA2B,QAAA,gBACtFvC,OAAA,CAACN,IAAI;YAACyC,KAAK,EAAE;cAAEE,OAAO,EAAE,EAAE;cAAEY,KAAK,EAAE,KAAK;cAAErC,UAAU,EAAE,QAAQ;cAAED,cAAc,EAAE;YAAS,CAAE;YAAA4B,QAAA,eACzFvC,OAAA,CAACN,IAAI;cAACyC,KAAK,EAAE;gBAAEsB,QAAQ,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBAAEtB,eAAe,EAAE,SAAS;gBAAEW,YAAY,EAAE,EAAE;gBAAEnC,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAS,CAAE;cAAA4B,QAAA,eACzIvC,OAAA,CAACL,IAAI;gBAACwC,KAAK,EAAE;kBAAE1B,KAAK,EAAE,MAAM;kBAAE+B,UAAU,EAAE,MAAM;kBAAEhC,QAAQ,EAAE;gBAAG,CAAE;gBAAA+B,QAAA,EAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACP7C,OAAA,CAACL,IAAI;YAACwC,KAAK,EAAE;cAAEE,OAAO,EAAE,EAAE;cAAEY,KAAK,EAAE,KAAK;cAAET,UAAU,EAAE,KAAK;cAAE/B,KAAK,EAAE,SAAS;cAAED,QAAQ,EAAE;YAAG,CAAE;YAAA+B,QAAA,EAAC;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzG7C,OAAA,CAACL,IAAI;YAACwC,KAAK,EAAE;cAAEE,OAAO,EAAE,EAAE;cAAEY,KAAK,EAAE,KAAK;cAAET,UAAU,EAAE,MAAM;cAAEzB,SAAS,EAAE,QAAQ;cAAEP,QAAQ,EAAE;YAAG,CAAE;YAAA+B,QAAA,EAAE,EAAAT,sBAAA,GAAAX,UAAU,CAACwC,6BAA6B,cAAA7B,sBAAA,wBAAAC,sBAAA,GAAxCD,sBAAA,CAA0CkC,GAAG,cAAAjC,sBAAA,uBAA7CA,sBAAA,CAA+C8B,KAAK,KAAI;UAAC;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP7C,OAAA,CAACL,IAAI;QAACwC,KAAK,EAAE;UAAE3B,QAAQ,EAAE,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEM,SAAS,EAAE,QAAQ;UAAE0B,YAAY,EAAE;QAAG,CAAE;QAAAF,QAAA,EAAC;MAA4B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC3H7C,OAAA,CAACL,IAAI;QAACwC,KAAK,EAAE;UAAE3B,QAAQ,EAAE,EAAE;UAAEiC,YAAY,EAAE,CAAC;UAAEhC,KAAK,EAAE;QAAU,CAAE;QAAA8B,QAAA,EAAC;MAElE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEP7C,OAAA,CAACN,IAAI;QAACyC,KAAK,EAAE;UAAEzB,aAAa,EAAE,KAAK;UAAEC,cAAc,EAAE,QAAQ;UAAE8B,YAAY,EAAE;QAAG,CAAE;QAAAF,QAAA,eAChFvC,OAAA,CAACN,IAAI;UAACyC,KAAK,EAAE;YAAEc,KAAK,EAAE,KAAK;YAAEgB,WAAW,EAAE,CAAC;YAAEC,WAAW,EAAE,SAAS;YAAEnB,YAAY,EAAE,CAAC;YAAEV,OAAO,EAAE,EAAE;YAAED,eAAe,EAAE,SAAS;YAAExB,UAAU,EAAE;UAAS,CAAE;UAAA2B,QAAA,gBACpJvC,OAAA,CAACL,IAAI;YAACwC,KAAK,EAAE;cAAE3B,QAAQ,EAAE,EAAE;cAAEgC,UAAU,EAAE,MAAM;cAAEzB,SAAS,EAAE,QAAQ;cAAE0B,YAAY,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEzF;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACNzB,aAAa,gBACZpB,OAAA,CAACJ,KAAK;YAACuE,GAAG,EAAE/C,aAAc;YAACe,KAAK,EAAE;cAAEc,KAAK,EAAE,GAAG;cAAEmB,MAAM,EAAE,GAAG;cAAEC,SAAS,EAAE,SAAS;cAAE5B,YAAY,EAAE;YAAE;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAExG7C,OAAA,CAACN,IAAI;YAACyC,KAAK,EAAE;cAAEc,KAAK,EAAE,GAAG;cAAEmB,MAAM,EAAE,GAAG;cAAEhC,eAAe,EAAE,SAAS;cAAExB,UAAU,EAAE,QAAQ;cAAED,cAAc,EAAE,QAAQ;cAAEoC,YAAY,EAAE;YAAG,CAAE;YAAAR,QAAA,eACrIvC,OAAA,CAACL,IAAI;cAACwC,KAAK,EAAE;gBAAE1B,KAAK,EAAE,SAAS;gBAAED,QAAQ,EAAE;cAAG,CAAE;cAAA+B,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAgBP7C,OAAA,CAACL,IAAI;QAACwC,KAAK,EAAE;UAAE3B,QAAQ,EAAE,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEM,SAAS,EAAE,QAAQ;UAAEuD,SAAS,EAAE;QAAE,CAAE;QAAA/B,QAAA,EAAC;MAEpF;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACP7C,OAAA,CAACL,IAAI;QAACwC,KAAK,EAAE;UAAEoC,OAAO,EAAE;QAAO,CAAE;QAACC,MAAM,EAAEA,CAAC;UAAEC;QAAW,CAAC,KAAK,EAAG;QAACC,KAAK;MAAA;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC,eACP7C,OAAA,CAACN,IAAI;MAACyC,KAAK,EAAElC,MAAM,CAACE,MAAO;MAACuE,KAAK;MAAAnC,QAAA,gBAC/BvC,OAAA,CAACL,IAAI;QAACwC,KAAK,EAAElC,MAAM,CAACa,UAAW;QAAAyB,QAAA,EAAEpB,UAAU,CAACwD,eAAe,IAAI;MAAiB;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACxF7C,OAAA,CAACL,IAAI;QAACwC,KAAK,EAAElC,MAAM,CAACgB,WAAY;QAACuD,MAAM,EAAEA,CAAC;UAAEC,UAAU;UAAEG;QAAW,CAAC,KAAM,GAAEH,UAAW,MAAKG,UAAW;MAAE;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEX,CAAC;AAACtB,EAAA,CA5HIL,qBAA2D;EAAA,QACjCpB,eAAe;AAAA;AAAA+E,EAAA,GADzC3D,qBAA2D;AA8HjE,eAAeA,qBAAqB;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}