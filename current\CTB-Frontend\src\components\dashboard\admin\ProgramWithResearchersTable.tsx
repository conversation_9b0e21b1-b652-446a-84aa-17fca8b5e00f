import React, { useMemo } from "react";
import { FaUsers, FaExclamationTriangle } from "react-icons/fa";
import { useNavigate } from "react-router-dom";

type Researcher = {
  userId: number;
  username: string;
};

type SeverityCount = {
  severity: string;
  count: number;
};

type ProgramWithSeverityCounts = {
  programId: number;
  programTitle: string;
  researchers: Researcher[];
  severityCounts: SeverityCount[];
  totalReportsForProgram: number;
};

type ProgramsWithResearchersTableProps = {
  programsWithSeverityCounts: ProgramWithSeverityCounts[];
};

const ProgramsWithResearchersTable: React.FC<
  ProgramsWithResearchersTableProps
> = ({ programsWithSeverityCounts }) => {
  // Hook for navigation
  const navigate = useNavigate();

  // Sort programs in descending order by programId
  const sortedPrograms = useMemo(
    () =>
      [...programsWithSeverityCounts].sort((a, b) => b.programId - a.programId),
    [programsWithSeverityCounts]
  );

  return (
    <div className="w-full overflow-hidden rounded-xl border border-gray-100 bg-white shadow-lg">
      <div className="sticky top-0 z-10 flex items-center justify-between gap-28 bg-blue-700 p-6">
        <div className="flex items-center space-x-4">
          <FaUsers className="h-6 w-6 text-white" />
          <h2 className="text-lg font-bold text-white">PTaaS programs</h2>
        </div>
        <div className="text-sm font-medium text-white">
          Total Programs: {sortedPrograms.length}
        </div>
      </div>

      <div
        className="scrollbar-thin scrollbar-track-gray-100 scrollbar-thumb-gray-300 overflow-y-auto"
        style={{
          maxHeight: "calc(4 * 85px + 20px)",
          scrollbarWidth: "thin",
          scrollbarColor: "#D1D5DB #F3F4F6"
        }}
      >
        <table className="w-full">
          <thead className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50">
            <tr>
              <th className="px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider text-gray-600">
                Program Title
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider text-gray-600">
                Pentesters
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider text-gray-600">
                Severity Counts
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider text-gray-600">
                Total Reports
              </th>
            </tr>
          </thead>
          <tbody>
            {sortedPrograms.length === 0 ? (
              <tr>
                <td colSpan={4} className="py-8 text-center text-gray-500">
                  <div className="flex flex-col items-center space-y-4">
                    <FaUsers className="h-12 w-12 text-gray-300" />
                    <p className="text-lg">No research programs found</p>
                  </div>
                </td>
              </tr>
            ) : (
              sortedPrograms.map(program => (
                <tr
                  key={program.programId}
                  onClick={() =>
                    navigate(`/dashboard/programs/${program.programId}`)
                  }
                  className="group cursor-pointer border-b border-gray-100 transition-colors duration-200 last:border-b-0 hover:bg-blue-50"
                >
                  <td className="px-6 py-4">
                    <div className="text-sm font-bold text-gray-900 transition-colors group-hover:text-blue-600">
                      {program.programTitle}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2">
                      <FaUsers className="h-4 w-4 text-blue-500" />
                      <span className="text-sm font-semibold text-gray-700">
                        {program.researchers.length} Pentester
                        {program.researchers.length !== 1 ? "s" : ""}
                      </span>
                    </div>
                    <ul className="mt-1 space-y-1 text-xs font-medium text-gray-500">
                      {program.researchers.slice(0, 3).map(researcher => (
                        <li key={researcher.userId}>@{researcher.username}</li>
                      ))}
                      {program.researchers.length > 3 && (
                        <li className="text-gray-400">
                          + {program.researchers.length - 3} more
                        </li>
                      )}
                    </ul>
                  </td>
                  <td className="px-6 py-4">
                    <div className="grid grid-cols-2 gap-2 text-xs font-medium lowercase text-gray-700">
                      {program.severityCounts
                        .filter((severityCount) => severityCount.count > 0)  
                        .map((severityCount) => (
                          <div
                            key={severityCount.severity}
                            className="flex items-center space-x-2"
                          >
                            <FaExclamationTriangle
                              className={`h-4 w-4 ${severityCount.severity === "CRITICAL"
                                  ? "text-red-500"
                                  : severityCount.severity === "HIGH"
                                    ? "text-orange-500"
                                    : severityCount.severity === "MEDIUM"
                                      ? "text-yellow-500"
                                      : "text-green-500"
                                }`}
                            />
                            <span>
                              {severityCount.severity}: {severityCount.count}
                            </span>
                          </div>
                        ))}
                    </div>
                  </td>

                  <td className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-900">
                      {program.totalReportsForProgram}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ProgramsWithResearchersTable;
