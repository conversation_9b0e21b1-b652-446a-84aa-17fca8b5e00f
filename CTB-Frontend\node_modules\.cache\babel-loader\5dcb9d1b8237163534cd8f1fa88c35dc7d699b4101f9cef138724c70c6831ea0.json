{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\jira-old\\\\CTB-Frontend\\\\src\\\\pages\\\\automated-report\\\\components\\\\sections\\\\ExecutiveSummaryPage.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Page, View, Text, Image, StyleSheet } from '@react-pdf/renderer';\nimport { useSectionPages } from '../SectionPageContext';\n// Use the same severity color codes as FullTableOfContentsPage\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst severityColors = {\n  Critical: '#800000',\n  // Maroon\n  High: '#dc2626',\n  // Red\n  Medium: '#fbbf24',\n  // Yellow\n  Low: '#10b981',\n  // Green\n  Informational: '#3b82f6' // Blue or keep as is\n};\n\n// Utility to strip HTML tags and preserve paragraph breaks\nfunction htmlToParagraphs(html) {\n  if (!html) return [];\n  // Replace <br> and <br/> with \\n\n  let withBreaks = html.replace(/<br\\s*\\/?>/gi, '\\n');\n  // Replace <p> and <div> with \\n (block-level)\n  withBreaks = withBreaks.replace(/<\\/?(p|div)[^>]*>/gi, '\\n');\n  // Remove all other tags\n  const tmp = document.createElement('div');\n  tmp.innerHTML = withBreaks;\n  const text = tmp.textContent || tmp.innerText || '';\n  // Split by double newlines or single newlines, filter empty\n  return text.split(/\\n+/).map(s => s.trim()).filter(Boolean);\n}\n\n// Helper function to get testing type description\nconst getTestingTypeDescription = testingType => {\n  if (!testingType) {\n    return 'comprehensive Vulnerability Assessment and Penetration Test (VAPT)';\n  }\n  const type = testingType.toLowerCase();\n  if (type.includes('web') && type.includes('mobile')) {\n    return 'comprehensive Web Application and Mobile Application Security Assessment';\n  } else if (type.includes('web')) {\n    return 'comprehensive Web Application Security Assessment';\n  } else if (type.includes('mobile')) {\n    return 'comprehensive Mobile Application Security Assessment';\n  } else if (type.includes('network')) {\n    return 'comprehensive Network Security Assessment';\n  } else if (type.includes('api')) {\n    return 'comprehensive API Security Assessment';\n  } else if (type.includes('cloud')) {\n    return 'comprehensive Cloud Security Assessment';\n  } else if (type.includes('infrastructure')) {\n    return 'comprehensive Infrastructure Security Assessment';\n  } else {\n    // For any other testing type, use it directly with \"Assessment\"\n    return `comprehensive ${testingType} Assessment`;\n  }\n};\nconst DEFAULT_COMPANY = 'Capture The Bug';\nconst styles = StyleSheet.create({\n  footer: {\n    position: 'absolute',\n    bottom: 30,\n    left: 0,\n    right: 0,\n    fontSize: 10,\n    color: 'grey',\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    paddingHorizontal: 24\n  },\n  footerLeft: {\n    textAlign: 'left',\n    flex: 1\n  },\n  footerRight: {\n    textAlign: 'right',\n    flex: 1\n  }\n});\nconst ExecutiveSummaryPage = ({\n  reportData,\n  pieChartImage,\n  barChartImage,\n  sectionId\n}) => {\n  _s();\n  const {\n    updateSectionPage\n  } = useSectionPages();\n  const summaryParagraphs = reportData.executive_summary ? htmlToParagraphs(reportData.executive_summary) : null;\n\n  // Get testing type from the first program\n  const testingType = reportData.program_details && reportData.program_details.length > 0 ? reportData.program_details[0].testing_type : undefined;\n  const testingDescription = getTestingTypeDescription(testingType);\n  return /*#__PURE__*/_jsxDEV(Page, {\n    size: \"A4\",\n    id: sectionId,\n    style: {\n      flexDirection: 'column',\n      backgroundColor: '#ffffff',\n      padding: '20mm 15mm',\n      fontFamily: 'Helvetica',\n      fontSize: 12\n    },\n    children: [/*#__PURE__*/_jsxDEV(View, {\n      style: {\n        paddingHorizontal: 24,\n        flexDirection: 'column',\n        flex: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(View, {\n        style: {\n          flex: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            fontSize: 16,\n            fontWeight: 'bold',\n            color: '#2563eb',\n            marginBottom: 16,\n            lineHeight: 1.4\n          },\n          children: \"EXECUTIVE SUMMARY\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), summaryParagraphs ? summaryParagraphs.map((para, idx) => /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            fontSize: 12,\n            lineHeight: 1.4,\n            marginBottom: 14,\n            color: '#374151',\n            textAlign: 'justify'\n          },\n          children: para\n        }, idx, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 15\n        }, this)) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              fontSize: 12,\n              lineHeight: 1.4,\n              marginBottom: 14,\n              color: '#374151',\n              textAlign: 'justify'\n            },\n            children: [reportData.branding_company || DEFAULT_COMPANY, \" is a modern Penetration Testing as a Service (PTaaS) platform designed to support fast-moving engineering teams and security-conscious enterprises. Organizations across New Zealand, Australia, and North America-including public companies and high-growth SaaS teams-trust \", reportData.branding_company || DEFAULT_COMPANY, \" to secure their CI/CD pipelines, meet compliance goals, and reduce time-to-remediation without slowing development velocity. \", reportData.branding_company || DEFAULT_COMPANY, \" is HQ in New Zealand and proudly works with customers across regulated and innovation-driven sectors.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              fontSize: 12,\n              lineHeight: 1.4,\n              marginBottom: 18,\n              color: '#374151',\n              textAlign: 'justify'\n            },\n            children: [reportData.company_name, \" entrusted \", reportData.branding_company || DEFAULT_COMPANY, \" to conduct a  Vulnerability Assessment and Penetration Test (VAPT). This assessment evaluated the application's security posture from a \", testingType || 'undefined', \" perspective to focus on its resilience against common attack patterns and identify vulnerabilities in its internal and external interfaces.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(View, {\n          style: {\n            width: '100%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            marginTop: 32\n          },\n          children: /*#__PURE__*/_jsxDEV(View, {\n            style: {\n              width: 420,\n              borderWidth: 1,\n              borderColor: '#e5e7eb',\n              borderRadius: 8,\n              padding: 20,\n              backgroundColor: '#f9fafb',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                fontSize: 12,\n                fontWeight: 'bold',\n                textAlign: 'center',\n                marginBottom: 12,\n                lineHeight: 1.4\n              },\n              children: \"FINDINGS BY SEVERITY\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this), barChartImage ? /*#__PURE__*/_jsxDEV(Image, {\n              src: barChartImage,\n              style: {\n                width: 380,\n                height: 240,\n                objectFit: 'contain',\n                marginBottom: 8\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(View, {\n              style: {\n                width: 380,\n                height: 240,\n                backgroundColor: '#e5e7eb',\n                alignItems: 'center',\n                justifyContent: 'center',\n                borderRadius: 12\n              },\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                style: {\n                  color: '#64748b',\n                  fontSize: 12,\n                  lineHeight: 1.4\n                },\n                children: \"No Chart\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(View, {\n      style: styles.footer,\n      fixed: true,\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        style: styles.footerLeft,\n        children: reportData.document_number || 'Document Number'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        style: styles.footerRight,\n        render: ({\n          pageNumber,\n          totalPages\n        }) => `${pageNumber} / ${totalPages}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      style: {\n        display: 'none'\n      },\n      render: ({\n        pageNumber\n      }) => {\n        updateSectionPage('ExecutiveSummary', pageNumber);\n        return '';\n      },\n      fixed: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_s(ExecutiveSummaryPage, \"3rf3HaIqG9R7of1rm8wX9C3kpH0=\", false, function () {\n  return [useSectionPages];\n});\n_c = ExecutiveSummaryPage;\nexport default ExecutiveSummaryPage;\nvar _c;\n$RefreshReg$(_c, \"ExecutiveSummaryPage\");", "map": {"version": 3, "names": ["React", "Page", "View", "Text", "Image", "StyleSheet", "useSectionPages", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "severityColors", "Critical", "High", "Medium", "Low", "Informational", "htmlToParagraphs", "html", "withBreaks", "replace", "tmp", "document", "createElement", "innerHTML", "text", "textContent", "innerText", "split", "map", "s", "trim", "filter", "Boolean", "getTestingTypeDescription", "testingType", "type", "toLowerCase", "includes", "DEFAULT_COMPANY", "styles", "create", "footer", "position", "bottom", "left", "right", "fontSize", "color", "flexDirection", "justifyContent", "alignItems", "paddingHorizontal", "footerLeft", "textAlign", "flex", "footerRight", "ExecutiveSummaryPage", "reportData", "pieChartImage", "barChartImage", "sectionId", "_s", "updateSectionPage", "summaryParagraphs", "executive_summary", "program_details", "length", "testing_type", "undefined", "testingDescription", "size", "id", "style", "backgroundColor", "padding", "fontFamily", "children", "fontWeight", "marginBottom", "lineHeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "para", "idx", "branding_company", "company_name", "width", "display", "marginTop", "borderWidth", "borderColor", "borderRadius", "src", "height", "objectFit", "fixed", "document_number", "render", "pageNumber", "totalPages", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/jira-old/CTB-Frontend/src/pages/automated-report/components/sections/ExecutiveSummaryPage.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Page, View, Text, Image, StyleSheet } from '@react-pdf/renderer';\r\nimport { ReportData } from '../../types/report.types';\r\nimport { useSectionPages } from '../SectionPageContext';\r\n// Use the same severity color codes as FullTableOfContentsPage\r\nconst severityColors: Record<string, string> = {\r\n  Critical: '#800000', // Maroon\r\n  High: '#dc2626',     // Red\r\n  Medium: '#fbbf24',   // Yellow\r\n  Low: '#10b981',      // Green\r\n  Informational: '#3b82f6', // Blue or keep as is\r\n};\r\n\r\n// Utility to strip HTML tags and preserve paragraph breaks\r\nfunction htmlToParagraphs(html: string): string[] {\r\n  if (!html) return [];\r\n  // Replace <br> and <br/> with \\n\r\n  let withBreaks = html.replace(/<br\\s*\\/?>/gi, '\\n');\r\n  // Replace <p> and <div> with \\n (block-level)\r\n  withBreaks = withBreaks.replace(/<\\/?(p|div)[^>]*>/gi, '\\n');\r\n  // Remove all other tags\r\n  const tmp = document.createElement('div');\r\n  tmp.innerHTML = withBreaks;\r\n  const text = tmp.textContent || tmp.innerText || '';\r\n  // Split by double newlines or single newlines, filter empty\r\n  return text.split(/\\n+/).map(s => s.trim()).filter(Boolean);\r\n}\r\n\r\n// Helper function to get testing type description\r\nconst getTestingTypeDescription = (testingType?: string): string => {\r\n  if (!testingType) {\r\n    return 'comprehensive Vulnerability Assessment and Penetration Test (VAPT)';\r\n  }\r\n\r\n  const type = testingType.toLowerCase();\r\n\r\n  if (type.includes('web') && type.includes('mobile')) {\r\n    return 'comprehensive Web Application and Mobile Application Security Assessment';\r\n  } else if (type.includes('web')) {\r\n    return 'comprehensive Web Application Security Assessment';\r\n  } else if (type.includes('mobile')) {\r\n    return 'comprehensive Mobile Application Security Assessment';\r\n  } else if (type.includes('network')) {\r\n    return 'comprehensive Network Security Assessment';\r\n  } else if (type.includes('api')) {\r\n    return 'comprehensive API Security Assessment';\r\n  } else if (type.includes('cloud')) {\r\n    return 'comprehensive Cloud Security Assessment';\r\n  } else if (type.includes('infrastructure')) {\r\n    return 'comprehensive Infrastructure Security Assessment';\r\n  } else {\r\n    // For any other testing type, use it directly with \"Assessment\"\r\n    return `comprehensive ${testingType} Assessment`;\r\n  }\r\n};\r\n\r\nconst DEFAULT_COMPANY = 'Capture The Bug';\r\n\r\ninterface ExecutiveSummaryPageProps {\r\n  reportData: ReportData;\r\n  pieChartImage?: string;\r\n  barChartImage?: string;\r\n  sectionId?: string;\r\n}\r\n\r\nconst styles = StyleSheet.create({\r\n  footer: {\r\n    position: 'absolute',\r\n    bottom: 30,\r\n    left: 0,\r\n    right: 0,\r\n    fontSize: 10,\r\n    color: 'grey',\r\n    flexDirection: 'row',\r\n    justifyContent: 'space-between',\r\n    alignItems: 'center',\r\n    paddingHorizontal: 24,\r\n  },\r\n  footerLeft: {\r\n    textAlign: 'left',\r\n    flex: 1,\r\n  },\r\n  footerRight: {\r\n    textAlign: 'right',\r\n    flex: 1,\r\n  },\r\n});\r\n\r\nconst ExecutiveSummaryPage: React.FC<ExecutiveSummaryPageProps> = ({ reportData, pieChartImage, barChartImage, sectionId }) => {\r\n  const { updateSectionPage } = useSectionPages();\r\n  const summaryParagraphs = reportData.executive_summary ? htmlToParagraphs(reportData.executive_summary) : null;\r\n\r\n  // Get testing type from the first program\r\n  const testingType = reportData.program_details && reportData.program_details.length > 0\r\n    ? reportData.program_details[0].testing_type\r\n    : undefined;\r\n  const testingDescription = getTestingTypeDescription(testingType);\r\n  return (\r\n    <Page size=\"A4\" id={sectionId} style={{\r\n      flexDirection: 'column',\r\n      backgroundColor: '#ffffff',\r\n      padding: '20mm 15mm',\r\n      fontFamily: 'Helvetica',\r\n      fontSize: 12,\r\n    }}>\r\n      <View style={{ paddingHorizontal: 24, flexDirection: 'column', flex: 1 }}>\r\n        <View style={{ flex: 1 }}>\r\n          <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#2563eb', marginBottom: 16, lineHeight: 1.4 }}>\r\n            EXECUTIVE SUMMARY\r\n          </Text>\r\n          {summaryParagraphs ? (\r\n            summaryParagraphs.map((para, idx) => (\r\n              <Text key={idx} style={{ fontSize: 12, lineHeight: 1.4, marginBottom: 14, color: '#374151', textAlign: 'justify' }}>\r\n                {para}\r\n              </Text>\r\n            ))\r\n          ) : (\r\n            <>\r\n              <Text style={{ fontSize: 12, lineHeight: 1.4, marginBottom: 14, color: '#374151', textAlign: 'justify' }}>\r\n                {(reportData.branding_company || DEFAULT_COMPANY)} is a modern Penetration Testing as a Service (PTaaS) platform designed to support fast-moving engineering teams and security-conscious enterprises. Organizations across New Zealand, Australia, and North America-including public companies and high-growth SaaS teams-trust {(reportData.branding_company || DEFAULT_COMPANY)} to secure their CI/CD pipelines, meet compliance goals, and reduce time-to-remediation without slowing development velocity. {(reportData.branding_company || DEFAULT_COMPANY)} is HQ in New Zealand and proudly works with customers across regulated and innovation-driven sectors.\r\n              </Text>\r\n              <Text style={{ fontSize: 12, lineHeight: 1.4, marginBottom: 18, color: '#374151', textAlign: 'justify' }}>\r\n                {reportData.company_name} entrusted {(reportData.branding_company || DEFAULT_COMPANY)} to conduct a  Vulnerability\r\n                Assessment and Penetration Test (VAPT). This assessment evaluated the application's security posture from a {testingType || 'undefined'} perspective to focus on its resilience against common attack patterns and identify vulnerabilities in its internal and external interfaces.\r\n              </Text>\r\n            </>\r\n          )}\r\n          {/*\r\n            Pie Chart is commented out as per request\r\n            <View style={{ width: '48%', borderWidth: 1, borderColor: '#e5e7eb', borderRadius: 8, padding: 20, backgroundColor: '#f9fafb', alignItems: 'center' }}>\r\n              <Text style={{ fontSize: 12, fontWeight: 'bold', textAlign: 'center', marginBottom: 12 }}>\r\n                VULNERABILITY DISTRIBUTION\r\n              </Text>\r\n              {pieChartImage ? (\r\n                <Image src={pieChartImage} style={{ width: 180, height: 180, objectFit: 'contain', marginBottom: 8 }} />\r\n              ) : (\r\n                <View style={{ width: 180, height: 180, backgroundColor: '#e5e7eb', alignItems: 'center', justifyContent: 'center', borderRadius: 90 }}>\r\n                  <Text style={{ color: '#64748b', fontSize: 12 }}>No Chart</Text>\r\n                </View>\r\n              )}\r\n            </View>\r\n          */}\r\n          {/* Bar Chart - Centered and wider */}\r\n          <View style={{ width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center', marginTop: 32 }}>\r\n            <View style={{ width: 420, borderWidth: 1, borderColor: '#e5e7eb', borderRadius: 8, padding: 20, backgroundColor: '#f9fafb', alignItems: 'center', justifyContent: 'center' }}>\r\n              <Text style={{ fontSize: 12, fontWeight: 'bold', textAlign: 'center', marginBottom: 12, lineHeight: 1.4 }}>\r\n                FINDINGS BY SEVERITY\r\n              </Text>\r\n              {barChartImage ? (\r\n                <Image src={barChartImage} style={{ width: 380, height: 240, objectFit: 'contain', marginBottom: 8 }} />\r\n              ) : (\r\n                <View style={{ width: 380, height: 240, backgroundColor: '#e5e7eb', alignItems: 'center', justifyContent: 'center', borderRadius: 12 }}>\r\n                  <Text style={{ color: '#64748b', fontSize: 12, lineHeight: 1.4 }}>No Chart</Text>\r\n                </View>\r\n              )}\r\n              {/*\r\n                If you render the bar chart here manually, use severityColors for each bar:\r\n                Example: <Rect fill={severityColors[severity]} ... />\r\n              */}\r\n            </View>\r\n          </View>\r\n        </View>\r\n      </View>\r\n      <View style={styles.footer} fixed>\r\n        <Text style={styles.footerLeft}>{reportData.document_number || 'Document Number'}</Text>\r\n        <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />\r\n      </View>\r\n      <Text style={{ display: 'none' }} render={({ pageNumber }) => { updateSectionPage('ExecutiveSummary', pageNumber); return ''; }} fixed />\r\n    </Page>\r\n  );\r\n};\r\n\r\nexport default ExecutiveSummaryPage; "], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,QAAQ,qBAAqB;AAEzE,SAASC,eAAe,QAAQ,uBAAuB;AACvD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAsC,GAAG;EAC7CC,QAAQ,EAAE,SAAS;EAAE;EACrBC,IAAI,EAAE,SAAS;EAAM;EACrBC,MAAM,EAAE,SAAS;EAAI;EACrBC,GAAG,EAAE,SAAS;EAAO;EACrBC,aAAa,EAAE,SAAS,CAAE;AAC5B,CAAC;;AAED;AACA,SAASC,gBAAgBA,CAACC,IAAY,EAAY;EAChD,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB;EACA,IAAIC,UAAU,GAAGD,IAAI,CAACE,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC;EACnD;EACAD,UAAU,GAAGA,UAAU,CAACC,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC;EAC5D;EACA,MAAMC,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EACzCF,GAAG,CAACG,SAAS,GAAGL,UAAU;EAC1B,MAAMM,IAAI,GAAGJ,GAAG,CAACK,WAAW,IAAIL,GAAG,CAACM,SAAS,IAAI,EAAE;EACnD;EACA,OAAOF,IAAI,CAACG,KAAK,CAAC,KAAK,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;AAC7D;;AAEA;AACA,MAAMC,yBAAyB,GAAIC,WAAoB,IAAa;EAClE,IAAI,CAACA,WAAW,EAAE;IAChB,OAAO,oEAAoE;EAC7E;EAEA,MAAMC,IAAI,GAAGD,WAAW,CAACE,WAAW,CAAC,CAAC;EAEtC,IAAID,IAAI,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,IAAI,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;IACnD,OAAO,0EAA0E;EACnF,CAAC,MAAM,IAAIF,IAAI,CAACE,QAAQ,CAAC,KAAK,CAAC,EAAE;IAC/B,OAAO,mDAAmD;EAC5D,CAAC,MAAM,IAAIF,IAAI,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;IAClC,OAAO,sDAAsD;EAC/D,CAAC,MAAM,IAAIF,IAAI,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;IACnC,OAAO,2CAA2C;EACpD,CAAC,MAAM,IAAIF,IAAI,CAACE,QAAQ,CAAC,KAAK,CAAC,EAAE;IAC/B,OAAO,uCAAuC;EAChD,CAAC,MAAM,IAAIF,IAAI,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;IACjC,OAAO,yCAAyC;EAClD,CAAC,MAAM,IAAIF,IAAI,CAACE,QAAQ,CAAC,gBAAgB,CAAC,EAAE;IAC1C,OAAO,kDAAkD;EAC3D,CAAC,MAAM;IACL;IACA,OAAQ,iBAAgBH,WAAY,aAAY;EAClD;AACF,CAAC;AAED,MAAMI,eAAe,GAAG,iBAAiB;AASzC,MAAMC,MAAM,GAAGnC,UAAU,CAACoC,MAAM,CAAC;EAC/BC,MAAM,EAAE;IACNC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,MAAM;IACbC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBC,iBAAiB,EAAE;EACrB,CAAC;EACDC,UAAU,EAAE;IACVC,SAAS,EAAE,MAAM;IACjBC,IAAI,EAAE;EACR,CAAC;EACDC,WAAW,EAAE;IACXF,SAAS,EAAE,OAAO;IAClBC,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AAEF,MAAME,oBAAyD,GAAGA,CAAC;EAAEC,UAAU;EAAEC,aAAa;EAAEC,aAAa;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC7H,MAAM;IAAEC;EAAkB,CAAC,GAAGzD,eAAe,CAAC,CAAC;EAC/C,MAAM0D,iBAAiB,GAAGN,UAAU,CAACO,iBAAiB,GAAGhD,gBAAgB,CAACyC,UAAU,CAACO,iBAAiB,CAAC,GAAG,IAAI;;EAE9G;EACA,MAAM9B,WAAW,GAAGuB,UAAU,CAACQ,eAAe,IAAIR,UAAU,CAACQ,eAAe,CAACC,MAAM,GAAG,CAAC,GACnFT,UAAU,CAACQ,eAAe,CAAC,CAAC,CAAC,CAACE,YAAY,GAC1CC,SAAS;EACb,MAAMC,kBAAkB,GAAGpC,yBAAyB,CAACC,WAAW,CAAC;EACjE,oBACE3B,OAAA,CAACP,IAAI;IAACsE,IAAI,EAAC,IAAI;IAACC,EAAE,EAAEX,SAAU;IAACY,KAAK,EAAE;MACpCxB,aAAa,EAAE,QAAQ;MACvByB,eAAe,EAAE,SAAS;MAC1BC,OAAO,EAAE,WAAW;MACpBC,UAAU,EAAE,WAAW;MACvB7B,QAAQ,EAAE;IACZ,CAAE;IAAA8B,QAAA,gBACArE,OAAA,CAACN,IAAI;MAACuE,KAAK,EAAE;QAAErB,iBAAiB,EAAE,EAAE;QAAEH,aAAa,EAAE,QAAQ;QAAEM,IAAI,EAAE;MAAE,CAAE;MAAAsB,QAAA,eACvErE,OAAA,CAACN,IAAI;QAACuE,KAAK,EAAE;UAAElB,IAAI,EAAE;QAAE,CAAE;QAAAsB,QAAA,gBACvBrE,OAAA,CAACL,IAAI;UAACsE,KAAK,EAAE;YAAE1B,QAAQ,EAAE,EAAE;YAAE+B,UAAU,EAAE,MAAM;YAAE9B,KAAK,EAAE,SAAS;YAAE+B,YAAY,EAAE,EAAE;YAAEC,UAAU,EAAE;UAAI,CAAE;UAAAH,QAAA,EAAC;QAExG;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACNpB,iBAAiB,GAChBA,iBAAiB,CAACnC,GAAG,CAAC,CAACwD,IAAI,EAAEC,GAAG,kBAC9B9E,OAAA,CAACL,IAAI;UAAWsE,KAAK,EAAE;YAAE1B,QAAQ,EAAE,EAAE;YAAEiC,UAAU,EAAE,GAAG;YAAED,YAAY,EAAE,EAAE;YAAE/B,KAAK,EAAE,SAAS;YAAEM,SAAS,EAAE;UAAU,CAAE;UAAAuB,QAAA,EAChHQ;QAAI,GADIC,GAAG;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAER,CACP,CAAC,gBAEF5E,OAAA,CAAAE,SAAA;UAAAmE,QAAA,gBACErE,OAAA,CAACL,IAAI;YAACsE,KAAK,EAAE;cAAE1B,QAAQ,EAAE,EAAE;cAAEiC,UAAU,EAAE,GAAG;cAAED,YAAY,EAAE,EAAE;cAAE/B,KAAK,EAAE,SAAS;cAAEM,SAAS,EAAE;YAAU,CAAE;YAAAuB,QAAA,GACrGnB,UAAU,CAAC6B,gBAAgB,IAAIhD,eAAe,EAAE,kRAAgR,EAAEmB,UAAU,CAAC6B,gBAAgB,IAAIhD,eAAe,EAAE,gIAA8H,EAAEmB,UAAU,CAAC6B,gBAAgB,IAAIhD,eAAe,EAAE,wGACtiB;UAAA;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP5E,OAAA,CAACL,IAAI;YAACsE,KAAK,EAAE;cAAE1B,QAAQ,EAAE,EAAE;cAAEiC,UAAU,EAAE,GAAG;cAAED,YAAY,EAAE,EAAE;cAAE/B,KAAK,EAAE,SAAS;cAAEM,SAAS,EAAE;YAAU,CAAE;YAAAuB,QAAA,GACtGnB,UAAU,CAAC8B,YAAY,EAAC,aAAW,EAAE9B,UAAU,CAAC6B,gBAAgB,IAAIhD,eAAe,EAAE,2IACsB,EAACJ,WAAW,IAAI,WAAW,EAAC,8IAC1I;UAAA;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,eACP,CACH,eAiBD5E,OAAA,CAACN,IAAI;UAACuE,KAAK,EAAE;YAAEgB,KAAK,EAAE,MAAM;YAAEC,OAAO,EAAE,MAAM;YAAEvC,UAAU,EAAE,QAAQ;YAAED,cAAc,EAAE,QAAQ;YAAEyC,SAAS,EAAE;UAAG,CAAE;UAAAd,QAAA,eAC7GrE,OAAA,CAACN,IAAI;YAACuE,KAAK,EAAE;cAAEgB,KAAK,EAAE,GAAG;cAAEG,WAAW,EAAE,CAAC;cAAEC,WAAW,EAAE,SAAS;cAAEC,YAAY,EAAE,CAAC;cAAEnB,OAAO,EAAE,EAAE;cAAED,eAAe,EAAE,SAAS;cAAEvB,UAAU,EAAE,QAAQ;cAAED,cAAc,EAAE;YAAS,CAAE;YAAA2B,QAAA,gBAC5KrE,OAAA,CAACL,IAAI;cAACsE,KAAK,EAAE;gBAAE1B,QAAQ,EAAE,EAAE;gBAAE+B,UAAU,EAAE,MAAM;gBAAExB,SAAS,EAAE,QAAQ;gBAAEyB,YAAY,EAAE,EAAE;gBAAEC,UAAU,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAE3G;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACNxB,aAAa,gBACZpD,OAAA,CAACJ,KAAK;cAAC2F,GAAG,EAAEnC,aAAc;cAACa,KAAK,EAAE;gBAAEgB,KAAK,EAAE,GAAG;gBAAEO,MAAM,EAAE,GAAG;gBAAEC,SAAS,EAAE,SAAS;gBAAElB,YAAY,EAAE;cAAE;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAExG5E,OAAA,CAACN,IAAI;cAACuE,KAAK,EAAE;gBAAEgB,KAAK,EAAE,GAAG;gBAAEO,MAAM,EAAE,GAAG;gBAAEtB,eAAe,EAAE,SAAS;gBAAEvB,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE,QAAQ;gBAAE4C,YAAY,EAAE;cAAG,CAAE;cAAAjB,QAAA,eACrIrE,OAAA,CAACL,IAAI;gBAACsE,KAAK,EAAE;kBAAEzB,KAAK,EAAE,SAAS;kBAAED,QAAQ,EAAE,EAAE;kBAAEiC,UAAU,EAAE;gBAAI,CAAE;gBAAAH,QAAA,EAAC;cAAQ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACP5E,OAAA,CAACN,IAAI;MAACuE,KAAK,EAAEjC,MAAM,CAACE,MAAO;MAACwD,KAAK;MAAArB,QAAA,gBAC/BrE,OAAA,CAACL,IAAI;QAACsE,KAAK,EAAEjC,MAAM,CAACa,UAAW;QAAAwB,QAAA,EAAEnB,UAAU,CAACyC,eAAe,IAAI;MAAiB;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACxF5E,OAAA,CAACL,IAAI;QAACsE,KAAK,EAAEjC,MAAM,CAACgB,WAAY;QAAC4C,MAAM,EAAEA,CAAC;UAAEC,UAAU;UAAEC;QAAW,CAAC,KAAM,GAAED,UAAW,MAAKC,UAAW;MAAE;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxG,CAAC,eACP5E,OAAA,CAACL,IAAI;MAACsE,KAAK,EAAE;QAAEiB,OAAO,EAAE;MAAO,CAAE;MAACU,MAAM,EAAEA,CAAC;QAAEC;MAAW,CAAC,KAAK;QAAEtC,iBAAiB,CAAC,kBAAkB,EAAEsC,UAAU,CAAC;QAAE,OAAO,EAAE;MAAE,CAAE;MAACH,KAAK;IAAA;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrI,CAAC;AAEX,CAAC;AAACtB,EAAA,CAlFIL,oBAAyD;EAAA,QAC/BnD,eAAe;AAAA;AAAAiG,EAAA,GADzC9C,oBAAyD;AAoF/D,eAAeA,oBAAoB;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}