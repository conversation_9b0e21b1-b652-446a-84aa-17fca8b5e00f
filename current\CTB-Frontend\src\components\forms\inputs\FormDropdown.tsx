import { Controller, useFormContext } from "react-hook-form";
import { InputBaseParams } from "../Form";
import Chevron from "../../../assets/icons/Chevron";
import { useRef } from "react";

export type DropdownOption<T extends string | number> = {
  value: T;
  label?: string;
  isDisabled?: boolean;
};
 
export const DropdownOptions = <T extends string | number>({
  value,
  options,
  error,
  className,
  onChange,
  placeholder
}: {
  value?: T;
  options: DropdownOption<T>[];
  error?: boolean;
  className?: string;
  onChange: (value: T) => void;
  placeholder?: string;
}) => {
  const selectRef = useRef<HTMLSelectElement>(null);

  const handleChevronClick = () => {
    if (selectRef.current) {
      selectRef.current.focus();
    }
  };

  return (
    <div>
      <select
        value={value}
        ref={selectRef}
        onChange={(e) => onChange(e.target.value as T)}
        className={
          "w-full bg-ctb-blue-600 px-4 font-medium text-md text-white py-4 rounded-md focus:outline-none focus:ring-2 focus:ring-ctb-blue-300 " +
          className +
          (error ? " border-ctb-red-500 text-ctb-red-500" : "")
        }
      >
        {placeholder && (
          <option value="" disabled className="bg-white text-black  ">
            {placeholder}
          </option>
        )}
        {options.map((option, index) => (
          <option
            key={index}
            value={option.value}
            disabled={option.isDisabled}
            className={value === option.value ? " bg-white text-black " : " bg-white text-black "}
          >
            {option.label || option.value}
          </option>
        ))}
      </select>
      <Chevron className="absolute -mr-2 top-4 transform -translate-y-4 cursor-pointer" onClick={handleChevronClick} />
    </div>
  );
};

const FormDropdown = <T extends string | number>({
  name,
  options,
  rules,
  className,
  onChangeCallback,
  placeholder,
  defaultValue
}: InputBaseParams & {
  options: DropdownOption<T>[];
  placeholder?: string;
  defaultValue?: T;
}) => {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      defaultValue={defaultValue}  
      rules={rules}
      render={({ field: { value, onChange }, fieldState: { error } }) => (
        <DropdownOptions
          value={value}
          options={options}
          error={error !== undefined}
          onChange={(value) => {
            onChangeCallback && onChangeCallback(value);
            onChange(value);
          }}
          className={className}
          placeholder={placeholder}  
        />
      )}
    />
  );
};

export default FormDropdown;