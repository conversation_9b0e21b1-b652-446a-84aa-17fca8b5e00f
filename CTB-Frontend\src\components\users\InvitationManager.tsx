import React, { useEffect, useState } from "react";
import { UserRole } from "../../utils/api/endpoints/user/credentials";
import toast from "react-hot-toast";
import {
  createInvitation,
  deleteInvitation,
  getInvitations
} from "../../utils/api/endpoints/user/invitation";
import {
  requestChangeUserRole,
  getActivityLogs
} from "../../utils/api/endpoints/user/multiTenant";
import useUserCredentials from "../../utils/hooks/user/useUserCredentials";
import usePageTitle from "../../utils/hooks/usePageTitle";

// Import types
import {
  Invitation,
  ActivityLog,
  ROLE_OPTIONS
} from "../../utils/hooks/multi-tenant/invitation";

// Import components
import SpinnerOverlay from "../multi_tenant/SpinnerOverlay";
import DeleteConfirmationModal from "../multi_tenant/DeleteConfirmationModal";
import RolePermissionsModal from "../multi_tenant/RolePermissionsModal";
import TabNavigation from "../multi_tenant/TabNavigation";
import CreateInvitationForm from "../multi_tenant/CreateInvitationForm";
import InvitationsTable from "../multi_tenant/InvitationsTable";
import InvitationsFilters from "../multi_tenant/InvitationsFilters";
import ActivityLogsTable from "../multi_tenant/ActivityLogsTable";
import ActivityLogsFilters from "../multi_tenant/ActivityLogsFilters";
import Pagination from "../retests/utils/Pagination";

const InvitationsManager: React.FC = () => {
  const { role } = useUserCredentials();
  const [activeTab, setActiveTab] = useState<"invitations" | "activityLogs">(
    "invitations"
  );
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [activityLogs, setActivityLogs] = useState<ActivityLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [logsLoading, setLogsLoading] = useState(false);
  const [isError, setIsError] = useState(false);
  const [logsError, setLogsError] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentInvitationsPage, setCurrentInvitationsPage] = useState(1);
  const [currentLogsPage, setCurrentLogsPage] = useState(1);

  // Invitations filters
  const [searchQuery, setSearchQuery] = useState("");
  const [filterRole, setFilterRole] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");

  // Activity logs filters
  const [logsSearchQuery, setLogsSearchQuery] = useState("");
  const [filterLogsRole, setFilterLogsRole] = useState("all");
  const [filterLogsModule, setFilterLogsModule] = useState("all");
  const [sortLogsOrder, setSortLogsOrder] = useState<"newest" | "oldest">(
    "newest"
  );

  // Modals
  const [isPermissionsModalOpen, setIsPermissionsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [invitationToDelete, setInvitationToDelete] = useState<number | null>(
    null
  );

  const itemsPerPage = 10;
  const availableRoles = role ? ROLE_OPTIONS[role] : [];

  usePageTitle("Tenant and role management | Capture The Bug");

  useEffect(() => {
    setCurrentInvitationsPage(1);
  }, [searchQuery, filterRole, filterStatus]);

  useEffect(() => {
    setCurrentLogsPage(1);
  }, [logsSearchQuery, filterLogsRole, filterLogsModule, sortLogsOrder]);

  const getRoleFilterOptions = () => {
    if (role === UserRole.ADMIN) {
      return [
        { value: "QA", label: "QA" },
        { value: "ADMIN_MANAGER", label: "Admin Manager" },
        { value: "SUB_ADMIN", label: "Sub Admin" }
      ];
    }
    if (role === UserRole.BUSINESS) {
      return [
        { value: "DEVELOPER", label: "Security Engineer" },
        { value: "BUSINESS_MANAGER", label: "Program Manager" },
        { value: "BUSINESS_ADMINISTRATOR", label: "Organisation Admin" }
      ];
    }
    return [];
  };

  const fetchInvitations = async () => {
    try {
      setIsLoading(true);
      const data = await getInvitations();
      setInvitations(data);
      setIsError(false);
    } catch (error) {
      setIsError(true);
      toast.error("Failed to load invitations");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchActivityLogs = async () => {
    try {
      setLogsLoading(true);
      const logs = await getActivityLogs();
      setActivityLogs(logs);
      setLogsError(false);
    } catch (error) {
      setLogsError(true);
      toast.error("Failed to load activity logs");
    } finally {
      setLogsLoading(false);
    }
  };

  useEffect(() => {
    fetchInvitations();
  }, []);

  useEffect(() => {
    if (activeTab === "activityLogs") {
      fetchActivityLogs();
    }
  }, [activeTab]);

  const handleDeleteClick = (id: number) => {
    setInvitationToDelete(id);
    setIsDeleteModalOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (invitationToDelete !== null) {
      try {
        setIsProcessing(true);
        await deleteInvitation(invitationToDelete);
        await fetchInvitations();
        toast.success("Invitation deleted successfully");
      } catch (error) {
        toast.error("Failed to delete invitation");
      } finally {
        setIsProcessing(false);
        setIsDeleteModalOpen(false);
        setInvitationToDelete(null);
      }
    }
  };

  const handleResendInvitation = async (email: string, role: UserRole) => {
    try {
      setIsProcessing(true);
      await createInvitation({ email, role });
      await fetchInvitations();
      toast.success("New invitation sent successfully");
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to send invitation");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRoleChange = async (invitationId: number, newRole: UserRole) => {
    setIsProcessing(true);
    try {
      await requestChangeUserRole({
        invitation_id: invitationId,
        new_role: newRole
      });
      await fetchInvitations();
      toast.success("Role updated successfully");
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to update role");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCreateInvitation = async () => {
    await fetchInvitations();
  };

  if (!role || ![UserRole.ADMIN, UserRole.BUSINESS].includes(role)) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="rounded-lg bg-white p-8 text-center shadow-lg">
          <h2 className="mb-4 text-2xl font-bold text-red-600">
            Access Denied
          </h2>
          <p className="text-gray-600">
            You don't have permission to access this page.
          </p>
        </div>
      </div>
    );
  }

  // Filter and paginate invitations
  const filteredInvitations = invitations.filter(invitation => {
    const roleMatch =
      filterRole === "all" || UserRole[invitation.role] === filterRole;
    const statusMatch =
      filterStatus === "all" ||
      invitation.status.toLowerCase() === filterStatus.toLowerCase();
    const searchMatch = invitation.invitee_email
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    return roleMatch && statusMatch && searchMatch;
  });

  const totalInvitationsPages = Math.ceil(
    filteredInvitations.length / itemsPerPage
  );
  const paginatedInvitations = filteredInvitations.slice(
    (currentInvitationsPage - 1) * itemsPerPage, // Adjusted
    currentInvitationsPage * itemsPerPage // Adjusted
  );

  // Filter and sort activity logs
  const filteredLogs = activityLogs.filter(log => {
    const searchMatch =
      log.user.email.toLowerCase().includes(logsSearchQuery.toLowerCase()) ||
      log.user.username.toLowerCase().includes(logsSearchQuery.toLowerCase()) ||
      log.action.toLowerCase().includes(logsSearchQuery.toLowerCase());
    const roleMatch =
      filterLogsRole === "all" || UserRole[log.role] === filterLogsRole;
    const moduleMatch =
      filterLogsModule === "all" || log.module === filterLogsModule;
    return searchMatch && roleMatch && moduleMatch;
  });

  const sortedLogs = [...filteredLogs].sort((a, b) => {
    const dateA = new Date(a.created_at).getTime();
    const dateB = new Date(b.created_at).getTime();
    return sortLogsOrder === "newest" ? dateB - dateA : dateA - dateB;
  });

  const totalLogsPages = Math.ceil(filteredLogs.length / itemsPerPage);
  const paginatedLogs = sortedLogs.slice(
    (currentLogsPage - 1) * itemsPerPage,
    currentLogsPage * itemsPerPage
  );

  // Get unique roles and modules for filters
  const uniqueRoles = Array.from(
    new Set(activityLogs.map(log => log.role))
  ) as UserRole[];

  const uniqueModules = Array.from(
    new Set(activityLogs.map(log => log.module))
  );

  return (
    <div className="min-h-screen bg-gray-50 p-6 sm:p-10">
      {isProcessing && <SpinnerOverlay />}
      <div className="max-w-8xl mx-auto">
        <div className="mb-8 rounded-xl bg-white p-6 shadow-xl">
          <h2 className="mb-4 border-b pb-4 text-3xl font-bold text-gray-900">
            Tenant and role management
          </h2>

          <TabNavigation
            activeTab={activeTab}
            setActiveTab={tab =>
              setActiveTab(tab as "invitations" | "activityLogs")
            }
            tabs={[
              { id: "invitations", label: "Manage Invitations" },
              { id: "activityLogs", label: "Activity Logs" }
            ]}
          />

          {activeTab === "invitations" ? (
            <>
              <div className="items-right flex justify-end text-right">
                <button
                  onClick={() => setIsPermissionsModalOpen(true)}
                  className="rounded-md px-4 py-2 text-blue-700 transition-colors hover:text-blue-800 hover:underline"
                >
                  View role access guide
                </button>
              </div>

              <CreateInvitationForm
                availableRoles={availableRoles}
                onInvitationCreated={handleCreateInvitation}
              />

              <InvitationsFilters
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                filterRole={filterRole}
                setFilterRole={setFilterRole}
                filterStatus={filterStatus}
                setFilterStatus={setFilterStatus}
                roleFilterOptions={getRoleFilterOptions()}
              />

              <InvitationsTable
                invitations={paginatedInvitations}
                isLoading={isLoading}
                isError={isError}
                availableRoles={availableRoles}
                onRoleChange={handleRoleChange}
                onDeleteClick={handleDeleteClick}
                onResendInvitation={handleResendInvitation}
              />

              <Pagination
                currentPage={currentInvitationsPage}
                totalPages={totalInvitationsPages}
                onPageChange={setCurrentInvitationsPage}
              />
            </>
          ) : (
            <>
              <ActivityLogsFilters
                searchQuery={logsSearchQuery}
                setSearchQuery={setLogsSearchQuery}
                filterRole={filterLogsRole}
                setFilterRole={setFilterLogsRole}
                filterModule={filterLogsModule}
                setFilterModule={setFilterLogsModule}
                sortOrder={sortLogsOrder}
                setSortOrder={setSortLogsOrder}
                availableRoles={uniqueRoles}
                availableModules={uniqueModules}
              />

              <ActivityLogsTable
                logs={paginatedLogs}
                isLoading={logsLoading}
                isError={logsError}
              />

              <Pagination
                currentPage={currentLogsPage}
                totalPages={totalLogsPages}
                onPageChange={setCurrentLogsPage}
              />
            </>
          )}
        </div>
      </div>

      {isPermissionsModalOpen && (
        <RolePermissionsModal
          isOpen={isPermissionsModalOpen}
          onClose={() => setIsPermissionsModalOpen(false)}
          userRole={role}
        />
      )}

      {isDeleteModalOpen && (
        <DeleteConfirmationModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          onConfirm={handleConfirmDelete}
        />
      )}
    </div>
  );
};

export default InvitationsManager;
