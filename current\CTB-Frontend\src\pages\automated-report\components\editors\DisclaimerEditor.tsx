import React from 'react';
import HtmlEditor from '../HtmlEditor';
import { ReportData } from '../../types/report.types';

interface DisclaimerEditorProps {
  reportData: ReportData;
  onHtmlChange: (field: string, value: string) => void;
}

const DisclaimerEditor: React.FC<DisclaimerEditorProps> = ({ reportData, onHtmlChange }) => {
  return (
    <div className="h-full overflow-y-auto">
      <div className="bg-white rounded-xl shadow-lg border border-slate-100 p-6 mb-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-1 h-8 bg-gradient-to-b from-blue-500 to-blue-700 rounded-full" />
          <div>
            <h3 className="text-base font-bold text-blue-900 tracking-tight">Disclaimer</h3>
            <p className="text-xs text-blue-700/80 font-medium mt-0.5">Edit disclaimer content</p>
          </div>
        </div>
        <div className="pt-2">
          <div className="text-xs">
            <HtmlEditor
              field="disclaimer"
              title=""
              reportData={reportData}
              onHtmlChange={onHtmlChange as any}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DisclaimerEditor; 