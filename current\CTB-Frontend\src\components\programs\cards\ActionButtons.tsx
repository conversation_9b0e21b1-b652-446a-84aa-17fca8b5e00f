import InlineContainer from "../../common/InlineContainer";
import OutlineButton from "../../buttons/OutlineButton";
import EditIcon from "../../../assets/icons/EditIcon";
import PieChartIcon from "../../../assets/icons/PieChartIcon";
import DeleteIcon from "../../../assets/icons/DeleteIcon";
import ErrorMessage from "../../forms/errors/ErrorMessage";
import { UserRole } from "../../../utils/api/endpoints/user/credentials";
import { useNavigate, useParams } from "react-router-dom";
import useUserCredentials from "../../../utils/hooks/user/useUserCredentials";
import { useState } from "react";
import useProgram from "../../../utils/hooks/programs/useProgram";
import toast from "react-hot-toast";
import ProgramStats from "../ProgramStats";

interface ProgramActionsProps {
  highlight?: boolean;
}

const ProgramActions = ({ highlight = false }: ProgramActionsProps) =>{
  const navigate = useNavigate();
  const { id } = useParams();
  const { role } = useUserCredentials();
  const programId = id ? parseInt(id, 10) : undefined;
  const { program, isLoading, deleteProgram } = useProgram(programId);
  const [showStats, setShowStats] = useState(false);

  
  const canEdit =
    (role === UserRole.ADMIN || role === UserRole.SUB_ADMIN || role === UserRole.ADMIN_MANAGER) ||  
    (!program?.isActivated && (role === UserRole.BUSINESS || role === UserRole.BUSINESS_ADMINISTRATOR || role === UserRole.BUSINESS_MANAGER));

  const canDelete =
    (role === UserRole.ADMIN) ||  
    (!program?.isActivated && role === UserRole.BUSINESS);

  const canAccessProgram =
    role &&
    role !== UserRole.RESEARCHER &&
    (role === UserRole.ADMIN ||
      role === UserRole.QA ||
      role === UserRole.ADMIN_MANAGER ||
      role === UserRole.SUB_ADMIN ||
      (role === UserRole.BUSINESS_MANAGER && !program?.isDelete) ||
      (role === UserRole.BUSINESS_ADMINISTRATOR && !program?.isDelete) ||
      (role === UserRole.BUSINESS && !program?.isDelete));
 
      
  const handleDelete = async () => {
    try {
      await deleteProgram();
      toast.success("Program deleted successfully!");
      navigate("/dashboard/programs");
    } catch (error) {
      toast.error("Failed to delete the program.");
    }
  };

  return (
    <div className="">
      {canAccessProgram ? (
        <InlineContainer className="w-full">
          {/* Edit Button */}
          {canEdit && (
           <OutlineButton
           onClick={() => navigate("edit")}
           className={`stroke-ctb-blue-400 border-r hover:stroke-white ${
             highlight ? "!text-white !stroke-white" : ""
           }`}
         >
              <EditIcon />
            </OutlineButton>
          )}

          {/* Stats Button */}
          <OutlineButton
            className={`stroke-ctb-blue-400 hover:stroke-white !border-r ${
              highlight ? "!text-white !stroke-white" : ""
            }`}
            onClick={() => setShowStats(true)}
          >
            <PieChartIcon />
          </OutlineButton>

          {/* Delete Button */}
          {canDelete && (
             <OutlineButton
             onClick={handleDelete}
             className={`stroke-ctb-blue-400 hover:!border-red-500 hover:!bg-red-500 hover:stroke-white ${
               highlight ? "!text-white !stroke-white" : ""
             }`}
           >
              <DeleteIcon />
            </OutlineButton>
          )}
        </InlineContainer>
      ) : ( 
        program?.isDelete &&
        (role === UserRole.BUSINESS ||
          role === UserRole.BUSINESS_ADMINISTRATOR ||
          role === UserRole.BUSINESS_MANAGER) && (
          <ErrorMessage
            message="Failed to load program"
            clearError={() => navigate("/dashboard/programs")}
          />
        )
      )}

      {/* Program Stats Modal */}
      {showStats && (
        <ProgramStats
          showStats={showStats}
          program={program}
          onClose={() => setShowStats(false)}
        />
      )}
    </div>
  );
};

export default ProgramActions;