import React from 'react';
import { Page, View, Text, Image, StyleSheet } from '@react-pdf/renderer';
import { ReportData } from '../../types/report.types';
import { useSectionPages } from '../SectionPageContext';
// Use the same severity color codes as FullTableOfContentsPage
const severityColors: Record<string, string> = {
  Critical: '#800000', // Maroon
  High: '#dc2626',     // Red
  Medium: '#fbbf24',   // Yellow
  Low: '#10b981',      // Green
  Informational: '#3b82f6', // Blue or keep as is
};

// Utility to strip HTML tags and preserve paragraph breaks
function htmlToParagraphs(html: string): string[] {
  if (!html) return [];
  // Replace <br> and <br/> with \n
  let withBreaks = html.replace(/<br\s*\/?>/gi, '\n');
  // Replace <p> and <div> with \n (block-level)
  withBreaks = withBreaks.replace(/<\/?(p|div)[^>]*>/gi, '\n');
  // Remove all other tags
  const tmp = document.createElement('div');
  tmp.innerHTML = withBreaks;
  const text = tmp.textContent || tmp.innerText || '';
  // Split by double newlines or single newlines, filter empty
  return text.split(/\n+/).map(s => s.trim()).filter(Boolean);
}

// Helper function to get testing type description
const getTestingTypeDescription = (testingType?: string): string => {
  if (!testingType) {
    return 'comprehensive Vulnerability Assessment and Penetration Test (VAPT)';
  }

  const type = testingType.toLowerCase();

  if (type.includes('web') && type.includes('mobile')) {
    return 'comprehensive Web Application and Mobile Application Security Assessment';
  } else if (type.includes('web')) {
    return 'comprehensive Web Application Security Assessment';
  } else if (type.includes('mobile')) {
    return 'comprehensive Mobile Application Security Assessment';
  } else if (type.includes('network')) {
    return 'comprehensive Network Security Assessment';
  } else if (type.includes('api')) {
    return 'comprehensive API Security Assessment';
  } else if (type.includes('cloud')) {
    return 'comprehensive Cloud Security Assessment';
  } else if (type.includes('infrastructure')) {
    return 'comprehensive Infrastructure Security Assessment';
  } else {
    // For any other testing type, use it directly with "Assessment"
    return `comprehensive ${testingType} Assessment`;
  }
};

const DEFAULT_COMPANY = 'Capture The Bug';

interface ExecutiveSummaryPageProps {
  reportData: ReportData;
  pieChartImage?: string;
  barChartImage?: string;
  sectionId?: string;
}

const styles = StyleSheet.create({
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    fontSize: 10,
    color: 'grey',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  footerLeft: {
    textAlign: 'left',
    flex: 1,
  },
  footerRight: {
    textAlign: 'right',
    flex: 1,
  },
});

const ExecutiveSummaryPage: React.FC<ExecutiveSummaryPageProps> = ({ reportData, pieChartImage, barChartImage, sectionId }) => {
  const { updateSectionPage } = useSectionPages();
  const summaryParagraphs = reportData.executive_summary ? htmlToParagraphs(reportData.executive_summary) : null;

  // Get testing type from the first program
  const testingType = reportData.program_details && reportData.program_details.length > 0
    ? reportData.program_details[0].testing_type
    : undefined;
  const testingDescription = getTestingTypeDescription(testingType);
  return (
    <Page size="A4" id={sectionId} style={{
      flexDirection: 'column',
      backgroundColor: '#ffffff',
      padding: '20mm 15mm',
      fontFamily: 'Helvetica',
      fontSize: 12,
    }}>
      <View style={{ paddingHorizontal: 24, flexDirection: 'column', flex: 1 }}>
        <View style={{ flex: 1 }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#2563eb', marginBottom: 16, lineHeight: 1.4 }}>
            EXECUTIVE SUMMARY
          </Text>
          {summaryParagraphs ? (
            summaryParagraphs.map((para, idx) => (
              <Text key={idx} style={{ fontSize: 12, lineHeight: 1.4, marginBottom: 14, color: '#374151', textAlign: 'justify' }}>
                {para}
              </Text>
            ))
          ) : (
            <>
              <Text style={{ fontSize: 12, lineHeight: 1.4, marginBottom: 14, color: '#374151', textAlign: 'justify' }}>
                {(reportData.branding_company || DEFAULT_COMPANY)} is a modern Penetration Testing as a Service (PTaaS) platform designed to support fast-moving engineering teams and security-conscious enterprises. Organizations across New Zealand, Australia, and North America-including public companies and high-growth SaaS teams-trust {(reportData.branding_company || DEFAULT_COMPANY)} to secure their CI/CD pipelines, meet compliance goals, and reduce time-to-remediation without slowing development velocity. {(reportData.branding_company || DEFAULT_COMPANY)} is HQ in New Zealand and proudly works with customers across regulated and innovation-driven sectors.
              </Text>
              <Text style={{ fontSize: 12, lineHeight: 1.4, marginBottom: 18, color: '#374151', textAlign: 'justify' }}>
                {reportData.company_name} entrusted {(reportData.branding_company || DEFAULT_COMPANY)} to conduct a  Vulnerability
                Assessment and Penetration Test (VAPT). This assessment evaluated the application's security posture from a {testingType || 'undefined'} perspective to focus on its resilience against common attack patterns and identify vulnerabilities in its internal and external interfaces.
              </Text>
            </>
          )}
          {/*
            Pie Chart is commented out as per request
            <View style={{ width: '48%', borderWidth: 1, borderColor: '#e5e7eb', borderRadius: 8, padding: 20, backgroundColor: '#f9fafb', alignItems: 'center' }}>
              <Text style={{ fontSize: 12, fontWeight: 'bold', textAlign: 'center', marginBottom: 12 }}>
                VULNERABILITY DISTRIBUTION
              </Text>
              {pieChartImage ? (
                <Image src={pieChartImage} style={{ width: 180, height: 180, objectFit: 'contain', marginBottom: 8 }} />
              ) : (
                <View style={{ width: 180, height: 180, backgroundColor: '#e5e7eb', alignItems: 'center', justifyContent: 'center', borderRadius: 90 }}>
                  <Text style={{ color: '#64748b', fontSize: 12 }}>No Chart</Text>
                </View>
              )}
            </View>
          */}
          {/* Bar Chart - Centered and wider */}
          <View style={{ width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center', marginTop: 32 }}>
            <View style={{ width: 420, borderWidth: 1, borderColor: '#e5e7eb', borderRadius: 8, padding: 20, backgroundColor: '#f9fafb', alignItems: 'center', justifyContent: 'center' }}>
              <Text style={{ fontSize: 12, fontWeight: 'bold', textAlign: 'center', marginBottom: 12, lineHeight: 1.4 }}>
                FINDINGS BY SEVERITY
              </Text>
              {barChartImage ? (
                <Image src={barChartImage} style={{ width: 380, height: 240, objectFit: 'contain', marginBottom: 8 }} />
              ) : (
                <View style={{ width: 380, height: 240, backgroundColor: '#e5e7eb', alignItems: 'center', justifyContent: 'center', borderRadius: 12 }}>
                  <Text style={{ color: '#64748b', fontSize: 12, lineHeight: 1.4 }}>No Chart</Text>
                </View>
              )}
              {/*
                If you render the bar chart here manually, use severityColors for each bar:
                Example: <Rect fill={severityColors[severity]} ... />
              */}
            </View>
          </View>
        </View>
      </View>
      <View style={styles.footer} fixed>
        <Text style={styles.footerLeft}>{reportData.document_number || 'Document Number'}</Text>
        <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
      </View>
      <Text style={{ display: 'none' }} render={({ pageNumber }) => { updateSectionPage('ExecutiveSummary', pageNumber); return ''; }} fixed />
    </Page>
  );
};

export default ExecutiveSummaryPage; 