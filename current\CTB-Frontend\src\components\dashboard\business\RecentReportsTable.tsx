import React from "react";
import { cropSentence } from "../../../utils/formatText";

type RecentReport = {
  reportId: number;
  reportTitle: string;
  submissionDate: string;
  severityCategory: string;
  state: string;
};

type RecentReportsTableProps = {
  recentReports: RecentReport[];
};

const formatDate = (dateString: string | null): string => {
  if (!dateString) return "N/A";
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit"
    });
  } catch {
    return "N/A";
  }
};

const severityColorMap = {
  critical: "bg-red-100 text-red-800", // Red
  high: "bg-orange-100 text-orange-800", // Orange
  medium: "bg-yellow-100 text-yellow-800", // Amber
  low: "bg-green-100 text-green-800" // Emerald
};

const getSeverityColor = (severity: string): string => {
  const severityKey = severity.toLowerCase();
  return (
    severityColorMap[severityKey as keyof typeof severityColorMap] ||
    "bg-gray-100 text-gray-800"
  );
};

const RecentReportsTable: React.FC<RecentReportsTableProps> = ({
  recentReports
}) => {
  return (
    <div className="w-full overflow-x-auto border border-gray-200 shadow-sm">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-100">
          <tr>
            {["Title", "Created at", "Severity", "State"].map(header => (
              <th
                key={header}
                className="px-6 py-3 text-left text-xs font-semibold uppercase tracking-wider text-black"
              >
                {header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 bg-white">
          {recentReports.length === 0 ? (
            <tr>
              <td
                colSpan={4}
                className="px-6 py-4 text-center text-sm text-gray-500"
              >
                No reports found
              </td>
            </tr>
          ) : (
            recentReports.map(report => (
              <tr
                key={report.reportId}
                className="cursor-pointer transition-all duration-200 ease-in-out hover:bg-gray-50"
                onClick={() => {
                  window.location.href = `/dashboard/reports/${report.reportId}`;
                }}
              >
                <td className="whitespace-nowrap px-6 py-4">
                  <div className="text-sm font-medium text-gray-900 transition-colors duration-200 hover:text-blue-600">
                    {cropSentence(report.reportTitle)}
                  </div>
                </td>
                <td className="whitespace-nowrap px-6 py-4">
                  <div className="text-sm font-medium text-gray-500">
                    {formatDate(report.submissionDate)}
                  </div>
                </td>
                <td className="whitespace-nowrap px-6 py-4">
                  <span
                    className={`inline-flex rounded-lg px-3 py-1 text-xs font-semibold leading-5 ${getSeverityColor(
                      report.severityCategory
                    )}`}
                  >
                    {report.severityCategory}
                  </span>
                </td>
                <td className="whitespace-nowrap px-6 py-4">
                  <span
                    className={`inline-flex rounded-full bg-blue-500 px-2 py-1 text-[12px] font-semibold leading-5 text-white`}
                  >
                    Business Review
                  </span>
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
};

export default RecentReportsTable;
