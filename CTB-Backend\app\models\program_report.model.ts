import { DataTypes, Model, Sequelize, ModelCtor } from "sequelize";

export type ProgramReportStatus =
  | "draft"
  | "qa_review"
  | "admin_review"
  | "approved"
  | "rejected"
  | "business_review"
  | "business_requested_changes"
  | "changes_added"
  | "report_updated"
  | "business_approved";

export type ProgramReportAttributes = {
  id?: string;
  user_id?: number;
  role?: string;
  status?: ProgramReportStatus;

  // Basic Report Information
  company_name?: string;
  program_name?: string;
  report_title?: string;
  version_number?: string;
  document_number?: string;
  current_date?: string;
  assessment_date?: string;
  date_of_request?: string;
  revision_date?: string;

  // Report Personnel
  test_lead?: string | null;
  prepared_by?: string | null;
  reviewed_by?: string | null;
  approved_by?: string | null;
  version_description?: string | null;

  // Content Sections (HTML)
  executive_summary?: string;
  key_findings?: string;
  scope?: string;
  project_objectives?: string;
  methodology?: any; // Change from string to any (object)
  findings?: string;
  recommendations?: string;
  conclusion?: string;
  disclaimer?: string;
  appendix?: string;
  recommendations_list?: { title: string; description: string }[];

  // Program Details (JSON array)
  program_details?: any[];

  // Target Details (JSON array)
  target_details?: any[];

  // Severity and Status Counts
  severity_counts?: any;
  status_counts?: any;
  open_close_counts_by_severity?: any;
  total_open?: number;
  total_closed?: number;
  total_findings?: number;

  // Reports List (JSON array)
  reports_list?: any[];

  // Vulnerability Ratings
  vulnerability_ratings?: any;

  // Detailed Findings (JSON array)
  detailed_findings?: any[];

  // Automated Report Reference
  automated_report_id?: string;

  // Program Names and IDs (added dynamically)
  program_names?: Record<number, string>;
  program_ids?: number[];

  // Branding (White Label)
  branding_logo?: string;
  branding_company?: string;

  // Metadata
  created_at?: Date;
  updated_at?: Date;
};

interface ProgramReportCreationAttributes extends ProgramReportAttributes {}

export class ProgramReport<T = unknown> extends Model<
  ProgramReportAttributes & T,
  ProgramReportCreationAttributes
> {
  declare id: string;
  declare user_id: number;
  declare role: string;
  declare status: ProgramReportStatus;

  // Basic Report Information
  declare company_name: string;
  declare program_name: string;
  declare report_title: string;
  declare version_number: string;
  declare document_number: string;
  declare current_date: string;
  declare assessment_date: string;
  declare date_of_request: string;
  declare revision_date: string;

  // Report Personnel
  declare test_lead: string | null;
  declare prepared_by: string | null;
  declare reviewed_by: string | null;
  declare approved_by: string | null;
  declare version_description: string | null;

  // Content Sections (HTML)
  declare executive_summary: string;
  declare key_findings: string;
  declare scope: string;
  declare project_objectives: string;
  declare methodology: any; // Change from string to any (object)
  declare findings: string;
  declare recommendations: string;
  declare conclusion: string;
  declare disclaimer: string;
  declare appendix: string;
  declare recommendations_list: { title: string; description: string }[];

  // Program Details
  declare program_details: any[];

  // Target Details
  declare target_details: any[];

  // Severity and Status Counts
  declare severity_counts: any;
  declare status_counts: any;
  declare open_close_counts_by_severity: any;
  declare total_open: number;
  declare total_closed: number;
  declare total_findings: number;

  // Reports List
  declare reports_list: any[];

  // Vulnerability Ratings
  declare vulnerability_ratings: any;

  // Detailed Findings
  declare detailed_findings: any[];

  // Automated Report Reference
  declare automated_report_id: string | null;

  // Program Names and IDs (added dynamically)
  declare program_names?: Record<number, string>;
  declare program_ids?: number[];

  // Branding (White Label)
  declare branding_logo: string;
  declare branding_company: string;

  // Metadata
  declare created_at: Date;
  declare updated_at: Date;

  public static associate(models: Record<string, ModelCtor<Model>>) {
    // Associate with user who created/edited the report
    this.belongsTo(models.user, {
      foreignKey: "user_id",
      as: "creator"
    });

    // Associate with automated reports if needed
    if (models.automated_reports) {
      this.belongsTo(models.automated_reports, {
        foreignKey: "automated_report_id",
        as: "automated_report"
      });
    }
  }
}

export default (sequelize: Sequelize) => {
  ProgramReport.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "users",
          key: "user_id"
        },
        comment: "User who created/edited the report"
      },
      role: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "Role of the user who created/edited the report"
      },
      status: {
        type: DataTypes.ENUM(
          "draft",
          "qa_review",
          "admin_review",
          "approved",
          "rejected",
          "business_review",
          "business_requested_changes",
          "changes_added",
          "report_updated",
          "business_approved"
        ),
        allowNull: false,
        defaultValue: "draft",
        comment: "Current status of the report"
      },

      // Basic Report Information
      company_name: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "Name of the company"
      },
      program_name: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "Name of the program for display on cover page"
      },
      report_title: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "Title of the report"
      },
      version_number: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "Version number of the report"
      },
      document_number: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "Document number/identifier"
      },
      current_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        comment: "Current date of the report"
      },
      assessment_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
        comment: "Date when the assessment was conducted"
      },
      date_of_request: {
        type: DataTypes.DATEONLY,
        allowNull: true,
        comment: "Date when the report was requested"
      },
      revision_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
        comment: "Date of the last revision"
      },

      // Report Personnel
      test_lead: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "Name of the test lead"
      },
      prepared_by: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "Name of person who prepared the report"
      },
      reviewed_by: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "Name of person who reviewed the report"
      },
      approved_by: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "Name of person who approved the report"
      },
      version_description: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "Description of the version changes"
      },

      // Content Sections (HTML)
      executive_summary: {
        type: DataTypes.TEXT("long"),
        allowNull: true,
        comment: "HTML content of executive summary"
      },
      key_findings: {
        type: DataTypes.TEXT("long"),
        allowNull: true,
        comment: "HTML content of key findings"
      },
      scope: {
        type: DataTypes.TEXT("long"),
        allowNull: true,
        comment: "HTML content of scope section"
      },
      project_objectives: {
        type: DataTypes.TEXT("long"),
        allowNull: true,
        comment: "HTML content of project objectives"
      },
      methodology: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: "Methodology selection (web/network/mobile)"
      },
      findings: {
        type: DataTypes.TEXT("long"),
        allowNull: true,
        comment: "HTML content of findings section"
      },
      recommendations: {
        type: DataTypes.TEXT("long"),
        allowNull: true,
        comment: "HTML content of recommendations section"
      },
      conclusion: {
        type: DataTypes.TEXT("long"),
        allowNull: true,
        comment: "HTML content of conclusion section"
      },
      disclaimer: {
        type: DataTypes.TEXT("long"),
        allowNull: true,
        comment: "HTML content of disclaimer"
      },
      appendix: {
        type: DataTypes.TEXT("long"),
        allowNull: true,
        comment: "HTML content of appendix"
      },
      recommendations_list: {
        type: DataTypes.JSON,
        allowNull: false,
        defaultValue: [],
        comment: "Structured recommendations (array of {title, description})"
      },

      // Program Details (JSON)
      program_details: {
        type: DataTypes.JSON,
        allowNull: false,
        defaultValue: [],
        comment:
          "Array of program details including program_id, title, testing_type, targets, etc."
      },

      // Target Details (JSON)
      target_details: {
        type: DataTypes.JSON,
        allowNull: false,
        defaultValue: [],
        comment: "Array of target details including type and URL"
      },

      // Severity and Status Counts (JSON)
      severity_counts: {
        type: DataTypes.JSON,
        allowNull: false,
        defaultValue: {},
        comment:
          "Count of vulnerabilities by severity (Critical, High, Medium, Low, Total)"
      },
      status_counts: {
        type: DataTypes.JSON,
        allowNull: false,
        defaultValue: {},
        comment: "Count of vulnerabilities by status (Open, Closed)"
      },
      open_close_counts_by_severity: {
        type: DataTypes.JSON,
        allowNull: false,
        defaultValue: {},
        comment: "Detailed breakdown of open/closed counts by severity"
      },
      total_open: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: "Total number of open findings"
      },
      total_closed: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: "Total number of closed findings"
      },
      total_findings: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: "Total number of findings"
      },

      // Reports List (JSON)
      reports_list: {
        type: DataTypes.JSON,
        allowNull: false,
        defaultValue: [],
        comment:
          "Array of individual vulnerability reports with abbreviation, title, severity, status"
      },

      // Vulnerability Ratings (JSON)
      vulnerability_ratings: {
        type: DataTypes.JSON,
        allowNull: false,
        defaultValue: {},
        comment: "Vulnerability rating descriptions for each severity level"
      },

      // Detailed Findings (JSON)
      detailed_findings: {
        type: DataTypes.JSON,
        allowNull: false,
        defaultValue: [],
        comment:
          "Array of detailed findings with full vulnerability information"
      },

      // Automated Report Reference
      automated_report_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "automated_reports",
          key: "id"
        },
        comment:
          "Reference to the original automated report (FK to automated_reports.id)"
      },

      // Branding (White Label)
      branding_logo: {
        type: DataTypes.STRING,
        allowNull: true,
        defaultValue: "https://your-default-logo-url.com/logo.png", // Replace with your actual default logo URL
        comment: "White label logo URL"
      },
      branding_company: {
        type: DataTypes.STRING,
        allowNull: true,
        defaultValue: "Capture The Bug Limited",
        comment: "White label company name"
      }
    },
    {
      sequelize,
      tableName: "program_reports",
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
      indexes: [
        {
          name: "idx_program_reports_user_id",
          fields: ["user_id"]
        },
        {
          name: "idx_program_reports_status",
          fields: ["status"]
        },
        {
          name: "idx_program_reports_company",
          fields: ["company_name"]
        },
        {
          name: "idx_program_reports_automated_id",
          fields: ["automated_report_id"]
        },
        {
          name: "idx_program_reports_created_at",
          fields: ["created_at"]
        }
      ]
    }
  );

  return ProgramReport;
};
