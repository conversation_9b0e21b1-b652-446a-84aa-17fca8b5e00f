import React from 'react';
import { Page, View, Text, Link } from '@react-pdf/renderer';
import { ReportData } from '../../types/report.types';
import { useSectionPages } from '../SectionPageContext';

interface TableOfContentsPageProps {
  reportData: ReportData;
}

const accentColor = '#2563eb';
const dividerColor = '#e5e7eb';
const appendixBg = '#f3f4f6';

const TableOfContentsPage: React.FC<TableOfContentsPageProps> = ({ reportData }) => {
  const { sectionPages } = useSectionPages();
  // Group findings by severity (using reports_list for technical summary)
  const findingsBySeverity = (reportData.reports_list || []).reduce(
    (acc: Record<string, any[]>, finding: any) => {
      const severity = finding.severity_category || 'Informational';
      if (!acc[severity]) {
        acc[severity] = [];
      }
      acc[severity].push(finding);
      return acc;
    },
    {} as Record<string, any[]>
  );

  // Helper for divider
  const Divider = () => (
    <View style={{ height: 1, backgroundColor: dividerColor, marginVertical: 8, opacity: 0.7 }} />
  );

  return (
    <Page size="A4" style={{ flexDirection: 'column', backgroundColor: '#ffffff', padding: '20mm 15mm', fontFamily: 'Helvetica', fontSize: 12 }}>
      {/* Premium Header */}
      <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 24 }}>
        <View style={{ width: 6, height: 32, backgroundColor: accentColor, borderRadius: 3, marginRight: 12 }} />
        <Text style={{ fontSize: 16, fontWeight: 'bold', color: accentColor, letterSpacing: 1.2, lineHeight: 1.4 }}>TABLE OF CONTENTS</Text>
      </View>

      {/* Main sections with accent bar */}
      <View style={{ marginBottom: 16 }}>
        {[
          { num: '1.', label: 'Disclaimer', key: 'Disclaimer' },
          { num: '2.', label: 'Document Reference', key: 'DocumentReference' },
          { num: '3.', label: 'Executive Summary', key: 'ExecutiveSummary' },
          { num: '4.', label: 'Key Findings', key: 'KeyFindings' },
          { num: '5.', label: 'Scope', key: 'Scope' },
          { num: '6.', label: 'Project Objectives', key: 'ProjectObjectives' },
          { num: '7.', label: 'Summary of Findings', key: 'SummaryOfFindings' },
          { num: '8.', label: 'Vulnerability Rating Definitions', key: 'VulnerabilityRatingDefinitions' },
          { num: '9.', label: 'Key Findings List', key: 'KeyFindingsList' },
        ].map((section, idx) => (
          <View key={section.key} style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
            <View style={{ width: 4, height: 16, backgroundColor: accentColor, borderRadius: 2, marginRight: 8, opacity: 0.7 }} />
            <Text style={{ fontSize: 12, width: 28, color: '#6b7280', fontWeight: 'bold', lineHeight: 1.4 }}>{section.num}</Text>
            <Link src={`#${section.key}`} style={{ fontSize: 12, flex: 1, color: '#1f2937', fontWeight: 500, lineHeight: 1.4, textDecoration: 'none' }}>{section.label}</Link>
            <Text style={{ fontSize: 12, width: 32, textAlign: 'right', color: accentColor, fontWeight: 'bold', lineHeight: 1.4 }}>{sectionPages[section.key] || ''}</Text>
          </View>
        ))}
      </View>

      

      <Divider />

      {/* Appendix - Soft background */}
      <View style={{ backgroundColor: appendixBg, borderRadius: 8, padding: 10, marginBottom: 8 }}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <View style={{ width: 4, height: 18, backgroundColor: accentColor, borderRadius: 2, marginRight: 8, opacity: 0.7 }} />
          <Text style={{ fontSize: 12, width: 28, color: '#6b7280', fontWeight: 'bold', lineHeight: 1.4 }}>10.</Text>
          <Text style={{ fontSize: 12, flex: 1, color: '#1f2937', fontWeight: 500, lineHeight: 1.4 }}>
            <Link src="#AppendixOwaspRiskRating_Page1" style={{ color: '#1f2937', textDecoration: 'none' }}>Appendix A: OWASP Risk Rating Methodology</Link>
          </Text>
          <Text style={{ fontSize: 12, width: 32, textAlign: 'right', color: accentColor, fontWeight: 'bold', lineHeight: 1.4 }}>{sectionPages['AppendixOwaspRiskRating_Page1'] || ''}</Text>
        </View>
      </View>
      {/* Conditionally render methodology sections */}
      {(() => {
        const meth = reportData.methodology || {};
        let num = 11;
        const rows = [];
        if (meth.web) {
          rows.push(
            <View key="web" style={{ backgroundColor: appendixBg, borderRadius: 8, padding: 10, marginBottom: 8 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <View style={{ width: 4, height: 18, backgroundColor: accentColor, borderRadius: 2, marginRight: 8, opacity: 0.7 }} />
                <Text style={{ fontSize: 12, width: 28, color: '#6b7280', fontWeight: 'bold', lineHeight: 1.4 }}>{num++ + '.'}</Text>
                <Text style={{ fontSize: 12, flex: 1, color: '#1f2937', fontWeight: 500, lineHeight: 1.4 }}>
                  <Link src="#Methodology" style={{ color: '#1f2937', textDecoration: 'none' }}>Methodology</Link>
                </Text>
                <Text style={{ fontSize: 12, width: 32, textAlign: 'right', color: accentColor, fontWeight: 'bold', lineHeight: 1.4 }}>{sectionPages['Methodology'] || ''}</Text>
              </View>
            </View>
          );
        }
        if (meth.network) {
          rows.push(
            <View key="network" style={{ backgroundColor: appendixBg, borderRadius: 8, padding: 10, marginBottom: 8 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <View style={{ width: 4, height: 18, backgroundColor: accentColor, borderRadius: 2, marginRight: 8, opacity: 0.7 }} />
                <Text style={{ fontSize: 12, width: 28, color: '#6b7280', fontWeight: 'bold', lineHeight: 1.4 }}>{num++ + '.'}</Text>
                <Text style={{ fontSize: 12, flex: 1, color: '#1f2937', fontWeight: 500, lineHeight: 1.4 }}>
                  <Link src="#NetworkMethodology" style={{ color: '#1f2937', textDecoration: 'none' }}>Network Methodology</Link>
                </Text>
                <Text style={{ fontSize: 12, width: 32, textAlign: 'right', color: accentColor, fontWeight: 'bold', lineHeight: 1.4 }}>{sectionPages['NetworkMethodology'] || ''}</Text>
              </View>
            </View>
          );
        }
        if (meth.mobile) {
          rows.push(
            <View key="mobile" style={{ backgroundColor: appendixBg, borderRadius: 8, padding: 10, marginBottom: 8 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <View style={{ width: 4, height: 18, backgroundColor: accentColor, borderRadius: 2, marginRight: 8, opacity: 0.7 }} />
                <Text style={{ fontSize: 12, width: 28, color: '#6b7280', fontWeight: 'bold', lineHeight: 1.4 }}>{num++ + '.'}</Text>
                <Text style={{ fontSize: 12, flex: 1, color: '#1f2937', fontWeight: 500, lineHeight: 1.4 }}>
                  <Link src="#MobileMethodology" style={{ color: '#1f2937', textDecoration: 'none' }}>Mobile Methodology</Link>
                </Text>
                <Text style={{ fontSize: 12, width: 32, textAlign: 'right', color: accentColor, fontWeight: 'bold', lineHeight: 1.4 }}>{sectionPages['MobileMethodology'] || ''}</Text>
              </View>
            </View>
          );
        }
        return rows;
      })()}
    </Page>
  );
};

export default TableOfContentsPage; 