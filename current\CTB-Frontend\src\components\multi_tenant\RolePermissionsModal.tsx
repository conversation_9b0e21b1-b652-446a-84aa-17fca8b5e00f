import React from "react";
import { UserRole } from "../../utils/api/endpoints/user/credentials";
import useUserCredentials from "../../utils/hooks/user/useUserCredentials";

interface RolePermissionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  userRole: UserRole;
}

const RolePermissionsModal: React.FC<RolePermissionsModalProps> = ({
  isOpen,
  onClose
}) => {
  const { role } = useUserCredentials();

  if (!isOpen) return null;

  const businessRolePermissions = [
    {
      role: "Business Owner (You)",
      permissions: ["All components including Tenant and role Management"]
    },
    {
      role: "Organization Admin",
      permissions: ["All components except Tenant and role Management"]
    },
    {
      role: "Program Manager",
      permissions: ["Dashboard", "Programs"]
    },
    {
      role: "Security Engineer",
      permissions: ["Dashboard", "Reports", "Retest"]
    }
  ];

  const adminRolePermissions = [
    {
      role: "Admin (You)",
      permissions: ["All components including Tenant and role management"]
    },
    {
      role: "Sub Admin",
      permissions: ["All components except Tenant and role management"]
    },
    {
      role: "Admin Manager",
      permissions: ["Dashboard", "Programs", "Users"]
    },
    {
      role: "QA",
      permissions: ["Dashboard", "Programs", "Reports", "Retests"]
    }
  ];

  const rolePermissions =
    role === UserRole.BUSINESS ? businessRolePermissions : adminRolePermissions;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center px-4">
        <div
          className="fixed inset-0 bg-black opacity-30"
          onClick={onClose}
        ></div>
        <div className="relative w-full max-w-3xl rounded-lg bg-white p-6">
          <div className="mb-6 flex items-center justify-between">
            <h2 className="border-b pb-4 text-2xl font-bold text-gray-900">
              Role Access Guide
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
          <div className="space-y-6">
            {rolePermissions.map((item, index) => (
              <div key={index} className="rounded-lg bg-gray-50 p-4">
                <h3 className="mb-2 text-lg font-semibold text-blue-700">
                  {item.role}
                </h3>
                <div className="flex flex-wrap gap-2">
                  {item.permissions.map((permission, pIndex) => (
                    <span
                      key={pIndex}
                      className="rounded-full bg-blue-100 px-3 py-1 text-sm text-blue-800"
                    >
                      {permission}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RolePermissionsModal;
