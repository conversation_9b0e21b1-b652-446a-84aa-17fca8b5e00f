[{"C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\store\\store.ts": "3", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\hooks.ts": "4", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\LandingPage.tsx": "5", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\ErrorPage.tsx": "6", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\Icons.tsx": "7", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\DashboardRoutes.tsx": "8", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\layouts\\GlobalLayout.tsx": "9", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\authentication\\LoginPage.tsx": "10", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\authentication\\ResetPassword.tsx": "11", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\layouts\\AuthenticationLayout.tsx": "12", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\authentication\\RegisterPage.tsx": "13", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\PublicProgramPage.tsx": "14", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\authentication\\ProtectedRoute.tsx": "15", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\common\\AnnouncementPopup.tsx": "16", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\authentication\\InvitedUserRegistrationForm.tsx": "17", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\store\\reducer\\slices\\modalReducer.ts": "18", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\api.ts": "19", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\store\\reducer\\slices\\userReducer.ts": "20", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\store\\reducer\\slices\\userReducerForAdmin.ts": "21", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\usePageTitle.tsx": "22", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CTBIcon.tsx": "23", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\AMLIcon.tsx": "24", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CloseIcon.tsx": "25", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CameraIcon.tsx": "26", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CashIcon.tsx": "27", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\Chevron.tsx": "28", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\user\\credentials.ts": "29", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\DeleteIcon.tsx": "30", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CogIcon.tsx": "31", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\Eye.tsx": "32", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\DashboardIcon.tsx": "33", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\EditIcon.tsx": "34", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CreateIcon.tsx": "35", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\FilterIcon.tsx": "36", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\Flag.tsx": "37", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\HelpIcon.tsx": "38", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\GearIcon.tsx": "39", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ImageIcon.tsx": "40", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\InfoIcon.tsx": "41", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\LeaderboardIcon.tsx": "42", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\LinkedInIcon.tsx": "43", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\LinkIcon.tsx": "44", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\LightBulb.tsx": "45", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\MultipleUsersIcon.tsx": "46", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\MinusCircle.tsx": "47", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\NotifcationUnreadIcon.tsx": "48", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\LocationIcon.tsx": "49", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\NotifcationIcon.tsx": "50", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\PlusIcon.tsx": "51", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\PieChartIcon.tsx": "52", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\PlusOutlineIcon.tsx": "53", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\PatchIcon.tsx": "54", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ProfileIcon.tsx": "55", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\SearchIcon.tsx": "56", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ReportsIcon.tsx": "57", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ProgramsIcon.tsx": "58", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ShieldIcon.tsx": "59", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\SmallCross.tsx": "60", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\SignoutIcon.tsx": "61", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ThumbsUp.tsx": "62", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\VerifiedIcon.tsx": "63", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\UploadIcon.tsx": "64", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ZoomIn.tsx": "65", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\TwitterIcon.tsx": "66", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\layouts\\DashboardLayout.tsx": "67", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ZoomOut.tsx": "68", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\RecentReportsIcon.tsx": "69", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ComputerIcon.tsx": "70", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\SpeakerphoneIcon.tsx": "71", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\users\\AdminRoutes.tsx": "72", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\users\\ResearcherRoutes.tsx": "73", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\user\\useUserCredentials.ts": "74", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\users\\BusinessRoutes.tsx": "75", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\users\\SubAdminRoutes.tsx": "76", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\users\\AdminManagerRoutes.tsx": "77", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\users\\DeveloperRoutes.tsx": "78", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\users\\QARoutes.tsx": "79", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\users\\BusinessManagerRoutes.tsx": "80", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\authentication\\ForgotPassword.tsx": "81", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\authentication\\OTPForm.tsx": "82", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\authentication\\LoginForm.tsx": "83", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\users\\BusinessAdminRoutes.tsx": "84", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\layouts\\TabbedLayout.tsx": "85", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\layouts\\DetailViewLayout.tsx": "86", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\authentication\\ResetPasswordForm.tsx": "87", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\FullProgramDetails.tsx": "88", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\footer\\Footer.tsx": "89", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\authentication\\RegisterForm.tsx": "90", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\programs\\usePublicProgram.ts": "91", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\cards\\BusinessProfileCard.tsx": "92", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\authentication\\Masthead.tsx": "93", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\axios.ts": "94", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\Form.tsx": "95", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\cards\\ProgramInfoCard.tsx": "96", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\user\\invitation.ts": "97", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\store\\reducer\\reducer.ts": "98", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\errors\\ErrorMessage.tsx": "99", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\index.ts": "100", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\user\\user.ts": "101", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\user\\authentication.ts": "102", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\authentication\\inputs\\AuthButton.tsx": "103", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\authentication\\inputs\\AuthTextBox.tsx": "104", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\ProgramEditor.tsx": "105", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\useIsMobile.tsx": "106", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\headers\\DashboardHeader.tsx": "107", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\navigation\\SideNav.tsx": "108", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\admin\\AdminDashboard.tsx": "109", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\ProgramPage.tsx": "110", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\PaginatedPrograms.tsx": "111", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\admin\\AdminProfile.tsx": "112", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\reports\\ReportEditor.tsx": "113", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\assistant\\Assistant.tsx": "114", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\reports\\PaginatedReports.tsx": "115", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\reports\\ReportPage.tsx": "116", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\payments\\PaginatedTransactions.tsx": "117", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\retests\\RetestPage.tsx": "118", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\retests\\RetestManagent.tsx": "119", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\users\\InvitationManager.tsx": "120", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\users\\PaginatedUsers.tsx": "121", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\PDFEditor.tsx": "122", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\PentestReports.tsx": "123", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\researcher\\ResearcherDashboard.tsx": "124", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\researcher\\ReseacherProfile.tsx": "125", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\business\\BusinessProfile.tsx": "126", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\business\\SolidityScan.tsx": "127", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\business\\EnhancedBusinessDashboard.tsx": "128", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CoinIcon.tsx": "129", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\programsApi.ts": "130", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\programs\\useProgram.ts": "131", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\common\\InfoCardContainer.tsx": "132", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\common\\Collapsible.tsx": "133", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\constants.tsx": "134", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\common\\InfoCardRow.tsx": "135", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\programs\\parsePrograms.ts": "136", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\errors\\ErrorMessages.tsx": "137", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\common\\InlineContainer.tsx": "138", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\ProgramStats.tsx": "139", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\Checkbox.tsx": "140", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\business\\generateSummaryReport.ts": "141", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\TextBox.tsx": "142", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\buttons\\OutlineButton.tsx": "143", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\authentication\\inputs\\VisiblityIcon.tsx": "144", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\layouts\\EditorLayout.tsx": "145", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\pages\\researcher\\PaymentMethodSettings.tsx": "146", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\useModal.tsx": "147", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\FormProgressTimeline.tsx": "148", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CircleChevron.tsx": "149", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\navigation\\SideNavData.tsx": "150", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\user\\useUserDetails.ts": "151", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\notifications\\useNotifications.ts": "152", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\dashboard\\useAdminDashboardDetails.ts": "153", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\FormDropdown.tsx": "154", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\NavbarImg.tsx": "155", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\EditorSection.tsx": "156", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\NavbarBanner.tsx": "157", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\programs\\usePrograms.ts": "158", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\EditorRichTextInput.tsx": "159", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\ProgramEditorAttachments.tsx": "160", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\ProgramEditorLogo.tsx": "161", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\EditorTextInput.tsx": "162", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\notifications\\NotificationsModal.tsx": "163", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\EditorInput.tsx": "164", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\ProfileImageSmall.tsx": "165", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\programs\\DropDownEditor.tsx": "166", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\cards\\HamburgerProgram.tsx": "167", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\cards\\RightbarProgram.tsx": "168", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\UserProfile.tsx": "169", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\ProgramQuickView.tsx": "170", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\programs\\TargetsInput.tsx": "171", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\programs\\ProgramRewardsInput.tsx": "172", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\programs\\DatePicker.tsx": "173", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\reports\\useReport.ts": "174", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\cards\\ProgramHighlight.tsx": "175", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\common\\ConfirmationModal.tsx": "176", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\ProfileSection.tsx": "177", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\assistant\\PatchAssistant.tsx": "178", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\assistant\\ChatAssistant.tsx": "179", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\automated-reports\\automated-reports.ts": "180", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\cards\\ProgramCard.tsx": "181", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\ControlledSeverityScoreSelector.tsx": "182", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\reports\\useReports.ts": "183", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\section\\Management.tsx": "184", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\section\\Attachments.tsx": "185", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\reports\\useFilter.ts": "186", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\EditorAttachmentsInput.tsx": "187", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\section\\TermsCondition.tsx": "188", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\section\\ReportTable.tsx": "189", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\section\\RecentActivity.tsx": "190", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\section\\Scope.tsx": "191", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\admin\\ProgramWithResearchersTable.tsx": "192", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\admin\\NumberBoxSectionAdmin.tsx": "193", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\admin\\PentestersLeaderboard.tsx": "194", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\admin\\RetestReportsListTable.tsx": "195", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\admin\\ResearcherPieChart.tsx": "196", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\admin\\UnderReviewReportsListTable.tsx": "197", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\admin\\RetestBarChart.tsx": "198", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\admin\\GroupedSeverityChart.tsx": "199", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\admin\\ProgramGrowthChart.tsx": "200", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\pagination\\PaginationHeader.tsx": "201", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\pagination\\PaginationLayout.tsx": "202", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\filters\\FilterToolBar.tsx": "203", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\filters\\inputs\\FilterDropdown.tsx": "204", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\reports\\ReportTargetSelector.tsx": "205", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\reports\\ReportQuickView.tsx": "206", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\reports\\ReportProgramBanner.tsx": "207", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\DownloadIcon.tsx": "208", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\reports\\cards\\ReportCard.tsx": "209", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\reports\\CategorySelector.tsx": "210", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\reports\\cards\\ProgramProfileCard.tsx": "211", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\reports\\cards\\BugInformationCard.tsx": "212", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\usersApi.ts": "213", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\reports\\ReportProgramSelector.tsx": "214", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\reportCommentsApi.ts": "215", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\utils\\LoadingSpinner.tsx": "216", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\constants\\reportStatusInfo.ts": "217", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\reports\\parseReports.ts": "218", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\reports\\cards\\DocumentationLink.tsx": "219", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\payments\\useTransactions.ts": "220", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\CommentBox.tsx": "221", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\reports\\reports.ts": "222", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\retests\\useReportDetails.ts": "223", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\retests\\useRetestLogs.ts": "224", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\retests\\retests.ts": "225", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\retests\\useRetestStatusRetestId.ts": "226", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\payments\\PaymentStatusPill.tsx": "227", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\table\\EmptyTableRow.tsx": "228", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\retests\\useRetestDetailsRight.ts": "229", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\table\\TableRow.tsx": "230", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\table\\TableContainer.tsx": "231", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\multi_tenant\\SpinnerOverlay.tsx": "232", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\multi_tenant\\ActivityLogsTable.tsx": "233", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\multi_tenant\\DeleteConfirmationModal.tsx": "234", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\multi_tenant\\CreateInvitationForm.tsx": "235", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\multi_tenant\\TabNavigation.tsx": "236", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\multi_tenant\\InvitationsTable.tsx": "237", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\multi_tenant\\RolePermissionsModal.tsx": "238", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\users\\UserTableRow.tsx": "239", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\multi_tenant\\ActivityLogsFilters.tsx": "240", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\multi_tenant\\InvitationsFilters.tsx": "241", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\users\\UserQuickView.tsx": "242", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\PreviewOnly.tsx": "243", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\EditorLayout.tsx": "244", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ReportsCounterIcon.tsx": "245", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\LogsPage\\RetestHeader.tsx": "246", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\LogsPage\\LogList.tsx": "247", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\utils\\NoDataAvailable.tsx": "248", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\LogsPage\\CommentForm.tsx": "249", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\utils\\Pagination.tsx": "250", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\Table\\RetestTable.tsx": "251", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\LogsPage\\RetestDetailsRightSection.tsx": "252", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\multi-tenant\\invitation.ts": "253", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\hooks\\useRoleString.ts": "254", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\user\\useAllUsers.tsx": "255", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\reports\\useReportSummary.ts": "256", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\MoneyPendingIcon.tsx": "257", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ReportsReviewIcon.tsx": "258", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\hooks\\usePDFEditorLogic.ts": "259", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ReportsApprovedIcon.tsx": "260", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\ChatSection.tsx": "261", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\user\\multiTenant.ts": "262", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\ChatModal.tsx": "263", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\common\\StatisticCard.tsx": "264", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\automated-report\\automated-report.ts": "265", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\common\\DashboardItem.tsx": "266", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\common\\DashboardTop.tsx": "267", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\common\\DashboardGrid.tsx": "268", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\solidity_scan\\SolidityScanForm.tsx": "269", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\researcher\\RecentReports.tsx": "270", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\researcher\\RecentPrograms.tsx": "271", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\sections\\About.tsx": "272", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\dashboard\\useDashboardSummary.ts": "273", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\sections\\PillsWrapper.tsx": "274", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\sections\\Links.tsx": "275", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\dashboard\\useUnifiedBusinessDashboard.ts": "276", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\solidity_scan\\SolidityScanResult.tsx": "277", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\dashboard\\useSeverityAndTrend.ts": "278", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\business\\solidityScan.ts": "279", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\business\\EnhancedDashboardStats.tsx": "280", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\business\\EnhancedTrendLineChart.tsx": "281", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\business\\EnhancedSeverityDonut.tsx": "282", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\business\\ActionableReportsSection.tsx": "283", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\business\\RetestingSummarySection.tsx": "284", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\business\\RetestActionsTable.tsx": "285", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\charts\\DoughnutChart.tsx": "286", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\pills\\Pill.tsx": "287", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\modal\\QuickViewModal.tsx": "288", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\sanitizers.tsx": "289", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\EditorTracker.tsx": "290", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\useEditorSections.tsx": "291", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\RetestIcon.tsx": "292", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\common\\ProgressTimeline.tsx": "293", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\Hamburger.tsx": "294", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\notifications\\NotificationRow.tsx": "295", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\modal\\RelativeModalContainer.tsx": "296", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\ProfileLinks.tsx": "297", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\SettingsModal.tsx": "298", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\UserBanner.tsx": "299", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\buttons\\PrimaryButton.tsx": "300", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\PayoutRangeDisplay.tsx": "301", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\ProfileQuickLook.tsx": "302", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\formatText.tsx": "303", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\categoryUtils.ts": "304", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\paymentsApi.ts": "305", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\notificationsApi.ts": "306", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\inputs\\InlineTabSelector.tsx": "307", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\RichTextArea.tsx": "308", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\AttachmentsInput.tsx": "309", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\ControlledDropdown.tsx": "310", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\reportsApi.ts": "311", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\ProgramTargets.tsx": "312", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\pagination\\PaginatedCardContainer.tsx": "313", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\ManageProgram.tsx": "314", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\BrowserCodeIcon.tsx": "315", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CircleCodeIcon.tsx": "316", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\SortArrows.tsx": "317", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\dashboard\\adminDashboard.ts": "318", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\dashboard\\adminDashboardParser.ts": "319", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\programs\\programFullview.ts": "320", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\reports\\getReport.ts": "321", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\business\\assistant.ts": "322", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\cards\\ProgramInfoSidebar.tsx": "323", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\cards\\RecentUpdatesRightbar.tsx": "324", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\cards\\ActionButtons.tsx": "325", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\SeverityScoreSelector.tsx": "326", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\pagination\\PageSelector.tsx": "327", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\zipAttachments.tsx": "328", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\reports\\VulnerabilityFlag.tsx": "329", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\reports\\ReportQuickProgramBanner.tsx": "330", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\pages\\researcher\\paymentMethodForms\\BankAcount.tsx": "331", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\pages\\researcher\\paymentMethodForms\\Paypal.tsx": "332", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\pages\\researcher\\paymentMethodForms\\OrbitRemix.tsx": "333", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\programs\\ProgramTargetsEditor.tsx": "334", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\reports\\scopeStats.ts": "335", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\reports\\target.ts": "336", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\retests\\retestDetails.ts": "337", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\reports\\ReportStatusPill.tsx": "338", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\table\\useTableDropdown.tsx": "339", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\EditorDropdown.tsx": "340", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\reports\\utils\\FIlterSectionReports.tsx": "341", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\CommentRichTextArea.tsx": "342", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\multi_tenant\\SearchBar.tsx": "343", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\buttons\\CloseButton.tsx": "344", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\retests\\useCommentForm.ts": "345", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\pills\\PillList.tsx": "346", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\ToggleRow.tsx": "347", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\ReportTemplate.tsx": "348", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\PreviewSection.tsx": "349", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\ChangeLogSidebar.tsx": "350", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\SectionRenderer.tsx": "351", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\UpArrowIcon.tsx": "352", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\DownArrowIcon.tsx": "353", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ResetIcon.tsx": "354", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\PDFDownloader.tsx": "355", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\EditorNavigation.tsx": "356", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\LogsPage\\LogItems.tsx": "357", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\utils\\StatusFormatter.tsx": "358", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\utils\\FilterSection.tsx": "359", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\Table\\RetestTableRow.tsx": "360", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\dashboard\\useFilteredSeverityOverview.ts": "361", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\headers\\DashboardTitle.tsx": "362", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\utils\\searchBar.tsx": "363", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\dashboard\\dashboardApi.ts": "364", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\common\\DashboardItemCard.tsx": "365", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\business\\BugLifecycleProgress.tsx": "366", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\program-reports\\program-reports.ts": "367", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\chat\\chat.ts": "368", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\modal\\FullscreenModalLayout.tsx": "369", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\PaymentIcon.tsx": "370", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\RejectedIcon.tsx": "371", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CommentIcon.tsx": "372", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CertificateIcon.tsx": "373", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\NotificationInfoIcon.tsx": "374", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\ProfileLink.tsx": "375", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\SettingsNav.tsx": "376", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ApprovedIcon.tsx": "377", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\SettingsPageLayout.tsx": "378", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\BrowseIcon.tsx": "379", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\file_upload\\useImageCrop.tsx": "380", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\cvss31\\cvsscalc31.js": "381", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\pages\\Security.tsx": "382", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\pages\\Account.tsx": "383", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\user\\usePrivateAccessUsers.tsx": "384", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\ExternalLink.tsx": "385", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\thirdPartyIntegrationsApi.ts": "386", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\ControlledButtonOptions.tsx": "387", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\pages\\business\\EditBusinessProfile.tsx": "388", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\pages\\researcher\\Verification.tsx": "389", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\pages\\researcher\\EditResearcherProfile.tsx": "390", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\DualProfileQuickLook.tsx": "391", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\inputs\\SettingsTextBox.tsx": "392", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\reports\\parseTarget.ts": "393", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\retests\\parseRetests.ts": "394", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\table\\TableDropdown.tsx": "395", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\pills\\PillInput.tsx": "396", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\Toggle.tsx": "397", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\ReportTemplatePDF.tsx": "398", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\pdfHtmlImageUtils.tsx": "399", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\ChartImageGenerator.tsx": "400", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\FullReportTemplatePDF.tsx": "401", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\utils\\helper.ts": "402", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\MethodologyEditor.tsx": "403", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\CoverPageEditor.tsx": "404", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\DisclaimerEditor.tsx": "405", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\ExecutiveSummaryEditor.tsx": "406", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\ConclusionEditor.tsx": "407", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\RecommendationsEditor.tsx": "408", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\DocumentReferenceEditor.tsx": "409", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\FindingsSummaryEditor.tsx": "410", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\ProjectObjectivesEditor.tsx": "411", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\FindingsEditor.tsx": "412", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\ScopeEditor.tsx": "413", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\VulnerabilityRatingsEditor.tsx": "414", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\HighFindingsEditor.tsx": "415", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\LowFindingsEditor.tsx": "416", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\BrandingEditor.tsx": "417", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\MediumFindingsEditor.tsx": "418", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\CriticalFindingsEditor.tsx": "419", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\KeyFindingsEditor.tsx": "420", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\UnlockedIcon.tsx": "421", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\LockIcon.tsx": "422", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\MethodologySelectorEditor.tsx": "423", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\SettingsTitle.tsx": "424", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\common\\HelpText.tsx": "425", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\file_upload\\ImageCrop.tsx": "426", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\pages\\EditProfile.tsx": "427", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\inputs\\SettingsTextArea.tsx": "428", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\ProfileImage.tsx": "429", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\SectionPageContext.tsx": "430", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\DisclaimerPage.tsx": "431", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\CoverPage.tsx": "432", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\DocumentReferencePage.tsx": "433", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\ScopePage.tsx": "434", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\KeyFindingsPage.tsx": "435", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\TableOfContentsPage.tsx": "436", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\SummaryOfFindingsPage.tsx": "437", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\ProjectObjectivesPage.tsx": "438", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\ExecutiveSummaryPage.tsx": "439", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\KeyFindingsListPage.tsx": "440", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\VulnerabilityRatingDefinitionsPage.tsx": "441", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\MethodologyPage.tsx": "442", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\NetworkMethodologyPage.tsx": "443", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\AppendixOwaspRiskRatingPages.tsx": "444", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\HtmlEditor.tsx": "445", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\DetailedFindingsCoverPage.tsx": "446", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\DetailedFindingsBySeverityPage.tsx": "447", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\MobileMethodologyPage.tsx": "448", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\FullTableOfContentsPage.tsx": "449", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\RecommendationsPage.tsx": "450", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\BaseFindingEditor.tsx": "451", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\TextArea.tsx": "452", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\inputs\\ProfileImageUploader.tsx": "453", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\utils\\defaultContent.ts": "454", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\FileInput.tsx": "455", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\types.ts": "456", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\file_upload\\FileDropZone.tsx": "457", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\useAcceptedFileTypes.tsx": "458", "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\modals\\JiraSetupGuideModal.tsx": "459"}, {"size": 1128, "mtime": 1746434607502, "results": "460", "hashOfConfig": "461"}, {"size": 3388, "mtime": 1753179121864, "results": "462", "hashOfConfig": "461"}, {"size": 1014, "mtime": 1746435054547, "results": "463", "hashOfConfig": "461"}, {"size": 332, "mtime": 1746434607544, "results": "464", "hashOfConfig": "461"}, {"size": 557, "mtime": 1746434607509, "results": "465", "hashOfConfig": "461"}, {"size": 753, "mtime": 1746435054495, "results": "466", "hashOfConfig": "461"}, {"size": 5995, "mtime": 1746434607508, "results": "467", "hashOfConfig": "461"}, {"size": 2378, "mtime": 1746435054511, "results": "468", "hashOfConfig": "461"}, {"size": 344, "mtime": 1746434607506, "results": "469", "hashOfConfig": "461"}, {"size": 992, "mtime": 1746434607513, "results": "470", "hashOfConfig": "461"}, {"size": 745, "mtime": 1746434607515, "results": "471", "hashOfConfig": "461"}, {"size": 442, "mtime": 1746434607503, "results": "472", "hashOfConfig": "461"}, {"size": 870, "mtime": 1746434607513, "results": "473", "hashOfConfig": "461"}, {"size": 2277, "mtime": 1746434607521, "results": "474", "hashOfConfig": "461"}, {"size": 768, "mtime": 1746434607528, "results": "475", "hashOfConfig": "461"}, {"size": 4369, "mtime": 1753179121873, "results": "476", "hashOfConfig": "461"}, {"size": 5926, "mtime": 1746435054414, "results": "477", "hashOfConfig": "461"}, {"size": 989, "mtime": 1746434607559, "results": "478", "hashOfConfig": "461"}, {"size": 994, "mtime": 1746435054517, "results": "479", "hashOfConfig": "461"}, {"size": 5912, "mtime": 1746435054546, "results": "480", "hashOfConfig": "461"}, {"size": 2176, "mtime": 1746435054546, "results": "481", "hashOfConfig": "461"}, {"size": 282, "mtime": 1746434607554, "results": "482", "hashOfConfig": "461"}, {"size": 720, "mtime": 1746435054404, "results": "483", "hashOfConfig": "461"}, {"size": 10262, "mtime": 1746434607351, "results": "484", "hashOfConfig": "461"}, {"size": 449, "mtime": 1746434607354, "results": "485", "hashOfConfig": "461"}, {"size": 1761, "mtime": 1746434607353, "results": "486", "hashOfConfig": "461"}, {"size": 1232, "mtime": 1746434607353, "results": "487", "hashOfConfig": "461"}, {"size": 455, "mtime": 1746434607354, "results": "488", "hashOfConfig": "461"}, {"size": 2418, "mtime": 1746435054531, "results": "489", "hashOfConfig": "461"}, {"size": 795, "mtime": 1746434607359, "results": "490", "hashOfConfig": "461"}, {"size": 2568, "mtime": 1746434607356, "results": "491", "hashOfConfig": "461"}, {"size": 1026, "mtime": 1746434607361, "results": "492", "hashOfConfig": "461"}, {"size": 825, "mtime": 1746435054405, "results": "493", "hashOfConfig": "461"}, {"size": 1118, "mtime": 1746434607361, "results": "494", "hashOfConfig": "461"}, {"size": 1484, "mtime": 1746434607359, "results": "495", "hashOfConfig": "461"}, {"size": 763, "mtime": 1746434607362, "results": "496", "hashOfConfig": "461"}, {"size": 1172, "mtime": 1746434607362, "results": "497", "hashOfConfig": "461"}, {"size": 1139, "mtime": 1746434607364, "results": "498", "hashOfConfig": "461"}, {"size": 2468, "mtime": 1746434607363, "results": "499", "hashOfConfig": "461"}, {"size": 1009, "mtime": 1746434607364, "results": "500", "hashOfConfig": "461"}, {"size": 890, "mtime": 1746434607365, "results": "501", "hashOfConfig": "461"}, {"size": 610, "mtime": 1746434607365, "results": "502", "hashOfConfig": "461"}, {"size": 1767, "mtime": 1746434607367, "results": "503", "hashOfConfig": "461"}, {"size": 3526, "mtime": 1746434607366, "results": "504", "hashOfConfig": "461"}, {"size": 1166, "mtime": 1746434607366, "results": "505", "hashOfConfig": "461"}, {"size": 2024, "mtime": 1746434607369, "results": "506", "hashOfConfig": "461"}, {"size": 1057, "mtime": 1746434607368, "results": "507", "hashOfConfig": "461"}, {"size": 1064, "mtime": 1746434607370, "results": "508", "hashOfConfig": "461"}, {"size": 1192, "mtime": 1746434607367, "results": "509", "hashOfConfig": "461"}, {"size": 986, "mtime": 1746434607370, "results": "510", "hashOfConfig": "461"}, {"size": 1359, "mtime": 1746434607373, "results": "511", "hashOfConfig": "461"}, {"size": 1034, "mtime": 1746434607372, "results": "512", "hashOfConfig": "461"}, {"size": 413, "mtime": 1746434607373, "results": "513", "hashOfConfig": "461"}, {"size": 1930, "mtime": 1746434607371, "results": "514", "hashOfConfig": "461"}, {"size": 633, "mtime": 1746435054408, "results": "515", "hashOfConfig": "461"}, {"size": 950, "mtime": 1746434607380, "results": "516", "hashOfConfig": "461"}, {"size": 607, "mtime": 1746435054410, "results": "517", "hashOfConfig": "461"}, {"size": 852, "mtime": 1746435054408, "results": "518", "hashOfConfig": "461"}, {"size": 668, "mtime": 1746434607381, "results": "519", "hashOfConfig": "461"}, {"size": 1842, "mtime": 1746434607382, "results": "520", "hashOfConfig": "461"}, {"size": 540, "mtime": 1746435054411, "results": "521", "hashOfConfig": "461"}, {"size": 1150, "mtime": 1746434607382, "results": "522", "hashOfConfig": "461"}, {"size": 1220, "mtime": 1746434607386, "results": "523", "hashOfConfig": "461"}, {"size": 1081, "mtime": 1746434607385, "results": "524", "hashOfConfig": "461"}, {"size": 1119, "mtime": 1746434607386, "results": "525", "hashOfConfig": "461"}, {"size": 526, "mtime": 1746434607384, "results": "526", "hashOfConfig": "461"}, {"size": 1317, "mtime": 1746435054493, "results": "527", "hashOfConfig": "461"}, {"size": 1027, "mtime": 1746434607387, "results": "528", "hashOfConfig": "461"}, {"size": 1310, "mtime": 1746434607376, "results": "529", "hashOfConfig": "461"}, {"size": 1093, "mtime": 1746434607357, "results": "530", "hashOfConfig": "461"}, {"size": 1189, "mtime": 1746434607382, "results": "531", "hashOfConfig": "461"}, {"size": 2318, "mtime": 1753179122002, "results": "532", "hashOfConfig": "461"}, {"size": 1692, "mtime": 1746435054516, "results": "533", "hashOfConfig": "461"}, {"size": 1618, "mtime": 1746434607555, "results": "534", "hashOfConfig": "461"}, {"size": 2470, "mtime": 1753179122008, "results": "535", "hashOfConfig": "461"}, {"size": 2324, "mtime": 1753179122011, "results": "536", "hashOfConfig": "461"}, {"size": 2332, "mtime": 1753179122002, "results": "537", "hashOfConfig": "461"}, {"size": 2206, "mtime": 1753179122009, "results": "538", "hashOfConfig": "461"}, {"size": 2312, "mtime": 1753179122010, "results": "539", "hashOfConfig": "461"}, {"size": 2032, "mtime": 1753179122006, "results": "540", "hashOfConfig": "461"}, {"size": 2311, "mtime": 1746434607404, "results": "541", "hashOfConfig": "461"}, {"size": 1749, "mtime": 1746434607405, "results": "542", "hashOfConfig": "461"}, {"size": 2331, "mtime": 1746434607404, "results": "543", "hashOfConfig": "461"}, {"size": 2028, "mtime": 1753179122005, "results": "544", "hashOfConfig": "461"}, {"size": 3411, "mtime": 1746435054495, "results": "545", "hashOfConfig": "461"}, {"size": 970, "mtime": 1746435054494, "results": "546", "hashOfConfig": "461"}, {"size": 2932, "mtime": 1746434607406, "results": "547", "hashOfConfig": "461"}, {"size": 4335, "mtime": 1746435054462, "results": "548", "hashOfConfig": "461"}, {"size": 1587, "mtime": 1746434607437, "results": "549", "hashOfConfig": "461"}, {"size": 6640, "mtime": 1746435054415, "results": "550", "hashOfConfig": "461"}, {"size": 1151, "mtime": 1753208874500, "results": "551", "hashOfConfig": "461"}, {"size": 1551, "mtime": 1746435054465, "results": "552", "hashOfConfig": "461"}, {"size": 813, "mtime": 1746434607405, "results": "553", "hashOfConfig": "461"}, {"size": 1823, "mtime": 1746434607532, "results": "554", "hashOfConfig": "461"}, {"size": 1954, "mtime": 1746435054438, "results": "555", "hashOfConfig": "461"}, {"size": 5388, "mtime": 1746435054467, "results": "556", "hashOfConfig": "461"}, {"size": 1374, "mtime": 1746435054531, "results": "557", "hashOfConfig": "461"}, {"size": 186, "mtime": 1746434607557, "results": "558", "hashOfConfig": "461"}, {"size": 675, "mtime": 1746434607440, "results": "559", "hashOfConfig": "461"}, {"size": 396, "mtime": 1746434607392, "results": "560", "hashOfConfig": "461"}, {"size": 8951, "mtime": 1746435054532, "results": "561", "hashOfConfig": "461"}, {"size": 4476, "mtime": 1746434607539, "results": "562", "hashOfConfig": "461"}, {"size": 542, "mtime": 1746434607407, "results": "563", "hashOfConfig": "461"}, {"size": 1510, "mtime": 1746434607407, "results": "564", "hashOfConfig": "461"}, {"size": 45807, "mtime": 1754374327737, "results": "565", "hashOfConfig": "461"}, {"size": 508, "mtime": 1746434607553, "results": "566", "hashOfConfig": "461"}, {"size": 2303, "mtime": 1746435054446, "results": "567", "hashOfConfig": "461"}, {"size": 4822, "mtime": 1746435054454, "results": "568", "hashOfConfig": "461"}, {"size": 4928, "mtime": 1746435054497, "results": "569", "hashOfConfig": "461"}, {"size": 16608, "mtime": 1753179121992, "results": "570", "hashOfConfig": "461"}, {"size": 5480, "mtime": 1753179121992, "results": "571", "hashOfConfig": "461"}, {"size": 2285, "mtime": 1746434607510, "results": "572", "hashOfConfig": "461"}, {"size": 7098, "mtime": 1746435054506, "results": "573", "hashOfConfig": "461"}, {"size": 440, "mtime": 1746434607511, "results": "574", "hashOfConfig": "461"}, {"size": 4853, "mtime": 1753179121992, "results": "575", "hashOfConfig": "461"}, {"size": 40582, "mtime": 1753179121999, "results": "576", "hashOfConfig": "461"}, {"size": 3034, "mtime": 1746435054499, "results": "577", "hashOfConfig": "461"}, {"size": 4277, "mtime": 1746435054510, "results": "578", "hashOfConfig": "461"}, {"size": 3380, "mtime": 1753179121999, "results": "579", "hashOfConfig": "461"}, {"size": 12804, "mtime": 1754373173353, "results": "580", "hashOfConfig": "461"}, {"size": 4907, "mtime": 1746435054511, "results": "581", "hashOfConfig": "461"}, {"size": 29415, "mtime": 1754373173357, "results": "582", "hashOfConfig": "461"}, {"size": 35316, "mtime": 1753179121915, "results": "583", "hashOfConfig": "461"}, {"size": 3248, "mtime": 1746434607525, "results": "584", "hashOfConfig": "461"}, {"size": 3754, "mtime": 1746435054509, "results": "585", "hashOfConfig": "461"}, {"size": 555, "mtime": 1746434607516, "results": "586", "hashOfConfig": "461"}, {"size": 4876, "mtime": 1746434607517, "results": "587", "hashOfConfig": "461"}, {"size": 7782, "mtime": 1753179121989, "results": "588", "hashOfConfig": "461"}, {"size": 1058, "mtime": 1746434607356, "results": "589", "hashOfConfig": "461"}, {"size": 6997, "mtime": 1753208899919, "results": "590", "hashOfConfig": "461"}, {"size": 7499, "mtime": 1754373173398, "results": "591", "hashOfConfig": "461"}, {"size": 598, "mtime": 1746435054416, "results": "592", "hashOfConfig": "461"}, {"size": 3110, "mtime": 1746434607412, "results": "593", "hashOfConfig": "461"}, {"size": 2421, "mtime": 1753179122025, "results": "594", "hashOfConfig": "461"}, {"size": 562, "mtime": 1746435054417, "results": "595", "hashOfConfig": "461"}, {"size": 10158, "mtime": 1754373968379, "results": "596", "hashOfConfig": "461"}, {"size": 1259, "mtime": 1746434607441, "results": "597", "hashOfConfig": "461"}, {"size": 1145, "mtime": 1746434607414, "results": "598", "hashOfConfig": "461"}, {"size": 5275, "mtime": 1746434607475, "results": "599", "hashOfConfig": "461"}, {"size": 1752, "mtime": 1746435054440, "results": "600", "hashOfConfig": "461"}, {"size": 576, "mtime": 1746434607533, "results": "601", "hashOfConfig": "461"}, {"size": 1018, "mtime": 1746446101722, "results": "602", "hashOfConfig": "461"}, {"size": 902, "mtime": 1746435054416, "results": "603", "hashOfConfig": "461"}, {"size": 497, "mtime": 1746434607408, "results": "604", "hashOfConfig": "461"}, {"size": 1149, "mtime": 1746435054494, "results": "605", "hashOfConfig": "461"}, {"size": 1668, "mtime": 1746434607493, "results": "606", "hashOfConfig": "461"}, {"size": 892, "mtime": 1746434607554, "results": "607", "hashOfConfig": "461"}, {"size": 3199, "mtime": 1753208334998, "results": "608", "hashOfConfig": "461"}, {"size": 738, "mtime": 1746435054404, "results": "609", "hashOfConfig": "461"}, {"size": 10081, "mtime": 1753179121888, "results": "610", "hashOfConfig": "461"}, {"size": 11530, "mtime": 1746435054543, "results": "611", "hashOfConfig": "461"}, {"size": 948, "mtime": 1746435054537, "results": "612", "hashOfConfig": "461"}, {"size": 1226, "mtime": 1746435054535, "results": "613", "hashOfConfig": "461"}, {"size": 2794, "mtime": 1746435054443, "results": "614", "hashOfConfig": "461"}, {"size": 787, "mtime": 1746435054407, "results": "615", "hashOfConfig": "461"}, {"size": 442, "mtime": 1746435054427, "results": "616", "hashOfConfig": "461"}, {"size": 977, "mtime": 1746435054407, "results": "617", "hashOfConfig": "461"}, {"size": 943, "mtime": 1746435054538, "results": "618", "hashOfConfig": "461"}, {"size": 671, "mtime": 1746434607425, "results": "619", "hashOfConfig": "461"}, {"size": 6508, "mtime": 1746435054431, "results": "620", "hashOfConfig": "461"}, {"size": 4726, "mtime": 1746435054431, "results": "621", "hashOfConfig": "461"}, {"size": 766, "mtime": 1746525200236, "results": "622", "hashOfConfig": "461"}, {"size": 2130, "mtime": 1746434607458, "results": "623", "hashOfConfig": "461"}, {"size": 1213, "mtime": 1746435054430, "results": "624", "hashOfConfig": "461"}, {"size": 342, "mtime": 1746434607467, "results": "625", "hashOfConfig": "461"}, {"size": 1334, "mtime": 1746435054432, "results": "626", "hashOfConfig": "461"}, {"size": 2180, "mtime": 1746435054465, "results": "627", "hashOfConfig": "461"}, {"size": 10794, "mtime": 1753179121897, "results": "628", "hashOfConfig": "461"}, {"size": 3385, "mtime": 1746435054460, "results": "629", "hashOfConfig": "461"}, {"size": 2909, "mtime": 1746435054464, "results": "630", "hashOfConfig": "461"}, {"size": 900, "mtime": 1746435054435, "results": "631", "hashOfConfig": "461"}, {"size": 3075, "mtime": 1746434607429, "results": "632", "hashOfConfig": "461"}, {"size": 1490, "mtime": 1746435054432, "results": "633", "hashOfConfig": "461"}, {"size": 5644, "mtime": 1753179122033, "results": "634", "hashOfConfig": "461"}, {"size": 6551, "mtime": 1746435054466, "results": "635", "hashOfConfig": "461"}, {"size": 2372, "mtime": 1753179121873, "results": "636", "hashOfConfig": "461"}, {"size": 513, "mtime": 1746434607470, "results": "637", "hashOfConfig": "461"}, {"size": 2485, "mtime": 1753179121911, "results": "638", "hashOfConfig": "461"}, {"size": 3995, "mtime": 1746434607511, "results": "639", "hashOfConfig": "461"}, {"size": 1163, "mtime": 1753179122012, "results": "640", "hashOfConfig": "461"}, {"size": 3144, "mtime": 1746435054466, "results": "641", "hashOfConfig": "461"}, {"size": 913, "mtime": 1746434607444, "results": "642", "hashOfConfig": "461"}, {"size": 634, "mtime": 1753179122035, "results": "643", "hashOfConfig": "461"}, {"size": 1788, "mtime": 1746435054503, "results": "644", "hashOfConfig": "461"}, {"size": 3403, "mtime": 1746435054502, "results": "645", "hashOfConfig": "461"}, {"size": 784, "mtime": 1746435054538, "results": "646", "hashOfConfig": "461"}, {"size": 1566, "mtime": 1746435054429, "results": "647", "hashOfConfig": "461"}, {"size": 688, "mtime": 1746435054505, "results": "648", "hashOfConfig": "461"}, {"size": 7352, "mtime": 1753179121992, "results": "649", "hashOfConfig": "461"}, {"size": 8333, "mtime": 1746435054503, "results": "650", "hashOfConfig": "461"}, {"size": 8026, "mtime": 1746435054505, "results": "651", "hashOfConfig": "461"}, {"size": 6601, "mtime": 1746435054420, "results": "652", "hashOfConfig": "461"}, {"size": 8484, "mtime": 1746435054419, "results": "653", "hashOfConfig": "461"}, {"size": 8061, "mtime": 1746435054419, "results": "654", "hashOfConfig": "461"}, {"size": 3798, "mtime": 1746435054423, "results": "655", "hashOfConfig": "461"}, {"size": 4572, "mtime": 1746435054422, "results": "656", "hashOfConfig": "461"}, {"size": 4486, "mtime": 1746435054423, "results": "657", "hashOfConfig": "461"}, {"size": 8815, "mtime": 1746435054422, "results": "658", "hashOfConfig": "461"}, {"size": 9766, "mtime": 1746435054418, "results": "659", "hashOfConfig": "461"}, {"size": 5367, "mtime": 1746435054420, "results": "660", "hashOfConfig": "461"}, {"size": 805, "mtime": 1746435054456, "results": "661", "hashOfConfig": "461"}, {"size": 1225, "mtime": 1746435054457, "results": "662", "hashOfConfig": "461"}, {"size": 1681, "mtime": 1746435054437, "results": "663", "hashOfConfig": "461"}, {"size": 951, "mtime": 1746435054437, "results": "664", "hashOfConfig": "461"}, {"size": 838, "mtime": 1746434607431, "results": "665", "hashOfConfig": "461"}, {"size": 6164, "mtime": 1753769613369, "results": "666", "hashOfConfig": "461"}, {"size": 4101, "mtime": 1753179121897, "results": "667", "hashOfConfig": "461"}, {"size": 1516, "mtime": 1746434607360, "results": "668", "hashOfConfig": "461"}, {"size": 25926, "mtime": 1753769613371, "results": "669", "hashOfConfig": "461"}, {"size": 13244, "mtime": 1746435054435, "results": "670", "hashOfConfig": "461"}, {"size": 1910, "mtime": 1753179121903, "results": "671", "hashOfConfig": "461"}, {"size": 1666, "mtime": 1753616937209, "results": "672", "hashOfConfig": "461"}, {"size": 4607, "mtime": 1746434607542, "results": "673", "hashOfConfig": "461"}, {"size": 2820, "mtime": 1746434607431, "results": "674", "hashOfConfig": "461"}, {"size": 2913, "mtime": 1746435054523, "results": "675", "hashOfConfig": "461"}, {"size": 306, "mtime": 1746435054484, "results": "676", "hashOfConfig": "461"}, {"size": 4897, "mtime": 1753179122027, "results": "677", "hashOfConfig": "461"}, {"size": 5250, "mtime": 1753769613369, "results": "678", "hashOfConfig": "461"}, {"size": 492, "mtime": 1746435054475, "results": "679", "hashOfConfig": "461"}, {"size": 733, "mtime": 1746434607546, "results": "680", "hashOfConfig": "461"}, {"size": 7718, "mtime": 1753179121888, "results": "681", "hashOfConfig": "461"}, {"size": 85975, "mtime": 1753179122019, "results": "682", "hashOfConfig": "461"}, {"size": 1489, "mtime": 1746435054541, "results": "683", "hashOfConfig": "461"}, {"size": 1078, "mtime": 1746435054542, "results": "684", "hashOfConfig": "461"}, {"size": 3150, "mtime": 1746435054529, "results": "685", "hashOfConfig": "461"}, {"size": 1093, "mtime": 1746435054543, "results": "686", "hashOfConfig": "461"}, {"size": 1045, "mtime": 1746434607462, "results": "687", "hashOfConfig": "461"}, {"size": 249, "mtime": 1746434607497, "results": "688", "hashOfConfig": "461"}, {"size": 1026, "mtime": 1746435054542, "results": "689", "hashOfConfig": "461"}, {"size": 542, "mtime": 1746434607499, "results": "690", "hashOfConfig": "461"}, {"size": 1500, "mtime": 1746435054490, "results": "691", "hashOfConfig": "461"}, {"size": 319, "mtime": 1746435054453, "results": "692", "hashOfConfig": "461"}, {"size": 5524, "mtime": 1746435054448, "results": "693", "hashOfConfig": "461"}, {"size": 1186, "mtime": 1746435054450, "results": "694", "hashOfConfig": "461"}, {"size": 2857, "mtime": 1746435054450, "results": "695", "hashOfConfig": "461"}, {"size": 862, "mtime": 1746435054454, "results": "696", "hashOfConfig": "461"}, {"size": 7100, "mtime": 1746435054451, "results": "697", "hashOfConfig": "461"}, {"size": 3513, "mtime": 1746435054453, "results": "698", "hashOfConfig": "461"}, {"size": 1567, "mtime": 1746434607501, "results": "699", "hashOfConfig": "461"}, {"size": 2920, "mtime": 1746435054448, "results": "700", "hashOfConfig": "461"}, {"size": 1659, "mtime": 1746435054450, "results": "701", "hashOfConfig": "461"}, {"size": 4195, "mtime": 1746435054491, "results": "702", "hashOfConfig": "461"}, {"size": 6103, "mtime": 1753179121919, "results": "703", "hashOfConfig": "461"}, {"size": 14885, "mtime": 1753179121913, "results": "704", "hashOfConfig": "461"}, {"size": 1308, "mtime": 1746434607377, "results": "705", "hashOfConfig": "461"}, {"size": 7020, "mtime": 1746435054481, "results": "706", "hashOfConfig": "461"}, {"size": 1840, "mtime": 1746435054480, "results": "707", "hashOfConfig": "461"}, {"size": 124, "mtime": 1746435054484, "results": "708", "hashOfConfig": "461"}, {"size": 14422, "mtime": 1753179121910, "results": "709", "hashOfConfig": "461"}, {"size": 3169, "mtime": 1746435054485, "results": "710", "hashOfConfig": "461"}, {"size": 5899, "mtime": 1746435054481, "results": "711", "hashOfConfig": "461"}, {"size": 3119, "mtime": 1746435054480, "results": "712", "hashOfConfig": "461"}, {"size": 2709, "mtime": 1746435054536, "results": "713", "hashOfConfig": "461"}, {"size": 605, "mtime": 1753179121985, "results": "714", "hashOfConfig": "461"}, {"size": 1236, "mtime": 1746434607555, "results": "715", "hashOfConfig": "461"}, {"size": 1254, "mtime": 1746434607550, "results": "716", "hashOfConfig": "461"}, {"size": 1190, "mtime": 1746434607369, "results": "717", "hashOfConfig": "461"}, {"size": 1334, "mtime": 1746434607379, "results": "718", "hashOfConfig": "461"}, {"size": 25924, "mtime": 1754373173389, "results": "719", "hashOfConfig": "461"}, {"size": 1266, "mtime": 1746434607377, "results": "720", "hashOfConfig": "461"}, {"size": 6983, "mtime": 1753179121926, "results": "721", "hashOfConfig": "461"}, {"size": 605, "mtime": 1746435054531, "results": "722", "hashOfConfig": "461"}, {"size": 1260, "mtime": 1753179121926, "results": "723", "hashOfConfig": "461"}, {"size": 1295, "mtime": 1746434607419, "results": "724", "hashOfConfig": "461"}, {"size": 2174, "mtime": 1753179122012, "results": "725", "hashOfConfig": "461"}, {"size": 1213, "mtime": 1746434607416, "results": "726", "hashOfConfig": "461"}, {"size": 858, "mtime": 1746434607418, "results": "727", "hashOfConfig": "461"}, {"size": 506, "mtime": 1746434607416, "results": "728", "hashOfConfig": "461"}, {"size": 1945, "mtime": 1746434607495, "results": "729", "hashOfConfig": "461"}, {"size": 1211, "mtime": 1753179121883, "results": "730", "hashOfConfig": "461"}, {"size": 1117, "mtime": 1746434607420, "results": "731", "hashOfConfig": "461"}, {"size": 368, "mtime": 1746434607471, "results": "732", "hashOfConfig": "461"}, {"size": 4025, "mtime": 1753179122030, "results": "733", "hashOfConfig": "461"}, {"size": 765, "mtime": 1746435054461, "results": "734", "hashOfConfig": "461"}, {"size": 1166, "mtime": 1746434607472, "results": "735", "hashOfConfig": "461"}, {"size": 6254, "mtime": 1753179122031, "results": "736", "hashOfConfig": "461"}, {"size": 6123, "mtime": 1746434607497, "results": "737", "hashOfConfig": "461"}, {"size": 4253, "mtime": 1753179122031, "results": "738", "hashOfConfig": "461"}, {"size": 392, "mtime": 1746434607534, "results": "739", "hashOfConfig": "461"}, {"size": 8847, "mtime": 1753179121876, "results": "740", "hashOfConfig": "461"}, {"size": 13853, "mtime": 1753179121880, "results": "741", "hashOfConfig": "461"}, {"size": 36607, "mtime": 1753179121876, "results": "742", "hashOfConfig": "461"}, {"size": 32501, "mtime": 1753179121873, "results": "743", "hashOfConfig": "461"}, {"size": 25045, "mtime": 1753179121883, "results": "744", "hashOfConfig": "461"}, {"size": 20857, "mtime": 1753179121880, "results": "745", "hashOfConfig": "461"}, {"size": 1881, "mtime": 1746434607411, "results": "746", "hashOfConfig": "461"}, {"size": 418, "mtime": 1746435054459, "results": "747", "hashOfConfig": "461"}, {"size": 1536, "mtime": 1746435054447, "results": "748", "hashOfConfig": "461"}, {"size": 3357, "mtime": 1746434607557, "results": "749", "hashOfConfig": "461"}, {"size": 4003, "mtime": 1746435054427, "results": "750", "hashOfConfig": "461"}, {"size": 2585, "mtime": 1746434607432, "results": "751", "hashOfConfig": "461"}, {"size": 556, "mtime": 1746435054411, "results": "752", "hashOfConfig": "461"}, {"size": 3570, "mtime": 1746435054418, "results": "753", "hashOfConfig": "461"}, {"size": 774, "mtime": 1746435054406, "results": "754", "hashOfConfig": "461"}, {"size": 2955, "mtime": 1746434607458, "results": "755", "hashOfConfig": "461"}, {"size": 663, "mtime": 1746434607454, "results": "756", "hashOfConfig": "461"}, {"size": 1016, "mtime": 1746434607469, "results": "757", "hashOfConfig": "461"}, {"size": 4768, "mtime": 1746435054488, "results": "758", "hashOfConfig": "461"}, {"size": 2136, "mtime": 1746434607470, "results": "759", "hashOfConfig": "461"}, {"size": 680, "mtime": 1746434607411, "results": "760", "hashOfConfig": "461"}, {"size": 370, "mtime": 1746434607474, "results": "761", "hashOfConfig": "461"}, {"size": 981, "mtime": 1746435054460, "results": "762", "hashOfConfig": "461"}, {"size": 220, "mtime": 1746435054534, "results": "763", "hashOfConfig": "461"}, {"size": 1325, "mtime": 1746435054532, "results": "764", "hashOfConfig": "461"}, {"size": 6233, "mtime": 1746434607535, "results": "765", "hashOfConfig": "461"}, {"size": 2193, "mtime": 1746434607534, "results": "766", "hashOfConfig": "461"}, {"size": 1423, "mtime": 1746434607487, "results": "767", "hashOfConfig": "461"}, {"size": 22402, "mtime": 1754373173350, "results": "768", "hashOfConfig": "461"}, {"size": 7476, "mtime": 1746435054439, "results": "769", "hashOfConfig": "461"}, {"size": 3515, "mtime": 1746435054441, "results": "770", "hashOfConfig": "461"}, {"size": 5138, "mtime": 1753179122024, "results": "771", "hashOfConfig": "461"}, {"size": 487, "mtime": 1746434607477, "results": "772", "hashOfConfig": "461"}, {"size": 3176, "mtime": 1746435054456, "results": "773", "hashOfConfig": "461"}, {"size": 11820, "mtime": 1753179121894, "results": "774", "hashOfConfig": "461"}, {"size": 958, "mtime": 1746435054403, "results": "775", "hashOfConfig": "461"}, {"size": 1596, "mtime": 1746435054405, "results": "776", "hashOfConfig": "461"}, {"size": 742, "mtime": 1746435054413, "results": "777", "hashOfConfig": "461"}, {"size": 211, "mtime": 1746435054519, "results": "778", "hashOfConfig": "461"}, {"size": 7537, "mtime": 1746435054520, "results": "779", "hashOfConfig": "461"}, {"size": 662, "mtime": 1746435054522, "results": "780", "hashOfConfig": "461"}, {"size": 1230, "mtime": 1746435054523, "results": "781", "hashOfConfig": "461"}, {"size": 857, "mtime": 1746434607533, "results": "782", "hashOfConfig": "461"}, {"size": 3846, "mtime": 1753179121894, "results": "783", "hashOfConfig": "461"}, {"size": 2909, "mtime": 1746435054469, "results": "784", "hashOfConfig": "461"}, {"size": 4148, "mtime": 1746435054464, "results": "785", "hashOfConfig": "461"}, {"size": 9630, "mtime": 1746435054444, "results": "786", "hashOfConfig": "461"}, {"size": 1387, "mtime": 1746434607459, "results": "787", "hashOfConfig": "461"}, {"size": 749, "mtime": 1746434607561, "results": "788", "hashOfConfig": "461"}, {"size": 2403, "mtime": 1753179121901, "results": "789", "hashOfConfig": "461"}, {"size": 2024, "mtime": 1746435054471, "results": "790", "hashOfConfig": "461"}, {"size": 1880, "mtime": 1746434607494, "results": "791", "hashOfConfig": "461"}, {"size": 2087, "mtime": 1746434607495, "results": "792", "hashOfConfig": "461"}, {"size": 3096, "mtime": 1746434607494, "results": "793", "hashOfConfig": "461"}, {"size": 4375, "mtime": 1746435054433, "results": "794", "hashOfConfig": "461"}, {"size": 599, "mtime": 1746435054526, "results": "795", "hashOfConfig": "461"}, {"size": 954, "mtime": 1746435054527, "results": "796", "hashOfConfig": "461"}, {"size": 1068, "mtime": 1746435054529, "results": "797", "hashOfConfig": "461"}, {"size": 8507, "mtime": 1753179121899, "results": "798", "hashOfConfig": "461"}, {"size": 1657, "mtime": 1746434607499, "results": "799", "hashOfConfig": "461"}, {"size": 793, "mtime": 1746434607424, "results": "800", "hashOfConfig": "461"}, {"size": 60183, "mtime": 1753179121907, "results": "801", "hashOfConfig": "461"}, {"size": 5411, "mtime": 1753179121888, "results": "802", "hashOfConfig": "461"}, {"size": 1127, "mtime": 1746435054453, "results": "803", "hashOfConfig": "461"}, {"size": 466, "mtime": 1746434607409, "results": "804", "hashOfConfig": "461"}, {"size": 1712, "mtime": 1746435054541, "results": "805", "hashOfConfig": "461"}, {"size": 2950, "mtime": 1746434607466, "results": "806", "hashOfConfig": "461"}, {"size": 769, "mtime": 1746435054445, "results": "807", "hashOfConfig": "461"}, {"size": 2693, "mtime": 1753179121934, "results": "808", "hashOfConfig": "461"}, {"size": 12943, "mtime": 1753179121932, "results": "809", "hashOfConfig": "461"}, {"size": 12655, "mtime": 1753179121925, "results": "810", "hashOfConfig": "461"}, {"size": 6658, "mtime": 1753179121923, "results": "811", "hashOfConfig": "461"}, {"size": 300, "mtime": 1746435054413, "results": "812", "hashOfConfig": "461"}, {"size": 304, "mtime": 1746435054406, "results": "813", "hashOfConfig": "461"}, {"size": 562, "mtime": 1746435054410, "results": "814", "hashOfConfig": "461"}, {"size": 5913, "mtime": 1753179121932, "results": "815", "hashOfConfig": "461"}, {"size": 6381, "mtime": 1753179121928, "results": "816", "hashOfConfig": "461"}, {"size": 5622, "mtime": 1753179121911, "results": "817", "hashOfConfig": "461"}, {"size": 1072, "mtime": 1746435054487, "results": "818", "hashOfConfig": "461"}, {"size": 7060, "mtime": 1746435054484, "results": "819", "hashOfConfig": "461"}, {"size": 1603, "mtime": 1746435054483, "results": "820", "hashOfConfig": "461"}, {"size": 1860, "mtime": 1753179122030, "results": "821", "hashOfConfig": "461"}, {"size": 203, "mtime": 1746434607450, "results": "822", "hashOfConfig": "461"}, {"size": 726, "mtime": 1746435054487, "results": "823", "hashOfConfig": "461"}, {"size": 8940, "mtime": 1753179122012, "results": "824", "hashOfConfig": "461"}, {"size": 1756, "mtime": 1746434607416, "results": "825", "hashOfConfig": "461"}, {"size": 10622, "mtime": 1753179121876, "results": "826", "hashOfConfig": "461"}, {"size": 2951, "mtime": 1753179122012, "results": "827", "hashOfConfig": "461"}, {"size": 1306, "mtime": 1753179122012, "results": "828", "hashOfConfig": "461"}, {"size": 1055, "mtime": 1746434607452, "results": "829", "hashOfConfig": "461"}, {"size": 681, "mtime": 1746434607372, "results": "830", "hashOfConfig": "461"}, {"size": 1712, "mtime": 1746434607376, "results": "831", "hashOfConfig": "461"}, {"size": 1229, "mtime": 1746434607357, "results": "832", "hashOfConfig": "461"}, {"size": 1346, "mtime": 1746434607354, "results": "833", "hashOfConfig": "461"}, {"size": 819, "mtime": 1746434607370, "results": "834", "hashOfConfig": "461"}, {"size": 626, "mtime": 1746434607469, "results": "835", "hashOfConfig": "461"}, {"size": 915, "mtime": 1746434607484, "results": "836", "hashOfConfig": "461"}, {"size": 1190, "mtime": 1746434607352, "results": "837", "hashOfConfig": "461"}, {"size": 436, "mtime": 1746434607485, "results": "838", "hashOfConfig": "461"}, {"size": 982, "mtime": 1746435054403, "results": "839", "hashOfConfig": "461"}, {"size": 1373, "mtime": 1746434607433, "results": "840", "hashOfConfig": "461"}, {"size": 41189, "mtime": 1746434607543, "results": "841", "hashOfConfig": "461"}, {"size": 3749, "mtime": 1746435054489, "results": "842", "hashOfConfig": "461"}, {"size": 2045, "mtime": 1746434607490, "results": "843", "hashOfConfig": "461"}, {"size": 2095, "mtime": 1746434607555, "results": "844", "hashOfConfig": "461"}, {"size": 2420, "mtime": 1746435054442, "results": "845", "hashOfConfig": "461"}, {"size": 2094, "mtime": 1746443585438, "results": "846", "hashOfConfig": "461"}, {"size": 2016, "mtime": 1746434607443, "results": "847", "hashOfConfig": "461"}, {"size": 846, "mtime": 1746434607492, "results": "848", "hashOfConfig": "461"}, {"size": 1472, "mtime": 1746434607493, "results": "849", "hashOfConfig": "461"}, {"size": 847, "mtime": 1746434607492, "results": "850", "hashOfConfig": "461"}, {"size": 1450, "mtime": 1746434607466, "results": "851", "hashOfConfig": "461"}, {"size": 849, "mtime": 1746434607488, "results": "852", "hashOfConfig": "461"}, {"size": 1757, "mtime": 1746435054524, "results": "853", "hashOfConfig": "461"}, {"size": 3836, "mtime": 1746435054529, "results": "854", "hashOfConfig": "461"}, {"size": 3387, "mtime": 1746434607498, "results": "855", "hashOfConfig": "461"}, {"size": 1490, "mtime": 1746434607465, "results": "856", "hashOfConfig": "461"}, {"size": 711, "mtime": 1746435054445, "results": "857", "hashOfConfig": "461"}, {"size": 5960, "mtime": 1753179121934, "results": "858", "hashOfConfig": "461"}, {"size": 17730, "mtime": 1754373173364, "results": "859", "hashOfConfig": "461"}, {"size": 1461, "mtime": 1753179121925, "results": "860", "hashOfConfig": "461"}, {"size": 8052, "mtime": 1753179121930, "results": "861", "hashOfConfig": "461"}, {"size": 1389, "mtime": 1746435054487, "results": "862", "hashOfConfig": "461"}, {"size": 1049, "mtime": 1753179121953, "results": "863", "hashOfConfig": "461"}, {"size": 4004, "mtime": 1754373173362, "results": "864", "hashOfConfig": "461"}, {"size": 1250, "mtime": 1753179121944, "results": "865", "hashOfConfig": "461"}, {"size": 9811, "mtime": 1753179121946, "results": "866", "hashOfConfig": "461"}, {"size": 1048, "mtime": 1753179121942, "results": "867", "hashOfConfig": "461"}, {"size": 6782, "mtime": 1753179121957, "results": "868", "hashOfConfig": "461"}, {"size": 7696, "mtime": 1753179121946, "results": "869", "hashOfConfig": "461"}, {"size": 8097, "mtime": 1753179121946, "results": "870", "hashOfConfig": "461"}, {"size": 1813, "mtime": 1753179121955, "results": "871", "hashOfConfig": "461"}, {"size": 5622, "mtime": 1753179121946, "results": "872", "hashOfConfig": "461"}, {"size": 8094, "mtime": 1753179121957, "results": "873", "hashOfConfig": "461"}, {"size": 3700, "mtime": 1753179121959, "results": "874", "hashOfConfig": "461"}, {"size": 615, "mtime": 1753179121950, "results": "875", "hashOfConfig": "461"}, {"size": 610, "mtime": 1753179121950, "results": "876", "hashOfConfig": "461"}, {"size": 3276, "mtime": 1754373173360, "results": "877", "hashOfConfig": "461"}, {"size": 625, "mtime": 1753179121953, "results": "878", "hashOfConfig": "461"}, {"size": 635, "mtime": 1753179121944, "results": "879", "hashOfConfig": "461"}, {"size": 14267, "mtime": 1753179121950, "results": "880", "hashOfConfig": "461"}, {"size": 1462, "mtime": 1746434607385, "results": "881", "hashOfConfig": "461"}, {"size": 1209, "mtime": 1746434607368, "results": "882", "hashOfConfig": "461"}, {"size": 3657, "mtime": 1753179121955, "results": "883", "hashOfConfig": "461"}, {"size": 200, "mtime": 1746434607485, "results": "884", "hashOfConfig": "461"}, {"size": 390, "mtime": 1746434607412, "results": "885", "hashOfConfig": "461"}, {"size": 7930, "mtime": 1746434607433, "results": "886", "hashOfConfig": "461"}, {"size": 1872, "mtime": 1746434607491, "results": "887", "hashOfConfig": "461"}, {"size": 781, "mtime": 1746434607488, "results": "888", "hashOfConfig": "461"}, {"size": 332, "mtime": 1746434607467, "results": "889", "hashOfConfig": "461"}, {"size": 1067, "mtime": 1753179121936, "results": "890", "hashOfConfig": "461"}, {"size": 3770, "mtime": 1754373173370, "results": "891", "hashOfConfig": "461"}, {"size": 3845, "mtime": 1754373173366, "results": "892", "hashOfConfig": "461"}, {"size": 13360, "mtime": 1754373173373, "results": "893", "hashOfConfig": "461"}, {"size": 5215, "mtime": 1754373173379, "results": "894", "hashOfConfig": "461"}, {"size": 10763, "mtime": 1754373173377, "results": "895", "hashOfConfig": "461"}, {"size": 7620, "mtime": 1753179121979, "results": "896", "hashOfConfig": "461"}, {"size": 9351, "mtime": 1754373173382, "results": "897", "hashOfConfig": "461"}, {"size": 3576, "mtime": 1753179121977, "results": "898", "hashOfConfig": "461"}, {"size": 8439, "mtime": 1754373173375, "results": "899", "hashOfConfig": "461"}, {"size": 8765, "mtime": 1753179121973, "results": "900", "hashOfConfig": "461"}, {"size": 7641, "mtime": 1753179121983, "results": "901", "hashOfConfig": "461"}, {"size": 23641, "mtime": 1753179121973, "results": "902", "hashOfConfig": "461"}, {"size": 23013, "mtime": 1753179121977, "results": "903", "hashOfConfig": "461"}, {"size": 26077, "mtime": 1753179121963, "results": "904", "hashOfConfig": "461"}, {"size": 1878, "mtime": 1753179121930, "results": "905", "hashOfConfig": "461"}, {"size": 1987, "mtime": 1753179121968, "results": "906", "hashOfConfig": "461"}, {"size": 13424, "mtime": 1754373173368, "results": "907", "hashOfConfig": "461"}, {"size": 24850, "mtime": 1753179121976, "results": "908", "hashOfConfig": "461"}, {"size": 10942, "mtime": 1753179121971, "results": "909", "hashOfConfig": "461"}, {"size": 2874, "mtime": 1753179121979, "results": "910", "hashOfConfig": "461"}, {"size": 9220, "mtime": 1753179121936, "results": "911", "hashOfConfig": "461"}, {"size": 793, "mtime": 1746434607447, "results": "912", "hashOfConfig": "461"}, {"size": 3263, "mtime": 1746434607487, "results": "913", "hashOfConfig": "461"}, {"size": 2651, "mtime": 1753179121989, "results": "914", "hashOfConfig": "461"}, {"size": 2775, "mtime": 1746434607445, "results": "915", "hashOfConfig": "461"}, {"size": 5559, "mtime": 1754373173391, "results": "916", "hashOfConfig": "461"}, {"size": 4871, "mtime": 1746435054436, "results": "917", "hashOfConfig": "461"}, {"size": 1465, "mtime": 1746434607551, "results": "918", "hashOfConfig": "461"}, {"size": 17919, "mtime": 1753687755775, "results": "919", "hashOfConfig": "461"}, {"filePath": "920", "messages": "921", "suppressedMessages": "922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "8myfdw", {"filePath": "923", "messages": "924", "suppressedMessages": "925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "926", "messages": "927", "suppressedMessages": "928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "929", "messages": "930", "suppressedMessages": "931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "932", "messages": "933", "suppressedMessages": "934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "935", "messages": "936", "suppressedMessages": "937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "938", "messages": "939", "suppressedMessages": "940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "941", "messages": "942", "suppressedMessages": "943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "944", "messages": "945", "suppressedMessages": "946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "947", "messages": "948", "suppressedMessages": "949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "950", "messages": "951", "suppressedMessages": "952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "953", "messages": "954", "suppressedMessages": "955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "956", "messages": "957", "suppressedMessages": "958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "959", "messages": "960", "suppressedMessages": "961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "962", "messages": "963", "suppressedMessages": "964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "965", "messages": "966", "suppressedMessages": "967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "968", "messages": "969", "suppressedMessages": "970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "971", "messages": "972", "suppressedMessages": "973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "974", "messages": "975", "suppressedMessages": "976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "977", "messages": "978", "suppressedMessages": "979", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "980", "messages": "981", "suppressedMessages": "982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "983", "messages": "984", "suppressedMessages": "985", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "986", "messages": "987", "suppressedMessages": "988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "989", "messages": "990", "suppressedMessages": "991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "992", "messages": "993", "suppressedMessages": "994", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "995", "messages": "996", "suppressedMessages": "997", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "998", "messages": "999", "suppressedMessages": "1000", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1001", "messages": "1002", "suppressedMessages": "1003", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1004", "messages": "1005", "suppressedMessages": "1006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1007", "messages": "1008", "suppressedMessages": "1009", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1010", "messages": "1011", "suppressedMessages": "1012", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1013", "messages": "1014", "suppressedMessages": "1015", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1016", "messages": "1017", "suppressedMessages": "1018", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1019", "messages": "1020", "suppressedMessages": "1021", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1022", "messages": "1023", "suppressedMessages": "1024", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1025", "messages": "1026", "suppressedMessages": "1027", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1028", "messages": "1029", "suppressedMessages": "1030", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1031", "messages": "1032", "suppressedMessages": "1033", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1034", "messages": "1035", "suppressedMessages": "1036", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1037", "messages": "1038", "suppressedMessages": "1039", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1040", "messages": "1041", "suppressedMessages": "1042", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1043", "messages": "1044", "suppressedMessages": "1045", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1046", "messages": "1047", "suppressedMessages": "1048", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1049", "messages": "1050", "suppressedMessages": "1051", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1052", "messages": "1053", "suppressedMessages": "1054", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1055", "messages": "1056", "suppressedMessages": "1057", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1058", "messages": "1059", "suppressedMessages": "1060", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1061", "messages": "1062", "suppressedMessages": "1063", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1064", "messages": "1065", "suppressedMessages": "1066", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1067", "messages": "1068", "suppressedMessages": "1069", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1070", "messages": "1071", "suppressedMessages": "1072", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1073", "messages": "1074", "suppressedMessages": "1075", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1076", "messages": "1077", "suppressedMessages": "1078", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1079", "messages": "1080", "suppressedMessages": "1081", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1082", "messages": "1083", "suppressedMessages": "1084", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1085", "messages": "1086", "suppressedMessages": "1087", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1088", "messages": "1089", "suppressedMessages": "1090", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1091", "messages": "1092", "suppressedMessages": "1093", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1094", "messages": "1095", "suppressedMessages": "1096", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1097", "messages": "1098", "suppressedMessages": "1099", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1100", "messages": "1101", "suppressedMessages": "1102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1103", "messages": "1104", "suppressedMessages": "1105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1106", "messages": "1107", "suppressedMessages": "1108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1109", "messages": "1110", "suppressedMessages": "1111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1112", "messages": "1113", "suppressedMessages": "1114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1115", "messages": "1116", "suppressedMessages": "1117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1118", "messages": "1119", "suppressedMessages": "1120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1121", "messages": "1122", "suppressedMessages": "1123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1124", "messages": "1125", "suppressedMessages": "1126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1127", "messages": "1128", "suppressedMessages": "1129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1130", "messages": "1131", "suppressedMessages": "1132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1133", "messages": "1134", "suppressedMessages": "1135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1136", "messages": "1137", "suppressedMessages": "1138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1139", "messages": "1140", "suppressedMessages": "1141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1142", "messages": "1143", "suppressedMessages": "1144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1145", "messages": "1146", "suppressedMessages": "1147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1148", "messages": "1149", "suppressedMessages": "1150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1151", "messages": "1152", "suppressedMessages": "1153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1154", "messages": "1155", "suppressedMessages": "1156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1157", "messages": "1158", "suppressedMessages": "1159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1160", "messages": "1161", "suppressedMessages": "1162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1163", "messages": "1164", "suppressedMessages": "1165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1166", "messages": "1167", "suppressedMessages": "1168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1169", "messages": "1170", "suppressedMessages": "1171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1172", "messages": "1173", "suppressedMessages": "1174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1175", "messages": "1176", "suppressedMessages": "1177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1178", "messages": "1179", "suppressedMessages": "1180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1181", "messages": "1182", "suppressedMessages": "1183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1184", "messages": "1185", "suppressedMessages": "1186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1187", "messages": "1188", "suppressedMessages": "1189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1190", "messages": "1191", "suppressedMessages": "1192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1193", "messages": "1194", "suppressedMessages": "1195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1196", "messages": "1197", "suppressedMessages": "1198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1199", "messages": "1200", "suppressedMessages": "1201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1202", "messages": "1203", "suppressedMessages": "1204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1205", "messages": "1206", "suppressedMessages": "1207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1208", "messages": "1209", "suppressedMessages": "1210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1211", "messages": "1212", "suppressedMessages": "1213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1214", "messages": "1215", "suppressedMessages": "1216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1217", "messages": "1218", "suppressedMessages": "1219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1220", "messages": "1221", "suppressedMessages": "1222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1223", "messages": "1224", "suppressedMessages": "1225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1226", "messages": "1227", "suppressedMessages": "1228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1229", "messages": "1230", "suppressedMessages": "1231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1232", "messages": "1233", "suppressedMessages": "1234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1235", "messages": "1236", "suppressedMessages": "1237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1238", "messages": "1239", "suppressedMessages": "1240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1241", "messages": "1242", "suppressedMessages": "1243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1244", "messages": "1245", "suppressedMessages": "1246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1247", "messages": "1248", "suppressedMessages": "1249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1250", "messages": "1251", "suppressedMessages": "1252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1253", "messages": "1254", "suppressedMessages": "1255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1256", "messages": "1257", "suppressedMessages": "1258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1259", "messages": "1260", "suppressedMessages": "1261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1262", "messages": "1263", "suppressedMessages": "1264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1265", "messages": "1266", "suppressedMessages": "1267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1268", "messages": "1269", "suppressedMessages": "1270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1271", "messages": "1272", "suppressedMessages": "1273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1274", "messages": "1275", "suppressedMessages": "1276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1277", "messages": "1278", "suppressedMessages": "1279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1280", "messages": "1281", "suppressedMessages": "1282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1283", "messages": "1284", "suppressedMessages": "1285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 60, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1286", "messages": "1287", "suppressedMessages": "1288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1289", "messages": "1290", "suppressedMessages": "1291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1292", "messages": "1293", "suppressedMessages": "1294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1295", "messages": "1296", "suppressedMessages": "1297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1298", "messages": "1299", "suppressedMessages": "1300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1301", "messages": "1302", "suppressedMessages": "1303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1304", "messages": "1305", "suppressedMessages": "1306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1307", "messages": "1308", "suppressedMessages": "1309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1310", "messages": "1311", "suppressedMessages": "1312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1313", "messages": "1314", "suppressedMessages": "1315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1316", "messages": "1317", "suppressedMessages": "1318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1319", "messages": "1320", "suppressedMessages": "1321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1322", "messages": "1323", "suppressedMessages": "1324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1325", "messages": "1326", "suppressedMessages": "1327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1328", "messages": "1329", "suppressedMessages": "1330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1331", "messages": "1332", "suppressedMessages": "1333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1334", "messages": "1335", "suppressedMessages": "1336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1337", "messages": "1338", "suppressedMessages": "1339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1340", "messages": "1341", "suppressedMessages": "1342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1343", "messages": "1344", "suppressedMessages": "1345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1346", "messages": "1347", "suppressedMessages": "1348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1349", "messages": "1350", "suppressedMessages": "1351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1352", "messages": "1353", "suppressedMessages": "1354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1355", "messages": "1356", "suppressedMessages": "1357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1358", "messages": "1359", "suppressedMessages": "1360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1361", "messages": "1362", "suppressedMessages": "1363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1364", "messages": "1365", "suppressedMessages": "1366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1367", "messages": "1368", "suppressedMessages": "1369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1370", "messages": "1371", "suppressedMessages": "1372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1373", "messages": "1374", "suppressedMessages": "1375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1376", "messages": "1377", "suppressedMessages": "1378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1379", "messages": "1380", "suppressedMessages": "1381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1382", "messages": "1383", "suppressedMessages": "1384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1385", "messages": "1386", "suppressedMessages": "1387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1388", "messages": "1389", "suppressedMessages": "1390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1391", "messages": "1392", "suppressedMessages": "1393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1394", "messages": "1395", "suppressedMessages": "1396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1397", "messages": "1398", "suppressedMessages": "1399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1400", "messages": "1401", "suppressedMessages": "1402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1403", "messages": "1404", "suppressedMessages": "1405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1406", "messages": "1407", "suppressedMessages": "1408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1409", "messages": "1410", "suppressedMessages": "1411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1412", "messages": "1413", "suppressedMessages": "1414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1415", "messages": "1416", "suppressedMessages": "1417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1418", "messages": "1419", "suppressedMessages": "1420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1421", "messages": "1422", "suppressedMessages": "1423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1424", "messages": "1425", "suppressedMessages": "1426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1427", "messages": "1428", "suppressedMessages": "1429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1430", "messages": "1431", "suppressedMessages": "1432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1433", "messages": "1434", "suppressedMessages": "1435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1436", "messages": "1437", "suppressedMessages": "1438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1439", "messages": "1440", "suppressedMessages": "1441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1442", "messages": "1443", "suppressedMessages": "1444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1445", "messages": "1446", "suppressedMessages": "1447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1448", "messages": "1449", "suppressedMessages": "1450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1451", "messages": "1452", "suppressedMessages": "1453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1454", "messages": "1455", "suppressedMessages": "1456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1457", "messages": "1458", "suppressedMessages": "1459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1460", "messages": "1461", "suppressedMessages": "1462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1463", "messages": "1464", "suppressedMessages": "1465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1466", "messages": "1467", "suppressedMessages": "1468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1469", "messages": "1470", "suppressedMessages": "1471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1472", "messages": "1473", "suppressedMessages": "1474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1475", "messages": "1476", "suppressedMessages": "1477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1478", "messages": "1479", "suppressedMessages": "1480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1481", "messages": "1482", "suppressedMessages": "1483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1484", "messages": "1485", "suppressedMessages": "1486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1487", "messages": "1488", "suppressedMessages": "1489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1490", "messages": "1491", "suppressedMessages": "1492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1493", "messages": "1494", "suppressedMessages": "1495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1496", "messages": "1497", "suppressedMessages": "1498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1499", "messages": "1500", "suppressedMessages": "1501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1502", "messages": "1503", "suppressedMessages": "1504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1505", "messages": "1506", "suppressedMessages": "1507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1508", "messages": "1509", "suppressedMessages": "1510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1511", "messages": "1512", "suppressedMessages": "1513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1514", "messages": "1515", "suppressedMessages": "1516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1517", "messages": "1518", "suppressedMessages": "1519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1520", "messages": "1521", "suppressedMessages": "1522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1523", "messages": "1524", "suppressedMessages": "1525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1526", "messages": "1527", "suppressedMessages": "1528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1529", "messages": "1530", "suppressedMessages": "1531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1532", "messages": "1533", "suppressedMessages": "1534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1535", "messages": "1536", "suppressedMessages": "1537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1538", "messages": "1539", "suppressedMessages": "1540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1541", "messages": "1542", "suppressedMessages": "1543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1544", "messages": "1545", "suppressedMessages": "1546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1547", "messages": "1548", "suppressedMessages": "1549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1550", "messages": "1551", "suppressedMessages": "1552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1553", "messages": "1554", "suppressedMessages": "1555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1556", "messages": "1557", "suppressedMessages": "1558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1559", "messages": "1560", "suppressedMessages": "1561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1562", "messages": "1563", "suppressedMessages": "1564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1565", "messages": "1566", "suppressedMessages": "1567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1568", "messages": "1569", "suppressedMessages": "1570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1571", "messages": "1572", "suppressedMessages": "1573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1574", "messages": "1575", "suppressedMessages": "1576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1577", "messages": "1578", "suppressedMessages": "1579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1580", "messages": "1581", "suppressedMessages": "1582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1583", "messages": "1584", "suppressedMessages": "1585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1586", "messages": "1587", "suppressedMessages": "1588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1589", "messages": "1590", "suppressedMessages": "1591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1592", "messages": "1593", "suppressedMessages": "1594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1595", "messages": "1596", "suppressedMessages": "1597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1598", "messages": "1599", "suppressedMessages": "1600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1601", "messages": "1602", "suppressedMessages": "1603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1604", "messages": "1605", "suppressedMessages": "1606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1607", "messages": "1608", "suppressedMessages": "1609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1610", "messages": "1611", "suppressedMessages": "1612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1613", "messages": "1614", "suppressedMessages": "1615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1616", "messages": "1617", "suppressedMessages": "1618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1619", "messages": "1620", "suppressedMessages": "1621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1622", "messages": "1623", "suppressedMessages": "1624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1625", "messages": "1626", "suppressedMessages": "1627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1628", "messages": "1629", "suppressedMessages": "1630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1631", "messages": "1632", "suppressedMessages": "1633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1634", "messages": "1635", "suppressedMessages": "1636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1637", "messages": "1638", "suppressedMessages": "1639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1640", "messages": "1641", "suppressedMessages": "1642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1643", "messages": "1644", "suppressedMessages": "1645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1646", "messages": "1647", "suppressedMessages": "1648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1649", "messages": "1650", "suppressedMessages": "1651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1652", "messages": "1653", "suppressedMessages": "1654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1655", "messages": "1656", "suppressedMessages": "1657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1658", "messages": "1659", "suppressedMessages": "1660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1661", "messages": "1662", "suppressedMessages": "1663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1664", "messages": "1665", "suppressedMessages": "1666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1667", "messages": "1668", "suppressedMessages": "1669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1670", "messages": "1671", "suppressedMessages": "1672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1673", "messages": "1674", "suppressedMessages": "1675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1676", "messages": "1677", "suppressedMessages": "1678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1679", "messages": "1680", "suppressedMessages": "1681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1682", "messages": "1683", "suppressedMessages": "1684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1685", "messages": "1686", "suppressedMessages": "1687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1688", "messages": "1689", "suppressedMessages": "1690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1691", "messages": "1692", "suppressedMessages": "1693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1694", "messages": "1695", "suppressedMessages": "1696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1697", "messages": "1698", "suppressedMessages": "1699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1700", "messages": "1701", "suppressedMessages": "1702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1703", "messages": "1704", "suppressedMessages": "1705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1706", "messages": "1707", "suppressedMessages": "1708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1709", "messages": "1710", "suppressedMessages": "1711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1712", "messages": "1713", "suppressedMessages": "1714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1715", "messages": "1716", "suppressedMessages": "1717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1718", "messages": "1719", "suppressedMessages": "1720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1721", "messages": "1722", "suppressedMessages": "1723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1724", "messages": "1725", "suppressedMessages": "1726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1727", "messages": "1728", "suppressedMessages": "1729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1730", "messages": "1731", "suppressedMessages": "1732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1733", "messages": "1734", "suppressedMessages": "1735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1736", "messages": "1737", "suppressedMessages": "1738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1739", "messages": "1740", "suppressedMessages": "1741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1742", "messages": "1743", "suppressedMessages": "1744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1745", "messages": "1746", "suppressedMessages": "1747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1748", "messages": "1749", "suppressedMessages": "1750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1751", "messages": "1752", "suppressedMessages": "1753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1754", "messages": "1755", "suppressedMessages": "1756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1757", "messages": "1758", "suppressedMessages": "1759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1760", "messages": "1761", "suppressedMessages": "1762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1763", "messages": "1764", "suppressedMessages": "1765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1766", "messages": "1767", "suppressedMessages": "1768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1769", "messages": "1770", "suppressedMessages": "1771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1772", "messages": "1773", "suppressedMessages": "1774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1775", "messages": "1776", "suppressedMessages": "1777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1778", "messages": "1779", "suppressedMessages": "1780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1781", "messages": "1782", "suppressedMessages": "1783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1784", "messages": "1785", "suppressedMessages": "1786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1787", "messages": "1788", "suppressedMessages": "1789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1790", "messages": "1791", "suppressedMessages": "1792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1793", "messages": "1794", "suppressedMessages": "1795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1796", "messages": "1797", "suppressedMessages": "1798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1799", "messages": "1800", "suppressedMessages": "1801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1802", "messages": "1803", "suppressedMessages": "1804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1805", "messages": "1806", "suppressedMessages": "1807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1808", "messages": "1809", "suppressedMessages": "1810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1811", "messages": "1812", "suppressedMessages": "1813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1814", "messages": "1815", "suppressedMessages": "1816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1817", "messages": "1818", "suppressedMessages": "1819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1820", "messages": "1821", "suppressedMessages": "1822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1823", "messages": "1824", "suppressedMessages": "1825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1826", "messages": "1827", "suppressedMessages": "1828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1829", "messages": "1830", "suppressedMessages": "1831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1832", "messages": "1833", "suppressedMessages": "1834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1835", "messages": "1836", "suppressedMessages": "1837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1838", "messages": "1839", "suppressedMessages": "1840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1841", "messages": "1842", "suppressedMessages": "1843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1844", "messages": "1845", "suppressedMessages": "1846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1847", "messages": "1848", "suppressedMessages": "1849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1850", "messages": "1851", "suppressedMessages": "1852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1853", "messages": "1854", "suppressedMessages": "1855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1856", "messages": "1857", "suppressedMessages": "1858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1859", "messages": "1860", "suppressedMessages": "1861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1862", "messages": "1863", "suppressedMessages": "1864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1865", "messages": "1866", "suppressedMessages": "1867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1868", "messages": "1869", "suppressedMessages": "1870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1871", "messages": "1872", "suppressedMessages": "1873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1874", "messages": "1875", "suppressedMessages": "1876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1877", "messages": "1878", "suppressedMessages": "1879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1880", "messages": "1881", "suppressedMessages": "1882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1883", "messages": "1884", "suppressedMessages": "1885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1886", "messages": "1887", "suppressedMessages": "1888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1889", "messages": "1890", "suppressedMessages": "1891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1892", "messages": "1893", "suppressedMessages": "1894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1895", "messages": "1896", "suppressedMessages": "1897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1898", "messages": "1899", "suppressedMessages": "1900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1901", "messages": "1902", "suppressedMessages": "1903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1904", "messages": "1905", "suppressedMessages": "1906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1907", "messages": "1908", "suppressedMessages": "1909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1910", "messages": "1911", "suppressedMessages": "1912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1913", "messages": "1914", "suppressedMessages": "1915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1916", "messages": "1917", "suppressedMessages": "1918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1919", "messages": "1920", "suppressedMessages": "1921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1922", "messages": "1923", "suppressedMessages": "1924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1925", "messages": "1926", "suppressedMessages": "1927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1928", "messages": "1929", "suppressedMessages": "1930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1931", "messages": "1932", "suppressedMessages": "1933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1934", "messages": "1935", "suppressedMessages": "1936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1937", "messages": "1938", "suppressedMessages": "1939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1940", "messages": "1941", "suppressedMessages": "1942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1943", "messages": "1944", "suppressedMessages": "1945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1946", "messages": "1947", "suppressedMessages": "1948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1949", "messages": "1950", "suppressedMessages": "1951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1952", "messages": "1953", "suppressedMessages": "1954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1955", "messages": "1956", "suppressedMessages": "1957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1958", "messages": "1959", "suppressedMessages": "1960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1961", "messages": "1962", "suppressedMessages": "1963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1964", "messages": "1965", "suppressedMessages": "1966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1967", "messages": "1968", "suppressedMessages": "1969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1970", "messages": "1971", "suppressedMessages": "1972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1973", "messages": "1974", "suppressedMessages": "1975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1976", "messages": "1977", "suppressedMessages": "1978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1979", "messages": "1980", "suppressedMessages": "1981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1982", "messages": "1983", "suppressedMessages": "1984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1985", "messages": "1986", "suppressedMessages": "1987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1988", "messages": "1989", "suppressedMessages": "1990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1991", "messages": "1992", "suppressedMessages": "1993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1994", "messages": "1995", "suppressedMessages": "1996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1997", "messages": "1998", "suppressedMessages": "1999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2000", "messages": "2001", "suppressedMessages": "2002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2003", "messages": "2004", "suppressedMessages": "2005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2006", "messages": "2007", "suppressedMessages": "2008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2009", "messages": "2010", "suppressedMessages": "2011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2012", "messages": "2013", "suppressedMessages": "2014", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2015", "messages": "2016", "suppressedMessages": "2017", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2018", "messages": "2019", "suppressedMessages": "2020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2021", "messages": "2022", "suppressedMessages": "2023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2024", "messages": "2025", "suppressedMessages": "2026", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2027", "messages": "2028", "suppressedMessages": "2029", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2030", "messages": "2031", "suppressedMessages": "2032", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2033", "messages": "2034", "suppressedMessages": "2035", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2036", "messages": "2037", "suppressedMessages": "2038", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2039", "messages": "2040", "suppressedMessages": "2041", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2042", "messages": "2043", "suppressedMessages": "2044", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2045", "messages": "2046", "suppressedMessages": "2047", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2048", "messages": "2049", "suppressedMessages": "2050", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2051", "messages": "2052", "suppressedMessages": "2053", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2054", "messages": "2055", "suppressedMessages": "2056", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2057", "messages": "2058", "suppressedMessages": "2059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2060", "messages": "2061", "suppressedMessages": "2062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2063", "messages": "2064", "suppressedMessages": "2065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2066", "messages": "2067", "suppressedMessages": "2068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2069", "messages": "2070", "suppressedMessages": "2071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2072", "messages": "2073", "suppressedMessages": "2074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2075", "messages": "2076", "suppressedMessages": "2077", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2078", "messages": "2079", "suppressedMessages": "2080", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2081", "messages": "2082", "suppressedMessages": "2083", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2084", "messages": "2085", "suppressedMessages": "2086", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2087", "messages": "2088", "suppressedMessages": "2089", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2090", "messages": "2091", "suppressedMessages": "2092", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2093", "messages": "2094", "suppressedMessages": "2095", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2096", "messages": "2097", "suppressedMessages": "2098", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2099", "messages": "2100", "suppressedMessages": "2101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2102", "messages": "2103", "suppressedMessages": "2104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2105", "messages": "2106", "suppressedMessages": "2107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2108", "messages": "2109", "suppressedMessages": "2110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2111", "messages": "2112", "suppressedMessages": "2113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2114", "messages": "2115", "suppressedMessages": "2116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2117", "messages": "2118", "suppressedMessages": "2119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2120", "messages": "2121", "suppressedMessages": "2122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2123", "messages": "2124", "suppressedMessages": "2125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2126", "messages": "2127", "suppressedMessages": "2128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2129", "messages": "2130", "suppressedMessages": "2131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2132", "messages": "2133", "suppressedMessages": "2134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2135", "messages": "2136", "suppressedMessages": "2137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2138", "messages": "2139", "suppressedMessages": "2140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2141", "messages": "2142", "suppressedMessages": "2143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2144", "messages": "2145", "suppressedMessages": "2146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2147", "messages": "2148", "suppressedMessages": "2149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2150", "messages": "2151", "suppressedMessages": "2152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2153", "messages": "2154", "suppressedMessages": "2155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2156", "messages": "2157", "suppressedMessages": "2158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2159", "messages": "2160", "suppressedMessages": "2161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2162", "messages": "2163", "suppressedMessages": "2164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2165", "messages": "2166", "suppressedMessages": "2167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2168", "messages": "2169", "suppressedMessages": "2170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2171", "messages": "2172", "suppressedMessages": "2173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2174", "messages": "2175", "suppressedMessages": "2176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2177", "messages": "2178", "suppressedMessages": "2179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2180", "messages": "2181", "suppressedMessages": "2182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2183", "messages": "2184", "suppressedMessages": "2185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2186", "messages": "2187", "suppressedMessages": "2188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2189", "messages": "2190", "suppressedMessages": "2191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2192", "messages": "2193", "suppressedMessages": "2194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2195", "messages": "2196", "suppressedMessages": "2197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2198", "messages": "2199", "suppressedMessages": "2200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2201", "messages": "2202", "suppressedMessages": "2203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2204", "messages": "2205", "suppressedMessages": "2206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2207", "messages": "2208", "suppressedMessages": "2209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2210", "messages": "2211", "suppressedMessages": "2212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2213", "messages": "2214", "suppressedMessages": "2215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2216", "messages": "2217", "suppressedMessages": "2218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2219", "messages": "2220", "suppressedMessages": "2221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2222", "messages": "2223", "suppressedMessages": "2224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2225", "messages": "2226", "suppressedMessages": "2227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2228", "messages": "2229", "suppressedMessages": "2230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2231", "messages": "2232", "suppressedMessages": "2233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2234", "messages": "2235", "suppressedMessages": "2236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2237", "messages": "2238", "suppressedMessages": "2239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2240", "messages": "2241", "suppressedMessages": "2242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2243", "messages": "2244", "suppressedMessages": "2245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2246", "messages": "2247", "suppressedMessages": "2248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2249", "messages": "2250", "suppressedMessages": "2251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2252", "messages": "2253", "suppressedMessages": "2254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2255", "messages": "2256", "suppressedMessages": "2257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2258", "messages": "2259", "suppressedMessages": "2260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2261", "messages": "2262", "suppressedMessages": "2263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2264", "messages": "2265", "suppressedMessages": "2266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2267", "messages": "2268", "suppressedMessages": "2269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2270", "messages": "2271", "suppressedMessages": "2272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2273", "messages": "2274", "suppressedMessages": "2275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2276", "messages": "2277", "suppressedMessages": "2278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2279", "messages": "2280", "suppressedMessages": "2281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2282", "messages": "2283", "suppressedMessages": "2284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2285", "messages": "2286", "suppressedMessages": "2287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2288", "messages": "2289", "suppressedMessages": "2290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2291", "messages": "2292", "suppressedMessages": "2293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2294", "messages": "2295", "suppressedMessages": "2296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\store\\store.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\hooks.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\LandingPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\ErrorPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\Icons.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\DashboardRoutes.tsx", ["2297", "2298", "2299", "2300", "2301", "2302"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\layouts\\GlobalLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\authentication\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\authentication\\ResetPassword.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\layouts\\AuthenticationLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\authentication\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\PublicProgramPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\authentication\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\common\\AnnouncementPopup.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\authentication\\InvitedUserRegistrationForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\store\\reducer\\slices\\modalReducer.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\store\\reducer\\slices\\userReducer.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\store\\reducer\\slices\\userReducerForAdmin.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\usePageTitle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CTBIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\AMLIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CloseIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CameraIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CashIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\Chevron.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\user\\credentials.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\DeleteIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CogIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\Eye.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\DashboardIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\EditIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CreateIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\FilterIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\Flag.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\HelpIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\GearIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ImageIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\InfoIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\LeaderboardIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\LinkedInIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\LinkIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\LightBulb.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\MultipleUsersIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\MinusCircle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\NotifcationUnreadIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\LocationIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\NotifcationIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\PlusIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\PieChartIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\PlusOutlineIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\PatchIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ProfileIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\SearchIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ReportsIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ProgramsIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ShieldIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\SmallCross.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\SignoutIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ThumbsUp.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\VerifiedIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\UploadIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ZoomIn.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\TwitterIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\layouts\\DashboardLayout.tsx", ["2303"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ZoomOut.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\RecentReportsIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ComputerIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\SpeakerphoneIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\users\\AdminRoutes.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\users\\ResearcherRoutes.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\user\\useUserCredentials.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\users\\BusinessRoutes.tsx", ["2304"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\users\\SubAdminRoutes.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\users\\AdminManagerRoutes.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\users\\DeveloperRoutes.tsx", ["2305", "2306", "2307", "2308", "2309", "2310"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\users\\QARoutes.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\users\\BusinessManagerRoutes.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\authentication\\ForgotPassword.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\authentication\\OTPForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\authentication\\LoginForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\routes\\users\\BusinessAdminRoutes.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\layouts\\TabbedLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\layouts\\DetailViewLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\authentication\\ResetPasswordForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\FullProgramDetails.tsx", ["2311"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\footer\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\authentication\\RegisterForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\programs\\usePublicProgram.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\cards\\BusinessProfileCard.tsx", ["2312", "2313", "2314", "2315"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\authentication\\Masthead.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\axios.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\Form.tsx", ["2316"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\cards\\ProgramInfoCard.tsx", ["2317"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\user\\invitation.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\store\\reducer\\reducer.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\errors\\ErrorMessage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\user\\user.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\user\\authentication.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\authentication\\inputs\\AuthButton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\authentication\\inputs\\AuthTextBox.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\ProgramEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\useIsMobile.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\headers\\DashboardHeader.tsx", ["2318"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\navigation\\SideNav.tsx", ["2319", "2320"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\admin\\AdminDashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\ProgramPage.tsx", ["2321", "2322"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\PaginatedPrograms.tsx", ["2323", "2324"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\admin\\AdminProfile.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\reports\\ReportEditor.tsx", ["2325", "2326"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\assistant\\Assistant.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\reports\\PaginatedReports.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\reports\\ReportPage.tsx", ["2327", "2328"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\payments\\PaginatedTransactions.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\retests\\RetestPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\retests\\RetestManagent.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\users\\InvitationManager.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\users\\PaginatedUsers.tsx", ["2329"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\PDFEditor.tsx", ["2330", "2331", "2332", "2333", "2334", "2335", "2336", "2337", "2338", "2339", "2340", "2341", "2342", "2343", "2344", "2345", "2346", "2347", "2348", "2349", "2350", "2351", "2352", "2353", "2354", "2355", "2356", "2357", "2358", "2359", "2360", "2361", "2362", "2363", "2364", "2365", "2366", "2367", "2368", "2369", "2370", "2371", "2372", "2373", "2374", "2375", "2376", "2377", "2378", "2379", "2380", "2381", "2382", "2383", "2384", "2385", "2386", "2387", "2388", "2389"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\PentestReports.tsx", ["2390"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\researcher\\ResearcherDashboard.tsx", ["2391", "2392", "2393"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\researcher\\ReseacherProfile.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\business\\BusinessProfile.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\business\\SolidityScan.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\business\\EnhancedBusinessDashboard.tsx", ["2394", "2395"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CoinIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\programsApi.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\programs\\useProgram.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\common\\InfoCardContainer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\common\\Collapsible.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\constants.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\common\\InfoCardRow.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\programs\\parsePrograms.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\errors\\ErrorMessages.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\common\\InlineContainer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\ProgramStats.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\Checkbox.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\business\\generateSummaryReport.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\TextBox.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\buttons\\OutlineButton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\authentication\\inputs\\VisiblityIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\layouts\\EditorLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\pages\\researcher\\PaymentMethodSettings.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\useModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\FormProgressTimeline.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CircleChevron.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\navigation\\SideNavData.tsx", ["2396", "2397", "2398"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\user\\useUserDetails.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\notifications\\useNotifications.ts", ["2399", "2400"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\dashboard\\useAdminDashboardDetails.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\FormDropdown.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\NavbarImg.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\EditorSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\NavbarBanner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\programs\\usePrograms.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\EditorRichTextInput.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\ProgramEditorAttachments.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\ProgramEditorLogo.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\EditorTextInput.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\notifications\\NotificationsModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\EditorInput.tsx", ["2401"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\ProfileImageSmall.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\programs\\DropDownEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\cards\\HamburgerProgram.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\cards\\RightbarProgram.tsx", ["2402", "2403", "2404", "2405", "2406", "2407", "2408", "2409", "2410", "2411", "2412", "2413", "2414", "2415", "2416", "2417", "2418", "2419", "2420", "2421", "2422", "2423"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\UserProfile.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\ProgramQuickView.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\programs\\TargetsInput.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\programs\\ProgramRewardsInput.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\programs\\DatePicker.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\reports\\useReport.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\cards\\ProgramHighlight.tsx", ["2424", "2425"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\common\\ConfirmationModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\ProfileSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\assistant\\PatchAssistant.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\assistant\\ChatAssistant.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\automated-reports\\automated-reports.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\cards\\ProgramCard.tsx", ["2426"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\ControlledSeverityScoreSelector.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\reports\\useReports.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\section\\Management.tsx", ["2427", "2428", "2429", "2430", "2431", "2432", "2433", "2434", "2435", "2436", "2437"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\section\\Attachments.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\reports\\useFilter.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\EditorAttachmentsInput.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\section\\TermsCondition.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\section\\ReportTable.tsx", ["2438"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\section\\RecentActivity.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\programs\\section\\Scope.tsx", ["2439", "2440"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\admin\\ProgramWithResearchersTable.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\admin\\NumberBoxSectionAdmin.tsx", ["2441", "2442", "2443", "2444", "2445", "2446", "2447"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\admin\\PentestersLeaderboard.tsx", ["2448"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\admin\\RetestReportsListTable.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\admin\\ResearcherPieChart.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\admin\\UnderReviewReportsListTable.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\admin\\RetestBarChart.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\admin\\GroupedSeverityChart.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\admin\\ProgramGrowthChart.tsx", ["2449"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\pagination\\PaginationHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\pagination\\PaginationLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\filters\\FilterToolBar.tsx", ["2450"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\filters\\inputs\\FilterDropdown.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\reports\\ReportTargetSelector.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\reports\\ReportQuickView.tsx", ["2451"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\reports\\ReportProgramBanner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\DownloadIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\reports\\cards\\ReportCard.tsx", ["2452"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\reports\\CategorySelector.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\reports\\cards\\ProgramProfileCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\reports\\cards\\BugInformationCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\usersApi.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\reports\\ReportProgramSelector.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\reportCommentsApi.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\utils\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\constants\\reportStatusInfo.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\reports\\parseReports.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\reports\\cards\\DocumentationLink.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\payments\\useTransactions.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\CommentBox.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\reports\\reports.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\retests\\useReportDetails.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\retests\\useRetestLogs.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\retests\\retests.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\retests\\useRetestStatusRetestId.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\payments\\PaymentStatusPill.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\table\\EmptyTableRow.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\retests\\useRetestDetailsRight.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\table\\TableRow.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\table\\TableContainer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\multi_tenant\\SpinnerOverlay.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\multi_tenant\\ActivityLogsTable.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\multi_tenant\\DeleteConfirmationModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\multi_tenant\\CreateInvitationForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\multi_tenant\\TabNavigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\multi_tenant\\InvitationsTable.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\multi_tenant\\RolePermissionsModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\users\\UserTableRow.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\multi_tenant\\ActivityLogsFilters.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\multi_tenant\\InvitationsFilters.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\users\\UserQuickView.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\PreviewOnly.tsx", ["2453"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\EditorLayout.tsx", ["2454", "2455", "2456", "2457", "2458", "2459", "2460", "2461", "2462", "2463", "2464", "2465", "2466", "2467", "2468", "2469", "2470", "2471", "2472", "2473", "2474", "2475", "2476", "2477", "2478", "2479", "2480"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ReportsCounterIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\LogsPage\\RetestHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\LogsPage\\LogList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\utils\\NoDataAvailable.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\LogsPage\\CommentForm.tsx", ["2481", "2482"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\utils\\Pagination.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\Table\\RetestTable.tsx", ["2483"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\LogsPage\\RetestDetailsRightSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\multi-tenant\\invitation.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\hooks\\useRoleString.ts", ["2484"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\user\\useAllUsers.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\reports\\useReportSummary.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\MoneyPendingIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ReportsReviewIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\hooks\\usePDFEditorLogic.ts", ["2485", "2486", "2487", "2488", "2489", "2490", "2491", "2492", "2493"], ["2494"], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ReportsApprovedIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\ChatSection.tsx", ["2495"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\user\\multiTenant.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\ChatModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\common\\StatisticCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\automated-report\\automated-report.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\common\\DashboardItem.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\common\\DashboardTop.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\common\\DashboardGrid.tsx", ["2496", "2497"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\solidity_scan\\SolidityScanForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\researcher\\RecentReports.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\researcher\\RecentPrograms.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\sections\\About.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\dashboard\\useDashboardSummary.ts", ["2498"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\sections\\PillsWrapper.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\sections\\Links.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\dashboard\\useUnifiedBusinessDashboard.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\solidity_scan\\SolidityScanResult.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\dashboard\\useSeverityAndTrend.ts", ["2499"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\business\\solidityScan.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\business\\EnhancedDashboardStats.tsx", ["2500"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\business\\EnhancedTrendLineChart.tsx", ["2501"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\business\\EnhancedSeverityDonut.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\business\\ActionableReportsSection.tsx", ["2502", "2503", "2504", "2505", "2506"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\business\\RetestingSummarySection.tsx", ["2507", "2508", "2509", "2510", "2511"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\business\\RetestActionsTable.tsx", ["2512"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\charts\\DoughnutChart.tsx", ["2513"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\pills\\Pill.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\modal\\QuickViewModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\sanitizers.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\EditorTracker.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\useEditorSections.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\RetestIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\common\\ProgressTimeline.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\Hamburger.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\notifications\\NotificationRow.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\modal\\RelativeModalContainer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\ProfileLinks.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\SettingsModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\UserBanner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\buttons\\PrimaryButton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\PayoutRangeDisplay.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\ProfileQuickLook.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\formatText.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\categoryUtils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\paymentsApi.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\notificationsApi.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\inputs\\InlineTabSelector.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\RichTextArea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\AttachmentsInput.tsx", ["2514"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\ControlledDropdown.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\reportsApi.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\ProgramTargets.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\pagination\\PaginatedCardContainer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\ManageProgram.tsx", ["2515", "2516", "2517", "2518", "2519", "2520", "2521", "2522", "2523", "2524", "2525", "2526"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\BrowserCodeIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CircleCodeIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\SortArrows.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\dashboard\\adminDashboard.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\dashboard\\adminDashboardParser.ts", ["2527", "2528"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\programs\\programFullview.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\reports\\getReport.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\business\\assistant.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\cards\\ProgramInfoSidebar.tsx", ["2529"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\cards\\RecentUpdatesRightbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\programs\\cards\\ActionButtons.tsx", ["2530"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\SeverityScoreSelector.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\pagination\\PageSelector.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\zipAttachments.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\reports\\VulnerabilityFlag.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\reports\\ReportQuickProgramBanner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\pages\\researcher\\paymentMethodForms\\BankAcount.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\pages\\researcher\\paymentMethodForms\\Paypal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\pages\\researcher\\paymentMethodForms\\OrbitRemix.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\programs\\ProgramTargetsEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\reports\\scopeStats.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\reports\\target.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\retests\\retestDetails.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\reports\\ReportStatusPill.tsx", ["2531"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\table\\useTableDropdown.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\editor\\inputs\\EditorDropdown.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\reports\\utils\\FIlterSectionReports.tsx", ["2532", "2533", "2534", "2535", "2536", "2537", "2538", "2539", "2540", "2541", "2542", "2543", "2544", "2545", "2546", "2547", "2548", "2549", "2550", "2551", "2552", "2553", "2554", "2555"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\CommentRichTextArea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\multi_tenant\\SearchBar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\buttons\\CloseButton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\retests\\useCommentForm.ts", ["2556"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\pills\\PillList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\ToggleRow.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\ReportTemplate.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\PreviewSection.tsx", ["2557", "2558", "2559", "2560", "2561", "2562", "2563"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\ChangeLogSidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\SectionRenderer.tsx", ["2564", "2565", "2566", "2567"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\UpArrowIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\DownArrowIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ResetIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\PDFDownloader.tsx", ["2568"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\EditorNavigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\LogsPage\\LogItems.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\utils\\StatusFormatter.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\utils\\FilterSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\Table\\RetestTableRow.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\dashboard\\useFilteredSeverityOverview.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\headers\\DashboardTitle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\utils\\searchBar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\dashboard\\dashboardApi.ts", ["2569"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\common\\DashboardItemCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\dashboard\\business\\BugLifecycleProgress.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\program-reports\\program-reports.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\chat\\chat.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\modal\\FullscreenModalLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\PaymentIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\RejectedIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CommentIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\CertificateIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\NotificationInfoIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\ProfileLink.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\SettingsNav.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\ApprovedIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\SettingsPageLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\BrowseIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\file_upload\\useImageCrop.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\cvss31\\cvsscalc31.js", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\pages\\Security.tsx", ["2570"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\pages\\Account.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\user\\usePrivateAccessUsers.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\ExternalLink.tsx", ["2571", "2572"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\thirdPartyIntegrationsApi.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\ControlledButtonOptions.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\pages\\business\\EditBusinessProfile.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\pages\\researcher\\Verification.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\pages\\researcher\\EditResearcherProfile.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\DualProfileQuickLook.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\inputs\\SettingsTextBox.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\reports\\parseTarget.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\api\\endpoints\\retests\\parseRetests.ts", ["2573"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\table\\TableDropdown.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\pills\\PillInput.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\Toggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\ReportTemplatePDF.tsx", ["2574"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\pdfHtmlImageUtils.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\ChartImageGenerator.tsx", [], ["2575", "2576", "2577"], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\FullReportTemplatePDF.tsx", ["2578"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\retests\\utils\\helper.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\MethodologyEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\CoverPageEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\DisclaimerEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\ExecutiveSummaryEditor.tsx", ["2579"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\ConclusionEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\RecommendationsEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\DocumentReferenceEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\FindingsSummaryEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\ProjectObjectivesEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\FindingsEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\ScopeEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\VulnerabilityRatingsEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\HighFindingsEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\LowFindingsEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\BrandingEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\MediumFindingsEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\CriticalFindingsEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\KeyFindingsEditor.tsx", ["2580", "2581"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\UnlockedIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\assets\\icons\\LockIcon.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\MethodologySelectorEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\SettingsTitle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\common\\HelpText.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\file_upload\\ImageCrop.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\pages\\EditProfile.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\inputs\\SettingsTextArea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\profile\\ProfileImage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\SectionPageContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\DisclaimerPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\CoverPage.tsx", ["2582"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\DocumentReferencePage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\ScopePage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\KeyFindingsPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\TableOfContentsPage.tsx", ["2583"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\SummaryOfFindingsPage.tsx", ["2584"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\ProjectObjectivesPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\ExecutiveSummaryPage.tsx", ["2585", "2586"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\KeyFindingsListPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\VulnerabilityRatingDefinitionsPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\MethodologyPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\NetworkMethodologyPage.tsx", ["2587", "2588", "2589", "2590", "2591"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\AppendixOwaspRiskRatingPages.tsx", ["2592", "2593"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\HtmlEditor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\DetailedFindingsCoverPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\DetailedFindingsBySeverityPage.tsx", ["2594", "2595", "2596"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\MobileMethodologyPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\FullTableOfContentsPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\sections\\RecommendationsPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\components\\editors\\BaseFindingEditor.tsx", ["2597"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\TextArea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\settings\\inputs\\ProfileImageUploader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\utils\\defaultContent.ts", ["2598"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\forms\\inputs\\FileInput.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\pages\\automated-report\\types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\file_upload\\FileDropZone.tsx", ["2599"], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\utils\\hooks\\useAcceptedFileTypes.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\jira-old\\CTB-Frontend\\src\\components\\modals\\JiraSetupGuideModal.tsx", ["2600"], [], {"ruleId": "2601", "severity": 1, "message": "2602", "line": 8, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 8, "endColumn": 24}, {"ruleId": "2601", "severity": 1, "message": "2605", "line": 9, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 9, "endColumn": 25}, {"ruleId": "2601", "severity": 1, "message": "2606", "line": 10, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 10, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2607", "line": 11, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 11, "endColumn": 18}, {"ruleId": "2601", "severity": 1, "message": "2608", "line": 12, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 12, "endColumn": 24}, {"ruleId": "2601", "severity": 1, "message": "2609", "line": 13, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 13, "endColumn": 18}, {"ruleId": "2601", "severity": 1, "message": "2610", "line": 4, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 4, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2611", "line": 4, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 4, "endColumn": 32}, {"ruleId": "2601", "severity": 1, "message": "2611", "line": 4, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 4, "endColumn": 32}, {"ruleId": "2601", "severity": 1, "message": "2612", "line": 7, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 7, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2613", "line": 8, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 8, "endColumn": 29}, {"ruleId": "2601", "severity": 1, "message": "2614", "line": 9, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 9, "endColumn": 26}, {"ruleId": "2601", "severity": 1, "message": "2605", "line": 12, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 12, "endColumn": 25}, {"ruleId": "2601", "severity": 1, "message": "2606", "line": 13, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 13, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2615", "line": 8, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 8, "endColumn": 22}, {"ruleId": "2601", "severity": 1, "message": "2616", "line": 4, "column": 3, "nodeType": "2603", "messageId": "2604", "endLine": 4, "endColumn": 14}, {"ruleId": "2601", "severity": 1, "message": "2617", "line": 9, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 9, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2618", "line": 10, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 10, "endColumn": 23}, {"ruleId": "2601", "severity": 1, "message": "2619", "line": 11, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 11, "endColumn": 13}, {"ruleId": "2601", "severity": 1, "message": "2620", "line": 1, "column": 40, "nodeType": "2603", "messageId": "2604", "endLine": 1, "endColumn": 47}, {"ruleId": "2601", "severity": 1, "message": "2621", "line": 28, "column": 13, "nodeType": "2603", "messageId": "2604", "endLine": 28, "endColumn": 17}, {"ruleId": "2601", "severity": 1, "message": "2622", "line": 1, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 1, "endColumn": 15}, {"ruleId": "2601", "severity": 1, "message": "2623", "line": 7, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 7, "endColumn": 18}, {"ruleId": "2601", "severity": 1, "message": "2624", "line": 13, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 13, "endColumn": 24}, {"ruleId": "2601", "severity": 1, "message": "2625", "line": 35, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 35, "endColumn": 24}, {"ruleId": "2601", "severity": 1, "message": "2626", "line": 65, "column": 31, "nodeType": "2603", "messageId": "2604", "endLine": 65, "endColumn": 44}, {"ruleId": "2601", "severity": 1, "message": "2617", "line": 16, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 16, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2627", "line": 28, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 28, "endColumn": 20}, {"ruleId": "2601", "severity": 1, "message": "2628", "line": 15, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 15, "endColumn": 24}, {"ruleId": "2629", "severity": 1, "message": "2630", "line": 106, "column": 6, "nodeType": "2631", "endLine": 106, "endColumn": 33, "suggestions": "2632"}, {"ruleId": "2601", "severity": 1, "message": "2633", "line": 45, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 45, "endColumn": 30}, {"ruleId": "2601", "severity": 1, "message": "2634", "line": 53, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 53, "endColumn": 25}, {"ruleId": "2601", "severity": 1, "message": "2635", "line": 16, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 16, "endColumn": 24}, {"ruleId": "2601", "severity": 1, "message": "2636", "line": 1, "column": 38, "nodeType": "2603", "messageId": "2604", "endLine": 1, "endColumn": 49}, {"ruleId": "2601", "severity": 1, "message": "2620", "line": 1, "column": 51, "nodeType": "2603", "messageId": "2604", "endLine": 1, "endColumn": 58}, {"ruleId": "2601", "severity": 1, "message": "2637", "line": 1, "column": 60, "nodeType": "2603", "messageId": "2604", "endLine": 1, "endColumn": 66}, {"ruleId": "2601", "severity": 1, "message": "2638", "line": 2, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 2, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2639", "line": 3, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 3, "endColumn": 30}, {"ruleId": "2601", "severity": 1, "message": "2640", "line": 3, "column": 32, "nodeType": "2603", "messageId": "2604", "endLine": 3, "endColumn": 57}, {"ruleId": "2601", "severity": 1, "message": "2641", "line": 4, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 4, "endColumn": 13}, {"ruleId": "2601", "severity": 1, "message": "2642", "line": 5, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 5, "endColumn": 20}, {"ruleId": "2601", "severity": 1, "message": "2643", "line": 6, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 6, "endColumn": 24}, {"ruleId": "2601", "severity": 1, "message": "2644", "line": 7, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 7, "endColumn": 22}, {"ruleId": "2601", "severity": 1, "message": "2645", "line": 8, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 8, "endColumn": 23}, {"ruleId": "2601", "severity": 1, "message": "2646", "line": 9, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 9, "endColumn": 31}, {"ruleId": "2601", "severity": 1, "message": "2647", "line": 10, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 10, "endColumn": 30}, {"ruleId": "2601", "severity": 1, "message": "2648", "line": 11, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 11, "endColumn": 24}, {"ruleId": "2601", "severity": 1, "message": "2649", "line": 12, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 12, "endColumn": 25}, {"ruleId": "2601", "severity": 1, "message": "2650", "line": 13, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 13, "endColumn": 29}, {"ruleId": "2601", "severity": 1, "message": "2651", "line": 14, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 14, "endColumn": 24}, {"ruleId": "2601", "severity": 1, "message": "2652", "line": 15, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 15, "endColumn": 22}, {"ruleId": "2601", "severity": 1, "message": "2653", "line": 16, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 16, "endColumn": 27}, {"ruleId": "2601", "severity": 1, "message": "2654", "line": 17, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 17, "endColumn": 31}, {"ruleId": "2601", "severity": 1, "message": "2655", "line": 18, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 18, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2656", "line": 19, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 19, "endColumn": 29}, {"ruleId": "2601", "severity": 1, "message": "2657", "line": 20, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 20, "endColumn": 34}, {"ruleId": "2601", "severity": 1, "message": "2658", "line": 21, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 21, "endColumn": 30}, {"ruleId": "2601", "severity": 1, "message": "2659", "line": 22, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 22, "endColumn": 26}, {"ruleId": "2601", "severity": 1, "message": "2660", "line": 23, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 23, "endColumn": 28}, {"ruleId": "2601", "severity": 1, "message": "2661", "line": 24, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 24, "endColumn": 25}, {"ruleId": "2601", "severity": 1, "message": "2662", "line": 25, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 25, "endColumn": 25}, {"ruleId": "2601", "severity": 1, "message": "2623", "line": 27, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 27, "endColumn": 18}, {"ruleId": "2601", "severity": 1, "message": "2663", "line": 28, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 28, "endColumn": 24}, {"ruleId": "2601", "severity": 1, "message": "2664", "line": 29, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 29, "endColumn": 22}, {"ruleId": "2601", "severity": 1, "message": "2665", "line": 30, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 30, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2666", "line": 31, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 31, "endColumn": 25}, {"ruleId": "2601", "severity": 1, "message": "2667", "line": 45, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 45, "endColumn": 27}, {"ruleId": "2601", "severity": 1, "message": "2668", "line": 64, "column": 6, "nodeType": "2603", "messageId": "2604", "endLine": 64, "endColumn": 16}, {"ruleId": "2601", "severity": 1, "message": "2669", "line": 67, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 67, "endColumn": 32}, {"ruleId": "2601", "severity": 1, "message": "2670", "line": 72, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 72, "endColumn": 27}, {"ruleId": "2601", "severity": 1, "message": "2671", "line": 93, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 93, "endColumn": 32}, {"ruleId": "2601", "severity": 1, "message": "2672", "line": 109, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 109, "endColumn": 48}, {"ruleId": "2601", "severity": 1, "message": "2673", "line": 200, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 200, "endColumn": 50}, {"ruleId": "2601", "severity": 1, "message": "2674", "line": 309, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 309, "endColumn": 39}, {"ruleId": "2601", "severity": 1, "message": "2675", "line": 318, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 318, "endColumn": 23}, {"ruleId": "2601", "severity": 1, "message": "2676", "line": 319, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 319, "endColumn": 23}, {"ruleId": "2601", "severity": 1, "message": "2677", "line": 320, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 320, "endColumn": 27}, {"ruleId": "2601", "severity": 1, "message": "2678", "line": 339, "column": 5, "nodeType": "2603", "messageId": "2604", "endLine": 339, "endColumn": 20}, {"ruleId": "2601", "severity": 1, "message": "2679", "line": 341, "column": 5, "nodeType": "2603", "messageId": "2604", "endLine": 341, "endColumn": 22}, {"ruleId": "2601", "severity": 1, "message": "2680", "line": 343, "column": 5, "nodeType": "2603", "messageId": "2604", "endLine": 343, "endColumn": 23}, {"ruleId": "2601", "severity": 1, "message": "2681", "line": 345, "column": 5, "nodeType": "2603", "messageId": "2604", "endLine": 345, "endColumn": 17}, {"ruleId": "2601", "severity": 1, "message": "2682", "line": 347, "column": 5, "nodeType": "2603", "messageId": "2604", "endLine": 347, "endColumn": 24}, {"ruleId": "2601", "severity": 1, "message": "2683", "line": 353, "column": 5, "nodeType": "2603", "messageId": "2604", "endLine": 353, "endColumn": 13}, {"ruleId": "2601", "severity": 1, "message": "2684", "line": 354, "column": 5, "nodeType": "2603", "messageId": "2604", "endLine": 354, "endColumn": 16}, {"ruleId": "2601", "severity": 1, "message": "2685", "line": 356, "column": 5, "nodeType": "2603", "messageId": "2604", "endLine": 356, "endColumn": 16}, {"ruleId": "2601", "severity": 1, "message": "2686", "line": 363, "column": 5, "nodeType": "2603", "messageId": "2604", "endLine": 363, "endColumn": 18}, {"ruleId": "2601", "severity": 1, "message": "2687", "line": 364, "column": 5, "nodeType": "2603", "messageId": "2604", "endLine": 364, "endColumn": 27}, {"ruleId": "2601", "severity": 1, "message": "2688", "line": 365, "column": 5, "nodeType": "2603", "messageId": "2604", "endLine": 365, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2689", "line": 394, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 394, "endColumn": 25}, {"ruleId": "2601", "severity": 1, "message": "2690", "line": 395, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 395, "endColumn": 31}, {"ruleId": "2601", "severity": 1, "message": "2691", "line": 405, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 405, "endColumn": 26}, {"ruleId": "2601", "severity": 1, "message": "2692", "line": 408, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 408, "endColumn": 32}, {"ruleId": "2601", "severity": 1, "message": "2693", "line": 415, "column": 15, "nodeType": "2603", "messageId": "2604", "endLine": 415, "endColumn": 28}, {"ruleId": "2629", "severity": 1, "message": "2694", "line": 113, "column": 6, "nodeType": "2631", "endLine": 113, "endColumn": 21, "suggestions": "2695"}, {"ruleId": "2601", "severity": 1, "message": "2696", "line": 12, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 12, "endColumn": 20}, {"ruleId": "2601", "severity": 1, "message": "2697", "line": 13, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 13, "endColumn": 25}, {"ruleId": "2601", "severity": 1, "message": "2698", "line": 20, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 20, "endColumn": 13}, {"ruleId": "2601", "severity": 1, "message": "2699", "line": 13, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 13, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2700", "line": 71, "column": 14, "nodeType": "2603", "messageId": "2604", "endLine": 71, "endColumn": 28}, {"ruleId": "2601", "severity": 1, "message": "2701", "line": 8, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 8, "endColumn": 17}, {"ruleId": "2601", "severity": 1, "message": "2702", "line": 10, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 10, "endColumn": 16}, {"ruleId": "2601", "severity": 1, "message": "2703", "line": 11, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 11, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2704", "line": 1, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 1, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2705", "line": 9, "column": 37, "nodeType": "2603", "messageId": "2604", "endLine": 9, "endColumn": 44}, {"ruleId": "2601", "severity": 1, "message": "2706", "line": 2, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 2, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2707", "line": 2, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 2, "endColumn": 24}, {"ruleId": "2601", "severity": 1, "message": "2708", "line": 3, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 3, "endColumn": 30}, {"ruleId": "2601", "severity": 1, "message": "2709", "line": 4, "column": 26, "nodeType": "2603", "messageId": "2604", "endLine": 4, "endColumn": 43}, {"ruleId": "2601", "severity": 1, "message": "2710", "line": 12, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 12, "endColumn": 23}, {"ruleId": "2601", "severity": 1, "message": "2617", "line": 13, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 13, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2711", "line": 16, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 16, "endColumn": 20}, {"ruleId": "2601", "severity": 1, "message": "2712", "line": 17, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 17, "endColumn": 18}, {"ruleId": "2601", "severity": 1, "message": "2713", "line": 18, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 18, "endColumn": 20}, {"ruleId": "2601", "severity": 1, "message": "2714", "line": 19, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 19, "endColumn": 16}, {"ruleId": "2601", "severity": 1, "message": "2715", "line": 20, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 20, "endColumn": 20}, {"ruleId": "2601", "severity": 1, "message": "2716", "line": 21, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 21, "endColumn": 20}, {"ruleId": "2601", "severity": 1, "message": "2717", "line": 26, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 26, "endColumn": 16}, {"ruleId": "2601", "severity": 1, "message": "2718", "line": 27, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 27, "endColumn": 36}, {"ruleId": "2601", "severity": 1, "message": "2719", "line": 29, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 29, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2720", "line": 33, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 33, "endColumn": 24}, {"ruleId": "2601", "severity": 1, "message": "2721", "line": 42, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 42, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2722", "line": 42, "column": 21, "nodeType": "2603", "messageId": "2604", "endLine": 42, "endColumn": 33}, {"ruleId": "2601", "severity": 1, "message": "2723", "line": 43, "column": 20, "nodeType": "2603", "messageId": "2604", "endLine": 43, "endColumn": 29}, {"ruleId": "2601", "severity": 1, "message": "2626", "line": 43, "column": 31, "nodeType": "2603", "messageId": "2604", "endLine": 43, "endColumn": 44}, {"ruleId": "2601", "severity": 1, "message": "2724", "line": 70, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 70, "endColumn": 25}, {"ruleId": "2601", "severity": 1, "message": "2725", "line": 71, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 71, "endColumn": 15}, {"ruleId": "2601", "severity": 1, "message": "2726", "line": 140, "column": 18, "nodeType": "2603", "messageId": "2604", "endLine": 140, "endColumn": 40}, {"ruleId": "2601", "severity": 1, "message": "2621", "line": 25, "column": 11, "nodeType": "2603", "messageId": "2604", "endLine": 25, "endColumn": 15}, {"ruleId": "2601", "severity": 1, "message": "2727", "line": 26, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 26, "endColumn": 17}, {"ruleId": "2601", "severity": 1, "message": "2728", "line": 4, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 4, "endColumn": 13}, {"ruleId": "2601", "severity": 1, "message": "2729", "line": 1, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 1, "endColumn": 18}, {"ruleId": "2601", "severity": 1, "message": "2730", "line": 4, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 4, "endColumn": 27}, {"ruleId": "2601", "severity": 1, "message": "2731", "line": 5, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 5, "endColumn": 23}, {"ruleId": "2601", "severity": 1, "message": "2710", "line": 8, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 8, "endColumn": 23}, {"ruleId": "2601", "severity": 1, "message": "2617", "line": 9, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 9, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2712", "line": 10, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 10, "endColumn": 18}, {"ruleId": "2601", "severity": 1, "message": "2714", "line": 11, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 11, "endColumn": 16}, {"ruleId": "2601", "severity": 1, "message": "2713", "line": 12, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 12, "endColumn": 20}, {"ruleId": "2601", "severity": 1, "message": "2727", "line": 15, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 15, "endColumn": 17}, {"ruleId": "2601", "severity": 1, "message": "2621", "line": 21, "column": 11, "nodeType": "2603", "messageId": "2604", "endLine": 21, "endColumn": 15}, {"ruleId": "2601", "severity": 1, "message": "2732", "line": 23, "column": 46, "nodeType": "2603", "messageId": "2604", "endLine": 23, "endColumn": 67}, {"ruleId": "2601", "severity": 1, "message": "2733", "line": 14, "column": 27, "nodeType": "2603", "messageId": "2604", "endLine": 14, "endColumn": 45}, {"ruleId": "2601", "severity": 1, "message": "2734", "line": 13, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 13, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2735", "line": 14, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 14, "endColumn": 20}, {"ruleId": "2601", "severity": 1, "message": "2736", "line": 5, "column": 3, "nodeType": "2603", "messageId": "2604", "endLine": 5, "endColumn": 14}, {"ruleId": "2601", "severity": 1, "message": "2737", "line": 6, "column": 3, "nodeType": "2603", "messageId": "2604", "endLine": 6, "endColumn": 16}, {"ruleId": "2601", "severity": 1, "message": "2738", "line": 7, "column": 3, "nodeType": "2603", "messageId": "2604", "endLine": 7, "endColumn": 9}, {"ruleId": "2601", "severity": 1, "message": "2739", "line": 8, "column": 3, "nodeType": "2603", "messageId": "2604", "endLine": 8, "endColumn": 10}, {"ruleId": "2601", "severity": 1, "message": "2740", "line": 9, "column": 3, "nodeType": "2603", "messageId": "2604", "endLine": 9, "endColumn": 9}, {"ruleId": "2601", "severity": 1, "message": "2741", "line": 10, "column": 3, "nodeType": "2603", "messageId": "2604", "endLine": 10, "endColumn": 22}, {"ruleId": "2601", "severity": 1, "message": "2742", "line": 17, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 17, "endColumn": 26}, {"ruleId": "2601", "severity": 1, "message": "2727", "line": 31, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 31, "endColumn": 17}, {"ruleId": "2601", "severity": 1, "message": "2704", "line": 1, "column": 27, "nodeType": "2603", "messageId": "2604", "endLine": 1, "endColumn": 36}, {"ruleId": "2629", "severity": 1, "message": "2743", "line": 30, "column": 6, "nodeType": "2631", "endLine": 30, "endColumn": 14, "suggestions": "2744"}, {"ruleId": "2601", "severity": 1, "message": "2745", "line": 4, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 4, "endColumn": 12}, {"ruleId": "2601", "severity": 1, "message": "2727", "line": 91, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 91, "endColumn": 17}, {"ruleId": "2601", "severity": 1, "message": "2665", "line": 4, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 4, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2645", "line": 4, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 4, "endColumn": 23}, {"ruleId": "2601", "severity": 1, "message": "2646", "line": 5, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 5, "endColumn": 31}, {"ruleId": "2601", "severity": 1, "message": "2647", "line": 6, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 6, "endColumn": 30}, {"ruleId": "2601", "severity": 1, "message": "2648", "line": 7, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 7, "endColumn": 24}, {"ruleId": "2601", "severity": 1, "message": "2649", "line": 8, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 8, "endColumn": 25}, {"ruleId": "2601", "severity": 1, "message": "2650", "line": 9, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 9, "endColumn": 29}, {"ruleId": "2601", "severity": 1, "message": "2651", "line": 10, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 10, "endColumn": 24}, {"ruleId": "2601", "severity": 1, "message": "2652", "line": 11, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 11, "endColumn": 22}, {"ruleId": "2601", "severity": 1, "message": "2653", "line": 12, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 12, "endColumn": 27}, {"ruleId": "2601", "severity": 1, "message": "2654", "line": 13, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 13, "endColumn": 31}, {"ruleId": "2601", "severity": 1, "message": "2655", "line": 14, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 14, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2656", "line": 15, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 15, "endColumn": 29}, {"ruleId": "2601", "severity": 1, "message": "2657", "line": 16, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 16, "endColumn": 34}, {"ruleId": "2601", "severity": 1, "message": "2658", "line": 17, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 17, "endColumn": 30}, {"ruleId": "2601", "severity": 1, "message": "2659", "line": 18, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 18, "endColumn": 26}, {"ruleId": "2601", "severity": 1, "message": "2660", "line": 19, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 19, "endColumn": 28}, {"ruleId": "2601", "severity": 1, "message": "2661", "line": 20, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 20, "endColumn": 25}, {"ruleId": "2601", "severity": 1, "message": "2662", "line": 21, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 21, "endColumn": 25}, {"ruleId": "2601", "severity": 1, "message": "2642", "line": 27, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 27, "endColumn": 20}, {"ruleId": "2601", "severity": 1, "message": "2746", "line": 87, "column": 5, "nodeType": "2603", "messageId": "2604", "endLine": 87, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2747", "line": 114, "column": 5, "nodeType": "2603", "messageId": "2604", "endLine": 114, "endColumn": 23}, {"ruleId": "2601", "severity": 1, "message": "2748", "line": 120, "column": 5, "nodeType": "2603", "messageId": "2604", "endLine": 120, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2749", "line": 121, "column": 5, "nodeType": "2603", "messageId": "2604", "endLine": 121, "endColumn": 18}, {"ruleId": "2601", "severity": 1, "message": "2750", "line": 122, "column": 5, "nodeType": "2603", "messageId": "2604", "endLine": 122, "endColumn": 17}, {"ruleId": "2601", "severity": 1, "message": "2751", "line": 123, "column": 5, "nodeType": "2603", "messageId": "2604", "endLine": 123, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2752", "line": 128, "column": 5, "nodeType": "2603", "messageId": "2604", "endLine": 128, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2753", "line": 129, "column": 5, "nodeType": "2603", "messageId": "2604", "endLine": 129, "endColumn": 16}, {"ruleId": "2629", "severity": 1, "message": "2754", "line": 79, "column": 6, "nodeType": "2631", "endLine": 79, "endColumn": 27, "suggestions": "2755"}, {"ruleId": "2629", "severity": 1, "message": "2756", "line": 86, "column": 6, "nodeType": "2631", "endLine": 86, "endColumn": 22, "suggestions": "2757"}, {"ruleId": "2601", "severity": 1, "message": "2704", "line": 1, "column": 27, "nodeType": "2603", "messageId": "2604", "endLine": 1, "endColumn": 36}, {"ruleId": "2601", "severity": 1, "message": "2623", "line": 2, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 2, "endColumn": 18}, {"ruleId": "2601", "severity": 1, "message": "2623", "line": 8, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 8, "endColumn": 18}, {"ruleId": "2601", "severity": 1, "message": "2758", "line": 9, "column": 39, "nodeType": "2603", "messageId": "2604", "endLine": 9, "endColumn": 45}, {"ruleId": "2601", "severity": 1, "message": "2759", "line": 15, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 15, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2675", "line": 16, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 16, "endColumn": 23}, {"ruleId": "2601", "severity": 1, "message": "2676", "line": 17, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 17, "endColumn": 23}, {"ruleId": "2601", "severity": 1, "message": "2669", "line": 21, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 21, "endColumn": 32}, {"ruleId": "2601", "severity": 1, "message": "2670", "line": 26, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 26, "endColumn": 27}, {"ruleId": "2601", "severity": 1, "message": "2671", "line": 47, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 47, "endColumn": 32}, {"ruleId": "2629", "severity": 1, "message": "2760", "line": 495, "column": 6, "nodeType": "2631", "endLine": 495, "endColumn": 36, "suggestions": "2761"}, {"ruleId": "2629", "severity": 1, "message": "2762", "line": 220, "column": 6, "nodeType": "2631", "endLine": 220, "endColumn": 18, "suggestions": "2763", "suppressions": "2764"}, {"ruleId": "2601", "severity": 1, "message": "2641", "line": 2, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 2, "endColumn": 13}, {"ruleId": "2601", "severity": 1, "message": "2765", "line": 2, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 2, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2766", "line": 3, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 3, "endColumn": 25}, {"ruleId": "2601", "severity": 1, "message": "2767", "line": 119, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 119, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2767", "line": 163, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 163, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2768", "line": 3, "column": 61, "nodeType": "2603", "messageId": "2604", "endLine": 3, "endColumn": 69}, {"ruleId": "2601", "severity": 1, "message": "2769", "line": 11, "column": 3, "nodeType": "2603", "messageId": "2604", "endLine": 11, "endColumn": 9}, {"ruleId": "2601", "severity": 1, "message": "2770", "line": 9, "column": 41, "nodeType": "2603", "messageId": "2604", "endLine": 9, "endColumn": 46}, {"ruleId": "2629", "severity": 1, "message": "2771", "line": 177, "column": 9, "nodeType": "2772", "endLine": 177, "endColumn": 45}, {"ruleId": "2629", "severity": 1, "message": "2773", "line": 177, "column": 9, "nodeType": "2772", "endLine": 177, "endColumn": 45}, {"ruleId": "2629", "severity": 1, "message": "2774", "line": 177, "column": 9, "nodeType": "2772", "endLine": 177, "endColumn": 45}, {"ruleId": "2601", "severity": 1, "message": "2775", "line": 228, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 228, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2776", "line": 33, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 33, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2777", "line": 65, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 65, "endColumn": 25}, {"ruleId": "2601", "severity": 1, "message": "2778", "line": 197, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 197, "endColumn": 22}, {"ruleId": "2601", "severity": 1, "message": "2779", "line": 240, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 240, "endColumn": 22}, {"ruleId": "2601", "severity": 1, "message": "2780", "line": 243, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 243, "endColumn": 22}, {"ruleId": "2601", "severity": 1, "message": "2781", "line": 243, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 243, "endColumn": 21}, {"ruleId": "2629", "severity": 1, "message": "2782", "line": 64, "column": 6, "nodeType": "2631", "endLine": 64, "endColumn": 16, "suggestions": "2783"}, {"ruleId": "2629", "severity": 1, "message": "2784", "line": 61, "column": 6, "nodeType": "2631", "endLine": 61, "endColumn": 8, "suggestions": "2785"}, {"ruleId": "2601", "severity": 1, "message": "2786", "line": 6, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 6, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2617", "line": 16, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 16, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2730", "line": 26, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 26, "endColumn": 27}, {"ruleId": "2601", "severity": 1, "message": "2710", "line": 27, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 27, "endColumn": 23}, {"ruleId": "2601", "severity": 1, "message": "2787", "line": 28, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 28, "endColumn": 16}, {"ruleId": "2601", "severity": 1, "message": "2731", "line": 29, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 29, "endColumn": 23}, {"ruleId": "2601", "severity": 1, "message": "2709", "line": 30, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 30, "endColumn": 27}, {"ruleId": "2601", "severity": 1, "message": "2727", "line": 37, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 37, "endColumn": 17}, {"ruleId": "2601", "severity": 1, "message": "2723", "line": 39, "column": 31, "nodeType": "2603", "messageId": "2604", "endLine": 39, "endColumn": 40}, {"ruleId": "2601", "severity": 1, "message": "2788", "line": 39, "column": 42, "nodeType": "2603", "messageId": "2604", "endLine": 39, "endColumn": 51}, {"ruleId": "2601", "severity": 1, "message": "2789", "line": 39, "column": 53, "nodeType": "2603", "messageId": "2604", "endLine": 39, "endColumn": 65}, {"ruleId": "2601", "severity": 1, "message": "2790", "line": 144, "column": 12, "nodeType": "2603", "messageId": "2604", "endLine": 144, "endColumn": 25}, {"ruleId": "2601", "severity": 1, "message": "2791", "line": 1, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 1, "endColumn": 14}, {"ruleId": "2601", "severity": 1, "message": "2792", "line": 2, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 2, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2704", "line": 1, "column": 20, "nodeType": "2603", "messageId": "2604", "endLine": 1, "endColumn": 29}, {"ruleId": "2601", "severity": 1, "message": "2723", "line": 24, "column": 20, "nodeType": "2603", "messageId": "2604", "endLine": 24, "endColumn": 29}, {"ruleId": "2601", "severity": 1, "message": "2745", "line": 8, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 8, "endColumn": 12}, {"ruleId": "2601", "severity": 1, "message": "2793", "line": 3, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 3, "endColumn": 20}, {"ruleId": "2601", "severity": 1, "message": "2794", "line": 3, "column": 22, "nodeType": "2603", "messageId": "2604", "endLine": 3, "endColumn": 30}, {"ruleId": "2601", "severity": 1, "message": "2795", "line": 13, "column": 3, "nodeType": "2603", "messageId": "2604", "endLine": 13, "endColumn": 9}, {"ruleId": "2601", "severity": 1, "message": "2617", "line": 22, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 22, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2796", "line": 24, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 24, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2797", "line": 25, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 25, "endColumn": 23}, {"ruleId": "2601", "severity": 1, "message": "2798", "line": 49, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 49, "endColumn": 23}, {"ruleId": "2601", "severity": 1, "message": "2799", "line": 55, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 55, "endColumn": 18}, {"ruleId": "2601", "severity": 1, "message": "2800", "line": 123, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 123, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2801", "line": 134, "column": 29, "nodeType": "2603", "messageId": "2604", "endLine": 134, "endColumn": 49}, {"ruleId": "2601", "severity": 1, "message": "2802", "line": 138, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 138, "endColumn": 23}, {"ruleId": "2601", "severity": 1, "message": "2803", "line": 139, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 139, "endColumn": 25}, {"ruleId": "2601", "severity": 1, "message": "2804", "line": 142, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 142, "endColumn": 25}, {"ruleId": "2601", "severity": 1, "message": "2805", "line": 146, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 146, "endColumn": 31}, {"ruleId": "2601", "severity": 1, "message": "2806", "line": 163, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 163, "endColumn": 26}, {"ruleId": "2601", "severity": 1, "message": "2807", "line": 164, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 164, "endColumn": 26}, {"ruleId": "2601", "severity": 1, "message": "2808", "line": 165, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 165, "endColumn": 23}, {"ruleId": "2601", "severity": 1, "message": "2809", "line": 166, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 166, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2810", "line": 244, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 244, "endColumn": 20}, {"ruleId": "2601", "severity": 1, "message": "2811", "line": 381, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 381, "endColumn": 30}, {"ruleId": "2601", "severity": 1, "message": "2812", "line": 385, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 385, "endColumn": 37}, {"ruleId": "2601", "severity": 1, "message": "2727", "line": 445, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 445, "endColumn": 17}, {"ruleId": "2601", "severity": 1, "message": "2813", "line": 447, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 447, "endColumn": 28}, {"ruleId": "2601", "severity": 1, "message": "2814", "line": 506, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 506, "endColumn": 26}, {"ruleId": "2601", "severity": 1, "message": "2815", "line": 29, "column": 13, "nodeType": "2603", "messageId": "2604", "endLine": 29, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2816", "line": 2, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 2, "endColumn": 13}, {"ruleId": "2601", "severity": 1, "message": "2817", "line": 3, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 3, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2818", "line": 23, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 23, "endColumn": 19}, {"ruleId": "2601", "severity": 1, "message": "2819", "line": 24, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 24, "endColumn": 24}, {"ruleId": "2601", "severity": 1, "message": "2820", "line": 25, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 25, "endColumn": 14}, {"ruleId": "2601", "severity": 1, "message": "2821", "line": 35, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 35, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2822", "line": 36, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 36, "endColumn": 22}, {"ruleId": "2601", "severity": 1, "message": "2653", "line": 11, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 11, "endColumn": 27}, {"ruleId": "2601", "severity": 1, "message": "2642", "line": 23, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 23, "endColumn": 20}, {"ruleId": "2601", "severity": 1, "message": "2823", "line": 23, "column": 22, "nodeType": "2603", "messageId": "2604", "endLine": 23, "endColumn": 37}, {"ruleId": "2601", "severity": 1, "message": "2758", "line": 23, "column": 39, "nodeType": "2603", "messageId": "2604", "endLine": 23, "endColumn": 45}, {"ruleId": "2601", "severity": 1, "message": "2824", "line": 29, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 29, "endColumn": 34}, {"ruleId": "2601", "severity": 1, "message": "2825", "line": 31, "column": 11, "nodeType": "2603", "messageId": "2604", "endLine": 31, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2727", "line": 30, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 30, "endColumn": 17}, {"ruleId": "2601", "severity": 1, "message": "2617", "line": 3, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 3, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2826", "line": 46, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 46, "endColumn": 24}, {"ruleId": "2601", "severity": 1, "message": "2792", "line": 2, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 2, "endColumn": 21}, {"ruleId": "2601", "severity": 1, "message": "2827", "line": 2, "column": 32, "nodeType": "2603", "messageId": "2604", "endLine": 2, "endColumn": 36}, {"ruleId": "2629", "severity": 1, "message": "2828", "line": 45, "column": 6, "nodeType": "2631", "endLine": 45, "endColumn": 59, "suggestions": "2829", "suppressions": "2830"}, {"ruleId": "2629", "severity": 1, "message": "2831", "line": 45, "column": 13, "nodeType": "2832", "endLine": 45, "endColumn": 33, "suppressions": "2833"}, {"ruleId": "2629", "severity": 1, "message": "2831", "line": 45, "column": 35, "nodeType": "2832", "endLine": 45, "endColumn": 58, "suppressions": "2834"}, {"ruleId": "2601", "severity": 1, "message": "2827", "line": 2, "column": 32, "nodeType": "2603", "messageId": "2604", "endLine": 2, "endColumn": 36}, {"ruleId": "2601", "severity": 1, "message": "2835", "line": 18, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 18, "endColumn": 38}, {"ruleId": "2601", "severity": 1, "message": "2729", "line": 1, "column": 17, "nodeType": "2603", "messageId": "2604", "endLine": 1, "endColumn": 25}, {"ruleId": "2601", "severity": 1, "message": "2836", "line": 20, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 20, "endColumn": 57}, {"ruleId": "2601", "severity": 1, "message": "2837", "line": 18, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 18, "endColumn": 22}, {"ruleId": "2601", "severity": 1, "message": "2838", "line": 17, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 17, "endColumn": 27}, {"ruleId": "2601", "severity": 1, "message": "2839", "line": 37, "column": 11, "nodeType": "2603", "messageId": "2604", "endLine": 37, "endColumn": 28}, {"ruleId": "2601", "severity": 1, "message": "2836", "line": 6, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 6, "endColumn": 45}, {"ruleId": "2601", "severity": 1, "message": "2840", "line": 97, "column": 9, "nodeType": "2603", "messageId": "2604", "endLine": 97, "endColumn": 27}, {"ruleId": "2841", "severity": 1, "message": "2842", "line": 196, "column": 75, "nodeType": "2843", "messageId": "2844", "endLine": 196, "endColumn": 76, "suggestions": "2845"}, {"ruleId": "2841", "severity": 1, "message": "2846", "line": 196, "column": 84, "nodeType": "2843", "messageId": "2844", "endLine": 196, "endColumn": 85, "suggestions": "2847"}, {"ruleId": "2841", "severity": 1, "message": "2848", "line": 196, "column": 94, "nodeType": "2843", "messageId": "2844", "endLine": 196, "endColumn": 95, "suggestions": "2849"}, {"ruleId": "2841", "severity": 1, "message": "2850", "line": 196, "column": 102, "nodeType": "2843", "messageId": "2844", "endLine": 196, "endColumn": 103, "suggestions": "2851"}, {"ruleId": "2841", "severity": 1, "message": "2852", "line": 196, "column": 117, "nodeType": "2843", "messageId": "2844", "endLine": 196, "endColumn": 118, "suggestions": "2853"}, {"ruleId": "2601", "severity": 1, "message": "2854", "line": 5, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 5, "endColumn": 15}, {"ruleId": "2601", "severity": 1, "message": "2855", "line": 25, "column": 7, "nodeType": "2603", "messageId": "2604", "endLine": 25, "endColumn": 17}, {"ruleId": "2601", "severity": 1, "message": "2856", "line": 3, "column": 29, "nodeType": "2603", "messageId": "2604", "endLine": 3, "endColumn": 55}, {"ruleId": "2601", "severity": 1, "message": "2857", "line": 3, "column": 57, "nodeType": "2603", "messageId": "2604", "endLine": 3, "endColumn": 76}, {"ruleId": "2601", "severity": 1, "message": "2858", "line": 18, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 18, "endColumn": 30}, {"ruleId": "2601", "severity": 1, "message": "2859", "line": 3, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 3, "endColumn": 18}, {"ruleId": "2601", "severity": 1, "message": "2860", "line": 2, "column": 10, "nodeType": "2603", "messageId": "2604", "endLine": 2, "endColumn": 23}, {"ruleId": "2601", "severity": 1, "message": "2861", "line": 2, "column": 8, "nodeType": "2603", "messageId": "2604", "endLine": 2, "endColumn": 18}, {"ruleId": "2601", "severity": 1, "message": "2862", "line": 2, "column": 23, "nodeType": "2603", "messageId": "2604", "endLine": 2, "endColumn": 35}, "@typescript-eslint/no-unused-vars", "'PaginatedReports' is defined but never used.", "Identifier", "unusedVar", "'PaginatedPrograms' is defined but never used.", "'ProgramPage' is defined but never used.", "'ReportPage' is defined but never used.", "'RetestManagement' is defined but never used.", "'RetestPage' is defined but never used.", "'DashboardGrid' is defined but never used.", "'UpdatedBusinessDashboard' is defined but never used.", "'ProgramEditor' is defined but never used.", "'PaginatedTransactions' is defined but never used.", "'InvitationsManager' is defined but never used.", "'ProgramTargets' is defined but never used.", "'ProgramType' is defined but never used.", "'OutlineButton' is defined but never used.", "'getPDFSummary' is defined but never used.", "'toast' is defined but never used.", "'useMemo' is defined but never used.", "'role' is assigned a value but never used.", "'CTBIcon' is defined but never used.", "'UserRole' is defined but never used.", "'SideNavMainButton' is assigned a value but never used.", "'isDateTodayOrPast' is assigned a value but never used.", "'deleteProgram' is assigned a value but never used.", "'isAdminRole' is assigned a value but never used.", "'CategorySelector' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useMemo has a missing dependency: 'saveReport'. Either include it or remove the dependency array.", "ArrayExpression", ["2863"], "'ReportRecommendation' is defined but never used.", "'retestRequested' is assigned a value but never used.", "'convertRoleToName' is assigned a value but never used.", "'useCallback' is defined but never used.", "'useRef' is defined but never used.", "'useParams' is defined but never used.", "'getProgramReportById' is defined but never used.", "'getProgramReportChangeLog' is defined but never used.", "'axios' is defined but never used.", "'ReportData' is defined but never used.", "'EditorNavigation' is defined but never used.", "'PreviewSection' is defined but never used.", "'CoverPageEditor' is defined but never used.", "'DocumentReferenceEditor' is defined but never used.", "'ExecutiveSummaryEditor' is defined but never used.", "'DisclaimerEditor' is defined but never used.", "'MethodologyEditor' is defined but never used.", "'RecommendationsEditor' is defined but never used.", "'ConclusionEditor' is defined but never used.", "'FindingsEditor' is defined but never used.", "'TargetDetailsEditor' is defined but never used.", "'ProjectObjectivesEditor' is defined but never used.", "'ScopeEditor' is defined but never used.", "'FindingsSummaryEditor' is defined but never used.", "'VulnerabilityRatingsEditor' is defined but never used.", "'CriticalFindingsEditor' is defined but never used.", "'HighFindingsEditor' is defined but never used.", "'MediumFindingsEditor' is defined but never used.", "'LowFindingsEditor' is defined but never used.", "'KeyFindingsEditor' is defined but never used.", "'ChangeLogSidebar' is defined but never used.", "'ReportTemplate' is defined but never used.", "'PDFDownloader' is defined but never used.", "'useReactToPrint' is defined but never used.", "'PREVIEW_UPDATE_DELAY' is assigned a value but never used.", "'StatusType' is defined but never used.", "'normalizeSeverityCategory' is assigned a value but never used.", "'getDefaultDisclaimer' is assigned a value but never used.", "'getDefaultOpenCloseCounts' is assigned a value but never used.", "'Notification' is assigned a value but never used.", "'FullViewModal' is assigned a value but never used.", "'ROLE_MAP' is assigned a value but never used.", "'MIN_EDITOR_WIDTH' is assigned a value but never used.", "'MAX_EDITOR_WIDTH' is assigned a value but never used.", "'DEFAULT_EDITOR_WIDTH' is assigned a value but never used.", "'setNotification' is assigned a value but never used.", "'setIsFullViewOpen' is assigned a value but never used.", "'setIsChangeLogOpen' is assigned a value but never used.", "'setChangeLog' is assigned a value but never used.", "'setChangeLogLoading' is assigned a value but never used.", "'viewMode' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "'handlePrint' is assigned a value but never used.", "'updatePreview' is assigned a value but never used.", "'debouncedUpdatePreview' is assigned a value but never used.", "'handleDataUpdate' is assigned a value but never used.", "'adminQaMessages' is assigned a value but never used.", "'adminBusinessMessages' is assigned a value but never used.", "'handleSendAdminQa' is assigned a value but never used.", "'handleSendAdminBusiness' is assigned a value but never used.", "'currentUserId' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchReports'. Either include it or remove the dependency array.", ["2864"], "'ComputerIcon' is defined but never used.", "'RecentReportsIcon' is defined but never used.", "'user' is assigned a value but never used.", "'FiRefreshCw' is defined but never used.", "'unifiedLoading' is assigned a value but never used.", "'PatchIcon' is defined but never used.", "'CashIcon' is defined but never used.", "'ReactNode' is defined but never used.", "'useEffect' is defined but never used.", "'refetch' is assigned a value but never used.", "'InfoMessage' is defined but never used.", "'BsClockHistory' is defined but never used.", "'IoCloudUploadOutline' is defined but never used.", "'HiOutlineChartPie' is defined but never used.", "'InlineContainer' is defined but never used.", "'CTBProgram' is defined but never used.", "'DeleteIcon' is defined but never used.", "'PieChartIcon' is defined but never used.", "'EditIcon' is defined but never used.", "'ErrorMessage' is defined but never used.", "'ProgramStats' is defined but never used.", "'InfoIcon' is defined but never used.", "'IoInformationCircleOutline' is defined but never used.", "'RecentUpdates' is defined but never used.", "'formatProgramType' is assigned a value but never used.", "'showStats' is assigned a value but never used.", "'setShowStats' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'loadingOverview' is assigned a value but never used.", "'error' is assigned a value but never used.", "'handleGenerateInsights' is defined but never used.", "'navigate' is assigned a value but never used.", "'parse' is defined but never used.", "'useState' is defined but never used.", "'BusinessProfileCard' is defined but never used.", "'ProgramInfoCard' is defined but never used.", "'deleteProgramFromHook' is assigned a value but never used.", "'setExpandedTitleId' is assigned a value but never used.", "'scopeData' is assigned a value but never used.", "'targetData' is assigned a value but never used.", "'FaChartLine' is defined but never used.", "'FaCheckCircle' is defined but never used.", "'FaCode' is defined but never used.", "'FaUsers' is defined but never used.", "'FaSync' is defined but never used.", "'FaExclamationCircle' is defined but never used.", "'TbDeviceImacCode' is defined but never used.", "React Hook useEffect has missing dependencies: 'filters' and 'onChange'. Either include them or remove the dependency array. If 'onChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2865"], "'Pill' is defined but never used.", "'setEditorWidth' is assigned a value but never used.", "'saveSectionChanges' is assigned a value but never used.", "'isFullViewOpen' is assigned a value but never used.", "'closeFullView' is assigned a value but never used.", "'notification' is assigned a value but never used.", "'hideNotification' is assigned a value but never used.", "'previewLoading' is assigned a value but never used.", "'sectionData' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getNextStatusOptions'. Either include it or remove the dependency array.", ["2866"], "React Hook useEffect has missing dependencies: 'methods' and 'setComment'. Either include them or remove the dependency array.", ["2867"], "'Report' is defined but never used.", "'STATUS_TYPES' is assigned a value but never used.", "React Hook useCallback has unnecessary dependencies: 'handleDataUpdate' and 'reportData'. Either exclude them or remove the dependency array.", ["2868"], "React Hook useEffect has a missing dependency: 'updatePreview'. Either include it or remove the dependency array.", ["2869"], ["2870"], "'ReportsIcon' is defined but never used.", "'DashboardItemCard' is defined but never used.", "'retryFetch' is assigned a value but never used.", "'FaFolder' is defined but never used.", "'Legend' is defined but never used.", "'FiEye' is defined but never used.", "The 'issues' logical expression could make the dependencies of useMemo Hook (at line 197) change on every render. To fix this, wrap the initialization of 'issues' in its own useMemo() Hook.", "VariableDeclarator", "The 'issues' logical expression could make the dependencies of useMemo Hook (at line 207) change on every render. To fix this, wrap the initialization of 'issues' in its own useMemo() Hook.", "The 'issues' logical expression could make the dependencies of useMemo Hook (at line 216) change on every render. To fix this, wrap the initialization of 'issues' in its own useMemo() Hook.", "'formatDate' is assigned a value but never used.", "'pulseAnimation' is assigned a value but never used.", "'cardHoverAnimation' is assigned a value but never used.", "'hasRetestData' is assigned a value but never used.", "'passRateTrend' is assigned a value but never used.", "'durationTrend' is assigned a value but never used.", "'isMobileView' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'backgroundColors', 'data', 'hoverBackgroundColors', and 'labels'. Either include them or remove the dependency array.", ["2871"], "React Hook useEffect has a missing dependency: 'value'. Either include it or remove the dependency array.", ["2872"], "'Collapsible' is defined but never used.", "'BiEdit' is defined but never used.", "'moreUsers' is assigned a value but never used.", "'loadNextPage' is assigned a value but never used.", "'deleteProgram' is defined but never used.", "'moment' is defined but never used.", "'DATE_FORMAT' is defined but never used.", "'BiSortAlt2' is defined but never used.", "'BiFilter' is defined but never used.", "'FaSort' is defined but never used.", "'FiColumns' is defined but never used.", "'FiChevronDown' is defined but never used.", "'isPentesterModal' is assigned a value but never used.", "'isNoneModal' is assigned a value but never used.", "'programName' is assigned a value but never used.", "'setPentesterUsername' is assigned a value but never used.", "'tempProgramId' is assigned a value but never used.", "'tempProgramName' is assigned a value but never used.", "'tempReportState' is assigned a value but never used.", "'tempPentesterUsername' is assigned a value but never used.", "'substatusModalRef' is assigned a value but never used.", "'pentesterModalRef' is assigned a value but never used.", "'togglePrograms' is assigned a value but never used.", "'toggleStates' is assigned a value but never used.", "'toggleModal' is assigned a value but never used.", "'handlePentesterChange' is assigned a value but never used.", "'handleSeverityCategoryChange' is assigned a value but never used.", "'handleClearSelected' is assigned a value but never used.", "'renderStatusModal' is assigned a value but never used.", "'response' is assigned a value but never used.", "'jsPDF' is defined but never used.", "'html2canvas' is defined but never used.", "'previewRef' is assigned a value but never used.", "'isContentReady' is assigned a value but never used.", "'zoom' is assigned a value but never used.", "'handleZoomIn' is assigned a value but never used.", "'handleZoomOut' is assigned a value but never used.", "'DetailedFinding' is defined but never used.", "'reportDataWithMethodology' is assigned a value but never used.", "'axiosError' is assigned a value but never used.", "'handleIntegrate' is assigned a value but never used.", "'Font' is defined but never used.", "React Hook useEffect has missing dependencies: 'data', 'onImageReady', and 'options'. Either include them or remove the dependency array. If 'onImageReady' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2873"], ["2874"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", ["2875"], ["2876"], "'Counter' is assigned a value but never used.", "'severityColors' is assigned a value but never used.", "'DEFAULT_COMPANY' is assigned a value but never used.", "'findingsBySeverity' is assigned a value but never used.", "'updateSectionPage' is assigned a value but never used.", "'testingDescription' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\S.", "TemplateElement", "unnecessaryEscape", ["2877", "2878"], "Unnecessary escape character: \\M.", ["2879", "2880"], "Unnecessary escape character: \\W.", ["2881", "2882"], "Unnecessary escape character: \\C.", ["2883", "2884"], "Unnecessary escape character: \\R.", ["2885", "2886"], "'headerBg' is assigned a value but never used.", "'appendixBg' is assigned a value but never used.", "'renderPdfElementsPlainText' is defined but never used.", "'parseHtmlToElements' is defined but never used.", "'formatDateToDDMMYYYY' is defined but never used.", "'HtmlEditor' is defined but never used.", "'ProgramDetail' is defined but never used.", "'CameraIcon' is defined but never used.", "'FiArrowRight' is defined but never used.", {"desc": "2887", "fix": "2888"}, {"desc": "2889", "fix": "2890"}, {"desc": "2891", "fix": "2892"}, {"desc": "2893", "fix": "2894"}, {"desc": "2895", "fix": "2896"}, {"desc": "2897", "fix": "2898"}, {"desc": "2899", "fix": "2900"}, {"kind": "2901", "justification": "2902"}, {"desc": "2903", "fix": "2904"}, {"desc": "2905", "fix": "2906"}, {"desc": "2907", "fix": "2908"}, {"kind": "2901", "justification": "2902"}, {"kind": "2901", "justification": "2902"}, {"kind": "2901", "justification": "2902"}, {"messageId": "2909", "fix": "2910", "desc": "2911"}, {"messageId": "2912", "fix": "2913", "desc": "2914"}, {"messageId": "2909", "fix": "2915", "desc": "2911"}, {"messageId": "2912", "fix": "2916", "desc": "2914"}, {"messageId": "2909", "fix": "2917", "desc": "2911"}, {"messageId": "2912", "fix": "2918", "desc": "2914"}, {"messageId": "2909", "fix": "2919", "desc": "2911"}, {"messageId": "2912", "fix": "2920", "desc": "2914"}, {"messageId": "2909", "fix": "2921", "desc": "2911"}, {"messageId": "2912", "fix": "2922", "desc": "2914"}, "Update the dependencies array to be: [isNewReport, isSubmitting, saveReport]", {"range": "2923", "text": "2924"}, "Update the dependencies array to be: [fetchReports, filters, role]", {"range": "2925", "text": "2926"}, "Update the dependencies array to be: [filters, onChange, search]", {"range": "2927", "text": "2928"}, "Update the dependencies array to be: [role, currentStatus, getNextStatusOptions]", {"range": "2929", "text": "2930"}, "Update the dependencies array to be: [methods, selectedStatus, setComment]", {"range": "2931", "text": "2932"}, "Update the dependencies array to be: []", {"range": "2933", "text": "2934"}, "Update the dependencies array to be: [reportData, updatePreview]", {"range": "2935", "text": "2936"}, "directive", "", "Update the dependencies array to be: [backgroundColors, chartRef, data, hoverBackgroundColors, labels]", {"range": "2937", "text": "2938"}, "Update the dependencies array to be: [value]", {"range": "2939", "text": "2940"}, "Update the dependencies array to be: [data, onImageReady, options, type]", {"range": "2941", "text": "2942"}, "removeEscape", {"range": "2943", "text": "2902"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "2944", "text": "2945"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "2946", "text": "2902"}, {"range": "2947", "text": "2945"}, {"range": "2948", "text": "2902"}, {"range": "2949", "text": "2945"}, {"range": "2950", "text": "2902"}, {"range": "2951", "text": "2945"}, {"range": "2952", "text": "2902"}, {"range": "2953", "text": "2945"}, [4080, 4107], "[isNewReport, isSubmitting, saveReport]", [3571, 3586], "[fetchReports, filters, role]", [781, 789], "[filters, onChange, search]", [3132, 3153], "[role, currentStatus, getNextStatusOptions]", [3375, 3391], "[methods, selectedStatus, setComment]", [21012, 21042], "[]", [9214, 9226], "[reportData, updatePreview]", [1771, 1781], "[backgroundColors, chartRef, data, hoverBackgroundColors, labels]", [1820, 1822], "[value]", [1257, 1310], "[data, onImageReady, options, type]", [13447, 13448], [13447, 13447], "\\", [13456, 13457], [13456, 13456], [13466, 13467], [13466, 13466], [13474, 13475], [13474, 13474], [13489, 13490], [13489, 13489]]