"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn("program_reports", "branding_logo", {
      type: Sequelize.STRING,
      allowNull: true,
      defaultValue: "https://i.postimg.cc/QVPV9d0x/Full-name-logo-blue.png", // Replace with your actual default logo URL
      comment: "White label logo URL"
    });
    await queryInterface.addColumn("program_reports", "branding_company", {
      type: Sequelize.STRING,
      allowNull: true,
      defaultValue: "Capture The Bug Limited",
      comment: "White label company name"
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn("program_reports", "branding_logo");
    await queryInterface.removeColumn("program_reports", "branding_company");
  }
};
