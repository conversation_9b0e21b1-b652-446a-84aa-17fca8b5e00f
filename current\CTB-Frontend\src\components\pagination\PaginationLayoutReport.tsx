import { PropsWithChildren, useRef } from "react";
import PageSelector from "./PageSelector";
import Footer from "../footer/Footer";

const PaginationLayoutReport = ({
  page,
  count,
  totalItems,
  children,
  className,
  setPage
}: PropsWithChildren<{
  page: number;
  count: number;
  totalItems: number;
  className?: string;
  setPage: (page: number) => void;
}>) => {
  const ref = useRef<HTMLDivElement>(null);

  return (
    <main
      ref={ref}
      className={
        "flex flex-grow flex-col items-center justify-between " + className
      }
    >
      <section className="w-full">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <tbody className="bg-white divide-y divide-gray-200">
              {children}
            </tbody>
          </table>
        </div>
      </section>

      <div className="flex w-full flex-col items-center">
        {totalItems > 0 && (
          <PageSelector
            className="mb-7 mt-7"
            page={page}
            count={count}
            totalItems={totalItems}
            setPage={page => {
              setPage(page);

              if (ref.current) ref.current.scroll(0, 0);
            }}
          />
        )}

        <Footer />
      </div>
    </main>
  );
};

export default PaginationLayoutReport;