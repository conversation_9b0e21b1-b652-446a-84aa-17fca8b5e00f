import React from 'react';
import { Page, View, Text } from '@react-pdf/renderer';
import { ReportData } from '../../types/report.types';
import { useSectionPages } from '../SectionPageContext';

interface DisclaimerPageProps {
  reportData: ReportData;
  disclaimerParagraphs: string[];
  processDisclaimerContent: (content: string) => string[];
  sectionId?: string;
}

const DEFAULT_COMPANY = 'Capture the bug Ltd.';

const DisclaimerPage: React.FC<DisclaimerPageProps> = ({ reportData, disclaimerParagraphs, processDisclaimerContent, sectionId }) => {
  const { updateSectionPage } = useSectionPages();
  return (
    <Page size="A4" id={sectionId} style={{
      flexDirection: 'column',
      backgroundColor: '#ffffff',
      padding: '20mm 15mm',
      fontFamily: 'Helvetica',
      fontSize: 12,
    }}>
      <View style={{
        paddingHorizontal: 24,
        flexDirection: 'column',
        flex: 1,
      }}>
        <View style={{
          flex: 1,
        }}>
          <Text style={{
            fontSize: 16,
            fontWeight: 'bold',
            color: '#2563eb',
            marginBottom: 16,
          }}>
            DISCLAIMER
          </Text>
          {disclaimerParagraphs.length > 0 ? (
            <>
              {disclaimerParagraphs.map((paragraph, index) => (
                <Text key={index} style={{
                  fontSize: 12,
                  lineHeight: 1.4,
                  marginBottom: 18,
                  color: '#374151',
                  textAlign: 'justify',
                  fontFamily: 'Helvetica',
                  fontWeight: 400,
                }}>
                  {paragraph.trim()}
                </Text>
              ))}
            </>
          ) : (
            <>
              {processDisclaimerContent(`<p>${reportData.branding_company || DEFAULT_COMPANY} has prepared this document exclusively for ${reportData.company_name}. Copying, or modification of this document is strictly prohibited without ${reportData.branding_company || DEFAULT_COMPANY}'s written consent, except for specific purposes when such permission is granted. This document is confidential and proprietary material of ${reportData.branding_company || DEFAULT_COMPANY} and must always be treated as such, not to be disclosed to third parties without prior consent.</p>
<p>The information within is provided 'as-is,' without any warranty. Vulnerability assessments offer a snapshot of security at a specific moment and are not exhaustive risk assessments. New vulnerabilities may emerge post-assessment. Thus, this report is a guide, not a definitive risk analysis.</p>
<p>${reportData.branding_company || DEFAULT_COMPANY} assumes no liability for any changes, omissions, or errors in this document. ${reportData.branding_company || DEFAULT_COMPANY} shall not be liable for any damages, financial or otherwise, arising out of the use or misuse of this report by any current employee of ${reportData.company_name} or any member of the general public</p>`).map((paragraph, index) => (
                <Text key={index} style={{
                  fontSize: 12,
                  lineHeight: 1.4,
                  marginBottom: 18,
                  color: '#374151',
                  textAlign: 'justify',
                  fontFamily: 'Helvetica',
                  fontWeight: 400,
                }}>
                  {paragraph.trim()}
                </Text>
              ))}
            </>
          )}
        </View>
      </View>
      <Text style={{ display: 'none' }} render={({ pageNumber }) => { updateSectionPage('Disclaimer', pageNumber); return ''; }} fixed />
    </Page>
  );
};

export default DisclaimerPage; 