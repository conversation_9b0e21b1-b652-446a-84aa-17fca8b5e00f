import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from "recharts";
import { 
  SeverityAndTrendData,
  MonthlyTrend,
  QuarterlyTrend,
  YearlyTrend,
  WeeklyTrend
} from "../../../utils/hooks/dashboard/useSeverityAndTrend";

// Period types
type PeriodType = "weekly" | "monthly" | "quarterly" | "yearly";

interface EnhancedTrendLineChartProps {
  data: SeverityAndTrendData;
}

// Define the structure of processed chart data
interface ChartDataItem {
  name: string;
  created: number;
  resolved: number;
  date: Date;
}

// Colors for trend lines
const trendColors = {
  created: "#2563EB", // Blue-600
  resolved: "#16A34A", // Green-600
};

// Period options
const periodOptions = [
  { value: "weekly", label: "Weekly" },
  { value: "monthly", label: "Monthly" },
  { value: "quarterly", label: "Quarterly" },
  { value: "yearly", label: "Yearly" }
];

// Enhanced tooltip component with premium styling
const CustomTooltip = ({ active, payload, label }: any) => {
  if (!active || !payload || !payload.length) return null;
  
    return (
    <div className="custom-tooltip backdrop-blur-sm rounded-lg bg-white/95 p-4 text-gray-800 shadow-xl border border-gray-200">
      <p className="text-lg font-semibold border-b border-gray-200 pb-2 mb-3">{label}</p>
      
      {/* Show created vs resolved counts */}
      <div className="space-y-2">
        {payload.map((entry: any, index: number) => (
          <div key={`item-${index}`} className="flex justify-between items-center">
            <div className="flex items-center">
            <span
              className="mr-2 inline-block h-3 w-3 rounded-full"
              style={{ backgroundColor: entry.color }}
              />
              <span className="text-sm">
                {entry.name === "created" ? "Incoming Reports" : "Resolved Reports"}:
              </span>
            </div>
            <span className="ml-4 font-bold">{entry.value}</span>
          </div>
        ))}
      </div>
      </div>
    );
};

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: { 
    opacity: 1,
    transition: { duration: 0.5, when: "beforeChildren" }
  }
};

const childVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1,
    y: 0,
    transition: { duration: 0.4, delay: 0.2 }
  }
};

const EnhancedTrendLineChart: React.FC<EnhancedTrendLineChartProps> = ({ data }) => {
  const [activePeriod, setActivePeriod] = useState<PeriodType>("monthly");
  const [chartData, setChartData] = useState<ChartDataItem[]>([]);
  const [noData, setNoData] = useState<boolean>(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Format and process data based on period selection
  useEffect(() => {
    // Handle standard period types (weekly, monthly, quarterly, yearly)
    let periodData;
    if (activePeriod === "weekly") {
      periodData = data.reportsTrend.weekly;
    } else if (activePeriod === "monthly") {
      periodData = data.reportsTrend.monthly;
    } else if (activePeriod === "quarterly") {
      periodData = data.reportsTrend.quarterly;
    } else { // yearly
      periodData = data.reportsTrend.yearly;
    }
    
    if (!periodData || periodData.length === 0) {
      setNoData(true);
      setChartData([]);
      return;
    }
    
    setNoData(false);

    // Format the data for the chart based on period type
    if (activePeriod === "weekly") {
      const weeklyData = periodData as WeeklyTrend[];
      
      // Format weekly data
      const formattedData = weeklyData.map(item => {
        return {
          name: `${item.week}, ${item.month}`,
          created: item.created,
          resolved: item.resolved,
          date: new Date(item.year, ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"].indexOf(item.month), 
            // Approximate week to day - Week 1 is day 7, Week 2 is day 14, etc.
            parseInt(item.week.replace("Week ", ""), 10) * 7)
        };
      }).sort((a, b) => a.date.getTime() - b.date.getTime());
      
      setChartData(formattedData);
    } else if (activePeriod === "monthly") {
      const monthlyData = periodData as MonthlyTrend[];
      
      // Simplified data formatting - just use month name
      const formattedData = monthlyData.map(item => {
        return {
          name: item.month,
          created: item.created,
          resolved: item.resolved,
          date: new Date(item.year, ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"].indexOf(item.month), 1)
        };
      }).sort((a, b) => a.date.getTime() - b.date.getTime());
      
      setChartData(formattedData);
    } else if (activePeriod === "quarterly") {
      const quarterlyData = periodData as QuarterlyTrend[];
      const formattedData = quarterlyData.map(item => {
        return {
          name: item.quarter,
          created: item.created,
          resolved: item.resolved,
          date: new Date(item.year, (parseInt(item.quarter.replace("Q", ""), 10) - 1) * 3, 1)
        };
      }).sort((a, b) => a.date.getTime() - b.date.getTime());
      
      setChartData(formattedData);
    } else { // yearly
      const yearlyData = periodData as YearlyTrend[];
      const formattedData = yearlyData.map(item => ({
        name: item.year.toString(),
        created: item.created,
        resolved: item.resolved,
        date: new Date(item.year, 0, 1)
      })).sort((a, b) => a.date.getTime() - b.date.getTime());
    
    setChartData(formattedData);
    }
  }, [data, activePeriod]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handlePeriodChange = (period: PeriodType) => {
    setActivePeriod(period);
    setDropdownOpen(false);
  };

  return (
    <motion.div
      className="relative w-full"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="mb-6 flex flex-col justify-between gap-3 sm:flex-row sm:items-center">
        <motion.div variants={childVariants} className="flex-1">
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Report Trends</h3>
          <p className="text-sm text-gray-500">
            Security vulnerabilities over time
          </p>
        </motion.div>
        
        {/* Enhanced dropdown */}
        <motion.div 
          className="relative z-10" 
          variants={childVariants}
          ref={dropdownRef}
        >
            <button
            onClick={() => setDropdownOpen(!dropdownOpen)}
            className="flex items-center gap-2 rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-all"
          >
            {periodOptions.find(opt => opt.value === activePeriod)?.label}
            <svg
              className={`h-4 w-4 transition-transform ${dropdownOpen ? 'rotate-180' : ''}`}
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          
          <AnimatePresence>
            {dropdownOpen && (
              <motion.div
                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -10, scale: 0.95 }}
                transition={{ type: "spring", damping: 20, stiffness: 300 }}
                className="absolute right-0 mt-2 w-48 rounded-lg border border-gray-200 bg-white shadow-lg backdrop-blur-sm"
              >
                <div className="py-1">
                  {periodOptions.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => handlePeriodChange(option.value as PeriodType)}
                      className={`w-full px-4 py-2 text-left text-sm ${
                        activePeriod === option.value
                          ? "bg-blue-50 text-blue-700 font-medium"
                          : "text-gray-700 hover:bg-gray-50"
                      } transition-colors`}
                    >
                      {activePeriod === option.value && (
                        <span className="mr-2">✓</span>
                      )}
                      {option.label}
            </button>
          ))}
        </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>

      {/* Legend at the top */}
      <div className="flex flex-wrap justify-center gap-4 mb-4">
        <div className="flex items-center text-sm">
          <div 
            className="h-3 w-3 rounded-full mr-1.5"
            style={{ backgroundColor: trendColors.created }}
          />
          <span className="text-gray-700">Incoming Reports</span>
        </div>
        <div className="flex items-center text-sm">
            <div 
              className="h-3 w-3 rounded-full mr-1.5"
            style={{ backgroundColor: trendColors.resolved }}
            />
          <span className="text-gray-700">Resolved Reports</span>
          </div>
      </div>

      <motion.div 
        className="relative h-[320px]"
        variants={childVariants}
      >
        {noData || chartData.length === 0 ? (
          <div className="flex h-full w-full items-center justify-center">
            <motion.div 
              className="text-center text-gray-500"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
            >
              <svg 
                className="mx-auto h-12 w-12 text-gray-400 mb-2" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth="1.5" 
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <p className="text-lg font-medium">No data available</p>
              <p className="text-sm">There are no reports for the selected time period</p>
            </motion.div>
          </div>
        ) : (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={chartData}
              margin={{ top: 10, right: 15, left: 5, bottom: 20 }}
            >
              <CartesianGrid 
                strokeDasharray="3 3" 
                vertical={false}
                horizontal={true}
                stroke="#E5E7EB"
                opacity={0.5}
              />
              
              <XAxis 
                dataKey="name" 
                axisLine={false}
                tickLine={false}
                tick={{ 
                  fill: "#6B7280", 
                  fontSize: 12,
                  dy: 10
                }}
                tickMargin={10}
              />
              
              <YAxis 
                axisLine={false}
                tickLine={false}
                tick={{ 
                  fill: "#6B7280", 
                  fontSize: 12
                }}
                width={35}
                allowDecimals={false}
              />
              
              <Tooltip 
                content={<CustomTooltip />}
                cursor={{
                  stroke: "#9CA3AF",
                  strokeDasharray: "5 5",
                  strokeWidth: 1
                }}
                wrapperStyle={{ zIndex: 10, pointerEvents: 'none' }}
              />
              
              {/* Created reports line */}
              <Line 
                type="monotone" 
                dataKey="created" 
                stroke={trendColors.created} 
                strokeWidth={2.5}
                dot={{ fill: trendColors.created, r: 5, strokeWidth: 1 }}
                activeDot={{ r: 7, strokeWidth: 1 }}
                animationDuration={1000}
                name="created"
              />
              
              {/* Resolved reports line */}
              <Line 
                type="monotone" 
                dataKey="resolved" 
                stroke={trendColors.resolved} 
                strokeWidth={2.5}
                dot={{ fill: trendColors.resolved, r: 5, strokeWidth: 1 }}
                activeDot={{ r: 7, strokeWidth: 1 }}
                animationDuration={1000}
                animationBegin={100}
                name="resolved"
              />
            </LineChart>
          </ResponsiveContainer>
        )}
      </motion.div>
    </motion.div>
  );
};

export default EnhancedTrendLineChart; 