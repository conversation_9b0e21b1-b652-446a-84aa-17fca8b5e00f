export interface DetailedFinding {
  abbreviation: string;
  title: string;
  severity_category: "Critical" | "High" | "Medium" | "Low";
  status: "Open" | "Closed" | "In Progress";
  scope: string;
  description: string;
  instructions: string;
  impact: string;
  fix: string;
  submitted_date: string;
}

export interface ReportData {
  report_title: string;
  company_name: string;
  program_name?: string;
  document_number: string;
  version_number: string;
  current_date: string;
  assessment_date: string;
  scope: string;
  project_objectives: string;
  methodology: string;
  findings: string;
  target_details: TargetDetail[];
  program_details: ProgramDetail[];
  executive_summary: string;
  key_findings: string;
  recommendations: string;
  conclusion: string;
  appendix: string;
  date_of_request: string;
  revision_date: string;
  open_close_counts_by_severity: {
    Critical: SeverityCounts;
    High: SeverityCounts;
    Medium: SeverityCounts;
    Low: SeverityCounts;
  };
  total_open: number;
  total_closed: number;
  total_findings: number;
  reports_list: Report[];
  prepared_by: string;
  reviewed_by: string;
  approved_by: string;
  test_lead: string;
  version_description: string;
  disclaimer?: string;
  vulnerability_ratings?: {
    critical: string;
    high: string;
    medium: string;
    low: string;
  };
  detailed_findings: DetailedFinding[];
}

export interface ProgramDetail {
  testing_type: string;
  description: string;
}

export interface TargetDetail {
  type: string;
  url: string;
}

export interface Finding {
  id: string;
  title: string;
  severity: string;
  status: string;
  description: string;
  impact: string;
  remediation: string;
}

export interface Report {
  abbreviation: string;
  title: string;
  severity_category: string;
  status: string;
}

export interface SeverityCounts {
  Open: number;
  Closed: number;
  Total: number;
}

export interface ProgramReportCreateRequest {
  user_id: number;
  role: string;
  company_name: string;
  program_name?: string;
  report_title: string;
  version_number: string;
  document_number: string;
  current_date: string;
  assessment_date?: string;
  date_of_request?: string;
  revision_date?: string;
  test_lead?: string;
  prepared_by?: string;
  reviewed_by?: string;
  approved_by?: string;
  version_description?: string;
  executive_summary?: string;
  key_findings?: string;
  scope?: string;
  project_objectives?: string;
  methodology?: string;
  findings?: string;
  recommendations?: string;
  conclusion?: string;
  disclaimer?: string;
  appendix?: string;
  program_details?: any[];
  target_details?: any[];
  severity_counts?: any;
  status_counts?: any;
  open_close_counts_by_severity?: any;
  total_open?: number;
  total_closed?: number;
  total_findings?: number;
  reports_list?: any[];
  vulnerability_ratings?: any;
  detailed_findings?: any[];
  automated_report_id?: string;
}

export interface ProgramReportUpdateRequest
  extends Partial<ProgramReportCreateRequest> {
  id: string;
}

export interface ProgramReportResponse {
  id: string;
  user_id: number;
  role: string;
  status: string;
  company_name: string;
  program_name?: string;
  report_title: string;
  version_number: string;
  document_number: string;
  current_date: string;
  assessment_date?: string;
  date_of_request?: string;
  revision_date?: string;
  test_lead?: string;
  prepared_by?: string;
  reviewed_by?: string;
  approved_by?: string;
  version_description?: string;
  executive_summary?: string;
  key_findings?: string;
  scope?: string;
  project_objectives?: string;
  methodology?: string;
  findings?: string;
  recommendations?: string;
  conclusion?: string;
  disclaimer?: string;
  appendix?: string;
  program_details: any[];
  target_details: any[];
  severity_counts: any;
  status_counts: any;
  open_close_counts_by_severity: any;
  total_open: number;
  total_closed: number;
  total_findings: number;
  reports_list: any[];
  vulnerability_ratings: any;
  detailed_findings: any[];
  automated_report_id?: string;
  created_at: string;
  updated_at: string;
}
