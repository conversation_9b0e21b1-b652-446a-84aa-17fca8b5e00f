import React, { useEffect, useState } from "react";
import { useForm, FormProvider } from "react-hook-form";
import RichTextArea from "../../../components/forms/inputs/RichTextArea";
import useCommentForm from "../../../utils/hooks/retests/useCommentForm";
import { UserRole } from "../../../utils/api/endpoints/user/credentials";
import { DownArrowIcon } from "../../../assets/icons/DownArrowIcon";
import { UpArrowIcon } from "../../../assets/icons/UpArrowIcon";
import OutlineButton from "../../buttons/OutlineButton";
import ConfirmationModal from "../../../components/common/ConfirmationModal";

interface CommentFormProps {
  retest_id: string;
  roleString: string;
  role: UserRole | undefined;
  onCommentSubmit: () => void;
  currentStatus: string | null;
  className?: string;
}

const defaultComments: { [key: string]: string } = {
  "Retest Requested": "A retest has been requested for this vulnerability.",
  "Retest In Process": "The retest process for this vulnerability has begun.",
  "Fix Verified": "The fix for this vulnerability has been verified.",
  "Fix Failed": "The attempted fix for this vulnerability has failed.",
  "QA Review In Process":
    "The QA review for this vulnerability is now in process.",
  "Fix Approved": "The fix for this vulnerability has been approved.",
  "Fix Rejected": "The fix for this vulnerability has been rejected.",
  "Findings Rejected":
    "The findings for this vulnerability have been rejected.",
  "Need Information":
    "Additional information is needed regarding this vulnerability.",
  "Fix in Progress": "A fix for this vulnerability is currently in progress.",
  "Risk Accepted and Closed":
    "The risk for this vulnerability has been accepted and the issue is closed.",
  "Close Retest": "The retest for this vulnerability is being closed.",
  "Reopen Retest": "The retest for this vulnerability is being reopened.",
  "Request Further Action":
    "Further review has been requested for this vulnerability. Please revisit the finding and verify if the issue has been appropriately addressed.",
  chat: "Initiating a chat regarding this vulnerability."
};

export const CommentForm: React.FC<CommentFormProps> = ({
  retest_id,
  roleString,
  role,
  onCommentSubmit,
  currentStatus,
  className = "w-full"
}) => {
  const {
    comment,
    setComment,
    selectedStatus,
    setSelectedStatus,
    pocFile,
    setPocFile,
    handleCommentSubmit,
    error,
    isSubmitting
  } = useCommentForm(retest_id, roleString, onCommentSubmit);

  const methods = useForm({
    defaultValues: {
      comment: comment
    }
  });

  const [nextStatusOptions, setNextStatusOptions] = useState<string[]>([]);
  const [isCommentOpen, setIsCommentOpen] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [pendingComment, setPendingComment] = useState<string | null>(null);

  useEffect(() => {
    if (role !== undefined && currentStatus !== null) {
      const options = getNextStatusOptions();
      setNextStatusOptions(options);
    }
  }, [role, currentStatus]);

  useEffect(() => {
    if (selectedStatus && defaultComments[selectedStatus]) {
      setComment(defaultComments[selectedStatus]);
      methods.setValue("comment", defaultComments[selectedStatus]);
    }
  }, [selectedStatus]);

  const getNextStatusOptions = () => {
    if (role === undefined || currentStatus === null) return [];

    const statusMap: { [key: string]: string[] } = {
      "Retest Requested": ["chat", "Retest In Process"],
      "Retest In Process": ["chat", "Fix Verified", "Fix Failed"],
      "Fix Failed": [
        "chat",
        "QA Review In Process",
        "Request Further Action",
        "Need Information",
        "Risk Accepted and Closed",
        "Retest In Process"
      ],
      "Fix Verified": ["chat", "QA Review In Process", "Close Retest"],
      "QA Review In Process": [
        "chat",
        "Fix Approved",
        "Fix Rejected",
        "Findings Rejected",
        "Need Information"
      ],
      "Fix Approved": ["chat", "Close Retest", "Request Further Action"],
      "Request Further Action": [
        "chat",
        "Retest In Process",
        "QA Review In Process"
      ],
      "Fix Rejected": [
        "chat",
        "Fix in Progress",
        "Retest In Process",
        "Risk Accepted and Closed"
      ],
      "Need Information": [
        "chat",
        "Retest In Process",
        "Request Further Action"
      ],
      "Findings Rejected": ["chat", "Retest In Process", "Need Information"],
      "Fix in Progress": ["chat", "Retest In Process", "Need Information"],
      "Risk Accepted and Closed": ["chat", "Close Retest", "Reopen Retest"],
      "Close Retest": ["chat", "Reopen Retest"],
      "Reopen Retest": ["chat", "Retest In Process"]
    };

    const roleMap: { [key in UserRole]: string[] } = {
      [UserRole.ADMIN]: [
        "QA Review In Process",
        "Fix Approved",
        "Fix Rejected",
        "Findings Rejected",
        "Need Information",
        "chat"
      ],
      [UserRole.QA]: [
        "QA Review In Process",
        "Fix Approved",
        "Fix Rejected",
        "Findings Rejected",
        "Need Information",
        "chat"
      ],
      [UserRole.ADMIN_MANAGER]: [
        "QA Review In Process",
        "Fix Approved",
        "Fix Rejected",
        "Findings Rejected",
        "Need Information",
        "chat"
      ],
      [UserRole.SUB_ADMIN]: [
        "QA Review In Process",
        "Fix Approved",
        "Fix Rejected",
        "Findings Rejected",
        "Need Information",
        "chat"
      ],
      [UserRole.RESEARCHER]: [
        "Retest In Process",
        "Fix Verified",
        "Fix Failed",
        "Need Information",
        "chat"
      ],
      [UserRole.BUSINESS]: [
        "Fix in Progress",
        "Risk Accepted and Closed",
        "Close Retest",
        "Reopen Retest",
        "Request Further Action",
        "Need Information",
        "chat"
      ],
      [UserRole.BUSINESS_MANAGER]: [
        "Fix in Progress",
        "Risk Accepted and Closed",
        "Close Retest",
        "Reopen Retest",
        "Request Further Action",
        "Need Information",
        "chat"
      ],
      [UserRole.BUSINESS_ADMINISTRATOR]: [
        "Fix in Progress",
        "Risk Accepted and Closed",
        "Close Retest",
        "Reopen Retest",
        "Request Further Action",
        "Need Information",
        "chat"
      ],
      [UserRole.DEVELOPER]: [
        "Fix in Progress",
        "Risk Accepted and Closed",
        "Close Retest",
        "Reopen Retest",
        "Request Further Action",
        "Need Information",
        "chat"
      ]
    };

    const nextStatuses = statusMap[currentStatus] || [];
    return nextStatuses.filter(status => roleMap[role].includes(status));
  };

  const getActionTaken = () => {
    if (role === undefined) return "";
    switch (role) {
      case UserRole.RESEARCHER:
        return "Researcher";
      case UserRole.BUSINESS:
        return "Business";
      case UserRole.DEVELOPER:
        return "Developer";
      case UserRole.BUSINESS_MANAGER:
        return "Business manager";
      case UserRole.BUSINESS_ADMINISTRATOR:
        return "Business Admin";
      case UserRole.ADMIN:
        return "QA";
      case UserRole.SUB_ADMIN:
        return "Sub Admin";
      case UserRole.ADMIN_MANAGER:
        return "Admin Manager";
      case UserRole.QA:
        return "QA";
      default:
        return "";
    }
  };

  const actionTaken = getActionTaken();

  const onSubmit = (data: { comment: string }) => {
    if (role === UserRole.RESEARCHER) {
      setPendingComment(data.comment);
      setShowConfirmation(true);
      return;
    }
    handleCommentSubmit(actionTaken, data.comment).then(() => {
      methods.reset({ comment: "" });
      setSelectedStatus("");
      setPocFile(null);
      setComment("");
      setIsCommentOpen(false);
    });
  };

  const handleConfirmSubmit = () => {
    if (pendingComment !== null) {
      handleCommentSubmit(actionTaken, pendingComment).then(() => {
        methods.reset({ comment: "" });
        setSelectedStatus("");
        setPocFile(null);
        setComment("");
        setIsCommentOpen(false);
        setPendingComment(null);
        setShowConfirmation(false);
      });
    }
  };

  const handleCancelSubmit = () => {
    setShowConfirmation(false);
    setPendingComment(null);
  };

  return (
    <div className={`overflow-hidden${className}`}>
      <div className="">
        <div className="flex items-center justify-between">
          <OutlineButton
            onClick={() => setIsCommentOpen(!isCommentOpen)}
            className="font-bold"
          >
            {isCommentOpen ? (
              <>
                Comment
                <DownArrowIcon className="ml-2" />
              </>
            ) : (
              <>
                Comment
                <UpArrowIcon className="ml-2" />
              </>
            )}
          </OutlineButton>

          <select
            id="status"
            value={selectedStatus}
            onChange={e => setSelectedStatus(e.target.value)}
            className="block w-48 cursor-pointer rounded-md border border-blue-600 bg-white px-3 py-2 text-[12px] font-bold text-blue-600 shadow-sm"
            required
          >
            <option value="" disabled>
              Select Retest Status
            </option>
            {nextStatusOptions.map(status => (
              <option key={status} value={status}>
                {status}
              </option>
            ))}
          </select>
        </div>
      </div>
      {isCommentOpen && (
        <FormProvider {...methods}>
          <form
            onSubmit={methods.handleSubmit(onSubmit)}
            className="space-y-6 bg-white p-6"
          >
            <div>
              <div className="relative rounded-md shadow-sm">
                <RichTextArea
                  name="comment"
                  rules={{ required: true }}
                  className="block w-full rounded-md border-gray-300 pl-10 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>
            </div>
            <div>
              <label
                htmlFor="poc"
                className="mb-2 block text-sm font-medium text-gray-700"
              >
                {role === UserRole.BUSINESS
                  ? "Add Attachment (Optional)"
                  : "PoC (Optional)"}
              </label>
              <div className="mt-1 flex items-center">
                <label
                  htmlFor="poc"
                  className="flex cursor-pointer items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                >
                  <span>Upload file</span>
                  <input
                    id="poc"
                    name="poc"
                    type="file"
                    className="sr-only"
                    accept=".pdf,.doc,.docx,.png,.jpg,.jpeg"
                    onChange={e =>
                      setPocFile(e.target.files ? e.target.files[0] : null)
                    }
                  />
                </label>
                {pocFile && (
                  <span className="ml-3 text-sm text-gray-500">
                    {pocFile.name}
                  </span>
                )}
              </div>
              <p className="mt-4 text-xs text-gray-500">
                PDF, DOC, PNG, JPG, JPEG up to 10MB
              </p>
            </div>
            <div>
              <button
                type="submit"
                disabled={isSubmitting || nextStatusOptions.length === 0}
                className={`flex w-full justify-center rounded-md border border-transparent px-4 py-2 text-sm font-medium text-white shadow-sm transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
                  isSubmitting || nextStatusOptions.length === 0
                    ? "cursor-not-allowed bg-blue-300"
                    : "bg-blue-600 hover:bg-blue-700"
                }`}
              >
                {isSubmitting ? "Submitting..." : "Submit Comment"}
              </button>
            </div>
          </form>
        </FormProvider>
      )}
      {error && (
        <div className="mx-6 mb-4 rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">{error}</div>
            </div>
          </div>
        </div>
      )}
      {showConfirmation && (
        <ConfirmationModal
          isOpen={showConfirmation}
          onClose={handleCancelSubmit}
          onConfirm={handleConfirmSubmit}
          title="Confirm Submission"
          description={`Are you sure your message and PoC are appropriate for status: ${selectedStatus || "the selected status"}?`}
          confirmText="Yes, Submit"
          cancelText="No, Edit"
        />
      )}
    </div>
  );
};

