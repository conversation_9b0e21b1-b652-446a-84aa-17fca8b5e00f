import { CTBReport } from "../../utils/api/endpoints/reports/parseReports";
import QuickViewModal, { QuickViewSection } from "../modal/QuickViewModal";
import VulnerabilityFlag, { getVulnerabilityColour } from "./VulnerabilityFlag";
import Pill from "../pills/Pill";
import ReportStatusPill from "./ReportStatusPill";
import parse from "html-react-parser";
import PrimaryButton from "../buttons/PrimaryButton";
import { useNavigate } from "react-router-dom";
import ReportQuickProgramBanner from "./ReportQuickProgramBanner";
import useProgram from "../../utils/hooks/programs/useProgram";
import { parseCategoryString } from "../../utils/categoryUtils";
import { FiCopy } from "react-icons/fi";
import { useState } from "react";

/**
 * A sidebar style preview of the given report (if defined)
 *
 * TODO: Integrate program information
 */
const ReportQuickView = ({
  report,
  onClose
}: {
  report?: CTBReport;
  onClose: () => void;
}) => {
  const navigate = useNavigate();
  const { program } = useProgram(report?.programId);
  const [copySuccess, setCopySuccess] = useState(false);

  const baseUrl = window.location.origin;
  const reportLink = report ? `${baseUrl}/dashboard/reports/${report.id}` : "";

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(reportLink);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000); // Reset after 2 seconds
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  return (
    <QuickViewModal
      title={report?.title}
      enabled={report !== undefined}
      onClick={onClose}
      className="flex flex-col"
    >
      <div className="mb-2 flex items-center gap-2">
        <div className="inline-flex items-center gap-2 rounded-lg border border-blue-200 bg-gradient-to-r from-blue-50 to-blue-100 px-2 py-1 shadow-sm">
          <span className="font-bold tracking-wide text-blue-800">
            #{report?.id}
          </span>
        </div>

        {report !== undefined && (
          <ReportStatusPill
            report={report}
            className="px-2 py-2 text-xs leading-[150%]"
          />
        )}

        {report?.severity && (
          <VulnerabilityFlag
            label={
              report.severity.score +
              " " +
              report.severity.category +
              " Severity"
            }
            className={`text-${getVulnerabilityColour(
              report.severity
            )} bg-transparent font-medium`}
            flagClassName={`fill-${getVulnerabilityColour(
              report.severity
            )} bg-transparent`}
          />
        )}
      </div>

      {report?.category && (
        <div className="mb-4">
          <div className="inline-flex items-center rounded-lg border border-blue-100 bg-gradient-to-r from-ctb-blue-50 to-blue-100 px-4 py-2.5 text-sm font-medium text-ctb-blue-700 shadow-sm transition-shadow duration-200 hover:shadow-md">
            <span className="mr-2 text-ctb-blue-500">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M2 5a2 2 0 012-2h12a2 2 0 012 2v10a2 2 0 01-2 2H4a2 2 0 01-2-2V5zm3.293 1.293a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 01-1.414-1.414L7.586 10 5.293 7.707a1 1 0 010-1.414zM11 12a1 1 0 100 2h3a1 1 0 100-2h-3z"
                  clipRule="evenodd"
                />
              </svg>
            </span>
            {parseCategoryString(report.category)}
          </div>
        </div>
      )}

      {/* Report Link Section */}
      {report && (
        <div className="mb-4 mt-2 flex items-center gap-2 rounded-lg border border-gray-200 bg-gray-50 p-2">
          <input
            type="text"
            value={reportLink}
            readOnly
            className="flex-1 bg-transparent text-sm font-medium text-gray-800 outline-none"
          />
          <button
            onClick={handleCopyLink}
            className="flex items-center gap-1 rounded-md px-2 py-1 text-sm text-gray-600 hover:bg-gray-200"
            title="Copy link"
          >
            <FiCopy className="h-4 w-4" />
            {copySuccess ? (
              <span className="text-xs text-green-600">Copied!</span>
            ) : (
              <span className="text-xs">Copy</span>
            )}
          </button>
        </div>
      )}

      <section className="h-full overflow-y-auto pt-1 font-thin">
        {report && program && (
          <ReportQuickProgramBanner
            report={report}
            program={program}
            className="mb-3 font-medium"
          />
        )}

        {report?.description && (
          <QuickViewSection className="font-medium">
            <span>{parse(report.description)}</span>
          </QuickViewSection>
        )}

        {report?.instructions && (
          <QuickViewSection title="Validation Steps">
            <span className="font-medium leading-[150%]">
              {parse(report.instructions)}
            </span>
          </QuickViewSection>
        )}

        {report?.impact && (
          <QuickViewSection title="Impact">
            <span className="font-medium leading-[150%]">
              {parse(report.impact)}
            </span>
          </QuickViewSection>
        )}

        {report?.fix && (
          <QuickViewSection title="Recommended Fix">
            <span className="font-medium leading-[150%]">
              {parse(report.fix)}
            </span>
          </QuickViewSection>
        )}
      </section>

      <PrimaryButton
        type="button"
        className="mt-10 w-full"
        onClick={() => report && navigate(report?.id.toString())}
      >
        View Full Details
      </PrimaryButton>
    </QuickViewModal>
  );
};

export default ReportQuickView;
