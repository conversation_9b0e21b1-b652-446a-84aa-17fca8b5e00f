import toast from "react-hot-toast";
import {
  LoginCredentials,
  postLogin
} from "../../api/endpoints/user/authentication";
import {
  setCredentials,
  verfiyOTP
} from "../../store/reducer/slices/userReducer";
import { useStoreDispatch, useStoreSelector } from "../hooks";
import { FieldValues, UseFormSetError } from "react-hook-form";

const useUserCredentials = () => {
  const dispatch = useStoreDispatch();

  // Provide specific access to each user credential
  const loadingStatus = useStoreSelector(
    state => state.user.credentials.status
  );
  const loginStatus = useStoreSelector(state => state.user.loginStatus);
  const username = useStoreSelector(state => state.user.credentials.username);
  const role = useStoreSelector(state => state.user.credentials.userRole);
  const id = useStoreSelector(state => state.user.credentials.userId);

  const handleLogin = async <T extends FieldValues>(
    credentials: LoginCredentials,
    setError: UseFormSetError<T>
  ) => {
    postLogin({ ...credentials })
      .then(credentials => {
        dispatch(setCredentials(credentials));
      })
      .catch(err => {
        if (err?.response?.data?.message) {
          toast.error(err?.response?.data?.message)
        }
        setError("root", {
          message: "Invalid login attempt"
        });
      });
  };

  const handleOTP = async (code: string) => dispatch(verfiyOTP(code));

  return {
    id,
    username,
    role,
    loadingStatus,
    loginStatus,
    handleLogin,
    handleOTP
  };
};

export default useUserCredentials;
