import React from "react";
import StatusFormatter from "../utils/StatusFormatter";

interface RetestHeaderProps {
  reportName?: string;
  programName?: string;
  severityScore?: string;
  severityCategory?: string;
  retestStatus?: string | null;
  retestDate?: string | null;
  // onClick?: () => void;
}

export const RetestHeader: React.FC<RetestHeaderProps> = ({
  reportName,
  programName,
  severityScore,
  severityCategory,
  retestStatus,
  retestDate
  // onClick = () => {}
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "QA Review In Process":
      case "Retest In Process":
      case "Fix in Progress":
      case "Reopen Retest":
      case "Request Further Action":
      case "Need Information":
        return "bg-[#0132f5b0] border-[3px] border-blue-600 text-white";
      case "Fix Approved":
      case "Fix Verified":
      case "Risk Accepted and Closed":
        return "bg-[#23b92f] border-[3px] border-green-600 text-white";
      case "Fix Rejected":
      case "Fix Failed":
      case "Findings Rejected":
      case "Close Retest":
        return "bg-[#F50101B2] border-[3px] border-red-600 text-white";
      default:
        return "bg-blue-600 text-white";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "QA Review In Process":
        return "QA is reviewing the retest";
      case "Retest In Process":
        return "Retesting is currently in progress";
      case "Fix in Progress":
        return "The fix is being implemented";
      case "Reopen Retest":
        return "Retest has been reopened for review";
      case "Request Further Action":
        return "Further actions are requested";
      case "Need Information":
        return "Additional information is required";
      case "Fix Approved":
        return "The fix has been approved";
      case "Fix Verified":
        return "The fix has been verified";
      case "Risk Accepted and Closed":
        return "Risk accepted, retest closed";
      case "Fix Rejected":
        return "The fix has been rejected";
      case "Fix Failed":
        return "The fix attempt has failed";
      case "Findings Rejected":
        return "Report findings were rejected";
      case "Close Retest":
        return "Retest process has been closed";
      case "Retest Requested":
        return "Retest process has been requested";
      default:
        return "Status is currently unknown";
    }
  };

  const getSeverityColor = (severityCategory: string) => {
    switch (severityCategory.toUpperCase()) {
      case "LOW":
        return "bg-green-100 border-2 border-green-500";
      case "MEDIUM":
        return "bg-yellow-100 border-2 border-yellow-500";
      case "HIGH":
        return "bg-red-100 border-2 border-red-500";
      case "CRITICAL":
        return "bg-red-100 border-2 border-red-700";
      default:
        return "bg-gray-100 border-2 border-gray-500";
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const day = date.getDate();
    const month = date.toLocaleString("default", { month: "short" });
    const year = date.getFullYear();
    return `${day} ${month}, ${year}`;
  };

  const getDaysAgo = (dateString: string) => {
    const today = new Date();
    const retestDate = new Date(dateString);
    const diffTime = Math.abs(today.getTime() - retestDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return `${diffDays} days ago`;
  };

  const truncateString = (str: string, maxLength: number) => {
    if (str.length > maxLength) {
      return str.substring(0, maxLength) + "...";
    }
    return str;
  };

  return (
    <div>
      <div className="mb-6 flex flex-col">
        <h2 className="truncate pl-4 text-2xl" style={{ fontWeight: "600" }}>
          {programName}
        </h2>
      </div>
      <div
        className="cursor-pointer overflow-hidden rounded-lg bg-white"
        // onClick={onClick}
      >
        <div className="px-6 py-4">
          <div className="-mx-3 flex flex-wrap">
            <div className="mb-4 w-full border-r-4 px-3 md:mb-0 md:w-3/12">
              <p className="mb-1 text-sm text-black">Report Title</p>
              <p
                className="text-xl font-semibold capitalize"
                style={{ fontWeight: "600" }}
              >
                {reportName ? truncateString(reportName, 48) : "N/A"}
              </p>
            </div>
            <div className="mb-4 flex w-full flex-col gap-2 border-r-4 px-6 md:mb-0 md:w-3/12">
              <p
                className="mb-1 text-sm text-black"
                style={{ fontWeight: "500" }}
              >
                Retest request date
              </p>
              <p
                className="text-xl font-semibold capitalize"
                style={{ fontWeight: "600" }}
              >
                {retestDate ? formatDate(retestDate) : "N/A"}
              </p>
              <div className="text-sm" style={{ fontWeight: "500" }}>
                {retestDate ? getDaysAgo(retestDate) : "N/A"}
              </div>
            </div>
            <div className="mb-4 w-full border-r-4 px-6 md:mb-0 md:w-3/12">
              <p
                className="mb-1 text-sm text-black"
                style={{ fontWeight: "500" }}
              >
                Severity
              </p>
              <div
                className={`w-[50%] py-1 text-center ${getSeverityColor(
                  severityCategory || ""
                )}`}
              >
                <span
                  className="text-sm font-medium"
                  style={{ fontWeight: "600" }}
                >
                  {severityCategory} ({severityScore})
                </span>
              </div>
              <div className="py-3 text-sm" style={{ fontWeight: "500" }}>
                CTB&apos;s vulnerability score
              </div>
            </div>
            <div className="w-full px-6 md:w-3/12">
              <p
                className="mb-1 text-sm text-black"
                style={{ fontWeight: "500" }}
              >
                Status
              </p>
              <div
                className={`w-[56%] py-1 text-center ${getStatusColor(
                  retestStatus || ""
                )}`}
              >
                <span className="text-sm" style={{ fontWeight: "500" }}>
                  <StatusFormatter status={retestStatus || ""} />
                </span>
              </div>
              <div className="py-3 text-sm" style={{ fontWeight: "500" }}>
                {getStatusText(retestStatus || "")}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
