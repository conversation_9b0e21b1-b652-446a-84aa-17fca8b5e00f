import React, { useState } from "react";
import { motion } from "framer-motion";
import { FaChartLine, FaExclamationCircle, FaCheck, FaSync, FaFolder, FaInfoCircle } from "react-icons/fa";
import { DashboardSummary } from "../../../utils/hooks/dashboard/useDashboardSummary";

interface EnhancedDashboardStatsProps {
  data: DashboardSummary;
}

// Animation variants for card container
const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: i * 0.1,
      duration: 0.5,
      ease: [0.25, 0.1, 0.25, 1]
    }
  })
};

// Animation variants for percentage badge
const badgeVariants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: { 
      delay: 0.3, 
      duration: 0.3,
      type: "spring",
      stiffness: 300
    }
  }
};

// Animation variants for icons
const iconVariants = {
  hidden: { opacity: 0, rotate: -10, scale: 0.9 },
  visible: {
    opacity: 1,
    rotate: 0,
    scale: 1,
    transition: { 
      delay: 0.2, 
      duration: 0.4,
      type: "spring",
      stiffness: 200
    }
  }
};

// Tooltip component for the info icon
const InfoTooltip = ({ content }: { content: string }) => {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <div className="relative">
      <div 
        className="cursor-help"
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
      >
        <FaInfoCircle size={16} className="text-blue-500" />
      </div>
      {isVisible && (
        <div className="absolute z-10 right-0 top-6 w-64 bg-white p-3 rounded-lg shadow-lg border border-gray-200 text-xs text-slate-700">
          {content}
          <div className="absolute top-[-6px] right-[7px] w-3 h-3 bg-white transform rotate-45 border-l border-t border-gray-200"></div>
        </div>
      )}
    </div>
  );
};

const EnhancedDashboardStats: React.FC<EnhancedDashboardStatsProps> = ({ data }) => {
  const metrics = [
    {
      title: "Total Reports",
      value: data.totalReports.current,
      change: data.totalReports.changePercent,
      icon: <FaChartLine size={24} />,
      iconBgClass: "bg-blue-100 text-blue-600",
      changeDescription: (change: number) => 
        change >= 0 
          ? `+${Math.abs(change)}% vs. last 30 days`
          : `-${Math.abs(change)}% vs. last 30 days`,
      tooltip: "The total number of reports received across all programs. This count excludes deleted reports but includes all report states."
    },
    {
      title: "Active Reports",
      value: data.activeIssues ? data.activeIssues.current : data.totalPrograms.current,
      change: data.activeIssues ? data.activeIssues.changePercent : data.totalPrograms.changePercent,
      icon: <FaExclamationCircle size={24} />,
      iconBgClass: "bg-blue-100 text-blue-600",
      changeDescription: (change: number) => 
        change >= 0 
          ? `+${Math.abs(change)}% reports (↑ is bad)` 
          : `-${Math.abs(change)}% reports (↓ is good)`,
      tooltip: "Reports that require attention. This count excludes resolved reports, 'Business Accept Risk' reports, and 'Business Non-Actionable Issue' reports."
    },
    {
      title: "Resolved Issues",
      value: data.resolvedIssues.current,
      change: data.resolvedIssues.changePercent,
      icon: <FaCheck size={24} />,
      iconBgClass: "bg-blue-100 text-blue-600",
      changeDescription: (change: number) => 
        change >= 0 
          ? `+${Math.abs(change)}% resolved (↑ is good)` 
          : `-${Math.abs(change)}% resolved (↓ is bad)`,
      tooltip: "Reports that have been successfully fixed and closed. Only includes reports in the 'Closed' state."
    },
    {
      title: "Retests Remaining",
      value: data.retestsRemaining,
      change: null, // No change percentage for retests remaining
      icon: <FaSync size={24} />,
      iconBgClass: "bg-blue-100 text-blue-600",
      changeDescription: () => "Current count of retests pending completion",
      tooltip: "The number of retests that are pending completion. These are reports where a fix has been implemented and is awaiting verification."
    }
  ];

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
      {metrics.map((metric, index) => {
        // Determine border color based on change value
        let borderStyle = "border border-neutral-200";
        if (typeof metric.change === "number") {
          // For Active Reports, an increase is bad (show red)
          if (metric.title === "Active Reports") {
            if (metric.change > 0) borderStyle = "border-2 border-red-500";
          } 
          // For Resolved Issues, a decrease is bad (show red)
          else if (metric.title === "Resolved Issues") {
            if (metric.change < 0) borderStyle = "border-2 border-red-500";
          }
          // Total Reports remain neutral regardless of change
        }
        
        // Check if we should show trend indicator
        const showTrend = metric.change !== null;

        return (
          <motion.div
            key={metric.title}
            className={`overflow-hidden rounded-xl bg-white shadow-md backdrop-blur-sm h-full ${borderStyle}`}
            custom={index}
            initial="hidden"
            animate="visible"
            variants={cardVariants}
            whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
          >
            <div className="relative px-6 py-5 h-full min-h-[110px]">
              {/* Info button in the top right */}
              <div className="absolute top-2 right-3 z-10">
                <InfoTooltip content={metric.tooltip} />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-500">{metric.title}</p>
                  <h3 className="mt-1 text-3xl font-bold tracking-tight text-slate-900">
                    {metric.value.toLocaleString()}
                  </h3>
                  {/* Change percentage badge - only shown when data is sufficient */}
                  {showTrend && (
                    <>
                      <motion.div
                        variants={badgeVariants}
                        className={`mt-2 inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                          // Special case for Active Issues - decreasing is good
                          metric.title === "Active Reports"
                            ? (metric.change! < 0
                                ? "bg-blue-50 text-blue-700"
                                : "bg-red-50 text-red-600")
                            : (metric.change! >= 0
                                ? "bg-blue-50 text-blue-700"
                                : "bg-red-50 text-red-600")
                        }`}
                      >
                        <span className="mr-1">
                          {/* For Active Issues, down arrow is good */}
                          {metric.title === "Active Reports"
                            ? (metric.change! < 0 ? "↓" : "↑")
                            : (metric.change! >= 0 ? "↑" : "↓")}
                        </span>
                        {Math.abs(metric.change!)}%
                      </motion.div>
                      <p className="text-xs text-slate-500 mt-1 max-w-[240px]">
                        {metric.changeDescription(metric.change!)}
                      </p>
                    </>
                  )}
                  {/* Empty div for consistent spacing when trend data is not shown */}
                  {!showTrend && (
                    <div className="mt-2">
                      <p className="text-xs text-slate-500 mt-1 max-w-[240px]">
                        {metric.changeDescription(0)}
                      </p>
                    </div>
                  )}
                </div>
                {/* Icon */}
                <motion.div
                  variants={iconVariants}
                  className={`rounded-full ${metric.iconBgClass} p-3`}
                >
                  {metric.icon}
                </motion.div>
              </div>
              {/* Glass reflection effect */}
              <div className="absolute inset-x-0 top-0 h-1/3 bg-gradient-to-b from-white/20 to-transparent pointer-events-none"></div>
            </div>
          </motion.div>
        );
      })}
    </div>
  );
};

export default EnhancedDashboardStats; 