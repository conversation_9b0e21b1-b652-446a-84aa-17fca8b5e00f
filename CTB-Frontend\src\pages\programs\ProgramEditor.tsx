import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import EditorLayout from "../../layouts/EditorLayout";
import EditorInput from "../../components/editor/inputs/EditorInput";
import usePageTitle from "../../utils/hooks/usePageTitle";

import {
  ProgramType,
  NotificationMethods
} from "../../utils/api/endpoints/programs/parsePrograms";
import EditorSection from "../../components/editor/EditorSection";
import TargetsInput from "../../components/editor/inputs/programs/TargetsInput";
import DropDownEditor from "../../components/editor/inputs/programs/DropDownEditor";
import DatePicker from "../../components/editor/inputs/programs/DatePicker";
import EditorRichTextInput from "../../components/editor/inputs/EditorRichTextInput";
import ProgramRewardsInput from "../../components/editor/inputs/programs/ProgramRewardsInput";

import useProgram, {
  ProgramUpdateValues
} from "../../utils/hooks/programs/useProgram";
import EditorTextInput from "../../components/editor/inputs/EditorTextInput";
import { useEffect, useMemo, useState } from "react";
import {
  testingTypeOptions,
  environmentTypeOptions,
  complianceTypeOptions,
  complianceTypes
} from "../../utils/constants";


import FormDropdown from "../../components/forms/inputs/FormDropdown";
import { FiHelpCircle } from "react-icons/fi";
import JiraSetupGuideModal from "../../components/modals/JiraSetupGuideModal";

import FormProgressTimeline from "./FormProgressTimeline";
import ProgramEditorLogo from "../../components/editor/inputs/ProgramEditorLogo";
import ProgramEditorAttachments from "../../components/editor/inputs/ProgramEditorAttachments";


// Define CSS keyframe animations using a style element
const AnimationStyles = () => (
  <style>
    {`
      @keyframes pulse {
        0%, 100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.02);
          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
      }
      
      @keyframes checkmark-appear {
        0% {
          transform: scale(0) rotate(-45deg);
          opacity: 0;
        }
        100% {
          transform: scale(1) rotate(-45deg);
          opacity: 1;
        }
      }
      
      @keyframes slide-in {
        0% {
          transform: translateX(16px);
        }
        100% {
          transform: translateX(0);
        }
      }
    `}
  </style>
);

const ProgramEditor = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [selectedPogramType, setSelectedPogramType] = useState<ProgramType>(
    ProgramType.PTAAS
  );
  const [privateVisibility, setPrivateVisibility] = useState<0 | 1 | undefined>(
    undefined
  ); // 0 = Public, 1 = Private
  // Always include CTB as a notification method
  const [notificationMethods, setNotificationMethods] = useState<String[]>([
    NotificationMethods.CTB
  ]);
  
  // Track which integrations are selected (Slack, Jira, or both)
  const [slackSelected, setSlackSelected] = useState<boolean>(false);
  const [jiraSelected, setJiraSelected] = useState<boolean>(false);
  
  // JIRA testing state
  const [jiraTestResult, setJiraTestResult] = useState<any>(null);

  // Jira setup guide modal state
  const [showJiraGuide, setShowJiraGuide] = useState<boolean>(false);
  const [testing, setTesting] = useState<boolean>(false);
  
  // Keep track of integration selections and notification methods
  const [selectedComplianceType, setSelectedComplianceType] = useState("");
  const { program, saveProgram } = useProgram(
    id !== undefined ? parseInt(id) : undefined
  );

  usePageTitle(id ? "Editing Program" : "Create Program");

  // Test JIRA connection function
  const testJiraConnection = async (formData: any) => {
    setTesting(true);
    setJiraTestResult(null);
    
    try {
      const config = {
        url: formData.jira_url,
        email: formData.jira_email,
        api_token: formData.jira_api_token,
        project_key: formData.jira_project_key
      };

      // Validate all fields are provided
      if (!config.url || !config.email || !config.api_token || !config.project_key) {
        setJiraTestResult({
          success: false,
          error: "Please fill in all JIRA configuration fields before testing"
        });
        return;
      }

      console.log("Testing JIRA connection with config:", {
        ...config,
        api_token: config.api_token ? "[HIDDEN]" : "MISSING"
      });

      // Import axios instance
      const axios = (await import("../../utils/api/axios")).default;
      
      console.log("Making request to:", '/v2/programs/jira/validate');
      console.log("Backend base URL:", process.env.REACT_APP_BACKEND_BASE_URL);
      
      const response = await axios.post('/v2/programs/jira/validate', config);
      console.log("JIRA test response:", response.data);
      setJiraTestResult(response.data);
    } catch (error: any) {
      console.error("JIRA test error details:", {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
        config: error.config
      });
      
      let errorMessage = "Failed to connect to server";
      let suggestions = ["Please check your internet connection and try again"];
      
      if (error.response?.status === 401) {
        errorMessage = "Authentication failed - Please log in again";
        suggestions = ["Try refreshing the page and logging in again"];
      } else if (error.response?.status === 403) {
        errorMessage = "Access denied - Insufficient permissions";
        suggestions = ["Contact your administrator for access to JIRA testing"];
      } else if (error.response?.status === 404) {
        errorMessage = "API endpoint not found";
        suggestions = ["Please check that the backend server is running", "Verify the API endpoint is properly configured"];
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
        suggestions = error.response.data.suggestions || suggestions;
      }
      
      setJiraTestResult({ 
        success: false, 
        error: errorMessage,
        suggestions: suggestions
      });
    } finally {
      setTesting(false);
    }
  };

  // Only showing the relevant part where we define defaultValues

  const defaultValues = useMemo(
    () =>
      ({
        title: program?.title || "",
        type: program?.type || ProgramType.PTAAS,
        private:
          program?.private === undefined ? undefined : program.private ? 1 : 0,
        description: program?.description || "",
        targets: program?.targets || [],
        vpn: program?.vpn,
        credentials: program?.credentials,
        scope: program?.scope || "",
        outOfScope: program?.outOfScope || "",
        knownVulnerabilities: program?.knownVulnerabilities,
        rewards: program?.payoutRange || {},
        rewardPolicy: program?.rewardPolicy || "",
        termsOfService: program?.termsOfService || "",
        other: program?.other,
        profilePicture: undefined,
        testingType: program?.testing_type || "",
        environmentType: program?.environment_type || "",
        complianceType: program?.compliance_type || "",
        otherComplianceType: program?.other_compliance_type || "",
        expectedStartDate: program?.expected_start_date
          ? new Date(program?.expected_start_date)
          : undefined,
        expectedEndDate: program?.expected_end_date
          ? new Date(program?.expected_end_date)
          : undefined,
        notification_methods: program?.notification_methods,
        attachments: [],
        existingAttachments: program?.attachments || [],
        // Integration fields
        slack_channel_link: program?.slack_channel_link || "",
        jira_url: program?.jira_url || "",
        jira_email: program?.jira_email || "",
        jira_api_token: program?.jira_api_token || "",
        jira_project_key: program?.jira_project_key || ""
      }) as Omit<ProgramUpdateValues, "private"> & {
        private?: 0 | 1;
        existingAttachments?: string[];
      },
    [program]
  );

  useEffect(() => {
    if (program?.type) setSelectedPogramType(program.type);
    if (program?.compliance_type)
      setSelectedComplianceType(program?.compliance_type);
  }, [program]);

  useEffect(() => {
    if (selectedPogramType === ProgramType.VDP) {
      setPrivateVisibility(0);
    } else if (selectedPogramType === ProgramType.PTAAS) {
      setPrivateVisibility(1);
    } else {
      setPrivateVisibility(undefined);
    }
  }, [selectedPogramType]);

  // Set integration states based on existing program data
  useEffect(() => {
    if (program) {
      // Check if Slack is configured
      const hasSlack = Boolean(program.slack_channel_link);
      setSlackSelected(hasSlack);

      // Check if Jira is configured (all fields must be present)
      const hasJira = Boolean(
        program.jira_url &&
        program.jira_email &&
        program.jira_api_token &&
        program.jira_project_key
      );
      setJiraSelected(hasJira);

      // Update notification methods based on existing integrations
      const methods = [NotificationMethods.CTB];
      if (hasSlack) methods.push(NotificationMethods.SLACK);
      if (hasJira) methods.push(NotificationMethods.JIRA);
      setNotificationMethods(methods);
    }
  }, [program]);

  return (
    <div className="min-h-screen">
      {/* Inject our CSS animations */}
      <AnimationStyles />
      <EditorLayout
        defaultValues={defaultValues}
        actions={[
          {
            label: id ? "Save" : "Create",
            type: "submit"
          }
        ]}
        onSubmit={(data: any) => {
          // Clone the data to avoid modifying the original
          const formData = { ...data };
          
          // Set private visibility and notification methods
          formData.private = privateVisibility;
          formData.notification_methods = notificationMethods;
          
          // Validate integration configurations
          
          // Validate Jira configuration if selected
          if (jiraSelected) {
            // Check all required Jira fields are filled
            const missingFields = [];
            
            if (!formData.jira_url) missingFields.push("Jira URL");
            if (!formData.jira_email) missingFields.push("Jira Email");
            if (!formData.jira_api_token) missingFields.push("Jira API Token");
            if (!formData.jira_project_key) missingFields.push("Jira Project Key");
            
            if (missingFields.length > 0) {
              alert(`Please fill in all required Jira fields: ${missingFields.join(", ")}`);
              return;
            }
          }
          
          // Validate Slack configuration if selected
          if (slackSelected && !formData.slack_channel_link) {
            alert("Please provide a Slack channel URL");
            return;
          }
          
          // Save the program and navigate back
          saveProgram(formData);
          navigate(-1);
        }}
      >
        <FormProgressTimeline />
        <EditorSection sectionTitle="Program Details">
          <EditorTextInput
            name="title"
            inputTitle="Program Name"
            details="Specify the name of this bug bounty (E.G. [Brand Name] Vulnerability Program)."
            placeholder=" "
            required
          />

          <EditorInput name="type" inputTitle="Type" required>
            <FormDropdown
              name="type"
              placeholder="Select Program Type"
              options={[
                {
                  value: ProgramType.PTAAS,
                  label: "PTaaS"
                },
                {
                  value: ProgramType.VDP,
                  label: ""
                },
                {
                  value: ProgramType.BugBounty,
                  label: ""
                }
              ]}
              rules={{
                required: true
              }}
              className=" m-0 flex w-full flex-col p-0  "
              onChangeCallback={value =>
                setSelectedPogramType(value as ProgramType)
              }
            />
            <p className="details mt-1 text-sm text-gray-600">
              Select the program's approach for researchers to process
              vulnerabilities.
            </p>
          </EditorInput>

          <EditorSection sectionTitle="App Integrations" className="mt-4 mb-4">
            <div className="mb-6 max-w-full overflow-hidden">
              <div className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-lg shadow-lg p-6 mb-6">
                <h3 className="text-xl font-bold text-white mb-2">Connect External Services</h3>
                <p className="text-blue-100 mb-0">
                  Enhance your workflow with seamless third-party integrations for real-time notifications and issue tracking.
                </p>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-6 max-w-full">
                {/* Slack Integration Card */}
                <div 
                  className={`rounded-xl border-2 p-5 max-w-full ${slackSelected ? "border-green-500 bg-gradient-to-br from-green-50 to-green-100" : "border-gray-200 bg-white"} hover:shadow-lg transition-all duration-300 cursor-pointer relative overflow-hidden`}
                  style={{
                    animation: slackSelected ? 'pulse 0.6s cubic-bezier(0.4, 0, 0.6, 1)' : 'none'
                  }}
                  onClick={() => {
                    // Toggle the selection when the card is clicked
                    const newValue = !slackSelected;
                    setSlackSelected(newValue);
                    
                    // Update notification methods
                    setNotificationMethods(prevMethods => {
                      const methods = [...prevMethods] as String[];
                      // Always include CTB
                      if (!methods.includes(NotificationMethods.CTB)) {
                        methods.push(NotificationMethods.CTB);
                      }
                      
                      // Add or remove Slack
                      if (newValue && !methods.includes(NotificationMethods.SLACK)) {
                        methods.push(NotificationMethods.SLACK);
                      } else if (!newValue) {
                        return methods.filter(m => m !== NotificationMethods.SLACK) as String[];
                      }
                      
                      return methods;
                    });
                  }}
                >
                  {/* Animated Corner Ribbon */}
                  <div 
                    className={`absolute top-0 right-0 transition-transform duration-500 ease-in-out ${slackSelected ? 'translate-x-0' : 'translate-x-16'}`}
                    style={{ transitionDelay: slackSelected ? '0.1s' : '0s' }}
                  >
                    <div className="bg-green-500 text-white h-8 w-8 flex items-center justify-center transform rotate-45 translate-y-[-16px] translate-x-[-16px] shadow-md">
                      <svg 
                        xmlns="http://www.w3.org/2000/svg" 
                        className={`h-4 w-4 transform -rotate-45 transition-opacity duration-300 ${slackSelected ? 'opacity-100' : 'opacity-0'}`} 
                        viewBox="0 0 20 20" 
                        fill="currentColor"
                      >
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="bg-gradient-to-br from-green-400 to-green-600 text-white rounded-xl h-12 w-12 flex items-center justify-center mr-4 shadow-md">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-lg font-bold">Slack</h3>
                        <p className="text-sm text-gray-600">Real-time notifications in your workspace</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Jira Integration Card */}
                <div 
                  className={`rounded-xl border-2 p-5 max-w-full ${jiraSelected ? "border-blue-500 bg-gradient-to-br from-blue-50 to-blue-100" : "border-gray-200 bg-white"} hover:shadow-lg transition-all duration-300 cursor-pointer relative overflow-hidden`}
                  style={{
                    animation: jiraSelected ? 'pulse 0.6s cubic-bezier(0.4, 0, 0.6, 1)' : 'none'
                  }}
                  onClick={() => {
                    // Toggle the selection when the card is clicked
                    const newValue = !jiraSelected;
                    setJiraSelected(newValue);
                    
                    // Update notification methods
                    setNotificationMethods(prevMethods => {
                      const methods = [...prevMethods] as String[];
                      // Always include CTB
                      if (!methods.includes(NotificationMethods.CTB)) {
                        methods.push(NotificationMethods.CTB);
                      }
                      
                      // Add or remove Jira
                      if (newValue && !methods.includes(NotificationMethods.JIRA)) {
                        methods.push(NotificationMethods.JIRA);
                      } else if (!newValue) {
                        return methods.filter(m => m !== NotificationMethods.JIRA) as String[];
                      }
                      
                      return methods;
                    });
                  }}
                >
                  {/* Animated Corner Ribbon */}
                  <div 
                    className={`absolute top-0 right-0 transition-transform duration-500 ease-in-out ${jiraSelected ? 'translate-x-0' : 'translate-x-16'}`}
                    style={{ transitionDelay: jiraSelected ? '0.1s' : '0s' }}
                  >
                    <div className="bg-blue-500 text-white h-8 w-8 flex items-center justify-center transform rotate-45 translate-y-[-16px] translate-x-[-16px] shadow-md">
                      <svg 
                        xmlns="http://www.w3.org/2000/svg" 
                        className={`h-4 w-4 transform -rotate-45 transition-opacity duration-300 ${jiraSelected ? 'opacity-100' : 'opacity-0'}`} 
                        viewBox="0 0 20 20" 
                        fill="currentColor"
                      >
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="bg-gradient-to-br from-blue-400 to-blue-600 text-white rounded-xl h-12 w-12 flex items-center justify-center mr-4 shadow-md">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
                        <path d="M12 3l8 4.5v9L12 21l-8-4.5v-9L12 3z"></path>
                        <path d="M12 12l8-4.5"></path>
                        <path d="M12 12v9"></path>
                        <path d="M12 12L4 7.5"></path>
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-bold">Jira</h3>
                      <p className="text-sm text-gray-600">Seamless issue tracking integration</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="ml-3 text-sm text-gray-700">
                    You can select multiple integrations for your program. Each integration requires specific configuration.
                  </p>
                </div>
              </div>
            </div>
          </EditorSection>
          
          {slackSelected && (
            <EditorSection sectionTitle="Slack Configuration" className="mt-6">
              <div className="bg-white p-6 rounded-xl shadow-md border-2 border-green-200 overflow-hidden">
                <div className="flex items-center mb-5 pb-4 border-b border-gray-100">
                  <div className="bg-gradient-to-br from-green-400 to-green-600 text-white rounded-xl h-12 w-12 flex items-center justify-center mr-4 shadow-md">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
                      <path d="M14.5 10c-.83 0-1.5-.67-1.5-1.5v-5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5z"></path>
                      <path d="M20.5 10H19V8.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"></path>
                      <path d="M9.5 14c.83 0 1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5S8 21.33 8 20.5v-5c0-.83.67-1.5 1.5-1.5z"></path>
                      <path d="M3.5 14H5v1.5c0 .83-.67 1.5-1.5 1.5S2 16.33 2 15.5 2.67 14 3.5 14z"></path>
                      <path d="M14 14.5c0-.83.67-1.5 1.5-1.5h5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-5c-.83 0-1.5-.67-1.5-1.5z"></path>
                      <path d="M15.5 19H14v1.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5-.67-1.5-1.5-1.5z"></path>
                      <path d="M10 9.5C10 8.67 9.33 8 8.5 8h-5C2.67 8 2 8.67 2 9.5S2.67 11 3.5 11h5c.83 0 1.5-.67 1.5-1.5z"></path>
                      <path d="M8.5 5H10V3.5C10 2.67 9.33 2 8.5 2S7 2.67 7 3.5 7.67 5 8.5 5z"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-bold text-xl text-gray-900">Slack Integration</h3>
                    <p className="text-gray-600">Configure real-time notifications for your Slack workspace</p>
                  </div>
                </div>
                
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-100 p-4 mb-6">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-6 w-6 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h4 className="text-green-800 font-medium">Integration Guide</h4>
                      <p className="text-sm text-gray-700 mt-1">
                        To integrate with Slack, you'll need to create an incoming webhook for your channel. Visit the <a href="https://api.slack.com/messaging/webhooks" target="_blank" rel="noopener noreferrer" className="font-medium text-green-600 hover:text-green-800 underline">Slack API documentation</a> to learn how.
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 gap-6 max-w-full">
                  {/* Slack Webhook URL Field with custom styling */}
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-5 border border-green-200 shadow-md transition-all duration-300 hover:shadow-lg max-w-full overflow-hidden">
                    <EditorTextInput
                      name="slack_channel_link"
                      inputTitle="Slack Webhook URL"
                      details="Enter your Slack webhook URL to receive notifications"
                      placeholder="https://hooks.slack.com/services/XXXXX/XXXXX/XXXXX"
                      required={slackSelected}
                    />
                    <p className="mt-2 text-sm text-green-700 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      The incoming webhook URL from your Slack workspace settings
                    </p>
                  </div>
                </div>
              </div>
            </EditorSection>
          )}

          {jiraSelected && (
            <EditorSection sectionTitle="Jira Configuration" className="mt-6">
              <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 max-w-full overflow-hidden">
                <div className="flex items-center mb-4 pb-3 border-b border-gray-100">
                  <div className="bg-blue-500 text-white rounded-lg h-10 w-10 flex items-center justify-center mr-3 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                      <path d="M12 3l8 4.5v9L12 21l-8-4.5v-9L12 3z"></path>
                      <path d="M12 12l8-4.5"></path>
                      <path d="M12 12v9"></path>
                      <path d="M12 12L4 7.5"></path>
                    </svg>
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold text-lg text-gray-900">Jira Integration</h3>
                        <p className="text-gray-600 text-sm">Connect your project with Jira for issue tracking</p>
                      </div>
                      <button
                        type="button"
                        onClick={() => setShowJiraGuide(true)}
                        className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors"
                      >
                        <FiHelpCircle className="w-4 h-4" />
                        Setup Guide
                      </button>
                    </div>
                  </div>
                </div>
                

                
                <div className="space-y-6 max-w-full">
                  {/* Jira Site URL Field */}
                  <div>
                    <EditorTextInput
                      name="jira_url"
                      inputTitle="Jira Site URL"
                      details="The base URL for your Jira site (e.g., https://company.atlassian.net)"
                      placeholder="https://yourdomain.atlassian.net"
                      required={jiraSelected}
                    />
                  </div>
                  
                  {/* Jira Email Field */}
                  <div>
                    <EditorTextInput
                      name="jira_email"
                      inputTitle="Jira Email"
                      details="The email associated with your Jira account"
                      placeholder="<EMAIL>"
                      required={jiraSelected}
                    />
                  </div>
                  
                  {/* Jira API Token Field */}
                  <div>
                    <EditorTextInput
                      name="jira_api_token"
                      inputTitle="Jira API Token"
                      details="Your Jira API token for authentication"
                      placeholder="Enter your Jira API token"
                      required={jiraSelected}
                    />
                  </div>
                  
                  {/* Jira Project Key Field */}
                  <div>
                    <EditorTextInput
                      name="jira_project_key"
                      inputTitle="Jira Project Key"
                      details="The project key for your Jira project (e.g., PROJECT)"
                      placeholder="e.g., PROJECT"
                      required={jiraSelected}
                    />
                  </div>
                </div>
                
                {/* Test Connection Section */}
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-semibold text-gray-900">Connection Test</h4>
                    <button
                      type="button"
                      onClick={() => {
                        const form = document.querySelector('form');
                        if (form) {
                          const formData = new FormData(form);
                          const data = {
                            jira_url: formData.get('jira_url') as string,
                            jira_email: formData.get('jira_email') as string,
                            jira_api_token: formData.get('jira_api_token') as string,
                            jira_project_key: formData.get('jira_project_key') as string,
                          };
                          testJiraConnection(data);
                        }
                      }}
                      disabled={testing}
                      className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 ${
                        testing
                          ? 'bg-gray-400 text-white cursor-not-allowed'
                          : 'bg-blue-500 hover:bg-blue-600 text-white hover:shadow-lg transform hover:scale-105'
                      }`}
                    >
                      {testing ? (
                        <span className="flex items-center">
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Testing...
                        </span>
                      ) : (
                        'Test Connection'
                      )}
                    </button>
                  </div>
                  
                  {/* Test Results */}
                  {jiraTestResult && (
                    <div className={`p-4 rounded-lg border max-w-full overflow-hidden ${
                      jiraTestResult.success 
                        ? 'bg-green-50 border-green-200' 
                        : 'bg-red-50 border-red-200'
                    }`}>
                      <div className="flex items-start">
                        <div className={`flex-shrink-0 ${jiraTestResult.success ? 'text-green-400' : 'text-red-400'}`}>
                          {jiraTestResult.success ? (
                            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                          ) : (
                            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                        <div className="ml-3 flex-1">
                          <h5 className={`font-medium ${jiraTestResult.success ? 'text-green-800' : 'text-red-800'}`}>
                            {jiraTestResult.success ? 'Connection Successful!' : 'Connection Failed'}
                          </h5>
                          {jiraTestResult.success ? (
                            <div className="mt-2">
                              {jiraTestResult.projectInfo && (
                                <div className="text-green-700 text-sm">
                                  <p><strong>Project:</strong> {jiraTestResult.projectInfo.name} ({jiraTestResult.projectInfo.key})</p>
                                  <p><strong>Available Issue Types:</strong> {jiraTestResult.projectInfo.issueTypes.join(', ')}</p>
                                </div>
                              )}
                            </div>
                          ) : (
                            <div className="mt-2">
                              <p className="text-red-700 text-sm">{jiraTestResult.error}</p>
                              {jiraTestResult.suggestions && jiraTestResult.suggestions.length > 0 && (
                                <div className="mt-2">
                                  <p className="text-red-700 text-sm font-medium">Suggestions:</p>
                                  <ul className="text-red-600 text-sm mt-1 list-disc list-inside">
                                    {jiraTestResult.suggestions.map((suggestion: string, index: number) => (
                                      <li key={index}>{suggestion}</li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </EditorSection>
          )}

          {selectedPogramType === ProgramType.BugBounty && (
            <EditorInput name="private" inputTitle="Program Access" required>
              <FormDropdown
                name="private"
                options={[
                  { value: 0, label: "Public", isDisabled: false },
                  { value: 1, label: "Private", isDisabled: false }
                ]}
                value={
                  privateVisibility !== undefined
                    ? privateVisibility.toString()
                    : "0"
                }
                onChangeCallback={value =>
                  setPrivateVisibility(value ? (Number(value) as 0 | 1) : 0)
                }
                rules={{ required: true }}
                className="py-3"
              />
              <p className="details mt-1 text-sm text-gray-600">
                Select the visibility and access type of this program.
              </p>
            </EditorInput>
          )}

          <EditorRichTextInput
            name="description"
            inputTitle="Description"
            details="Provide a description about the company and the program."
            required
          />
        </EditorSection>

        <EditorSection sectionTitle="Targets & Credentitals" className="mt-6">
          <TargetsInput
            name="targets"
            inputTitle="Targets"
            details="Specify digital assets and their types that are eligible for testing and reporting in this program."
            required
          />

          <ProgramEditorAttachments
            name="attachments"
            inputTitle="Target Attachments"
            details="Optionally add attachments to this program."
            note="Supports any file type"
            placeholder="No Attachments"
            accept="*" // Allow all files
            multiple={true} // Enable multiple selection
            defaultLinks={program?.attachments || []}
          />

          <EditorRichTextInput
            name="vpn"
            inputTitle="VPN Details"
            details="Provide information about any required VPNs (virtual private networks) to access the target assets"
          />

          <EditorRichTextInput
            name="credentials"
            inputTitle="Credentials"
            details="Provide necessary access information for testing the target assets (if applicable)"
          />
        </EditorSection>

        <EditorSection sectionTitle="Scope Description" className="mt-6">
          <EditorRichTextInput
            name="scope"
            inputTitle="Scope"
            details="For a thorough understanding of the testing scope and assets, please provide the necessary documentation and any relevant walkthrough video links."
            required
          />

          {!!(selectedPogramType !== ProgramType.PTAAS) && (
            <EditorRichTextInput
              name="outOfScope"
              inputTitle="Out of Scope"
              details="Provide details about the digital assets that are NOT open for examination and reporting"
              required
            />
          )}

          {!!(selectedPogramType !== ProgramType.PTAAS) && (
            <EditorRichTextInput
              name="knownVulnerabilities"
              inputTitle="Known Vulnerabilities"
              details="Provide details about any addressed vulnerabilites (from previous experience or approved reports)"
            />
          )}
        </EditorSection>

        <EditorSection
          sectionTitle={
            selectedPogramType === ProgramType.PTAAS
              ? "Service Level Agreement"
              : "Rewards"
          }
          className="mt-6"
        >
          {!!(selectedPogramType === ProgramType.BugBounty) && (
            <ProgramRewardsInput
              name="rewards"
              inputTitle="Reward Tiers"
              required
            />
          )}

          {!!(selectedPogramType === ProgramType.BugBounty) && (
            <EditorRichTextInput
              name="rewardPolicy"
              inputTitle="Reward Policy"
              details="Provide rules and guidelines outlining how researchers are compenstated for discovering and responsibly disclosing vulnerabilities"
              required
            />
          )}

          <EditorRichTextInput
            name="termsOfService"
            inputTitle={
              selectedPogramType === ProgramType.PTAAS
                ? "Service Level Agreement"
                : "Terms and Conditions"
            }
            details={
              selectedPogramType === ProgramType.PTAAS
                ? "SLA will be automatically updated once the program is approved by our admins"
                : "Provide an outline of the terms and conditions of how this program will be conducted"
            }
            required={selectedPogramType !== ProgramType.PTAAS}
            disabled={selectedPogramType === ProgramType.PTAAS}
          />
        </EditorSection>

        <EditorSection sectionTitle="Additional Information" className="mt-6">
          {!!(selectedPogramType === ProgramType.PTAAS) && (
            <DropDownEditor
              name="testingType"
              inputTitle="Testing Type"
              placeholder="Select Testing Type"
              options={testingTypeOptions}
              value=""
              required
            />
          )}
          {!!(selectedPogramType === ProgramType.PTAAS) && (
            <DropDownEditor
              name="environmentType"
              inputTitle="Environment Type"
              placeholder="Select Environment Type"
              options={environmentTypeOptions}
              value=""
              required
              className=" mt-5 !bg-ctb-blue-600"
            />
          )}
          {!!(selectedPogramType === ProgramType.PTAAS) && (
            <div className="flex w-full gap-6">
              <DatePicker
                name="expectedStartDate"
                inputTitle="Expected Start Date"
                required
              />

              <DatePicker
                name="expectedEndDate"
                inputTitle="Expected End Date"
                required
              />
            </div>
          )}
          {!!(selectedPogramType === ProgramType.PTAAS) && (
            <DropDownEditor
              name="complianceType"
              placeholder="Select Compliance Type"
              inputTitle="Compliance Type"
              options={complianceTypeOptions}
              value=""
              required
              onChangeCallback={value => {
                setSelectedComplianceType(value);
              }}
            />
          )}
          {!!(selectedPogramType === ProgramType.PTAAS) &&
            selectedComplianceType === complianceTypes.Other && (
              <EditorTextInput
                name="otherComplianceType"
                inputTitle="Specify Compliance Type"
                details="Mention the type of compliance, mention NA if not applicable"
                placeholder=""
              />
            )}

          <EditorRichTextInput
            name="other"
            inputTitle="Other"
            details="Provide any essential information that has not been disclosed"
          />

          <ProgramEditorLogo
            name="profilePicture"
            inputTitle="Logo Image"
            details="Optionally add a logo image to this program."
            note="Only support .jpg, .png"
            placeholder="No Logo"
            accept="image/*"
            cropImage={{
              title: "New Logo",
              cropSize: {
                width: 300,
                height: 300
              },
              previewSize: {
                width: 330,
                height: 390
              },
              minZoom: 50,
              maxZoom: 300
            }}
            defaultLinks={
              program?.profilePicture ? [program.profilePicture] : []
            }
          />
        </EditorSection>
      </EditorLayout>

      {/* Jira Setup Guide Modal */}
      <JiraSetupGuideModal
        isOpen={showJiraGuide}
        onClose={() => setShowJiraGuide(false)}
      />
    </div>
  );
};

export default ProgramEditor;
