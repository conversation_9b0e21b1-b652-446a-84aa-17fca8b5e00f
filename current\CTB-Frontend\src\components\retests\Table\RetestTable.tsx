import React, { useState, useEffect } from "react";
import { CTBRetest } from "../../../utils/api/endpoints/retests/parseRetests";
import RetestTableRow from "./RetestTableRow";
import SearchBar from "../utils/searchBar";
import FilterSection from "../utils/FilterSection";
import Pagination from "../utils/Pagination";
import ResetIcon from "../../../assets/icons/ResetIcon";

interface RetestTableProps {
  retests: CTBRetest[];
  onRowClick: (retest: CTBRetest) => void;
}

// Load filters from local storage
const loadFiltersFromLocalStorage = () => {
  const savedFilters = localStorage.getItem("retestFilters");
  return savedFilters
    ? JSON.parse(savedFilters)
    : {
        status: [],
        month: [],
        year: [],
        order: "desc",
      };
};

// Save filters to local storage
const saveFiltersToLocalStorage = (filters: {
  status: string[];
  month: string[];
  year: string[];
  order: string;
}) => {
  localStorage.setItem("retestFilters", JSON.stringify(filters));
};

const RetestTable: React.FC<RetestTableProps> = ({ retests, onRowClick }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState(loadFiltersFromLocalStorage());
  const rowsPerPage = 6;

  // Reverse the order of retests
  const reversedRetests = [...retests].reverse();

  // Filter retests based on search query
  const filteredRetests = reversedRetests.filter((retest) =>
    Object.values(retest).some((value) =>
      value.toString().toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  // Apply filters
  const filteredAndSortedRetests = filteredRetests
    .filter((retest) => {
      const statusMatch =
        filters.status.length === 0 || filters.status.includes(retest.status);
      const monthMatch =
        filters.month.length === 0 ||
        filters.month.includes(
          new Date(retest.createdAt).toLocaleString("default", {
            month: "long",
          })
        );
      const yearMatch =
        filters.year.length === 0 ||
        filters.year.includes(
          new Date(retest.createdAt).getFullYear().toString()
        );
      return statusMatch && monthMatch && yearMatch;
    })
    .sort((a, b) => {
      if (filters.order === "desc") {
        return (
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
      } else {
        return (
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );
      }
    });

  // Calculate the total number of pages
  const totalPages = Math.ceil(filteredAndSortedRetests.length / rowsPerPage);

  // Get the rows for the current page
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const currentRows = filteredAndSortedRetests.slice(startIndex, endIndex);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle search query change
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // Reset to the first page when a new search query is entered
  };

  // Handle filter change
  const handleFilter = (newFilters: {
    status: string[];
    month: string[];
    year: string[];
    order: string;
  }) => {
    setFilters(newFilters);
    saveFiltersToLocalStorage(newFilters); // Save filters to local storage
    setCurrentPage(1); // Reset to the first page when filters change
  };

  // Handle reload
  const handleReload = () => {
    setSearchQuery("");
    setFilters({ status: [], month: [], year: [], order: "desc" });
    localStorage.removeItem("retestFilters"); // Clear filters from local storage
    setCurrentPage(1);
  };

  // Determine if pagination should be shown
  const showPagination = filteredAndSortedRetests.length > rowsPerPage;

  return (
    <div className="overflow-hidden">
      <div className="mb-4 flex items-center justify-between">
        <div className="flex w-full gap-2">
          <FilterSection retests={retests} filters={filters} onFilter={handleFilter} />
          <SearchBar onSearch={handleSearch} />
        </div>
        <div className="flex items-center space-x-4">
          <button onClick={handleReload}>
            <ResetIcon className="stroke-black" />
          </button>
        </div>
      </div>
      <table className="w-full table-auto">
        <thead className="border-y-2 border-gray-300 text-black">
          <tr>
            <th className="px-6 py-4 text-left font-semibold">Report Name</th>
            <th className="px-6 py-4 text-left font-semibold">Status</th>
            <th className="px-6 py-4 text-left font-semibold">
              Retest open date
            </th>
            <th className="px-6 py-4 text-left font-semibold">
              Latest update date
            </th>
          </tr>
        </thead>
        <tbody className="">
          {currentRows.length === 0 ? (
            <tr>
              <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                No retests found
              </td>
            </tr>
          ) : (
            currentRows.map((retest, index) => (
              <RetestTableRow
                key={retest.retest_id}
                retest={retest}
                index={startIndex + index}
                onClick={() => onRowClick(retest)}
              />
            ))
          )}
        </tbody>
      </table>
      {showPagination && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
        />
      )}
    </div>
  );
};

export default RetestTable;