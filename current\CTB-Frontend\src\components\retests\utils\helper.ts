export const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric"
  });
};

export const getStatusColor = (status: string) => {
  switch (status) {
    case "QA Review In Process":
    case "Retest In Process":
    case "Fix in Progress":
    case "Reopen Retest":
    case "Request Further Action":
    case "Need Information":
    case "Retest Requested":
      return "bg-blue-100 text-blue-800";
    case "Fix Approved":
    case "Fix Verified":
    case "Risk Accepted and Closed":
      return "bg-green-100 text-green-800";
    case "Fix Rejected":
    case "Fix Failed":
    case "Findings Rejected":
    case "Close Retest":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export const renderTrimmedHTML = (html: string, maxLength: number = 35) => {
  if (html.length <= maxLength) {
    return { __html: html };
  }

  const trimmedContent = html.substring(0, maxLength);
  const lastSpaceIndex = trimmedContent.lastIndexOf(" ");

  // Trim at the last space to avoid cutting off words
  const safeTrimmedContent =
    lastSpaceIndex === -1
      ? trimmedContent
      : trimmedContent.substring(0, lastSpaceIndex);

  return { __html: safeTrimmedContent + "..." };
};
