import { useState, useEffect, useCallback, useRef } from "react";
import { getSeverityAndTrend } from "../../api/endpoints/dashboard/dashboardApi";

export interface SeverityItem {
  total: number;
  businessResolved: number;
  realResolved?: number;
}

export interface SeverityOverview {
  critical: SeverityItem;
  high: SeverityItem;
  medium: SeverityItem;
  low: SeverityItem;
}

export interface ProgramSeverityData {
  program_id: number;
  name: string;
  description?: string;
  severityOverview: SeverityOverview;
}

export interface WeeklyTrend {
  week: string;
  year: number;
  month: string;
  critical: number;
  high: number;
  medium: number;
  low: number;
  created: number;
  resolved: number;
}

export interface MonthlyTrend {
  month: string;
  year: number;
  critical: number;
  high: number;
  medium: number;
  low: number;
  created: number;
  resolved: number;
}

export interface QuarterlyTrend {
  quarter: string;
  year: number;
  critical: number;
  high: number;
  medium: number;
  low: number;
  created: number;
  resolved: number;
}

export interface YearlyTrend {
  year: number;
  critical: number;
  high: number;
  medium: number;
  low: number;
  created: number;
  resolved: number;
}

export interface SeverityAndTrendData {
  severityOverview: {
    total: SeverityOverview;
    programs: ProgramSeverityData[];
  };
  reportsTrend: {
    weekly: WeeklyTrend[];
    monthly: MonthlyTrend[];
    quarterly: QuarterlyTrend[];
    yearly: YearlyTrend[];
  };
  metadata: {
    dataStartDate: string | null;
    dataEndDate: string | null;
  };
}

/**
 * Hook to fetch severity overview and reports trend data
 * @returns Severity and trend data with loading and error states
 */
export default function useSeverityAndTrend() {
  const [data, setData] = useState<SeverityAndTrendData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const isFetchingRef = useRef(false);
  const mountedRef = useRef(true);

  const fetchData = useCallback(async () => {
    // Prevent duplicate API calls
    if (isFetchingRef.current) return;
    try {
      isFetchingRef.current = true;
      setLoading(true);
      setError(null);
      
      const response = await getSeverityAndTrend();
      
      // Prevent updates if component unmounted
      if (!mountedRef.current) return;
      
      // Validate response has the expected shape
      if (
        response &&
        typeof response === 'object' &&
        'success' in response &&
        response.success === true &&
        'data' in response
      ) {
        const trendData = response.data;
        
        if (
          trendData &&
          typeof trendData === 'object' &&
          'severityOverview' in trendData &&
          'reportsTrend' in trendData &&
          'metadata' in trendData
      ) {
          setData(trendData);
        } else {
          console.error("Invalid severity and trend data structure:", trendData);
          setError("The severity and trend data format is invalid");
        }
      } else {
        console.error("Unexpected severity and trend response format:", response);
        setError("The severity and trend API response format is invalid");
      }
    } catch (err) {
      // Prevent updates if component unmounted
      if (!mountedRef.current) return;
      
      console.error("Error fetching severity and trend data:", err);
      setError("Failed to load severity and trend data");
    } finally {
      // Prevent updates if component unmounted
      if (mountedRef.current) {
        setLoading(false);
      }
      isFetchingRef.current = false;
    }
  }, []);

  useEffect(() => {
    // Set up mounted ref
    mountedRef.current = true;
    
    // Initial fetch
    fetchData();
    
    // Cleanup function to prevent state updates after unmount
    return () => {
      mountedRef.current = false;
    };
  }, [fetchData]);

  const retryFetch = () => {
    fetchData();
  };

  return { data, loading, error, refetch: fetchData };
} 