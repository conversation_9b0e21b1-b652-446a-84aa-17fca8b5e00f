import axios from "../axios";
import toast from "react-hot-toast";
import { NotificationMethods } from "./programs/parsePrograms";

export const postSlackLink = async (
  slackChannelLink: string,
  programId: string
) => {
  try {
    if (!slackChannelLink || !programId) {
      toast.error("cannot send empty link");
    } else {
      axios
        .post("admin/add-slack-link", {
          slackChannelLink: slackChannelLink,
          programId: programId
        })
        .then(res => {
          res.status === 201
            ? toast.success("slack webhook url added successfully")
            : toast.error("something went wrong please try again");
        });
    }
  } catch (error) {
    console.log(`an error happened at /add-slack-link: ${error}`);
    toast.error("an error occured, try again");
  }
};

export const deleteSlackLink = async (programId: string) => {
  try {
    if (!programId) {
      toast.error("program Id not found");
    } else {
      await axios
        .post("admin/delete-slack-link", {
          programId: programId.toString()
        })
        .then(res => {
          res.status === 200
            ? toast.success("slack webhook url deleted")
            : toast.error("an error occured, try again");
        });
    }
  } catch (error) {
    console.log(`an error happened at /delete-slack-link: ${error}`);
    toast.error("an error occured, try again");
  }
};

export const activateMethod = async (
  method: NotificationMethods,
  programId: string
) => {
  try {
    if (!method || !programId) {
      toast.error("error occured try again");
    } else {
      const res = await axios.post("admin/opt-integration", {
        optingMethod: method,
        programId: programId.toString()
      });
      res.status === 200
        ? toast.success(`opted for ${method} integration`)
        : toast.error("could not opt for integration");
      return [NotificationMethods.CTB, method];
    }
  } catch (error) {
    toast.error("could not activate try again");
  }
};
