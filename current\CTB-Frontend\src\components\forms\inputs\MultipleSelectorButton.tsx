import { Controller, useFormContext } from "react-hook-form";
import InlineContainer from "../../common/InlineContainer";
import OutlineButton from "../../buttons/OutlineButton";
import { InputBaseParams } from "../Form";

export type ButtonOption<T extends string | number | boolean | undefined> = {
  value: T;
  label?: string;
  isDisabled?: boolean;
};

export const ButtonOptions = <T extends string | number | boolean | undefined>({
  value,
  options,
  error,
  className,
  onChange
}: {
  value?: T[];
  options: ButtonOption<T>[];
  error?: boolean;
  className?: string;
  onChange: (values: T[]) => void;
}) => (
  <InlineContainer className="w-fit">
    {options.map((option, index) => (
      <OutlineButton
        key={index}
        className={
          className +
          " " +
          (value && value.includes(option.value)
            ? "!bg-ctb-blue-400 !text-white hover:!cursor-default"
            : error
            ? "border-ctb-red-500 text-ctb-red-500"
            : "")
        }
        onClick={() => {
          const updatedValues = value ? [...value] : [];
          const valueIndex = updatedValues.indexOf(option.value);
          if (valueIndex !== -1) {
            updatedValues.splice(valueIndex, 1);
          } else {
            updatedValues.push(option.value);
          }
        //   console.log(updatedValues)
          onChange(updatedValues);
        }}
        disabled={option?.isDisabled}
      >
        {option.label || option.value}
      </OutlineButton>
    ))}
  </InlineContainer>
);

const MultipleSelectorButton = <T extends string | number | boolean>({
  name,
  options,
  rules,
  className,
  onChangeCallback
}: InputBaseParams & {
  options: ButtonOption<T>[];
  onChangeCallback?: (values: T[]) => void;
}) => {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field: { value, onChange }, fieldState: { error } }) => (
        <ButtonOptions
          value={value || []}
          options={options}
          error={error !== undefined}
          onChange={(values) => {
            onChangeCallback && onChangeCallback(values);
            onChange(values);
          }}
          className={className}
        />
      )}
    />
  );
};

export default MultipleSelectorButton;
