import { Request, Response } from "express";
import axios from "axios";
import { logger } from "../logger/index";
import { database } from "../models/db";
import {
  AWAITING_FIX,
  BUSINESS_APPROVED,
  BUSINESS_REJECTED,
  BUSINESS_REVIEW,
  CERTIFICATE_GENERATED_CTB_BODY,
  CERTIFICATE_GENERATED_CTB_PARTNER_BODY,
  CERTIFICATE_GENERATED_SUBJECT,
  CLOSED,
  CTB_LOGO,
  CTB_VDP_ID,
  DEFAULT_PROFILE,
  DELETED,
  NEW_COMMENT_ON_REPORT_BODY,
  NEW_COMMENT_SUBJECT,
  NotificationMethod,
  ProgramTypes,
  RECEIVED,
  TRIAGE_APPROVED,
  TRIAGE_REJECTED,
  TRIAGE_SERVICE_NOT_OPTED,
  TRIAGE_SERVICE_OPTED_FALSE,
  TRIAGE_STATUS_APPROVED,
  UNDER_REVIEW,
  USER_ROLE_ADMIN
} from "../utils/constants";
import { ReportAttributes, Report } from "../models/report.model";
import viewCertificate from "../utils/viewCertificate";
import sendEmail from "../utils/send-email";
import {
  CTBBodyRequest,
  CTBParamsRequest,
  CTBQueryRequest,
  CTBRequest
} from "../../server";
import { decodeAndSaveImages } from "../utils/imageParser";
import { CommentAttributes } from "../models/comments.model";
import formidable from "formidable";
import { checkFile } from "../utils/fileChecking";
import { Op, WhereOptions, Sequelize, TextDataType } from "sequelize";
import { viewBusinessSummaryReport } from "../utils/viewBusinessSummary";
import { UserRole } from "../utils/auth";
import { uploadFileToBlob } from "../utils/blobStorageHelper";
import { GET_CVCSS_CATEGORY } from "../utils/constants";
import { safeArray, safeInt, safeString } from "../utils/validation";
import { sendNotification } from "./notifications.controller";
import { NotificationType } from "../models/notifications.model";
import { ProgramCreator } from "./program.controller";
import { Program } from "../models/program.model";
import { getReportsOrderedBySeverityCategory } from "../utils/parsers";
import {
  createJiraIssue,
  revertJiraIssueStatus,
  revertJiraIssueToStatus
} from "../utils/jira-integration";

const Users = database.user;
const Programs = database.program;
const Reports = database.report;
const Certificates = database.certificate;
const Comments = database.comments;
const Retest = database.retest;
const ActivityLog = database.activity_logs;

export const notifySlack = async (channelLink: string, message: string) => {
  try {
    if (!channelLink) return false;

    await axios.post(channelLink, {
      text: message
    });
    return true;
  } catch (error) {
    logger.log({
      level: "error",
      label: "Slack Notification",
      message: `Failed to send Slack notification: ${error}`
    });
    return false;
  }
};

// Possible report states (simplified)
export enum ReportState {
  BUSINESS_APPROVED = "Business Approved",
  BUSINESS_REJECTED = "Business Rejected",
  BUSINESS_REVIEW = "Business Review",
  AWAITING_FIX = "Awaiting Fix",
  BUSINESS_REQUEST_INFO = "Request more info",
  BUSINESS_ACCEPT_RISK = "Business Accepts Risk",
  BUSINESS_NON_ACTIONABLE_ISSUE = "Business Non Actionable Issue",
  OUT_OF_SCOPE = "out of scope",
  // QA_DISAGREES = "QA Disagrees",
  QA_MODIFIED = "QA Modified",
  TRIAGE_APPROVED = "Triage Approved",
  TRIAGE_REJECTED = "Triage Rejected",
  UNDER_REVIEW = "underReview",
  CLOSED = "closed",
  PAYMENT_INITIATED = "Payment Initiated",
  DELETED = "deleted"
}

// Possible report status
export enum ReportStatus {
  DRAFT = "Draft",
  REVIEW = "In Review",
  APPROVED = "Approved",
  REJECTED = "Rejected"
}

type ReportCreator = {
  displayName: string;
  pfp?: string | TextDataType;
};

class ReportResponse extends Report<{
  creator?: ReportCreator;
  program_creator?: ProgramCreator;
}> {
  declare creator?: ReportCreator;
  program_creator?: ProgramCreator;
}

// The number of reports to return per page
const PAGE_COUNT = 10;
// The maximum size of attachments in bytes
const MAX_FILE_SIZE = 5242880;

/**
 * Retrieve the query options
 */
const getReportQueryOptions = async (user_id: number, role: UserRole) => {
  // For DEVELOPER and BUSINESS_MANAGER, get parent_user_id first
  let effectiveUserId = user_id;

  if (
    role === UserRole.DEVELOPER ||
    role === UserRole.BUSINESS_MANAGER ||
    role === UserRole.BUSINESS_ADMINISTRATOR
  ) {
    const user = await Users.findOne({ where: { user_id } });
    if (!user?.parent_user_id) {
      throw new Error("Parent business not found");
    }
    effectiveUserId = user.parent_user_id;
  }

  // Retrieve the list of programs created by the current user (if the user is a business)
  const programs =
    role === UserRole.BUSINESS ||
    role === UserRole.DEVELOPER ||
    role === UserRole.BUSINESS_MANAGER ||
    role === UserRole.BUSINESS_ADMINISTRATOR
      ? await Programs.findAll({
          attributes: ["program_id"],
          where: { user_id: effectiveUserId } // Use effectiveUserId here
        })
      : role === UserRole.RESEARCHER
      ? await Programs.findAll({
          attributes: ["program_id"],
          where: { user_id } // Use original user_id for researchers
        })
      : [];

  /**
   * Use particular query options based on user role
   */
  const where: WhereOptions<ReportAttributes> =
    role === UserRole.ADMIN ||
    role === UserRole.SUB_ADMIN ||
    role === UserRole.ADMIN_MANAGER ||
    role === UserRole.QA
      ? // Admin
        {
          state: {
            [Op.not]: null
          },
          is_delete: false
        }
      : role === UserRole.RESEARCHER
      ? // Researcher
        { user_id } // Use original user_id for researchers
      : // Business, Developer, Business Manager
        {
          triage_status: {
            [Op.in]: [TRIAGE_STATUS_APPROVED, TRIAGE_SERVICE_NOT_OPTED]
          },
          program_id: {
            [Op.in]: programs.map(program => program.program_id)
          }
        };

  return where;
};
const prepareReportsResponse = async (data: Report[]) =>
  Promise.all(
    data.map(async (report: ReportResponse) => {
      // Get Program creator info
      await Programs.findOne({
        where: { program_id: report.program_id }
      })
        .then(async program => {
          await Users.findOne({
            where: { user_id: program?.user_id }
          }).then(data => {
            report.setDataValue("program_creator", {
              displayName:
                data.display_name && data.display_name !== ""
                  ? data.display_name
                  : data.username,
              pfp: data.pfp
            });
          });
        })
        .catch(err => {
          logger.log({
            level: "error",
            label: "GET | Reports",
            message: `Error getting user ${report.user_id} for report ${report.report_id}: ${err}`
          });
        });

      // Get report creator info
      await Users.findOne({
        where: { user_id: report.user_id }
      })
        .then(data => {
          report.setDataValue("creator", {
            displayName:
              data.display_name && data.display_name !== ""
                ? data.display_name
                : data.username,
            pfp: data.pfp
          });
        })
        .catch(err => {
          logger.log({
            level: "error",
            label: "GET | Reports",
            message: `Error getting user ${report.user_id} for report ${report.report_id}: ${err}`
          });
        });

      // Check if the report status is 'Awaiting Fix' and fetch retest status
      if (report.state === "Awaiting Fix") {
        try {
          const retest = await Retest.findOne({
            where: { report_id: report.report_id },
            order: [["created_at", "DESC"]] // Get the latest retest
          });

          if (retest) {
            report.setDataValue("state", retest.status);
          }
        } catch (err) {
          logger.log({
            level: "error",
            label: "GET | Reports",
            message: `Error fetching retest for report ${report.report_id}: ${err}`
          });
        }
      }

      return report;
    })
  );

/**
 * Handle a GET request to retrieve a list of all
 * reports available to the current user
 */
export const getReports = async (
  req: CTBQueryRequest<{
    severity?: string;
    status?: string;
    type?: string;
    page?: string;
    pageCount?: string;
    searchQuery?: string;
    program?: string;
    reportState?: string;
    severityCategory?: string;
    vulnerabilityType?: string;
    sortOrder?: string;
    pentesterUsername?: string;
  }>,
  res: Response
) => {
  const { user_id, role } = req.user;
  try {
    // Base query options to fetch reports for the user
    const where = await getReportQueryOptions(user_id, role);
    // Additional filters
    const {
      page = "1",
      pageCount = "10",
      searchQuery = "",
      program = "",
      reportState = "",
      severityCategory = "",
      vulnerabilityType = "",
      sortOrder = "desc",
      pentesterUsername = ""
    } = req.query;
    const limit = parseInt(pageCount as string) || 10;
    const offset = (parseInt(page as string) - 1) * limit;
    // Build dynamic where clause
    const dynamicWhere: any = { ...where };
    const orConditions = [];
    const include = [];

    // Add Users join for pentester search
    include.push({
      model: database.user,
      as: "user",
      required: false
    });

    if (searchQuery) {
      orConditions.push({
        [Op.or]: [
          { report_title: { [Op.like]: `%${searchQuery}%` } },
          { description: { [Op.like]: `%${searchQuery}%` } },
          { category: { [Op.like]: `%${searchQuery}%` } },
          { severity_category: { [Op.like]: `%${searchQuery}%` } },
          { state: { [Op.like]: `%${searchQuery}%` } },
          { scope: { [Op.like]: `%${searchQuery}%` } },
          { "$user.display_name$": { [Op.like]: `%${searchQuery}%` } },
          { "$user.username$": { [Op.like]: `%${searchQuery}%` } }
        ]
      });
    }

    // Handle multiple program IDs
    if (program) {
      const programIds = program.split(",").map(id => id.trim());
      orConditions.push({
        program_id: {
          [Op.in]: programIds
        }
      });
    }

    // Handle multiple report states
    if (reportState) {
      const states = reportState.split(",").map(state => state.trim());

      // Special handling for "Awaiting Fix" status
      if (states.includes("Awaiting Fix")) {
        // First get all reports with "Awaiting Fix" state
        const awaitingFixReports = await Reports.findAll({
          where: {
            ...where,
            state: "Awaiting Fix"
          },
          attributes: ["report_id"]
        });

        // Then get all reports that have retests
        const reportsWithRetests = await database.retest.findAll({
          attributes: ["report_id"],
          group: ["report_id"]
        });

        // Filter out reports that have retests
        const reportIdsWithoutRetests = awaitingFixReports
          .map(report => report.report_id)
          .filter(
            id => !reportsWithRetests.some(retest => retest.report_id === id)
          );

        // If there are other states besides "Awaiting Fix", combine them
        const otherStates = states.filter(state => state !== "Awaiting Fix");
        if (otherStates.length > 0) {
          orConditions.push({
            [Op.or]: [
              {
                report_id: {
                  [Op.in]: reportIdsWithoutRetests
                }
              },
              {
                state: {
                  [Op.in]: otherStates
                }
              }
            ]
          });
        } else {
          orConditions.push({
            report_id: {
              [Op.in]: reportIdsWithoutRetests
            }
          });
        }
      } else {
        orConditions.push({
          state: {
            [Op.in]: states
          }
        });
      }
    }

    // Handle multiple severity categories
    if (severityCategory) {
      const severities = severityCategory.split(",").map(sev => sev.trim());
      orConditions.push({
        severity_category: {
          [Op.in]: severities
        }
      });
    }

    // Handle multiple vulnerability types
    if (vulnerabilityType) {
      const types = vulnerabilityType.split(",").map(type => type.trim());
      orConditions.push({
        category: {
          [Op.in]: types
        }
      });
    }

    // Add OR conditions to the where clause if any exist
    if (orConditions.length > 0) {
      dynamicWhere[Op.and] = [...orConditions];
    }

    // Join with Users for pentesterUsername
    if (pentesterUsername) {
      const pentesters = pentesterUsername.split(",").map(name => name.trim());
      include.push({
        model: database.user,
        as: "user",
        where: {
          [Op.or]: [
            { display_name: { [Op.in]: pentesters } },
            { username: { [Op.in]: pentesters } }
          ]
        },
        required: true
      });
    }

    // Fetch paginated and filtered reports
    const { rows: reportsData, count: total } = await Reports.findAndCountAll({
      where: dynamicWhere,
      include,
      order: [["submitted_date", sortOrder === "asc" ? "ASC" : "DESC"]],
      limit,
      offset
    });

    const reports = await prepareReportsResponse(reportsData);
    return res.status(200).send({
      data: reports,
      results: total,
      page: parseInt(page as string),
      pageCount: limit
    });
  } catch (err) {
    logger.log({
      level: "error",
      label: "GET | Reports",
      message: `Error retrieving reports for user ${user_id}: ${err.message}`
    });
    console.error(err);
    return res.status(500).send({
      message: "500 INTERNAL ERROR: Cannot retrieve reports."
    });
  }
};

export const getReport = async (
  req: CTBParamsRequest<{
    id: string;
  }>,
  res: Response
) => {
  const { user_id, role } = req.user;
  const id = safeInt(req.params.id, -1, 0);

  logger.log({
    level: "info",
    label: "GET | Reports",
    message: `Retrieving report ${id} for user ${user_id}...`
  });

  if (id === -1) {
    return res.status(400).send({ message: "Invalid report ID" });
  }

  const where = await getReportQueryOptions(user_id, role);

  Reports.findOne({
    where: {
      ...where,
      report_id: id
    }
  })
    .then(async data => {
      if (data === null) {
        logger.log({
          level: "info",
          label: "GET | Report",
          message: `No report ${id} found for user ${user_id}.`
        });

        return res
          .status(404)
          .send({ message: "No report found with that ID" });
      }

      const report = await prepareReportsResponse([data]);

      logger.log({
        level: "info",
        label: "GET | Report",
        message: `Retrieved report ${id} for user ${user_id}.`
      });

      return res.status(200).send(report);
    })
    .catch(err => {
      logger.log({
        level: "error",
        label: "GET | Report",
        message: `Failed to retrieve report ${id} for user ${user_id}.`
      });
      console.log(err);

      return res.status(500).send({ message: "Failed to get report." });
    });
};

/**
 * Handles a GET request to retrieve a summary of all
 * reports available to the current user
 */
export const getReportSummary = async (
  req: CTBQueryRequest<{ num?: number }>,
  res: Response
) => {
  const { user_id, role } = req.user;

  const numResults = safeInt(req.query.num, 3, 0);

  logger.log({
    level: "info",
    label: "GET | Report Summary",
    message: `Generating report summary for user ${user_id}...`
  });

  // Retrieve the most recent reports
  const where: WhereOptions<ReportAttributes> = await getReportQueryOptions(
    user_id,
    role
  );

  try {
    // Grab a list of the most recent reports
    const latest = await Reports.findAll({
      where,
      limit: numResults,
      order: [["submitted_date", "DESC"]]
    });

    // Get the counts of various report states

    // Create a copy of the `where` object without the specified key to remove state filter only for numReceived
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { triage_status: removedKey, ...newWhere } = where;
    const numReceived =
      role === UserRole.BUSINESS
        ? await Reports.count({
            where: {
              ...newWhere,
              is_delete: false
            }
          })
        : undefined;

    const numDrafts =
      role === UserRole.RESEARCHER
        ? await Reports.count({
            where: {
              ...where,
              is_delete: false,
              state: null
            }
          })
        : undefined;

    const numReview = await Reports.count({
      where: {
        ...where,
        state: {
          [Op.in]: [
            RECEIVED,
            ReportState.UNDER_REVIEW,
            ReportState.BUSINESS_REVIEW
          ]
        }
      }
    });

    const numTriageApproved = await Reports.count({
      where: {
        ...where,
        state: ReportState.TRIAGE_APPROVED || ReportState.BUSINESS_REVIEW
      }
    });

    const numTriageRejected = await Reports.count({
      where: {
        ...where,
        state: ReportState.TRIAGE_REJECTED
      }
    });

    const numBusinessApproved = await Reports.count({
      where: {
        ...where,
        state: ReportState.BUSINESS_APPROVED
      }
    });

    const numBusinessRejected = await Reports.count({
      where: {
        ...where,
        state: ReportState.BUSINESS_REJECTED
      }
    });

    const numClosed = await Reports.count({
      where: {
        ...where,
        state: ReportState.CLOSED
      }
    });

    // Also get the distribution of reports across severity levels
    const numLow = await Reports.count({
      where: {
        ...where,
        severity_category: "LOW"
      }
    });

    const numMedium = await Reports.count({
      where: {
        ...where,
        severity_category: "MEDIUM"
      }
    });

    const numHigh = await Reports.count({
      where: {
        ...where,
        severity_category: "HIGH"
      }
    });

    const numCritical = await Reports.count({
      where: {
        ...where,
        severity_category: "CRITICAL"
      }
    });

    logger.log({
      level: "info",
      label: "GET | Report Summary",
      message: `Generated report summary with ${latest.length} for user ${user_id}...`
    });

    return res.status(200).send({
      latest,
      status: {
        numReceived: numReceived,
        draft: numDrafts,
        review: numReview,
        triage_approved: numTriageApproved,
        triage_rejected: numTriageRejected,
        business_approved: numBusinessApproved,
        business_rejected: numBusinessRejected,
        closed: numClosed
      },
      severity: {
        low: numLow,
        medium: numMedium,
        high: numHigh,
        critical: numCritical
      }
    });
  } catch (err) {
    logger.log({
      level: "error",
      label: "GET | Reports Summary",
      message: `Error generating report summary for user ${user_id}: ${err.message}`
    });

    console.log(err);

    return res.status(500).send({
      message: "500 INTERNAL ERROR: Failed to generate summary"
    });
  }
};

// The possible details that could be provided to update an existing or new report
type ReportUpdateDetails = {
  report_id?: number;
  program_id?: number;
  report_title?: string;
  scope?: string;
  category?: string;
  severity?: number;
  severity_score?: number;
  description?: TextDataType;
  instructions?: TextDataType;
  impact?: TextDataType;
  fix?: TextDataType;
  additional_info?: TextDataType;
};

/**
 * Handles a POST request to update the given report
 * if it exists, or create a new one if no report ID
 * was given
 */
// Request tracking to prevent duplicate submissions
const pendingSubmissions = new Map<
  string,
  {
    timestamp: number;
    processing: boolean;
    createdAt?: number; // Track when a report was created
  }
>();

// Clean up old entries periodically
setInterval(() => {
  const now = Date.now();
  for (const [key, value] of pendingSubmissions.entries()) {
    // Remove entries older than 5 minutes
    if (now - value.timestamp > 5 * 60 * 1000) {
      pendingSubmissions.delete(key);
    }
  }
}, 60 * 1000); // Run cleanup every minute

/**
 * Generates a unique hash for a submission to detect duplicates
 */
function generateSubmissionHash(
  userId: number,
  body: ReportUpdateDetails
): string {
  // If updating an existing report, use the report_id
  if (body.report_id) {
    return `${userId}-${body.report_id}`;
  }

  // For new reports, use a combination of user_id, program_id, and report_title
  return `${userId}-${body.program_id}-${body.report_title}`;
}

/**
 * Manages the submission lock for a request
 */
const submissionLock = {
  acquire: (hash: string, isEdit: boolean): boolean => {
    if (pendingSubmissions.has(hash)) {
      const submission = pendingSubmissions.get(hash)!;

      // If it's currently being processed, don't allow another operation
      if (submission.processing) {
        return false;
      }

      // If this is an edit attempt on a newly created report
      if (isEdit && submission.createdAt) {
        const timeSinceCreation = Date.now() - submission.createdAt;

        // If it's been less than 1 second since creation, suggest waiting
        if (timeSinceCreation < 1000) {
          return false;
        }
      }
    }

    // Set processing to true, but preserve createdAt if it exists
    const existing = pendingSubmissions.get(hash);
    pendingSubmissions.set(hash, {
      timestamp: Date.now(),
      processing: true,
      createdAt: existing?.createdAt
    });

    return true;
  },

  release: (hash: string, wasCreation: boolean = false): void => {
    if (pendingSubmissions.has(hash)) {
      // If this was a creation operation, record the creation timestamp
      if (wasCreation) {
        pendingSubmissions.set(hash, {
          timestamp: Date.now(),
          processing: false,
          createdAt: Date.now()
        });
      } else {
        // For edits, just mark as not processing
        const existing = pendingSubmissions.get(hash);
        pendingSubmissions.set(hash, {
          timestamp: Date.now(),
          processing: false,
          createdAt: existing?.createdAt
        });
      }
    }
  }
};

/**
 * Validates program access for the user
 */
async function validateProgramAccess(
  programId: number,
  userId: number,
  role: number // Add role parameter
): Promise<{
  valid: boolean;
  program?: any;
  notificationMethods?: any[];
  message?: string;
}> {
  console.log(
    `Validating program access for program ${programId} and user ${userId}`
  );
  // First check if program exists at all
  const program = await Programs.findOne({ where: { program_id: programId } });

  if (!program) {
    return { valid: false, message: "Program not found" };
  }

  // Bypass access checks for admins/sub-admins/QA
  if ([UserRole.ADMIN, UserRole.SUB_ADMIN, UserRole.QA].includes(role)) {
    return {
      valid: true,
      program,
      notificationMethods: program.notification_methods
        ? [...Object.values(program.notification_methods)]
        : [NotificationMethod.CTB]
    };
  }

  // Existing checks for non-admin users
  const privateAccessUsers = safeArray(program.private_access_users, []);

  if (program.private && !privateAccessUsers.includes(userId)) {
    return { valid: false, message: "Program not found" };
  }

  return {
    valid: true,
    program,
    notificationMethods: program.notification_methods
      ? [...Object.values(program.notification_methods)]
      : [NotificationMethod.CTB]
  };
}

/**
 * Processes file attachments for a report
 */
async function processAttachments(attachments: any): Promise<{
  success: boolean;
  blobURL?: string;
  message?: string;
}> {
  if (!attachments) {
    return { success: true };
  }

  const result = checkFile(attachments, MAX_FILE_SIZE);
  if (!result.validFile) {
    return {
      success: false,
      message: `Invalid file attachments: ${result.reason}`
    };
  }

  const blob = await uploadFileToBlob(
    attachments.originalFilename,
    attachments.filepath,
    ".zip"
  );

  if (!blob.success) {
    return {
      success: false,
      message: `An error occurred uploading to blob storage: ${blob.message}`
    };
  }

  return { success: true, blobURL: blob.blobURL };
}

/**
 * Prepares report data from request body
 */
async function prepareReportData(
  body: ReportUpdateDetails,
  userId: number,
  attachmentUrl?: string
): Promise<ReportAttributes> {
  return {
    user_id: userId,
    report_id: body.report_id,
    program_id: body.program_id,
    report_title: body.report_title,
    scope: body.scope,
    category: body.category,
    severity: body.severity,
    severity_category: GET_CVCSS_CATEGORY(body.severity_score),
    description: await decodeAndSaveImages(body.description),
    instructions: await decodeAndSaveImages(body.instructions),
    impact: await decodeAndSaveImages(body.impact),
    fix: await decodeAndSaveImages(body.fix),
    additional_info: await decodeAndSaveImages(body.additional_info),
    attachments: attachmentUrl
  };
}

/**
 * Checks for existing reports with the same title
 */
async function findExistingReport(
  userId: number,
  programId: number,
  reportTitle: string
): Promise<any> {
  return await Reports.findOne({
    where: {
      user_id: userId,
      program_id: programId,
      report_title: reportTitle
    }
  });
}

/**
 * Handles the creation of a new report
 */
async function createReport(
  newReport: ReportAttributes,
  email: string,
  role: number,
  program: any,
  notificationMethods: any[]
): Promise<{
  success: boolean;
  data?: any;
  message?: string;
}> {
  try {
    const data = await Reports.create(newReport);

    await ActivityLog.create({
      user_id: newReport.user_id,
      role,
      action: `A report titled "${newReport.report_title}" has been created by ${email}.`,
      module: "Report",
      created_at: new Date()
    });

    logger.log({
      level: "info",
      label: "POST | Report",
      message: `Successfully created new report for ${email}`
    });

    // Jira integration intentionally disabled in createReport to avoid duplicate issues
    // Jira issues are only created during submitReport
    const jiraEnabled = false;

    logger.log({
      level: "info",
      label: "POST | Report | Jira Integration",
      message: `Jira integration ${
        jiraEnabled ? "enabled" : "disabled"
      } for program ${program.program_id}`
    });

    if (jiraEnabled) {
      logger.log({
        level: "info",
        label: "POST | Report | Jira Integration",
        message: `Checking Jira configuration for program ${program.program_id}`
      });

      // Check if Jira configuration is valid
      if (
        program.jira_url &&
        program.jira_email &&
        program.jira_api_token &&
        program.jira_project_key
      ) {
        logger.log({
          level: "info",
          label: "POST | Report | Jira Integration",
          message: `Jira configuration valid for program ${program.program_id}, creating Jira issue`
        });

        // Create Jira config object
        const jiraConfig = {
          url: String(program.jira_url),
          email: String(program.jira_email),
          api_token: String(program.jira_api_token),
          project_key: String(program.jira_project_key)
        };

        // Create Jira issue
        try {
          const jiraResult = await createJiraIssue(jiraConfig, {
            report_id: data.report_id,
            report_title: newReport.report_title,
            description: newReport.description,
            category: newReport.category,
            severity_category: newReport.severity_category,
            scope: newReport.scope,
            program_title: program.program_title
          });

          if (jiraResult.success) {
            logger.log({
              level: "info",
              label: "POST | Report | Jira Integration",
              message: `Successfully created Jira issue ${jiraResult.issueKey} for report ${data.report_id}`
            });
          } else {
            logger.log({
              level: "error",
              label: "POST | Report | Jira Integration",
              message: `Failed to create Jira issue for report ${data.report_id}: ${jiraResult.message}`
            });
          }
        } catch (jiraError) {
          logger.log({
            level: "error",
            label: "POST | Report | Jira Integration",
            message: `Error creating Jira issue for report ${data.report_id}: ${jiraError.message}`,
            meta: { stack: jiraError.stack }
          });
        }
      } else {
        logger.log({
          level: "error",
          label: "POST | Report | Jira Integration",
          message: `Invalid Jira configuration for program ${program.program_id}`,
          meta: {
            jira_url_exists: !!program.jira_url,
            jira_email_exists: !!program.jira_email,
            jira_api_token_exists: !!program.jira_api_token,
            jira_project_key_exists: !!program.jira_project_key
          }
        });
      }
    }

    // Send notifications
    if (program.type === ProgramTypes.PTAAS) {
      sendNotification(
        {
          type: NotificationType.ReportInfo,
          payload: "report ID created",
          message: `New Report created by Researcher: ${email} for Program: ${program.program_title}`
        },
        [program.user_id],
        notificationMethods,
        `${program.slack_channel_link}`,
        `A new report has been created by the researcher: ${email}. Report Title: ${newReport.report_title}.`
      );
    }

    if (program.slack_channel_link) {
      await notifySlack(
        program.slack_channel_link as string,
        `🆕 New Report Created\nTitle: ${newReport.report_title}\nResearcher: ${email}`
      );
    }

    return { success: true, data };
  } catch (err) {
    logger.log({
      level: "error",
      label: "POST | Report",
      message: `Failed to create report for ${email}: ${err}`
    });

    return { success: false, message: "Failed to create report." };
  }
}

/**
 * Tracks changes between old and new report versions
 */
function trackReportChanges(
  existingReport: any,
  newReport: ReportAttributes
): string[] {
  const changes: string[] = [];

  if (existingReport.report_title !== newReport.report_title) {
    changes.push(
      `Title: "${existingReport.report_title}" → "${newReport.report_title}"`
    );
  }
  if (existingReport.scope !== newReport.scope) changes.push("Scope updated");
  if (existingReport.category !== newReport.category)
    changes.push(`Category: ${newReport.category}`);
  if (existingReport.severity !== newReport.severity)
    changes.push(`Severity: ${newReport.severity}`);
  if (existingReport.severity_category !== newReport.severity_category)
    changes.push(`Severity Category: ${newReport.severity_category}`);
  if (existingReport.description !== newReport.description)
    changes.push(`Description updated`);
  if (existingReport.instructions !== newReport.instructions)
    changes.push(`Instructions updated`);
  if (existingReport.impact !== newReport.impact)
    changes.push(`Impact updated`);
  if (existingReport.fix !== newReport.fix) changes.push(`Fix updated`);
  if (existingReport.additional_info !== newReport.additional_info)
    changes.push(`Additional info updated`);
  if (existingReport.attachments !== newReport.attachments)
    changes.push("Attachments updated");

  return changes;
}

/**
 * Updates an existing report
 */
async function updateReport(
  newReport: ReportAttributes,
  existingReport: any,
  user_id,
  email: string,
  role: number
): Promise<{
  success: boolean;
  data?: any;
  message?: string;
}> {
  try {
    await Reports.update(newReport, {
      where: { user_id: newReport.user_id, report_id: newReport.report_id }
    });

    const changes = trackReportChanges(existingReport, newReport);

    await ActivityLog.create({
      user_id: user_id,
      role,
      action: `Report "${existingReport.report_title}" updated by ${email}.`,
      module: "Report",
      created_at: new Date()
    });

    logger.log({
      level: "info",
      label: "POST | Report",
      message: `Updated report ${newReport.report_id} for ${email}`
    });

    return { success: true, data: newReport };
  } catch (err) {
    logger.log({
      level: "error",
      label: "POST | Report",
      message: `Failed to update report for ${email}: ${err}`
    });
    console.error(err);

    return { success: false, message: "Failed to update report." };
  }
}

/**
 * Main controller function for posting a report
 */
export const postReport = async (req: CTBRequest, res: Response) => {
  const { user_id, email, role } = req.user;

  // Add explicit console logs to debug report creation
  console.log("=========== REPORT CREATION STARTED ===========");
  console.log(`User ${email} attempting to create/edit a report`);

  logger.log({
    level: "info",
    label: "POST | Report",
    message: `${email} saving report...`
  });
  // Extract the form data from the request
  const form = formidable({ keepExtensions: true });
  form.parse(req, async (err, fields, files) => {
    if (err) {
      logger.log({
        level: "error",
        label: "POST | Report",
        message: "An error occurred parsing form data for report"
      });
      console.error(err);
      return res.status(400).send({ message: "Invalid Form Data" });
    }
    logger.log({
      level: "info",
      label: "POST | Report",
      message: "Parsed form data succesfully..."
    });
    try {
      const body = JSON.parse(fields.data[0]) as ReportUpdateDetails;
      console.log("Report data parsed from request:", {
        report_id: body.report_id,
        program_id: body.program_id,
        report_title: body.report_title
      });
      // Generate submission hash
      const submissionHash = generateSubmissionHash(user_id, body);
      // Determine if this is an edit or a new report
      const isEditingExistingReport =
        body.report_id !== undefined && body.report_id !== null;
      // Try to acquire the lock, informing the lock manager if this is an edit
      if (!submissionLock.acquire(submissionHash, isEditingExistingReport)) {
        if (isEditingExistingReport) {
          logger.log({
            level: "warn",
            label: "POST | Report",
            message: `Edit attempt too soon after creation for report by ${email}`
          });
          return res.status(429).send({
            message:
              "Your report was just created. Please wait a moment before editing."
          });
        } else {
          logger.log({
            level: "warn",
            label: "POST | Report",
            message: `Duplicate submission detected from ${email}`
          });
          return res.status(429).send({
            message:
              "Your previous submission is still processing. Please wait."
          });
        }
      }
      // Validate program ID
      const program_id = safeInt(body.program_id, undefined, 0);
      if (program_id === undefined) {
        submissionLock.release(submissionHash);
        return res.status(400).send({ message: "Missing Program ID" });
      }
      // Validate program access
      console.log(`Validating program access for program_id: ${program_id}`);
      const programAccess = await validateProgramAccess(
        program_id,
        user_id,
        role
      );

      console.log("Program access validation result:", {
        valid: programAccess.valid,
        notificationMethods: programAccess.notificationMethods,
        program: programAccess.program
          ? {
              program_id: programAccess.program.program_id,
              program_title: programAccess.program.program_title,
              notification_methods: programAccess.program.notification_methods,
              jira_url: programAccess.program.jira_url ? "EXISTS" : "MISSING",
              jira_email: programAccess.program.jira_email
                ? "EXISTS"
                : "MISSING",
              jira_api_token: programAccess.program.jira_api_token
                ? "EXISTS"
                : "MISSING",
              jira_project_key: programAccess.program.jira_project_key
                ? "EXISTS"
                : "MISSING"
            }
          : null
      });
      if (!programAccess.valid) {
        submissionLock.release(submissionHash);
        return res.status(400).send({ message: programAccess.message });
      }
      // Process attachments
      const attachments =
        files.attachments !== undefined ? files.attachments[0] : undefined;
      const attachmentResult = await processAttachments(attachments);
      if (!attachmentResult.success) {
        submissionLock.release(submissionHash);
        return res.status(400).send({ message: attachmentResult.message });
      }
      // Prepare report data
      const newReport = await prepareReportData(
        body,
        user_id,
        attachmentResult.blobURL
      );
      try {
        // Handle report editing
        if (isEditingExistingReport) {
          logger.log({
            level: "info",
            label: "POST | Report",
            message: `Editing existing report ${newReport.report_id} for ${email}`
          });

          // Fetch existing report - modified to allow admins to edit any report
          let existingReport;
          if (
            role === UserRole.ADMIN ||
            role === UserRole.SUB_ADMIN ||
            role === UserRole.QA
          ) {
            // Admins can edit any report
            existingReport = await Reports.findOne({
              where: { report_id: newReport.report_id }
            });
          } else {
            // Researchers can only edit their own reports
            existingReport = await Reports.findOne({
              where: { user_id, report_id: newReport.report_id }
            });
          }

          if (!existingReport) {
            logger.log({
              level: "error",
              label: "POST | Report",
              message: `Report ${newReport.report_id} not found or user doesn't have permission`
            });
            submissionLock.release(submissionHash);
            return res.status(404).send({
              message:
                "Report not found or you don't have permission to edit it"
            });
          }

          // For admins editing researcher reports, preserve the original user_id
          if (
            (role === UserRole.ADMIN ||
              role === UserRole.SUB_ADMIN ||
              role === UserRole.QA) &&
            existingReport.user_id !== user_id
          ) {
            // Keep the original creator's user_id when admin is editing
            newReport.user_id = existingReport.user_id;

            logger.log({
              level: "info",
              label: "POST | Report",
              message: `Admin ${email} editing report ${newReport.report_id} created by user ${existingReport.user_id}`
            });
          }

          const updateResult = await updateReport(
            newReport,
            existingReport,
            user_id,
            email,
            role
          );
          submissionLock.release(submissionHash);
          if (updateResult.success) {
            return res.status(200).send(updateResult.data);
          } else {
            return res.status(500).send({ message: updateResult.message });
          }
        }
        // Handle report creation
        else {
          // Check for missing program ID
          if (
            newReport.program_id === undefined ||
            newReport.program_id === null
          ) {
            submissionLock.release(submissionHash);
            return res.status(400).send({ message: "Missing Program ID" });
          }
          // Check for existing report with same title
          const existingReport = await findExistingReport(
            user_id,
            newReport.program_id,
            newReport.report_title
          );
          if (existingReport) {
            logger.log({
              level: "info",
              label: "POST | Report",
              message: `Found existing report with same title for ${email}, updating instead of creating new`
            });
            // Update existing report instead of creating duplicate
            newReport.report_id = existingReport.report_id;
            const updateResult = await updateReport(
              newReport,
              existingReport,
              user_id,
              email,
              role
            );
            submissionLock.release(submissionHash);
            if (updateResult.success) {
              return res.status(200).send({
                ...updateResult.data,
                report_id: existingReport.report_id
              });
            } else {
              return res.status(500).send({ message: updateResult.message });
            }
          }
          // Create new report
          logger.log({
            level: "info",
            label: "POST | Report",
            message: `Creating new report for ${email}`
          });
          console.log(
            "About to call createReport with notification methods:",
            programAccess.notificationMethods
          );

          const createResult = await createReport(
            newReport,
            email,
            role,
            programAccess.program,
            programAccess.notificationMethods
          );
          // Release the lock and mark as a creation
          submissionLock.release(submissionHash, true);
          if (createResult.success) {
            return res.status(200).send(createResult.data);
          } else {
            return res.status(500).send({ message: createResult.message });
          }
        }
      } catch (err) {
        submissionLock.release(submissionHash);
        throw err; // Re-throw to be caught by outer try/catch
      }
    } catch (error) {
      logger.log({
        level: "error",
        label: "POST | Report",
        message: `Unexpected error processing report for ${email}: ${error}`
      });
      console.error(error);
      return res.status(500).send({
        message: "An unexpected error occurred while processing your report."
      });
    }
  });
};

/**
 * Handles a POST request to update the given reports
 * state
 */
export const postReportState = async (
  req: CTBRequest<
    // Request body
    {
      reason?: string;
      state?:
        | "approved"
        | "rejected"
        | "closed"
        | "request info"
        | "acceptRisk"
        | "nonActionableIssue"
        | "requestFix"
        | "out of scope"
        | "QA Modified";
    },
    // Request query
    unknown,
    // Request params
    {
      report_id?: string;
    }
  >,
  res: Response
) => {
  const { user_id, role } = req.user;
  const { report_id } = req.params;

  // Determine effective user ID and role for sub-roles
  let effectiveUserId = user_id;
  let effectiveRole = role;

  if (
    role === UserRole.DEVELOPER ||
    role === UserRole.BUSINESS_MANAGER ||
    role === UserRole.BUSINESS_ADMINISTRATOR
  ) {
    try {
      const user = await Users.findOne({ where: { user_id } });
      if (!user?.parent_user_id) {
        return res.status(404).json({ message: "Parent business not found" });
      }
      effectiveUserId = user.parent_user_id;
      effectiveRole = UserRole.BUSINESS; // Treat sub-roles as BUSINESS for permissions
    } catch (error) {
      console.error("Error finding user:", error);
      return res.status(500).json({ message: "Internal server error" });
    }
  }

  if (
    role === UserRole.SUB_ADMIN ||
    role === UserRole.ADMIN_MANAGER ||
    role === UserRole.QA
  ) {
    try {
      const user = await Users.findOne({ where: { user_id } });
      if (!user?.parent_user_id) {
        return res.status(404).json({ message: "Parent Admin not found" });
      }
      effectiveUserId = user.parent_user_id;
      effectiveRole = UserRole.ADMIN; // Treat sub-roles as ADMIN for permissions
    } catch (error) {
      console.error("Error finding user:", error);
      return res.status(500).json({ message: "Internal server error" });
    }
  }

  // Ensure required fields are given
  const state = safeString(req.body.state, undefined, [
    "approved",
    "rejected",
    "closed",
    "request info",
    "acceptRisk",
    "nonActionableIssue",
    "requestFix",
    "out of scope",
    "QA Modified"
  ]);
  if (!state) {
    return res.status(400).send({ message: "Missing report state" });
  }

  const reject_reason = safeString(req.body.reason, undefined);

  if (state === "rejected" && !reject_reason) {
    return res.status(400).send({
      message: "Missing reject reason - required when rejecting a report"
    });
  }

  if (effectiveRole === UserRole.ADMIN && state === "closed") {
    return res.status(400).send({
      message: "Admins cannot close reports"
    });
  }

  // Using the "where" selector with effective user ID and role
  const where = await getReportQueryOptions(effectiveUserId, effectiveRole);

  let newStatus: ReportAttributes;

  switch (state) {
    case "approved":
      if (effectiveRole === UserRole.ADMIN) {
        newStatus = {
          triage_status: state,
          state: ReportState.BUSINESS_REVIEW,
          reject_reason
        };
      } else if (effectiveRole === UserRole.BUSINESS) {
        newStatus = {
          state: ReportState.BUSINESS_APPROVED,
          reject_reason
        };
      } else {
        return res.status(403).send({ message: "Unauthorized" });
      }
      break;
    case "rejected":
      if (effectiveRole === UserRole.ADMIN) {
        newStatus = {
          triage_status: state,
          state: ReportState.TRIAGE_REJECTED,
          reject_reason
        };
      } else if (effectiveRole === UserRole.BUSINESS) {
        newStatus = {
          state: ReportState.BUSINESS_REJECTED,
          reject_reason
        };
      } else {
        return res.status(403).send({ message: "Unauthorized" });
      }
      break;
    case "closed":
      if (effectiveRole === UserRole.BUSINESS) {
        newStatus = {
          state: ReportState.CLOSED,
          reject_reason
        };
      } else {
        return res.status(403).send({ message: "Unauthorized" });
      }
      break;
    case "request info":
      if (effectiveRole !== UserRole.BUSINESS) {
        return res.status(403).send({ message: "Unauthorized" });
      }
      newStatus = {
        state: ReportState.BUSINESS_REQUEST_INFO,
        reject_reason
      };
      break;
    case "acceptRisk":
      if (effectiveRole !== UserRole.BUSINESS) {
        return res.status(403).send({ message: "Unauthorized" });
      }
      newStatus = {
        state: ReportState.BUSINESS_ACCEPT_RISK,
        reject_reason
      };
      break;
    case "nonActionableIssue":
      if (effectiveRole !== UserRole.BUSINESS) {
        return res.status(403).send({ message: "Unauthorized" });
      }
      newStatus = {
        state: ReportState.BUSINESS_NON_ACTIONABLE_ISSUE,
        reject_reason
      };
      break;
    case "requestFix":
      if (effectiveRole !== UserRole.BUSINESS) {
        return res.status(403).send({ message: "Unauthorized" });
      }
      newStatus = {
        state: ReportState.AWAITING_FIX,
        reject_reason
      };
      break;
    case "out of scope":
      if (effectiveRole !== UserRole.ADMIN) {
        return res.status(403).send({ message: "Unauthorized" });
      }
      newStatus = {
        state: ReportState.OUT_OF_SCOPE,
        reject_reason
      };
      break;
    case "QA Modified":
      if (effectiveRole !== UserRole.ADMIN) {
        return res.status(403).send({ message: "Unauthorized" });
      }
      newStatus = {
        state: ReportState.QA_MODIFIED,
        reject_reason
      };
      break;
    default:
      return res.status(400).send({ message: "Invalid state" });
  }

  return await Reports.update(newStatus, { where: { ...where, report_id } })
    .then(async data => {
      if (data && data.length > 0 && data[0] > 0) {
        logger.log({
          level: "info",
          label: "POST | Report State",
          message: `Updated report ${report_id} state`
        });

        const report = await Reports.findOne({
          where: { report_id }
        });

        const program = await Programs.findOne({
          where: { program_id: report.program_id }
        });

        const business = await Users.findOne({
          where: { user_id: program.user_id }
        });

        const notificationMethodsArr = program.notification_methods
          ? [...Object.values(program.notification_methods)]
          : [NotificationMethod.CTB];

        // Notify based on effectiveRole
        if (effectiveRole === UserRole.BUSINESS) {
          // Notify RESEARCHER, ADMIN, QA, SUB_ADMIN
          const researcher = await Users.findOne({
            where: { user_id: report.user_id }
          });

          const admins = await Users.findAll({
            where: { role: [UserRole.ADMIN, UserRole.QA, UserRole.SUB_ADMIN] }
          });

          const recipients = [
            researcher.user_id,
            ...admins.map(admin => admin.user_id)
          ];

          // Send notifications
          sendNotification(
            {
              type:
                state === "rejected"
                  ? NotificationType.ReportRejected
                  : NotificationType.ReportApproved,
              payload: report_id.toString(),
              message: `${report.report_title} has been ${state} by ${business.username}`
            },
            recipients,
            notificationMethodsArr,
            `${program.slack_channel_link}`,
            `The report "${report.report_title}" has been ${state} by ${business.username}.`
          );

          // Send Slack message
          if (program.slack_channel_link) {
            await notifySlack(
              program.slack_channel_link as string,
              `📋 Report Status Update\nTitle: ${
                report.report_title
              }\nStatus: ${state}\nUpdated by: ${business.username}${
                reject_reason ? `\nReason: ${reject_reason}` : ""
              }`
            );
          }

          // Send detailed email to RESEARCHER and ADMIN, QA, SUB_ADMIN
          const emailRecipients = [
            researcher.email,
            ...admins.map(admin => admin.email)
          ];

          const emailSubject = `Report ${state}: ${report.report_title}`;
          const emailBody = `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en">
<head>
    <title>Email Verification</title>
    <!-- Meta Tags -->
    <meta charset="UTF-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="x-apple-disable-message-reformatting" content="" />
    <meta content="target-densitydpi=device-dpi" name="viewport" />
    <meta content="true" name="HandheldFriendly" />
    <meta content="width=device-width" name="viewport" />
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no, url=no" />

    <!-- Base Styles -->
    <style type="text/css">
        /* Reset Styles */
        table {
            border-collapse: separate;
            table-layout: fixed;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        table td {
            border-collapse: collapse;
        }
        
        /* External Class Styles */
        .ExternalClass {
            width: 100%;
        }
        .ExternalClass,
        .ExternalClass p,
        .ExternalClass span,
        .ExternalClass font,
        .ExternalClass td,
        .ExternalClass div {
            line-height: 100%;
        }

        /* Base Element Styles */
        body, a, li, p, h1, h2, h3 {
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
        }
        html {
            -webkit-text-size-adjust: none !important;
        }
        body, #innerTable {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Image Styles */
        #innerTable img+div {
            display: none !important;
        }
        img {
            margin: 0;
            padding: 0;
            -ms-interpolation-mode: bicubic;
        }

        /* Typography Styles */
        h1, h2, h3, p, a {
            line-height: inherit;
            overflow-wrap: normal;
            white-space: normal;
            word-break: break-word;
        }
        a {
            text-decoration: none;
        }
        h1, h2, h3, p {
            min-width: 100% !important;
            width: 100% !important;
            max-width: 100% !important;
            display: inline-block !important;
            border: 0;
            padding: 0;
            margin: 0;
        }

        /* OTP Box Styles */
        .otp-box {
            display: inline-block;
            background-color: #F5F5F5;
            width: 50px;
            height: 50px;
            margin: 0 5px;
            border-radius: 8px;
            text-align: center;
            vertical-align: middle;
        }
        .otp-number {
            font-family: Inter, BlinkMacSystemFont, Segoe UI, Helvetica Neue, Arial, sans-serif;
            font-size: 24px;
            font-weight: 700;
            color: #111111;
            line-height: 50px;
        }
    </style>

    <!-- Responsive Styles -->
    <style type="text/css">
        @media (min-width: 481px) {
            .hd { display: none !important; }
        }
        @media (max-width: 480px) {
            .hm { display: none !important; }
            .t67, .t72 { 
                mso-line-height-alt: 0px !important;
                line-height: 0 !important;
                display: none !important;
            }
            .t68 { padding-top: 43px !important; }
            .t70 { 
                border: 0 !important;
                border-radius: 0 !important;
            }
            .t65, .t9 { width: 320px !important; }
            .t63 { padding: 40px 30px !important; }
            .t59 { padding-bottom: 36px !important; }
            .t32, .t61 { width: 260px !important; }
            .t54 { text-align: center !important; }
            .t43, .t45, .t49, .t51 { display: revert !important; }
            .t47, .t53 {
                vertical-align: middle !important;
                width: 74px !important;
            }
            .t40 { width: 224px !important; }
        }
    </style>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@500;600;700&family=Lato:wght@400;700&family=Inter+Tight:wght@700&display=swap" rel="stylesheet" type="text/css" />
</head>

<body id="body" class="t75" style="min-width:100%; margin:0; padding:0; background-color:#F9F9F9;">
    <div class="t74" style="background-color:#F9F9F9;">
        <!-- Main Container -->
        <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0" align="center">
            <tr>
                <td class="t73" style="font-size:0; line-height:0; background-color:#F9F9F9;" valign="top" align="center">
                    <!-- Inner Container -->
                    <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0" align="center" id="innerTable">
                        <!-- Spacer -->
                        <tr>
                            <td>
                                <div class="t67" style="mso-line-height-rule:exactly; line-height:70px; font-size:1px;">&nbsp;</div>
                            </td>
                        </tr>
                        
                        <!-- Content Card -->
                        <tr>
                            <td align="center">
                                <table class="t71" role="presentation" cellpadding="0" cellspacing="0" style="margin:0 auto;">
                                    <tr>
                                        <td class="t70" style="background-color:#FFFFFF; border:1px solid #CECECE; width:400px; border-radius:20px;">
                                            <!-- Card Content -->
                                            <table class="t69" role="presentation" cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <td class="t68" style="padding:50px 40px 40px 40px;">
                                                        <!-- Logo -->
                                                        <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
                                                            <tr>
                                                                <td align="center">
                                                                    <img width="158" height="53" alt="Logo" src="https://c8270c56-7594-4100-9b34-7589297588fa.b-cdn.net/e/bac89f4c-a951-463b-81dd-86c0e12c3427/28695d5f-3f29-41fa-a53d-2f93062026e6.jpeg"/>
                                                                </td>
                                                            </tr>
                                                        </table>
   
                                                            <!-- Title -->
                                                     <!-- Title -->
                                                        <div style="margin-top:80px; text-align:center; margin-bottom: 40px;">
                                                            <h1 style="font-family:Inter,sans-serif; font-size:24px; font-weight:600; color:#111111;">
                                                                Report Status Update
                                                            </h1>
                                                        </div>

                                                        <!-- Status Box -->
                                                        <div style="margin:30px 0; text-align:center; background-color:#F5F5F5; padding:20px; border-radius:8px;">
                                                           <p style="font-family:Inter,sans-serif; font-size:16px; color:#424040; line-height:24px; margin:0;">
  The report titled <strong>"${report.report_title}"</strong> has been <strong style="color:#017BFB;">${state}</strong> for the program <strong>"${program.program_title}"</strong>.
</p>

                                                        </div>

                                                        <!-- Action Button -->
                                                        <div style="margin:40px 0; text-align:center;">
                                                            <a href="${process.env.FRONTEND_BASE_URL}/dashboard/report/${report.report_id}" 
                                                               style="background-color:#017BFB; color:#FFFFFF; padding:15px 30px; border-radius:8px; font-family:Inter,sans-serif; font-size:16px; font-weight:600; text-decoration:none; display:inline-block;">
                                                                View Report Details
                                                            </a>
                                                        </div>

                                                        <!-- Thank You Section -->
                                                        <div style="margin-top:45px; text-align:center;">
                                                            <h2 style="font-family:'Inter Tight',sans-serif; font-size:28px; color:#017BFB;">
                                                                Thank You!
                                                            </h2>
                                                            <p style="font-family:Lato,sans-serif; font-size:20px; color:#333333; margin-top:50px;">Team</p>
                                                            <p style="font-family:Lato,sans-serif; font-size:20px; color:#333333; margin-top:30px;">
                                                                <strong style="color:#017BFB; margin-top: 100px; padding-top: 100px;">Capture The Bug</strong>
                                                            </p>
                                                    </td>
                                                </tr>
                                                <tr>
        <td align="center" style="">
            <a href="https://nz.linkedin.com/company/capture-the-bug" target="_blank">
                <img src="https://img.icons8.com/?size=100&id=13930&format=png&color=000000" alt="LinkedIn" width="50" height="50">
            </a>
            <a href="https://x.com/Capturethebugs" target="_blank">
                <img src="https://img.icons8.com/?size=100&id=phOKFKYpe00C&format=png&color=000000" alt="Twitter" width="45" height="45">
            </a>
        </td>
    </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>`;

          emailRecipients.forEach(recipient => {
            sendEmail(recipient, emailSubject, emailBody);
          });
        } else if (effectiveRole === UserRole.ADMIN) {
          // Notify BUSINESS and RESEARCHER
          const researcher = await Users.findOne({
            where: { user_id: report.user_id }
          });

          const business = await Users.findOne({
            where: { user_id: program.user_id }
          });

          const recipients = [researcher.user_id, business.user_id];

          // Send notifications
          sendNotification(
            {
              type:
                state === "rejected"
                  ? NotificationType.ReportRejected
                  : NotificationType.ReportApproved,
              payload: report_id.toString(),
              message: `${report.report_title} has been ${state} by the CTB team`
            },
            recipients,
            notificationMethodsArr,
            `${program.slack_channel_link}`,
            `The report "${report.report_title}" has been ${state} by the CTB team.`
          );

          // Send Slack message
          if (program.slack_channel_link) {
            await notifySlack(
              program.slack_channel_link as string,
              `📋 Report Status Update\nTitle: ${
                report.report_title
              }\nStatus: ${state}\nUpdated by: CTB Team${
                reject_reason ? `\nReason: ${reject_reason}` : ""
              }`
            );
          }

          // Send detailed email to BUSINESS and RESEARCHER
          const emailRecipients = [researcher.email, business.email];

          const emailSubject = `Report ${state}: ${report.report_title}`;
          const emailBody = `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en">
<head>
    <title>Email Verification</title>
    <!-- Meta Tags -->
    <meta charset="UTF-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="x-apple-disable-message-reformatting" content="" />
    <meta content="target-densitydpi=device-dpi" name="viewport" />
    <meta content="true" name="HandheldFriendly" />
    <meta content="width=device-width" name="viewport" />
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no, url=no" />

    <!-- Base Styles -->
    <style type="text/css">
        /* Reset Styles */
        table {
            border-collapse: separate;
            table-layout: fixed;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        table td {
            border-collapse: collapse;
        }
        
        /* External Class Styles */
        .ExternalClass {
            width: 100%;
        }
        .ExternalClass,
        .ExternalClass p,
        .ExternalClass span,
        .ExternalClass font,
        .ExternalClass td,
        .ExternalClass div {
            line-height: 100%;
        }

        /* Base Element Styles */
        body, a, li, p, h1, h2, h3 {
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
        }
        html {
            -webkit-text-size-adjust: none !important;
        }
        body, #innerTable {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Image Styles */
        #innerTable img+div {
            display: none !important;
        }
        img {
            margin: 0;
            padding: 0;
            -ms-interpolation-mode: bicubic;
        }

        /* Typography Styles */
        h1, h2, h3, p, a {
            line-height: inherit;
            overflow-wrap: normal;
            white-space: normal;
            word-break: break-word;
        }
        a {
            text-decoration: none;
        }
        h1, h2, h3, p {
            min-width: 100% !important;
            width: 100% !important;
            max-width: 100% !important;
            display: inline-block !important;
            border: 0;
            padding: 0;
            margin: 0;
        }

        /* OTP Box Styles */
        .otp-box {
            display: inline-block;
            background-color: #F5F5F5;
            width: 50px;
            height: 50px;
            margin: 0 5px;
            border-radius: 8px;
            text-align: center;
            vertical-align: middle;
        }
        .otp-number {
            font-family: Inter, BlinkMacSystemFont, Segoe UI, Helvetica Neue, Arial, sans-serif;
            font-size: 24px;
            font-weight: 700;
            color: #111111;
            line-height: 50px;
        }
    </style>

    <!-- Responsive Styles -->
    <style type="text/css">
        @media (min-width: 481px) {
            .hd { display: none !important; }
        }
        @media (max-width: 480px) {
            .hm { display: none !important; }
            .t67, .t72 { 
                mso-line-height-alt: 0px !important;
                line-height: 0 !important;
                display: none !important;
            }
            .t68 { padding-top: 43px !important; }
            .t70 { 
                border: 0 !important;
                border-radius: 0 !important;
            }
            .t65, .t9 { width: 320px !important; }
            .t63 { padding: 40px 30px !important; }
            .t59 { padding-bottom: 36px !important; }
            .t32, .t61 { width: 260px !important; }
            .t54 { text-align: center !important; }
            .t43, .t45, .t49, .t51 { display: revert !important; }
            .t47, .t53 {
                vertical-align: middle !important;
                width: 74px !important;
            }
            .t40 { width: 224px !important; }
        }
    </style>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@500;600;700&family=Lato:wght@400;700&family=Inter+Tight:wght@700&display=swap" rel="stylesheet" type="text/css" />
</head>

<body id="body" class="t75" style="min-width:100%; margin:0; padding:0; background-color:#F9F9F9;">
    <div class="t74" style="background-color:#F9F9F9;">
        <!-- Main Container -->
        <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0" align="center">
            <tr>
                <td class="t73" style="font-size:0; line-height:0; background-color:#F9F9F9;" valign="top" align="center">
                    <!-- Inner Container -->
                    <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0" align="center" id="innerTable">
                        <!-- Spacer -->
                        <tr>
                            <td>
                                <div class="t67" style="mso-line-height-rule:exactly; line-height:70px; font-size:1px;">&nbsp;</div>
                            </td>
                        </tr>
                        
                        <!-- Content Card -->
                        <tr>
                            <td align="center">
                                <table class="t71" role="presentation" cellpadding="0" cellspacing="0" style="margin:0 auto;">
                                    <tr>
                                        <td class="t70" style="background-color:#FFFFFF; border:1px solid #CECECE; width:400px; border-radius:20px;">
                                            <!-- Card Content -->
                                            <table class="t69" role="presentation" cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <td class="t68" style="padding:50px 40px 40px 40px;">
                                                        <!-- Logo -->
                                                        <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
                                                            <tr>
                                                                <td align="center">
                                                                    <img width="158" height="53" alt="Logo" src="https://c8270c56-7594-4100-9b34-7589297588fa.b-cdn.net/e/bac89f4c-a951-463b-81dd-86c0e12c3427/28695d5f-3f29-41fa-a53d-2f93062026e6.jpeg"/>
                                                                </td>
                                                            </tr>
                                                        </table>
   
                                                            <!-- Title -->
                                                     <!-- Title -->
                                                        <div style="margin-top:80px; text-align:center; margin-bottom: 40px;">
                                                            <h1 style="font-family:Inter,sans-serif; font-size:24px; font-weight:600; color:#111111;">
                                                                Report Status Update
                                                            </h1>
                                                        </div>

                                                        <!-- Status Box -->
                                                        <div style="margin:30px 0; text-align:center; background-color:#F5F5F5; padding:20px; border-radius:8px;">
                                                          <p style="font-family:Inter,sans-serif; font-size:16px; color:#424040; line-height:24px; margin:0;">
  The report titled <strong>"${report.report_title}"</strong> has been <strong style="color:#017BFB;">${state}</strong> for the program <strong>"${program.program_title}"</strong>.
</p>

                                                        </div>

                                                        <!-- Action Button -->
                                                        <div style="margin:40px 0; text-align:center;">
                                                            <a href="${process.env.FRONTEND_BASE_URL}/dashboard/report/${report.report_id}" 
                                                               style="background-color:#017BFB; color:#FFFFFF; padding:15px 30px; border-radius:8px; font-family:Inter,sans-serif; font-size:16px; font-weight:600; text-decoration:none; display:inline-block;">
                                                                View Report Details
                                                            </a>
                                                        </div>

                                                        <!-- Thank You Section -->
                                                        <div style="margin-top:45px; text-align:center;">
                                                            <h2 style="font-family:'Inter Tight',sans-serif; font-size:28px; color:#017BFB;">
                                                                Thank You!
                                                            </h2>
                                                            <p style="font-family:Lato,sans-serif; font-size:20px; color:#333333; margin-top:50px;">Team</p>
                                                            <p style="font-family:Lato,sans-serif; font-size:20px; color:#333333; margin-top:30px;">
                                                                <strong style="color:#017BFB; margin-top: 100px; padding-top: 100px;">Capture The Bug</strong>
                                                            </p>
                                                    </td>
                                                </tr>
                                                <tr>
        <td align="center" style="">
            <a href="https://nz.linkedin.com/company/capture-the-bug" target="_blank">
                <img src="https://img.icons8.com/?size=100&id=13930&format=png&color=000000" alt="LinkedIn" width="50" height="50">
            </a>
            <a href="https://x.com/Capturethebugs" target="_blank">
                <img src="https://img.icons8.com/?size=100&id=phOKFKYpe00C&format=png&color=000000" alt="Twitter" width="45" height="45">
            </a>
        </td>
    </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>`;

          emailRecipients.forEach(recipient => {
            sendEmail(recipient, emailSubject, emailBody);
          });
        }

        const reportTitle = report ? report.report_title : "Unknown Report";
        await ActivityLog.create({
          user_id,
          role,
          action: `report "${reportTitle}(${report_id})" status has been changed to ${state}`,
          module: "Report",
          created_at: new Date()
        });

        // Create Jira issue when admin approves a report
        if (state === "approved" && effectiveRole === UserRole.ADMIN) {
          try {
            logger.log({
              level: "info",
              label: "POST | Report State | Jira Integration",
              message: `Admin approved report ${report_id}, checking Jira configuration`
            });

            // Check if Jira configuration is valid
            if (
              program.jira_url &&
              program.jira_email &&
              program.jira_api_token &&
              program.jira_project_key
            ) {
              // Create Jira config object
              const jiraConfig = {
                url: String(program.jira_url),
                email: String(program.jira_email),
                api_token: String(program.jira_api_token),
                project_key: String(program.jira_project_key)
              };

              // Create Jira issue
              const jiraResult = await createJiraIssue(jiraConfig, {
                report_id: report.report_id,
                report_title: report.report_title,
                description: report.description,
                category: report.category,
                severity_category: report.severity_category,
                scope: report.scope,
                program_title: program.program_title
              });

              if (jiraResult.success) {
                // Store Jira issue key in the dedicated jira_issue_key field
                await Reports.update(
                  {
                    jira_issue_key: jiraResult.issueKey
                  },
                  {
                    where: { report_id: report.report_id }
                  }
                );

                logger.log({
                  level: "info",
                  label: "POST | Report State | Jira Integration",
                  message: `Successfully created Jira issue ${jiraResult.issueKey} for approved report ${report_id} and stored in jira_issue_key field`
                });
              } else {
                logger.log({
                  level: "error",
                  label: "POST | Report State | Jira Integration",
                  message: `Failed to create Jira issue for approved report ${report_id}: ${jiraResult.message}`
                });
              }
            } else {
              logger.log({
                level: "info",
                label: "POST | Report State | Jira Integration",
                message: `Jira configuration not complete for program ${program.program_id}, skipping issue creation`
              });
            }
          } catch (error) {
            logger.log({
              level: "error",
              label: "POST | Report State | Jira Integration",
              message: `Error creating Jira issue for approved report ${report_id}: ${error.message}`
            });
            // Continue with approval process even if Jira integration fails
          }
        }

        return res
          .status(200)
          .send({ message: "Successfully updated report state", newStatus });
      }

      return res.status(404).send({ message: "No report to update" });
    })
    .catch(err => {
      logger.log({
        level: "error",
        label: "POST | Report State",
        message: `Failed to update report ${report_id} state: ${err}`
      });
      console.log(err);

      return res.status(500).send({ message: "Failed to update report state" });
    });
};

/**
 * Submits the given report
 */
export const submitReport = async (
  req: CTBRequest<{
    report_id: number;
  }>,
  res: Response
) => {
  const { user_id, role } = req.user;
  const report_id = safeInt(req.params.report_id, undefined, 0);

  if (report_id === undefined) {
    return res.status(400).send({ message: "Missing report ID" });
  }

  // Get the current date and time
  const date = new Date();
  const where = await getReportQueryOptions(user_id, role);

  // Check if the program is triage-opted
  let triageOpted = false;

  // Only the report owner can find the report
  const report = await Reports.findOne({ where: { report_id, user_id } });
  const program = await Programs.findOne({
    where: { program_id: report.program_id }
  });

  triageOpted = program.triage_service_opted !== TRIAGE_SERVICE_OPTED_FALSE;
  const notificationMethodsArr = program.notification_methods
    ? [...Object.values(program.notification_methods)]
    : [NotificationMethod.CTB];

  // Check if Jira integration is enabled by verifying all credentials exist
  const jiraEnabled = Boolean(
    program?.jira_url &&
      program?.jira_email &&
      program?.jira_api_token &&
      program?.jira_project_key
  );

  logger.log({
    level: "info",
    label: "POST | Submit Report | Jira Integration",
    message: `Jira integration ${
      jiraEnabled ? "enabled" : "disabled"
    } for report ${report.report_id}`
  });

  // Setup the new report details - going straight to be reviewed by
  // the business if the triage service is not opted
  const newDetails: ReportAttributes = {
    submitted_date: date,
    state: triageOpted ? ReportState.UNDER_REVIEW : ReportState.BUSINESS_REVIEW,
    triage_status: triageOpted
      ? ReportState.UNDER_REVIEW
      : TRIAGE_SERVICE_NOT_OPTED
  };

  return await Reports.update(newDetails, {
    where: {
      report_id,
      state: null,
      is_delete: false,
      ...where
    }
  })
    .then(async data => {
      if (data && data[0] && data[0] > 0) {
        logger.log({
          level: "info",
          label: "POST | Submit Report",
          message: `Submitted report ${report_id}`
        });

        // Jira integration disabled during report submission
        // Jira issues will be created only after admin approval
        if (false) {
          try {
            logger.log({
              level: "info",
              label: "POST | Submit Report | Jira Integration",
              message: `Attempting to create Jira issue for report ${report_id}`
            });

            // Check if Jira configuration is valid
            if (
              program.jira_url &&
              program.jira_email &&
              program.jira_api_token &&
              program.jira_project_key
            ) {
              // Create Jira config object
              const jiraConfig = {
                url: String(program.jira_url),
                email: String(program.jira_email),
                api_token: String(program.jira_api_token),
                project_key: String(program.jira_project_key)
              };

              // Fetch additional details for the report to include in the Jira issue
              const reportDetails = await Reports.findOne({
                where: { report_id },
                attributes: [
                  "report_id",
                  "report_title",
                  "description",
                  "category",
                  "severity_category",
                  "scope"
                ]
              });

              // Create Jira issue
              const jiraResult = await createJiraIssue(jiraConfig, {
                report_id: reportDetails.report_id,
                report_title: reportDetails.report_title,
                description: reportDetails.description,
                category: reportDetails.category,
                severity_category: reportDetails.severity_category,
                scope: reportDetails.scope,
                program_title: program.program_title
              });

              if (jiraResult.success) {
                logger.log({
                  level: "info",
                  label: "POST | Submit Report | Jira Integration",
                  message: `Successfully created Jira issue ${jiraResult.issueKey} for report ${report_id}`
                });
              } else {
                logger.log({
                  level: "error",
                  label: "POST | Submit Report | Jira Integration",
                  message: `Failed to create Jira issue for report ${report_id}: ${jiraResult.message}`
                });
              }
            } else {
              logger.log({
                level: "error",
                label: "POST | Submit Report | Jira Integration",
                message: `Invalid Jira configuration for program ${program.program_id}`
              });
            }
          } catch (error) {
            logger.log({
              level: "error",
              label: "POST | Submit Report | Jira Integration",
              message: `Error creating Jira issue for report ${report_id}: ${error.message}`
            });
            // Continue with report submission even if Jira integration fails
          }
        }

        // Notify the business that a new report has been submitted if they are not triaged opted
        // otherwise admin

        if (triageOpted) {
          // Notify Admin
          const adminIDs = (
            await Users.findAll({
              where: { role: UserRole.ADMIN }
            })
          ).map(user => user.user_id);

          sendNotification(
            {
              type: NotificationType.ReportInfo,
              payload: report_id.toString(),
              message: `${report.report_title} waiting review for program ${program.program_title}`
            },
            adminIDs,
            notificationMethodsArr,
            `${program.slack_channel_link}`,
            `The report is currently under review by the CTB team.\n Report ID: ${report.report_id}, Report Title: ${report.report_title}`
          );

          if (program.slack_channel_link) {
            await notifySlack(
              program.slack_channel_link as string,
              `📋 Report Submitted for Review\nTitle: ${report.report_title}\nStatus: Under CTB Review\nReport ID: ${report.report_id}`
            );
          }
        } else {
          // Notify Business
          sendNotification(
            {
              type: NotificationType.ReportInfo,
              payload: report_id.toString(),
              message: `${report.report_title} waiting review for program ${program.program_title}`
            },
            [program.user_id],
            notificationMethodsArr,
            `${program.slack_channel_link}`,
            `The report is currently under review by ${program.program_title} team.\n Report ID: ${report.report_id}, Report Title: ${report.report_title}`
          );

          if (program.slack_channel_link) {
            await notifySlack(
              program.slack_channel_link as string,
              `📋 Report Submitted for Review\nTitle: ${report.report_title}\nStatus: Under Business Review\nReport ID: ${report.report_id}`
            );
          }
        }

        return res
          .status(200)
          .send({ message: "Successfully submitted report." });
      }

      return res.status(404).send({ message: "No report found to submit" });
    })
    .catch(err => {
      logger.log({
        level: "error",
        label: "POST | Submit Report",
        message: `Failed to submit report: ${err}`
      });
      console.error(err);

      return res.status(500).send({ message: "Failed to submit report." });
    });
};

/**
 * Handles a DELETE request to delete the given
 * report
 */
export const deleteReport = async (
  req: CTBParamsRequest<{ report_id?: string }>,
  res: Response
) => {
  const { user_id, email, role } = req.user;
  const report_id = safeInt(req.params.report_id, undefined, 0);

  if (report_id === undefined)
    return res.status(400).send({
      message: "Cannot delete report - ID not specified"
    });

  logger.log({
    level: "info",
    label: "DELETE | Report",
    message: `${email} requested to delete report with ID=${report_id}`
  });

  await ActivityLog.create({
    user_id,
    role,
    action: `${email} requested to delete report with ID=${report_id}`,
    module: "Report",
    created_at: new Date()
  });

  try {
    // First, check if the report exists
    const report = await Reports.findOne({
      where: { report_id }
    });

    if (!report) {
      logger.log({
        level: "error",
        label: "DELETE | Report",
        message: `Report with ID=${report_id} not found`
      });
      return res.status(404).send({ message: "Report not found" });
    }

    // Determine if user has permission to delete this report
    const canDelete =
      role === UserRole.ADMIN ||
      role === UserRole.SUB_ADMIN ||
      report.user_id === user_id;

    if (!canDelete) {
      logger.log({
        level: "error",
        label: "DELETE | Report",
        message: `User ${email} attempted to delete report ID=${report_id} without permission`
      });
      return res
        .status(403)
        .send({ message: "You don't have permission to delete this report" });
    }

    // For admins, we don't need to check user_id
    // For researchers, we only allow them to delete their own reports
    const whereClause =
      role === UserRole.ADMIN || role === UserRole.SUB_ADMIN
        ? { report_id, state: UNDER_REVIEW }
        : { user_id, report_id, state: UNDER_REVIEW };

    const updateResult = await Reports.update(
      { is_delete: true, state: DELETED, triage_status: DELETED },
      { where: whereClause }
    );

    if (updateResult[0]) {
      // Log who deleted the report (especially important for admin actions)
      const actionDescription =
        (role === UserRole.ADMIN || role === UserRole.SUB_ADMIN) &&
        report.user_id !== user_id
          ? `${email} deleted report with ID=${report_id} originally created by user ID=${report.user_id}`
          : `Report with ID=${report_id} successfully deleted by ${email}`;

      logger.log({
        level: "info",
        label: "DELETE | Report",
        message: actionDescription
      });

      // Add another activity log entry for the actual deletion
      await ActivityLog.create({
        user_id,
        role,
        action: actionDescription,
        module: "Report",
        created_at: new Date()
      });

      return res.status(200).send({ message: "Report successfully deleted!" });
    } else {
      logger.log({
        level: "error",
        label: "DELETE | Report",
        message: `Report with ID=${report_id} was unable to be deleted or is not in UNDER_REVIEW state`
      });
      return res.status(404).send({
        message:
          "Failed to delete report. It may have already been processed or doesn't exist."
      });
    }
  } catch (error) {
    logger.log({
      level: "error",
      label: "DELETE | Report",
      message: `Error deleting report with ID=${report_id}: ${error}`
    });
    return res.status(500).send({
      message: "An error occurred while trying to delete the report."
    });
  }
};

const getVulnerabilitiesByTarget = async (
  programs: number[],
  userId: number
): Promise<JSON[]> => {
  return Reports.findAll({
    group: "severity_category",
    attributes: [
      "severity_category",
      [Sequelize.literal("COUNT (DISTINCT program_id)"), "ProgramCount"]
    ],
    where: {
      program_id: {
        [Op.in]: programs
      },
      triage_status: "approved"
    },
    raw: true
  })
    .then(data => {
      logger.log({
        level: "info",
        label: "Report | Get Severity Category Program Count",
        message: `Severity Category Program Count data successfully retrieved for business with id ${userId}`
      });
      return data;
    })
    .catch(err => {
      logger.log({
        level: "error",
        label: "Report | Get Severity Category Program Count",
        message: `Error retrieving Severity Category Program Count data for business with id ${userId}`,
        additional_data: err
      });
      return null;
    });
};

/**
 * Retrieves the count of report states for all programs of the passed in business and returns it as a JSON object
 * i.e. count of Low, Medium, High and Critical
 */
const getReportSeverityCategorySummaryData = async (
  programs: number[],
  userId: number
): Promise<JSON> => {
  return Reports.findAll({
    attributes: [
      [
        Sequelize.literal(
          "COUNT(CASE WHEN severity_category = 'LOW' then 1 ELSE NULL END)"
        ),
        "Low"
      ],
      [
        Sequelize.literal(
          "COUNT(CASE WHEN severity_category = 'MEDIUM' then 1 ELSE NULL END)"
        ),
        "Medium"
      ],
      [
        Sequelize.literal(
          "COUNT(CASE WHEN severity_category = 'HIGH' then 1 ELSE NULL END)"
        ),
        "High"
      ],
      [
        Sequelize.literal(
          "COUNT(CASE WHEN severity_category = 'CRITICAL' then 1 ELSE NULL END)"
        ),
        "Critical"
      ]
    ],
    where: {
      program_id: {
        [Op.in]: programs
      },
      triage_status: "approved"
    },
    raw: true
  })
    .then(data => {
      logger.log({
        level: "info",
        label: "Report | Get Report Severity Summary Data",
        message: `Report Severity Summary data successfully retrieved for business with id ${userId}`
      });
      return data[0];
    })
    .catch(err => {
      logger.log({
        level: "error",
        label: "Report | Get Report Severity Summary Data",
        message: `Error retrieving Report Severity Summary data for business with id ${userId}`,
        additional_data: err
      });
      return null;
    });
};

/**
 * Retrieves the names and severities of all active reports for all programs of the passed in business and returns it as an array of JSON objects
 * i.e. [{report_title: '', severity_category: '', severity: ''}]
 */
const getReportTitlesAndSeverityData = async (
  programs: number[],
  userId: number
): Promise<JSON[]> => {
  return Reports.findAll({
    attributes: ["report_title", "severity_category", "severity"],
    where: {
      program_id: {
        [Op.in]: programs
      },
      triage_status: "approved"
    },
    raw: true
  })
    .then(data => {
      logger.log({
        level: "info",
        label: "Report | Get Reports and Severity Data",
        message: `Reports and Severity data successfully retrieved for business with id ${userId}`
      });
      const orderedArray = getReportsOrderedBySeverityCategory(data);
      return orderedArray;
    })
    .catch(err => {
      logger.log({
        level: "error",
        label: "Report | Get Reports and Severity Data",
        message: `Error retrieving Reports and Severity data for business with id ${userId}`,
        additional_data: err
      });
      return null;
    });
};

/**
 * Retrieves the count of the severity categories of all the reports for each programs of the passed in business and returns it as an array of JSON objects
 * i.e. [{ program_title: 'TestProgram', Low: 0, Medium: 0, High: 0, Critical: 1 }]
 */
const getReportSeverityCategorySummaryByProgram = async (
  programs: number[],
  userId: number
): Promise<JSON[]> => {
  return Reports.findAll({
    include: [
      { model: Programs, attributes: [] } //perform the join query without retrieving the model information
    ],
    group: "report.program_id",
    attributes: ["program.program_title"],
    where: {
      program_id: {
        [Op.in]: programs
      },
      triage_status: "approved"
    },
    raw: true
  })
    .then(data => {
      logger.log({
        level: "info",
        label: "Report | Get Report Severity Category Summary By Program",
        message: `Report Severity Category Summary By Program data successfully retrieved for business with id ${userId}`
      });
      return data;
    })
    .catch(err => {
      logger.log({
        level: "error",
        label: "Report | Get Report Severity Category Summary By Program",
        message: `Error retrieving Report Severity Category Summary By Program data for business with id ${userId}`,
        additional_data: err
      });
      return null;
    });
};

/**
 * Retrieves the count of the number of active reports with each severity score and returns it as an array of JSON objects
 * i.e. [ { severity: '5', 'Total Reports': 1 } ]
 */
const getReportCountBySeverityScore = async (
  programs: number[],
  userId: number
): Promise<JSON[]> => {
  return Reports.findAll({
    group: "severity_category",
    attributes: [
      "severity_category",
      [Sequelize.literal("COUNT (*)"), "ReportCount"]
    ],
    where: {
      program_id: {
        [Op.in]: programs
      },
      triage_status: "approved"
    },
    raw: true
  })
    .then(data => {
      logger.log({
        level: "info",
        label: "Report | Get Report State Summary Data",
        message: `Report State Summary data successfully retrieved for business with id ${userId}`
      });
      return data;
    })
    .catch(err => {
      logger.log({
        level: "error",
        label: "Report | Get Report State Summary Data",
        message: `Error retrieving Report State Summary data for business with id ${userId}`,
        additional_data: err
      });
      return null;
    });
};

/**
 * Retrieves the specified number of reports (NUM_REPORTS_IN_SUMMARY_PDF) with the highest severity scores for the passed in business
 */
const getTopCriticalReports = async (
  programs: number[],
  userId: number
): Promise<JSON[]> => {
  return Reports.findAll({
    include: [
      { model: Programs, attributes: [] } //perform the join query without retrieving the model information
    ],
    attributes: [
      "program.program_title",
      "report_title",
      "scope",
      "category",
      "severity",
      "severity_category",
      "description",
      "instructions",
      "impact",
      "fix",
      "additional_info"
    ],
    where: {
      program_id: {
        [Op.in]: programs
      },
      triage_status: "approved"
    },
    raw: true
  })
    .then(data => {
      logger.log({
        level: "info",
        label: "Report | Get Top Critical Reports",
        message: `Top Critical Reports successfully retrieved for business with id ${userId}`
      });
      const ordered_data = getReportsOrderedBySeverityCategory(data);
      return ordered_data;
    })
    .catch(err => {
      logger.log({
        level: "error",
        label: "Report | Get Top Critical Reports",
        message: `Error retrieving Top Critical Reports for business with id ${userId}`,
        additional_data: err
      });
      return null;
    });
};

/**
 * Retrieves the name of the business
 */
const getCompanyName = async (userId: number): Promise<JSON> => {
  return Users.findAll({
    attributes: ["username"],
    where: {
      user_id: userId
    },
    raw: true
  })
    .then(data => {
      logger.log({
        level: "info",
        label: "Report | Get Company Name",
        message: `Company Name successfully retrieved for business with id ${userId}`
      });
      return data[0];
    })
    .catch(err => {
      logger.log({
        level: "error",
        label: "Report | Get Company Name",
        message: `Error retrieving Company Name for business with id ${userId}`,
        additional_data: err
      });
      return null;
    });
};

/**
 * Retrieves all the programs for a business and then retrieves all the data required to populate the business summary report pdf
 * and returns it as an array of JSON objects, if any of the data isn't able to be retrieved returns null
 */
const getBusinessSummaryData = async (
  res: Response,
  userId: number,
  role: UserRole,
  program_id: number
): Promise<JSON> => {
  const programs =
    role === UserRole.BUSINESS
      ? await Programs.findAll({
          where: { user_id: userId, program_id }
        })
      : [];
  if (!programs.length) return null;
  const programIds: number[] = programs?.map(program => program.program_id);

  const companyName: JSON = await getCompanyName(userId);
  const company: string = companyName["username"];

  const reportSeveritySummaryData: JSON =
    await getReportSeverityCategorySummaryData(programIds, userId);
  const reportsAndSeverities: JSON[] = await getReportTitlesAndSeverityData(
    programIds,
    userId
  );
  const scopeTargets: any = programs[0]["targets"];
  const reportCountBySeverityScore: JSON[] =
    await getReportCountBySeverityScore(programIds, userId);
  const vulnerabilitiesByTarget = await getVulnerabilitiesByTarget(
    programIds,
    userId
  );

  const topCriticalReports: JSON[] = await getTopCriticalReports(
    programIds,
    userId
  );

  const programData = await Program.findAll({
    where: { program_id: { [Op.in]: programIds } }
  });

  if (
    !company ||
    !reportSeveritySummaryData ||
    !reportsAndSeverities ||
    !scopeTargets ||
    !reportCountBySeverityScore ||
    !topCriticalReports ||
    !vulnerabilitiesByTarget ||
    !programData
  ) {
    return null;
  }

  const data: JSON = <JSON>(<unknown>{
    severitySummary: reportSeveritySummaryData,
    vulnSummaries: reportsAndSeverities,
    scopeTargets: scopeTargets,
    vulnsByCvssScore: reportCountBySeverityScore,
    topCriticalReports: topCriticalReports,
    companyName: company,
    vulnerabilitiesByTarget: vulnerabilitiesByTarget,
    programData: programData
  });
  return data;
};

/**
 * Retrieves the data needed to create a summary report for the business, dynamically creates the pdf with
 * the retrieved data and returns it as a base64 string
 */
export const getBusinessSummaryReport = async (
  req: CTBQueryRequest<{
    user_id?: string; // admin only, can supply business ID
    program_id?: number;
  }>,
  res: Response
) => {
  const user = req.user;
  const user_id =
    user.role === UserRole.ADMIN
      ? safeInt(req.query.user_id, user.user_id, 0)
      : user.user_id;

  const program_id = safeInt(req?.query?.program_id, -1);

  // Check if the user has access to the business
  const business = await Users.findOne({
    where: { user_id, role: UserRole.BUSINESS }
  });

  if (!business) {
    return res.status(403).send({ message: "Unauthorised" });
  }

  const data: JSON = await getBusinessSummaryData(
    res,
    user_id,
    business.role,
    program_id
  );

  if (data != null) {
    res.setHeader("Content-Type", "application/pdf");
    res.setHeader(
      "Content-Disposition",
      "inline; filename=business_summary_report.pdf"
    );
    viewBusinessSummaryReport(data, user_id, res);
  } else {
    return res
      .status(500)
      .send({ message: "Failed to generate business summary report pdf." });
  }
};

/**
 * Handles a GET request to retrieve the certificate
 * for the given report (if it exists)
 */
export const getCertificate = async (
  req: CTBRequest<
    {
      report_id?: number | null;
    },
    {
      id?: string;
    }
  >,
  res: Response
) => {
  const { user_id, role } = req.user;

  // Report ID could be in the body if this was made with the outdated endpoint
  const report_id =
    safeInt(req.query.id, undefined, 0) ||
    safeInt(req.body.report_id, undefined, 0);

  if (report_id === undefined)
    return res.status(400).send({
      message: "Missing report ID"
    });

  logger.log({
    level: "info",
    label: "GET | Certificate",
    message: `Retrieving the certificate for user ${user_id} on report ${report_id}`
  });

  const certificate = await Certificates.findOne({ where: { report_id } });
  const report = await Reports.findOne({ where: { report_id } });

  // If a reseacher is making this request, only return the certificate if it belongs to them
  if (role === UserRole.RESEARCHER && certificate.user_id !== user_id) {
    return res.status(401).send({ message: "Unauthorized" });
  }

  const certificateData = await viewCertificate(report_id, report.program_id);

  if (!certificateData) {
    return res
      .status(404)
      .send({ message: "No certificate for the requested report." });
  } else {
    res.setHeader("Content-Type", "application/pdf");
    res.setHeader("Content-Disposition", "inline; filename=certificate.pdf");
    return res.send(certificateData);
  }
};

/**
 * Handles a POST request to generate a certificate
 * for the given report
 */
export const postCertificate = async (
  req: CTBBodyRequest<{ id?: number | null; report_id?: number | null }>,
  res: Response
) => {
  const report_id =
    safeInt(req.body.report_id, undefined, 0) ||
    safeInt(req.body.id, undefined, 0);

  if (report_id === undefined)
    return res.status(400).send({
      message: "Missing report ID"
    });

  try {
    // Retrieve the user ID associated with the report ID
    const report = await Reports.findOne({ where: { report_id } });
    if (!report) {
      return res.status(400).send({ error: "Report not found" });
    }

    const userId = report.user_id;
    const program_id = report.program_id;
    const program = await Programs.findOne({ where: { program_id } });
    if (!report) {
      return res.status(400).send({ error: "User not found" });
    }
    const user = await Users.findOne({ where: { user_id: userId } });
    // Create a new certificate entry
    await Certificates.create({
      user_id: userId,
      report_id: report_id,
      name_on_certificate: user.username,
      issue_date: new Date()
    });

    report.certificate_generated = true;
    await report.save();
    const certificateContent = await viewCertificate(report_id, program_id);

    await sendNotification(
      {
        type: NotificationType.Certificate,
        payload: report_id.toString(),
        message: report.report_title
      },
      [userId],
      [NotificationMethod.CTB]
    );

    sendEmail(
      user.email,
      CERTIFICATE_GENERATED_SUBJECT,
      program_id === CTB_VDP_ID
        ? CERTIFICATE_GENERATED_CTB_BODY(user.username)
        : CERTIFICATE_GENERATED_CTB_PARTNER_BODY(user.username, program?.type),
      [
        {
          name: "Certificate.pdf",
          contentType: "application/pdf",
          contentInBase64: certificateContent.toString("base64")
        }
      ]
    );
    return res.status(200).send({ message: "Certificate Generated" });
  } catch (error) {
    console.error("Error generating certificate:", error);
    return res.status(500).send({ error: "Failed to generate certificate" });
  }
};

export const getComments = async (
  req: CTBQueryRequest<{ reportId?: number }>,
  res: Response
) => {
  const { user_id, role } = req.user;
  const report_id = safeInt(req.query.reportId, undefined, 0);

  if (report_id === undefined) {
    return res.status(400).send({ message: "Missing report ID" });
  }

  const log = {
    level: "info",
    label: "report.controller | getComments",
    message: `User ${user_id} requested comments for report ${report_id}`
  };

  logger.log(log);

  // Make sure to only request comments on a report that the user has access to
  const where = await getReportQueryOptions(user_id, role);
  const report = await Reports.findOne({ where: { ...where, report_id } });

  // If the report is null - it either doesn't exist or the user doesn't have access
  if (report === null) {
    return res.status(404).send({ message: "Report not found" });
  }

  // Retrieve all the comments on the requested report
  await Comments.findAll({
    where: {
      report_id
    }
  })
    .then(async data => {
      // Returns array of reports or returns 404 error.
      if (!data || !data?.length) {
        log.message = `No comments found for report ${report_id}`;
        logger.log(log);
        return res.status(200).send([]);
      } else {
        const pfpComments = await Promise.all(
          data.map(async (com: any) => {
            const user = await Users.findOne({
              where: { user_id: com.user_id }
            });
            com.dataValues.pfp = user.pfp ?? DEFAULT_PROFILE;
            com.dataValues.username = user.username;
            com.dataValues.is_deleted === true
              ? (com.dataValues.comment = "[Deleted]")
              : null;
            return com;
          })
        );

        log.message = `User ${user_id} retrieved comments for report ${report_id}`;
        logger.log(log);

        return res.status(200).send(pfpComments);
      }
    })
    .catch(err => {
      log.level = "error";
      log.message = `Error retrieving comments for report ${report_id} by user ${user_id}: ${err}`;
      logger.log(log);
      return res.status(400).send({
        message:
          "400 BAD REQUEST: Cannot retrieve comment for the report. Ensure you specified a valid report"
      });
    });
};

export const notifyUsersComment = async (
  comment_data: CommentAttributes,
  report_data: ReportAttributes,
  role: UserRole,
  program_creator_id: number,
  notificationMethodsArr: string[],
  slackLink: string,
  slackMessage: string
) => {
  const researcher = await Users.findOne({
    where: { user_id: report_data.user_id }
  });
  const business = await Users.findOne({
    where: { user_id: program_creator_id }
  });

  const commentor = await Users.findOne({
    where: { user_id: comment_data.user_id }
  });

  const subject = NEW_COMMENT_SUBJECT(report_data?.report_id);
  const commonBody: [string, string, string, string] = [
    researcher?.username,
    report_data?.report_title,
    commentor?.username,
    comment_data?.comment
  ];

  if (slackLink) {
    const slackNotification = `💬 New Comment\nReport: ${report_data.report_title}\nFrom: ${commentor?.username}\nComment: ${comment_data.comment}`;
    await notifySlack(slackLink as string, slackNotification);
  }

  switch (role) {
    case UserRole.ADMIN:
      sendNotification(
        {
          type: NotificationType.Comments,
          payload: report_data.report_id.toString(),
          message: report_data.report_title
        },
        [business.user_id, researcher.user_id],
        [...notificationMethodsArr],
        slackLink,
        slackMessage
      );

      sendEmail(
        researcher?.email,
        subject,
        NEW_COMMENT_ON_REPORT_BODY(...commonBody)
      );
      commonBody[0] = business?.username;
      sendEmail(
        business?.email,
        subject,
        NEW_COMMENT_ON_REPORT_BODY(...commonBody)
      );
      break;

    case UserRole.RESEARCHER:
      sendNotification(
        {
          type: NotificationType.Comments,
          payload: report_data.report_id.toString(),
          message: report_data.report_title
        },
        [business.user_id],
        [...notificationMethodsArr],
        slackLink,
        slackMessage
      );

      sendEmail(
        business?.email,
        subject,
        NEW_COMMENT_ON_REPORT_BODY(...commonBody)
      );
      break;

    case UserRole.BUSINESS:
      sendNotification(
        {
          type: NotificationType.Comments,
          payload: report_data.report_id.toString(),
          message: report_data.report_title
        },
        [researcher.user_id],
        [...notificationMethodsArr],
        slackLink,
        slackMessage
      );

      commonBody[0] = business?.username;
      sendEmail(
        researcher?.email,
        subject,
        NEW_COMMENT_ON_REPORT_BODY(...commonBody)
      );
      break;
  }
};

export const addComment = async (
  req: CTBBodyRequest<{
    id?: number;
    comment?: string;
  }>,
  res: Response
) => {
  const { user_id, role, email } = req.user;
  const report_id = safeInt(req.body.id, undefined, 0);
  if (report_id === undefined) {
    return res.status(400).send({ message: "Missing report ID" });
  }

  const rawComment = safeString(req.body.comment, "");
  if (rawComment === "") {
    return res.status(400).send({ message: "Missing comment" });
  }

  const log = {
    level: "info",
    label: "report.controller | addComment",
    message: `User ${user_id} requested to add comment to report ${report_id}`
  };
  logger.log(log);

  /**
   * Retrieve the details for the report being commented on, so long as
   *   -  the user has access and
   *   -  the report is not a draft.
   */
  try {
    // Process any embedded images in the comment
    const comment = await decodeAndSaveImages(rawComment);

    const where = await getReportQueryOptions(user_id, role);
    const report = await Reports.findOne({
      where: {
        ...where,
        report_id,
        state: {
          [Op.not]: null
        },
        is_delete: false
      }
    });

    if (report === null) {
      log.level = "warn";
      log.message = `Report ${report_id} not found or user ${user_id} does not have access`;
      logger.log(log);
      return res.status(404).send({ message: "Report not found" });
    }

    // Prepare the comment details
    const cData: CommentAttributes = {
      report_id,
      comment,
      user_id
    };

    // We need the ID of the business who made the program that was reported on
    const program = await Programs.findOne({
      where: { program_id: report.program_id }
    });

    if (!program) {
      log.level = "error";
      log.message = `Program not found for report ${report_id}`;
      logger.log(log);
      return res.status(404).send({ message: "Program not found" });
    }

    const reportTitle = report.report_title || "Unknown Report";
    await ActivityLog.create({
      user_id,
      role,
      action: `A comment has been added to report "${reportTitle}(${report_id})" by user ${email}.`,
      module: "Report",
      created_at: new Date()
    });

    // Create the comment
    await Comments.create(cData);

    // Prepare notification methods safely
    const notificationMethodsArr = program.notification_methods
      ? [...Object.values(program.notification_methods)]
      : [];

    // Notify users about the comment
    notifyUsersComment(
      cData,
      report,
      role,
      program.user_id,
      notificationMethodsArr,
      String(program.slack_channel_link || ""),
      `A comment has been added to report "${reportTitle}(${report_id})" by user ${user_id}.\n Comment: ${comment}`
    );

    log.message = `Comment added to report "${reportTitle}(${report_id})" by user ${user_id}`;
    logger.log(log);
    return res.status(200).send();
  } catch (err) {
    log.level = "error";
    log.message = `Error adding comment to report ${report_id} by user ${user_id}: ${err}`;
    logger.log(log);
    return res.status(500).send({
      message:
        "500 SERVER ERROR: Unable to add comment to the report. Please try again later."
    });
  }
};

// soft deletes the speficied comment of the user
export const deleteComment = async (
  req: CTBBodyRequest<{ comment_id: number }>,
  res: Response
) => {
  const { user_id, role, email } = req.user;
  const cid = req.body.comment_id;
  const uid = req.user.user_id;

  const comment = await Comments.findOne({
    where: { comment_id: cid, is_deleted: false }
  });

  const commentContent = await Comments.findOne({
    where: { comment_id: cid },
    attributes: ["comment"]
  });

  const commentText = commentContent
    ? commentContent.comment
    : "No comment found";

  await ActivityLog.create({
    user_id,
    role,
    action: `A comment has been deleted by user ${email}}.\n Comment: ${commentText}`,
    module: "Report",
    created_at: new Date()
  });

  if (comment.user_id === uid) {
    comment.is_deleted = true;
    await comment.save();
    res.status(200).send({ message: "comment deleted" });
  } else {
    res.status(401).end();
  }
};

export const editComment = async (
  req: CTBBodyRequest<{
    comment_id: number;
    newComment: string;
  }>,
  res: Response
) => {
  const { user_id, role, email } = req.user;
  const uid = req.user.user_id;
  const { comment_id, newComment: rawNewComment } = req.body;

  try {
    const comment = await Comments.findOne({
      where: { comment_id: comment_id, is_deleted: false }
    });

    if (!comment) {
      return res.status(404).send({ message: "Comment not found" });
    }

    const commentContent = await Comments.findOne({
      where: { comment_id },
      attributes: ["comment"]
    });

    const commentText = commentContent
      ? commentContent.comment
      : "No comment found";

    await ActivityLog.create({
      user_id,
      role,
      action: `A comment has been edited by user ${email}.\n Comment: ${commentText}`,
      module: "Report",
      created_at: new Date()
    });

    if (comment.user_id === uid) {
      // Process any embedded images in the new comment
      const processedComment = await decodeAndSaveImages(rawNewComment);

      comment.comment = processedComment;
      await comment.save();
      res.status(200).send({ message: "comment updated" });
    } else {
      res.status(401).end();
    }
  } catch (err) {
    logger.log({
      level: "error",
      label: "report.controller | editComment",
      message: `Error editing comment ${comment_id} by user ${user_id}: ${err}`
    });
    return res.status(500).send({
      message:
        "500 SERVER ERROR: Unable to edit comment. Please try again later."
    });
  }
};

export const getFilterOptions = async (req: any, res: Response) => {
  const { user_id, role } = req.user;

  try {
    // For DEVELOPER and BUSINESS_MANAGER, use parent_user_id
    const effectiveUserId =
      role === UserRole.DEVELOPER || role === UserRole.BUSINESS_MANAGER
        ? (await Users.findOne({ where: { user_id } }))?.parent_user_id
        : user_id;

    if (
      role !== UserRole.RESEARCHER &&
      role !== UserRole.ADMIN &&
      !effectiveUserId
    ) {
      return res.status(404).json({ message: "Parent business not found" });
    }

    let whereClause: any = {};
    let programIds: number[] = [];

    if (
      role === UserRole.BUSINESS ||
      role === UserRole.DEVELOPER ||
      role === UserRole.BUSINESS_MANAGER
    ) {
      // Fetch programs created by the business user (or parent business for DEVELOPER/BUSINESS_MANAGER)
      const programs = await Programs.findAll({
        attributes: ["program_id"],
        where: { user_id: effectiveUserId }
      });

      programIds = programs.map(program => program.program_id);

      whereClause = {
        program_id: {
          [Op.in]: programIds
        }
      };
    } else if (role === UserRole.RESEARCHER) {
      // For researchers, we need to handle JSON array search differently for MariaDB
      // First, get all public programs
      const publicPrograms = await Programs.findAll({
        attributes: ["program_id"],
        where: {
          private: false,
          is_delete: false,
          is_activated: true
        }
      });

      // Then, get private programs where the user has access
      // Using MariaDB's JSON_CONTAINS function
      const privatePrograms = await Programs.findAll({
        attributes: ["program_id"],
        where: {
          private: true,
          is_delete: false,
          is_activated: true,
          // Use Sequelize.literal for raw SQL with JSON_CONTAINS
          [Op.and]: [
            Sequelize.literal(
              `JSON_CONTAINS(private_access_users, '${user_id}')`
            )
          ]
        }
      });

      // Combine the program IDs
      const accessibleProgramIds = [
        ...publicPrograms.map(p => p.program_id),
        ...privatePrograms.map(p => p.program_id)
      ];

      if (accessibleProgramIds.length > 0) {
        programIds = accessibleProgramIds;
        whereClause = {
          program_id: {
            [Op.in]: programIds
          }
        };
      }
    }
    // Admin can access all reports, so whereClause remains empty

    // Add triage status condition for business roles
    if (
      role === UserRole.BUSINESS ||
      role === UserRole.DEVELOPER ||
      role === UserRole.BUSINESS_MANAGER
    ) {
      whereClause = {
        ...whereClause,
        triage_status: {
          [Op.in]: [TRIAGE_STATUS_APPROVED, TRIAGE_SERVICE_NOT_OPTED]
        }
      };
    }

    // Fetch distinct values for each filter
    const programNames = await Programs.findAll({
      attributes: ["program_id", "program_title"],
      ...(programIds.length > 0
        ? {
            where: {
              program_id: {
                [Op.in]: programIds
              }
            }
          }
        : {}),
      group: ["program_id", "program_title"]
    });

    // Use whereClause for Reports queries
    const vulnerabilityTypes = await Reports.findAll({
      attributes: ["category"],
      where: whereClause,
      group: ["category"]
    });

    const severityCategories = await Reports.findAll({
      attributes: ["severity_category"],
      where: whereClause,
      group: ["severity_category"]
    });

    const reportStates = await Reports.findAll({
      attributes: ["state"],
      where: whereClause,
      group: ["state"]
    });

    const submissionDates = await Reports.findAll({
      attributes: ["submitted_date"],
      where: whereClause,
      group: ["submitted_date"]
    });

    const reportIds = await Reports.findAll({
      attributes: ["report_id"],
      where: whereClause,
      group: ["report_id"]
    });

    // Get unique pentesters
    const pentesters = await Reports.findAll({
      attributes: ["user_id"],
      where: whereClause,
      include: [
        {
          model: database.user,
          as: "user",
          attributes: ["display_name", "username"],
          required: true
        }
      ],
      group: ["user_id", "user.display_name", "user.username"]
    });

    // Prepare the response data
    const filterOptions = {
      programNames: programNames.map(program => ({
        id: program.program_id,
        name: program.program_title
      })),
      vulnerabilityTypes: vulnerabilityTypes.map(report => report.category),
      severityCategories: severityCategories.map(
        report => report.severity_category
      ),
      reportStates: reportStates.map(report => report.state),
      submissionDates: submissionDates.map(report => report.submitted_date),
      reportIds: reportIds.map(report => report.report_id),
      pentesters: pentesters
        .map(report => {
          const user = (report as any).user as {
            display_name?: string;
            username?: string;
          };
          return user?.display_name || user?.username;
        })
        .filter((name): name is string => name !== undefined && name !== null)
    };

    return res.status(200).send({
      message: "Filter options retrieved successfully.",
      data: filterOptions
    });
  } catch (err) {
    console.error(err);
    return res.status(500).send({ message: "Internal server error." });
  }
};

export const getReportsInfo = async (req: Request, res: Response) => {
  const { program_id } = req.params;

  if (!program_id || isNaN(Number(program_id))) {
    return res.status(400).json({ message: "Invalid program ID." });
  }

  try {
    const program = await Programs.findOne({
      where: { program_id },
      attributes: ["program_id", "program_title"]
    });

    if (!program) {
      return res.status(404).json({ message: "Program not found." });
    }

    const ninetyDaysAgo = new Date();
    ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

    const reportCount = await Reports.count({
      where: {
        program_id,
        submitted_date: { [Op.gte]: ninetyDaysAgo }
      }
    });

    let resolvedReportsCount = await Reports.count({
      where: { program_id, state: "closed" }
    });

    const assetsInScope = await Reports.count({
      where: { program_id },
      distinct: true,
      col: "scope"
    });

    const reports = await Reports.findAll({
      where: { program_id },
      attributes: [
        "report_id",
        "report_title",
        "state",
        "category",
        "severity_category",
        "submitted_date"
      ]
    });

    const openReportsIds = reports
      .filter(report => report.state !== "closed")
      .map(report => report.report_id);

    if (openReportsIds.length > 0) {
      const fixVerifiedRetests = await Retest.count({
        where: {
          report_id: { [Op.in]: openReportsIds },
          status: "Fix Verified"
        }
      });

      resolvedReportsCount += fixVerifiedRetests;
    }

    const groupedReports = reports.reduce(
      (acc, report) => {
        acc[report.state] = (acc[report.state] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    return res.status(200).json({
      message: "Reports details retrieved successfully.",
      data: {
        program: {
          id: program.program_id,
          name: program.program_title,
          total_reports: reports.length
        },
        summary: {
          reports_received_90_days: reportCount,
          reports_resolved: resolvedReportsCount,
          assets_in_scope: assetsInScope,
          report_status_counts: groupedReports
        },
        reports: reports.map(report => ({
          id: report.report_id,
          title: report.report_title,
          status: report.state,
          category: report.category,
          severity: report.severity_category,
          submitted_date: report.submitted_date
        }))
      }
    });
  } catch (error) {
    console.error("Error fetching reports details:", error);
    return res.status(500).json({ message: "Internal server error." });
  }
};

export const getScopeStats = async (req: Request, res: Response) => {
  try {
    console.log("req.params:", req.params);
    const { id } = req.params;
    console.log("Extracted id:", id);

    const program_id = parseInt(id, 10);
    console.log("Parsed program_id:", program_id);

    if (isNaN(program_id)) {
      return res.status(400).json({ message: "Invalid program ID." });
    }

    const program = await Program.findByPk(program_id);
    if (!program) {
      return res.status(404).json({ message: "Program not found." });
    }

    const scopeStats = await Report.findAll({
      attributes: [
        "scope",
        [Sequelize.fn("COUNT", Sequelize.col("scope")), "reportCount"],
        [Sequelize.fn("MAX", Sequelize.col("submitted_date")), "lastSubmitted"]
      ],
      where: { program_id },
      group: ["scope"]
    });

    console.log("Fetched scopeStats:", scopeStats);

    return res.status(200).json({ program_id, scopeStats });
  } catch (error) {
    console.error("Error fetching scope stats:", error);
    return res.status(500).json({ message: "Internal server error" });
  }
};

/**
 * Returns a list of recommended reports for the current user based on their role and the current report
 */
export const getReportRecommendations = async (
  req: CTBParamsRequest<{ id: string }>,
  res: Response
) => {
  const { user_id, role } = req.user;
  const currentReportId = parseInt(req.params.id, 10);
  if (isNaN(currentReportId)) {
    return res.status(400).json({ message: "Invalid report ID." });
  }

  try {
    // Get accessible reports for the user
    const where = await getReportQueryOptions(user_id, role);
    const reports = await Reports.findAll({
      where,
      attributes: ["report_id", "report_title", "severity_category"],
      order: [["report_id", "DESC"]],
      raw: true
    });

    // Remove current report
    const filteredReports = reports.filter(
      r => r.report_id !== currentReportId
    );

    if (filteredReports.length === 0) {
      return res.status(200).json({ recommendations: [] });
    }

    let recommendations = [];
    if (
      role === UserRole.ADMIN ||
      role === UserRole.SUB_ADMIN ||
      role === UserRole.ADMIN_MANAGER ||
      role === UserRole.QA ||
      role === UserRole.RESEARCHER
    ) {
      // Find the index of the current report in the original list
      const idx = reports.findIndex(r => r.report_id === currentReportId);
      // Find the closest index in filteredReports that is just before the current report
      let beforeIdx = filteredReports.findIndex(
        r => r.report_id < currentReportId
      );
      if (beforeIdx === -1) beforeIdx = filteredReports.length; // all are after
      // Get up to 2 before
      const before = filteredReports.slice(
        Math.max(0, beforeIdx - 2),
        beforeIdx
      );
      // Get up to 2 after (or more if not enough before)
      const after = filteredReports.slice(
        beforeIdx,
        beforeIdx + (5 - before.length)
      );
      recommendations = before.concat(after).slice(0, 5);
    } else {
      // For business roles, just take the first 4 from filteredReports
      recommendations = filteredReports.slice(0, 4);
    }

    return res.status(200).json({
      recommendations
    });
  } catch (err) {
    console.error(err);
    return res
      .status(500)
      .json({ message: "Failed to get report recommendations." });
  }
};

/**
 * Close CTB report via Jira automation
 * POST /api/reports/jira-close
 */
/**
 * Handle Jira webhook events for issue status changes
 * This endpoint receives all Jira webhook events and processes relevant ones
 */
export const handleJiraWebhook = async (req: Request, res: Response) => {
  try {
    // Log webhook request details for debugging
    const clientIP = req.ip || req.connection.remoteAddress;
    const userAgent = req.headers["user-agent"] || "Unknown";
    const contentType = req.headers["content-type"] || "Unknown";

    logger.log({
      level: "info",
      label: "Jira Webhook | Request Details",
      message: `Webhook request from ${clientIP}, User-Agent: ${userAgent}, Content-Type: ${contentType}`
    });

    // Authenticate webhook request using User-Agent and IP validation
    const isFromJira = userAgent.includes("Atlassian Webhook");

    if (!isFromJira) {
      logger.log({
        level: "warn",
        label: "Jira Webhook | Authentication Failed",
        message: `Unauthorized webhook request from ${clientIP}. User-Agent: ${userAgent}. Expected: Atlassian Webhook`
      });
      return res.status(401).json({
        success: false,
        message: "Unauthorized - Invalid User-Agent"
      });
    }

    // Validate request body
    if (!req.body || Object.keys(req.body).length === 0) {
      logger.log({
        level: "error",
        label: "Jira Webhook | Empty Body",
        message: `Webhook request from ${clientIP} has empty body. This may indicate webhook configuration issues.`
      });
      return res.status(400).json({
        success: false,
        message: "Empty request body"
      });
    }

    const webhookEvent = req.body;

    // Validate webhook event structure
    if (!webhookEvent.webhookEvent) {
      logger.log({
        level: "error",
        label: "Jira Webhook | Invalid Event",
        message: `Webhook request from ${clientIP} missing webhookEvent field. Body: ${JSON.stringify(
          webhookEvent
        )}`
      });
      return res.status(400).json({
        success: false,
        message: "Invalid webhook event structure"
      });
    }

    logger.log({
      level: "info",
      label: "Jira Webhook | Received",
      message: `Event: ${webhookEvent.webhookEvent}, Issue: ${
        webhookEvent.issue?.key || "Unknown"
      }`
    });

    // Only process issue update events
    if (
      !webhookEvent.webhookEvent ||
      !webhookEvent.webhookEvent.includes("issue")
    ) {
      return res.status(200).json({
        success: true,
        message: "Event ignored - not an issue event"
      });
    }

    // Only process CTB reports
    const issueSummary = webhookEvent.issue?.fields?.summary || "";
    if (!issueSummary.includes("[CTB Report]")) {
      return res.status(200).json({
        success: true,
        message: "Event ignored - not a CTB report"
      });
    }

    // Check if status changed to "Done"
    const currentStatus = webhookEvent.issue?.fields?.status?.name;
    const previousStatus = webhookEvent.changelog?.items?.find(
      (item: any) => item.field === "status"
    )?.fromString;

    logger.log({
      level: "info",
      label: "Jira Webhook | Status Change",
      message: `Issue ${webhookEvent.issue.key}: ${previousStatus} → ${currentStatus}`
    });

    // Handle transitions TO "Done" status (closure attempts)
    if (currentStatus === "Done") {
      // Process the closure request
      const result = await processJiraIssueClosure({
        jiraIssueKey: webhookEvent.issue.key,
        jiraStatus: currentStatus,
        jiraProjectKey: webhookEvent.issue.fields.project.key
      });

      return res.status(200).json(result);
    }

    // Handle transitions AWAY FROM "Done" status (prevent reopening of closed reports)
    if (previousStatus === "Done" && currentStatus !== "Done") {
      // Check if this is a system reversion (happens within a short time window)
      // If the report is not actually closed, this is likely a system reversion
      const reversionCheckCondition: any = {
        [Op.and]: [
          { jira_issue_key: webhookEvent.issue.key },
          { is_delete: false }
        ]
      };

      const reversionCheckReport = await Reports.findOne({
        where: reversionCheckCondition,
        include: [
          {
            model: Programs,
            as: "program",
            where: {
              jira_project_key: webhookEvent.issue.fields.project.key
            }
          }
        ]
      });

      // If report is not closed, this is likely a system reversion - allow it
      if (!reversionCheckReport || reversionCheckReport.state !== "closed") {
        logger.log({
          level: "info",
          label: "Jira Webhook | System Reversion",
          message: `Allowing system reversion of issue ${webhookEvent.issue.key} from 'Done' to '${currentStatus}' - report not closed`
        });
        return res.status(200).json({
          success: true,
          message: "System reversion allowed - report not closed"
        });
      }

      // Only prevent reopening if the report is actually closed
      logger.log({
        level: "warn",
        label: "Jira Webhook | Prevent Reopening",
        message: `Attempt to move closed issue ${webhookEvent.issue.key} from 'Done' to '${currentStatus}'. Checking if this should be prevented.`
      });

      // Find the report to check if it was properly closed
      const whereCondition: any = {
        [Op.and]: [
          { jira_issue_key: webhookEvent.issue.key },
          { is_delete: false }
        ]
      };

      const report = await Reports.findOne({
        where: whereCondition,
        include: [
          {
            model: Programs,
            as: "program",
            where: {
              jira_project_key: webhookEvent.issue.fields.project.key
            }
          }
        ]
      });

      if (report && report.state === "closed") {
        logger.log({
          level: "warn",
          label: "Jira Webhook | Reverting Reopening",
          message: `Preventing reopening of closed report. Reverting ${webhookEvent.issue.key} back to 'Done'.`
        });

        // Revert back to "Done" status
        const jiraConfig = {
          url: String((report as any).program.jira_url),
          email: String((report as any).program.jira_email),
          api_token: String((report as any).program.jira_api_token),
          project_key: String((report as any).program.jira_project_key)
        };

        const revertResult = await revertJiraIssueToStatus(
          jiraConfig,
          webhookEvent.issue.key,
          "Done"
        );

        return res.status(200).json({
          success: false,
          message: `Cannot reopen closed report. Jira issue reverted back to 'Done'.`,
          action: "reverted_to_done",
          revertResult: revertResult
        });
      }

      // If report is not closed, allow the transition
      return res.status(200).json({
        success: true,
        message: "Transition allowed - report not in closed state"
      });
    }

    // Ignore all other transitions (not involving "Done" status)
    return res.status(200).json({
      success: true,
      message: "Event ignored - not involving Done status"
    });
  } catch (error) {
    // Enhanced error logging for webhook failures
    const errorMessage = error.message || "Unknown error";
    const errorStack = error.stack || "No stack trace";

    logger.log({
      level: "error",
      label: "Jira Webhook | Processing Error",
      message: `Failed to process webhook: ${errorMessage}. Stack: ${errorStack}`
    });

    // Log specific error types for better debugging
    if (error.name === "ValidationError") {
      logger.log({
        level: "error",
        label: "Jira Webhook | Validation Error",
        message: `Webhook validation failed: ${errorMessage}. This may indicate webhook configuration issues.`
      });
    } else if (error.name === "DatabaseError") {
      logger.log({
        level: "error",
        label: "Jira Webhook | Database Error",
        message: `Database error during webhook processing: ${errorMessage}. Check database connectivity.`
      });
    } else if (error.code === "ECONNREFUSED" || error.code === "ENOTFOUND") {
      logger.log({
        level: "error",
        label: "Jira Webhook | Network Error",
        message: `Network error during webhook processing: ${errorMessage}. Check network connectivity and Jira server status.`
      });
    } else if (error.response) {
      logger.log({
        level: "error",
        label: "Jira Webhook | External API Error",
        message: `External API error during webhook processing: Status ${
          error.response.status
        }, Message: ${JSON.stringify(error.response.data)}`
      });
    }

    return res.status(500).json({
      success: false,
      message: "Internal server error during webhook processing",
      error:
        process.env.NODE_ENV === "development"
          ? errorMessage
          : "Webhook processing failed"
    });
  }
};

/**
 * Shared function to process Jira issue closure requests
 */
async function processJiraIssueClosure({
  jiraIssueKey,
  jiraStatus,
  jiraProjectKey
}: {
  jiraIssueKey: string;
  jiraStatus: string;
  jiraProjectKey: string;
}) {
  try {
    logger.log({
      level: "info",
      label: "Jira Integration | Process Closure",
      message: `Processing closure request for ${jiraIssueKey} with status ${jiraStatus}`
    });

    // Validate required fields
    if (!jiraIssueKey || !jiraStatus || !jiraProjectKey) {
      return {
        success: false,
        message:
          "Missing required fields: jiraIssueKey, jiraStatus, jiraProjectKey"
      };
    }

    // Find the report by searching for the Jira issue key in the description
    // We store the Jira issue key in format [JIRA_ISSUE_KEY: KEY-123]
    const whereCondition: any = {
      [Op.and]: [{ jira_issue_key: jiraIssueKey }, { is_delete: false }]
    };

    const report = await Reports.findOne({
      where: whereCondition,
      include: [
        {
          model: Programs,
          as: "program",
          where: {
            jira_project_key: jiraProjectKey
          }
        }
      ]
    });

    if (!report) {
      logger.log({
        level: "warn",
        label: "Jira Integration | Report Not Found",
        message: `No CTB report found for Jira issue ${jiraIssueKey} in project ${jiraProjectKey}`
      });
      return {
        success: false,
        message: `No CTB report found for Jira issue ${jiraIssueKey}`
      };
    }

    // CRITICAL: Prevent infinite loop - if report is already closed, don't process again
    if (report.state === "closed") {
      logger.log({
        level: "info",
        label: "Jira Integration | Already Closed",
        message: `Report for ${jiraIssueKey} is already closed. Ignoring closure request to prevent infinite loop.`
      });
      return {
        success: true,
        message: `Report already closed. Jira issue should remain 'Done'.`,
        reportId: report.report_id
      };
    }

    // Validate business rules
    const validation = await validateReportClosureConditions(
      report,
      jiraStatus
    );

    logger.log({
      level: "info",
      label: "Jira Integration | Validation Result",
      message: `Validation for ${jiraIssueKey}: ${
        validation.allowed ? "ALLOWED" : "DENIED"
      } - ${validation.reason || "All conditions met"}`
    });

    if (!validation.allowed) {
      logger.log({
        level: "warn",
        label: "Jira Integration | Reverting Issue",
        message: `Reverting ${jiraIssueKey} to 'To Do' due to: ${validation.reason}`
      });

      // Revert Jira issue back to "To Do" status
      const jiraConfig = {
        url: String((report as any).program.jira_url),
        email: String((report as any).program.jira_email),
        api_token: String((report as any).program.jira_api_token),
        project_key: String((report as any).program.jira_project_key)
      };
      const revertResult = await revertJiraIssueStatus(
        jiraConfig,
        jiraIssueKey
      );

      return {
        success: false,
        message: `Report closure not allowed: ${validation.reason}. Jira issue reverted to To Do.`,
        validation: validation,
        revertResult: revertResult
      };
    }

    // Close the report
    await Reports.update(
      {
        state: "closed"
      },
      {
        where: { report_id: report.report_id }
      }
    );

    logger.log({
      level: "info",
      label: "Jira Integration | Report Closed",
      message: `✅ Successfully closed CTB report ${report.report_id} for Jira issue ${jiraIssueKey}. Jira issue will remain 'Done'.`
    });

    return {
      success: true,
      message: `Report ${report.report_id} successfully closed. Jira issue remains 'Done'.`,
      reportId: report.report_id
    };
  } catch (error) {
    logger.log({
      level: "error",
      label: "Jira Integration | Process Error",
      message: `Error processing closure for ${jiraIssueKey}: ${error}`
    });

    return {
      success: false,
      message: "Internal server error while processing closure request"
    };
  }
}

/**
 * Validate if a report can be closed based on business rules
 */
async function validateReportClosureConditions(
  report: any,
  jiraStatus: string
): Promise<{ allowed: boolean; reason?: string }> {
  // Business rule 1: Only allow closure if report is in 'Awaiting Fix' status
  if (report.state !== AWAITING_FIX) {
    return {
      allowed: false,
      reason: `Report cannot be closed. Current status: ${report.state}. Required status: ${AWAITING_FIX}`
    };
  }

  // Business rule 2: Check retest status - must be 'Fix Verified'
  // First, get the latest retest record for this report
  const latestRetest = await database.retest.findOne({
    where: { report_id: report.report_id },
    order: [["created_at", "DESC"]]
  });

  if (!latestRetest || latestRetest.status !== "Fix Verified") {
    const currentRetestStatus = latestRetest
      ? latestRetest.status
      : "no_retest";
    return {
      allowed: false,
      reason: `Report cannot be closed. Retest status: ${currentRetestStatus}. Required status: Fix Verified`
    };
  }

  // Business rule 3: Only allow closure for specific Jira statuses
  const allowedJiraStatuses = ["Done", "Closed", "Resolved"];
  if (!allowedJiraStatuses.includes(jiraStatus)) {
    return {
      allowed: false,
      reason: `Jira status '${jiraStatus}' is not allowed for closing reports. Allowed statuses: ${allowedJiraStatuses.join(
        ", "
      )}`
    };
  }

  // All conditions met - allow closure
  return {
    allowed: true,
    reason: `All conditions met: Report status is '${AWAITING_FIX}', retest status is 'Fix Verified', Jira status is '${jiraStatus}'`
  };
}
