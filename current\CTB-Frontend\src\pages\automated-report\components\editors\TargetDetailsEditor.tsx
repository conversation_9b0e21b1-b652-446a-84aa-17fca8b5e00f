import React from 'react';
import { ReportData } from '../../types/report.types';
import HtmlEditor from '../HtmlEditor';

interface TargetDetailsEditorProps {
  reportData: ReportData;
  onHtmlChange: (field: keyof ReportData, value: string) => void;
}

const TargetDetailsEditor: React.FC<TargetDetailsEditorProps> = ({
  reportData,
  onHtmlChange,
}) => {
  return (
    <div className="space-y-6">
      <div className="p-6">
        <HtmlEditor
          field="target_details"
          title="Target Details"
          reportData={reportData}
          onHtmlChange={onHtmlChange as any}
        />
      </div>
    </div>
  );
};

export default TargetDetailsEditor; 