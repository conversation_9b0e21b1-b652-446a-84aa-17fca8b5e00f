import React from 'react';

interface MethodologySelectorEditorProps {
  value: { web?: boolean; network?: boolean; mobile?: boolean };
  onChange: (value: { web?: boolean; network?: boolean; mobile?: boolean }) => void;
  disabled?: boolean;
}

const options = [
  { key: 'web', label: 'Web/API', icon: '🌐', color: 'bg-blue-100', selectedColor: 'bg-blue-600 text-white' },
  { key: 'network', label: 'Network', icon: '🖧', color: 'bg-green-100', selectedColor: 'bg-green-600 text-white' },
  { key: 'mobile', label: 'Mobile', icon: '📱', color: 'bg-purple-100', selectedColor: 'bg-purple-600 text-white' },
];

const MethodologySelectorEditor: React.FC<MethodologySelectorEditorProps> = ({ value = {}, onChange, disabled }) => {
  // Helper: treat web as selected if nothing is selected
  const isSelected = (key: 'web' | 'network' | 'mobile') => {
    const anySelected = value.web || value.network || value.mobile;
    if (!anySelected && key === 'web') return true;
    return !!value[key];
  };

  const handleToggle = (key: 'web' | 'network' | 'mobile') => {
    const anySelected = value.web || value.network || value.mobile;
    // If nothing is selected, and user clicks web, toggle it on
    if (!anySelected && key === 'web') {
      onChange({ ...value, web: true });
      return;
    }
    // Otherwise, toggle as normal
    onChange({ ...value, [key]: !value[key] });
  };

  return (
    <div className="space-y-8">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 px-6 py-4 border-b border-gray-200">
          <h3 className="text-xl font-semibold text-blue-800">Methodology Selection</h3>
          <p className="text-sm text-blue-700 mt-1">Choose the methodology sections to include in your report. You can select any combination.</p>
        </div>
        <div className="p-6 flex flex-col items-center">
          <div className="flex flex-wrap gap-4 justify-center">
            {options.map(opt => {
              const selected = isSelected(opt.key as 'web' | 'network' | 'mobile');
              return (
                <button
                  key={opt.key}
                  type="button"
                  disabled={disabled}
                  onClick={() => handleToggle(opt.key as 'web' | 'network' | 'mobile')}
                  className={`flex items-center gap-2 px-6 py-3 rounded-full shadow transition-all duration-150 font-semibold text-base border-2 focus:outline-none focus:ring-2 focus:ring-blue-400
                    ${selected ? opt.selectedColor + ' border-transparent scale-105' : opt.color + ' border-gray-200 hover:border-blue-400 hover:bg-blue-50'}
                    ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  <span className="text-xl">{opt.icon}</span>
                  <span>{opt.label}</span>
                  {selected && <span className="ml-2 text-xs bg-white/80 text-blue-700 rounded px-2 py-0.5 font-bold animate-pulse">Selected</span>}
                </button>
              );
            })}
          </div>
          <div className="mt-6 text-sm text-gray-500">
            <p>
              Selected: {
                options
                  .filter(opt => isSelected(opt.key as 'web' | 'network' | 'mobile'))
                  .map(opt => opt.label)
                  .join(', ') || 'None'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MethodologySelectorEditor; 