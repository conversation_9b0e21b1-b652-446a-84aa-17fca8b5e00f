import React from 'react';
import { useFormContext, useWatch } from 'react-hook-form'; 
import ProgressTimeline from '../../components/common/ProgressTimeline';


type ProgramType = 'BugBounty' | 'VDP' | 'PTAAS';

interface FormValues {
  title: string;
  type: ProgramType;
  private: number;
  description?: string;
  targets: any[];
  vpn?: string;
  credentials?: string;
  scope: string;
  outOfScope?: string;
  knownVulnerabilities?: string;
  rewards?: Record<string, any>;
  rewardPolicy?: string;
  termsOfService: string;
  testingType?: string;
  environmentType?: string;
  expectedStartDate?: Date;
  expectedEndDate?: Date;
  complianceType?: string;
  otherComplianceType?: string;
  other?: string;
  profilePicture?: File;
}

type FieldCondition = (type: ProgramType, values?: Partial<FormValues>) => boolean;

interface Section {
  title: string;
  requiredFields: string[] | ((type: ProgramType) => string[]);
  optionalFields: string[];
  conditionalFields?: Record<string, FieldCondition>;
}

const FormProgressTimeline: React.FC = () => {
  const { control } = useFormContext<FormValues>();
  const programType = useWatch({ control, name: 'type' }) as ProgramType;

  const commonSections: Section[] = [
    {
      title: "Program Details",
      requiredFields: ["title", "type", "description"],
      optionalFields: [],
    },
    {
      title: "T & C",
      requiredFields: ["targets"],
      optionalFields: ["vpn", "credentials"]
    },
    {
      title: "Scope",
      requiredFields: (type) => {
        let fields = ["scope" , "outOfScope"];  
        if (type === "PTAAS") {
          fields = fields.filter(field => field !== "outOfScope");  
        }
        return fields;
      },
      optionalFields: ["knownVulnerabilities"],
      conditionalFields: {
        knownVulnerabilities: (type) => type !== "PTAAS"  
      }
    },    
    {
      title: "Rewards & Policy",
      requiredFields: (type: ProgramType) => type === 'VDP' ? ["termsOfService"] : ["rewards", "rewardPolicy", "termsOfService"],
      optionalFields: []
    },
  ];

  const sections = programType === 'PTAAS'
    ? commonSections.map(section => {
      if (section.title === "Rewards & Policy") {
        return {
          ...section,
          title: "Additional Information",
          requiredFields: ["testingType", "environmentType", "expectedStartDate", "expectedEndDate" , "complianceType"],
          optionalFields: ["other", "profilePicture", "otherComplianceType" ],
        };
      }
      return section;
    })
    : commonSections;

  const getRequiredFields = (section: Section): string[] => {
    const fields = typeof section.requiredFields === 'function'
      ? section.requiredFields(programType)
      : section.requiredFields;
    return fields;
  };

  const getFlatSectionsForTimeline = () => {
    return sections.map(section => ({
      title: section.title,
      requiredFields: getRequiredFields(section)
    }));
  };

  return (
    <ProgressTimeline sections={getFlatSectionsForTimeline()} />
  );
};

export default FormProgressTimeline;