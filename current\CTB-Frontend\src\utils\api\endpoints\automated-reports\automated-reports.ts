import axios from "../../axios";

const URL = "/v2/automated-report";

// API call to create an automated report
export const createAutomatedReport = async (data: {
  company_name?: string;
  program_ids: number[];
}) => {
  const response = await axios.post(`${URL}/create`, data);
  return response.data;
};

// API call to get user programs
export const getUserPrograms = async () => {
  const response = await axios.post(`${URL}/getUserPrograms`);
  return response.data;
};

// API call to fetch business reports
export const getBusinessReports = async () => {
  const response = await axios.get(`${URL}/business-reports`);
  return response.data;
};

// API call to get program reports for a specific report
export const getProgramReports = async (report_id: string) => {
  const response = await axios.post(`${URL}/get-program-reports/${report_id}`);
  return response.data;
};

// API call to check if a program has an automated report
export const checkProgramAutomatedReport = async (program_id: string) => {
  const response = await axios.get(`${URL}/check-program-report/${program_id}`);
  return response.data;
};
