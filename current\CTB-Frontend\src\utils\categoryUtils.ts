import { VULNERABILITY_CATEGORIES } from "../utils/api/endpoints/reports/reports";

export interface CategoryPath {
  categoryId: string;
  subcategoryId?: string;
  variantId?: string;
}

export const getCategoryDisplayName = (categoryPath: CategoryPath): string => {
  const category = VULNERABILITY_CATEGORIES.find(
    c => c.id === categoryPath.categoryId
  );
  if (!category) return "";

  let result = category.name;

  if (categoryPath.subcategoryId && category.children) {
    const subcategory = category.children.find(
      sc => sc.id === categoryPath.subcategoryId
    );
    if (subcategory) {
      result += ` > ${subcategory.name}`;

      if (categoryPath.variantId && subcategory.children) {
        const variant = subcategory.children.find(
          v => v.id === categoryPath.variantId
        );
        if (variant) {
          result += ` > ${variant.name}`;
        }
      }
    }
  }

  return result;
};

export const parseCategoryString = (category: string): string => {
  try {
    const parsed: CategoryPath = JSON.parse(category);
    const displayName = getCategoryDisplayName(parsed);
    return displayName || category; // Fallback to original if display name is empty
  } catch (e) {
    return category; // Not a valid JSON string
  }
};
