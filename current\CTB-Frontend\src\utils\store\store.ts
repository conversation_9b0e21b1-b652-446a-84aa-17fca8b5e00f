import { configureStore } from "@reduxjs/toolkit";
import userReducer from "./reducer/slices/userReducer";
import userReducerForAdmin from "./reducer/slices/userReducerForAdmin";
import { setupListeners } from "@reduxjs/toolkit/dist/query";
import { api } from "../api/api";
import modalReducer from "./reducer/slices/modalReducer";

/**
 * The global Redux store.
 *
 * This is the root point for the state manager that contains global states
 * and variables, including the API caching system
 */
export const store = configureStore({
  reducer: {
    modal: modalReducer,
    user: userReducer,
    other_user: userReducerForAdmin,
    [api.reducerPath]: api.reducer
  },
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware().concat(api.middleware)
});

/**
 * Infer the types for the store state & its dispatch function.
 */
export type RootState = ReturnType<typeof store.getState>;
export type StoreDispatch = typeof store.dispatch;

setupListeners(store.dispatch);
