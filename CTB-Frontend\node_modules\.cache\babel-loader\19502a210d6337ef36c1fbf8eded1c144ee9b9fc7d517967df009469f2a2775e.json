{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\jira-old\\\\CTB-Frontend\\\\src\\\\pages\\\\automated-report\\\\PDFEditor.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport useUserCredentials from '../../utils/hooks/user/useUserCredentials';\nimport './PDFEditor.css';\nimport './report-print.css';\n// Import custom-scrollbar class globally for chat scroll UX\n// If not present, add the .custom-scrollbar CSS to your global stylesheet (e.g., index.css or App.scss)\nimport PreviewOnly from './PreviewOnly';\nimport { useRoleString } from './hooks/useRoleString';\nimport EditorLayout from './EditorLayout';\nimport { usePDFEditorLogic } from './hooks/usePDFEditorLogic';\nimport ChatSection from './components/ChatSection';\nimport ChatModal from './components/ChatModal';\nimport ConfirmationModal from '../../components/common/ConfirmationModal';\n\n// Constants\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PREVIEW_UPDATE_DELAY = 500;\nconst SEVERITY_CATEGORIES = ['Critical', 'High', 'Medium', 'Low'];\nconst STATUS_TYPES = ['Open', 'Closed', 'Total'];\n\n// Types\n\n// Utility functions\nconst normalizeSeverityCategory = category => {\n  const normalized = category.charAt(0).toUpperCase() + category.slice(1).toLowerCase();\n  return normalized;\n};\nconst getDefaultDisclaimer = companyName => `\n  <p>\n    Capture The Bug Ltd. has prepared this document exclusively for ${companyName}.\n    Copying, or modification of this document is strictly prohibited without Capture The Bug Ltd.'s written consent, \n    except for specific purposes when such permission is granted. This document is confidential and proprietary material \n    of Capture The Bug Ltd. and must always be treated as such, not to be disclosed to third parties without prior consent.\n  </p>\n  \n  <p>\n    The information within is provided 'as-is,' without any warranty. Vulnerability assessments offer a snapshot of \n    security at a specific moment and are not exhaustive risk assessments. New vulnerabilities may emerge post-assessment. \n    Thus, this report is a guide, not a definitive risk analysis.\n  </p>\n  \n  <p>\n    Capture The Bug Ltd. assumes no liability for any changes, omissions, or errors in this document. \n    Capture The Bug Ltd. shall not be liable for any damages, financial or otherwise, arising out of the use or misuse \n    of this report by any current employee of ${companyName} or any member of the general public.\n  </p>\n`;\nconst getDefaultOpenCloseCounts = () => ({\n  Critical: {\n    Open: 0,\n    Closed: 0,\n    Total: 0\n  },\n  High: {\n    Open: 0,\n    Closed: 0,\n    Total: 0\n  },\n  Medium: {\n    Open: 0,\n    Closed: 0,\n    Total: 0\n  },\n  Low: {\n    Open: 0,\n    Closed: 0,\n    Total: 0\n  }\n});\n\n// Notification component\n\nconst Notification = ({\n  type,\n  title,\n  message,\n  isVisible,\n  onClose\n}) => {\n  _s();\n  useEffect(() => {\n    if (isVisible) {\n      const timer = setTimeout(() => {\n        onClose();\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [isVisible, onClose]);\n  if (!isVisible) return null;\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-6 h-6\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: \"2\",\n            d: \"M5 13l4 4L19 7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-6 h-6\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: \"2\",\n            d: \"M6 18L18 6M6 6l12 12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this);\n      case 'info':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-6 h-6\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: \"2\",\n            d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  const getStyles = () => {\n    switch (type) {\n      case 'success':\n        return 'bg-gradient-to-r from-emerald-50 to-green-50 border-emerald-200 text-emerald-800';\n      case 'error':\n        return 'bg-gradient-to-r from-red-50 to-pink-50 border-red-200 text-red-800';\n      case 'info':\n        return 'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 text-blue-800';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed top-4 right-4 z-50 animate-slide-in\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `max-w-sm w-full rounded-xl border shadow-2xl backdrop-blur-sm ${getStyles()}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex-shrink-0 p-2 rounded-lg ${type === 'success' ? 'bg-emerald-100 text-emerald-600' : type === 'error' ? 'bg-red-100 text-red-600' : 'bg-blue-100 text-blue-600'}`,\n            children: getIcon()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-3 flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-semibold\",\n              children: title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm opacity-90\",\n              children: message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4 flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onClose,\n              className: `inline-flex rounded-lg p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 ${type === 'success' ? 'hover:bg-emerald-200 focus:ring-emerald-500' : type === 'error' ? 'hover:bg-red-200 focus:ring-red-500' : 'hover:bg-blue-200 focus:ring-blue-500'}`,\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 156,\n    columnNumber: 5\n  }, this);\n};\n\n// Full View Modal component\n_s(Notification, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = Notification;\nconst FullViewModal = ({\n  isOpen,\n  onClose,\n  previewHtml,\n  previewLoading\n}) => {\n  _s2();\n  const [zoom, setZoom] = useState(0.9); // 0.7 = 70%\n  const handleZoomIn = () => setZoom(z => Math.min(z + 0.1, 2));\n  const handleZoomOut = () => setZoom(z => Math.max(z - 0.1, 0.5));\n  useEffect(() => {\n    const handleEscape = e => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 z-50 overflow-y-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-75 transition-opacity\",\n      onClick: onClose\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex min-h-full items-center justify-center p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative w-full max-w-7xl h-[90vh] bg-white rounded-2xl shadow-2xl overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-blue-600 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 text-white\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-gray-900\",\n              children: \"Full Preview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleZoomOut,\n              title: \"Zoom Out\",\n              className: \"flex items-center justify-center p-2 rounded-lg bg-blue-100 hover:bg-blue-200 transition-colors shadow-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 text-blue-700\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M20 12H4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-blue-900 font-semibold px-2 select-none\",\n              children: [Math.round(zoom * 100), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleZoomIn,\n              title: \"Zoom In\",\n              className: \"flex items-center justify-center p-2 rounded-lg bg-blue-100 hover:bg-blue-200 transition-colors shadow-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 text-blue-700\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M12 4v16m8-8H4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onClose,\n              className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full overflow-y-auto bg-gray-50\",\n          children: previewLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center h-full\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-4 text-gray-600\",\n                children: \"Loading preview...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-5xl mx-auto p-8 flex flex-col items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-lg shadow-lg p-8\",\n              style: {\n                transform: `scale(${zoom})`,\n                transformOrigin: 'top center',\n                transition: 'transform 0.2s',\n                minWidth: '220mm',\n                width: 'auto',\n                display: 'inline-block'\n              },\n              dangerouslySetInnerHTML: {\n                __html: previewHtml\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 226,\n    columnNumber: 5\n  }, this);\n};\n\n// Add this mapping at the top of the file (after imports)\n_s2(FullViewModal, \"6GOlrIL9TArDyY1GpXwV7Ati7/M=\");\n_c2 = FullViewModal;\nconst ROLE_MAP = {\n  1: 'HACKER',\n  2: 'BUSINESS',\n  3: 'ADMIN',\n  4: 'QA',\n  5: 'SUB_ADMIN',\n  6: 'ADMIN_MANAGER'\n};\nconst MIN_EDITOR_WIDTH = 320; // px\nconst MAX_EDITOR_WIDTH = 700; // px\nconst DEFAULT_EDITOR_WIDTH = '50%'; // 50% of the container\n\n// Component\nconst PDFEditor = () => {\n  _s3();\n  const logic = usePDFEditorLogic();\n  const {\n    report_id,\n    role,\n    loading,\n    previewLoading,\n    previewHtml,\n    reportData,\n    activeSection,\n    setActiveSection,\n    sectionData,\n    saveLoading,\n    approveLoading,\n    rejectLoading,\n    notification,\n    setNotification,\n    isFullViewOpen,\n    setIsFullViewOpen,\n    isChangeLogOpen,\n    setIsChangeLogOpen,\n    changeLog,\n    setChangeLog,\n    changeLogLoading,\n    setChangeLogLoading,\n    previewMode,\n    setPreviewMode,\n    editorWidth,\n    setEditorWidth,\n    dragging,\n    viewMode,\n    setViewMode,\n    previewRef,\n    handlePrint,\n    showNotification,\n    hideNotification,\n    openFullView,\n    closeFullView,\n    openChangeLog,\n    closeChangeLog,\n    updatePreview,\n    debouncedUpdatePreview,\n    handleDataUpdate,\n    fetchReportData,\n    updateReportStatus,\n    handleSave,\n    handleApprove,\n    handleReject,\n    canApprove,\n    canReject,\n    handleInputChange,\n    handleHtmlChange,\n    handleDataChange,\n    handleFindingChange,\n    handleAddFinding,\n    handleRemoveFinding,\n    handleTargetDetailsChange,\n    handleAddTarget,\n    handleRemoveTarget,\n    handleTableChange,\n    handleKeyFindingChange,\n    handleAddKeyFinding,\n    handleRemoveKeyFinding,\n    handleRecommendationsChange,\n    saveSectionChanges,\n    currentData\n  } = logic;\n  const roleStr = useRoleString(role);\n  const [selectedChat, setSelectedChat] = useState('QA');\n  const [adminQaMessages, setAdminQaMessages] = useState([]);\n  const [adminBusinessMessages, setAdminBusinessMessages] = useState([]);\n\n  // Determine label for Admin/Sub Admin tab\n  let adminSubAdminLabel = 'Admin/Sub Admin';\n  if (roleStr.toUpperCase() === 'ADMIN') {\n    adminSubAdminLabel = 'Sub Admin';\n  } else if (roleStr.toUpperCase() === 'SUB_ADMIN') {\n    adminSubAdminLabel = 'Admin';\n  }\n  const handleSendAdminQa = msg => {\n    setAdminQaMessages(prev => [...prev, {\n      sender: roleStr,\n      text: msg,\n      timestamp: new Date().toISOString()\n    }]);\n  };\n  const handleSendAdminBusiness = msg => {\n    setAdminBusinessMessages(prev => [...prev, {\n      sender: roleStr,\n      text: msg,\n      timestamp: new Date().toISOString()\n    }]);\n  };\n  const [chatOpen, setChatOpen] = useState(false);\n\n  // Get currentUserId from user credentials\n  const {\n    id: currentUserId\n  } = useUserCredentials();\n\n  // For BUSINESS users, pass automated_report_id to ChatSection\n  let chatId = report_id || '';\n  if (roleStr.toUpperCase() === 'BUSINESS' && reportData && reportData.automated_report_id) {\n    chatId = reportData.automated_report_id;\n    if (report_id && report_id === chatId) {\n      // eslint-disable-next-line no-console\n      console.warn('BUSINESS user: programReportId should be automated_report.id, not program_report.id');\n    }\n  }\n\n  // Chat modal/button for all roles\n  const chatSection = /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"fixed bottom-6 right-6 z-50 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg w-16 h-16 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-blue-400\",\n      onClick: () => setChatOpen(open => !open),\n      \"aria-label\": chatOpen ? \"Close chat\" : \"Open chat\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"w-8 h-8\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          strokeWidth: 2,\n          d: \"M8 10h.01M12 10h.01M16 10h.01M21 12c0 4.418-4.03 8-9 8a9.77 9.77 0 01-4-.8l-4 1 1-3.2A7.96 7.96 0 013 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 431,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ChatModal, {\n      isOpen: chatOpen,\n      onClose: () => setChatOpen(false),\n      header: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between px-5 py-3 rounded-t-3xl bg-gradient-to-r from-blue-700 via-blue-500 to-blue-400 text-white shadow-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 rounded-full bg-white flex items-center justify-center shadow-md\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-7 h-7 text-blue-500\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M8 10h.01M12 10h.01M16 10h.01M21 12c0 4.418-4.03 8-9 8a9.77 9.77 0 01-4-.8l-4 1 1-3.2A7.96 7.96 0 013 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-bold text-lg leading-tight\",\n              children: \"Pentest Report Thread\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-1 text-xs text-blue-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-block w-2 h-2 bg-green-400 rounded-full mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 19\n              }, this), \"Online\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"p-2 rounded-full hover:bg-blue-500 text-white transition\",\n          onClick: () => setChatOpen(false),\n          \"aria-label\": \"Close chat\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 11\n      }, this),\n      tabs: ['ADMIN', 'SUB_ADMIN'].includes(roleStr.toUpperCase()) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sticky top-0 z-10 bg-white mb-3 flex justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex bg-gray-100 rounded-full p-1 shadow-sm gap-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: `px-4 py-1.5 rounded-full font-semibold text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400\n                  ${selectedChat === 'QA' ? 'bg-gradient-to-r from-blue-600 to-blue-500 text-white shadow-md' : 'bg-transparent text-blue-700 hover:bg-blue-100'}\n                `,\n            onClick: () => setSelectedChat('QA'),\n            tabIndex: 0,\n            children: \"QA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `px-4 py-1.5 rounded-full font-semibold text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400\n                  ${selectedChat === 'BUSINESS' ? 'bg-gradient-to-r from-green-500 to-teal-400 text-white shadow-md' : 'bg-transparent text-green-700 hover:bg-green-100'}\n                `,\n            onClick: () => setSelectedChat('BUSINESS'),\n            tabIndex: 0,\n            children: \"Business\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `px-4 py-1.5 rounded-full font-semibold text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400\n                  ${selectedChat === 'ADMIN_SUBADMIN' ? 'bg-gradient-to-r from-purple-600 to-violet-500 text-white shadow-md' : 'bg-transparent text-purple-700 hover:bg-purple-100'}\n                `,\n            onClick: () => setSelectedChat('ADMIN_SUBADMIN'),\n            tabIndex: 0,\n            children: adminSubAdminLabel\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 11\n      }, this),\n      children: ['ADMIN', 'SUB_ADMIN'].includes(roleStr.toUpperCase()) ? selectedChat === 'QA' ? /*#__PURE__*/_jsxDEV(ChatSection, {\n        programReportId: chatId,\n        chatType: \"admin_qa\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 13\n      }, this) : selectedChat === 'BUSINESS' ? /*#__PURE__*/_jsxDEV(ChatSection, {\n        programReportId: chatId,\n        chatType: \"admin_business\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(ChatSection, {\n        programReportId: chatId,\n        chatType: \"admin_subadmin\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 523,\n        columnNumber: 13\n      }, this) : roleStr.toUpperCase() === 'QA' ? /*#__PURE__*/_jsxDEV(ChatSection, {\n        programReportId: chatId,\n        chatType: \"admin_qa\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(ChatSection, {\n        programReportId: chatId,\n        chatType: \"admin_business\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n\n  // Confirmation modal state\n  const [approveModalOpen, setApproveModalOpen] = useState(false);\n  const [rejectModalOpen, setRejectModalOpen] = useState(false);\n  const handleApproveWithConfirm = () => setApproveModalOpen(true);\n  const handleRejectWithConfirm = () => setRejectModalOpen(true);\n  const confirmApprove = () => {\n    setApproveModalOpen(false);\n    handleApprove();\n  };\n  const confirmReject = () => {\n    setRejectModalOpen(false);\n    handleReject();\n  };\n\n  // Move all useState hooks to the top level, before any conditionals\n  const [businessActionLoading, setBusinessActionLoading] = useState(false);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-700 text-lg font-medium\",\n          children: \"Loading report data...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 563,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 7\n    }, this);\n  }\n  if (!reportData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-8 rounded-xl shadow-xl text-center max-w-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-500 mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-16 h-16 mx-auto\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: \"2\",\n              d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-800 mb-4\",\n          children: \"No Report Data Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Please check if the report ID is valid or try again later.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 573,\n      columnNumber: 7\n    }, this);\n  }\n  if (roleStr.toUpperCase() === 'BUSINESS') {\n    // Business action handlers\n    const handleBusinessApprove = async () => {\n      setBusinessActionLoading(true);\n      try {\n        await updateReportStatus('business_approved');\n        showNotification('success', 'Report Approved', 'You have approved the report.');\n        await fetchReportData();\n      } catch (e) {\n        showNotification('error', 'Error', 'Failed to approve report.');\n      } finally {\n        setBusinessActionLoading(false);\n      }\n    };\n    const handleBusinessRequestChanges = async () => {\n      setBusinessActionLoading(true);\n      try {\n        await updateReportStatus('business_requested_changes');\n        showNotification('info', 'Requested Changes', 'You have requested changes.');\n        await fetchReportData();\n      } catch (e) {\n        showNotification('error', 'Error', 'Failed to request changes.');\n      } finally {\n        setBusinessActionLoading(false);\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [chatSection, /*#__PURE__*/_jsxDEV(PreviewOnly, {\n        reportData: reportData,\n        currentData: currentData,\n        openFullView: openFullView,\n        previewMode: previewMode || 'full',\n        setPreviewMode: mode => setPreviewMode(mode),\n        previewHtml: previewHtml,\n        previewRef: previewRef,\n        isFullViewOpen: isFullViewOpen,\n        closeFullView: closeFullView,\n        notification: notification,\n        hideNotification: hideNotification,\n        isChangeLogOpen: isChangeLogOpen,\n        closeChangeLog: closeChangeLog,\n        changeLog: changeLog,\n        changeLogLoading: changeLogLoading,\n        previewLoading: previewLoading,\n        onBusinessApprove: handleBusinessApprove,\n        onBusinessRequestChanges: handleBusinessRequestChanges,\n        businessActionLoading: businessActionLoading,\n        isBusinessUser: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 616,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(EditorLayout, {\n      currentData: currentData,\n      reportData: reportData,\n      editorWidth: editorWidth,\n      dragging: dragging,\n      setEditorWidth: setEditorWidth,\n      activeSection: activeSection,\n      setActiveSection: setActiveSection,\n      openChangeLog: openChangeLog,\n      handleSave: handleSave,\n      saveLoading: saveLoading,\n      approveLoading: approveLoading,\n      rejectLoading: rejectLoading,\n      canApprove: canApprove,\n      handleApprove: handleApproveWithConfirm,\n      canReject: canReject,\n      handleReject: handleRejectWithConfirm,\n      reportDataStatus: reportData.status,\n      handleInputChange: handleInputChange,\n      handleHtmlChange: handleHtmlChange,\n      handleDataChange: handleDataChange,\n      handleFindingChange: handleFindingChange,\n      handleRemoveFinding: handleRemoveFinding,\n      handleAddFinding: handleAddFinding,\n      handleTargetDetailsChange: handleTargetDetailsChange,\n      handleAddTarget: handleAddTarget,\n      handleRemoveTarget: handleRemoveTarget,\n      handleTableChange: handleTableChange,\n      handleKeyFindingChange: handleKeyFindingChange,\n      handleAddKeyFinding: handleAddKeyFinding,\n      handleRemoveKeyFinding: handleRemoveKeyFinding,\n      handleRecommendationsChange: handleRecommendationsChange,\n      saveSectionChanges: saveSectionChanges,\n      previewRef: previewRef,\n      openFullView: openFullView,\n      previewMode: previewMode || 'full',\n      setPreviewMode: mode => setPreviewMode(mode),\n      previewHtml: previewHtml,\n      isFullViewOpen: isFullViewOpen,\n      closeFullView: closeFullView,\n      notification: notification,\n      hideNotification: hideNotification,\n      isChangeLogOpen: isChangeLogOpen,\n      closeChangeLog: closeChangeLog,\n      changeLog: changeLog,\n      changeLogLoading: changeLogLoading,\n      previewLoading: previewLoading,\n      sectionData: sectionData,\n      children: ['ADMIN', 'QA', 'BUSINESS', 'SUB_ADMIN'].includes(roleStr.toUpperCase()) && chatSection\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 644,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n      isOpen: approveModalOpen,\n      onClose: () => setApproveModalOpen(false),\n      onConfirm: confirmApprove,\n      title: \"Approve Report?\",\n      description: \"Are you sure you want to approve this report? This action cannot be undone.\",\n      confirmText: \"Approve\",\n      cancelText: \"Cancel\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 695,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n      isOpen: rejectModalOpen,\n      onClose: () => setRejectModalOpen(false),\n      onConfirm: confirmReject,\n      title: \"Reject Report?\",\n      description: \"Are you sure you want to reject this report? This will send it back for editing.\",\n      confirmText: \"Reject\",\n      cancelText: \"Cancel\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 704,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s3(PDFEditor, \"GdN3Kq+KO+i5hoByz8mAgmUsPTg=\", false, function () {\n  return [usePDFEditorLogic, useRoleString, useUserCredentials];\n});\n_c3 = PDFEditor;\nexport default PDFEditor;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"Notification\");\n$RefreshReg$(_c2, \"FullViewModal\");\n$RefreshReg$(_c3, \"PDFEditor\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useUserCredentials", "PreviewOnly", "useRoleString", "EditorLayout", "usePDFEditorLogic", "ChatSection", "ChatModal", "ConfirmationModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PREVIEW_UPDATE_DELAY", "SEVERITY_CATEGORIES", "STATUS_TYPES", "normalizeSeverityCategory", "category", "normalized", "char<PERSON>t", "toUpperCase", "slice", "toLowerCase", "getDefaultDisclaimer", "companyName", "getDefaultOpenCloseCounts", "Critical", "Open", "Closed", "Total", "High", "Medium", "Low", "Notification", "type", "title", "message", "isVisible", "onClose", "_s", "timer", "setTimeout", "clearTimeout", "getIcon", "className", "fill", "stroke", "viewBox", "children", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStyles", "onClick", "_c", "FullViewModal", "isOpen", "previewHtml", "previewLoading", "_s2", "zoom", "setZoom", "handleZoomIn", "z", "Math", "min", "handleZoomOut", "max", "handleEscape", "e", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "round", "transform", "transform<PERSON><PERSON>in", "transition", "min<PERSON><PERSON><PERSON>", "width", "display", "dangerouslySetInnerHTML", "__html", "_c2", "ROLE_MAP", "MIN_EDITOR_WIDTH", "MAX_EDITOR_WIDTH", "DEFAULT_EDITOR_WIDTH", "PDFEditor", "_s3", "logic", "report_id", "role", "loading", "reportData", "activeSection", "setActiveSection", "sectionData", "saveLoading", "approveLoading", "rejectLoading", "notification", "setNotification", "isFullViewOpen", "setIsFullViewOpen", "isChangeLogOpen", "setIsChangeLogOpen", "changeLog", "setChangeLog", "changeLogLoading", "setChangeLogLoading", "previewMode", "setPreviewMode", "<PERSON><PERSON><PERSON><PERSON>", "setE<PERSON>orWidth", "dragging", "viewMode", "setViewMode", "previewRef", "handlePrint", "showNotification", "hideNotification", "openFullView", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openChangeLog", "closeChangeLog", "updatePreview", "debouncedUpdatePreview", "handleDataUpdate", "fetchReportData", "updateReportStatus", "handleSave", "handleApprove", "handleReject", "canApprove", "canReject", "handleInputChange", "handleHtmlChange", "handleDataChange", "handleFindingChange", "handleAddFinding", "handleRemoveFinding", "handleTargetDetailsChange", "handleAddTarget", "handleRemoveTarget", "handleTableChange", "handleKeyFindingChange", "handleAddKeyFinding", "handleRemoveKeyFinding", "handleRecommendationsChange", "saveSectionChanges", "currentData", "roleStr", "selectedC<PERSON>", "setSelectedChat", "adminQaMessages", "setAdminQaMessages", "adminBusinessMessages", "setAdminBusinessMessages", "adminSubAdminLabel", "handleSendAdminQa", "msg", "prev", "sender", "text", "timestamp", "Date", "toISOString", "handleSendAdminBusiness", "chatOpen", "setChatOpen", "id", "currentUserId", "chatId", "automated_report_id", "console", "warn", "chatSection", "open", "header", "tabs", "includes", "tabIndex", "programReportId", "chatType", "approveModalOpen", "setApproveModalOpen", "rejectModalOpen", "setRejectModalOpen", "handleApproveWithConfirm", "handleRejectWithConfirm", "confirmApprove", "confirmReject", "businessActionLoading", "setBusinessActionLoading", "handleBusinessApprove", "handleBusinessRequestChanges", "mode", "onBusinessApprove", "onBusinessRequestChanges", "isBusinessUser", "reportDataStatus", "status", "onConfirm", "description", "confirmText", "cancelText", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/jira-old/CTB-Frontend/src/pages/automated-report/PDFEditor.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';\r\nimport { useParams } from 'react-router-dom';\r\nimport { getProgramReportById, getProgramReportChangeLog } from '../../utils/api/endpoints/program-reports/program-reports';\r\nimport axios from '../../utils/api/axios';\r\nimport { ReportData } from './types/report.types';\r\nimport EditorNavigation from './components/EditorNavigation';\r\nimport PreviewSection from './components/PreviewSection';\r\nimport CoverPageEditor from './components/editors/CoverPageEditor';\r\nimport DocumentReferenceEditor from './components/editors/DocumentReferenceEditor';\r\nimport ExecutiveSummaryEditor from './components/editors/ExecutiveSummaryEditor';\r\nimport DisclaimerEditor from './components/editors/DisclaimerEditor';\r\nimport MethodologyEditor from './components/editors/MethodologyEditor';\r\nimport RecommendationsEditor from './components/editors/RecommendationsEditor';\r\nimport ConclusionEditor from './components/editors/ConclusionEditor';\r\nimport FindingsEditor from './components/editors/FindingsEditor';\r\nimport TargetDetailsEditor from './components/editors/TargetDetailsEditor';\r\nimport ProjectObjectivesEditor from './components/editors/ProjectObjectivesEditor';\r\nimport ScopeEditor from './components/editors/ScopeEditor';\r\nimport FindingsSummaryEditor from './components/editors/FindingsSummaryEditor';\r\nimport VulnerabilityRatingsEditor from './components/editors/VulnerabilityRatingsEditor';\r\nimport CriticalFindingsEditor from './components/editors/CriticalFindingsEditor';\r\nimport HighFindingsEditor from './components/editors/HighFindingsEditor';\r\nimport MediumFindingsEditor from './components/editors/MediumFindingsEditor';\r\nimport LowFindingsEditor from './components/editors/LowFindingsEditor';\r\nimport KeyFindingsEditor from './components/editors/KeyFindingsEditor';\r\nimport useUserCredentials from '../../utils/hooks/user/useUserCredentials';\r\nimport { UserRole } from '../../utils/api/endpoints/user/credentials';\r\nimport ChangeLogSidebar from './components/ChangeLogSidebar';\r\nimport ReportTemplate from './components/ReportTemplate';\r\nimport PDFDownloader from './components/PDFDownloader';\r\nimport { useReactToPrint } from 'react-to-print';\r\nimport './PDFEditor.css';\r\nimport './report-print.css';\r\n// Import custom-scrollbar class globally for chat scroll UX\r\n// If not present, add the .custom-scrollbar CSS to your global stylesheet (e.g., index.css or App.scss)\r\nimport PreviewOnly from './PreviewOnly';\r\nimport { useRoleString } from './hooks/useRoleString';\r\nimport EditorLayout from './EditorLayout';\r\nimport { usePDFEditorLogic } from './hooks/usePDFEditorLogic';\r\nimport ChatSection from './components/ChatSection';\r\nimport ChatModal from './components/ChatModal';\r\nimport ConfirmationModal from '../../components/common/ConfirmationModal';\r\n\r\n// Constants\r\nconst PREVIEW_UPDATE_DELAY = 500;\r\nconst SEVERITY_CATEGORIES = ['Critical', 'High', 'Medium', 'Low'] as const;\r\nconst STATUS_TYPES = ['Open', 'Closed', 'Total'] as const;\r\n\r\n// Types\r\ninterface SeverityCounts {\r\n  Open: number;\r\n  Closed: number;\r\n  Total: number;\r\n}\r\n\r\ninterface OpenCloseCounts {\r\n  Critical: SeverityCounts;\r\n  High: SeverityCounts;\r\n  Medium: SeverityCounts;\r\n  Low: SeverityCounts;\r\n}\r\n\r\ntype SeverityCategory = typeof SEVERITY_CATEGORIES[number];\r\ntype StatusType = typeof STATUS_TYPES[number];\r\n\r\n// Utility functions\r\nconst normalizeSeverityCategory = (category: string): SeverityCategory => {\r\n  const normalized = category.charAt(0).toUpperCase() + category.slice(1).toLowerCase();\r\n  return normalized as SeverityCategory;\r\n};\r\n\r\nconst getDefaultDisclaimer = (companyName: string) => `\r\n  <p>\r\n    Capture The Bug Ltd. has prepared this document exclusively for ${companyName}.\r\n    Copying, or modification of this document is strictly prohibited without Capture The Bug Ltd.'s written consent, \r\n    except for specific purposes when such permission is granted. This document is confidential and proprietary material \r\n    of Capture The Bug Ltd. and must always be treated as such, not to be disclosed to third parties without prior consent.\r\n  </p>\r\n  \r\n  <p>\r\n    The information within is provided 'as-is,' without any warranty. Vulnerability assessments offer a snapshot of \r\n    security at a specific moment and are not exhaustive risk assessments. New vulnerabilities may emerge post-assessment. \r\n    Thus, this report is a guide, not a definitive risk analysis.\r\n  </p>\r\n  \r\n  <p>\r\n    Capture The Bug Ltd. assumes no liability for any changes, omissions, or errors in this document. \r\n    Capture The Bug Ltd. shall not be liable for any damages, financial or otherwise, arising out of the use or misuse \r\n    of this report by any current employee of ${companyName} or any member of the general public.\r\n  </p>\r\n`;\r\n\r\nconst getDefaultOpenCloseCounts = (): OpenCloseCounts => ({\r\n  Critical: { Open: 0, Closed: 0, Total: 0 },\r\n  High: { Open: 0, Closed: 0, Total: 0 },\r\n  Medium: { Open: 0, Closed: 0, Total: 0 },\r\n  Low: { Open: 0, Closed: 0, Total: 0 }\r\n});\r\n\r\n// Notification component\r\ninterface NotificationProps {\r\n  type: 'success' | 'error' | 'info';\r\n  title: string;\r\n  message: string;\r\n  isVisible: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nconst Notification: React.FC<NotificationProps> = ({ type, title, message, isVisible, onClose }) => {\r\n  useEffect(() => {\r\n    if (isVisible) {\r\n      const timer = setTimeout(() => {\r\n        onClose();\r\n      }, 5000);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [isVisible, onClose]);\r\n\r\n  if (!isVisible) return null;\r\n\r\n  const getIcon = () => {\r\n    switch (type) {\r\n      case 'success':\r\n        return (\r\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\r\n          </svg>\r\n        );\r\n      case 'error':\r\n        return (\r\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\r\n          </svg>\r\n        );\r\n      case 'info':\r\n        return (\r\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n          </svg>\r\n        );\r\n    }\r\n  };\r\n\r\n  const getStyles = () => {\r\n    switch (type) {\r\n      case 'success':\r\n        return 'bg-gradient-to-r from-emerald-50 to-green-50 border-emerald-200 text-emerald-800';\r\n      case 'error':\r\n        return 'bg-gradient-to-r from-red-50 to-pink-50 border-red-200 text-red-800';\r\n      case 'info':\r\n        return 'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 text-blue-800';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed top-4 right-4 z-50 animate-slide-in\">\r\n      <div className={`max-w-sm w-full rounded-xl border shadow-2xl backdrop-blur-sm ${getStyles()}`}>\r\n        <div className=\"p-4\">\r\n          <div className=\"flex items-start\">\r\n            <div className={`flex-shrink-0 p-2 rounded-lg ${\r\n              type === 'success' ? 'bg-emerald-100 text-emerald-600' :\r\n              type === 'error' ? 'bg-red-100 text-red-600' :\r\n              'bg-blue-100 text-blue-600'\r\n            }`}>\r\n              {getIcon()}\r\n            </div>\r\n            <div className=\"ml-3 flex-1\">\r\n              <h3 className=\"text-sm font-semibold\">{title}</h3>\r\n              <p className=\"mt-1 text-sm opacity-90\">{message}</p>\r\n            </div>\r\n            <div className=\"ml-4 flex-shrink-0\">\r\n              <button\r\n                onClick={onClose}\r\n                className={`inline-flex rounded-lg p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 ${\r\n                  type === 'success' ? 'hover:bg-emerald-200 focus:ring-emerald-500' :\r\n                  type === 'error' ? 'hover:bg-red-200 focus:ring-red-500' :\r\n                  'hover:bg-blue-200 focus:ring-blue-500'\r\n                }`}\r\n              >\r\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\r\n                </svg>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Full View Modal component\r\ninterface FullViewModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  previewHtml: string;\r\n  previewLoading: boolean;\r\n}\r\n\r\nconst FullViewModal: React.FC<FullViewModalProps> = ({ isOpen, onClose, previewHtml, previewLoading }) => {\r\n  const [zoom, setZoom] = useState(0.9); // 0.7 = 70%\r\n  const handleZoomIn = () => setZoom(z => Math.min(z + 0.1, 2));\r\n  const handleZoomOut = () => setZoom(z => Math.max(z - 0.1, 0.5));\r\n\r\n  useEffect(() => {\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (e.key === 'Escape') {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      document.addEventListener('keydown', handleEscape);\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('keydown', handleEscape);\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, [isOpen, onClose]);\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n      {/* Backdrop */}\r\n      <div \r\n        className=\"fixed inset-0 bg-black bg-opacity-75 transition-opacity\"\r\n        onClick={onClose}\r\n      />\r\n      {/* Modal */}\r\n      <div className=\"flex min-h-full items-center justify-center p-4\">\r\n        <div className=\"relative w-full max-w-7xl h-[90vh] bg-white rounded-2xl shadow-2xl overflow-hidden\">\r\n          {/* Header */}\r\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"p-2 bg-blue-600 rounded-lg\">\r\n                <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\r\n                </svg>\r\n              </div>\r\n              <h2 className=\"text-xl font-bold text-gray-900\">Full Preview</h2>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <button\r\n                onClick={handleZoomOut}\r\n                title=\"Zoom Out\"\r\n                className=\"flex items-center justify-center p-2 rounded-lg bg-blue-100 hover:bg-blue-200 transition-colors shadow-sm\"\r\n              >\r\n                <svg className=\"w-5 h-5 text-blue-700\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 12H4\" />\r\n                </svg>\r\n              </button>\r\n              <span className=\"text-xs text-blue-900 font-semibold px-2 select-none\">{Math.round(zoom * 100)}%</span>\r\n              <button\r\n                onClick={handleZoomIn}\r\n                title=\"Zoom In\"\r\n                className=\"flex items-center justify-center p-2 rounded-lg bg-blue-100 hover:bg-blue-200 transition-colors shadow-sm\"\r\n              >\r\n                <svg className=\"w-5 h-5 text-blue-700\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\r\n                </svg>\r\n              </button>\r\n              <button\r\n                onClick={onClose}\r\n                className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\"\r\n              >\r\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                </svg>\r\n              </button>\r\n            </div>\r\n          </div>\r\n          {/* Content */}\r\n          <div className=\"h-full overflow-y-auto bg-gray-50\">\r\n            {previewLoading ? (\r\n              <div className=\"flex items-center justify-center h-full\">\r\n                <div className=\"flex flex-col items-center\">\r\n                  <div className=\"animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-600\"></div>\r\n                  <p className=\"mt-4 text-gray-600\">Loading preview...</p>\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <div className=\"max-w-5xl mx-auto p-8 flex flex-col items-center\">\r\n                <div\r\n                  className=\"bg-white rounded-lg shadow-lg p-8\"\r\n                  style={{\r\n                    transform: `scale(${zoom})`,\r\n                    transformOrigin: 'top center',\r\n                    transition: 'transform 0.2s',\r\n                    minWidth: '220mm',\r\n                    width: 'auto',\r\n                    display: 'inline-block'\r\n                  }}\r\n                  dangerouslySetInnerHTML={{ __html: previewHtml }}\r\n                />\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Add this mapping at the top of the file (after imports)\r\nconst ROLE_MAP: Record<number, string> = {\r\n  1: 'HACKER',\r\n  2: 'BUSINESS',\r\n  3: 'ADMIN',\r\n  4: 'QA',\r\n  5: 'SUB_ADMIN',\r\n  6: 'ADMIN_MANAGER',\r\n};\r\n\r\nconst MIN_EDITOR_WIDTH = 320; // px\r\nconst MAX_EDITOR_WIDTH = 700; // px\r\nconst DEFAULT_EDITOR_WIDTH = '50%'; // 50% of the container\r\n\r\n// Component\r\nconst PDFEditor: React.FC = () => {\r\n  const logic = usePDFEditorLogic();\r\n  const {\r\n    report_id,\r\n    role,\r\n    loading,\r\n    previewLoading,\r\n    previewHtml,\r\n    reportData,\r\n    activeSection,\r\n    setActiveSection,\r\n    sectionData,\r\n    saveLoading,\r\n    approveLoading,\r\n    rejectLoading,\r\n    notification,\r\n    setNotification,\r\n    isFullViewOpen,\r\n    setIsFullViewOpen,\r\n    isChangeLogOpen,\r\n    setIsChangeLogOpen,\r\n    changeLog,\r\n    setChangeLog,\r\n    changeLogLoading,\r\n    setChangeLogLoading,\r\n    previewMode,\r\n    setPreviewMode,\r\n    editorWidth,\r\n    setEditorWidth,\r\n    dragging,\r\n    viewMode,\r\n    setViewMode,\r\n    previewRef,\r\n    handlePrint,\r\n    showNotification,\r\n    hideNotification,\r\n    openFullView,\r\n    closeFullView,\r\n    openChangeLog,\r\n    closeChangeLog,\r\n    updatePreview,\r\n    debouncedUpdatePreview,\r\n    handleDataUpdate,\r\n    fetchReportData,\r\n    updateReportStatus,\r\n    handleSave,\r\n    handleApprove,\r\n    handleReject,\r\n    canApprove,\r\n    canReject,\r\n    handleInputChange,\r\n    handleHtmlChange,\r\n    handleDataChange,\r\n    handleFindingChange,\r\n    handleAddFinding,\r\n    handleRemoveFinding,\r\n    handleTargetDetailsChange,\r\n    handleAddTarget,\r\n    handleRemoveTarget,\r\n    handleTableChange,\r\n    handleKeyFindingChange,\r\n    handleAddKeyFinding,\r\n    handleRemoveKeyFinding,\r\n    handleRecommendationsChange,\r\n    saveSectionChanges,\r\n    currentData,\r\n  } = logic;\r\n\r\n  const roleStr = useRoleString(role);\r\n\r\n  const [selectedChat, setSelectedChat] = useState<'QA' | 'BUSINESS' | 'ADMIN_SUBADMIN'>('QA');\r\n  const [adminQaMessages, setAdminQaMessages] = useState<{ sender: string; text: string; timestamp: string }[]>([]);\r\n  const [adminBusinessMessages, setAdminBusinessMessages] = useState<{ sender: string; text: string; timestamp: string }[]>([]);\r\n\r\n  // Determine label for Admin/Sub Admin tab\r\n  let adminSubAdminLabel = 'Admin/Sub Admin';\r\n  if (roleStr.toUpperCase() === 'ADMIN') {\r\n    adminSubAdminLabel = 'Sub Admin';\r\n  } else if (roleStr.toUpperCase() === 'SUB_ADMIN') {\r\n    adminSubAdminLabel = 'Admin';\r\n  }\r\n\r\n  const handleSendAdminQa = (msg: string) => {\r\n    setAdminQaMessages(prev => [...prev, { sender: roleStr, text: msg, timestamp: new Date().toISOString() }]);\r\n  };\r\n  const handleSendAdminBusiness = (msg: string) => {\r\n    setAdminBusinessMessages(prev => [...prev, { sender: roleStr, text: msg, timestamp: new Date().toISOString() }]);\r\n  };\r\n\r\n  const [chatOpen, setChatOpen] = useState(false);\r\n\r\n  // Get currentUserId from user credentials\r\n  const { id: currentUserId } = useUserCredentials();\r\n\r\n  // For BUSINESS users, pass automated_report_id to ChatSection\r\n  let chatId = report_id || '';\r\n  if (roleStr.toUpperCase() === 'BUSINESS' && reportData && reportData.automated_report_id) {\r\n    chatId = reportData.automated_report_id;\r\n    if (report_id && report_id === chatId) {\r\n      // eslint-disable-next-line no-console\r\n      console.warn('BUSINESS user: programReportId should be automated_report.id, not program_report.id');\r\n    }\r\n  }\r\n\r\n  // Chat modal/button for all roles\r\n  const chatSection = (\r\n    <>\r\n      {/* Floating chat button */}\r\n      <button\r\n        className=\"fixed bottom-6 right-6 z-50 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg w-16 h-16 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-blue-400\"\r\n        onClick={() => setChatOpen(open => !open)}\r\n        aria-label={chatOpen ? \"Close chat\" : \"Open chat\"}\r\n      >\r\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 10h.01M12 10h.01M16 10h.01M21 12c0 4.418-4.03 8-9 8a9.77 9.77 0 01-4-.8l-4 1 1-3.2A7.96 7.96 0 013 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\r\n        </svg>\r\n      </button>\r\n      {/* Chat modal */}\r\n      <ChatModal\r\n        isOpen={chatOpen}\r\n        onClose={() => setChatOpen(false)}\r\n        header={\r\n          <div className=\"flex items-center justify-between px-5 py-3 rounded-t-3xl bg-gradient-to-r from-blue-700 via-blue-500 to-blue-400 text-white shadow-md\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"w-10 h-10 rounded-full bg-white flex items-center justify-center shadow-md\">\r\n                <svg className=\"w-7 h-7 text-blue-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 10h.01M12 10h.01M16 10h.01M21 12c0 4.418-4.03 8-9 8a9.77 9.77 0 01-4-.8l-4 1 1-3.2A7.96 7.96 0 013 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\r\n                </svg>\r\n              </div>\r\n              <div>\r\n                <div className=\"font-bold text-lg leading-tight\">Pentest Report Thread</div>\r\n                <div className=\"flex items-center gap-1 text-xs text-blue-100\">\r\n                  <span className=\"inline-block w-2 h-2 bg-green-400 rounded-full mr-1\"></span>\r\n                  Online\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <button\r\n              className=\"p-2 rounded-full hover:bg-blue-500 text-white transition\"\r\n              onClick={() => setChatOpen(false)}\r\n              aria-label=\"Close chat\"\r\n            >\r\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        }\r\n        tabs={['ADMIN', 'SUB_ADMIN'].includes(roleStr.toUpperCase()) && (\r\n          <div className=\"sticky top-0 z-10 bg-white mb-3 flex justify-center\">\r\n            <div className=\"inline-flex bg-gray-100 rounded-full p-1 shadow-sm gap-1\">\r\n              <button\r\n                className={`px-4 py-1.5 rounded-full font-semibold text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400\r\n                  ${selectedChat === 'QA'\r\n                    ? 'bg-gradient-to-r from-blue-600 to-blue-500 text-white shadow-md'\r\n                    : 'bg-transparent text-blue-700 hover:bg-blue-100'}\r\n                `}\r\n                onClick={() => setSelectedChat('QA')}\r\n                tabIndex={0}\r\n              >\r\n                QA\r\n              </button>\r\n              <button\r\n                className={`px-4 py-1.5 rounded-full font-semibold text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400\r\n                  ${selectedChat === 'BUSINESS'\r\n                    ? 'bg-gradient-to-r from-green-500 to-teal-400 text-white shadow-md'\r\n                    : 'bg-transparent text-green-700 hover:bg-green-100'}\r\n                `}\r\n                onClick={() => setSelectedChat('BUSINESS')}\r\n                tabIndex={0}\r\n              >\r\n                Business\r\n              </button>\r\n              <button\r\n                className={`px-4 py-1.5 rounded-full font-semibold text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400\r\n                  ${selectedChat === 'ADMIN_SUBADMIN'\r\n                    ? 'bg-gradient-to-r from-purple-600 to-violet-500 text-white shadow-md'\r\n                    : 'bg-transparent text-purple-700 hover:bg-purple-100'}\r\n                `}\r\n                onClick={() => setSelectedChat('ADMIN_SUBADMIN')}\r\n                tabIndex={0}\r\n              >\r\n                {adminSubAdminLabel}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        )}\r\n      >\r\n        {['ADMIN', 'SUB_ADMIN'].includes(roleStr.toUpperCase()) ? (\r\n          selectedChat === 'QA' ? (\r\n            <ChatSection\r\n              programReportId={chatId}\r\n              chatType=\"admin_qa\"\r\n            />\r\n          ) : selectedChat === 'BUSINESS' ? (\r\n            <ChatSection\r\n              programReportId={chatId}\r\n              chatType=\"admin_business\"\r\n            />\r\n          ) : (\r\n            <ChatSection\r\n              programReportId={chatId}\r\n              chatType=\"admin_subadmin\"\r\n            />\r\n          )\r\n        ) : roleStr.toUpperCase() === 'QA' ? (\r\n          <ChatSection\r\n            programReportId={chatId}\r\n            chatType=\"admin_qa\"\r\n          />\r\n        ) : (\r\n          <ChatSection\r\n            programReportId={chatId}\r\n            chatType=\"admin_business\"\r\n          />\r\n        )}\r\n      </ChatModal>\r\n    </>\r\n  );\r\n\r\n  // Confirmation modal state\r\n  const [approveModalOpen, setApproveModalOpen] = useState(false);\r\n  const [rejectModalOpen, setRejectModalOpen] = useState(false);\r\n  const handleApproveWithConfirm = () => setApproveModalOpen(true);\r\n  const handleRejectWithConfirm = () => setRejectModalOpen(true);\r\n  const confirmApprove = () => {\r\n    setApproveModalOpen(false);\r\n    handleApprove();\r\n  };\r\n  const confirmReject = () => {\r\n    setRejectModalOpen(false);\r\n    handleReject();\r\n  };\r\n\r\n  // Move all useState hooks to the top level, before any conditionals\r\n  const [businessActionLoading, setBusinessActionLoading] = useState(false);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100\">\r\n        <div className=\"flex flex-col items-center\">\r\n          <div className=\"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-600\"></div>\r\n          <p className=\"mt-4 text-gray-700 text-lg font-medium\">Loading report data...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!reportData) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100\">\r\n        <div className=\"bg-white p-8 rounded-xl shadow-xl text-center max-w-md\">\r\n          <div className=\"text-red-500 mb-4\">\r\n            <svg className=\"w-16 h-16 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\" />\r\n            </svg>\r\n          </div>\r\n          <h2 className=\"text-2xl font-bold text-gray-800 mb-4\">No Report Data Available</h2>\r\n          <p className=\"text-gray-600\">Please check if the report ID is valid or try again later.</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (roleStr.toUpperCase() === 'BUSINESS') {\r\n    // Business action handlers\r\n    const handleBusinessApprove = async () => {\r\n      setBusinessActionLoading(true);\r\n      try {\r\n        await updateReportStatus('business_approved');\r\n        showNotification('success', 'Report Approved', 'You have approved the report.');\r\n        await fetchReportData();\r\n      } catch (e) {\r\n        showNotification('error', 'Error', 'Failed to approve report.');\r\n      } finally {\r\n        setBusinessActionLoading(false);\r\n      }\r\n    };\r\n    const handleBusinessRequestChanges = async () => {\r\n      setBusinessActionLoading(true);\r\n      try {\r\n        await updateReportStatus('business_requested_changes');\r\n        showNotification('info', 'Requested Changes', 'You have requested changes.');\r\n        await fetchReportData();\r\n      } catch (e) {\r\n        showNotification('error', 'Error', 'Failed to request changes.');\r\n      } finally {\r\n        setBusinessActionLoading(false);\r\n      }\r\n    };\r\n    return (\r\n      <>\r\n        {chatSection}\r\n        <PreviewOnly\r\n          reportData={reportData}\r\n          currentData={currentData}\r\n          openFullView={openFullView}\r\n          previewMode={previewMode || 'full'}\r\n          setPreviewMode={mode => setPreviewMode(mode as 'full' | 'technical')}\r\n          previewHtml={previewHtml}\r\n          previewRef={previewRef}\r\n          isFullViewOpen={isFullViewOpen}\r\n          closeFullView={closeFullView}\r\n          notification={notification}\r\n          hideNotification={hideNotification}\r\n          isChangeLogOpen={isChangeLogOpen}\r\n          closeChangeLog={closeChangeLog}\r\n          changeLog={changeLog}\r\n          changeLogLoading={changeLogLoading}\r\n          previewLoading={previewLoading}\r\n          onBusinessApprove={handleBusinessApprove}\r\n          onBusinessRequestChanges={handleBusinessRequestChanges}\r\n          businessActionLoading={businessActionLoading}\r\n          isBusinessUser={true}\r\n        />\r\n      </>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <EditorLayout\r\n        currentData={currentData}\r\n        reportData={reportData}\r\n        editorWidth={editorWidth}\r\n        dragging={dragging}\r\n        setEditorWidth={setEditorWidth}\r\n        activeSection={activeSection}\r\n        setActiveSection={setActiveSection}\r\n        openChangeLog={openChangeLog}\r\n        handleSave={handleSave}\r\n        saveLoading={saveLoading}\r\n        approveLoading={approveLoading}\r\n        rejectLoading={rejectLoading}\r\n        canApprove={canApprove}\r\n        handleApprove={handleApproveWithConfirm}\r\n        canReject={canReject}\r\n        handleReject={handleRejectWithConfirm}\r\n        reportDataStatus={reportData.status}\r\n        handleInputChange={handleInputChange}\r\n        handleHtmlChange={handleHtmlChange}\r\n        handleDataChange={handleDataChange}\r\n        handleFindingChange={handleFindingChange}\r\n        handleRemoveFinding={handleRemoveFinding}\r\n        handleAddFinding={handleAddFinding}\r\n        handleTargetDetailsChange={handleTargetDetailsChange}\r\n        handleAddTarget={handleAddTarget}\r\n        handleRemoveTarget={handleRemoveTarget}\r\n        handleTableChange={handleTableChange}\r\n        handleKeyFindingChange={handleKeyFindingChange}\r\n        handleAddKeyFinding={handleAddKeyFinding}\r\n        handleRemoveKeyFinding={handleRemoveKeyFinding}\r\n        handleRecommendationsChange={handleRecommendationsChange}\r\n        saveSectionChanges={saveSectionChanges}\r\n        previewRef={previewRef}\r\n        openFullView={openFullView}\r\n        previewMode={previewMode || 'full'}\r\n        setPreviewMode={mode => setPreviewMode(mode as 'full' | 'technical')}\r\n        previewHtml={previewHtml}\r\n        isFullViewOpen={isFullViewOpen}\r\n        closeFullView={closeFullView}\r\n        notification={notification}\r\n        hideNotification={hideNotification}\r\n        isChangeLogOpen={isChangeLogOpen}\r\n        closeChangeLog={closeChangeLog}\r\n        changeLog={changeLog}\r\n        changeLogLoading={changeLogLoading}\r\n        previewLoading={previewLoading}\r\n        sectionData={sectionData}\r\n      >\r\n        {['ADMIN', 'QA', 'BUSINESS', 'SUB_ADMIN'].includes(roleStr.toUpperCase()) && chatSection}\r\n      </EditorLayout>\r\n      <ConfirmationModal\r\n        isOpen={approveModalOpen}\r\n        onClose={() => setApproveModalOpen(false)}\r\n        onConfirm={confirmApprove}\r\n        title=\"Approve Report?\"\r\n        description=\"Are you sure you want to approve this report? This action cannot be undone.\"\r\n        confirmText=\"Approve\"\r\n        cancelText=\"Cancel\"\r\n      />\r\n      <ConfirmationModal\r\n        isOpen={rejectModalOpen}\r\n        onClose={() => setRejectModalOpen(false)}\r\n        onConfirm={confirmReject}\r\n        title=\"Reject Report?\"\r\n        description=\"Are you sure you want to reject this report? This will send it back for editing.\"\r\n        confirmText=\"Reject\"\r\n        cancelText=\"Cancel\"\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default PDFEditor;"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAsC,OAAO;AAyBhF,OAAOC,kBAAkB,MAAM,2CAA2C;AAM1E,OAAO,iBAAiB;AACxB,OAAO,oBAAoB;AAC3B;AACA;AACA,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,aAAa,QAAQ,uBAAuB;AACrD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,iBAAiB,MAAM,2CAA2C;;AAEzE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,oBAAoB,GAAG,GAAG;AAChC,MAAMC,mBAAmB,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAU;AAC1E,MAAMC,YAAY,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAU;;AAEzD;;AAiBA;AACA,MAAMC,yBAAyB,GAAIC,QAAgB,IAAuB;EACxE,MAAMC,UAAU,GAAGD,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGH,QAAQ,CAACI,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EACrF,OAAOJ,UAAU;AACnB,CAAC;AAED,MAAMK,oBAAoB,GAAIC,WAAmB,IAAM;AACvD;AACA,sEAAsEA,WAAY;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgDA,WAAY;AAC5D;AACA,CAAC;AAED,MAAMC,yBAAyB,GAAGA,CAAA,MAAwB;EACxDC,QAAQ,EAAE;IAAEC,IAAI,EAAE,CAAC;IAAEC,MAAM,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAE,CAAC;EAC1CC,IAAI,EAAE;IAAEH,IAAI,EAAE,CAAC;IAAEC,MAAM,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAE,CAAC;EACtCE,MAAM,EAAE;IAAEJ,IAAI,EAAE,CAAC;IAAEC,MAAM,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAE,CAAC;EACxCG,GAAG,EAAE;IAAEL,IAAI,EAAE,CAAC;IAAEC,MAAM,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAE;AACtC,CAAC,CAAC;;AAEF;;AASA,MAAMI,YAAyC,GAAGA,CAAC;EAAEC,IAAI;EAAEC,KAAK;EAAEC,OAAO;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAClGvC,SAAS,CAAC,MAAM;IACd,IAAIqC,SAAS,EAAE;MACb,MAAMG,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BH,OAAO,CAAC,CAAC;MACX,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMI,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACH,SAAS,EAAEC,OAAO,CAAC,CAAC;EAExB,IAAI,CAACD,SAAS,EAAE,OAAO,IAAI;EAE3B,MAAMM,OAAO,GAAGA,CAAA,KAAM;IACpB,QAAQT,IAAI;MACV,KAAK,SAAS;QACZ,oBACExB,OAAA;UAAKkC,SAAS,EAAC,SAAS;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAC,QAAA,eAC5EtC,OAAA;YAAMuC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAC,GAAG;YAACC,CAAC,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC;MAEV,KAAK,OAAO;QACV,oBACE9C,OAAA;UAAKkC,SAAS,EAAC,SAAS;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAC,QAAA,eAC5EtC,OAAA;YAAMuC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAC,GAAG;YAACC,CAAC,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3F,CAAC;MAEV,KAAK,MAAM;QACT,oBACE9C,OAAA;UAAKkC,SAAS,EAAC,SAAS;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAC,QAAA,eAC5EtC,OAAA;YAAMuC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAC,GAAG;YAACC,CAAC,EAAC;UAA2D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChI,CAAC;IAEZ;EACF,CAAC;EAED,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACtB,QAAQvB,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,kFAAkF;MAC3F,KAAK,OAAO;QACV,OAAO,qEAAqE;MAC9E,KAAK,MAAM;QACT,OAAO,0EAA0E;IACrF;EACF,CAAC;EAED,oBACExB,OAAA;IAAKkC,SAAS,EAAC,2CAA2C;IAAAI,QAAA,eACxDtC,OAAA;MAAKkC,SAAS,EAAG,iEAAgEa,SAAS,CAAC,CAAE,EAAE;MAAAT,QAAA,eAC7FtC,OAAA;QAAKkC,SAAS,EAAC,KAAK;QAAAI,QAAA,eAClBtC,OAAA;UAAKkC,SAAS,EAAC,kBAAkB;UAAAI,QAAA,gBAC/BtC,OAAA;YAAKkC,SAAS,EAAG,gCACfV,IAAI,KAAK,SAAS,GAAG,iCAAiC,GACtDA,IAAI,KAAK,OAAO,GAAG,yBAAyB,GAC5C,2BACD,EAAE;YAAAc,QAAA,EACAL,OAAO,CAAC;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eACN9C,OAAA;YAAKkC,SAAS,EAAC,aAAa;YAAAI,QAAA,gBAC1BtC,OAAA;cAAIkC,SAAS,EAAC,uBAAuB;cAAAI,QAAA,EAAEb;YAAK;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClD9C,OAAA;cAAGkC,SAAS,EAAC,yBAAyB;cAAAI,QAAA,EAAEZ;YAAO;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACN9C,OAAA;YAAKkC,SAAS,EAAC,oBAAoB;YAAAI,QAAA,eACjCtC,OAAA;cACEgD,OAAO,EAAEpB,OAAQ;cACjBM,SAAS,EAAG,oFACVV,IAAI,KAAK,SAAS,GAAG,6CAA6C,GAClEA,IAAI,KAAK,OAAO,GAAG,qCAAqC,GACxD,uCACD,EAAE;cAAAc,QAAA,eAEHtC,OAAA;gBAAKkC,SAAS,EAAC,SAAS;gBAACC,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAC,QAAA,eAC5EtC,OAAA;kBAAMuC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAjB,EAAA,CAnFMN,YAAyC;AAAA0B,EAAA,GAAzC1B,YAAyC;AA2F/C,MAAM2B,aAA2C,GAAGA,CAAC;EAAEC,MAAM;EAAEvB,OAAO;EAAEwB,WAAW;EAAEC;AAAe,CAAC,KAAK;EAAAC,GAAA;EACxG,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGnE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;EACvC,MAAMoE,YAAY,GAAGA,CAAA,KAAMD,OAAO,CAACE,CAAC,IAAIC,IAAI,CAACC,GAAG,CAACF,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;EAC7D,MAAMG,aAAa,GAAGA,CAAA,KAAML,OAAO,CAACE,CAAC,IAAIC,IAAI,CAACG,GAAG,CAACJ,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;EAEhEpE,SAAS,CAAC,MAAM;IACd,MAAMyE,YAAY,GAAIC,CAAgB,IAAK;MACzC,IAAIA,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;QACtBrC,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAED,IAAIuB,MAAM,EAAE;MACVe,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,YAAY,CAAC;MAClDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC;IAEA,OAAO,MAAM;MACXJ,QAAQ,CAACK,mBAAmB,CAAC,SAAS,EAAER,YAAY,CAAC;MACrDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACnB,MAAM,EAAEvB,OAAO,CAAC,CAAC;EAErB,IAAI,CAACuB,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEnD,OAAA;IAAKkC,SAAS,EAAC,oCAAoC;IAAAI,QAAA,gBAEjDtC,OAAA;MACEkC,SAAS,EAAC,yDAAyD;MACnEc,OAAO,EAAEpB;IAAQ;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAEF9C,OAAA;MAAKkC,SAAS,EAAC,iDAAiD;MAAAI,QAAA,eAC9DtC,OAAA;QAAKkC,SAAS,EAAC,oFAAoF;QAAAI,QAAA,gBAEjGtC,OAAA;UAAKkC,SAAS,EAAC,0GAA0G;UAAAI,QAAA,gBACvHtC,OAAA;YAAKkC,SAAS,EAAC,yBAAyB;YAAAI,QAAA,gBACtCtC,OAAA;cAAKkC,SAAS,EAAC,4BAA4B;cAAAI,QAAA,eACzCtC,OAAA;gBAAKkC,SAAS,EAAC,oBAAoB;gBAACC,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAC,QAAA,gBACvFtC,OAAA;kBAAMuC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAkC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1G9C,OAAA;kBAAMuC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAyH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9C,OAAA;cAAIkC,SAAS,EAAC,iCAAiC;cAAAI,QAAA,EAAC;YAAY;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACN9C,OAAA;YAAKkC,SAAS,EAAC,yBAAyB;YAAAI,QAAA,gBACtCtC,OAAA;cACEgD,OAAO,EAAEa,aAAc;cACvBpC,KAAK,EAAC,UAAU;cAChBS,SAAS,EAAC,2GAA2G;cAAAI,QAAA,eAErHtC,OAAA;gBAAKkC,SAAS,EAAC,uBAAuB;gBAACC,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAC,QAAA,eAC1FtC,OAAA;kBAAMuC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACT9C,OAAA;cAAMkC,SAAS,EAAC,sDAAsD;cAAAI,QAAA,GAAEqB,IAAI,CAACa,KAAK,CAACjB,IAAI,GAAG,GAAG,CAAC,EAAC,GAAC;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvG9C,OAAA;cACEgD,OAAO,EAAES,YAAa;cACtBhC,KAAK,EAAC,SAAS;cACfS,SAAS,EAAC,2GAA2G;cAAAI,QAAA,eAErHtC,OAAA;gBAAKkC,SAAS,EAAC,uBAAuB;gBAACC,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAC,QAAA,eAC1FtC,OAAA;kBAAMuC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACT9C,OAAA;cACEgD,OAAO,EAAEpB,OAAQ;cACjBM,SAAS,EAAC,sFAAsF;cAAAI,QAAA,eAEhGtC,OAAA;gBAAKkC,SAAS,EAAC,SAAS;gBAACC,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAC,QAAA,eAC5EtC,OAAA;kBAAMuC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9C,OAAA;UAAKkC,SAAS,EAAC,mCAAmC;UAAAI,QAAA,EAC/Ce,cAAc,gBACbrD,OAAA;YAAKkC,SAAS,EAAC,yCAAyC;YAAAI,QAAA,eACtDtC,OAAA;cAAKkC,SAAS,EAAC,4BAA4B;cAAAI,QAAA,gBACzCtC,OAAA;gBAAKkC,SAAS,EAAC;cAA2E;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjG9C,OAAA;gBAAGkC,SAAS,EAAC,oBAAoB;gBAAAI,QAAA,EAAC;cAAkB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEN9C,OAAA;YAAKkC,SAAS,EAAC,kDAAkD;YAAAI,QAAA,eAC/DtC,OAAA;cACEkC,SAAS,EAAC,mCAAmC;cAC7CmC,KAAK,EAAE;gBACLI,SAAS,EAAG,SAAQlB,IAAK,GAAE;gBAC3BmB,eAAe,EAAE,YAAY;gBAC7BC,UAAU,EAAE,gBAAgB;gBAC5BC,QAAQ,EAAE,OAAO;gBACjBC,KAAK,EAAE,MAAM;gBACbC,OAAO,EAAE;cACX,CAAE;cACFC,uBAAuB,EAAE;gBAAEC,MAAM,EAAE5B;cAAY;YAAE;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAQ,GAAA,CA5GMJ,aAA2C;AAAA+B,GAAA,GAA3C/B,aAA2C;AA6GjD,MAAMgC,QAAgC,GAAG;EACvC,CAAC,EAAE,QAAQ;EACX,CAAC,EAAE,UAAU;EACb,CAAC,EAAE,OAAO;EACV,CAAC,EAAE,IAAI;EACP,CAAC,EAAE,WAAW;EACd,CAAC,EAAE;AACL,CAAC;AAED,MAAMC,gBAAgB,GAAG,GAAG,CAAC,CAAC;AAC9B,MAAMC,gBAAgB,GAAG,GAAG,CAAC,CAAC;AAC9B,MAAMC,oBAAoB,GAAG,KAAK,CAAC,CAAC;;AAEpC;AACA,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAChC,MAAMC,KAAK,GAAG7F,iBAAiB,CAAC,CAAC;EACjC,MAAM;IACJ8F,SAAS;IACTC,IAAI;IACJC,OAAO;IACPtC,cAAc;IACdD,WAAW;IACXwC,UAAU;IACVC,aAAa;IACbC,gBAAgB;IAChBC,WAAW;IACXC,WAAW;IACXC,cAAc;IACdC,aAAa;IACbC,YAAY;IACZC,eAAe;IACfC,cAAc;IACdC,iBAAiB;IACjBC,eAAe;IACfC,kBAAkB;IAClBC,SAAS;IACTC,YAAY;IACZC,gBAAgB;IAChBC,mBAAmB;IACnBC,WAAW;IACXC,cAAc;IACdC,WAAW;IACXC,cAAc;IACdC,QAAQ;IACRC,QAAQ;IACRC,WAAW;IACXC,UAAU;IACVC,WAAW;IACXC,gBAAgB;IAChBC,gBAAgB;IAChBC,YAAY;IACZC,aAAa;IACbC,aAAa;IACbC,cAAc;IACdC,aAAa;IACbC,sBAAsB;IACtBC,gBAAgB;IAChBC,eAAe;IACfC,kBAAkB;IAClBC,UAAU;IACVC,aAAa;IACbC,YAAY;IACZC,UAAU;IACVC,SAAS;IACTC,iBAAiB;IACjBC,gBAAgB;IAChBC,gBAAgB;IAChBC,mBAAmB;IACnBC,gBAAgB;IAChBC,mBAAmB;IACnBC,yBAAyB;IACzBC,eAAe;IACfC,kBAAkB;IAClBC,iBAAiB;IACjBC,sBAAsB;IACtBC,mBAAmB;IACnBC,sBAAsB;IACtBC,2BAA2B;IAC3BC,kBAAkB;IAClBC;EACF,CAAC,GAAG7D,KAAK;EAET,MAAM8D,OAAO,GAAG7J,aAAa,CAACiG,IAAI,CAAC;EAEnC,MAAM,CAAC6D,YAAY,EAAEC,eAAe,CAAC,GAAGnK,QAAQ,CAAuC,IAAI,CAAC;EAC5F,MAAM,CAACoK,eAAe,EAAEC,kBAAkB,CAAC,GAAGrK,QAAQ,CAAwD,EAAE,CAAC;EACjH,MAAM,CAACsK,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGvK,QAAQ,CAAwD,EAAE,CAAC;;EAE7H;EACA,IAAIwK,kBAAkB,GAAG,iBAAiB;EAC1C,IAAIP,OAAO,CAAC5I,WAAW,CAAC,CAAC,KAAK,OAAO,EAAE;IACrCmJ,kBAAkB,GAAG,WAAW;EAClC,CAAC,MAAM,IAAIP,OAAO,CAAC5I,WAAW,CAAC,CAAC,KAAK,WAAW,EAAE;IAChDmJ,kBAAkB,GAAG,OAAO;EAC9B;EAEA,MAAMC,iBAAiB,GAAIC,GAAW,IAAK;IACzCL,kBAAkB,CAACM,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAAEC,MAAM,EAAEX,OAAO;MAAEY,IAAI,EAAEH,GAAG;MAAEI,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC,CAAC;EAC5G,CAAC;EACD,MAAMC,uBAAuB,GAAIP,GAAW,IAAK;IAC/CH,wBAAwB,CAACI,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAAEC,MAAM,EAAEX,OAAO;MAAEY,IAAI,EAAEH,GAAG;MAAEI,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC,CAAC;EAClH,CAAC;EAED,MAAM,CAACE,QAAQ,EAAEC,WAAW,CAAC,GAAGnL,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACA,MAAM;IAAEoL,EAAE,EAAEC;EAAc,CAAC,GAAGnL,kBAAkB,CAAC,CAAC;;EAElD;EACA,IAAIoL,MAAM,GAAGlF,SAAS,IAAI,EAAE;EAC5B,IAAI6D,OAAO,CAAC5I,WAAW,CAAC,CAAC,KAAK,UAAU,IAAIkF,UAAU,IAAIA,UAAU,CAACgF,mBAAmB,EAAE;IACxFD,MAAM,GAAG/E,UAAU,CAACgF,mBAAmB;IACvC,IAAInF,SAAS,IAAIA,SAAS,KAAKkF,MAAM,EAAE;MACrC;MACAE,OAAO,CAACC,IAAI,CAAC,qFAAqF,CAAC;IACrG;EACF;;EAEA;EACA,MAAMC,WAAW,gBACf/K,OAAA,CAAAE,SAAA;IAAAoC,QAAA,gBAEEtC,OAAA;MACEkC,SAAS,EAAC,4LAA4L;MACtMc,OAAO,EAAEA,CAAA,KAAMwH,WAAW,CAACQ,IAAI,IAAI,CAACA,IAAI,CAAE;MAC1C,cAAYT,QAAQ,GAAG,YAAY,GAAG,WAAY;MAAAjI,QAAA,eAElDtC,OAAA;QAAKkC,SAAS,EAAC,SAAS;QAACC,IAAI,EAAC,MAAM;QAACC,MAAM,EAAC,cAAc;QAACC,OAAO,EAAC,WAAW;QAAAC,QAAA,eAC5EtC,OAAA;UAAMuC,aAAa,EAAC,OAAO;UAACC,cAAc,EAAC,OAAO;UAACC,WAAW,EAAE,CAAE;UAACC,CAAC,EAAC;QAA0I;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/M;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAET9C,OAAA,CAACH,SAAS;MACRsD,MAAM,EAAEoH,QAAS;MACjB3I,OAAO,EAAEA,CAAA,KAAM4I,WAAW,CAAC,KAAK,CAAE;MAClCS,MAAM,eACJjL,OAAA;QAAKkC,SAAS,EAAC,wIAAwI;QAAAI,QAAA,gBACrJtC,OAAA;UAAKkC,SAAS,EAAC,yBAAyB;UAAAI,QAAA,gBACtCtC,OAAA;YAAKkC,SAAS,EAAC,4EAA4E;YAAAI,QAAA,eACzFtC,OAAA;cAAKkC,SAAS,EAAC,uBAAuB;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAC,QAAA,eAC1FtC,OAAA;gBAAMuC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA0I;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/M;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN9C,OAAA;YAAAsC,QAAA,gBACEtC,OAAA;cAAKkC,SAAS,EAAC,iCAAiC;cAAAI,QAAA,EAAC;YAAqB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5E9C,OAAA;cAAKkC,SAAS,EAAC,+CAA+C;cAAAI,QAAA,gBAC5DtC,OAAA;gBAAMkC,SAAS,EAAC;cAAqD;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,UAE/E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN9C,OAAA;UACEkC,SAAS,EAAC,0DAA0D;UACpEc,OAAO,EAAEA,CAAA,KAAMwH,WAAW,CAAC,KAAK,CAAE;UAClC,cAAW,YAAY;UAAAlI,QAAA,eAEvBtC,OAAA;YAAKkC,SAAS,EAAC,SAAS;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAC,QAAA,eAC5EtC,OAAA;cAAMuC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;MACDoI,IAAI,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,CAACC,QAAQ,CAAC7B,OAAO,CAAC5I,WAAW,CAAC,CAAC,CAAC,iBAC1DV,OAAA;QAAKkC,SAAS,EAAC,qDAAqD;QAAAI,QAAA,eAClEtC,OAAA;UAAKkC,SAAS,EAAC,0DAA0D;UAAAI,QAAA,gBACvEtC,OAAA;YACEkC,SAAS,EAAG;AAC5B,oBAAoBqH,YAAY,KAAK,IAAI,GACnB,iEAAiE,GACjE,gDAAiD;AACvE,iBAAkB;YACFvG,OAAO,EAAEA,CAAA,KAAMwG,eAAe,CAAC,IAAI,CAAE;YACrC4B,QAAQ,EAAE,CAAE;YAAA9I,QAAA,EACb;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9C,OAAA;YACEkC,SAAS,EAAG;AAC5B,oBAAoBqH,YAAY,KAAK,UAAU,GACzB,kEAAkE,GAClE,kDAAmD;AACzE,iBAAkB;YACFvG,OAAO,EAAEA,CAAA,KAAMwG,eAAe,CAAC,UAAU,CAAE;YAC3C4B,QAAQ,EAAE,CAAE;YAAA9I,QAAA,EACb;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9C,OAAA;YACEkC,SAAS,EAAG;AAC5B,oBAAoBqH,YAAY,KAAK,gBAAgB,GAC/B,qEAAqE,GACrE,oDAAqD;AAC3E,iBAAkB;YACFvG,OAAO,EAAEA,CAAA,KAAMwG,eAAe,CAAC,gBAAgB,CAAE;YACjD4B,QAAQ,EAAE,CAAE;YAAA9I,QAAA,EAEXuH;UAAkB;YAAAlH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACL;MAAAR,QAAA,EAED,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC6I,QAAQ,CAAC7B,OAAO,CAAC5I,WAAW,CAAC,CAAC,CAAC,GACrD6I,YAAY,KAAK,IAAI,gBACnBvJ,OAAA,CAACJ,WAAW;QACVyL,eAAe,EAAEV,MAAO;QACxBW,QAAQ,EAAC;MAAU;QAAA3I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,GACAyG,YAAY,KAAK,UAAU,gBAC7BvJ,OAAA,CAACJ,WAAW;QACVyL,eAAe,EAAEV,MAAO;QACxBW,QAAQ,EAAC;MAAgB;QAAA3I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,gBAEF9C,OAAA,CAACJ,WAAW;QACVyL,eAAe,EAAEV,MAAO;QACxBW,QAAQ,EAAC;MAAgB;QAAA3I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CACF,GACCwG,OAAO,CAAC5I,WAAW,CAAC,CAAC,KAAK,IAAI,gBAChCV,OAAA,CAACJ,WAAW;QACVyL,eAAe,EAAEV,MAAO;QACxBW,QAAQ,EAAC;MAAU;QAAA3I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,gBAEF9C,OAAA,CAACJ,WAAW;QACVyL,eAAe,EAAEV,MAAO;QACxBW,QAAQ,EAAC;MAAgB;QAAA3I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA,eACZ,CACH;;EAED;EACA,MAAM,CAACyI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnM,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoM,eAAe,EAAEC,kBAAkB,CAAC,GAAGrM,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMsM,wBAAwB,GAAGA,CAAA,KAAMH,mBAAmB,CAAC,IAAI,CAAC;EAChE,MAAMI,uBAAuB,GAAGA,CAAA,KAAMF,kBAAkB,CAAC,IAAI,CAAC;EAC9D,MAAMG,cAAc,GAAGA,CAAA,KAAM;IAC3BL,mBAAmB,CAAC,KAAK,CAAC;IAC1BtD,aAAa,CAAC,CAAC;EACjB,CAAC;EACD,MAAM4D,aAAa,GAAGA,CAAA,KAAM;IAC1BJ,kBAAkB,CAAC,KAAK,CAAC;IACzBvD,YAAY,CAAC,CAAC;EAChB,CAAC;;EAED;EACA,MAAM,CAAC4D,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG3M,QAAQ,CAAC,KAAK,CAAC;EAEzE,IAAIsG,OAAO,EAAE;IACX,oBACE3F,OAAA;MAAKkC,SAAS,EAAC,0FAA0F;MAAAI,QAAA,eACvGtC,OAAA;QAAKkC,SAAS,EAAC,4BAA4B;QAAAI,QAAA,gBACzCtC,OAAA;UAAKkC,SAAS,EAAC;QAA2E;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjG9C,OAAA;UAAGkC,SAAS,EAAC,wCAAwC;UAAAI,QAAA,EAAC;QAAsB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAAC8C,UAAU,EAAE;IACf,oBACE5F,OAAA;MAAKkC,SAAS,EAAC,0FAA0F;MAAAI,QAAA,eACvGtC,OAAA;QAAKkC,SAAS,EAAC,wDAAwD;QAAAI,QAAA,gBACrEtC,OAAA;UAAKkC,SAAS,EAAC,mBAAmB;UAAAI,QAAA,eAChCtC,OAAA;YAAKkC,SAAS,EAAC,mBAAmB;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAC,QAAA,eACtFtC,OAAA;cAAMuC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAC,GAAG;cAACC,CAAC,EAAC;YAAsI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3M;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN9C,OAAA;UAAIkC,SAAS,EAAC,uCAAuC;UAAAI,QAAA,EAAC;QAAwB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnF9C,OAAA;UAAGkC,SAAS,EAAC,eAAe;UAAAI,QAAA,EAAC;QAA0D;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIwG,OAAO,CAAC5I,WAAW,CAAC,CAAC,KAAK,UAAU,EAAE;IACxC;IACA,MAAMuL,qBAAqB,GAAG,MAAAA,CAAA,KAAY;MACxCD,wBAAwB,CAAC,IAAI,CAAC;MAC9B,IAAI;QACF,MAAMhE,kBAAkB,CAAC,mBAAmB,CAAC;QAC7CV,gBAAgB,CAAC,SAAS,EAAE,iBAAiB,EAAE,+BAA+B,CAAC;QAC/E,MAAMS,eAAe,CAAC,CAAC;MACzB,CAAC,CAAC,OAAO/D,CAAC,EAAE;QACVsD,gBAAgB,CAAC,OAAO,EAAE,OAAO,EAAE,2BAA2B,CAAC;MACjE,CAAC,SAAS;QACR0E,wBAAwB,CAAC,KAAK,CAAC;MACjC;IACF,CAAC;IACD,MAAME,4BAA4B,GAAG,MAAAA,CAAA,KAAY;MAC/CF,wBAAwB,CAAC,IAAI,CAAC;MAC9B,IAAI;QACF,MAAMhE,kBAAkB,CAAC,4BAA4B,CAAC;QACtDV,gBAAgB,CAAC,MAAM,EAAE,mBAAmB,EAAE,6BAA6B,CAAC;QAC5E,MAAMS,eAAe,CAAC,CAAC;MACzB,CAAC,CAAC,OAAO/D,CAAC,EAAE;QACVsD,gBAAgB,CAAC,OAAO,EAAE,OAAO,EAAE,4BAA4B,CAAC;MAClE,CAAC,SAAS;QACR0E,wBAAwB,CAAC,KAAK,CAAC;MACjC;IACF,CAAC;IACD,oBACEhM,OAAA,CAAAE,SAAA;MAAAoC,QAAA,GACGyI,WAAW,eACZ/K,OAAA,CAACR,WAAW;QACVoG,UAAU,EAAEA,UAAW;QACvByD,WAAW,EAAEA,WAAY;QACzB7B,YAAY,EAAEA,YAAa;QAC3BX,WAAW,EAAEA,WAAW,IAAI,MAAO;QACnCC,cAAc,EAAEqF,IAAI,IAAIrF,cAAc,CAACqF,IAA4B,CAAE;QACrE/I,WAAW,EAAEA,WAAY;QACzBgE,UAAU,EAAEA,UAAW;QACvBf,cAAc,EAAEA,cAAe;QAC/BoB,aAAa,EAAEA,aAAc;QAC7BtB,YAAY,EAAEA,YAAa;QAC3BoB,gBAAgB,EAAEA,gBAAiB;QACnChB,eAAe,EAAEA,eAAgB;QACjCoB,cAAc,EAAEA,cAAe;QAC/BlB,SAAS,EAAEA,SAAU;QACrBE,gBAAgB,EAAEA,gBAAiB;QACnCtD,cAAc,EAAEA,cAAe;QAC/B+I,iBAAiB,EAAEH,qBAAsB;QACzCI,wBAAwB,EAAEH,4BAA6B;QACvDH,qBAAqB,EAAEA,qBAAsB;QAC7CO,cAAc,EAAE;MAAK;QAAA3J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA,eACF,CAAC;EAEP;EAEA,oBACE9C,OAAA,CAAAE,SAAA;IAAAoC,QAAA,gBACEtC,OAAA,CAACN,YAAY;MACX2J,WAAW,EAAEA,WAAY;MACzBzD,UAAU,EAAEA,UAAW;MACvBmB,WAAW,EAAEA,WAAY;MACzBE,QAAQ,EAAEA,QAAS;MACnBD,cAAc,EAAEA,cAAe;MAC/BnB,aAAa,EAAEA,aAAc;MAC7BC,gBAAgB,EAAEA,gBAAiB;MACnC4B,aAAa,EAAEA,aAAc;MAC7BO,UAAU,EAAEA,UAAW;MACvBjC,WAAW,EAAEA,WAAY;MACzBC,cAAc,EAAEA,cAAe;MAC/BC,aAAa,EAAEA,aAAc;MAC7BkC,UAAU,EAAEA,UAAW;MACvBF,aAAa,EAAEyD,wBAAyB;MACxCtD,SAAS,EAAEA,SAAU;MACrBF,YAAY,EAAEyD,uBAAwB;MACtCW,gBAAgB,EAAE3G,UAAU,CAAC4G,MAAO;MACpClE,iBAAiB,EAAEA,iBAAkB;MACrCC,gBAAgB,EAAEA,gBAAiB;MACnCC,gBAAgB,EAAEA,gBAAiB;MACnCC,mBAAmB,EAAEA,mBAAoB;MACzCE,mBAAmB,EAAEA,mBAAoB;MACzCD,gBAAgB,EAAEA,gBAAiB;MACnCE,yBAAyB,EAAEA,yBAA0B;MACrDC,eAAe,EAAEA,eAAgB;MACjCC,kBAAkB,EAAEA,kBAAmB;MACvCC,iBAAiB,EAAEA,iBAAkB;MACrCC,sBAAsB,EAAEA,sBAAuB;MAC/CC,mBAAmB,EAAEA,mBAAoB;MACzCC,sBAAsB,EAAEA,sBAAuB;MAC/CC,2BAA2B,EAAEA,2BAA4B;MACzDC,kBAAkB,EAAEA,kBAAmB;MACvChC,UAAU,EAAEA,UAAW;MACvBI,YAAY,EAAEA,YAAa;MAC3BX,WAAW,EAAEA,WAAW,IAAI,MAAO;MACnCC,cAAc,EAAEqF,IAAI,IAAIrF,cAAc,CAACqF,IAA4B,CAAE;MACrE/I,WAAW,EAAEA,WAAY;MACzBiD,cAAc,EAAEA,cAAe;MAC/BoB,aAAa,EAAEA,aAAc;MAC7BtB,YAAY,EAAEA,YAAa;MAC3BoB,gBAAgB,EAAEA,gBAAiB;MACnChB,eAAe,EAAEA,eAAgB;MACjCoB,cAAc,EAAEA,cAAe;MAC/BlB,SAAS,EAAEA,SAAU;MACrBE,gBAAgB,EAAEA,gBAAiB;MACnCtD,cAAc,EAAEA,cAAe;MAC/B0C,WAAW,EAAEA,WAAY;MAAAzD,QAAA,EAExB,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC6I,QAAQ,CAAC7B,OAAO,CAAC5I,WAAW,CAAC,CAAC,CAAC,IAAIqK;IAAW;MAAApI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5E,CAAC,eACf9C,OAAA,CAACF,iBAAiB;MAChBqD,MAAM,EAAEoI,gBAAiB;MACzB3J,OAAO,EAAEA,CAAA,KAAM4J,mBAAmB,CAAC,KAAK,CAAE;MAC1CiB,SAAS,EAAEZ,cAAe;MAC1BpK,KAAK,EAAC,iBAAiB;MACvBiL,WAAW,EAAC,6EAA6E;MACzFC,WAAW,EAAC,SAAS;MACrBC,UAAU,EAAC;IAAQ;MAAAjK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,eACF9C,OAAA,CAACF,iBAAiB;MAChBqD,MAAM,EAAEsI,eAAgB;MACxB7J,OAAO,EAAEA,CAAA,KAAM8J,kBAAkB,CAAC,KAAK,CAAE;MACzCe,SAAS,EAAEX,aAAc;MACzBrK,KAAK,EAAC,gBAAgB;MACtBiL,WAAW,EAAC,kFAAkF;MAC9FC,WAAW,EAAC,QAAQ;MACpBC,UAAU,EAAC;IAAQ;MAAAjK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC;EAAA,eACF,CAAC;AAEP,CAAC;AAACyC,GAAA,CAxYID,SAAmB;EAAA,QACT3F,iBAAiB,EAmEfF,aAAa,EAwBCF,kBAAkB;AAAA;AAAAsN,GAAA,GA5F5CvH,SAAmB;AA0YzB,eAAeA,SAAS;AAAC,IAAArC,EAAA,EAAAgC,GAAA,EAAA4H,GAAA;AAAAC,YAAA,CAAA7J,EAAA;AAAA6J,YAAA,CAAA7H,GAAA;AAAA6H,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}