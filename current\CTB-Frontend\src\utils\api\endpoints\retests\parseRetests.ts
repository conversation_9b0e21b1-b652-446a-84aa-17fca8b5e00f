import moment from "moment";
import { DATE_FORMAT } from "../../../..";
 
export interface CTBRetest {
  retest_id: number;
  report_id: number;
  program_name: string;
  report_name: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  latest_comment?: string;  
  severity?: string;  
  comments?: CTBRetestLog[]; 
}

export interface CTBRetestLog {
  id: number;
  comments: string;  
  username: string;  
  pfp: string;
  role: string;  
  new_status: string; 
  poc?: string;
  createdAt: string;
}  
 

export type User = {
  username: string;
  pfp: string;
};

export type RetestLog = {
  retestId: number;
  previousStatus: string;
  newStatus: string;
  actionTaken: string;
  comments: string;
  createdAt: string;
  updatedAt: string;
  username?: string;
  profileImage?: string;
};

export type Retest = {
  retestId: number;
  currentStatus: string;
  researcher: User;
  programOwner: User;
  logs: RetestLog[];
};

export type Report = {
  reportId: number;
  reportTitle: string;
  submittedDate: string;
  severityCategory: string;
  reportCreator: User;
  retests: Retest[];
};

export type RetestDetailsResponse = {
  message: string;
  reports: Report[];
};

export const parseRetestDetails = (data: any): RetestDetailsResponse => {
  console.log("Raw API Response:", data);

  return {
    message: data?.message ?? "Retest details retrieved successfully.",
    reports: (data?.data || []).map((report: any) => {
      console.log(`Processing Report: ${report?.report_title ?? "Unknown"}`);

      return {
        reportId: report?.report_id ?? 0,
        reportTitle: report?.report_title ?? "Untitled",
        submittedDate: report?.submitted_date
          ? moment(report.submitted_date).format("YYYY-MM-DD HH:mm:ss")
          : "Unknown",
        severityCategory: report?.severity_category ?? "Uncategorized",
        reportCreator: {
          username: report?.reportCreator?.username ?? "Unknown User",
          pfp: report?.reportCreator?.pfp ?? "",
        },

        retests: (report?.retests || []).map((retest: any) => ({
          retestId: retest?.retest_id ?? 0,
          currentStatus: retest?.current_status ?? "Unknown",

          researcher: {
            username: retest?.researcher?.username ?? "Unknown Researcher",
            pfp: retest?.researcher?.pfp ?? "",
          },

          programOwner: {
            username: retest?.program_owner?.username ?? "Unknown Owner",
            pfp: retest?.program_owner?.pfp ?? "",
          },

          logs: (retest?.logs || []).map((log: any) => {
            console.log(`Processing Log for Retest ID ${log?.retest_id ?? "N/A"}`);

            return {
              retestId: log?.retest_id ?? 0,
              previousStatus: log?.previous_status ?? "Unknown",
              newStatus: log?.new_status ?? "Unknown",
              actionTaken: log?.action_taken ?? "No action recorded",
              comments: log?.comments ?? "No comments",
              createdAt: log?.createdAt
                ? moment(log.createdAt).format("YYYY-MM-DD HH:mm:ss")
                : "Unknown",
              updatedAt: log?.updatedAt
                ? moment(log.updatedAt).format("YYYY-MM-DD HH:mm:ss")
                : "Unknown",
              username:
                log?.action_taken === "Business"
                  ? retest?.program_owner?.username ?? "Unknown User"
                  : retest?.researcher?.username ?? "Unknown User",
              profileImage:
                log?.action_taken === "Business"
                  ? retest?.program_owner?.pfp ?? ""
                  : retest?.researcher?.pfp ?? "",
            };
          }),
        })),
      };
    }),
  };
};