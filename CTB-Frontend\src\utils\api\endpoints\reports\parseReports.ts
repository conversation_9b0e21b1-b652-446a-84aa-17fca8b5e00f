import moment from "moment";
import {
  SeverityScore,
  parseCVSSVector
} from "../../../../components/forms/inputs/SeverityScoreSelector";
import { ReportState, ReportStatus, ReportUpdateFields } from "./reports";
import { BaseItem } from "../../axios";
import { DATE_FORMAT } from "../../../..";
import { createZip } from "../../../zipAttachments";
import { TriageStatus } from "../usersApi";
import { ProgramCreator } from "../programs/parsePrograms";

type ReportCreator = {
  displayName: string;
  pfp?: string;
};

export enum ReportRejectReason {
  DUPLICATE = "Duplicate",
  OUT_OF_SCOPE = "Out Of Scope",
  DISCLOSED = "Disclosed",
  INFORMATIVE = "Informative",
  SPAM = "Spam",
  NONE = ""
}

// The report object that is returned by the API
export type CTBReportAPIResponse = {
  report_id: number;
  user_id: number;
  program_id: number;
  is_delete: boolean;
  report_title: string;
  scope?: string;
  category?: string;
  severity?: string;
  severity_category?: string;
  description?: string;
  instructions?: string;
  impact?: string;
  fix?: string;
  additional_info?: string;
  jira_issue_key?: string;
  submitted_date?: string;
  state?: ReportState;
  payout?: number;
  triage_status?: TriageStatus;
  comments?: string;
  attachments?: string;
  reject_reason?: ReportRejectReason;
  certificate_generated?: boolean;
  creator?: ReportCreator;
  program_creator?: ProgramCreator;
};

export type CTBReport = {
  title: string;
  programId: number;
  scope?: string;
  category?: string;
  severity?: SeverityScore;
  description?: string;
  instructions?: string;
  impact?: string;
  fix?: string;
  additionalInfo?: string;
  jiraIssueKey?: string;
  attachments?: string;
  payout?: number;
  comments?: string;
  rejectReason?: ReportRejectReason;
  certificateGenerated: boolean;
  submittedDate?: string;
  state?: ReportState;
  triageStatus?: TriageStatus;
  status?: ReportStatus;
  adminState?: TriageStatus;
  userId: number;
  isDelete: boolean;
  creator?: ReportCreator;
  program_creator?: ProgramCreator;
} & BaseItem;

/**
 * Extract and parse each of the expected fields from
 * the reports API response
 */
export const parseReport = (report: CTBReportAPIResponse) =>
  ({
    id: report.report_id,
    userId: report.user_id,
    programId: report.program_id,
    isDelete: report.is_delete,
    title: report.report_title,
    submittedDate: report.submitted_date
      ? moment(report.submitted_date).format(DATE_FORMAT)
      : undefined,
    status: report.is_delete
      ? ReportStatus.DELETED
      : report.state !== null
      ? ReportStatus.SUBMITTED
      : report.state === null
      ? ReportStatus.DRAFT
      : undefined,
    additionalInfo: report.additional_info,
    jiraIssueKey: report.jira_issue_key,
    triageStatus: report.triage_status,
    certificateGenerated: report.certificate_generated,
    severityCategory: report.severity_category,
    severity: parseCVSSVector(report.severity ?? ""),
    rejectReason: report.reject_reason,
    scope: report.scope,
    category: report.category,
    description: report.description,
    instructions: report.instructions,
    impact: report.impact,
    fix: report.fix,
    state: report.state,
    payout: report.payout,
    comments: report.comments,
    attachments: report.attachments,
    severityVector: report.severity,
    creator: report.creator,
    program_creator: report.program_creator
  }) as CTBReport;

/**
 * Convert report update fields into form data
 * to be used in an API request
 */
export const prepareReportFormData = async ({
  id,
  details,
  files
}: {
  id?: number;
  details: ReportUpdateFields;
  files?: Blob[];
}) => {
  // Prepare the POST body
  const severityValues = details.severity?.values;
  const cvssVector = severityValues
    ? `CVSS:3.1/AV:${severityValues.AV}/AC:${severityValues.AC}/PR:${severityValues.PR}/UI:${severityValues.UI}/S:${severityValues.S}/C:${severityValues.C}/I:${severityValues.I}/A:${severityValues.A}`
    : undefined;

  const body = {
    report_id: id,
    program_id: details.programId,
    report_title: details.title,
    scope: details.scope,
    category: details.category,
    severity: cvssVector,
    severity_score: details.severity?.score,
    description: details.description,
    instructions: details.instructions,
    impact: details.impact,
    fix: details.fix,
    additional_info: details.additionalInfo
  };

  // Add the body to some Form Data
  const formData = new FormData();
  formData.append("data", JSON.stringify(body));

  // Compress the attachment to send
  if (files) {
    const zippedFiles = await createZip(files);
    if (zippedFiles !== null) formData.append("attachments", zippedFiles);
  }

  return formData;
};

export type ReportCommentAPIresponse = {
  comment_id: number;
  user_id: number;
  report_id: number;
  comment: string;
  createdAt: string;
  updatedAt: string;
  pfp: string;
  username: string;
};

export type ReportRecommendation = {
  report_id: number;
  report_title: string;
  severity_category: string;
};
