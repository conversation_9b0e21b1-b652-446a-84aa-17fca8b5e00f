import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import useUserCredentials from "../../../utils/hooks/user/useUserCredentials";
import useProgram from "../../../utils/hooks/programs/useProgram";
import parse from "html-react-parser";
import moment from "moment";
import { getProgramOverview } from "../../../utils/api/endpoints/programs/programFullview";
import { ProgramType } from "../../../utils/api/endpoints/programs/parsePrograms";
// Removed unused import

interface ProgramOverview {
  id: number;
  status: string;
  totalReports: number;
  createdAt: string;
  testingStatus: string;
  expected_start_date?: string;
  type?: ProgramType;
}



const ProgramHighlight = () => {
  const { id } = useParams();
  const { role } = useUserCredentials();
  const navigate = useNavigate();

  const { program } = useProgram(id ? parseInt(id) : undefined);

  const statusColors: Record<string, string> = {
    Active: "border-blue-500 border bg-blue-600 text-white",
    Inactive: "border-gray-700 border bg-gray-200 bg-opacity-60 text-black",
  };

  const testingStatusColors: Record<string, string> = {
    Upcoming: "border-blue-500 bg-blue-300 text-black",
    Ongoing: "border-yellow-500 bg-orange-100 text-black",
    Completed: "border-green-700 bg-green-300 text-black",
  };

  const [programOverview, setProgramOverview] = useState<ProgramOverview | null>(null);
  const [loadingOverview, setLoadingOverview] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    if (id) {
      const fetchProgramOverview = async () => {
        try {
          const data = await getProgramOverview(parseInt(id));
          setProgramOverview({
            ...data.program,
            type: data.program.type as ProgramType,
          });
        } catch (err) {
          setError("Failed to fetch program overview.");
        } finally {
          setLoadingOverview(false);
        }
      };
      fetchProgramOverview();
    }
  }, [id]);

  const truncateText = (text: string, wordLimit: number): string => {
    const words = text.split(/\s+/);
    return words.length > wordLimit ? words.slice(0, wordLimit).join(" ") + "..." : text;
  };

  return (
    <>
      <section className="bg-white p-4 mb-4">
        <h2 className="text-xl font-semibold text-gray-900 py-2">Program Highlights</h2>
        <div className="border-t-2 border-blue-600"></div>
        <div className="flex items-center justify-between mt-3">
          <div className="flex-1">
            <h3 className="text-xl font-bold text-gray-900 mb-4 capitalize">
              {program?.title || "No program available"}
            </h3>
            <p className="text-gray-600 mt-2 text-sm leading-relaxed">
              {isExpanded
                ? parse(program?.description || "No description available")
                : parse(truncateText(program?.description || "No description available", 50))}
            </p>
            {program?.description && program.description.split(/\s+/).length > 50 && (
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-blue-600 text-sm font-semibold mt-1"
              >
                {isExpanded ? "Show Less" : "Read More"}
              </button>
            )}
          </div>
          <div className="w-24 h-24 flex items-center justify-center rounded-lg ml-6">
            {program?.profilePicture && (
              <img src={program.profilePicture} alt="profile" className="w-full" />
            )}
          </div>
        </div>
      </section>

      <section className="bg-white px-5 py-2">
        {loadingOverview ? (
          <p className="text-gray-500 text-sm">Loading program details...</p>
        ) : error ? (
          <p className="text-red-500 text-sm">{error}</p>
        ) : programOverview ? (
          <div className={`p-4 grid ${programOverview?.type === ProgramType.BugBounty || programOverview?.type === ProgramType.VDP ? "grid-cols-3" : "grid-cols-4"} gap-6 text-lg w-full`}>
            <div className="flex flex-col items-start border-r-2 border-gray-300 pr-6 w-full">
              <span className="text-black mb-2 text-lg">Status</span>
              <span className={`px-6 py-2 mt-1 text-sm font-semibold ${statusColors[programOverview.status] || "bg-gray-500 text-white"}`}>
                {programOverview.status || "Unknown"}
              </span>
              <span className="text-[11px] text-black mt-0.5 whitespace-nowrap">
                {moment(programOverview.createdAt).utc().format("DD MMM YYYY HH:mm:ss")}
              </span>
            </div>

            <div className="flex flex-col items-start border-r-2 border-gray-300 pr-6 w-full">
              <span className="text-black text-lg mb-2">Start Date</span>
              <span className="text-gray-900 mt-2 text-lg font-semibold">
                {programOverview?.type === ProgramType.PTAAS ? programOverview.expected_start_date : programOverview.createdAt}
              </span>
              <span className="text-[11px] text-black mt-1.5 whitespace-nowrap">
                Program Start Date
              </span>
            </div>


            {programOverview?.type === ProgramType.PTAAS && (
              <div className="flex flex-col items-start pr-6 border-r-2 border-gray-200 w-full">
                <span className="text-black text-lg mb-2">Testing Period</span>
                <span className={`border px-6 py-2 mt-1 text-sm font-semibold ${testingStatusColors[programOverview.testingStatus] || "border-gray-500 text-gray-700"}`}>
                  {programOverview.testingStatus}
                </span>
                <span className="text-[11px] text-black">Started at {programOverview.expected_start_date}</span>
              </div>
            )}

            <div className="flex flex-col items-start w-full">
              <span className="text-black mb-2 text-lg">Total Reports</span>
              <span className="text-black text-xl mt-2 font-semibold">{programOverview.totalReports || 0}</span>
              <span className="text-[11px] text-black mt-1.5">Avg. number reports</span>
            </div>
          </div>
        ) : null}
      </section>

    </>
  );
};

export default ProgramHighlight;