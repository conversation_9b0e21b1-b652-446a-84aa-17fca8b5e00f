import { CTBReport } from "../../../utils/api/endpoints/reports/parseReports";
import QuickViewModal, { QuickViewSection } from "../../modal/QuickViewModal";
import CommentBox from "../../../components/forms/inputs/CommentBox";
import useUserCredentials from "../../../utils/hooks/user/useUserCredentials";
import { useEffect, useState } from "react";
import {
  createReportComment,
  deleteReportComment,
  getReportComments,
  editReportComment
} from "../../../utils/api/endpoints/reportCommentsApi";
import { useParams } from "react-router-dom";

const ReportQuickView = ({
  report,
  onClose
}: {
  report?: CTBReport;
  onClose: () => void;
}) => {
  const { id } = useParams();
  const currentUser = useUserCredentials();

  const [comments, setComments] = useState<any[]>([]);
  async function getComment(reportId: number | undefined) {
    const data = await getReportComments(reportId);
    data !== undefined ? setComments(data) : setComments([]);
  }

  function addNewComment(reportId: number, userComment: string) {
    createReportComment(reportId, userComment);
  }

  function deleteComment(commentId: number) {
    deleteReportComment(commentId);
  }

  function editCurrentComment(commentId: number, comment: string) {
    editReportComment(commentId, comment);
  }

  useEffect(() => {
    getComment(id !== undefined ? parseInt(id) : undefined);

    const intervalId = setInterval(
      () => getComment(id !== undefined ? parseInt(id) : undefined),
      10000
    );
    return () => clearInterval(intervalId);
  }, [id]);

  return (
    <QuickViewModal
      title={"Comments"}
      enabled={true}
      onClick={onClose}
      className="flex max-h-screen flex-col overflow-y-scroll"
    >
      <QuickViewSection>
        <CommentBox
          commentList={comments}
          reportId={report?.id}
          userId={currentUser.id}
          username={currentUser.username}
          onSubmitComment={addNewComment}
          onDeleteComment={deleteComment}
          onEditComment={editCurrentComment}
        />
      </QuickViewSection>
    </QuickViewModal>
  );
};

export default ReportQuickView;
