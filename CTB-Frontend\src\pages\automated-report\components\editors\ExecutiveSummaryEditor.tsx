import React from 'react';
import HtmlEditor from '../HtmlEditor';
import { ReportData } from '../../types/report.types';

interface ExecutiveSummaryEditorProps {
  reportData: ReportData;
  onHtmlChange: (field: string, value: string) => void;
  onTableChange: (field: string, value: number) => void;
}

interface CounterProps {
  value: number;
  onChange: (value: number) => void;
  label: string;
  className?: string;
}

const Counter: React.FC<CounterProps> = ({ value, onChange, label, className = '' }) => (
  <div className={`flex items-center gap-2 text-xs ${className}`}>
    <span className="text-gray-600 min-w-[80px]">{label}</span>
    <div className="flex-1 flex items-center bg-white border border-gray-200 rounded-md overflow-hidden">
      <button
        onClick={() => onChange(Math.max(0, value - 1))}
        className="w-6 h-6 flex items-center justify-center hover:bg-gray-50 active:bg-gray-100 border-r border-gray-200 text-gray-600"
      >
        −
      </button>
      <input
        type="number"
        value={value}
        onChange={(e) => onChange(parseInt(e.target.value) || 0)}
        className="w-14 text-center focus:outline-none focus:ring-1 focus:ring-blue-500"
        min="0"
      />
      <button
        onClick={() => onChange(value + 1)}
        className="w-6 h-6 flex items-center justify-center hover:bg-gray-50 active:bg-gray-100 border-l border-gray-200 text-gray-600"
      >
        +
      </button>
    </div>
  </div>
);

const ExecutiveSummaryEditor: React.FC<ExecutiveSummaryEditorProps> = ({ 
  reportData, 
  onHtmlChange,
  onTableChange 
}) => {
  return (
    <div className="h-full overflow-y-auto space-y-3">
      {/* Executive Summary Section */}
      <div className="bg-white rounded-xl shadow-lg border border-slate-100 p-6 mb-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-1 h-8 bg-gradient-to-b from-blue-500 to-blue-700 rounded-full" />
          <div>
            <h3 className="text-base font-bold text-blue-900 tracking-tight">Executive Summary</h3>
            <p className="text-xs text-blue-700/80 font-medium mt-0.5">Edit summary content</p>
          </div>
        </div>
        <div className="pt-2">
          <div className="flex gap-1 mb-2">
            <button
              onClick={() => {
                const editor = document.querySelector('[data-field="executive_summary"]');
                if (editor) {
                  const selection = window.getSelection();
                  const range = selection?.getRangeAt(0);
                  const strong = document.createElement('strong');
                  range?.surroundContents(strong);
                }
              }}
              className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors duration-200 flex items-center gap-1"
            >
              <span className="font-bold">B</span>
              <span>Bold</span>
            </button>
            <button
              onClick={() => {
                const editor = document.querySelector('[data-field="executive_summary"]');
                if (editor) {
                  const selection = window.getSelection();
                  const range = selection?.getRangeAt(0);
                  const em = document.createElement('em');
                  range?.surroundContents(em);
                }
              }}
              className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors duration-200 flex items-center gap-1"
            >
              <span className="italic">I</span>
              <span>Italic</span>
            </button>
            <button
              onClick={() => {
                const editor = document.querySelector('[data-field="executive_summary"]');
                if (editor) {
                  const p = document.createElement('p');
                  p.innerHTML = '<br>';
                  editor.appendChild(p);
                }
              }}
              className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors duration-200 flex items-center gap-1"
            >
              <span>¶</span>
              <span>New Line</span>
            </button>
          </div>
          <div className="text-xs">
            <HtmlEditor
              field="executive_summary"
              title=""
              reportData={reportData}
              onHtmlChange={onHtmlChange as any}
            />
          </div>
        </div>
      </div>

      {/* Severity Counts Section */}
      <div className="bg-white rounded-xl shadow-lg border border-slate-100 p-6 mb-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-1 h-8 bg-gradient-to-b from-blue-500 to-blue-700 rounded-full" />
          <div>
            <h3 className="text-base font-bold text-blue-900 tracking-tight">Severity Counts</h3>
            <p className="text-xs text-blue-700/80 font-medium mt-0.5">Manage findings distribution</p>
          </div>
        </div>
        <div className="pt-2">
          <div className="grid grid-cols-2 gap-4">
            {Object.entries(reportData.open_close_counts_by_severity).map(([severity, counts]) => (
              <div key={severity} className="bg-blue-50 rounded-lg p-3 border border-blue-100">
                <div className="flex items-center gap-2 mb-3">
                  <div className={`w-2 h-2 rounded-full ${
                    severity === 'Critical' ? 'bg-red-500' :
                    severity === 'High' ? 'bg-orange-500' :
                    severity === 'Medium' ? 'bg-yellow-500' :
                    'bg-blue-500'
                  }`} />
                  <h4 className="font-semibold text-sm text-blue-900">{severity}</h4>
                </div>
                <div className="space-y-2">
                  {Object.entries(counts).map(([type, value]) => (
                    <div key={type} className="flex items-center gap-2">
                      <span className="text-xs text-blue-700 min-w-[60px]">{type}:</span>
                      <div className="flex-1 flex items-center bg-white border border-blue-100 rounded-md overflow-hidden">
                        <button
                          onClick={() => onTableChange(`open_close_counts_by_severity.${severity}.${type}`, Math.max(0, value - 1))}
                          className="w-6 h-6 flex items-center justify-center hover:bg-blue-50 active:bg-blue-100 border-r border-blue-100 text-blue-700"
                        >
                          −
                        </button>
                        <input
                          type="number"
                          value={value}
                          onChange={(e) => onTableChange(`open_close_counts_by_severity.${severity}.${type}`, parseInt(e.target.value) || 0)}
                          className="w-12 text-center focus:outline-none focus:ring-1 focus:ring-blue-500"
                          min="0"
                        />
                        <button
                          onClick={() => onTableChange(`open_close_counts_by_severity.${severity}.${type}`, value + 1)}
                          className="w-6 h-6 flex items-center justify-center hover:bg-blue-50 active:bg-blue-100 border-l border-blue-100 text-blue-700"
                        >
                          +
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Total Counts */}
          <div className="mt-4 grid grid-cols-3 gap-4">
            {[
              { label: 'Total Open', value: reportData.total_open, field: 'total_open', color: 'bg-blue-50 border-blue-200' },
              { label: 'Total Closed', value: reportData.total_closed, field: 'total_closed', color: 'bg-green-50 border-green-200' },
              { label: 'Total Findings', value: reportData.total_findings, field: 'total_findings', color: 'bg-purple-50 border-purple-200' }
            ].map(({ label, value, field, color }) => (
              <div key={field} className={`${color} rounded-lg p-3 border`}>
                <h4 className="font-semibold text-sm text-blue-900 mb-2">{label}</h4>
                <div className="flex items-center bg-white border border-blue-100 rounded-md overflow-hidden">
                  <button
                    onClick={() => onTableChange(field, Math.max(0, value - 1))}
                    className="w-6 h-6 flex items-center justify-center hover:bg-blue-50 active:bg-blue-100 border-r border-blue-100 text-blue-700"
                  >
                    −
                  </button>
                  <input
                    type="number"
                    value={value}
                    onChange={(e) => onTableChange(field, parseInt(e.target.value) || 0)}
                    className="flex-1 text-center focus:outline-none focus:ring-1 focus:ring-blue-500"
                    min="0"
                  />
                  <button
                    onClick={() => onTableChange(field, value + 1)}
                    className="w-6 h-6 flex items-center justify-center hover:bg-blue-50 active:bg-blue-100 border-l border-blue-100 text-blue-700"
                  >
                    +
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExecutiveSummaryEditor; 