import { DataTypes, Model, Sequelize, Model<PERSON><PERSON> } from "sequelize";

interface AutomatedReportsAttributes {
  id: string;
  business_id: number;
  title: string;
  company_name: string;
  program_name?: string;
  program_ids: number[];
  document_number?: string;
  status: string;
  total_findings: number;
  critical_count: number;
  high_count: number;
  medium_count: number;
  low_count: number;
}

interface AutomatedReportsCreationAttributes
  extends AutomatedReportsAttributes {}

// AutomatedReports class extends Model
class AutomatedReports extends Model<
  AutomatedReportsAttributes,
  AutomatedReportsCreationAttributes
> {
  declare id: string;
  declare business_id: number;
  declare title: string;
  declare company_name: string;
  declare program_name?: string;
  declare program_ids: number[];
  declare document_number?: string;
  declare status: string;
  declare total_findings: number;
  declare critical_count: number;
  declare high_count: number;
  declare medium_count: number;
  declare low_count: number;

  public static associate(models: Record<string, ModelCtor<Model>>) {
    // Check if user model exists before associating
    if (models.user) {
      this.belongsTo(models.user, {
        foreignKey: "business_id",
        as: "user"
      });
    }

    // Check if automated_report_versions model exists in models
    // The model name in sequelize.models is usually lowercase and underscored
    const versionModelName = Object.keys(models).find(
      key =>
        key.toLowerCase() === "automated_report_versions" ||
        key === "automated_report_versions"
    );

    if (versionModelName && models[versionModelName]) {
      this.hasMany(models[versionModelName], {
        foreignKey: "report_id",
        as: "versions"
      });
    }
  }
}

export default (sequelize: Sequelize) => {
  AutomatedReports.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
      },
      business_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "users",
          key: "user_id"
        },
        onDelete: "CASCADE"
      },
      title: {
        type: DataTypes.STRING,
        allowNull: false
      },
      company_name: {
        type: DataTypes.STRING,
        allowNull: false
      },
      program_name: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "Name of the program for display on cover page"
      },
      program_ids: {
        type: DataTypes.JSON,
        allowNull: false
      },
      document_number: {
        type: DataTypes.STRING,
        allowNull: true
      },
      status: {
        type: DataTypes.STRING,
        allowNull: false
      },
      total_findings: {
        type: DataTypes.INTEGER,
        allowNull: false
      },
      critical_count: {
        type: DataTypes.INTEGER,
        allowNull: false
      },
      high_count: {
        type: DataTypes.INTEGER,
        allowNull: false
      },
      medium_count: {
        type: DataTypes.INTEGER,
        allowNull: false
      },
      low_count: {
        type: DataTypes.INTEGER,
        allowNull: false
      }
    },
    {
      sequelize,
      tableName: "automated_reports",
      timestamps: true
    }
  );

  return AutomatedReports;
};
