import React from "react";
import { CTBRetest } from "../../../utils/api/endpoints/retests/parseRetests";
import { formatDate, getStatusColor, renderTrimmedHTML } from "../utils/helper";
import StatusFormatter from "../utils/StatusFormatter";

interface RetestTableRowProps {
  retest: CTBRetest;
  index: number;
  onClick: () => void;
}

const RetestTableRow: React.FC<RetestTableRowProps> = ({
  retest,
  index,
  onClick
}) => {
  return (
    <tr
      onClick={onClick}
      className={`cursor-pointer transition-colors duration-150 ease-in-out hover:bg-gray-50 ${
        index % 2 === 0 ? "bg-white" : "bg-gray-50"
      }`}
    >
      <td
        className="flex items-center px-6 py-4 font-medium text-gray-900"
        dangerouslySetInnerHTML={renderTrimmedHTML(retest.report_name)}
        title={retest.report_name}
      />

      <td className="px-6 py-4">
        <span
          className={`inline-flex items-center rounded-sm px-3 py-1 text-sm font-medium ${getStatusColor(
            retest.status
          )}`}
        >
          <StatusFormatter status={retest.status} />
        </span>
      </td>
      <td className="px-6 py-4 text-gray-500">
        {formatDate(retest.createdAt)}
      </td>
      <td className="px-6 py-4 text-gray-500">
        {formatDate(retest.updatedAt)}
      </td>
      {/* <td
        className="px-6 py-4 text-gray-500"
        dangerouslySetInnerHTML={renderTrimmedHTML(
          retest.latest_comment || "N/A"
        )}
      /> */}
    </tr>
  );
};

export default RetestTableRow;
