import { Outlet, Route, Routes, useNavigate } from "react-router-dom";
import UserSettingsModal from "../settings/SettingsModal";
import React, { PropsWithChildren, useState } from "react";
import UserBanner from "./UserBanner";
import ProfileLinks from "./ProfileLinks";
import usePageTitle from "../../utils/hooks/usePageTitle";
import useUserCredentials from "../../utils/hooks/user/useUserCredentials";
import { UserRole } from "../../utils/api/endpoints/user/credentials";
import { useStoreSelector } from "../../utils/hooks/hooks";

/*
 * Provide User Profile page with routes
 * for settings modal
 */
const UserProfile = (props: PropsWithChildren) => {
  usePageTitle("Profile");

  return (
    <Routes>
      <Route element={<Profile {...props} />}>
        <Route index element={<></>} />
        <Route path="settings/*" element={<UserSettingsModal />} />
      </Route>
    </Routes>
  );
};

/**
 * The actual user profile component,
 * to be used as UserProfile layout
 */
const Profile = ({ children }: { children?: React.ReactNode }) => {
  const navigate = useNavigate();
  const { role } = useUserCredentials();
  const user = useStoreSelector(state => state.other_user.details);

  // Loop through children to get profile titles
  const titles =
    React.Children.map(children, child => {
      if (!React.isValidElement(child)) return undefined;

      if ("props" in child) {
        return child.props.profileTitle
          ? {
              title: child.props.profileTitle,
              id: child.props.id
            }
          : undefined;
      }
    }) || [];

  //keep state of current position of scroll
  const [currentPos, setCurrentPos] = useState<number>(0);

  //Handle the OnScroll event
  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const scrollPos: number = event.currentTarget.scrollTop;
    //set the state of the scroll position
    setCurrentPos(scrollPos);
  };

  return (
    <main className="grid h-full grid-cols-6 grid-rows-[211px_auto]">
      <UserBanner
        user={user}
        className="col-span-6"
        isVerified={role === UserRole.BUSINESS}
      />

      <div className="col-span-2 ml-12 mt-14 border-r-[1px] pr-1">
        <h1 className="mb-2 text-2xl leading-[150%]">Profile</h1>

        <div className="flex flex-col gap-1 border-b-[1px] pb-2">
          <ProfileLinks scrollPos={currentPos} titles={titles}></ProfileLinks>
        </div>
        <button
          className="mt-2 rounded-2xl pl-2 pr-6 transition hover:bg-ctb-grey-200 "
          onClick={() => {
            navigate("settings");
          }}
        >
          Settings
        </button>
      </div>

      <div
        onScroll={handleScroll}
        className="col-span-4 overflow-auto pl-[28px] pt-9 "
      >
        {children}
      </div>

      {/* Outlet for settings modal (when route is active) */}
      <Outlet />
    </main>
  );
};

export default UserProfile;
