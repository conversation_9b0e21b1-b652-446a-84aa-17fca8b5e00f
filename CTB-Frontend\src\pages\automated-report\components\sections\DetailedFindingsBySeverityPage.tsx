import React from 'react';
import { Page, View, Text, StyleSheet } from '@react-pdf/renderer';
import { renderPdfElements, renderPdfElementsPlainText, parseHtmlToElements, PdfElement } from '../pdfHtmlImageUtils';

interface DetailedFindingsBySeverityPageProps {
  severity: string;
  findings: any[];
  documentNumber?: string;
}

// Utility to sanitize HTML by removing only dangerous attributes while preserving styling
function sanitizeHtml(html: string): string {
  // Remove only dangerous attributes, keep style attributes for formatting
  return html.replace(/(on\w+|javascript:|data-(?!language))="[^"]*"/gi, '');
}

// Helper to format date as dd-mm-yyyy
function formatDateToDDMMYYYY(dateString: string): string {
  if (!dateString) return '';
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString;
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  return `${day}-${month}-${year}`;
}

// Helper to ensure the field is always an array of PdfElement
function ensurePdfElements(field: any): PdfElement[] {
  if (!field) return [];
  if (Array.isArray(field) && field.length > 0 && typeof field[0] === 'object' && 'type' in field[0]) {
    // Already parsed
    return field;
  }
  if (typeof field === 'string') {
    // Handle HTML content properly
    if (field.trim().startsWith('<')) {
      const cleanHtml = sanitizeHtml(field);
      // Use synchronous HTML parsing to preserve formatting
      return parseHtmlToElementsSync(cleanHtml);
    }
    return [{ type: 'text', content: field }];
  }
  return [{ type: 'text', content: String(field) }];
}

// Synchronous HTML parser for PDF rendering
function parseHtmlToElementsSync(htmlString: string): PdfElement[] {
  if (!htmlString) return [];
  if (typeof window === 'undefined' || typeof DOMParser === 'undefined') {
    // SSR fallback: return as text
    return [{ type: 'text', content: htmlString.replace(/<[^>]+>/g, '') }];
  }

  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlString, 'text/html');
  const elements: PdfElement[] = [];

  const processNode = (node: Node, container: PdfElement[] = elements): void => {
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent?.trim() ?? '';
      if (text) {
        container.push({ type: 'text', content: text });
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      const el = node as HTMLElement;
      switch (el.tagName.toLowerCase()) {
        case 'p': {
          const paragraph: PdfElement = { type: 'paragraph', children: [] };
          for (const child of Array.from(el.childNodes)) {
            processNode(child, paragraph.children);
          }
          if (paragraph.children.length > 0) {
            container.push(paragraph);
          }
          break;
        }
        case 'h1':
        case 'h2':
        case 'h3':
        case 'h4':
        case 'h5':
        case 'h6':
          container.push({
            type: 'heading',
            level: parseInt(el.tagName.substring(1)),
            content: el.textContent?.trim() ?? ''
          });
          break;
        case 'strong':
        case 'b':
          container.push({
            type: 'text',
            content: el.textContent?.trim() ?? '',
            style: 'bold'
          });
          break;
        case 'em':
        case 'i':
          container.push({
            type: 'text',
            content: el.textContent?.trim() ?? '',
            style: 'italic'
          });
          break;
        case 'ul':
        case 'ol': {
          const list: PdfElement = {
            type: 'list',
            ordered: el.tagName.toLowerCase() === 'ol',
            items: []
          };
          for (const li of Array.from(el.querySelectorAll('li'))) {
            list.items.push(li.textContent?.trim() ?? '');
          }
          if (list.items.length > 0) {
            container.push(list);
          }
          break;
        }
        case 'pre':
        case 'code':
          // Handle code blocks - preserve content but mark as code
          container.push({
            type: 'code',
            content: el.textContent?.trim() ?? '',
            language: el.getAttribute('data-language') || 'code'
          } as any);
          break;
        case 'blockquote':
          container.push({
            type: 'blockquote',
            content: el.textContent?.trim() ?? ''
          } as any);
          break;
        case 'img': {
          const src = el.getAttribute('src');
          const width = el.getAttribute('width');
          const height = el.getAttribute('height');
          container.push({
            type: 'image',
            src: src,
            alt: el.getAttribute('alt') || '',
            width: width ?? undefined,
            height: height ?? undefined,
          });
          break;
        }
        case 'br':
          container.push({ type: 'break' });
          break;
        default:
          // For other elements, process children
          for (const child of Array.from(el.childNodes)) {
            processNode(child, container);
          }
          break;
      }
    }
  };

  for (const child of Array.from(doc.body.childNodes)) {
    processNode(child);
  }
  return elements;
}

const styles = StyleSheet.create({
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    fontSize: 10,
    color: 'grey',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  footerLeft: {
    textAlign: 'left',
    flex: 1,
  },
  footerRight: {
    textAlign: 'right',
    flex: 1,
  },
});

const DetailedFindingsBySeverityPage: React.FC<DetailedFindingsBySeverityPageProps & { registerSectionPage?: (section: string, page: number) => void, sectionId?: string }> = ({ severity, findings, documentNumber, registerSectionPage, sectionId }) => {
  const severityColors: Record<string, { text: string; badge: string }> = {
    Critical: { text: '#800000', badge: '#800000' }, // maroon
    High: { text: '#dc2626', badge: '#dc2626' },     // red
    Medium: { text: '#fbbf24', badge: '#fbbf24' },   // yellow
    Low: { text: '#10b981', badge: '#10b981' },      // green
  };
  const colorKey = (severity.charAt(0).toUpperCase() + severity.slice(1).toLowerCase()) as keyof typeof severityColors;
  const titleColor = severityColors[colorKey]?.text || '#2563eb';
  const badgeColor = severityColors[colorKey]?.badge || '#2563eb';

  // If there are no findings, render nothing
  if (!findings || findings.length === 0) {
    return null;
  }
  // Render each finding on a new Page
  return (
    <>
      {findings.map((finding, idx) => {
        // Generate default abbreviation if missing
        const severityCounts: Record<string, number> = {};
        findings.slice(0, idx + 1).forEach((f) => {
          const sev = (f.severity_category || severity || '').toLowerCase();
          severityCounts[sev] = (severityCounts[sev] || 0) + 1;
        });
        const sev = (finding.severity_category || severity || '').toLowerCase();
        let abbrPrefix = '';
        if (sev === 'critical') abbrPrefix = 'C';
        else if (sev === 'high') abbrPrefix = 'H';
        else if (sev === 'medium') abbrPrefix = 'M';
        else if (sev === 'low') abbrPrefix = 'L';
        else abbrPrefix = 'X';
        const defaultAbbr = abbrPrefix + severityCounts[sev];
        const abbr = finding.abbreviation && finding.abbreviation.trim() ? finding.abbreviation : defaultAbbr;
        return (
          <Page key={idx} size="A4" id={idx === 0 ? sectionId : undefined} style={{
            flexDirection: 'column',
            backgroundColor: '#ffffff',
            padding: '20mm 8mm',
            fontFamily: 'Helvetica',
            fontSize: 12,
          }}>
            {/* Hidden Text to register section page for TOC (only on first finding) */}
            {registerSectionPage && idx === 0 && (
              <Text style={{ display: 'none', fontSize: 1 }} render={({ pageNumber }) => {
                registerSectionPage(`DetailedFindings_${severity}`, pageNumber);
                return '';
              }} />
            )}
            <View style={{ paddingHorizontal: 24, flexDirection: 'column', flex: 1 }}>
              <View style={{ flex: 1}}>
                {idx === 0 && (
                  <Text style={{ fontSize: 16, fontWeight: 'bold', color: titleColor, marginBottom: 12, lineHeight: 1.4 }}>
                    {severity.toUpperCase()} SEVERITY FINDINGS
                  </Text>
                )}
                <View style={{ backgroundColor: '#fff', borderWidth: 1, borderColor: '#e5e7eb', borderRadius: 8, padding: 12, marginBottom: 16 }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}>
                    <View style={{ backgroundColor: badgeColor, color: '#fff', borderRadius: 4, paddingHorizontal: 8, paddingVertical: 2, marginRight: 10 }}>
                      <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 12, lineHeight: 1.4 }}>{abbr}</Text>
                    </View>
                    <Text style={{
                      fontWeight: 'bold',
                      fontSize: 14,
                      color: '#1f2937',
                      maxWidth: '80%',
                      lineHeight: 1.4,
                      textAlign: 'justify'
                    }}>{finding.title}</Text>
                  </View>
                  {finding.scope && (
                    <View style={{ marginBottom: 12 }}>
                      <Text style={{ fontWeight: 'bold', fontSize: 13, color: '#374151', marginBottom: 6, lineHeight: 1.4 }}>I. Vulnerable Asset / Target</Text>
                      <Text style={{
                        fontSize: 12,
                        color: '#6b7280',
                        lineHeight: 1.4,
                        textAlign: 'justify'
                      }}>{finding.scope}</Text>
                    </View>
                  )}
                  {finding.description && (
                    <View style={{ marginBottom: 12 }}>
                      <Text style={{ fontWeight: 'bold', fontSize: 13, color: '#374151', marginBottom: 6, lineHeight: 1.4 }}>II. Description</Text>
                      <View style={{
                        fontSize: 12,
                        lineHeight: 1.4,
                        textAlign: 'justify'
                      }}>{renderPdfElements(ensurePdfElements(finding.description))}</View>
                    </View>
                  )}
                  {finding.instructions && (
                    <View style={{ marginBottom: 12 }}>
                      <Text style={{ fontWeight: 'bold', fontSize: 13, color: '#374151', marginBottom: 6, lineHeight: 1.4 }}>III. Steps to Reproduce</Text>
                      <View style={{
                        fontSize: 12,
                        lineHeight: 1.4,
                        textAlign: 'justify'
                      }}>{renderPdfElements(ensurePdfElements(finding.instructions))}</View>
                    </View>
                  )}
                  {finding.impact && (
                    <View style={{ marginBottom: 12 }}>
                      <Text style={{ fontWeight: 'bold', fontSize: 13, color: '#374151', marginBottom: 6, lineHeight: 1.4 }}>IV. Impact</Text>
                      <View style={{
                        fontSize: 12,
                        lineHeight: 1.4,
                        textAlign: 'justify'
                      }}>{renderPdfElements(ensurePdfElements(finding.impact))}</View>
                    </View>
                  )}
                  {finding.fix && (
                    <View style={{ marginBottom: 12 }}>
                      <Text style={{ fontWeight: 'bold', fontSize: 13, color: '#374151', marginBottom: 6, lineHeight: 1.4 }}>V. Recommendation</Text>
                      <View style={{
                        fontSize: 12,
                        lineHeight: 1.4,
                        textAlign: 'justify'
                      }}>{renderPdfElements(ensurePdfElements(finding.fix))}</View>
                    </View>
                  )}
                  {/* <View>
                    <Text style={{ fontWeight: 'bold', fontSize: 15, color: '#374151', marginBottom: 6 }}>VI. Submission Date</Text>
                    <Text style={{ fontSize: 12, color: '#6b7280' }}>{formatDateToDDMMYYYY(finding.submitted_date)}</Text>
                  </View> */}
                </View>
              </View>
            </View>
            <View style={styles.footer} fixed>
              <Text style={styles.footerLeft}>{documentNumber || 'Document Number'}</Text>
              <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
            </View>
          </Page>
        );
      })}
    </>
  );
};

export default DetailedFindingsBySeverityPage; 