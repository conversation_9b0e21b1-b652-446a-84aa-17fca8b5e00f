import React from 'react';
import { Page, View, Text, StyleSheet } from '@react-pdf/renderer';

const accentColor = '#2563eb';
const headerBg = '#dbeafe';
const tableHeaderBg = '#e0e7ef';
const tableRowAltBg = '#f3f4f6';
const dividerColor = '#e5e7eb';
const formulaBg = '#dbeafe';
const sectionTitleColor = accentColor;
const sectionTitleFontSize = 16;
const sectionTitleWeight = 'bold';
const sectionTitleSpacing = 1.2;
const sectionTitleLineHeight = 1.4;
const bodyFontSize = 12;
const bodyColor = '#374151';
const tableBorderRadius = 10;
const tableBorderColor = '#e5e7eb';
const tableCellPadding = 12;
const tableHeaderFontWeight = 'bold';
const tableHeaderFontSize = 12;
const tableCellFontSize = 12;
const tableCellColor = '#374151';
const tableHeaderColor = accentColor;
const appendixBg = '#f3f4f6';

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: '20mm 15mm',
    fontFamily: 'Helvetica',
    fontSize: bodyFontSize,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 18,
    marginTop: 8,
  },
  accentBar: {
    width: 6,
    height: 28,
    backgroundColor: accentColor,
    borderRadius: 3,
    marginRight: 12,
  },
  sectionTitle: {
    fontSize: sectionTitleFontSize,
    fontWeight: sectionTitleWeight,
    color: sectionTitleColor,
    letterSpacing: sectionTitleSpacing,
    lineHeight: sectionTitleLineHeight,
  },
  divider: {
    height: 1,
    backgroundColor: dividerColor,
    marginVertical: 12,
    opacity: 0.7,
  },
  formulaBox: {
    backgroundColor: formulaBg,
    borderRadius: 8,
    padding: 16,
    marginVertical: 12,
    alignItems: 'center',
  },
  formulaText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: accentColor,
    lineHeight: 1.4,
    textAlign: 'center',
  },
  table: {
    borderWidth: 1,
    borderColor: tableBorderColor,
    borderRadius: tableBorderRadius,
    marginBottom: 20,
    overflow: 'hidden',
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: tableHeaderBg,
  },
  tableHeaderCell: {
    padding: tableCellPadding,
    fontWeight: tableHeaderFontWeight,
    fontSize: tableHeaderFontSize,
    color: tableHeaderColor,
  },
  tableRow: {
    flexDirection: 'row',
    backgroundColor: '#fff',
  },
  tableRowAlt: {
    flexDirection: 'row',
    backgroundColor: tableRowAltBg,
  },
  tableCell: {
    padding: tableCellPadding,
    fontSize: tableCellFontSize,
    color: tableCellColor,
  },
  appendixBox: {
    // No background for table sections, only use for non-table content if needed
    borderRadius: 8,
    padding: 0,
    marginBottom: 0,
  },
  bodyText: {
    fontSize: bodyFontSize,
    color: bodyColor,
    marginBottom: 10,
    lineHeight: 1.4,
    textAlign: 'justify',
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    fontSize: 10,
    color: 'grey',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  footerLeft: {
    textAlign: 'left',
    flex: 1,
  },
  footerRight: {
    textAlign: 'right',
    flex: 1,
  },
});

// Helper for rendering tables with alternating row backgrounds
interface TableHeader {
  label: string;
  width: string;
}

type TableRow = string[];

const renderTable = (headers: TableHeader[], rows: TableRow[]) => (
  <View style={styles.table}>
    <View style={styles.tableHeader}>
      {headers.map((header: TableHeader, idx: number) => (
        <Text key={idx} style={[styles.tableHeaderCell, { width: header.width }]}>{header.label}</Text>
      ))}
    </View>
    {rows.map((row: string[], rowIdx: number) => (
      <View key={rowIdx} style={rowIdx % 2 === 0 ? styles.tableRow : styles.tableRowAlt}>
        {row.map((cell: string, cellIdx: number) => (
          <Text key={cellIdx} style={[styles.tableCell, { width: headers[cellIdx].width }]}>{cell}</Text>
        ))}
      </View>
    ))}
  </View>
);

// Accept documentNumber and registerSectionPage as props
const AppendixOwaspRiskRatingPages = (props: { documentNumber?: string, registerSectionPage?: (section: string, page: number) => void, sectionId?: string }) => [
  // Page 1: Intro & Approach
  <Page size="A4" id={props.sectionId} style={styles.page}>
    {props.registerSectionPage && (
      <Text style={{ display: 'none', fontSize: 1 }} render={({ pageNumber }) => {
        props.registerSectionPage?.('AppendixOwaspRiskRating_Page1', pageNumber);
        return '';
      }} />
    )}
    <View style={styles.sectionHeader}>
      <View style={styles.accentBar} />
      <Text style={styles.sectionTitle}>APPENDIX A: OWASP RISK RATING METHODOLOGY</Text>
      </View>
    <Text style={styles.bodyText}>
        Discovering vulnerabilities is important, but being able to estimate the associated risk to the business is just as important. Early in the life cycle, one may identify security concerns in the architecture or design by using threat modeling. Later, one may find security issues using code review or penetration testing. Or problems may not be discovered until the application is in production and is actually compromised.
      </Text>
    <Text style={styles.bodyText}>
        It is possible to estimate the severity of all of these risks to a business and make an informed decision about what to do about these risks. Having a system in place for rating risks will save time and eliminate arguing about priorities.
      </Text>
    <Text style={styles.bodyText}>
        OWASP Risk Rating Methodology is one such approach to calculate the risks imposed by a vulnerability. It is a customizable framework that takes into view the business factors and the end result is a score implying the risk imposed by the vulnerability.
      </Text>
    <View style={styles.sectionHeader}>
      <View style={styles.accentBar} />
      <Text style={styles.sectionTitle}>APPROACH</Text>
    </View>
    <Text style={styles.bodyText}>
        OWASP's approach for calculating risks can be summarized as:
      </Text>
    <View style={styles.formulaBox}>
      <Text style={styles.formulaText}>RISK = LIKELIHOOD * IMPACT</Text>
      </View>
      <View style={{ alignItems: 'center', marginTop: 16 }}>
      <Text style={{ color: accentColor, fontSize: 12, lineHeight: 1.4 }}>
          OWASP RISK RATING METHODOLOGY: https://owasp.org/www-community/OWASP_Risk_Rating_Methodology
        </Text>
    </View>
    <View style={styles.footer} fixed>
      <Text style={styles.footerLeft}>{props.documentNumber || 'Document Number'}</Text>
      <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
    </View>
  </Page>,

  // Page 2: Likelihood Factors (Threat Agent)
  <Page size="A4" style={styles.page}>
    {props.registerSectionPage && (
      <Text style={{ display: 'none', fontSize: 1 }} render={({ pageNumber }) => {
        props.registerSectionPage?.('AppendixOwaspRiskRating_Page2', pageNumber);
        return '';
      }} />
    )}
    <View style={styles.sectionHeader}>
      <View style={styles.accentBar} />
      <Text style={styles.sectionTitle}>FACTORS FOR ESTIMATING LIKELIHOOD</Text>
    </View>
    <Text style={styles.bodyText}>
      It takes into consideration the Threat Agent and the Vulnerability itself.
    </Text>
    <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#082480', marginBottom: 4, marginTop: 18 }}>A. Threat Agent</Text>
    <Text style={styles.bodyText}>
      It is used to estimate how likely it is for a particular threat agent to successfully exploit the vulnerability. Based on the following points, a score is generated.
    </Text>
    {renderTable(
      [
        { label: 'SCORE', width: '30%' },
        { label: 'SKILL LEVEL', width: '70%' },
      ],
      [
        ['1', 'No technical Skills'],
        ['3', 'Some Technical Skills'],
        ['5', 'Advanced Computer User'],
        ['6', 'Network and Programming Skills'],
        ['9', 'Security Penetration Skills'],
      ]
    )}
    {renderTable(
      [
        { label: 'SCORE', width: '30%' },
        { label: 'MOTIVE', width: '70%' },
      ],
      [
        ['1', 'Low or No Reward'],
        ['4', 'Possible Reward'],
        ['9', 'High Reward'],
      ]
    )}
    <View style={styles.footer} fixed>
      <Text style={styles.footerLeft}>{props.documentNumber || 'Document Number'}</Text>
      <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
    </View>
  </Page>,

  // Page 3: Opportunity & Size
  <Page size="A4" style={styles.page}>
    {props.registerSectionPage && (
      <Text style={{ display: 'none', fontSize: 1 }} render={({ pageNumber }) => {
        props.registerSectionPage?.('AppendixOwaspRiskRating_Page3', pageNumber);
        return '';
      }} />
    )}
    <View style={styles.appendixBox}>
      <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#082480', marginBottom: 4 }}>3. Opportunity (O):</Text>
      {renderTable(
        [
          { label: 'SCORE', width: '30%' },
          { label: 'OPPORTUNITY', width: '70%' },
        ],
        [
          ['0', 'Expensive Resources Required'],
          ['4', 'Special Access or Resources Required'],
          ['7', 'Some Access or Resources Required'],
          ['9', 'No Access or Resources Required'],
        ]
      )}
      <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#082480', marginBottom: 4, marginTop: 12 }}>4. Size (S):</Text>
      {renderTable(
        [
          { label: 'SCORE', width: '30%' },
          { label: 'SIZE', width: '70%' },
        ],
        [
          ['2', 'Developers/Administrators'],
          ['4', 'Logged-in Users'],
          ['5', 'Partners'],
          ['9', 'Anonymous Internet Users'],
        ]
      )}
    </View>
    <View style={styles.footer} fixed>
      <Text style={styles.footerLeft}>{props.documentNumber || 'Document Number'}</Text>
      <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
    </View>
  </Page>,

  // Page 4: Vulnerability Factors (ED, EE)
  <Page size="A4" style={styles.page}>
    {props.registerSectionPage && (
      <Text style={{ display: 'none', fontSize: 1 }} render={({ pageNumber }) => {
        props.registerSectionPage?.('AppendixOwaspRiskRating_Page4', pageNumber);
        return '';
      }} />
    )}
    <View style={styles.sectionHeader}>
      <View style={styles.accentBar} />
      <Text style={styles.sectionTitle}>B. Vulnerability Factors</Text>
    </View>
    <Text style={styles.bodyText}>
        We take into account the factors related to the vulnerability with the goal of the vulnerability being discovered and successfully exploited by the threat agents.
      </Text>
    <View style={styles.appendixBox}>
      <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#082480', marginBottom: 4 }}>1. Ease Of Discovery (ED):</Text>
      {renderTable(
        [
          { label: 'SCORE', width: '30%' },
          { label: 'EASE OF DISCOVERY', width: '70%' },
        ],
        [
          ['1', 'Practically Impossible to Discover'],
          ['3', 'Difficult to Discover'],
          ['7', 'Easy'],
          ['9', 'Automated Tools available for Discovery'],
        ]
      )}
      <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#082480', marginBottom: 4, marginTop: 12 }}>2. Ease Of Exploit (EE):</Text>
      {renderTable(
        [
          { label: 'SCORE', width: '30%' },
          { label: 'EASE OF EXPLOIT', width: '70%' },
        ],
        [
          ['1', 'Theoretical Exploitation Possible'],
          ['3', 'Exploitation is Difficult'],
          ['5', 'Exploitation is Easy'],
          ['9', 'Automated tools available for exploitation'],
        ]
      )}
    </View>
    <View style={styles.footer} fixed>
      <Text style={styles.footerLeft}>{props.documentNumber || 'Document Number'}</Text>
      <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
    </View>
  </Page>,

  // Page 5: Vulnerability Factors (A, ID) + Formula
  <Page size="A4" style={styles.page}>
    {props.registerSectionPage && (
      <Text style={{ display: 'none', fontSize: 1 }} render={({ pageNumber }) => {
        props.registerSectionPage?.('AppendixOwaspRiskRating_Page5', pageNumber);
        return '';
      }} />
    )}
    <View style={styles.appendixBox}>
      <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#082480', marginBottom: 4 }}>3. Awareness (A):</Text>
      {renderTable(
        [
          { label: 'SCORE', width: '30%' },
          { label: 'AWARENESS', width: '70%' },
        ],
        [
          ['1', 'Unknown'],
          ['3', 'Hidden'],
          ['5', 'Obvious'],
          ['9', 'Publicly Known'],
        ]
      )}
      <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#082480', marginBottom: 4, marginTop: 12 }}>4. Intrusion Detection (ID):</Text>
      {renderTable(
        [
          { label: 'SCORE', width: '30%' },
          { label: 'INTRUSION DETECTION', width: '70%' },
        ],
        [
          ['1', 'Active Detection'],
          ['3', 'Logged and reviewed'],
          ['5', 'Logged without review'],
          ['9', 'Not logged at all'],
        ]
      )}
      <View style={styles.formulaBox}>
        <Text style={styles.formulaText}>Likelihood score can be calculated as =  (SL + M + O + S + ED + EE + A + ID)/8</Text>
      </View>
    </View>
    <View style={styles.footer} fixed>
      <Text style={styles.footerLeft}>{props.documentNumber || 'Document Number'}</Text>
      <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
    </View>
  </Page>,

  // Page 6: Technical Impact Factors (LC, LI)
  <Page size="A4" style={styles.page}>
    {props.registerSectionPage && (
      <Text style={{ display: 'none', fontSize: 1 }} render={({ pageNumber }) => {
        props.registerSectionPage?.('AppendixOwaspRiskRating_Page6', pageNumber);
        return '';
      }} />
    )}
    <View style={styles.sectionHeader}>
      <View style={styles.accentBar} />
      <Text style={styles.sectionTitle}>FACTORS FOR ESTIMATING IMPACT</Text>
    </View>
    <Text style={styles.bodyText}>
        It takes into consideration the Technical Impact and the Business Impact that can be caused if the vulnerability is successfully exploited by the threat actors.
      </Text>
    <Text style={styles.bodyText}>
      <Text style={{ fontWeight: 'bold', color: '#082480' }}>A. Technical Impact Factors</Text> This section encompasses how the Confidentiality, Integrity, Availability (CIA) of the data and the service provided will be affected after the exploitation of the vulnerability.
      </Text>
    <View style={styles.appendixBox}>
      <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#082480', marginBottom: 4 }}>1. Loss Of Confidentiality (LC):</Text>
      {renderTable(
        [
          { label: 'SCORE', width: '30%' },
          { label: 'LOSS OF CONFIDENTIALITY', width: '70%' },
        ],
        [
          ['2', 'Minimal Non-Sensitive Data Disclosure'],
          ['6', 'Minimal Critical Data Disclosure'],
          ['7', 'Extensive Critical Data Disclosure'],
          ['9', 'All Data gets Disclosed'],
        ]
      )}
      <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#082480', marginBottom: 4, marginTop: 12 }}>2. Loss Of Integrity (LI):</Text>
      {renderTable(
        [
          { label: 'SCORE', width: '30%' },
          { label: 'LOSS OF INTEGRITY', width: '70%' },
        ],
        [
          ['1', 'Minimal and Slightly Corrupted'],
          ['3', 'Minimal and Seriously Corrupted'],
          ['5', 'Extensive and Slightly Corrupted'],
          ['7', 'Extensive and Seriously Corrupted'],
          ['9', 'All Data Corrupted'],
        ]
      )}
    </View>
    <View style={styles.footer} fixed>
      <Text style={styles.footerLeft}>{props.documentNumber || 'Document Number'}</Text>
      <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
    </View>
  </Page>,

  // Page 7: Technical Impact Factors (LAV, LAC)
  <Page size="A4" style={styles.page}>
    {props.registerSectionPage && (
      <Text style={{ display: 'none', fontSize: 1 }} render={({ pageNumber }) => {
        props.registerSectionPage?.('AppendixOwaspRiskRating_Page7', pageNumber);
        return '';
      }} />
    )}
    <View style={styles.appendixBox}>
      <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#082480', marginBottom: 4 }}>3. Loss Of Availability (LAV):</Text>
      {renderTable(
        [
          { label: 'SCORE', width: '30%' },
          { label: 'LOSS OF AVAILABILITY', width: '70%' },
        ],
        [
          ['1', 'Minimal Secondary Services Interrupted'],
          ['5', 'Minimal Primary Services Interrupted'],
          ['5', 'Extensive Secondary Services Interrupted'],
          ['7', 'Extensive Primary Services Interrupted'],
          ['9', 'All Services Completely Lost'],
        ]
      )}
      <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#082480', marginBottom: 4, marginTop: 12 }}>4. Loss Of Accountability (LAC):</Text>
      {renderTable(
        [
          { label: 'SCORE', width: '30%' },
          { label: 'LOSS OF ACCOUNTABILITY', width: '70%' },
        ],
        [
          ['1', 'Fully Traceable'],
          ['7', 'Possibly Traceable'],
          ['9', 'Threat Agent Completely Anonymous'],
        ]
      )}
    </View>
    <View style={styles.footer} fixed>
      <Text style={styles.footerLeft}>{props.documentNumber || 'Document Number'}</Text>
      <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
    </View>
  </Page>,

  // Page 8: Business Impact Factors (FD, RD)
  <Page size="A4" style={styles.page}>
    {props.registerSectionPage && (
      <Text style={{ display: 'none', fontSize: 1 }} render={({ pageNumber }) => {
        props.registerSectionPage?.('AppendixOwaspRiskRating_Page8', pageNumber);
        return '';
      }} />
    )}
    <View style={styles.sectionHeader}>
      <View style={styles.accentBar} />
      <Text style={styles.sectionTitle}>B. Business Impact Factors</Text>
    </View>
    <Text style={styles.bodyText}>
        This is the final impact factor. The effects on the business after the exploitation of the vulnerability is considered
      </Text>
    <View style={styles.appendixBox}>
      <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#082480', marginBottom: 4 }}>1. Financial Damage (FD):</Text>
      {renderTable(
        [
          { label: 'SCORE', width: '30%' },
          { label: 'FINANCIAL DAMAGE', width: '70%' },
        ],
        [
          ['1', 'Less than the cost to fix the vulnerability'],
          ['3', 'Minor effect on the annual profit'],
          ['7', 'Significant effect on the annual profit'],
          ['9', 'Exploitation results in Bankruptcy'],
        ]
      )}
      <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#082480', marginBottom: 4, marginTop: 12 }}>2. Reputation Damage (RD):</Text>
      {renderTable(
        [
          { label: 'SCORE', width: '30%' },
          { label: 'REPUTATION DAMAGE', width: '70%' },
        ],
        [
          ['1', 'Minimal Damage'],
          ['4', 'Loss of major Accounts'],
          ['5', 'Loss of Goodwill'],
          ['9', 'Brand Damage'],
        ]
      )}
    </View>
    <View style={styles.footer} fixed>
      <Text style={styles.footerLeft}>{props.documentNumber || 'Document Number'}</Text>
      <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
    </View>
  </Page>,

  // Page 9: Business Impact Factors (NC, PV) + Formula
  <Page size="A4" style={styles.page}>
    {props.registerSectionPage && (
      <Text style={{ display: 'none', fontSize: 1 }} render={({ pageNumber }) => {
        props.registerSectionPage?.('AppendixOwaspRiskRating_Page9', pageNumber);
        return '';
      }} />
    )}
    <View style={styles.appendixBox}>
      <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#082480', marginBottom: 4 }}>3. Non-Compliance (NC):</Text>
      {renderTable(
        [
          { label: 'SCORE', width: '30%' },
          { label: 'NON-COMPLIANCE', width: '70%' },
        ],
        [
          ['2', 'Minor Violation'],
          ['5', 'Clear Violation'],
          ['7', 'High Profile Violation'],
        ]
      )}
      <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#082480', marginBottom: 4, marginTop: 12 }}>4. Privacy Violation (PV):</Text>
      {renderTable(
        [
          { label: 'SCORE', width: '30%' },
          { label: 'PRIVACY VIOLATION', width: '70%' },
        ],
        [
          ['3', 'PII of one individual Disclosed'],
          ['5', 'PII of hundreds of people Disclosed'],
          ['7', 'PII of thousands of people Disclosed'],
          ['9', 'PII of millions of people Disclosed'],
        ]
      )}
      <View style={styles.formulaBox}>
        <Text style={styles.formulaText}>Impact Score can be calculated as = ( LC + LI + LAV + LAC + FD + RD + NC + PV )/8</Text>
      </View>
    </View>
    <View style={styles.footer} fixed>
      <Text style={styles.footerLeft}>{props.documentNumber || 'Document Number'}</Text>
      <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
    </View>
  </Page>,

  // Page 10: Risk Matrix
  <Page size="A4" style={styles.page}>
    {props.registerSectionPage && (
      <Text style={{ display: 'none', fontSize: 1 }} render={({ pageNumber }) => {
        props.registerSectionPage?.('AppendixOwaspRiskRating_Page10', pageNumber);
        return '';
      }} />
    )}
    <View style={styles.sectionHeader}>
      <View style={styles.accentBar} />
      <Text style={styles.sectionTitle}>RISK MATRIX</Text>
    </View>
    <Text style={styles.bodyText}>
        Once the likelihood and the impact scores have been calculated, they are replaced by their corresponding risk levels as per the chart given below.
      </Text>
    <View style={styles.appendixBox}>
      {renderTable(
        [
          { label: 'SCORE', width: '40%' },
          { label: 'LIKELIHOOD AND IMPACT LEVELS', width: '60%' },
        ],
        [
          ['0 to <3', 'Low'],
          ['3 to <6', 'Medium'],
          ['6 to 9', 'High'],
        ]
      )}
        </View>
    <Text style={styles.bodyText}>
        After the Impact and Likelihood risk levels are generated, the intersection of their risk levels is found to calculate the final risk level.
      </Text>
    <View style={styles.appendixBox}>
      {/* Risk Matrix Table (simplified for PDF) */}
      <View style={[styles.table, { marginBottom: 0 }]}> 
        <View style={styles.tableHeader}>
          <Text style={[styles.tableHeaderCell, { width: '25%' }]}>IMPACT \ LIKELIHOOD</Text>
          <Text style={[styles.tableHeaderCell, { width: '25%' }]}>LOW</Text>
          <Text style={[styles.tableHeaderCell, { width: '25%' }]}>MEDIUM</Text>
          <Text style={[styles.tableHeaderCell, { width: '25%' }]}>HIGH</Text>
        </View>
        {/* Row: Low Impact */}
        <View style={styles.tableRowAlt}>
          <Text style={[styles.tableCell, { width: '25%', fontWeight: 'bold' }]}>LOW</Text>
          <Text style={[styles.tableCell, { width: '25%', backgroundColor: '#bbf7d0', fontWeight: 'bold' }]}>LOW</Text>
          <Text style={[styles.tableCell, { width: '25%', backgroundColor: '#fef9c3', fontWeight: 'bold' }]}>MEDIUM</Text>
          <Text style={[styles.tableCell, { width: '25%', backgroundColor: '#fecaca', fontWeight: 'bold' }]}>HIGH</Text>
        </View>
        {/* Row: Medium Impact */}
        <View style={styles.tableRow}>
          <Text style={[styles.tableCell, { width: '25%', fontWeight: 'bold' }]}>MEDIUM</Text>
          <Text style={[styles.tableCell, { width: '25%', backgroundColor: '#fef9c3', fontWeight: 'bold' }]}>MEDIUM</Text>
          <Text style={[styles.tableCell, { width: '25%', backgroundColor: '#bbf7d0', fontWeight: 'bold' }]}>LOW</Text>
          <Text style={[styles.tableCell, { width: '25%', backgroundColor: '#fef9c3', fontWeight: 'bold' }]}>MEDIUM</Text>
        </View>
        {/* Row: High Impact */}
        <View style={styles.tableRowAlt}>
          <Text style={[styles.tableCell, { width: '25%', fontWeight: 'bold' }]}>HIGH</Text>
          <Text style={[styles.tableCell, { width: '25%', backgroundColor: '#fecaca', fontWeight: 'bold' }]}>HIGH</Text>
          <Text style={[styles.tableCell, { width: '25%', backgroundColor: '#fef9c3', fontWeight: 'bold' }]}>MEDIUM</Text>
          <Text style={[styles.tableCell, { width: '25%', backgroundColor: '#f87171', fontWeight: 'bold', color: '#fff' }]}>CRITICAL</Text>
        </View>
      </View>
    </View>
    <View style={styles.footer} fixed>
      <Text style={styles.footerLeft}>{props.documentNumber || 'Document Number'}</Text>
      <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
    </View>
  </Page>
];

export default AppendixOwaspRiskRatingPages; 