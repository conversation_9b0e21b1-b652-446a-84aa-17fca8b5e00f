import { useState, useEffect } from "react";
import { getRetestStatusByRetestId } from "../../api/endpoints/retests/retests";

const useRetestStatusRetestId = (retestId?: string) => {
  const [status, setStatus] = useState<string | null>(null);
  const [createdAt, setCreatedAt] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!retestId) return;

    const fetchRetestStatus = async () => {
      try {
        const { status, createdAt } = await getRetestStatusByRetestId(
          parseInt(retestId, 10)
        );
        setStatus(status);
        setCreatedAt(createdAt);
      } catch (err) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError(String(err));
        }
      } finally {
        setLoading(false);
      }
    };

    fetchRetestStatus();
  }, [retestId]);

  return { status, createdAt, loading, error };
};

export default useRetestStatusRetestId;
