import React from 'react';
import { Page, View, Text, StyleSheet } from '@react-pdf/renderer';
import { ReportData } from '../../types/report.types';

interface RecommendationsPageProps {
  reportData: ReportData;
  sectionId?: string;
}

const styles = StyleSheet.create({
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    fontSize: 10,
    color: 'grey',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  footerLeft: {
    textAlign: 'left',
    flex: 1,
  },
  footerRight: {
    textAlign: 'right',
    flex: 1,
  },
});

const RecommendationsPage: React.FC<RecommendationsPageProps> = ({ reportData, sectionId }) => (
  <Page size="A4" id={sectionId} style={{
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: '20mm 15mm',
    fontFamily: 'Helvetica',
    fontSize: 12,
  }}>
    <View style={{ paddingHorizontal: 24, flexDirection: 'column', flex: 1 }}>
      <View style={{ flex: 1}}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#2563eb', marginBottom: 12, lineHeight: 1.4 }}>
          RECOMMENDATIONS
        </Text>
        <Text style={{ fontSize: 12, lineHeight: 1.4, marginBottom: 14, color: '#374151', textAlign: 'justify' }}>
          Based on the identified vulnerabilities, here are some key recommendations:
        </Text>
        {(() => {
          const recommendations = Array.isArray(reportData.recommendations_list) ? reportData.recommendations_list : [];
          return recommendations.length > 0 ? (
            <View style={{ paddingLeft: 16, borderRadius: 8, marginBottom: 12 }}>
              {recommendations.map((rec, idx) => (
                <View key={idx} style={{ marginBottom: 10, flexDirection: 'row', alignItems: 'flex-start' }}>
                  <Text style={{ fontSize: 12, color: '#374151', marginRight: 6, lineHeight: 1.4 }}>•</Text>
                  <Text style={{ fontSize: 12, color: '#374151', lineHeight: 1.4 }}>
                    <Text style={{ fontWeight: 'bold' }}>{rec.title}</Text>
                    {rec.description ? `: ${rec.description}` : ''}
                  </Text>
                </View>
              ))}
            </View>
          ) : (
            <Text style={{ fontSize: 12, color: '#374151', fontStyle: 'italic', lineHeight: 1.4 }}>
              No recommendations provided.
            </Text>
          );
        })()}
      </View>
    </View>
    <View style={styles.footer} fixed>
      <Text style={styles.footerLeft}>{reportData.document_number || 'Document Number'}</Text>
      <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
    </View>
  </Page>
);

export default RecommendationsPage; 