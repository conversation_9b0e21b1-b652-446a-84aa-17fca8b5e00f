{"ast": null, "code": "var _s = $RefreshSig$();\nimport toast from \"react-hot-toast\";\nimport { ProgramType, prepareProgramFormData } from \"../../api/endpoints/programs/parsePrograms\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useDeleteProgramMutation, useGetProgramQuery, usePostProgramActivationMutation, useSetPrivateAccessUsersMutation, useUpdateProgramMutation } from \"../../api/endpoints/programsApi\";\n/**\r\n * Provides access to a single program\r\n */\nconst useProgram = id => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    data: program,\n    isError,\n    isLoading\n  } = useGetProgramQuery(id, {\n    skip: id === undefined || id === null || isNaN(id)\n  });\n  const [updateProgramMutation] = useUpdateProgramMutation();\n  const [setPrivateAccessUsersMutation] = useSetPrivateAccessUsersMutation();\n  const [postProgramActivationMutataion] = usePostProgramActivationMutation();\n  const [deleteProgramMutation] = useDeleteProgramMutation();\n\n  /**\r\n   * Provides error & success messages when updating private access users\r\n   */\n  const setPrivateAccessUsers = users => {\n    if (id) {\n      const loader = toast.loading(\"Updating private access users...\");\n      setPrivateAccessUsersMutation({\n        id,\n        users\n      }).then(res => {\n        toast.remove(loader);\n        if (\"error\" in res) {\n          toast.error(\"Failed to update private access users...\");\n        } else {\n          toast.success(\"Updated private access users!\");\n        }\n      });\n    } else {\n      toast.error(\"Failed to update private access users...\");\n    }\n  };\n\n  /**\r\n   * Adds the given user to the list of private access users for this program\r\n   */\n  const addPrivateAccessUser = userId => {\n    var _program$privateAcces;\n    if (id && program) setPrivateAccessUsers([...(((_program$privateAcces = program.privateAccessUsers) === null || _program$privateAcces === void 0 ? void 0 : _program$privateAcces.filter(id => id !== userId)) || []), userId]);\n  };\n\n  /**\r\n   * Removes the given user from the list of private access users for this program\r\n   */\n  const removePrivateAccessUser = userId => {\n    if (id && program) setPrivateAccessUsers((program.privateAccessUsers || []).filter(id => id !== userId));\n  };\n\n  /**\r\n   * Makes a request to save the program in the backend\r\n   * (creating it if it does not exist)\r\n   */\n  const saveProgram = data => {\n    var _data$rewards, _data$rewards2, _data$rewards3, _data$rewards4;\n    const loader = toast.loading(\"Saving...\");\n\n    // Extract file attachments from the data\n    const attachments = Array.isArray(data.attachments) ? data.attachments.filter(item => item instanceof File) : [];\n    console.log(\"Raw form data in saveProgram:\", {\n      ...data,\n      jira_url: data.jira_url ? \"[EXISTS]\" : \"[MISSING]\",\n      jira_email: data.jira_email ? \"[EXISTS]\" : \"[MISSING]\",\n      jira_api_token: data.jira_api_token ? \"[EXISTS]\" : \"[MISSING]\",\n      jira_project_key: data.jira_project_key ? \"[EXISTS]\" : \"[MISSING]\",\n      slack_channel_link: data.slack_channel_link ? \"[EXISTS]\" : \"[MISSING]\"\n    });\n    const details = {\n      ...data,\n      payoutRange: [((_data$rewards = data.rewards) === null || _data$rewards === void 0 ? void 0 : _data$rewards.low) || 0, ((_data$rewards2 = data.rewards) === null || _data$rewards2 === void 0 ? void 0 : _data$rewards2.medium) || 0, ((_data$rewards3 = data.rewards) === null || _data$rewards3 === void 0 ? void 0 : _data$rewards3.high) || 0, ((_data$rewards4 = data.rewards) === null || _data$rewards4 === void 0 ? void 0 : _data$rewards4.critical) || 0],\n      private: data.private === 1 ? true : data.private === 0 ? false : false,\n      triaged: id ? undefined : true,\n      profilePicture: undefined,\n      attachments: undefined,\n      rewards: undefined,\n      notification_methods: data.notification_methods,\n      jira_url: data.jira_url,\n      jira_email: data.jira_email,\n      jira_api_token: data.jira_api_token,\n      jira_project_key: data.jira_project_key,\n      existingAttachments: data.existingAttachments || []\n    };\n    console.log(\"Details after processing in saveProgram:\", {\n      ...details,\n      jira_url: details.jira_url ? \"[EXISTS]\" : \"[MISSING]\",\n      jira_email: details.jira_email ? \"[EXISTS]\" : \"[MISSING]\",\n      jira_api_token: details.jira_api_token ? \"[EXISTS]\" : \"[MISSING]\",\n      jira_project_key: details.jira_project_key ? \"[EXISTS]\" : \"[MISSING]\",\n      slack_channel_link: details.slack_channel_link ? \"[EXISTS]\" : \"[MISSING]\"\n    });\n    const profilePicture = data.profilePicture && data.profilePicture.length > 0 ? data.profilePicture[0] : undefined;\n\n    // Validation checks\n    if (details.type === ProgramType.VDP && details.private) {\n      toast.error(\"VDP cannot be private\");\n      toast.remove(loader);\n      return;\n    }\n    if (details.type === ProgramType.PTAAS && details.private === false) {\n      toast.error(\"PTAAS cannot be public\");\n      toast.remove(loader);\n      return;\n    }\n    updateProgramMutation({\n      id,\n      data: prepareProgramFormData({\n        details,\n        icon: profilePicture,\n        attachments: attachments\n      })\n    }).then(res => {\n      toast.remove(loader);\n      if (\"error\" in res) {\n        toast.error(\"Failed to save program\");\n      } else {\n        toast.success(\"Program saved\");\n        if (res !== null && res !== void 0 && res.data) {\n          navigate(`/dashboard/programs/${res.data}`);\n        } else if (id) {\n          navigate(`/dashboard/programs/${id}`);\n        } else {\n          navigate(`/dashboard/programs`);\n        }\n      }\n    });\n  };\n  return {\n    program,\n    isLoading,\n    isError,\n    addPrivateAccessUser,\n    removePrivateAccessUser,\n    saveProgram,\n    toggleProgramActivation: isActivated => {\n      if (id) {\n        const loader = toast.loading(\"Processing...\");\n        postProgramActivationMutataion({\n          id,\n          isActivated\n        }).then(res => {\n          toast.remove(loader);\n          if (\"error\" in res) {\n            toast.error(`Failed to ${isActivated ? \"activate\" : \"deactivate\"} program`);\n          } else {\n            toast.success(`Program ${isActivated ? \"activated!\" : \"deactivated.\"}`);\n          }\n        });\n      }\n    },\n    deleteProgram: () => {\n      if (id) {\n        const loader = toast.loading(\"Deleting...\");\n        deleteProgramMutation(id).then(res => {\n          toast.remove(loader);\n          if (\"error\" in res) {\n            toast.error(\"Failed to delete program...\");\n          } else {\n            toast.success(\"Program deleted!\");\n          }\n        });\n      }\n    }\n  };\n};\n_s(useProgram, \"4C6ytZfMKkBQ1jpZ+7oFc/kJhvQ=\", false, function () {\n  return [useNavigate, useGetProgramQuery, useUpdateProgramMutation, useSetPrivateAccessUsersMutation, usePostProgramActivationMutation, useDeleteProgramMutation];\n});\nexport default useProgram;", "map": {"version": 3, "names": ["toast", "ProgramType", "prepareProgramFormData", "useNavigate", "useDeleteProgramMutation", "useGetProgramQuery", "usePostProgramActivationMutation", "useSetPrivateAccessUsersMutation", "useUpdateProgramMutation", "useProgram", "id", "_s", "navigate", "data", "program", "isError", "isLoading", "skip", "undefined", "isNaN", "updateProgramMutation", "setPrivateAccessUsersMutation", "postProgramActivationMutataion", "deleteProgramMutation", "setPrivateAccessUsers", "users", "loader", "loading", "then", "res", "remove", "error", "success", "addPrivateAccessUser", "userId", "_program$privateAcces", "privateAccessUsers", "filter", "removePrivateAccessUser", "saveProgram", "_data$rewards", "_data$rewards2", "_data$rewards3", "_data$rewards4", "attachments", "Array", "isArray", "item", "File", "console", "log", "jira_url", "jira_email", "jira_api_token", "jira_project_key", "slack_channel_link", "details", "payoutRange", "rewards", "low", "medium", "high", "critical", "private", "triaged", "profilePicture", "notification_methods", "existingAttachments", "length", "type", "VDP", "PTAAS", "icon", "toggleProgramActivation", "isActivated", "deleteProgram"], "sources": ["C:/Users/<USER>/Desktop/jira-old/CTB-Frontend/src/utils/hooks/programs/useProgram.ts"], "sourcesContent": ["import toast from \"react-hot-toast\";\r\nimport {\r\n  ProgramTarget,\r\n  ProgramType,\r\n  prepareProgramFormData\r\n} from \"../../api/endpoints/programs/parsePrograms\";\r\nimport { RewardTiers } from \"../../../components/editor/inputs/programs/ProgramRewardsInput\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport {\r\n  useDeleteProgramMutation,\r\n  useGetProgramQuery,\r\n  usePostProgramActivationMutation,\r\n  useSetPrivateAccessUsersMutation,\r\n  useUpdateProgramMutation\r\n} from \"../../api/endpoints/programsApi\";\r\n\r\nexport type ProgramUpdateValues = {\r\n  title: string;\r\n  description: string;\r\n  scope: string;\r\n  outOfScope: string;\r\n  targets: ProgramTarget[];\r\n  type: ProgramType;\r\n  rewards?: RewardTiers;\r\n  knownVulnerabilities?: string;\r\n  other?: string;\r\n  vpn?: string;\r\n  termsOfService: string;\r\n  credentials?: string;\r\n  rewardPolicy: string;\r\n  private?: boolean;\r\n  profilePicture?: Blob[];\r\n  attachments?: Blob[];\r\n  testingType?: string;\r\n  environmentType?: string;\r\n  complianceType?: string;\r\n  otherComplianceType?: string;\r\n  expectedStartDate?: Date;\r\n  expectedEndDate?: Date;\r\n  notification_methods?: String[];\r\n  existingAttachments?: string[];\r\n  jira_url?: string;\r\n  jira_email?: string;\r\n  jira_api_token?: string;\r\n  jira_project_key?: string;\r\n  slack_channel_link?: string;\r\n};\r\n\r\n/**\r\n * Provides access to a single program\r\n */\r\nconst useProgram = (id?: number) => {\r\n  const navigate = useNavigate();\r\n  const {\r\n    data: program,\r\n    isError,\r\n    isLoading\r\n  } = useGetProgramQuery(id as number, {\r\n    skip: id === undefined || id === null || isNaN(id)\r\n  });\r\n  const [updateProgramMutation] = useUpdateProgramMutation();\r\n  const [setPrivateAccessUsersMutation] = useSetPrivateAccessUsersMutation();\r\n  const [postProgramActivationMutataion] = usePostProgramActivationMutation();\r\n  const [deleteProgramMutation] = useDeleteProgramMutation();\r\n\r\n  /**\r\n   * Provides error & success messages when updating private access users\r\n   */\r\n  const setPrivateAccessUsers = (users: number[]) => {\r\n    if (id) {\r\n      const loader = toast.loading(\"Updating private access users...\");\r\n\r\n      setPrivateAccessUsersMutation({ id, users }).then(res => {\r\n        toast.remove(loader);\r\n\r\n        if (\"error\" in res) {\r\n          toast.error(\"Failed to update private access users...\");\r\n        } else {\r\n          toast.success(\"Updated private access users!\");\r\n        }\r\n      });\r\n    } else {\r\n      toast.error(\"Failed to update private access users...\");\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Adds the given user to the list of private access users for this program\r\n   */\r\n  const addPrivateAccessUser = (userId: number) => {\r\n    if (id && program)\r\n      setPrivateAccessUsers([\r\n        ...(program.privateAccessUsers?.filter(id => id !== userId) || []),\r\n        userId\r\n      ]);\r\n  };\r\n\r\n  /**\r\n   * Removes the given user from the list of private access users for this program\r\n   */\r\n  const removePrivateAccessUser = (userId: number) => {\r\n    if (id && program)\r\n      setPrivateAccessUsers(\r\n        (program.privateAccessUsers || []).filter(id => id !== userId)\r\n      );\r\n  };\r\n\r\n  /**\r\n   * Makes a request to save the program in the backend\r\n   * (creating it if it does not exist)\r\n   */\r\n  const saveProgram = (\r\n    data: Omit<ProgramUpdateValues, \"private\"> & {\r\n      private?: 0 | 1;\r\n      existingAttachments?: string[];\r\n    }\r\n  ) => {\r\n    const loader = toast.loading(\"Saving...\");\r\n\r\n    // Extract file attachments from the data\r\n    const attachments = Array.isArray(data.attachments)\r\n      ? data.attachments.filter(item => item instanceof File)\r\n      : [];\r\n\r\n    console.log(\"Raw form data in saveProgram:\", {\r\n      ...data,\r\n      jira_url: data.jira_url ? \"[EXISTS]\" : \"[MISSING]\",\r\n      jira_email: data.jira_email ? \"[EXISTS]\" : \"[MISSING]\",\r\n      jira_api_token: data.jira_api_token ? \"[EXISTS]\" : \"[MISSING]\",\r\n      jira_project_key: data.jira_project_key ? \"[EXISTS]\" : \"[MISSING]\",\r\n      slack_channel_link: data.slack_channel_link ? \"[EXISTS]\" : \"[MISSING]\",\r\n    });\r\n\r\n    const details = {\r\n      ...data,\r\n      payoutRange: [\r\n        data.rewards?.low || 0,\r\n        data.rewards?.medium || 0,\r\n        data.rewards?.high || 0,\r\n        data.rewards?.critical || 0\r\n      ],\r\n      private: data.private === 1 ? true : data.private === 0 ? false : false,\r\n      triaged: id ? undefined : true,\r\n      profilePicture: undefined,\r\n      attachments: undefined,\r\n      rewards: undefined,\r\n      notification_methods: data.notification_methods,\r\n      jira_url: data.jira_url,\r\n      jira_email: data.jira_email,\r\n      jira_api_token: data.jira_api_token,\r\n      jira_project_key: data.jira_project_key,\r\n      existingAttachments: data.existingAttachments || []\r\n    };\r\n    \r\n    console.log(\"Details after processing in saveProgram:\", {\r\n      ...details,\r\n      jira_url: details.jira_url ? \"[EXISTS]\" : \"[MISSING]\",\r\n      jira_email: details.jira_email ? \"[EXISTS]\" : \"[MISSING]\",\r\n      jira_api_token: details.jira_api_token ? \"[EXISTS]\" : \"[MISSING]\",\r\n      jira_project_key: details.jira_project_key ? \"[EXISTS]\" : \"[MISSING]\",\r\n      slack_channel_link: details.slack_channel_link ? \"[EXISTS]\" : \"[MISSING]\",\r\n    });\r\n\r\n    const profilePicture =\r\n      data.profilePicture && data.profilePicture.length > 0\r\n        ? data.profilePicture[0]\r\n        : undefined;\r\n\r\n    // Validation checks\r\n    if (details.type === ProgramType.VDP && details.private) {\r\n      toast.error(\"VDP cannot be private\");\r\n      toast.remove(loader);\r\n      return;\r\n    }\r\n    if (details.type === ProgramType.PTAAS && details.private === false) {\r\n      toast.error(\"PTAAS cannot be public\");\r\n      toast.remove(loader);\r\n      return;\r\n    }\r\n\r\n    updateProgramMutation({\r\n      id,\r\n      data: prepareProgramFormData({\r\n        details,\r\n        icon: profilePicture,\r\n        attachments: attachments\r\n      })\r\n    }).then(res => {\r\n      toast.remove(loader);\r\n      if (\"error\" in res) {\r\n        toast.error(\"Failed to save program\");\r\n      } else {\r\n        toast.success(\"Program saved\");\r\n        if (res?.data) {\r\n          navigate(`/dashboard/programs/${(res as { data: number }).data}`);\r\n        } else if (id) {\r\n          navigate(`/dashboard/programs/${id}`);\r\n        } else {\r\n          navigate(`/dashboard/programs`);\r\n        }\r\n      }\r\n    });\r\n  };\r\n\r\n  return {\r\n    program,\r\n    isLoading,\r\n    isError,\r\n    addPrivateAccessUser,\r\n    removePrivateAccessUser,\r\n    saveProgram,\r\n    toggleProgramActivation: (isActivated: boolean) => {\r\n      if (id) {\r\n        const loader = toast.loading(\"Processing...\");\r\n\r\n        postProgramActivationMutataion({ id, isActivated }).then(res => {\r\n          toast.remove(loader);\r\n\r\n          if (\"error\" in res) {\r\n            toast.error(\r\n              `Failed to ${isActivated ? \"activate\" : \"deactivate\"} program`\r\n            );\r\n          } else {\r\n            toast.success(\r\n              `Program ${isActivated ? \"activated!\" : \"deactivated.\"}`\r\n            );\r\n          }\r\n        });\r\n      }\r\n    },\r\n    deleteProgram: () => {\r\n      if (id) {\r\n        const loader = toast.loading(\"Deleting...\");\r\n\r\n        deleteProgramMutation(id).then(res => {\r\n          toast.remove(loader);\r\n\r\n          if (\"error\" in res) {\r\n            toast.error(\"Failed to delete program...\");\r\n          } else {\r\n            toast.success(\"Program deleted!\");\r\n          }\r\n        });\r\n      }\r\n    }\r\n  };\r\n};\r\n\r\nexport default useProgram;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,iBAAiB;AACnC,SAEEC,WAAW,EACXC,sBAAsB,QACjB,4CAA4C;AAEnD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,wBAAwB,EACxBC,kBAAkB,EAClBC,gCAAgC,EAChCC,gCAAgC,EAChCC,wBAAwB,QACnB,iCAAiC;AAkCxC;AACA;AACA;AACA,MAAMC,UAAU,GAAIC,EAAW,IAAK;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM;IACJU,IAAI,EAAEC,OAAO;IACbC,OAAO;IACPC;EACF,CAAC,GAAGX,kBAAkB,CAACK,EAAE,EAAY;IACnCO,IAAI,EAAEP,EAAE,KAAKQ,SAAS,IAAIR,EAAE,KAAK,IAAI,IAAIS,KAAK,CAACT,EAAE;EACnD,CAAC,CAAC;EACF,MAAM,CAACU,qBAAqB,CAAC,GAAGZ,wBAAwB,CAAC,CAAC;EAC1D,MAAM,CAACa,6BAA6B,CAAC,GAAGd,gCAAgC,CAAC,CAAC;EAC1E,MAAM,CAACe,8BAA8B,CAAC,GAAGhB,gCAAgC,CAAC,CAAC;EAC3E,MAAM,CAACiB,qBAAqB,CAAC,GAAGnB,wBAAwB,CAAC,CAAC;;EAE1D;AACF;AACA;EACE,MAAMoB,qBAAqB,GAAIC,KAAe,IAAK;IACjD,IAAIf,EAAE,EAAE;MACN,MAAMgB,MAAM,GAAG1B,KAAK,CAAC2B,OAAO,CAAC,kCAAkC,CAAC;MAEhEN,6BAA6B,CAAC;QAAEX,EAAE;QAAEe;MAAM,CAAC,CAAC,CAACG,IAAI,CAACC,GAAG,IAAI;QACvD7B,KAAK,CAAC8B,MAAM,CAACJ,MAAM,CAAC;QAEpB,IAAI,OAAO,IAAIG,GAAG,EAAE;UAClB7B,KAAK,CAAC+B,KAAK,CAAC,0CAA0C,CAAC;QACzD,CAAC,MAAM;UACL/B,KAAK,CAACgC,OAAO,CAAC,+BAA+B,CAAC;QAChD;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLhC,KAAK,CAAC+B,KAAK,CAAC,0CAA0C,CAAC;IACzD;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAME,oBAAoB,GAAIC,MAAc,IAAK;IAAA,IAAAC,qBAAA;IAC/C,IAAIzB,EAAE,IAAII,OAAO,EACfU,qBAAqB,CAAC,CACpB,IAAI,EAAAW,qBAAA,GAAArB,OAAO,CAACsB,kBAAkB,cAAAD,qBAAA,uBAA1BA,qBAAA,CAA4BE,MAAM,CAAC3B,EAAE,IAAIA,EAAE,KAAKwB,MAAM,CAAC,KAAI,EAAE,CAAC,EAClEA,MAAM,CACP,CAAC;EACN,CAAC;;EAED;AACF;AACA;EACE,MAAMI,uBAAuB,GAAIJ,MAAc,IAAK;IAClD,IAAIxB,EAAE,IAAII,OAAO,EACfU,qBAAqB,CACnB,CAACV,OAAO,CAACsB,kBAAkB,IAAI,EAAE,EAAEC,MAAM,CAAC3B,EAAE,IAAIA,EAAE,KAAKwB,MAAM,CAC/D,CAAC;EACL,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMK,WAAW,GACf1B,IAGC,IACE;IAAA,IAAA2B,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;IACH,MAAMjB,MAAM,GAAG1B,KAAK,CAAC2B,OAAO,CAAC,WAAW,CAAC;;IAEzC;IACA,MAAMiB,WAAW,GAAGC,KAAK,CAACC,OAAO,CAACjC,IAAI,CAAC+B,WAAW,CAAC,GAC/C/B,IAAI,CAAC+B,WAAW,CAACP,MAAM,CAACU,IAAI,IAAIA,IAAI,YAAYC,IAAI,CAAC,GACrD,EAAE;IAENC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;MAC3C,GAAGrC,IAAI;MACPsC,QAAQ,EAAEtC,IAAI,CAACsC,QAAQ,GAAG,UAAU,GAAG,WAAW;MAClDC,UAAU,EAAEvC,IAAI,CAACuC,UAAU,GAAG,UAAU,GAAG,WAAW;MACtDC,cAAc,EAAExC,IAAI,CAACwC,cAAc,GAAG,UAAU,GAAG,WAAW;MAC9DC,gBAAgB,EAAEzC,IAAI,CAACyC,gBAAgB,GAAG,UAAU,GAAG,WAAW;MAClEC,kBAAkB,EAAE1C,IAAI,CAAC0C,kBAAkB,GAAG,UAAU,GAAG;IAC7D,CAAC,CAAC;IAEF,MAAMC,OAAO,GAAG;MACd,GAAG3C,IAAI;MACP4C,WAAW,EAAE,CACX,EAAAjB,aAAA,GAAA3B,IAAI,CAAC6C,OAAO,cAAAlB,aAAA,uBAAZA,aAAA,CAAcmB,GAAG,KAAI,CAAC,EACtB,EAAAlB,cAAA,GAAA5B,IAAI,CAAC6C,OAAO,cAAAjB,cAAA,uBAAZA,cAAA,CAAcmB,MAAM,KAAI,CAAC,EACzB,EAAAlB,cAAA,GAAA7B,IAAI,CAAC6C,OAAO,cAAAhB,cAAA,uBAAZA,cAAA,CAAcmB,IAAI,KAAI,CAAC,EACvB,EAAAlB,cAAA,GAAA9B,IAAI,CAAC6C,OAAO,cAAAf,cAAA,uBAAZA,cAAA,CAAcmB,QAAQ,KAAI,CAAC,CAC5B;MACDC,OAAO,EAAElD,IAAI,CAACkD,OAAO,KAAK,CAAC,GAAG,IAAI,GAAGlD,IAAI,CAACkD,OAAO,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK;MACvEC,OAAO,EAAEtD,EAAE,GAAGQ,SAAS,GAAG,IAAI;MAC9B+C,cAAc,EAAE/C,SAAS;MACzB0B,WAAW,EAAE1B,SAAS;MACtBwC,OAAO,EAAExC,SAAS;MAClBgD,oBAAoB,EAAErD,IAAI,CAACqD,oBAAoB;MAC/Cf,QAAQ,EAAEtC,IAAI,CAACsC,QAAQ;MACvBC,UAAU,EAAEvC,IAAI,CAACuC,UAAU;MAC3BC,cAAc,EAAExC,IAAI,CAACwC,cAAc;MACnCC,gBAAgB,EAAEzC,IAAI,CAACyC,gBAAgB;MACvCa,mBAAmB,EAAEtD,IAAI,CAACsD,mBAAmB,IAAI;IACnD,CAAC;IAEDlB,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;MACtD,GAAGM,OAAO;MACVL,QAAQ,EAAEK,OAAO,CAACL,QAAQ,GAAG,UAAU,GAAG,WAAW;MACrDC,UAAU,EAAEI,OAAO,CAACJ,UAAU,GAAG,UAAU,GAAG,WAAW;MACzDC,cAAc,EAAEG,OAAO,CAACH,cAAc,GAAG,UAAU,GAAG,WAAW;MACjEC,gBAAgB,EAAEE,OAAO,CAACF,gBAAgB,GAAG,UAAU,GAAG,WAAW;MACrEC,kBAAkB,EAAEC,OAAO,CAACD,kBAAkB,GAAG,UAAU,GAAG;IAChE,CAAC,CAAC;IAEF,MAAMU,cAAc,GAClBpD,IAAI,CAACoD,cAAc,IAAIpD,IAAI,CAACoD,cAAc,CAACG,MAAM,GAAG,CAAC,GACjDvD,IAAI,CAACoD,cAAc,CAAC,CAAC,CAAC,GACtB/C,SAAS;;IAEf;IACA,IAAIsC,OAAO,CAACa,IAAI,KAAKpE,WAAW,CAACqE,GAAG,IAAId,OAAO,CAACO,OAAO,EAAE;MACvD/D,KAAK,CAAC+B,KAAK,CAAC,uBAAuB,CAAC;MACpC/B,KAAK,CAAC8B,MAAM,CAACJ,MAAM,CAAC;MACpB;IACF;IACA,IAAI8B,OAAO,CAACa,IAAI,KAAKpE,WAAW,CAACsE,KAAK,IAAIf,OAAO,CAACO,OAAO,KAAK,KAAK,EAAE;MACnE/D,KAAK,CAAC+B,KAAK,CAAC,wBAAwB,CAAC;MACrC/B,KAAK,CAAC8B,MAAM,CAACJ,MAAM,CAAC;MACpB;IACF;IAEAN,qBAAqB,CAAC;MACpBV,EAAE;MACFG,IAAI,EAAEX,sBAAsB,CAAC;QAC3BsD,OAAO;QACPgB,IAAI,EAAEP,cAAc;QACpBrB,WAAW,EAAEA;MACf,CAAC;IACH,CAAC,CAAC,CAAChB,IAAI,CAACC,GAAG,IAAI;MACb7B,KAAK,CAAC8B,MAAM,CAACJ,MAAM,CAAC;MACpB,IAAI,OAAO,IAAIG,GAAG,EAAE;QAClB7B,KAAK,CAAC+B,KAAK,CAAC,wBAAwB,CAAC;MACvC,CAAC,MAAM;QACL/B,KAAK,CAACgC,OAAO,CAAC,eAAe,CAAC;QAC9B,IAAIH,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEhB,IAAI,EAAE;UACbD,QAAQ,CAAE,uBAAuBiB,GAAG,CAAsBhB,IAAK,EAAC,CAAC;QACnE,CAAC,MAAM,IAAIH,EAAE,EAAE;UACbE,QAAQ,CAAE,uBAAsBF,EAAG,EAAC,CAAC;QACvC,CAAC,MAAM;UACLE,QAAQ,CAAE,qBAAoB,CAAC;QACjC;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED,OAAO;IACLE,OAAO;IACPE,SAAS;IACTD,OAAO;IACPkB,oBAAoB;IACpBK,uBAAuB;IACvBC,WAAW;IACXkC,uBAAuB,EAAGC,WAAoB,IAAK;MACjD,IAAIhE,EAAE,EAAE;QACN,MAAMgB,MAAM,GAAG1B,KAAK,CAAC2B,OAAO,CAAC,eAAe,CAAC;QAE7CL,8BAA8B,CAAC;UAAEZ,EAAE;UAAEgE;QAAY,CAAC,CAAC,CAAC9C,IAAI,CAACC,GAAG,IAAI;UAC9D7B,KAAK,CAAC8B,MAAM,CAACJ,MAAM,CAAC;UAEpB,IAAI,OAAO,IAAIG,GAAG,EAAE;YAClB7B,KAAK,CAAC+B,KAAK,CACR,aAAY2C,WAAW,GAAG,UAAU,GAAG,YAAa,UACvD,CAAC;UACH,CAAC,MAAM;YACL1E,KAAK,CAACgC,OAAO,CACV,WAAU0C,WAAW,GAAG,YAAY,GAAG,cAAe,EACzD,CAAC;UACH;QACF,CAAC,CAAC;MACJ;IACF,CAAC;IACDC,aAAa,EAAEA,CAAA,KAAM;MACnB,IAAIjE,EAAE,EAAE;QACN,MAAMgB,MAAM,GAAG1B,KAAK,CAAC2B,OAAO,CAAC,aAAa,CAAC;QAE3CJ,qBAAqB,CAACb,EAAE,CAAC,CAACkB,IAAI,CAACC,GAAG,IAAI;UACpC7B,KAAK,CAAC8B,MAAM,CAACJ,MAAM,CAAC;UAEpB,IAAI,OAAO,IAAIG,GAAG,EAAE;YAClB7B,KAAK,CAAC+B,KAAK,CAAC,6BAA6B,CAAC;UAC5C,CAAC,MAAM;YACL/B,KAAK,CAACgC,OAAO,CAAC,kBAAkB,CAAC;UACnC;QACF,CAAC,CAAC;MACJ;IACF;EACF,CAAC;AACH,CAAC;AAACrB,EAAA,CAnMIF,UAAU;EAAA,QACGN,WAAW,EAKxBE,kBAAkB,EAGUG,wBAAwB,EAChBD,gCAAgC,EAC/BD,gCAAgC,EACzCF,wBAAwB;AAAA;AAyL1D,eAAeK,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}