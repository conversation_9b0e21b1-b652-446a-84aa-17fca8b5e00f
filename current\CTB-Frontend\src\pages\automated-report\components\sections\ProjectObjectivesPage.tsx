import React from 'react';
import { Page, View, Text, StyleSheet } from '@react-pdf/renderer';
import { ReportData } from '../../types/report.types';
import { useSectionPages } from '../SectionPageContext';

interface ProjectObjectivesPageProps {
  reportData: ReportData;
  sectionId?: string;
}

const styles = StyleSheet.create({
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    fontSize: 10,
    color: 'grey',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  footerLeft: {
    textAlign: 'left',
    flex: 1,
  },
  footerRight: {
    textAlign: 'right',
    flex: 1,
  },
});

const ProjectObjectivesPage: React.FC<ProjectObjectivesPageProps> = ({ reportData, sectionId }) => {
  const { updateSectionPage } = useSectionPages();
  return (
    <Page size="A4" id={sectionId} style={{
      flexDirection: 'column',
      backgroundColor: '#ffffff',
      padding: '20mm 15mm',
      fontFamily: 'Helvetica',
      fontSize: 12,
    }}>
      <View style={{ paddingHorizontal: 24, flexDirection: 'column', flex: 1 }}>
        <View style={{ flex: 1}}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#2563eb', marginBottom: 12, lineHeight: 1.4 }}>
            PROJECT OBJECTIVES
          </Text>
          <Text style={{ fontSize: 12, lineHeight: 1.4, marginBottom: 14, color: '#374151', textAlign: 'justify' }}>
            {reportData.objectives ||
              'The objective of this assessment was to validate the overall security posture of the in-scope systems from a security perspective. It included determining the application\'s ability to resist common attack patterns and identifying vulnerable areas in the internal or external interfaces that might be exploited by a malicious user.'}
          </Text>
          {/* Assumptions Section */}
          <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#2563eb', marginBottom: 8, marginTop: 16, lineHeight: 1.4 }}>
            ASSUMPTIONS
          </Text>
          <Text style={{ fontSize: 12, lineHeight: 1.4, marginBottom: 10, color: '#374151', textAlign: 'justify' }}>
            {`These vulnerabilities are based on the current version of the web application which was live on ${reportData.current_date ? new Date(reportData.current_date).toLocaleDateString('en-GB', { day: 'numeric', month: 'short', year: 'numeric' }) : '20th Feb 2025'}.`}
          </Text>
          {reportData.assumptions && Array.isArray(reportData.assumptions) && reportData.assumptions.length > 0 && (
            <View style={{ paddingLeft: 16, borderLeftWidth: 4, borderLeftColor: '#d1d5db', backgroundColor: '#f3f4f6', borderRadius: 8, marginBottom: 12 }}>
              {reportData.assumptions.map((assumption, idx) => (
                <Text key={idx} style={{ fontSize: 12, color: '#374151', marginBottom: 6, lineHeight: 1.4 }}>• {assumption}</Text>
              ))}
            </View>
          )}
        </View>
      </View>
      <View style={styles.footer} fixed>
        <Text style={styles.footerLeft}>{reportData.document_number || 'Document Number'}</Text>
        <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
      </View>
      <Text style={{ display: 'none' }} render={({ pageNumber }) => { updateSectionPage('ProjectObjectives', pageNumber); return ''; }} fixed />
    </Page>
  );
};

export default ProjectObjectivesPage; 