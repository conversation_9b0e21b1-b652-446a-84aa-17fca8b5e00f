import {
  ProgramTarget,
  ProgramType,
} from "../../api/endpoints/programs/parsePrograms";
import { RewardTiers } from "../../../components/editor/inputs/programs/ProgramRewardsInput";
import {
  useGetPublicProgramQuery,
} from "../../api/endpoints/programsApi";

export type ProgramUpdateValues = {
  title: string;
  description: string;
  scope: string;
  outOfScope: string;
  targets: ProgramTarget[];
  type: ProgramType;
  rewards?: RewardTiers;
  knownVulnerabilities?: string;
  other?: string;
  vpn?: string;
  termsOfService: string;
  credentials?: string;
  rewardPolicy: string;
  private?: boolean;
  profilePicture?: Blob[];
};

/**
 * Provides access to a single program
 */
const usePublicProgram = (id?: number) => {
  const {
    data: program,
    isError,
    isLoading
  } = useGetPublicProgramQuery(id as number, {
    skip: id === undefined || id === null || isNaN(id)
  });

  return {
    program,
    isLoading,
    isError,
  };
};

export default usePublicProgram;
