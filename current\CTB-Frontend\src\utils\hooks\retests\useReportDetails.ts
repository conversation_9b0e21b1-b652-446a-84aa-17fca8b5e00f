import { useEffect, useState } from "react";
import { getReportDetailsByRetestId } from "../../../utils/api/endpoints/retests/retests";
import { parseCVSSVector } from "../../../components/forms/inputs/SeverityScoreSelector";
import { CTBReport } from "../../../utils/api/endpoints/reports/parseReports";

const useReportDetails = (retest_id: string | undefined) => {
  const [reportDetails, setReportDetails] = useState<CTBReport | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReportDetails = async () => {
      if (!retest_id) {
        setError("Retest ID is undefined");
        setLoading(false);
        return;
      }

      try {
        const response = await getReportDetailsByRetestId(retest_id);
        const parsedReport = response.data;

        // Parse the severity value
        if (parsedReport.severity) {
          const severityScore = parseCVSSVector(parsedReport.severity);
          parsedReport.severity = severityScore;
        }

        setReportDetails(parsedReport);
      } catch (err) {
        console.error("Failed to fetch report details:", err);
        setError("Failed to fetch report details");
      } finally {
        setLoading(false);
      }
    };

    fetchReportDetails();
  }, [retest_id]);

  return { reportDetails, loading, error };
};

export default useReportDetails;
