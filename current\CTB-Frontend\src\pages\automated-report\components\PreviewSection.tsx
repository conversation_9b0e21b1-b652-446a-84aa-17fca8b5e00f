import React, { useRef, useEffect, useState } from 'react';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import PDFDownloader from './PDFDownloader';
import { ReportData } from '../types/report.types';
import { PDFViewer } from '@react-pdf/renderer';
import ReportTemplatePDF from './ReportTemplatePDF';
import FullReportTemplatePDF from './FullReportTemplatePDF';
import { parseHtmlToElements } from './pdfHtmlImageUtils';
import ChartImageGenerator from './ChartImageGenerator';

interface PreviewSectionProps {
  children: React.ReactNode;
  previewLoading?: boolean;
  onFullView?: () => void;
  reportData?: ReportData;
  initialPreviewMode?: 'full' | 'technical';
  onPreviewModeChange?: (mode: 'full' | 'technical') => void;
  previewHtml?: string; // HTML content for HTML mode
}

const PreviewSection: React.FC<PreviewSectionProps> = ({ children, previewLoading = false, onFullView, reportData, initialPreviewMode, onPreviewModeChange, previewHtml }) => {
  const previewRef = useRef<HTMLDivElement>(null);
  const [isContentReady, setIsContentReady] = useState(false);
  const [zoom, setZoom] = useState(0.7); // 0.7 = 70%
  const [previewMode, setPreviewMode] = useState<'full' | 'technical'>(initialPreviewMode || 'technical'); // New state for preview mode
  const [pdfKey, setPdfKey] = useState(0); // Force PDF re-render
  const [pdfProcessing, setPdfProcessing] = useState(false); // Track PDF processing state
  const [pendingDownload, setPendingDownload] = useState(false); // Track if user requested download while processing
  const pdfDownloaderRef = useRef<any>(null); // Ref to PDFDownloader
  const [processedFindings, setProcessedFindings] = useState<Record<string, any[]> | null>(null);
  const [pieChartImage, setPieChartImage] = useState<string | undefined>();
  const [barChartImage, setBarChartImage] = useState<string | undefined>();

  const handleZoomIn = () => setZoom(z => Math.min(z + 0.1, 2));
  const handleZoomOut = () => setZoom(z => Math.max(z - 0.1, 0.5));

  // Handle preview mode changes
  const handlePreviewModeChange = (mode: 'full' | 'technical') => {
    console.log('PreviewSection - Switching to mode:', mode);
    console.log('PreviewSection - reportData available:', !!reportData);
    setPreviewMode(mode);
    onPreviewModeChange?.(mode);
    if (mode === 'full') {
      setPdfProcessing(true);
    } else {
      setPdfProcessing(false);
    }
  };

  // Force PDF refresh
  const handlePdfRefresh = () => {
    setPdfKey(prev => prev + 1);
  };

  // Move findings processing logic here
  useEffect(() => {
    let cancelled = false;
    if (!reportData) {
      setProcessedFindings(null);
      setPdfProcessing(false);
      return;
    }
    // If switching to technical summary, no need to process again
    if (previewMode === 'technical') {
      setPdfProcessing(false);
      return;
    }
    const processAllFindings = async () => {
      setPdfProcessing(true);
      const result: Record<string, any[]> = {};
      for (const severity of ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']) {
        const findings = reportData.detailed_findings?.filter(f => (f.severity_category || '').toUpperCase() === severity) || [];
        result[severity] = [];
        for (const finding of findings) {
          const processed: Record<string, any> = {};
          for (const field of ['description', 'instructions', 'impact', 'fix']) {
            if ((finding as any)[field]) {
              processed[field] = await parseHtmlToElements((finding as any)[field]);
            }
          }
          result[severity].push({ ...finding, ...processed });
        }
      }
      if (!cancelled) {
        setProcessedFindings(result);
        setPdfProcessing(false);
      }
    };
    processAllFindings();
    return () => { cancelled = true; };
  }, [reportData, previewMode]);

  // Handle download button click
  const handleDownload = () => {
    if (pdfProcessing) {
      setPendingDownload(true);
    } else {
      if (pdfDownloaderRef.current && pdfDownloaderRef.current.downloadPDF) {
        pdfDownloaderRef.current.downloadPDF();
      }
    }
  };

  // When processing finishes and download is pending, trigger download
  useEffect(() => {
    if (!pdfProcessing && pendingDownload) {
      setPendingDownload(false);
      if (pdfDownloaderRef.current && pdfDownloaderRef.current.downloadPDF) {
        pdfDownloaderRef.current.downloadPDF();
      }
    }
  }, [pdfProcessing, pendingDownload]);

  // Wait for content to be fully rendered
  useEffect(() => {
    // For PDF mode, set content as ready immediately
    setIsContentReady(true);
  }, [children, previewLoading, previewMode, previewHtml]);

  const pieData = {
    labels: ['Critical', 'High', 'Medium', 'Low'],
    datasets: [{
      data: [
        reportData?.open_close_counts_by_severity?.Critical?.Total || 0,
        reportData?.open_close_counts_by_severity?.High?.Total || 0,
        reportData?.open_close_counts_by_severity?.Medium?.Total || 0,
        reportData?.open_close_counts_by_severity?.Low?.Total || 0,
      ],
      backgroundColor: ['#8b0000', '#ff4500', '#ffd700', '#32cd32'],
    }],
  };

  const barData = {
    labels: ['Critical', 'High', 'Medium', 'Low'],
    datasets: [{
      label: 'Number of Findings',
      data: [
        reportData?.open_close_counts_by_severity?.Critical?.Total || 0,
        reportData?.open_close_counts_by_severity?.High?.Total || 0,
        reportData?.open_close_counts_by_severity?.Medium?.Total || 0,
        reportData?.open_close_counts_by_severity?.Low?.Total || 0,
      ],
      backgroundColor: ['#8b0000', '#ff4500', '#ffd700', '#32cd32'],
    }],
  };

  return (
    <div className="relative h-full bg-gradient-to-br from-blue-50/80 to-blue-100/60 flex flex-col">
      {/* Glassy Sticky Header */}
      <div className="sticky top-0 z-20 w-full bg-blue-700/80 backdrop-blur-md shadow-lg px-6 py-2 flex items-center justify-between rounded-b-xl border-b border-blue-200/30">
        <div className="flex items-center gap-2">
          <div className="p-1.5 bg-white/20 rounded-lg">
            <svg className="w-5 h-5 text-white/90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          </div>
          <h2 className="text-base font-bold text-white tracking-tight drop-shadow">Preview</h2>
          
          {/* Report Type Switch */}
          <div className="flex items-center gap-2 ml-4">
            <div className="flex bg-white/20 rounded-lg p-1">
              <button
                onClick={() => handlePreviewModeChange('technical')}
                className={`px-3 py-1 rounded text-xs font-semibold transition-all duration-200 ${
                  previewMode === 'technical'
                    ? 'bg-white text-blue-700 shadow-sm'
                    : 'text-white/80 hover:text-white hover:bg-white/10'
                }`}
              >
                Technical Summary
              </button>
              <button
                onClick={() => handlePreviewModeChange('full')}
                className={`px-3 py-1 rounded text-xs font-semibold transition-all duration-200 ${
                  previewMode === 'full'
                    ? 'bg-white text-blue-700 shadow-sm'
                    : 'text-white/80 hover:text-white hover:bg-white/10'
                }`}
              >
                Full Report
              </button>
            </div>
           
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={handlePdfRefresh}
            title="Refresh PDF Preview"
            className="flex items-center justify-center p-2 rounded-lg bg-white/20 hover:bg-white/30 transition-colors shadow-sm"
          >
            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
          {reportData && (
            <>
              <PDFDownloader 
                ref={pdfDownloaderRef}
                reportData={reportData} 
                processedFindings={processedFindings}
                fileName={`${reportData.company_name}-${previewMode === 'full' ? 'full' : 'technical'}-security-report.pdf`}
                compact={true}
                onClick={handleDownload}
                disabled={pdfProcessing && !pendingDownload}
              />
              {pendingDownload && (
                <span className="ml-2 text-xs text-blue-700 animate-pulse">Preparing PDF...</span>
              )}
            </>
          )}
        </div>
      </div>
      {/* Preview Area */}
      <div className="flex-1 overflow-auto p-4 md:p-8 bg-gradient-to-br from-blue-50/80 to-blue-100/60">
        <div className="flex flex-col items-center w-full min-h-[80vh]">
          {previewLoading ? (
            <div className="absolute inset-0 flex items-center justify-center bg-white/60 backdrop-blur rounded-xl z-10">
              <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-600"></div>
            </div>
          ) : (
            <div className="w-full h-full">
              {reportData ? (
                <>
                  {!pieChartImage && (
                    <ChartImageGenerator
                      type="pie"
                      data={pieData}
                      onImageReady={setPieChartImage}
                    />
                  )}
                  {!barChartImage && (
                    <ChartImageGenerator
                      type="bar"
                      data={barData}
                      options={{
                        scales: { y: { beginAtZero: true, ticks: { precision: 0 } }, },
                        plugins: { legend: { display: false } },
                      }}
                      onImageReady={setBarChartImage}
                    />
                  )}
                  {pieChartImage && barChartImage && (
                    <PDFViewer 
                      key={pdfKey}
                      style={{ 
                        width: '100%', 
                        height: '100%',
                        border: 'none',
                        minHeight: '600px'
                      }}
                    >
                      {previewMode === 'full' ? (
                        <FullReportTemplatePDF 
                          reportData={reportData} 
                          processedFindings={processedFindings} 
                          processing={pdfProcessing}
                          pieChartImage={pieChartImage}
                          barChartImage={barChartImage}
                        />
                      ) : (
                        <ReportTemplatePDF 
                          reportData={reportData} 
                          processedFindings={processedFindings} 
                          processing={pdfProcessing}
                          pieChartImage={pieChartImage}
                          barChartImage={barChartImage}
                        />
                      )}
                    </PDFViewer>
                  )}
                </>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="text-gray-500 mb-4">
                      <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-700 mb-2">No Report Data</h3>
                    <p className="text-gray-500">Please ensure report data is loaded to view PDF preview.</p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PreviewSection; 