import React, { useState } from "react";
import PDFViewer from "./PDFViewer";

interface BusinessOwner {
  name: string;
  email: string;
  company?: string;
}

interface ReportCardProps {
  companyName: string;
  title: string;
  description: string;
  date: string;
  versionNumber: string;
  status: string;
  pdfUrl?: string;
  isAdmin?: boolean;
  businessOwner?: BusinessOwner;
}

export default function ReportCard({
  companyName,
  title,
  description,
  date,
  versionNumber,
  status = "pending",
  pdfUrl,
  isAdmin = false,
  businessOwner
}: ReportCardProps) {
  const [isPdfPreviewOpen, setIsPdfPreviewOpen] = useState(false);

  const handleOpenPdfPreview = () => {
    if (pdfUrl) {
      setIsPdfPreviewOpen(true);
    }
  };

  return (
    <>
      <div
        className="group relative w-full max-w-md rounded-xl border border-gray-200 bg-white shadow-sm transition-shadow duration-200 focus-within:ring-2 focus-within:ring-blue-500 hover:shadow-lg"
        role="region"
        aria-label={`Report card for ${title}`}
      >
        <div className="p-6">
          {/* Header */}
          <div className="mb-4 flex items-start justify-between">
            {/* <h3
              className="text-lg font-semibold text-gray-900 transition-colors duration-150 group-hover:text-blue-600"
              title={companyName}
            >
              {companyName.length > 25
                ? `${companyName.slice(0, 22)}...`
                : companyName}
            </h3> */}
            <span
              className={`inline-flex items-center rounded-full px-3 py-1 text-xs font-semibold ${
                status === "pending"
                  ? "bg-gray-200 text-gray-700"
                  : "bg-green-100 text-green-700"
              }`}
            >
              {versionNumber}
            </span>
          </div>

          {/* Title and Description */}
          <div className="mb-5">
            {pdfUrl ? (
              <a
                href={pdfUrl}
                className="block text-base font-medium leading-tight text-blue-600 transition-colors duration-150 hover:text-blue-700"
                target="_blank"
                rel="noopener noreferrer"
                aria-label={`Open ${title} PDF`}
              >
                {title}
              </a>
            ) : (
              <div className="flex flex-wrap items-center gap-2">
                <span
                  className="text-base font-medium leading-tight text-gray-700"
                  title={title}
                >
                  {title}
                </span>
                <span className="inline-flex items-center rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-600">
                  Processing
                </span>
              </div>
            )}
            <p
              className="mt-2 line-clamp-3 text-sm text-gray-500"
              title={description}
            >
              {description}
            </p>
          </div>

          {/* Business Owner Info (Admin Only) */}
          {isAdmin && businessOwner && (
            <div className="mb-5 rounded-lg bg-gray-50 px-4 py-3">
              <p className="text-xs font-semibold uppercase tracking-wide text-gray-600">
                Owner
              </p>
              <p className="text-sm font-medium text-gray-800">
                {businessOwner.name}
              </p>
              <p className="text-xs text-gray-500">{businessOwner.email}</p>
            </div>
          )}

          {/* Footer */}
          <div className="flex items-center justify-between text-sm text-gray-600">
            <p className="font-medium">
              {new Date(date).toLocaleDateString("en-US", {
                month: "short",
                day: "numeric",
                year: "numeric"
              })}
            </p>
            <div className="flex items-center gap-2">
              {pdfUrl && (
                <>
                  <button
                    onClick={handleOpenPdfPreview}
                    className="inline-flex items-center rounded-md bg-blue-50 px-4 py-1.5 text-sm font-medium text-blue-600 transition-colors duration-150 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    aria-label="Preview PDF report"
                  >
                    Preview
                  </button>
                  <a
                    href={pdfUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center rounded-md bg-green-50 px-4 py-1.5 text-sm font-medium text-green-600 transition-colors duration-150 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-500"
                    aria-label="Download PDF report"
                  >
                    Download
                  </a>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {pdfUrl && (
        <PDFViewer
          pdfUrl={pdfUrl}
          isOpen={isPdfPreviewOpen}
          onClose={() => setIsPdfPreviewOpen(false)}
        />
      )}
    </>
  );
}
