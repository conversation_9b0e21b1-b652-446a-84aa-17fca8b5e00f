import { useState, useEffect, useCallback, useRef } from "react";
import { getUnifiedBusinessDashboard } from "../../api/endpoints/dashboard/dashboardApi";
import { BusinessReviewIssue, BusinessReviewMeta } from "./useBusinessReviewIssues";

// Define interfaces for all the dashboard data
export interface DashboardMetrics {
  totalReports: {
    current: number;
    changePercent: number;
  };
  activeIssues: {
    current: number;
    changePercent: number;
  };
  resolvedIssues: {
    current: number;
    changePercent: number;
  };
  retestsRemaining: number;
}

export interface SeverityCount {
  total: number;
  businessResolved: number;
}

export interface SeverityOverview {
  critical: SeverityCount;
  high: SeverityCount;
  medium: SeverityCount;
  low: SeverityCount;
}

export interface ProgramSeverity {
  program_id: number;
  name: string;
  description?: string;
  severityOverview?: SeverityOverview;
}

export interface TimeSeriesPoint {
  critical: number;
  high: number;
  medium: number;
  low: number;
  [key: string]: any; // For additional properties like date, month, etc.
}

export interface ReportsTrend {
  weekly: TimeSeriesPoint[];
  monthly: TimeSeriesPoint[];
  quarterly: TimeSeriesPoint[];
  yearly: TimeSeriesPoint[];
}

export interface StatusCounts {
  pending: number;
  inProgress: number;
  completed: number;
}

export interface PassRate {
  percentage: number;
  completed: number;
  changePercent: number;
}

export interface ResultsSummary {
  passed: number;
  failed: number;
  actionRequired: number;
}

export interface AverageDuration {
  days: number;
  changePercent: number;
}

export interface RetestSummary {
  statusCounts: StatusCounts;
  passRate: PassRate;
  resultsSummary: ResultsSummary;
  latestCompletion: string | null;
  averageDuration: AverageDuration;
}

export interface RetestAction {
  retest_id: number;
  report_id: number;
  title: string;
  severity_category: string;
  scope: string | null;
  status: string;
  submitted_date: string | null;
  updated_date: string;
  days_taken: number | null;
}

export interface UnifiedDashboardData {
  metrics: DashboardMetrics;
  severityOverview: {
    total: SeverityOverview;
    programs: ProgramSeverity[];
  };
  reportsTrend: ReportsTrend;
  businessReviewReports: BusinessReviewIssue[];
  businessReviewMeta?: BusinessReviewMeta;
  retestSummary: RetestSummary;
  retestActions: RetestAction[];
}

/**
 * Hook to fetch unified business dashboard data
 * @returns All dashboard data with loading and error states
 */
export default function useUnifiedBusinessDashboard() {
  const [data, setData] = useState<UnifiedDashboardData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const isFetchingRef = useRef(false);
  const mountedRef = useRef(true);

  const fetchData = useCallback(async () => {
    // Prevent duplicate API calls
    if (isFetchingRef.current) return;
    try {
      isFetchingRef.current = true;
      setLoading(true);
      setError(null);
      
      const response = await getUnifiedBusinessDashboard();
      
      // Prevent updates if component unmounted
      if (!mountedRef.current) return;
      
      // Validate response has the expected shape
      if (
        response &&
        typeof response === 'object' &&
        'success' in response &&
        response.success === true &&
        'data' in response
      ) {
        const dashboardData = response.data;
        
        if (
          dashboardData &&
          typeof dashboardData === 'object' &&
          'metrics' in dashboardData &&
          'severityOverview' in dashboardData &&
          'reportsTrend' in dashboardData &&
          'businessReviewReports' in dashboardData &&
          'retestSummary' in dashboardData &&
          'retestActions' in dashboardData
        ) {
          setData(dashboardData);
        } else {
          console.error("Invalid dashboard data structure:", dashboardData);
          setError("The dashboard data format is invalid");
        }
      } else {
        console.error("Unexpected dashboard response format:", response);
        setError("The dashboard API response format is invalid");
      }
    } catch (err) {
      // Prevent updates if component unmounted
      if (!mountedRef.current) return;
      
      console.error("Error fetching unified dashboard data:", err);
      setError("Failed to load dashboard data");
    } finally {
      // Prevent updates if component unmounted
      if (mountedRef.current) {
        setLoading(false);
      }
      isFetchingRef.current = false;
    }
  }, []);

  useEffect(() => {
    // Set up mounted ref
    mountedRef.current = true;
    
    // Initial fetch
    fetchData();
    
    // Cleanup function to prevent state updates after unmount
    return () => {
      mountedRef.current = false;
    };
  }, [fetchData]);

  return { 
    data, 
    loading, 
    error, 
    refetch: fetchData,
    
    // Convenience getters
    metrics: data?.metrics,
    severityOverview: data?.severityOverview,
    reportsTrend: data?.reportsTrend,
    businessReviewReports: data?.businessReviewReports,
    retestSummary: data?.retestSummary,
    retestActions: data?.retestActions,
    
    // Meta information
    businessReviewMeta: data?.businessReviewMeta || 
      // Fallback to creating meta info from available data
      (data ? { 
        // Use metrics.totalReports.current as the source of truth for total report count if available
        totalCount: data?.metrics?.totalReports?.current || 
                   (data?.businessReviewReports ? data.businessReviewReports.length : 0),
        // If we have both metrics and reports list, we can determine if there are more reports
        hasMore: data?.metrics?.totalReports?.current && data?.businessReviewReports ? 
                data?.metrics?.totalReports?.current > data.businessReviewReports.length : 
                false
      } : null)
  };
} 