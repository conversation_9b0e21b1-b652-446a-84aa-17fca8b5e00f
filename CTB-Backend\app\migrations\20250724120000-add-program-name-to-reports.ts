"use strict";

module.exports = {
  async up(queryInterface, Sequelize) {
    // Add program_name column to automated_reports table
    await queryInterface.addColumn("automated_reports", "program_name", {
      type: Sequelize.STRING,
      allowNull: true,
      comment: "Name of the program for display on cover page"
    });

    // Add program_name column to program_reports table
    await queryInterface.addColumn("program_reports", "program_name", {
      type: Sequelize.STRING,
      allowNull: true,
      comment: "Name of the program for display on cover page"
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove program_name column from program_reports table
    await queryInterface.removeColumn("program_reports", "program_name");

    // Remove program_name column from automated_reports table
    await queryInterface.removeColumn("automated_reports", "program_name");
  }
};
