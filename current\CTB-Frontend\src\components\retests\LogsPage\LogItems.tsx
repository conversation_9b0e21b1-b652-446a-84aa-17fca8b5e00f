import React, { useCallback } from "react";
import { CTBRetestLog } from "../../../utils/api/endpoints/retests/parseRetests";
import StatusFormatter from "../utils/StatusFormatter";

interface LogItemProps {
  log: CTBRetestLog;
}

export const LogItem: React.FC<LogItemProps> = ({ log }) => {
  const getRoleColor = useCallback((role: string) => {
    switch (role) {
      case "Business":
        return "bg-green-800 text-white border-green-300";
      case "Business Admin":
        return "bg-green-700 text-white border-green-300";
      case "Business Manager":
        return "bg-green-600 text-white border-green-300";
      case "Developer":
        return "bg-green-500 text-white border-green-300";
      case "Researcher":
        return "bg-yellow-500 text-white border-yellow-300";
      case "QA":
        return "bg-blue-700 text-white border-blue-300";
      case "Admin Manager":
        return "bg-blue-600 text-white border-purple-300";
      case "Sub Admin":
        return "bg-blue-500 text-white border-red-300";
      default:
        return "bg-gray-100 text-gray-800 border-gray-300";
    }
  }, []);

  const getInitials = useCallback((name: string) => {
    return name
      .split(" ")
      .map(n => n[0])
      .join("")
      .toUpperCase();
  }, []);

  // const getStatusColor = useCallback((status: string) => {
  //   switch (status) {
  //     case "QA Review In Process":
  //     case "Retest In Process":
  //     case "Fix in Progress":
  //     case "Reopen Retest":
  //     case "Request Further Action":
  //     case "Need Information":
  //       return { backgroundColor: "#1348FC", color: "white" };
  //     case "Fix Approved":
  //     case "Fix Verified":
  //     case "Risk Accepted and Closed":
  //       return { backgroundColor: "#04AB44CC", color: "white" };
  //     case "Fix Rejected":
  //     case "Fix Failed":
  //     case "Findings Rejected":
  //     case "Close Retest":
  //       return { backgroundColor: "#F50101CC", color: "white" };
  //     default:
  //       return { backgroundColor: "#1348FC", color: "white" };
  //   }
  // }, []);

  const formatRelativeTime = (timestamp: string): string => {
    const date = new Date(timestamp);

    // Custom formatting for "Sept 4, 2024 10:35am UTC" format
    const options: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
      hour12: true,
      timeZone: "UTC",
      timeZoneName: "short"
    };

    return new Intl.DateTimeFormat("en-US", options).format(date);
  };

  return (
    <div className="log-item relative overflow-hidden px-6 py-2 transition-all duration-300">
      <div className="flex items-start space-x-4">
        <div className="pfp relative flex h-12 w-12 items-center justify-center rounded-full bg-gray-100 text-lg font-semibold shadow-inner">
          {log.pfp ? (
            <img
              src={log.pfp}
              alt="Profile"
              className="h-full w-full rounded-full"
            />
          ) : (
            <div
              className={`flex h-12 w-12 items-center justify-center rounded-full ${getRoleColor(
                log.role
              )} text-lg font-semibold shadow-inner`}
            >
              {getInitials(log.username)}
            </div>
          )}
        </div>

        <div className="min-w-0 flex-1">
          <div className="mb-2 flex flex-wrap items-center justify-between">
            <div className="flex flex-wrap items-center space-x-2">
              <h4 className="text-lg font-semibold capitalize text-gray-900">
                {log.username}
              </h4>

              <span
                className={`rounded-full px-2 py-1 text-xs ${getRoleColor(
                  log.role
                )} border`}
              >
                {log.role === "Researcher"
                  ? "Pentester"
                  : log.role === "Developer"
                  ? "Security Engineer"
                  : log.role === "Business Admin"
                  ? "Organization Admin"
                  : log.role === "Business Manager"
                  ? "Program Manager"
                  : log.role}
              </span>

              {log.new_status !== "chat" && (
                <>
                  <span
                    className="font-light text-gray-700"
                    style={{ fontWeight: "500" }}
                  >
                    updated status
                  </span>
                  <span className="inline-block rounded-full text-[16px] font-bold">
                    <StatusFormatter status={log.new_status} />
                  </span>
                </>
              )}
            </div>
            <span className="mt-2 text-sm text-gray-500 md:mt-0">
              {formatRelativeTime(log.createdAt)}
            </span>
          </div>

          <div
            className="break-words text-sm leading-relaxed text-gray-700 sm:text-base"
            dangerouslySetInnerHTML={{ __html: log.comments }}
            style={{ fontWeight: "500" }}
          />

          {log.poc && (
            <a
              href={log.poc}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500 hover:underline"
            >
              View the attachment
            </a>
          )}
        </div>
      </div>
    </div>
  );
};
