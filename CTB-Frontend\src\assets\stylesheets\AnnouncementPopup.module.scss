.overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(2px);
    z-index: 9998;
    animation: fadeIn 0.3s ease-out;
  }
  
  .announcementPopup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15), 0 8px 32px rgba(0, 0, 0, 0.08);
    width: 90%;
    max-width: 500px;
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    border: 1px solid rgba(0, 0, 0, 0.06);
    
    &.animate {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px 24px 0 24px;
    position: relative;
  }
  
  .badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: linear-gradient(135deg, #007bfc 0%, #00238c 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    box-shadow: 0 4px 12px rgba(0, 123, 252, 0.18);
  }
  
  .badgeIcon {
    font-size: 12px;
    animation: sparkle 2s infinite;
  }
  
  .badgeText {
    line-height: 1;
  }
  
  .closeBtn {
    background: none;
    border: none;
    font-size: 24px;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    
    &:hover {
      background: #f3f4f6;
      color: #374151;
      transform: scale(1.1);
    }
  }
  
  .content {
    padding: 24px 24px 20px 24px;
    text-align: center;
  }
  
  .iconContainer {
    margin-bottom: 16px;
  }
  
  .mainIcon {
    display: inline-block;
    font-size: 48px;
    animation: bounce 2s infinite;
  }
  
  .title {
    margin: 0 0 12px 0;
    font-size: 24px;
    font-weight: 700;
    color: #00238c;
    line-height: 1.3;
    letter-spacing: -0.5px;
  }
  
  .subtitle {
    margin: 0 0 24px 0;
    font-size: 16px;
    color: #374151;
    line-height: 1.5;
    
    strong {
      color: #007bfc;
      font-weight: 600;
    }
  }
  
  .features {
    display: grid;
    gap: 12px;
    margin-bottom: 8px;
  }
  
  .feature {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    color: #00238c;
    font-weight: 500;
  }
  
  .featureIcon {
    font-size: 16px;
    width: 20px;
    display: flex;
    justify-content: center;
  }
  
  .actions {
    display: flex;
    gap: 12px;
    padding: 0 24px 20px 24px;
  }
  
  .primaryBtn {
    flex: 1;
    background: linear-gradient(135deg, #007bfc 0%, #00238c 100%);
    color: white;
    border: none;
    padding: 14px 24px;
    border-radius: 10px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 4px 12px rgba(0, 123, 252, 0.18);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 35, 140, 0.22);
      background: linear-gradient(135deg, #005bb5 0%, #00238c 100%);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  .btnIcon {
    transition: transform 0.2s ease;
    font-size: 16px;
  }
  
  .primaryBtn:hover .btnIcon {
    transform: translateX(2px);
  }
  
  .secondaryBtn {
    flex: 1;
    background: transparent;
    color: #007bfc;
    border: 2px solid #e5e7eb;
    padding: 12px 24px;
    border-radius: 10px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background: #f3f8fe;
      border-color: #b9d6fa;
      color: #00238c;
      transform: translateY(-1px);
    }
  }
  
  .footer {
    padding: 0 24px 24px 24px;
    text-align: center;
    border-top: 1px solid #f3f4f6;
    margin-top: 8px;
    padding-top: 16px;
  }
  
  .footerText {
    font-size: 12px;
    color: #007bfc;
    font-weight: 500;
  }
  
  // Animations
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-8px);
    }
    60% {
      transform: translateY(-4px);
    }
  }
  
  @keyframes sparkle {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.1);
    }
  }
  
  // Mobile responsiveness
  @media (max-width: 640px) {
    .announcementPopup {
      width: 95%;
      margin: 20px;
    }
    
    .title {
      font-size: 22px;
    }
    
    .subtitle {
      font-size: 15px;
    }
    
    .actions {
      flex-direction: column;
    }
    
    .primaryBtn,
    .secondaryBtn {
      width: 100%;
    }
  } 