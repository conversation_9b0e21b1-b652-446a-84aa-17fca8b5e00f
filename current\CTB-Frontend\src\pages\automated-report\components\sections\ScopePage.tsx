import React from 'react';
import { Page, View, Text, StyleSheet } from '@react-pdf/renderer';
import { ReportData } from '../../types/report.types';
import { useSectionPages } from '../SectionPageContext';

interface ScopePageProps {
  reportData: ReportData;
  sectionId?: string;
}

const styles = StyleSheet.create({
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    fontSize: 10,
    color: 'grey',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  footerLeft: {
    textAlign: 'left',
    flex: 1,
  },
  footerRight: {
    textAlign: 'right',
    flex: 1,
  },
});

// Utility to strip HTML tags and preserve paragraph breaks
function htmlToParagraphs(html: string): string[] {
  if (!html) return [];
  // Replace <br> and <br/> with \n
  let withBreaks = html.replace(/<br\s*\/?>(?![^<]*<)/gi, '\n');
  // Replace <p> and <div> with \n (block-level)
  withBreaks = withBreaks.replace(/<\/?(p|div)[^>]*>/gi, '\n');
  // Remove all other tags
  const tmp = document.createElement('div');
  tmp.innerHTML = withBreaks;
  const text = tmp.textContent || tmp.innerText || '';
  // Split by double newlines or single newlines, filter empty
  return text.split(/\n+/).map(s => s.trim()).filter(Boolean);
}

const ScopePage: React.FC<ScopePageProps> = ({ reportData, sectionId }) => {
  const { updateSectionPage } = useSectionPages();
  return (
    <Page size="A4" id={sectionId} style={{
      flexDirection: 'column',
      backgroundColor: '#ffffff',
      padding: '20mm 15mm',
      fontFamily: 'Helvetica',
      fontSize: 12,
    }}>
      <View style={{ paddingHorizontal: 14, paddingTop: 1, flexDirection: 'column', flex: 1 }}>
        <View style={{ flex: 1 }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#2563eb', marginBottom: 12, lineHeight: 1.4 }}>
            SCOPE
          </Text>
          {reportData.scope ? (
            htmlToParagraphs(reportData.scope).map((para, idx) => (
              <Text key={idx} style={{ fontSize: 12, lineHeight: 1.4, marginBottom: 14, color: '#374151', textAlign: 'justify' }}>{para}</Text>
            ))
          ) : (
            <Text style={{ fontSize: 12, lineHeight: 1.4, marginBottom: 18, color: '#374151', textAlign: 'justify' }}>
              The scope of the assessment was limited to performing Vulnerability Assessment and Penetration Testing on the {reportData.company_name} systems mentioned below:
            </Text>
          )}
          <View style={{
            borderRadius: 10,
            borderWidth: 1,
            borderColor: '#d1d5db',
            marginBottom: 16,
            width: '100%',
            alignSelf: 'center',
            backgroundColor: '#fff',
            overflow: 'hidden',
          }}>
            <View style={{ flexDirection: 'column', width: '100%' }}>
              {/* Table Header */}
              <View style={{ flexDirection: 'row', backgroundColor: '#2563eb', borderTopLeftRadius: 10, borderTopRightRadius: 10 }}>
                <Text style={{ padding: 14, textAlign: 'center', fontWeight: 'bold', width: '20%', color: '#fff', fontSize: 12, lineHeight: 1.4 }}>SL No.</Text>
                <Text style={{ padding: 14, textAlign: 'left', fontWeight: 'bold', width: '30%', color: '#fff', fontSize: 12, lineHeight: 1.4 }}>Asset Type</Text>
                <Text style={{ padding: 14, textAlign: 'left', fontWeight: 'bold', width: '50%', color: '#fff', fontSize: 12, lineHeight: 1.4 }}>Asset</Text>
              </View>
              {/* Table Rows */}
              {reportData.target_details && reportData.target_details.map((target, idx) => (
                <View key={idx} style={{
                  flexDirection: 'row',
                  backgroundColor: idx % 2 === 0 ? '#f1f5f9' : '#fff',
                  borderTopWidth: idx === 0 ? 0 : 1,
                  borderTopColor: '#e5e7eb',
                }}>
                  <Text style={{ padding: 12, width: '20%', textAlign: 'center', fontSize: 12, color: '#2563eb', fontWeight: 'bold', lineHeight: 1.4 }}>{String(idx + 1).padStart(2, '0')}</Text>
                  <Text style={{ padding: 12, width: '30%', fontWeight: '600', fontSize: 12, lineHeight: 1.4 }}>{target.type}</Text>
                  <Text style={{ padding: 12, width: '50%', fontSize: 12, lineHeight: 1.4 }}>{target.url}</Text>
                </View>
              ))}
            </View>
          </View>
          <Text style={{ fontSize: 12, color: '#64748b', textAlign: 'center', marginTop: 8, lineHeight: 1.4 }}>Table 1: Scope of Work</Text>
        </View>
      </View>
      <View style={styles.footer} fixed>
        <Text style={styles.footerLeft}>{reportData.document_number || 'Document Number'}</Text>
        <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
      </View>
      <Text style={{ display: 'none' }} render={({ pageNumber }) => { updateSectionPage('Scope', pageNumber); return ''; }} fixed />
    </Page>
  );
};

export default ScopePage; 