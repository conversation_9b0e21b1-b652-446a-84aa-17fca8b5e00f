import React, { useState, useMemo } from "react";
import { Bar } from "react-chartjs-2";
import { ParsedBusinessDashboard } from "../../../utils/api/endpoints/dashboard/businessDashboardParser";

type TimeInterval = "week" | "month" | "year";

type ReportTimeSeriesLineChartProps = {
  details: ParsedBusinessDashboard;
};

type AccumulatedData = {
  label: string;
  value: number;
};

const ReportTimeSeriesLineChart: React.FC<ReportTimeSeriesLineChartProps> = ({
  details
}) => {
  const [selectedInterval, setSelectedInterval] =
    useState<TimeInterval>("week");

  const { timeSeriesReports } = details;

  const { data, labels, availableIntervals } = useMemo(() => {
    const sortedReports = [...timeSeriesReports].sort(
      (a, b) =>
        new Date(a.date || 0).getTime() - new Date(b.date || 0).getTime()
    );

    // Helper function to accumulate reports based on interval
    const accumulateReports = (interval: TimeInterval): AccumulatedData[] => {
      const result: AccumulatedData[] = [];
      const groupedData: Record<string, number> = {};
      const baseDate = new Date(sortedReports[0]?.date || Date.now());

      sortedReports.forEach(report => {
        const date = new Date(report.date || Date.now());
        let key: string;

        switch (interval) {
          case "week":
            const weekNumber = Math.floor((date.getTime() - baseDate.getTime()) / (7 * 24 * 60 * 60 * 1000));
            key = `Week ${weekNumber + 1}`;
            break;
          case "month":
            key = `${date.getFullYear()}-${date.getMonth() + 1}`; // "YYYY-MM"
            break;
          case "year":
            key = `${date.getFullYear()}`; // "YYYY"
            break;
          default:
            key = '';
        }

        if (!groupedData[key]) {
          groupedData[key] = 0;
        }
        groupedData[key] += report.count;
      });

      // Convert grouped data to arrays for chart
      for (const [key, value] of Object.entries(groupedData)) {
        result.push({ label: key, value });
      }

      return result;
    };

    const accumulatedReports = accumulateReports(selectedInterval);
    const processedData = accumulatedReports.map(item => item.value);
    const processedLabels = accumulatedReports.map(item => item.label);

    const intervals: TimeInterval[] = ["week", "month", "year"];

    return {
      data: processedData,
      labels: processedLabels,
      availableIntervals: intervals
    };
  }, [timeSeriesReports, selectedInterval]);

  const chartData = {
    labels,
    datasets: [
      {
        label: "Number of Reports",
        data,
        backgroundColor: "rgba(59, 130, 246, 0.6)",
        borderColor: "rgb(59, 130, 246)",
        borderWidth: 1,
        hoverBackgroundColor: "rgba(59, 130, 246, 0.8)",
        hoverBorderColor: "rgb(59, 130, 246)"
      }
    ]
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        padding: 12,
        titleColor: "white",
        bodyColor: "white",
        borderColor: "rgba(255, 255, 255, 0.1)",
        borderWidth: 1
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 12
          },
          color: "#6B7280"
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: "rgba(0, 0, 0, 0.05)"
        },
        ticks: {
          font: {
            size: 12
          },
          color: "#6B7280",
          padding: 8,
          stepSize: 1, 
          callback: (value: any) => Number(value).toFixed(0)
        }
      }
    }
  };

  return (
    <div className="rounded-xl border border-gray-100 bg-white pb-2 shadow-sm">
      <div className="mb-6 flex items-center justify-between">
        <h3 className="bg-[#0B45DB] px-[10px] py-[7px] text-lg font-semibold text-white">
          Report Overview
        </h3>
        <div className="relative">
          <select
            value={selectedInterval}
            onChange={(e) => setSelectedInterval(e.target.value as TimeInterval)}
            className="appearance-none bg-gray-50 border border-gray-200 px-4 py-[10px] pr-8 text-sm text-gray-600 font-medium hover:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20 transition-colors"
          >
            {availableIntervals.map(interval => (
              <option key={interval} value={interval}>
                By {interval.charAt(0).toUpperCase() + interval.slice(1)}
              </option>
            ))}
          </select>
          <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
            <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
      </div>

      <div className="h-[300px]">
        <Bar data={chartData} options={options} />
      </div>
    </div>
  );
};

export default ReportTimeSeriesLineChart;
