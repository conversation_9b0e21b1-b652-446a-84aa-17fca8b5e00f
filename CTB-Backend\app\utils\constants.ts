//User Roles
export const USER_ROLE_HACKER = 1;
export const USER_ROLE_BUSINESS = 2;
export const USER_ROLE_ADMIN = 3;

export enum ProgramTypes {
  VDP = "VDP",
  BUG_BOUNTY = "Bug Bounty",
  PTAAS = "PTAAS"
}

export enum SeverityTypes {
  CRITICAL = "CRITICAL",
  HIGH = "HIGH",
  MEDIUM = "MEDIUM",
  LOW = "LOW"
}

export const severityOrder = {
  [SeverityTypes.CRITICAL]: 1,
  [SeverityTypes.HIGH]: 2,
  [SeverityTypes.MEDIUM]: 3,
  [SeverityTypes.LOW]: 4
};

export const CTB_VDP_ID = 7;

export const SUPPORT_EMAIL_ADDRESS = "<EMAIL>"; //Support Email Address
export const LOGIN_PAGE_URL = process.env.FRONTEND_BASE_URL + "/login";
export const CTB_LOGO =
  "https://ctbtstbucket.blob.core.windows.net/pfps/ctb.png";
export const DEFAULT_PROFILE =
  "https://img.icons8.com/?size=100&id=2yC9SZKcXDdX&format=png&color=000000";

//report states
export const BUSINESS_APPROVED = "Business Approved";
export const BUSINESS_REVIEW = "Business Review";
export const BUSINESS_REJECTED = "Business Rejected";
export const AWAITING_FIX = "Awaiting Fix";
export const BUSINESS_REQUEST_INFO = "Request more info";
export const BUSINESS_ACCEPT_RISK = "Business Accepts Risk";
export const OUT_OF_SCOPE = "out of scope";
// export const QA_DISAGREES = "QA Disagrees";
export const QA_MODIFIED = "QA Modified";
export const TRIAGE_APPROVED = "Triage Approved";
export const TRIAGE_REJECTED = "Triage Rejected";
export const TRIAGE_SERVICE_NOT_OPTED = "Service Not Opted";
export const UNDER_REVIEW = "underReview";
export const RECEIVED = "received";
export const DELETED = "deleted";
export const CLOSED = "closed";
export const PAYMENT_INITIATED = "Payment Initiated";
export const TRIAGE_SERVICE_OPTED_TRUE = "1";
export const TRIAGE_SERVICE_OPTED_FALSE = "0";
export const TRIAGE_NOT_OPTED_FE_ROUTE = "triage-not-opted";
export const TRIAGE_STATUS_APPROVED = "approved";

//Payment Constants
export const PAYMENT_MODES = ["paypal", "bank account", "orbit remit"];

//cvss score to category
export const GET_CVCSS_CATEGORY = score => {
  score = parseFloat(score).toFixed(2);
  if (score > 0 && score < 4) {
    return "LOW";
  } else if (score >= 4 && score < 7) {
    return "MEDIUM";
  } else if (score >= 7 && score < 9) {
    return "HIGH";
  } else if (score >= 9 && score <= 10) {
    return "CRITICAL";
  }
};

export const complianceTypes = {
  SOC2: "SOC2",
  HIPA: "HIPA",
  ISO27001: "ISO27001",
  PCIDSS: "PCIDSS",
  Other: "Other"
};

export const AZURE_BLOB_STORAGE_REPORT_ATTACHMENTS_INLINE_NAME =
  "reportattachmentsinline";

//Create User - Duplicate field sql
export const SQL_DUPLICATE_ENTRY_ERROR = "ER_DUP_ENTRY";
const RESET_PASSWORD_PAGE_URL = "reset-password";

//emails
export const OTP_SUBJECT = "OTP | CaptureTheBug.xyz";
export const WELCOME_MESSAGE_SUBJECT = "Welcome to Capture The Bug!";
export const ACCOUNT_APPROVED_SUBJECT =
  "Your account has been approved! | Capture The Bug";
export const PASSWORD_RESET_SUBJECT = "Password Reset | Capture The Bug";
export const CERTIFICATE_GENERATED_SUBJECT = `Capture The Bug | Certificate of Appreciation`;
export const NEW_REPORT_NOTIFICATION = `Capture The Bug | New Report Notification for Your Program`;
export const BOUNTY_RESEARCHER_SUBJECT = (company_name: string) =>
  `Bounty earned: ${company_name} recognizes your contribution`;
export const BOUNTY_BUSINESS_SUBJECT = (report_id: number) =>
  `Bounty Submission Processed | Report Id : ${report_id.toString()}`;
export const NEW_COMMENT_SUBJECT = (report_id: number) => `CTB| New Comment`;

export const OTP_BODY = (otp: string, username: string) => {
  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en">
<head>
    <title>Email Verification</title>
    <!-- Meta Tags -->
    <meta charset="UTF-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="x-apple-disable-message-reformatting" content="" />
    <meta content="target-densitydpi=device-dpi" name="viewport" />
    <meta content="true" name="HandheldFriendly" />
    <meta content="width=device-width" name="viewport" />
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no, url=no" />

    <!-- Base Styles -->
    <style type="text/css">
        /* Reset Styles */
        table {
            border-collapse: separate;
            table-layout: fixed;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        table td {
            border-collapse: collapse;
        }
        
        /* External Class Styles */
        .ExternalClass {
            width: 100%;
        }
        .ExternalClass,
        .ExternalClass p,
        .ExternalClass span,
        .ExternalClass font,
        .ExternalClass td,
        .ExternalClass div {
            line-height: 100%;
        }

        /* Base Element Styles */
        body, a, li, p, h1, h2, h3 {
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
        }
        html {
            -webkit-text-size-adjust: none !important;
        }
        body, #innerTable {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Image Styles */
        #innerTable img+div {
            display: none !important;
        }
        img {
            margin: 0;
            padding: 0;
            -ms-interpolation-mode: bicubic;
        }

        /* Typography Styles */
        h1, h2, h3, p, a {
            line-height: inherit;
            overflow-wrap: normal;
            white-space: normal;
            word-break: break-word;
        }
        a {
            text-decoration: none;
        }
        h1, h2, h3, p {
            min-width: 100% !important;
            width: 100% !important;
            max-width: 100% !important;
            display: inline-block !important;
            border: 0;
            padding: 0;
            margin: 0;
        }

        /* OTP Box Styles */
        .otp-box {
            display: inline-block;
            background-color: #F5F5F5;
            width: 50px;
            height: 50px;
            margin: 0 5px;
            border-radius: 8px;
            text-align: center;
            vertical-align: middle;
        }
        .otp-number {
            font-family: Inter, BlinkMacSystemFont, Segoe UI, Helvetica Neue, Arial, sans-serif;
            font-size: 24px;
            font-weight: 700;
            color: #111111;
            line-height: 50px;
        }
    </style>

    <!-- Responsive Styles -->
    <style type="text/css">
        @media (min-width: 481px) {
            .hd { display: none !important; }
        }
        @media (max-width: 480px) {
            .hm { display: none !important; }
            .t67, .t72 { 
                mso-line-height-alt: 0px !important;
                line-height: 0 !important;
                display: none !important;
            }
            .t68 { padding-top: 43px !important; }
            .t70 { 
                border: 0 !important;
                border-radius: 0 !important;
            }
            .t65, .t9 { width: 320px !important; }
            .t63 { padding: 40px 30px !important; }
            .t59 { padding-bottom: 36px !important; }
            .t32, .t61 { width: 260px !important; }
            .t54 { text-align: center !important; }
            .t43, .t45, .t49, .t51 { display: revert !important; }
            .t47, .t53 {
                vertical-align: middle !important;
                width: 74px !important;
            }
            .t40 { width: 224px !important; }
        }
    </style>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@500;600;700&family=Lato:wght@400;700&family=Inter+Tight:wght@700&display=swap" rel="stylesheet" type="text/css" />
</head>

<body id="body" class="t75" style="min-width:100%; margin:0; padding:0; background-color:#F9F9F9;">
    <div class="t74" style="background-color:#F9F9F9;">
        <!-- Main Container -->
        <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0" align="center">
            <tr>
                <td class="t73" style="font-size:0; line-height:0; background-color:#F9F9F9;" valign="top" align="center">
                    <!-- Inner Container -->
                    <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0" align="center" id="innerTable">
                        <!-- Spacer -->
                        <tr>
                            <td>
                                <div class="t67" style="mso-line-height-rule:exactly; line-height:70px; font-size:1px;">&nbsp;</div>
                            </td>
                        </tr>
                        
                        <!-- Content Card -->
                        <tr>
                            <td align="center">
                                <table class="t71" role="presentation" cellpadding="0" cellspacing="0" style="margin:0 auto;">
                                    <tr>
                                        <td class="t70" style="background-color:#FFFFFF; border:1px solid #CECECE; width:400px; border-radius:20px;">
                                            <!-- Card Content -->
                                            <table class="t69" role="presentation" cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <td class="t68" style="padding:50px 40px 40px 40px;">
                                                        <!-- Logo -->
                                                        <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
                                                            <tr>
                                                                <td align="center">
                                                                    <img width="158" height="53" alt="Logo" src="https://c8270c56-7594-4100-9b34-7589297588fa.b-cdn.net/e/bac89f4c-a951-463b-81dd-86c0e12c3427/28695d5f-3f29-41fa-a53d-2f93062026e6.jpeg"/>
                                                                </td>
                                                            </tr>
                                                        </table>

                                                        <!-- Title -->
                                                        <div style="margin-top:80px; text-align:center; margin-bottom: 40px;;">
                                                            <h1 style="font-family:Inter,sans-serif; font-size:24px; font-weight:600; color:#111111;">
                                                                OTP Verification
                                                            </h1>
                                                        </div>

                                                        <!-- Description -->
                                                        <div style="margin-top:17px; text-align:center;">
                                                            <p style="font-family:Inter,sans-serif; font-size:15px; color:#424040; line-height:22px;">
                                                                Your One-Time Password (OTP) for secure login is:
                                                            </p>
                                                        </div>

                                                        <!-- OTP Section -->
                                                        <div style="margin:40px 0; text-align:center;">
                                                            <table role="presentation" cellpadding="0" cellspacing="0" style="margin:0 auto;">
                                                                <tr>
                                                                    <td class="otp-box"><span class="otp-number">${otp[0]}</span></td>
                                                                    <td class="otp-box"><span class="otp-number">${otp[1]}</span></td>
                                                                    <td class="otp-box"><span class="otp-number">${otp[2]}</span></td>
                                                                    <td class="otp-box"><span class="otp-number">${otp[3]}</span></td>
                                                                </tr>
                                                            </table>
                                                        </div>

                                                        <!-- Footer Text -->
                                                        <div style="margin-top:40px; margin-bottom: 80px; text-align:center;">
                                                            <p style="font-family:Inter,sans-serif; font-size:14px; color:#424040; line-height:22px;">
                                                               Hey ${username}, you’re receiving this email because you have an account with Capture The Bug. If you’re unsure why you received this, please contact us at
                                                                <br>
                                                                <div style=" font-family:Inter,sans-serif; font-size: large; margin-top: 20px;">${SUPPORT_EMAIL_ADDRESS}</div>
                                                            </p>
                                                        </div>

                                                        <!-- Thank You Section -->
                                                        <div style="margin-top:45px; text-align:center;">
                                                            <h2 style="font-family:'Inter Tight',sans-serif; font-size:28px; color:#017BFB;">
                                                                Thank You!
                                                            </h2>
                                                            <p style="font-family:Lato,sans-serif; font-size:20px; color:#333333; margin-top:50px;">Team</p>
                                                            <p style="font-family:Lato,sans-serif; font-size:20px; color:#333333; margin-top:30px;">
                                                                <strong style="color:#017BFB; margin-top: 100px; padding-top: 100px;">Capture The Bug</strong>
                                                            </p>
                                                    </td>
                                                </tr>
                                                <tr>
        <td align="center" style="">
            <a href="https://nz.linkedin.com/company/capture-the-bug" target="_blank">
                <img src="https://img.icons8.com/?size=100&id=13930&format=png&color=000000" alt="LinkedIn" width="50" height="50">
            </a>
            <a href="https://x.com/Capturethebugs" target="_blank">
                <img src="https://img.icons8.com/?size=100&id=phOKFKYpe00C&format=png&color=000000" alt="Twitter" width="45" height="45">
            </a>
        </td>
    </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>`;
};

export const WELCOME_MESSAGE_BODY = (username: string) => {
  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en">
<head>
    <title>Email Verification</title>
    <!-- Meta Tags -->
    <meta charset="UTF-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="x-apple-disable-message-reformatting" content="" />
    <meta content="target-densitydpi=device-dpi" name="viewport" />
    <meta content="true" name="HandheldFriendly" />
    <meta content="width=device-width" name="viewport" />
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no, url=no" />

    <!-- Base Styles -->
    <style type="text/css">
        /* Reset Styles */
        table {
            border-collapse: separate;
            table-layout: fixed;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        table td {
            border-collapse: collapse;
        }
        
        /* External Class Styles */
        .ExternalClass {
            width: 100%;
        }
        .ExternalClass,
        .ExternalClass p,
        .ExternalClass span,
        .ExternalClass font,
        .ExternalClass td,
        .ExternalClass div {
            line-height: 100%;
        }

        /* Base Element Styles */
        body, a, li, p, h1, h2, h3 {
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
        }
        html {
            -webkit-text-size-adjust: none !important;
        }
        body, #innerTable {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Image Styles */
        #innerTable img+div {
            display: none !important;
        }
        img {
            margin: 0;
            padding: 0;
            -ms-interpolation-mode: bicubic;
        }

        /* Typography Styles */
        h1, h2, h3, p, a {
            line-height: inherit;
            overflow-wrap: normal;
            white-space: normal;
            word-break: break-word;
        }
        a {
            text-decoration: none;
        }
        h1, h2, h3, p {
            min-width: 100% !important;
            width: 100% !important;
            max-width: 100% !important;
            display: inline-block !important;
            border: 0;
            padding: 0;
            margin: 0;
        }

        /* OTP Box Styles */
        .otp-box {
            display: inline-block;
            background-color: #F5F5F5;
            width: 50px;
            height: 50px;
            margin: 0 5px;
            border-radius: 8px;
            text-align: center;
            vertical-align: middle;
        }
        .otp-number {
            font-family: Inter, BlinkMacSystemFont, Segoe UI, Helvetica Neue, Arial, sans-serif;
            font-size: 24px;
            font-weight: 700;
            color: #111111;
            line-height: 50px;
        }
    </style>

    <!-- Responsive Styles -->
    <style type="text/css">
        @media (min-width: 481px) {
            .hd { display: none !important; }
        }
        @media (max-width: 480px) {
            .hm { display: none !important; }
            .t67, .t72 { 
                mso-line-height-alt: 0px !important;
                line-height: 0 !important;
                display: none !important;
            }
            .t68 { padding-top: 43px !important; }
            .t70 { 
                border: 0 !important;
                border-radius: 0 !important;
            }
            .t65, .t9 { width: 320px !important; }
            .t63 { padding: 40px 30px !important; }
            .t59 { padding-bottom: 36px !important; }
            .t32, .t61 { width: 260px !important; }
            .t54 { text-align: center !important; }
            .t43, .t45, .t49, .t51 { display: revert !important; }
            .t47, .t53 {
                vertical-align: middle !important;
                width: 74px !important;
            }
            .t40 { width: 224px !important; }
        }
    </style>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@500;600;700&family=Lato:wght@400;700&family=Inter+Tight:wght@700&display=swap" rel="stylesheet" type="text/css" />
</head>

<body id="body" class="t75" style="min-width:100%; margin:0; padding:0; background-color:#F9F9F9;">
    <div class="t74" style="background-color:#F9F9F9;">
        <!-- Main Container -->
        <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0" align="center">
            <tr>
                <td class="t73" style="font-size:0; line-height:0; background-color:#F9F9F9;" valign="top" align="center">
                    <!-- Inner Container -->
                    <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0" align="center" id="innerTable">
                        <!-- Spacer -->
                        <tr>
                            <td>
                                <div class="t67" style="mso-line-height-rule:exactly; line-height:70px; font-size:1px;">&nbsp;</div>
                            </td>
                        </tr>
                        
                        <!-- Content Card -->
                        <tr>
                            <td align="center">
                                <table class="t71" role="presentation" cellpadding="0" cellspacing="0" style="margin:0 auto;">
                                    <tr>
                                        <td class="t70" style="background-color:#FFFFFF; border:1px solid #CECECE; width:400px; border-radius:20px;">
                                            <!-- Card Content -->
                                            <table class="t69" role="presentation" cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <td class="t68" style="padding:50px 40px 40px 40px;">
                                                        <!-- Logo -->
                                                        <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
                                                            <tr>
                                                                <td align="center">
                                                                    <img width="158" height="53" alt="Logo" src="https://c8270c56-7594-4100-9b34-7589297588fa.b-cdn.net/e/bac89f4c-a951-463b-81dd-86c0e12c3427/28695d5f-3f29-41fa-a53d-2f93062026e6.jpeg"/>
                                                                </td>
                                                            </tr>
                                                        </table>

                                                        <!-- Title -->
                                                       <div style="margin-top:40px; text-align:center;">
    <p style="font-family:Inter,sans-serif; font-size:15px; color:#424040; line-height:22px;">
        We are excited to welcome you to Capture The Bug!
    </p>
</div>

<!-- Main Message Section (replacing OTP section) -->
<div style="margin:40px 0; text-align:center;">
    <p style="font-family:Inter,sans-serif; font-size:15px; color:#424040; line-height:22px;">
        We know you will love using our platform. As we are testing with a limited number of users, please wait while our admins whitelist your account.
    </p>
    <p style="font-family:Inter,sans-serif; font-size:15px; color:#424040; line-height:22px; margin-top:20px;">
        Once whitelisted, simply log in to your account using the email address and password you provided during registration.
    </p>
</div>

                                                        <!-- Footer Text -->
                                                        <div style="margin-top:40px; margin-bottom: 80px; text-align:center;">
                                                            <p style="font-family:Inter,sans-serif; font-size:14px; color:#424040; line-height:22px;">
                                                             Hey ${username}, you’re receiving this email because you have an account with Capture The Bug. If you’re unsure why you received this, please contact us at
                                                                <br>
                                                                <div style=" font-family:Inter,sans-serif; font-size: large; margin-top: 20px;">${SUPPORT_EMAIL_ADDRESS}</div>
                                                            </p>
                                                        </div>

                                                        <!-- Thank You Section -->
                                                        <div style="margin-top:45px; text-align:center;">
                                                            <h2 style="font-family:'Inter Tight',sans-serif; font-size:28px; color:#017BFB;">
                                                                Thank You!
                                                            </h2>
                                                            <p style="font-family:Lato,sans-serif; font-size:20px; color:#333333; margin-top:50px;">Team</p>
                                                            <p style="font-family:Lato,sans-serif; font-size:20px; color:#333333; margin-top:30px;">
                                                                <strong style="color:#017BFB; margin-top: 100px; padding-top: 100px;">Capture The Bug</strong>
                                                            </p>
                                                    </td>
                                                </tr>
                                                <tr>
        <td align="center" style="">
            <a href="https://nz.linkedin.com/company/capture-the-bug" target="_blank">
                <img src="https://img.icons8.com/?size=100&id=13930&format=png&color=000000" alt="LinkedIn" width="50" height="50">
            </a>
            <a href="https://x.com/Capturethebugs" target="_blank">
                <img src="https://img.icons8.com/?size=100&id=phOKFKYpe00C&format=png&color=000000" alt="Twitter" width="45" height="45">
            </a>
        </td>
    </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>
`;
};

export const ACCOUNT_APPROVED_BODY = (username: string) => {
  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en">
<head>
    <title>Email Verification</title>
    <!-- Meta Tags -->
    <meta charset="UTF-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="x-apple-disable-message-reformatting" content="" />
    <meta content="target-densitydpi=device-dpi" name="viewport" />
    <meta content="true" name="HandheldFriendly" />
    <meta content="width=device-width" name="viewport" />
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no, url=no" />

    <!-- Base Styles -->
    <style type="text/css">
        /* Reset Styles */
        table {
            border-collapse: separate;
            table-layout: fixed;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        table td {
            border-collapse: collapse;
        }
        
        /* External Class Styles */
        .ExternalClass {
            width: 100%;
        }
        .ExternalClass,
        .ExternalClass p,
        .ExternalClass span,
        .ExternalClass font,
        .ExternalClass td,
        .ExternalClass div {
            line-height: 100%;
        }

        /* Base Element Styles */
        body, a, li, p, h1, h2, h3 {
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
        }
        html {
            -webkit-text-size-adjust: none !important;
        }
        body, #innerTable {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Image Styles */
        #innerTable img+div {
            display: none !important;
        }
        img {
            margin: 0;
            padding: 0;
            -ms-interpolation-mode: bicubic;
        }

        /* Typography Styles */
        h1, h2, h3, p, a {
            line-height: inherit;
            overflow-wrap: normal;
            white-space: normal;
            word-break: break-word;
        }
        a {
            text-decoration: none;
        }
        h1, h2, h3, p {
            min-width: 100% !important;
            width: 100% !important;
            max-width: 100% !important;
            display: inline-block !important;
            border: 0;
            padding: 0;
            margin: 0;
        }

        /* OTP Box Styles */
        .otp-box {
            display: inline-block;
            background-color: #F5F5F5;
            width: 50px;
            height: 50px;
            margin: 0 5px;
            border-radius: 8px;
            text-align: center;
            vertical-align: middle;
        }
        .otp-number {
            font-family: Inter, BlinkMacSystemFont, Segoe UI, Helvetica Neue, Arial, sans-serif;
            font-size: 24px;
            font-weight: 700;
            color: #111111;
            line-height: 50px;
        }
    </style>

    <!-- Responsive Styles -->
    <style type="text/css">
        @media (min-width: 481px) {
            .hd { display: none !important; }
        }
        @media (max-width: 480px) {
            .hm { display: none !important; }
            .t67, .t72 { 
                mso-line-height-alt: 0px !important;
                line-height: 0 !important;
                display: none !important;
            }
            .t68 { padding-top: 43px !important; }
            .t70 { 
                border: 0 !important;
                border-radius: 0 !important;
            }
            .t65, .t9 { width: 320px !important; }
            .t63 { padding: 40px 30px !important; }
            .t59 { padding-bottom: 36px !important; }
            .t32, .t61 { width: 260px !important; }
            .t54 { text-align: center !important; }
            .t43, .t45, .t49, .t51 { display: revert !important; }
            .t47, .t53 {
                vertical-align: middle !important;
                width: 74px !important;
            }
            .t40 { width: 224px !important; }
        }
    </style>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@500;600;700&family=Lato:wght@400;700&family=Inter+Tight:wght@700&display=swap" rel="stylesheet" type="text/css" />
</head>

<body id="body" class="t75" style="min-width:100%; margin:0; padding:0; background-color:#F9F9F9;">
    <div class="t74" style="background-color:#F9F9F9;">
        <!-- Main Container -->
        <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0" align="center">
            <tr>
                <td class="t73" style="font-size:0; line-height:0; background-color:#F9F9F9;" valign="top" align="center">
                    <!-- Inner Container -->
                    <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0" align="center" id="innerTable">
                        <!-- Spacer -->
                        <tr>
                            <td>
                                <div class="t67" style="mso-line-height-rule:exactly; line-height:70px; font-size:1px;">&nbsp;</div>
                            </td>
                        </tr>
                        
                        <!-- Content Card -->
                        <tr>
                            <td align="center">
                                <table class="t71" role="presentation" cellpadding="0" cellspacing="0" style="margin:0 auto;">
                                    <tr>
                                        <td class="t70" style="background-color:#FFFFFF; border:1px solid #CECECE; width:400px; border-radius:20px;">
                                            <!-- Card Content -->
                                            <table class="t69" role="presentation" cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <td class="t68" style="padding:50px 40px 40px 40px;">
                                                        <!-- Logo -->
                                                        <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
                                                            <tr>
                                                                <td align="center">
                                                                    <img width="158" height="53" alt="Logo" src="https://c8270c56-7594-4100-9b34-7589297588fa.b-cdn.net/e/bac89f4c-a951-463b-81dd-86c0e12c3427/28695d5f-3f29-41fa-a53d-2f93062026e6.jpeg"/>
                                                                </td>
                                                            </tr>
                                                        </table>

  <div style="margin-top:80px; text-align:center; margin-bottom: 40px;">
    <h1 style="font-family:Inter,sans-serif; font-size:24px; font-weight:600; color:#111111;">
        Account Approved!
    </h1>
</div>

<!-- Description -->
<div style="margin-top:17px; text-align:center;">
    <p style="font-family:Inter,sans-serif; font-size:15px; color:#424040; line-height:22px;">
      We’re excited to inform you that your Capture The Bug account has been approved! You can now start using the platform.
    </p>
</div>

<!-- Login Instructions Section -->
<div style="margin:40px 0; text-align:center;">
    <p style="font-family:Inter,sans-serif; font-size:15px; color:#424040; line-height:22px;">
        To log in to your account, simply visit our website at:
    </p>
    <p style="font-family:Inter,sans-serif; font-size:16px; color:#017BFB; margin-top:15px;">
        <a href="${LOGIN_PAGE_URL}" style="color:#017BFB; text-decoration:underline;">${LOGIN_PAGE_URL}</a>
    </p>
    <p style="font-family:Inter,sans-serif; font-size:15px; color:#424040; line-height:22px; margin-top:15px;">
        Enter the email address and password you provided during registration.
    </p>
</div>

                                                        <!-- Footer Text -->
                                                        <div style="margin-top:40px; margin-bottom: 80px; text-align:center;">
    <p style="font-family:Inter,sans-serif; font-size:14px; color:#424040; line-height:22px;">
        If you need help or have any questions, please don't hesitate to contact our support team at<br>
        <div style="font-family:Inter,sans-serif; font-size: large; margin-top: 20px;">
            ${SUPPORT_EMAIL_ADDRESS}
        </div>
    </p>
    <p style="font-family:Inter,sans-serif; font-size:15px; color:#424040; line-height:22px; margin-top:20px;">
       We look forward to working with you!
    </p>
</div>

                                                        <!-- Thank You Section -->
                                                        <div style="margin-top:45px; text-align:center;">
                                                            <h2 style="font-family:'Inter Tight',sans-serif; font-size:28px; color:#017BFB;">
                                                                Thank You!
                                                            </h2>
                                                            <p style="font-family:Lato,sans-serif; font-size:20px; color:#333333; margin-top:50px;">Team</p>
                                                            <p style="font-family:Lato,sans-serif; font-size:20px; color:#333333; margin-top:30px;">
                                                                <strong style="color:#017BFB; margin-top: 100px; padding-top: 100px;">Capture The Bug</strong>
                                                            </p>
                                                    </td>
                                                </tr>
                                                <tr>
        <td align="center" style="">
            <a href="https://nz.linkedin.com/company/capture-the-bug" target="_blank">
                <img src="https://img.icons8.com/?size=100&id=13930&format=png&color=000000" alt="LinkedIn" width="50" height="50">
            </a>
            <a href="https://x.com/Capturethebugs" target="_blank">
                <img src="https://img.icons8.com/?size=100&id=phOKFKYpe00C&format=png&color=000000" alt="Twitter" width="45" height="45">
            </a>
        </td>
    </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>

`;
};

export const INVITATION_SUBJECT =
  "You're Invited to Capture The Bug – Get Started Now!";

export const INVITATION_BODY = (token: string) => `
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en">
<head>
    <title>Email Verification</title>
    <!-- Meta Tags -->
    <meta charset="UTF-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="x-apple-disable-message-reformatting" content="" />
    <meta content="target-densitydpi=device-dpi" name="viewport" />
    <meta content="true" name="HandheldFriendly" />
    <meta content="width=device-width" name="viewport" />
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no, url=no" />

    <!-- Base Styles -->
    <style type="text/css">
        /* Reset Styles */
        table {
            border-collapse: separate;
            table-layout: fixed;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        table td {
            border-collapse: collapse;
        }
        
        /* External Class Styles */
        .ExternalClass {
            width: 100%;
        }
        .ExternalClass,
        .ExternalClass p,
        .ExternalClass span,
        .ExternalClass font,
        .ExternalClass td,
        .ExternalClass div {
            line-height: 100%;
        }

        /* Base Element Styles */
        body, a, li, p, h1, h2, h3 {
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
        }
        html {
            -webkit-text-size-adjust: none !important;
        }
        body, #innerTable {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Image Styles */
        #innerTable img+div {
            display: none !important;
        }
        img {
            margin: 0;
            padding: 0;
            -ms-interpolation-mode: bicubic;
        }

        /* Typography Styles */
        h1, h2, h3, p, a {
            line-height: inherit;
            overflow-wrap: normal;
            white-space: normal;
            word-break: break-word;
        }
        a {
            text-decoration: none;
        }
        h1, h2, h3, p {
            min-width: 100% !important;
            width: 100% !important;
            max-width: 100% !important;
            display: inline-block !important;
            border: 0;
            padding: 0;
            margin: 0;
        }

        /* OTP Box Styles */
        .otp-box {
            display: inline-block;
            background-color: #F5F5F5;
            width: 50px;
            height: 50px;
            margin: 0 5px;
            border-radius: 8px;
            text-align: center;
            vertical-align: middle;
        }
        .otp-number {
            font-family: Inter, BlinkMacSystemFont, Segoe UI, Helvetica Neue, Arial, sans-serif;
            font-size: 24px;
            font-weight: 700;
            color: #111111;
            line-height: 50px;
        }
    </style>

    <!-- Responsive Styles -->
    <style type="text/css">
        @media (min-width: 481px) {
            .hd { display: none !important; }
        }
        @media (max-width: 480px) {
            .hm { display: none !important; }
            .t67, .t72 { 
                mso-line-height-alt: 0px !important;
                line-height: 0 !important;
                display: none !important;
            }
            .t68 { padding-top: 43px !important; }
            .t70 { 
                border: 0 !important;
                border-radius: 0 !important;
            }
            .t65, .t9 { width: 320px !important; }
            .t63 { padding: 40px 30px !important; }
            .t59 { padding-bottom: 36px !important; }
            .t32, .t61 { width: 260px !important; }
            .t54 { text-align: center !important; }
            .t43, .t45, .t49, .t51 { display: revert !important; }
            .t47, .t53 {
                vertical-align: middle !important;
                width: 74px !important;
            }
            .t40 { width: 224px !important; }
        }
    </style>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@500;600;700&family=Lato:wght@400;700&family=Inter+Tight:wght@700&display=swap" rel="stylesheet" type="text/css" />
</head>

<body id="body" class="t75" style="min-width:100%; margin:0; padding:0; background-color:#F9F9F9;">
    <div class="t74" style="background-color:#F9F9F9;">
        <!-- Main Container -->
        <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0" align="center">
            <tr>
                <td class="t73" style="font-size:0; line-height:0; background-color:#F9F9F9;" valign="top" align="center">
                    <!-- Inner Container -->
                    <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0" align="center" id="innerTable">
                        <!-- Spacer -->
                        <tr>
                            <td>
                                <div class="t67" style="mso-line-height-rule:exactly; line-height:70px; font-size:1px;">&nbsp;</div>
                            </td>
                        </tr>
                        
                        <!-- Content Card -->
                        <tr>
                            <td align="center">
                                <table class="t71" role="presentation" cellpadding="0" cellspacing="0" style="margin:0 auto;">
                                    <tr>
                                        <td class="t70" style="background-color:#FFFFFF; border:1px solid #CECECE; width:400px; border-radius:20px;">
                                            <!-- Card Content -->
                                            <table class="t69" role="presentation" cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <td class="t68" style="padding:50px 40px 40px 40px;">
                                                        <!-- Logo -->
                                                        <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
                                                            <tr>
                                                                <td align="center">
                                                                    <img width="158" height="53" alt="Logo" src="https://c8270c56-7594-4100-9b34-7589297588fa.b-cdn.net/e/bac89f4c-a951-463b-81dd-86c0e12c3427/28695d5f-3f29-41fa-a53d-2f93062026e6.jpeg"/>
                                                                </td>
                                                            </tr>
                                                        </table>

   <div style="margin-top:80px; text-align:center; margin-bottom: 40px;">
                                                            <h1 style="font-family:Inter,sans-serif; font-size:24px; font-weight:600; color:#111111;">
                                                                You're Invited!
                                                            </h1>
                                                        </div>

                                                        <!-- Main Content -->
                                                        <div style="margin-top:17px; text-align:center;">
                                                            <p style="font-family:Inter,sans-serif; font-size:15px; color:#424040; line-height:22px;">
                                                               You’ve been invited to join Capture The Bug and have been assigned a role on our platform!
                                                            </p>
                                                        </div>

                                                        <!-- Action Button -->
                                                        <div style="margin:40px 0; text-align:center;">
                                                            <a href="${process.env.FRONTEND_BASE_URL}/register/invited/${token}" 
                                                               style="background-color:#017BFB; color:#FFFFFF; padding:15px 30px; border-radius:8px; font-family:Inter,sans-serif; font-size:16px; font-weight:600; text-decoration:none; display:inline-block;">
                                                                Click Here to Get Started
                                                            </a>
                                                        </div>

                                                        <!-- Warning Note -->
                                                        <div style="margin-top:30px; text-align:center; background-color:#FFF4E5; padding:15px; border-radius:8px;">
                                                            <p style="font-family:Inter,sans-serif; font-size:14px; color:#424040; line-height:22px; margin:0;">
                                                                ⚠️ <strong>Note:</strong> This invitation link expires in <strong>7 days</strong>, so be sure to sign up soon!
                                                            </p>
                                                        </div>

                                                        <!-- Support Text -->
                                                        <div style="margin-top:40px; margin-bottom:80px; text-align:center;">
                                                            <p style="font-family:Inter,sans-serif; font-size:14px; color:#424040; line-height:22px;">
                                                                If you have any questions, feel free to reach out to our support team at<br>
                                                                <div style="font-family:Inter,sans-serif; font-size:large; margin-top:20px;">
                                                                    <EMAIL>
                                                                </div>
                                                            </p>
                                                        </div>

                                                        <!-- Thank You Section -->
                                                        <div style="margin-top:45px; text-align:center;">
                                                            <h2 style="font-family:'Inter Tight',sans-serif; font-size:28px; color:#017BFB;">
                                                                Thank You!
                                                            </h2>
                                                            <p style="font-family:Lato,sans-serif; font-size:20px; color:#333333; margin-top:50px;">Team</p>
                                                            <p style="font-family:Lato,sans-serif; font-size:20px; color:#333333; margin-top:30px;">
                                                                <strong style="color:#017BFB; margin-top: 100px; padding-top: 100px;">Capture The Bug</strong>
                                                            </p>
                                                    </td>
                                                </tr>
                                                <tr>
        <td align="center" style="">
            <a href="https://nz.linkedin.com/company/capture-the-bug" target="_blank">
                <img src="https://img.icons8.com/?size=100&id=13930&format=png&color=000000" alt="LinkedIn" width="50" height="50">
            </a>
            <a href="https://x.com/Capturethebugs" target="_blank">
                <img src="https://img.icons8.com/?size=100&id=phOKFKYpe00C&format=png&color=000000" alt="Twitter" width="45" height="45">
            </a>
        </td>
    </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>
`;

export const PASSWORD_RESET_BODY = (
  username: string,
  token: string,
  email: string
) =>
  `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en">
<head>
    <title>Email Verification</title>
    <!-- Meta Tags -->
    <meta charset="UTF-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="x-apple-disable-message-reformatting" content="" />
    <meta content="target-densitydpi=device-dpi" name="viewport" />
    <meta content="true" name="HandheldFriendly" />
    <meta content="width=device-width" name="viewport" />
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no, url=no" />

    <!-- Base Styles -->
    <style type="text/css">
        /* Reset Styles */
        table {
            border-collapse: separate;
            table-layout: fixed;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        table td {
            border-collapse: collapse;
        }
        
        /* External Class Styles */
        .ExternalClass {
            width: 100%;
        }
        .ExternalClass,
        .ExternalClass p,
        .ExternalClass span,
        .ExternalClass font,
        .ExternalClass td,
        .ExternalClass div {
            line-height: 100%;
        }

        /* Base Element Styles */
        body, a, li, p, h1, h2, h3 {
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
        }
        html {
            -webkit-text-size-adjust: none !important;
        }
        body, #innerTable {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Image Styles */
        #innerTable img+div {
            display: none !important;
        }
        img {
            margin: 0;
            padding: 0;
            -ms-interpolation-mode: bicubic;
        }

        /* Typography Styles */
        h1, h2, h3, p, a {
            line-height: inherit;
            overflow-wrap: normal;
            white-space: normal;
            word-break: break-word;
        }
        a {
            text-decoration: none;
        }
        h1, h2, h3, p {
            min-width: 100% !important;
            width: 100% !important;
            max-width: 100% !important;
            display: inline-block !important;
            border: 0;
            padding: 0;
            margin: 0;
        }

        /* OTP Box Styles */
        .otp-box {
            display: inline-block;
            background-color: #F5F5F5;
            width: 50px;
            height: 50px;
            margin: 0 5px;
            border-radius: 8px;
            text-align: center;
            vertical-align: middle;
        }
        .otp-number {
            font-family: Inter, BlinkMacSystemFont, Segoe UI, Helvetica Neue, Arial, sans-serif;
            font-size: 24px;
            font-weight: 700;
            color: #111111;
            line-height: 50px;
        }
    </style>

    <!-- Responsive Styles -->
    <style type="text/css">
        @media (min-width: 481px) {
            .hd { display: none !important; }
        }
        @media (max-width: 480px) {
            .hm { display: none !important; }
            .t67, .t72 { 
                mso-line-height-alt: 0px !important;
                line-height: 0 !important;
                display: none !important;
            }
            .t68 { padding-top: 43px !important; }
            .t70 { 
                border: 0 !important;
                border-radius: 0 !important;
            }
            .t65, .t9 { width: 320px !important; }
            .t63 { padding: 40px 30px !important; }
            .t59 { padding-bottom: 36px !important; }
            .t32, .t61 { width: 260px !important; }
            .t54 { text-align: center !important; }
            .t43, .t45, .t49, .t51 { display: revert !important; }
            .t47, .t53 {
                vertical-align: middle !important;
                width: 74px !important;
            }
            .t40 { width: 224px !important; }
        }
    </style>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@500;600;700&family=Lato:wght@400;700&family=Inter+Tight:wght@700&display=swap" rel="stylesheet" type="text/css" />
</head>

<body id="body" class="t75" style="min-width:100%; margin:0; padding:0; background-color:#F9F9F9;">
    <div class="t74" style="background-color:#F9F9F9;">
        <!-- Main Container -->
        <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0" align="center">
            <tr>
                <td class="t73" style="font-size:0; line-height:0; background-color:#F9F9F9;" valign="top" align="center">
                    <!-- Inner Container -->
                    <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0" align="center" id="innerTable">
                        <!-- Spacer -->
                        <tr>
                            <td>
                                <div class="t67" style="mso-line-height-rule:exactly; line-height:70px; font-size:1px;">&nbsp;</div>
                            </td>
                        </tr>
                        
                        <!-- Content Card -->
                        <tr>
                            <td align="center">
                                <table class="t71" role="presentation" cellpadding="0" cellspacing="0" style="margin:0 auto;">
                                    <tr>
                                        <td class="t70" style="background-color:#FFFFFF; border:1px solid #CECECE; width:400px; border-radius:20px;">
                                            <!-- Card Content -->
                                            <table class="t69" role="presentation" cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <td class="t68" style="padding:50px 40px 40px 40px;">
                                                        <!-- Logo -->
                                                        <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
                                                            <tr>
                                                                <td align="center">
                                                                    <img width="158" height="53" alt="Logo" src="https://c8270c56-7594-4100-9b34-7589297588fa.b-cdn.net/e/bac89f4c-a951-463b-81dd-86c0e12c3427/28695d5f-3f29-41fa-a53d-2f93062026e6.jpeg"/>
                                                                </td>
                                                            </tr>
                                                        </table>
<div style="margin-top:80px; text-align:center; margin-bottom: 40px;">
                                                            <h1 style="font-family:Inter,sans-serif; font-size:24px; font-weight:600; color:#111111;">
                                                                Password Reset
                                                            </h1>
                                                        </div>

                                                        <!-- Main Content -->
                                                        <div style="margin-top:17px; text-align:center;">
                                                            <p style="font-family:Inter,sans-serif; font-size:15px; color:#424040; line-height:22px;">
                                                                Hey ${username},<br><br>
                                                               We received a request to reset your account password. If you didn’t request this, please ignore this email.
                                                            </p>
                                                        </div>

                                                        <!-- Reset Button -->
                                                        <div style="margin:40px 0; text-align:center;">
                                                            <a href="${process.env.FRONTEND_BASE_URL}/${RESET_PASSWORD_PAGE_URL}?email=${email}&token=${token}" 
                                                               style="background-color:#017BFB; color:#FFFFFF; padding:15px 30px; border-radius:8px; font-family:Inter,sans-serif; font-size:16px; font-weight:600; text-decoration:none; display:inline-block;">
                                                                Reset Password
                                                            </a>
                                                        </div>

                                                       

                                                        <!-- Support Text -->
                                                        <div style="margin-top:40px; margin-bottom:80px; text-align:center;">
                                                            <p style="font-family:Inter,sans-serif; font-size:14px; color:#424040; line-height:22px;">
                                                                If you have any questions or concerns, please contact us at<br>
                                                                <div style="font-family:Inter,sans-serif; font-size:large; margin-top:20px;">
                                                                    ${SUPPORT_EMAIL_ADDRESS}
                                                                </div>
                                                            </p>
                                                        </div>

                                                        <!-- Thank You Section -->
                                                        <div style="margin-top:45px; text-align:center;">
                                                            <h2 style="font-family:'Inter Tight',sans-serif; font-size:28px; color:#017BFB;">
                                                                Thank You!
                                                            </h2>
                                                            <p style="font-family:Lato,sans-serif; font-size:20px; color:#333333; margin-top:50px;">Team</p>
                                                            <p style="font-family:Lato,sans-serif; font-size:20px; color:#333333; margin-top:30px;">
                                                                <strong style="color:#017BFB; margin-top: 100px; padding-top: 100px;">Capture The Bug</strong>
                                                            </p>
                                                    </td>
                                                </tr>
                                                <tr>
        <td align="center" style="">
            <a href="https://nz.linkedin.com/company/capture-the-bug" target="_blank">
                <img src="https://img.icons8.com/?size=100&id=13930&format=png&color=000000" alt="LinkedIn" width="50" height="50">
            </a>
            <a href="https://x.com/Capturethebugs" target="_blank">
                <img src="https://img.icons8.com/?size=100&id=phOKFKYpe00C&format=png&color=000000" alt="Twitter" width="45" height="45">
            </a>
        </td>
    </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>

`;

export const BOUNTY_RESEARCHER_TEMPLATE = (
  username: string,
  company_name: string,
  amount: string,
  payment_mode: string
) =>
  `
Hey ${username},

We are pleased to inform you that you have received a bounty from ${company_name} for your recent research submission on our platform. 
We appreciate your contribution and the value that it brings to the community.

The details of the bounty are as follows:

    Amount: $${amount}
    Payout method: ${payment_mode}

Thank you for your efforts and for helping to drive innovation and improvement in our community. 
We look forward to seeing more of your valuable contributions in the future.

Sincerely,
Capture The Bug

`;

export const BOUNTY_BUSINESS_TEMPLATE = (
  username: string,
  researcher_name: string,
  amount: string,
  report_id: string
) =>
  `
Dear ${username},

We are writing to inform you that we have processed the bounty submission from a researcher on our platform.

Details of the submission are as follows:

    Report Id: ${report_id}
    Researcher name: ${researcher_name}
    Amount: ${amount}

Please let us know if you have any questions or concerns about this submission.

Thank you for your partnership in responsible disclosure.

Sincerely,
Capture The Bug

`;

export const CERTIFICATE_GENERATED_CTB_BODY = (username: string) => {
  return `Dear ${username},

We are writing to express our deepest gratitude and appreciation for your valuable contribution to our VDP Program. 
Your expertise and dedication in identifying and reporting critical security vulnerabilities on our platform have greatly contributed to enhancing the security and integrity of our system.

In recognition of your exceptional skills and efforts, we are pleased to present you with this Certificate of Appreciation for your successful submission of valid bugs.

Once again, we would like to express our sincere appreciation for your valuable contributions. 
Please find attached your Certificate of Appreciation as a token of our gratitude.

Should you have any further questions or require any assistance, please do not hesitate to contact us. 
We look forward to future collaboration and wish you continued success in your security research endeavors.

Best regards,
Capture The Bug
    
`;
};

export const CERTIFICATE_GENERATED_CTB_PARTNER_BODY = (
  username: string,
  type: string
) => {
  return `Dear ${username},

We are writing to express our deepest gratitude and appreciation for your valuable contribution to our partner's ${type} Program. 
Your expertise and dedication in identifying and reporting critical security vulnerabilities on our partner's platform have greatly contributed to enhancing the security and integrity of the system.

In recognition of your exceptional skills and efforts, we are pleased to present you with this Certificate of Appreciation for your successful submission of valid bugs.

Once again, we would like to express our sincere appreciation for your valuable contributions. 
Please find attached your Certificate of Appreciation as a token of our gratitude.

Should you have any further questions or require any assistance, please do not hesitate to contact us. 
We look forward to future collaboration and wish you continued success in your security research endeavors.

Best regards,
Capture The Bug
    
`;
};

export const NEW_COMMENT_ON_REPORT_BODY = (
  username: string,
  report_title: string,
  commentor_username: string,
  comment: string
) => {
  return `
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en">
<head>
    <title>Email Verification</title>
    <!-- Meta Tags -->
    <meta charset="UTF-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="x-apple-disable-message-reformatting" content="" />
    <meta content="target-densitydpi=device-dpi" name="viewport" />
    <meta content="true" name="HandheldFriendly" />
    <meta content="width=device-width" name="viewport" />
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no, url=no" />

    <!-- Base Styles -->
    <style type="text/css">
        /* Reset Styles */
        table {
            border-collapse: separate;
            table-layout: fixed;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        table td {
            border-collapse: collapse;
        }
        
        /* External Class Styles */
        .ExternalClass {
            width: 100%;
        }
        .ExternalClass,
        .ExternalClass p,
        .ExternalClass span,
        .ExternalClass font,
        .ExternalClass td,
        .ExternalClass div {
            line-height: 100%;
        }

        /* Base Element Styles */
        body, a, li, p, h1, h2, h3 {
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
        }
        html {
            -webkit-text-size-adjust: none !important;
        }
        body, #innerTable {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Image Styles */
        #innerTable img+div {
            display: none !important;
        }
        img {
            margin: 0;
            padding: 0;
            -ms-interpolation-mode: bicubic;
        }

        /* Typography Styles */
        h1, h2, h3, p, a {
            line-height: inherit;
            overflow-wrap: normal;
            white-space: normal;
            word-break: break-word;
        }
        a {
            text-decoration: none;
        }
        h1, h2, h3, p {
            min-width: 100% !important;
            width: 100% !important;
            max-width: 100% !important;
            display: inline-block !important;
            border: 0;
            padding: 0;
            margin: 0;
        }

        /* OTP Box Styles */
        .otp-box {
            display: inline-block;
            background-color: #F5F5F5;
            width: 50px;
            height: 50px;
            margin: 0 5px;
            border-radius: 8px;
            text-align: center;
            vertical-align: middle;
        }
        .otp-number {
            font-family: Inter, BlinkMacSystemFont, Segoe UI, Helvetica Neue, Arial, sans-serif;
            font-size: 24px;
            font-weight: 700;
            color: #111111;
            line-height: 50px;
        }
    </style>

    <!-- Responsive Styles -->
    <style type="text/css">
        @media (min-width: 481px) {
            .hd { display: none !important; }
        }
        @media (max-width: 480px) {
            .hm { display: none !important; }
            .t67, .t72 { 
                mso-line-height-alt: 0px !important;
                line-height: 0 !important;
                display: none !important;
            }
            .t68 { padding-top: 43px !important; }
            .t70 { 
                border: 0 !important;
                border-radius: 0 !important;
            }
            .t65, .t9 { width: 320px !important; }
            .t63 { padding: 40px 30px !important; }
            .t59 { padding-bottom: 36px !important; }
            .t32, .t61 { width: 260px !important; }
            .t54 { text-align: center !important; }
            .t43, .t45, .t49, .t51 { display: revert !important; }
            .t47, .t53 {
                vertical-align: middle !important;
                width: 74px !important;
            }
            .t40 { width: 224px !important; }
        }
    </style>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@500;600;700&family=Lato:wght@400;700&family=Inter+Tight:wght@700&display=swap" rel="stylesheet" type="text/css" />
</head>

<body id="body" class="t75" style="min-width:100%; margin:0; padding:0; background-color:#F9F9F9;">
    <div class="t74" style="background-color:#F9F9F9;">
        <!-- Main Container -->
        <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0" align="center">
            <tr>
                <td class="t73" style="font-size:0; line-height:0; background-color:#F9F9F9;" valign="top" align="center">
                    <!-- Inner Container -->
                    <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0" align="center" id="innerTable">
                        <!-- Spacer -->
                        <tr>
                            <td>
                                <div class="t67" style="mso-line-height-rule:exactly; line-height:70px; font-size:1px;">&nbsp;</div>
                            </td>
                        </tr>
                        
                        <!-- Content Card -->
                        <tr>
                            <td align="center">
                                <table class="t71" role="presentation" cellpadding="0" cellspacing="0" style="margin:0 auto;">
                                    <tr>
                                        <td class="t70" style="background-color:#FFFFFF; border:1px solid #CECECE; width:400px; border-radius:20px;">
                                            <!-- Card Content -->
                                            <table class="t69" role="presentation" cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <td class="t68" style="padding:50px 40px 40px 40px;">
                                                        <!-- Logo -->
                                                        <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
                                                            <tr>
                                                                <td align="center">
                                                                    <img width="158" height="53" alt="Logo" src="https://c8270c56-7594-4100-9b34-7589297588fa.b-cdn.net/e/bac89f4c-a951-463b-81dd-86c0e12c3427/28695d5f-3f29-41fa-a53d-2f93062026e6.jpeg"/>
                                                                </td>
                                                            </tr>
                                                        </table>
      <div style="margin-top:80px; text-align:center; margin-bottom: 40px;">
                                                            <h1 style="font-family:Inter,sans-serif; font-size:24px; font-weight:600; color:#111111;">
                                                                New Comment
                                                            </h1>
                                                        </div>

                                                        <!-- Main Content -->
                                                        <div style="margin-top:17px; text-align:center;">
                                                            <p style="font-family:Inter,sans-serif; font-size:15px; color:#424040; line-height:22px;">
                                                                Hello ${username},<br><br>
                                                               A new comment has been added to your report:
                                                            </p>
                                                        </div>

                                                        <!-- Report Title Box -->
                                                        <div style="margin:20px 0; text-align:center; background-color:#F5F5F5; padding:15px; border-radius:8px;">
                                                            <p style="font-family:Inter,sans-serif; font-size:16px; color:#017BFB; font-weight:600; margin:0;">
                                                                "${report_title}"
                                                            </p>
                                                        </div>

                                                        <!-- Comment Details -->
                                                        <div style="margin:20px 0; text-align:center;">
                                                            <p style="font-family:Inter,sans-serif; font-size:15px; color:#424040; line-height:22px;">
                                                                Comment by: <strong>${commentor_username}</strong>
                                                            </p>
                                                        </div>

                                                        <!-- Action Button -->
                                                        

                                                        <!-- Support Text -->
                                                        <div style="margin-top:40px; margin-bottom:80px; text-align:center;">
                                                            <p style="font-family:Inter,sans-serif; font-size:14px; color:#424040; line-height:22px;">
                                                                If you have any questions or need assistance, contact us at<br>
                                                                <div style="font-family:Inter,sans-serif; font-size:large; margin-top:20px;">
                                                                    ${SUPPORT_EMAIL_ADDRESS}
                                                                </div>
                                                            </p>
                                                        </div>

                                                        <!-- Thank You Section -->
                                                        <div style="margin-top:45px; text-align:center;">
                                                            <h2 style="font-family:'Inter Tight',sans-serif; font-size:28px; color:#017BFB;">
                                                                Thank You!
                                                            </h2>
                                                            <p style="font-family:Lato,sans-serif; font-size:20px; color:#333333; margin-top:50px;">Team</p>
                                                            <p style="font-family:Lato,sans-serif; font-size:20px; color:#333333; margin-top:30px;">
                                                                <strong style="color:#017BFB; margin-top: 100px; padding-top: 100px;">Capture The Bug</strong>
                                                            </p>
                                                    </td>
                                                </tr>
                                                <tr>
        <td align="center" style="">
            <a href="https://nz.linkedin.com/company/capture-the-bug" target="_blank">
                <img src="https://img.icons8.com/?size=100&id=13930&format=png&color=000000" alt="LinkedIn" width="50" height="50">
            </a>
            <a href="https://x.com/Capturethebugs" target="_blank">
                <img src="https://img.icons8.com/?size=100&id=phOKFKYpe00C&format=png&color=000000" alt="Twitter" width="45" height="45">
            </a>
        </td>
    </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>
`;
};

export const NEW_REPORT_BODY = (
  username: string,
  report_title: string,
  report_id: string
) => {
  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en">
<head>
    <title>Email Verification</title>
    <!-- Meta Tags -->
    <meta charset="UTF-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="x-apple-disable-message-reformatting" content="" />
    <meta content="target-densitydpi=device-dpi" name="viewport" />
    <meta content="true" name="HandheldFriendly" />
    <meta content="width=device-width" name="viewport" />
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no, url=no" />

    <!-- Base Styles -->
    <style type="text/css">
        /* Reset Styles */
        table {
            border-collapse: separate;
            table-layout: fixed;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        table td {
            border-collapse: collapse;
        }
        
        /* External Class Styles */
        .ExternalClass {
            width: 100%;
        }
        .ExternalClass,
        .ExternalClass p,
        .ExternalClass span,
        .ExternalClass font,
        .ExternalClass td,
        .ExternalClass div {
            line-height: 100%;
        }

        /* Base Element Styles */
        body, a, li, p, h1, h2, h3 {
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
        }
        html {
            -webkit-text-size-adjust: none !important;
        }
        body, #innerTable {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Image Styles */
        #innerTable img+div {
            display: none !important;
        }
        img {
            margin: 0;
            padding: 0;
            -ms-interpolation-mode: bicubic;
        }

        /* Typography Styles */
        h1, h2, h3, p, a {
            line-height: inherit;
            overflow-wrap: normal;
            white-space: normal;
            word-break: break-word;
        }
        a {
            text-decoration: none;
        }
        h1, h2, h3, p {
            min-width: 100% !important;
            width: 100% !important;
            max-width: 100% !important;
            display: inline-block !important;
            border: 0;
            padding: 0;
            margin: 0;
        }

        /* OTP Box Styles */
        .otp-box {
            display: inline-block;
            background-color: #F5F5F5;
            width: 50px;
            height: 50px;
            margin: 0 5px;
            border-radius: 8px;
            text-align: center;
            vertical-align: middle;
        }
        .otp-number {
            font-family: Inter, BlinkMacSystemFont, Segoe UI, Helvetica Neue, Arial, sans-serif;
            font-size: 24px;
            font-weight: 700;
            color: #111111;
            line-height: 50px;
        }
    </style>

    <!-- Responsive Styles -->
    <style type="text/css">
        @media (min-width: 481px) {
            .hd { display: none !important; }
        }
        @media (max-width: 480px) {
            .hm { display: none !important; }
            .t67, .t72 { 
                mso-line-height-alt: 0px !important;
                line-height: 0 !important;
                display: none !important;
            }
            .t68 { padding-top: 43px !important; }
            .t70 { 
                border: 0 !important;
                border-radius: 0 !important;
            }
            .t65, .t9 { width: 320px !important; }
            .t63 { padding: 40px 30px !important; }
            .t59 { padding-bottom: 36px !important; }
            .t32, .t61 { width: 260px !important; }
            .t54 { text-align: center !important; }
            .t43, .t45, .t49, .t51 { display: revert !important; }
            .t47, .t53 {
                vertical-align: middle !important;
                width: 74px !important;
            }
            .t40 { width: 224px !important; }
        }
    </style>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@500;600;700&family=Lato:wght@400;700&family=Inter+Tight:wght@700&display=swap" rel="stylesheet" type="text/css" />
</head>

<body id="body" class="t75" style="min-width:100%; margin:0; padding:0; background-color:#F9F9F9;">
    <div class="t74" style="background-color:#F9F9F9;">
        <!-- Main Container -->
        <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0" align="center">
            <tr>
                <td class="t73" style="font-size:0; line-height:0; background-color:#F9F9F9;" valign="top" align="center">
                    <!-- Inner Container -->
                    <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0" align="center" id="innerTable">
                        <!-- Spacer -->
                        <tr>
                            <td>
                                <div class="t67" style="mso-line-height-rule:exactly; line-height:70px; font-size:1px;">&nbsp;</div>
                            </td>
                        </tr>
                        
                        <!-- Content Card -->
                        <tr>
                            <td align="center">
                                <table class="t71" role="presentation" cellpadding="0" cellspacing="0" style="margin:0 auto;">
                                    <tr>
                                        <td class="t70" style="background-color:#FFFFFF; border:1px solid #CECECE; width:400px; border-radius:20px;">
                                            <!-- Card Content -->
                                            <table class="t69" role="presentation" cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <td class="t68" style="padding:50px 40px 40px 40px;">
                                                        <!-- Logo -->
                                                        <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
                                                            <tr>
                                                                <td align="center">
                                                                    <img width="158" height="53" alt="Logo" src="https://c8270c56-7594-4100-9b34-7589297588fa.b-cdn.net/e/bac89f4c-a951-463b-81dd-86c0e12c3427/28695d5f-3f29-41fa-a53d-2f93062026e6.jpeg"/>
                                                                </td>
                                                            </tr>
                                                        </table>
   
                                                            <!-- Title -->
                                                        <div style="margin-top:80px; text-align:center; margin-bottom: 40px;">
                                                            <h1 style="font-family:Inter,sans-serif; font-size:24px; font-weight:600; color:#111111; line-height: 40px;">
                                                                New Security Report Available
                                                            </h1>
                                                        </div>

                                                        <!-- Main Content -->
                                                        <div style="margin-top:17px; text-align:center;">
                                                            <p style="font-family:Inter,sans-serif; font-size:15px; color:#424040; line-height:22px;">
                                                                Dear ${username},<br><br>
                                                                A new security report has been generated for your program, providing valuable insights from our security researchers.
                                                            </p>
                                                        </div>

                                                        <!-- Report Details Box -->
                                                        <div style="margin:30px 0; text-align:left; background-color:#F5F5F5; padding:20px; border-radius:8px;">
                                                            <p style="font-family:Inter,sans-serif; font-size:15px; color:#424040; line-height:24px; margin:0;">
                                                                <strong>Report Title:</strong> ${report_title}<br>
                                                                <strong>Report ID:</strong> ${report_id}
                                                            </p>
                                                        </div>

                                                        <!-- Action Button -->
                                                        <div style="margin:40px 0; text-align:center;">
                                                            <a href="${process.env.FRONTEND_BASE_URL}/dashboard/report/${report_id}" 
                                                               style="background-color:#017BFB; color:#FFFFFF; padding:15px 30px; border-radius:8px; font-family:Inter,sans-serif; font-size:16px; font-weight:600; text-decoration:none; display:inline-block;">
                                                                Review Security Report
                                                            </a>
                                                        </div>

                                                        <!-- Support Text -->
                                                        <div style="margin-top:40px; margin-bottom:80px; text-align:center;">
                                                            <p style="font-family:Inter,sans-serif; font-size:14px; color:#424040; line-height:22px;">
                                                                For any questions or assistance, contact our support team at<br>
                                                                <div style="font-family:Inter,sans-serif; font-size:large; margin-top:20px;">
                                                                    ${SUPPORT_EMAIL_ADDRESS}
                                                                </div>
                                                            </p>
                                                        </div>

                                                        <!-- Thank You Section -->
                                                        <div style="margin-top:45px; text-align:center;">
                                                            <h2 style="font-family:'Inter Tight',sans-serif; font-size:28px; color:#017BFB;">
                                                                Thank You!
                                                            </h2>
                                                            <p style="font-family:Lato,sans-serif; font-size:20px; color:#333333; margin-top:50px;">Team</p>
                                                            <p style="font-family:Lato,sans-serif; font-size:20px; color:#333333; margin-top:30px;">
                                                                <strong style="color:#017BFB; margin-top: 100px; padding-top: 100px;">Capture The Bug</strong>
                                                            </p>
                                                    </td>
                                                </tr>
                                                <tr>
        <td align="center" style="">
            <a href="https://nz.linkedin.com/company/capture-the-bug" target="_blank">
                <img src="https://img.icons8.com/?size=100&id=13930&format=png&color=000000" alt="LinkedIn" width="50" height="50">
            </a>
            <a href="https://x.com/Capturethebugs" target="_blank">
                <img src="https://img.icons8.com/?size=100&id=phOKFKYpe00C&format=png&color=000000" alt="Twitter" width="45" height="45">
            </a>
        </td>
    </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>
`;
};

export const PTAAS_EXECUTIVE_SUMMARY = (
  company_name: string,
  severity_stats: {
    total_issues: number;
    [SeverityTypes.CRITICAL]: number;
    [SeverityTypes.HIGH]: number;
    [SeverityTypes.MEDIUM]: number;
    [SeverityTypes.LOW]: number;
  }
): string =>
  `
  Capture The Bug is a modern Penetration Testing as a Service (PTaaS) platform designed to support fast-moving engineering teams and security-conscious enterprises. Organizations across New Zealand, Australia, and North America-including public companies and high-growth SaaS teams-trust Capture The Bug to secure their CI/CD pipelines, meet compliance goals, and reduce time-to-remediation without slowing development velocity. Capture The Bug is HQ in New Zealand and proudly works with customers across regulated and innovation-driven sectors.
  ${company_name}, has developed an in-house software application. The company has commissioned Capture The Bug
  to conduct a comprehensive Vulnerability Assessment and Penetration Test (VAPT).
  The aim of this assessment is to evaluate the application's security posture from a
  gray-box perspective, focusing on its resilience against common attack patterns and
  pinpointing vulnerabilities in its internal and external interfaces that could be
  exploited by malicious users.
  SecurityLit's thorough assessment resulted in the identification of ${severity_stats?.total_issues}
  findings. Of these, 
  ${
    severity_stats?.[SeverityTypes.CRITICAL]
      ? `${severity_stats?.[
          SeverityTypes.CRITICAL
        ]} were categorized as Critical severity,`
      : ""
  } 
  ${
    severity_stats?.[SeverityTypes.HIGH]
      ? `${severity_stats?.[
          SeverityTypes.HIGH
        ]} were categorized as High severity,`
      : ""
  }
  ${
    severity_stats?.[SeverityTypes.MEDIUM]
      ? `${severity_stats?.[
          SeverityTypes.MEDIUM
        ]} were categorized as Medium severity,`
      : ""
  }
  ${
    severity_stats?.[SeverityTypes.LOW]
      ? `${severity_stats?.[
          SeverityTypes.LOW
        ]} were categorized as Low severity,`
      : ""
  }
  These findings are detailed in the subsequent sections of this report,
  each accompanied by recommended mitigation strategies to address the identified vulnerabilities.
  Critical and High severity vulnerabilities are already been reported as advance vulnerability.
  `;

export enum NotificationMethod {
  CTB = "notify-ctb",
  SLACK = "notify-slack",
  JIRA = "notify-jira",
  ALL = "notify-all"
}

export enum SlackMessage {
  ReportIn = "A vulnerability report has been submitted",
  ReportCheck = "Report is in review",
  ReportRejected = "Report has been rejected",
  ReportFeedback = "Report needs modification",
  ReportAccepted = "Report has been accepted",
  ReportReview = "Report is under review"
}
