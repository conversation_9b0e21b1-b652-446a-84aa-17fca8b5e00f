import { Controller, useFormContext } from "react-hook-form";
import EditorInput from "./EditorInput";
import { InputBaseParams } from "../../forms/Form";
import { CropOptions } from "../../file_upload/useImageCrop";
import AttachmentsInput from "../../forms/inputs/AttachmentsInput";
import React, { useEffect, useState } from "react";
import { FiPaperclip, FiTrash2, FiRotateCcw } from "react-icons/fi";
import { FaDownload } from "react-icons/fa";

const ProgramEditorAttachments = ({
  name,
  inputTitle: title,
  details,
  placeholder,
  note,
  accept,
  multiple,
  cropImage,
  defaultLinks = []
}: InputBaseParams & {
  inputTitle: string;
  details: string;
  placeholder?: string;
  accept?: string;
  multiple?: boolean;
  note?: string;
  cropImage?: CropOptions;
  defaultLinks?: string[] | string;
}) => {
  const { control, setError, clearErrors, setValue, getValues } =
    useFormContext();
  const [normalizedDefaultLinks, setNormalizedDefaultLinks] = useState<
    string[]
  >([]);
  const [deletedLinks, setDeletedLinks] = useState<string[]>([]);

  useEffect(() => {
    try {
      const initialLinks =
        typeof defaultLinks === "string"
          ? JSON.parse(defaultLinks)
          : defaultLinks;
      setNormalizedDefaultLinks(
        Array.isArray(initialLinks) ? initialLinks : []
      );
    } catch {
      setNormalizedDefaultLinks([]);
    }
  }, [defaultLinks]);

  const handleDelete = (url: string) => {
    setDeletedLinks(prev => [...prev, url]);
    setValue(
      "existingAttachments",
      normalizedDefaultLinks.filter(link => link !== url)
    );
  };

  const handleRestore = (url: string) => {
    setDeletedLinks(prev => prev.filter(link => link !== url));
    setValue("existingAttachments", [...getValues("existingAttachments"), url]);
  };

  return (
    <EditorInput name={name} inputTitle={title} details={details}>
      <input type="hidden" name="existingAttachments" />
      {normalizedDefaultLinks.length > 0 && (
        <div className="mb-4">
          <h3 className="mb-3 flex items-center gap-2 text-base font-semibold text-gray-700">
            📁 Existing Attachments
          </h3>
          <ul className="grid max-w-[800px] grid-cols-1 gap-3 p-4 md:max-w-[1000px] md:grid-cols-2">
            {normalizedDefaultLinks.map((url, index) => {
              const decodedUrl = decodeURIComponent(url);
              const fileName =
                decodedUrl.split("/").pop() || `Attachment-${index + 1}`;
              const extension = fileName.split(".").pop()?.toLowerCase() || "";
              const isDeleted = deletedLinks.includes(url);

              return (
                <li
                  key={url}
                  className={`group flex items-center justify-between rounded-xl border px-4 py-3 shadow-sm transition-all ${
                    isDeleted
                      ? "border-gray-200 bg-gray-100"
                      : "border-gray-200 bg-white hover:bg-blue-50"
                  }`}
                >
                  <div className="flex items-center gap-3 overflow-hidden">
                    <div
                      className={`rounded-full p-2 ${
                        isDeleted
                          ? "bg-gray-200 text-gray-500"
                          : "bg-blue-100 text-blue-700"
                      }`}
                    >
                      <FiPaperclip size={18} />
                    </div>
                    <div className="flex flex-col overflow-hidden">
                      <span
                        className={`max-w-lg truncate text-sm font-medium ${
                          isDeleted
                            ? "text-gray-500 line-through"
                            : "text-gray-800"
                        }`}
                      >
                        {fileName}
                      </span>
                      <span className="text-xs uppercase text-gray-500">
                        {extension}
                      </span>
                    </div>
                  </div>
                  {isDeleted ? (
                    <button
                      type="button"
                      onClick={() => handleRestore(url)}
                      className="flex items-center gap-1 text-sm font-medium text-green-600 transition-transform hover:scale-110 hover:text-green-800"
                      title="Restore"
                    >
                      <FiRotateCcw size={16} />
                      <span>Restore</span>
                    </button>
                  ) : (
                    <div className="flex items-center gap-3">
                      <a
                        href={url}
                        target="_blank"
                        rel="noopener noreferrer"
                        download
                        className="text-sm font-medium text-blue-600 transition-transform hover:scale-110 hover:text-blue-800"
                        title="Download"
                      >
                        <FaDownload />
                      </a>
                      <button
                        type="button"
                        onClick={() => handleDelete(url)}
                        className="text-sm font-medium text-red-500 transition-transform hover:scale-110 hover:text-red-700"
                        title="Delete"
                      >
                        <FiTrash2 size={18} />
                      </button>
                    </div>
                  )}
                </li>
              );
            })}
          </ul>
        </div>
      )}
      <Controller
        name={name}
        control={control}
        render={({
          field: { value, onChange, onBlur },
          fieldState: { error }
        }) => (
          <AttachmentsInput
            accept={accept}
            multiple={multiple}
            placeholder={placeholder}
            value={value}
            note={note}
            error={!!error}
            onChange={val => {
              clearErrors(name);
              onChange(val);
            }}
            onBlur={onBlur}
            setError={message => setError(name, { message })}
            cropImage={cropImage}
          />
        )}
      />
    </EditorInput>
  );
};

export default ProgramEditorAttachments;
