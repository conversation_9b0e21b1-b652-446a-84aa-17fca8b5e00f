import React from 'react';
import { Page, View, Text, StyleSheet } from '@react-pdf/renderer';
import { ReportData } from '../../types/report.types';
import { useSectionPages } from '../SectionPageContext';

interface KeyFindingsListPageProps {
  reportData: ReportData;
  sectionId?: string;
}

const styles = StyleSheet.create({
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    fontSize: 10,
    color: 'grey',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  footerLeft: {
    textAlign: 'left',
    flex: 1,
  },
  footerRight: {
    textAlign: 'right',
    flex: 1,
  },
});

const KeyFindingsListPage: React.FC<KeyFindingsListPageProps> = ({ reportData, sectionId }) => {
  const { updateSectionPage } = useSectionPages();
  return (
    <Page size="A4" id={sectionId} style={{
      flexDirection: 'column',
      backgroundColor: '#ffffff',
      padding: '20mm 15mm',
      fontFamily: 'Helvetica',
      fontSize: 12,
    }}>
      <View style={{ flex: 1 }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#2563eb', marginBottom: 16, lineHeight: 1.4 }}>
          KEY FINDINGS
        </Text>
        <Text style={{ fontSize: 12, marginBottom: 12, color: '#374151', lineHeight: 1.4 }}>
          The table below contains a list of the vulnerabilities identified during this assessment:
        </Text>
        <View style={{
          borderRadius: 14,
          overflow: 'hidden',
          marginBottom: 20,
          backgroundColor: '#f8fafc',
          width: '100%',
          alignSelf: 'center',
        }}>
          <View style={{ flexDirection: 'column', width: '100%' }}>
            {/* Table Header */}
            <View style={{ flexDirection: 'row', backgroundColor: '#2563eb', borderTopLeftRadius: 14, borderTopRightRadius: 14, borderBottomWidth: 2, borderBottomColor: '#1e293b' }}>
              <Text style={{ padding: 12, fontWeight: 'bold', width: '10%', textAlign: 'center', color: '#fff', fontSize: 12, letterSpacing: 1, lineHeight: 1.4 }}>SL No.</Text>
              <Text style={{ padding: 12, fontWeight: 'bold', width: '15%', color: '#fff', fontSize: 12, letterSpacing: 1, lineHeight: 1.4 }}>Abbreviation</Text>
              <Text style={{ padding: 12, fontWeight: 'bold', width: '35%', color: '#fff', fontSize: 12, letterSpacing: 1, lineHeight: 1.4 }}>Finding Title</Text>
              <Text style={{ padding: 12, fontWeight: 'bold', width: '20%', color: '#fff', fontSize: 12, letterSpacing: 1, lineHeight: 1.4 }}>Severity</Text>
              <Text style={{ padding: 12, fontWeight: 'bold', width: '20%', color: '#fff', fontSize: 12, letterSpacing: 1, lineHeight: 1.4 }}>Status</Text>
            </View>
            {/* Table Rows */}
            {reportData.reports_list && [...reportData.reports_list]
              .sort((a, b) => {
                const sevOrder: Record<string, number> = { critical: 1, high: 2, medium: 3, low: 4 };
                const aSev = (a.severity_category || '').toLowerCase();
                const bSev = (b.severity_category || '').toLowerCase();
                return (sevOrder[aSev] || 99) - (sevOrder[bSev] || 99);
              })
              .map((finding, idx) => {
                // Generate default abbreviation if missing
                const severityCounts: Record<string, number> = {};
                const sortedList = [...reportData.reports_list]
                  .sort((a, b) => {
                    const sevOrder: Record<string, number> = { critical: 1, high: 2, medium: 3, low: 4 };
                    const aSev = (a.severity_category || '').toLowerCase();
                    const bSev = (b.severity_category || '').toLowerCase();
                    return (sevOrder[aSev] || 99) - (sevOrder[bSev] || 99);
                  });
                sortedList.slice(0, idx + 1).forEach((f) => {
                  const sev = (f.severity_category || '').toLowerCase();
                  severityCounts[sev] = (severityCounts[sev] || 0) + 1;
                });
                const sev = (finding.severity_category || '').toLowerCase();
                let abbrPrefix = '';
                if (sev === 'critical') abbrPrefix = 'C';
                else if (sev === 'high') abbrPrefix = 'H';
                else if (sev === 'medium') abbrPrefix = 'M';
                else if (sev === 'low') abbrPrefix = 'L';
                else abbrPrefix = 'X';
                const defaultAbbr = abbrPrefix + severityCounts[sev];
                const abbr = finding.abbreviation && finding.abbreviation.trim() ? finding.abbreviation : defaultAbbr;
                return (
                  <View key={idx} style={{
                    flexDirection: 'row',
                    backgroundColor: idx % 2 === 0 ? '#fff' : '#f1f5f9',
                    borderTopWidth: idx === 0 ? 0 : 1,
                    borderTopColor: '#e5e7eb',
                    alignItems: 'center',
                  }}>
                    <Text style={{ padding: 10, width: '10%', textAlign: 'center', fontWeight: 'bold', color: '#2563eb', fontSize: 12, lineHeight: 1.4 }}>{String(idx + 1).padStart(2, '0')}</Text>
                    <Text style={{ padding: 10, width: '15%', fontWeight: 'bold', color: '#2563eb', fontSize: 12, lineHeight: 1.4 }}>{abbr}</Text>
                    <Text style={{ padding: 10, width: '35%', fontSize: 12, lineHeight: 1.4 }}>{finding.title}</Text>
                    <View style={{ padding: 10, width: '20%' }}>
                      {(() => {
                        const sev = (finding.severity_category || '').toLowerCase();
                        let bgColor = '#64748b';
                        if (sev === 'critical') bgColor = '#B91C1C';
                        else if (sev === 'high') bgColor = '#F59E42';
                        else if (sev === 'medium') bgColor = '#FACC15';
                        else if (sev === 'low') bgColor = '#22C55E';
                        return (
                          <View style={{
                            backgroundColor: bgColor,
                            borderRadius: 12,
                            paddingHorizontal: 10,
                            paddingVertical: 4,
                            alignSelf: 'flex-start',
                          }}>
                            <Text style={{
                              fontWeight: 'bold',
                              color: '#fff',
                              fontSize: 11,
                              letterSpacing: 1,
                              textTransform: 'capitalize',
                              lineHeight: 1.4,
                            }}>{sev}</Text>
                          </View>
                        );
                      })()}
                    </View>
                    <View style={{ padding: 10, width: '20%' }}>
                      <View style={{
                        backgroundColor:
                          finding.status === 'Open' ? '#fee2e2' :
                          finding.status === 'Closed' ? '#dcfce7' : '#f3f4f6',
                        borderRadius: 12,
                        paddingHorizontal: 10,
                        paddingVertical: 4,
                        alignSelf: 'flex-start',
                      }}>
                        <Text style={{
                          color:
                            finding.status === 'Open' ? '#dc2626' :
                            finding.status === 'Closed' ? '#16a34a' : '#374151',
                          fontWeight: 'bold',
                          fontSize: 11,
                          letterSpacing: 1,
                          lineHeight: 1.4,
                        }}>{finding.status}</Text>
                      </View>
                    </View>
                  </View>
                );
              })}
          </View>
        </View>
        <Text style={{ fontSize: 12, color: '#64748b', textAlign: 'center', marginTop: 8, lineHeight: 1.4 }}>
          Table: Summary of Key Findings
        </Text>
      </View>
      <View style={styles.footer} fixed>
        <Text style={styles.footerLeft}>{reportData.document_number || 'Document Number'}</Text>
        <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
      </View>
      <Text style={{ display: 'none' }} render={({ pageNumber }) => { updateSectionPage('KeyFindingsList', pageNumber); return ''; }} fixed />
    </Page>
  );
};

export default KeyFindingsListPage; 