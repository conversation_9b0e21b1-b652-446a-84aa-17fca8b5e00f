import React, { useState } from 'react';
import { ReportData } from '../../types/report.types';

interface KeyFindingsEditorProps {
  reportData: ReportData;
  onFindingChange: (index: number, field: string, value: string) => void;
  onAddFinding: () => void;
  onRemoveFinding: (index: number) => void;
}

type SeverityCategory = 'Critical' | 'High' | 'Medium' | 'Low';
type FindingStatus = 'Open' | 'Closed' | 'In Progress';

const KeyFindingsEditor: React.FC<KeyFindingsEditorProps> = ({
  reportData,
  onFindingChange,
  onAddFinding,
  onRemoveFinding 
}) => {
  const severityColors: Record<SeverityCategory, string> = {
    Critical: 'bg-red-700 text-white',
    High: 'bg-red-500 text-white',
    Medium: 'bg-orange-500 text-white',
    Low: 'bg-blue-500 text-white'
  };

  const statusColors: Record<FindingStatus, string> = {
    Open: 'bg-red-100 text-red-800',
    Closed: 'bg-green-100 text-green-800',
    'In Progress': 'bg-yellow-100 text-yellow-800'
  };

  const handleSave = (index: number, field: string, value: string) => {
    onFindingChange(index, field, value);
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header Section */}
      <div className="bg-white rounded-xl shadow-lg border border-slate-100 p-6 mb-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-1 h-8 bg-gradient-to-b from-blue-500 to-blue-700 rounded-full" />
          <div className="flex-1">
            <h2 className="text-base font-bold text-blue-900 tracking-tight">Key Findings</h2>
            <p className="text-xs text-blue-700/80 font-medium mt-0.5">Manage and track identified vulnerabilities</p>
          </div>
          <button
            onClick={onAddFinding}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors shadow-sm"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
            </svg>
            Add New Finding
          </button>
        </div>

        {/* Findings Cards */}
        <div className="space-y-5">
          {reportData.reports_list && [...reportData.reports_list]
            .sort((a, b) => {
              const sevOrder: Record<string, number> = { critical: 1, high: 2, medium: 3, low: 4 };
              const aSev = (a.severity_category || '').toLowerCase();
              const bSev = (b.severity_category || '').toLowerCase();
              return (sevOrder[aSev] || 99) - (sevOrder[bSev] || 99);
            })
            .map((finding, index) => {
              // Count the number of previous findings of the same severity
              const severityCounts: Record<string, number> = {};
              const sortedList = [...reportData.reports_list]
                .sort((a, b) => {
                  const sevOrder: Record<string, number> = { critical: 1, high: 2, medium: 3, low: 4 };
                  const aSev = (a.severity_category || '').toLowerCase();
                  const bSev = (b.severity_category || '').toLowerCase();
                  return (sevOrder[aSev] || 99) - (sevOrder[bSev] || 99);
                });
              sortedList.slice(0, index + 1).forEach((f) => {
                const sev = (f.severity_category || '').toLowerCase();
                severityCounts[sev] = (severityCounts[sev] || 0) + 1;
              });
              const sev = (finding.severity_category || '').toLowerCase();
              let abbrPrefix = '';
              if (sev === 'critical') abbrPrefix = 'C';
              else if (sev === 'high') abbrPrefix = 'H';
              else if (sev === 'medium') abbrPrefix = 'M';
              else if (sev === 'low') abbrPrefix = 'L';
              else abbrPrefix = 'X';
              const defaultAbbr = abbrPrefix + severityCounts[sev];
              return (
                <div key={index} className="bg-white border border-slate-200 rounded-lg shadow-sm flex flex-col">
                  {/* Card Header */}
                  <div className="flex items-center justify-between px-4 py-2 border-b border-slate-100 bg-slate-50 rounded-t-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-1 h-6 bg-gradient-to-b from-blue-400 to-blue-700 rounded-full" />
                      <span className="text-sm font-semibold text-slate-800">Finding #{String(index + 1).padStart(2, '0')}</span>
                      <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${statusColors[finding.status as FindingStatus]}`}>{finding.status}</span>
                    </div>
                    <button
                      onClick={() => onRemoveFinding(index)}
                      className="inline-flex items-center px-2 py-1 text-red-600 hover:text-red-900 focus:outline-none focus:underline text-xs"
                    >
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                      Delete
                    </button>
                  </div>

                  {/* Card Content */}
                  <div className="p-4 space-y-3">
                    {/* Abbreviation Field (always show, fallback to generated if blank) */}
                    <div className="grid grid-cols-3 gap-3 items-center">
                      <label className="text-xs font-medium text-slate-700">Abbreviation</label>
                      <div className="col-span-2">
                        <input
                          type="text"
                          value={finding.abbreviation && finding.abbreviation.trim() !== '' ? finding.abbreviation : defaultAbbr}
                          onChange={(e) => handleSave(index, 'abbreviation', e.target.value)}
                          className="w-full px-3 py-2 text-xs border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 bg-slate-50"
                          placeholder={`e.g., ${defaultAbbr}`}
                        />
                      </div>
                    </div>

                    {/* Title Field */}
                    <div className="grid grid-cols-3 gap-3 items-center">
                      <label className="text-xs font-medium text-slate-700">Finding Title</label>
                      <div className="col-span-2">
                        <input
                          type="text"
                          value={finding.title}
                          onChange={(e) => handleSave(index, 'title', e.target.value)}
                          className="w-full px-3 py-2 text-xs border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 bg-slate-50"
                          placeholder="Enter finding title"
                        />
                      </div>
                    </div>

                    {/* Severity Field (disabled) */}
                    <div className="grid grid-cols-3 gap-3 items-center">
                      <label className="text-xs font-medium text-slate-700">Severity</label>
                      <div className="col-span-2">
                        <input
                          type="text"
                          value={finding.severity_category}
                          disabled
                          className="w-full px-3 py-2 text-xs border border-slate-200 rounded-md bg-gray-100 text-gray-700 cursor-not-allowed"
                        />
                      </div>
                    </div>

                    {/* Status Field */}
                    <div className="grid grid-cols-3 gap-3 items-center">
                      <label className="text-xs font-medium text-slate-700">Status</label>
                      <div className="col-span-2">
                        <select
                          value={finding.status}
                          onChange={(e) => handleSave(index, 'status', e.target.value)}
                          className="w-full px-3 py-2 text-xs border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 bg-white"
                        >
                          {Object.keys(statusColors).map((status) => (
                            <option key={status} value={status}>
                              {status}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}

          {/* Empty State */}
          {(!reportData.reports_list || reportData.reports_list.length === 0) && (
            <div className="text-center py-16 bg-white rounded-lg border border-slate-200">
              <svg className="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="mt-4 text-base font-medium text-slate-900">No findings added yet</h3>
              <p className="mt-2 text-xs text-slate-500">Get started by adding your first finding.</p>
              <div className="mt-6">
                <button
                  onClick={onAddFinding}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-xs font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg className="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                  </svg>
                  Add Finding
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Tips Section */}
      <div className="bg-white rounded-xl shadow-lg border border-slate-100 p-6 mt-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-1 h-8 bg-gradient-to-b from-blue-500 to-blue-700 rounded-full" />
          <h3 className="text-base font-bold text-blue-900 tracking-tight">Best Practices</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0">
              <div className="p-2 bg-blue-50 rounded-lg">
                <svg className="h-5 w-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div>
              <h4 className="text-xs font-medium text-slate-900">Clear Titles</h4>
              <p className="mt-1 text-xs text-slate-500">Use clear and concise titles that accurately describe the vulnerability</p>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0">
              <div className="p-2 bg-blue-50 rounded-lg">
                <svg className="h-5 w-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div>
              <h4 className="text-xs font-medium text-slate-900">Consistent Abbreviations</h4>
              <p className="mt-1 text-xs text-slate-500">Keep abbreviations consistent and meaningful for easy reference</p>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0">
              <div className="p-2 bg-blue-50 rounded-lg">
                <svg className="h-5 w-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div>
              <h4 className="text-xs font-medium text-slate-900">Status Updates</h4>
              <p className="mt-1 text-xs text-slate-500">Regularly update the status to reflect the current state of findings</p>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0">
              <div className="p-2 bg-blue-50 rounded-lg">
                <svg className="h-5 w-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div>
              <h4 className="text-xs font-medium text-slate-900">Severity Levels</h4>
              <p className="mt-1 text-xs text-slate-500">Ensure findings are properly categorized by severity level</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default KeyFindingsEditor; 