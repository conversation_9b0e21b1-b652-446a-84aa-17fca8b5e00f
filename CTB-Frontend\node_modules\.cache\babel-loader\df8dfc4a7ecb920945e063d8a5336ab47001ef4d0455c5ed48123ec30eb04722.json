{"ast": null, "code": "var _s = $RefreshSig$();\n// usePDFEditorLogic.ts - Custom hook for all PDFEditor state, effects, and handlers\nimport { useState, useEffect, useCallback, useMemo, useRef } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { getProgramReportById, getProgramReportChangeLog, updateReportStatus, updateProgramReport } from '../../../utils/api/endpoints/program-reports/program-reports';\nimport axios from '../../../utils/api/axios';\nimport useUserCredentials from '../../../utils/hooks/user/useUserCredentials';\nimport { useReactToPrint } from 'react-to-print';\n// Constants\nconst PREVIEW_UPDATE_DELAY = 500;\nconst SEVERITY_CATEGORIES = ['Critical', 'High', 'Medium', 'Low'];\nconst STATUS_TYPES = ['Open', 'Closed', 'Total'];\nconst MIN_EDITOR_WIDTH = 320;\nconst MAX_EDITOR_WIDTH = 700;\nconst DEFAULT_EDITOR_WIDTH = '50%';\n\n// Utility functions (copy from PDFEditor)\nconst normalizeSeverityCategory = category => {\n  const normalized = category.charAt(0).toUpperCase() + category.slice(1).toLowerCase();\n  return normalized;\n};\nconst getDefaultDisclaimer = companyName => `\n  <p>\n    Capture The Bug Ltd. has prepared this document exclusively for ${companyName}.\n    Copying, or modification of this document is strictly prohibited without Capture The Bug Ltd.'s written consent, \n    except for specific purposes when such permission is granted. This document is confidential and proprietary material \n    of Capture The Bug Ltd. and must always be treated as such, not to be disclosed to third parties without prior consent.\n  </p>\n  \n  <p>\n    The information within is provided 'as-is,' without any warranty. Vulnerability assessments offer a snapshot of \n    security at a specific moment and are not exhaustive risk assessments. New vulnerabilities may emerge post-assessment. \n    Thus, this report is a guide, not a definitive risk analysis.\n  </p>\n  \n  <p>\n    Capture The Bug Ltd. assumes no liability for any changes, omissions, or errors in this document. \n    Capture The Bug Ltd. shall not be liable for any damages, financial or otherwise, arising out of the use or misuse \n    of this report by any current employee of ${companyName} or any member of the general public.\n  </p>\n`;\nconst getDefaultOpenCloseCounts = () => ({\n  Critical: {\n    Open: 0,\n    Closed: 0,\n    Total: 0\n  },\n  High: {\n    Open: 0,\n    Closed: 0,\n    Total: 0\n  },\n  Medium: {\n    Open: 0,\n    Closed: 0,\n    Total: 0\n  },\n  Low: {\n    Open: 0,\n    Closed: 0,\n    Total: 0\n  }\n});\nexport function usePDFEditorLogic() {\n  _s();\n  const {\n    report_id\n  } = useParams();\n  const {\n    role\n  } = useUserCredentials();\n\n  // State\n  const [loading, setLoading] = useState(true);\n  const [previewLoading, setPreviewLoading] = useState(false);\n  const [previewHtml, setPreviewHtml] = useState('');\n  const [reportData, setReportData] = useState(null);\n  const [activeSection, setActiveSection] = useState('cover');\n  const [sectionData, setSectionData] = useState({});\n  const [statusUpdateLoading, setStatusUpdateLoading] = useState(false);\n  const [notification, setNotification] = useState({\n    type: 'info',\n    title: '',\n    message: '',\n    isVisible: false\n  });\n  const [isFullViewOpen, setIsFullViewOpen] = useState(false);\n  const [isChangeLogOpen, setIsChangeLogOpen] = useState(false);\n  const [changeLog, setChangeLog] = useState([]);\n  const [changeLogLoading, setChangeLogLoading] = useState(false);\n  const [previewMode, setPreviewMode] = useState('full');\n  const [editorWidth, setEditorWidth] = useState(DEFAULT_EDITOR_WIDTH);\n  const dragging = useRef(false);\n  const [viewMode, setViewMode] = useState(false);\n  const previewRef = useRef(null);\n  const [currentData, setCurrentData] = useState(null);\n  const [saveLoading, setSaveLoading] = useState(false);\n  const [approveLoading, setApproveLoading] = useState(false);\n  const [rejectLoading, setRejectLoading] = useState(false);\n\n  // Notification helpers\n  const showNotification = useCallback((type, title, message) => {\n    setNotification({\n      type,\n      title,\n      message,\n      isVisible: true\n    });\n  }, []);\n  const hideNotification = useCallback(() => {\n    setNotification(prev => ({\n      ...prev,\n      isVisible: false\n    }));\n  }, []);\n\n  // Full view modal\n  const openFullView = useCallback(() => setIsFullViewOpen(true), []);\n  const closeFullView = useCallback(() => setIsFullViewOpen(false), []);\n\n  // Change log\n  const openChangeLog = useCallback(async () => {\n    if (!report_id) return;\n    setIsChangeLogOpen(true);\n    setChangeLogLoading(true);\n    try {\n      const res = await getProgramReportChangeLog(report_id);\n      if (res.success) {\n        setChangeLog(res.data);\n      } else {\n        setChangeLog([]);\n      }\n    } catch (e) {\n      setChangeLog([]);\n    } finally {\n      setChangeLogLoading(false);\n    }\n  }, [report_id]);\n  const closeChangeLog = useCallback(() => setIsChangeLogOpen(false), []);\n\n  // Preview update\n  const updatePreview = useCallback(async data => {\n    if (!report_id) return;\n    try {\n      setPreviewLoading(true);\n      const response = await axios.post(`/v2/program-reports/${report_id}/preview`, {\n        reportData: data\n      }, {\n        headers: {\n          'Accept': 'text/html'\n        },\n        responseType: 'text'\n      });\n      setPreviewHtml(response.data);\n    } catch (error) {\n      // eslint-disable-next-line no-console\n      console.error('Error updating preview:', error);\n    } finally {\n      setPreviewLoading(false);\n    }\n  }, [report_id]);\n  const debouncedUpdatePreview = useMemo(() => {\n    let timeoutId;\n    return data => {\n      clearTimeout(timeoutId);\n      timeoutId = setTimeout(() => updatePreview(data), PREVIEW_UPDATE_DELAY);\n    };\n  }, [updatePreview]);\n\n  // Data update\n  const handleDataUpdate = useCallback(updates => {\n    if (!reportData) return;\n    let updatedData = {\n      ...reportData,\n      ...updates\n    };\n    setReportData(updatedData);\n    setCurrentData(updatedData); // <-- ensure currentData is always up to date\n    setSectionData(prev => ({\n      ...prev,\n      ...updates\n    }));\n    debouncedUpdatePreview(updatedData);\n  }, [reportData, debouncedUpdatePreview]);\n\n  // Data fetching\n  const fetchReportData = useCallback(async () => {\n    if (!report_id) return;\n    try {\n      setLoading(true);\n      const response = await getProgramReportById(report_id);\n      if (response.status === 'success') {\n        let data = response.data;\n        // Robustly handle methodology as stringified JSON or object\n        if (typeof data.methodology === 'string') {\n          try {\n            // Try to parse as JSON\n            const parsed = JSON.parse(data.methodology);\n            if (typeof parsed === 'object' && parsed !== null) {\n              data.methodology = parsed;\n            } else {\n              // Fallback: old CSV logic\n              data.methodology = data.methodology.split(',').reduce((acc, key) => {\n                acc[key.trim()] = true;\n                return acc;\n              }, {});\n            }\n          } catch (e) {\n            // Fallback: old CSV logic\n            data.methodology = data.methodology.split(',').reduce((acc, key) => {\n              acc[key.trim()] = true;\n              return acc;\n            }, {});\n          }\n        }\n        // Patch: Ensure methodology default is always set if missing or empty\n        if (!data.methodology || typeof data.methodology === 'object' && !data.methodology.web && !data.methodology.network && !data.methodology.mobile) {\n          data.methodology = {\n            web: true,\n            network: false,\n            mobile: false\n          };\n        }\n        setReportData(data);\n        await updatePreview(data);\n      }\n    } catch (error) {\n      // eslint-disable-next-line no-console\n      console.error('Error fetching report data:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, [report_id, updatePreview]);\n  useEffect(() => {\n    if (report_id) {\n      fetchReportData();\n    }\n  }, [report_id, fetchReportData]);\n  useEffect(() => {\n    setCurrentData(reportData);\n  }, [reportData]);\n  useEffect(() => {\n    // If reportData is loaded and methodology.web is true, ensure preview is up to date\n    if (reportData && reportData.methodology && reportData.methodology.web) {\n      updatePreview(reportData);\n    }\n    // Only run when reportData changes\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [reportData]);\n  useEffect(() => {\n    // Patch: Ensure methodology default is always set if missing or empty for new reports (no report_id)\n    if (!report_id && reportData) {\n      if (!reportData.methodology || typeof reportData.methodology === 'object' && !reportData.methodology.web && !reportData.methodology.network && !reportData.methodology.mobile) {\n        const patched = {\n          ...reportData,\n          methodology: {\n            web: true,\n            network: false,\n            mobile: false\n          }\n        };\n        setReportData(patched);\n        setCurrentData(patched);\n        debouncedUpdatePreview(patched);\n      }\n    }\n  }, [report_id, reportData, debouncedUpdatePreview]);\n\n  // Status update\n  const updateReportStatusHandler = useCallback(async newStatus => {\n    if (!report_id) return;\n    try {\n      setStatusUpdateLoading(true);\n      const response = await updateReportStatus(report_id, newStatus);\n      if (response.success) {\n        setReportData(prev => prev ? {\n          ...prev,\n          status: newStatus\n        } : null);\n        showNotification('success', 'Report Status Updated', `Report status updated to ${newStatus}`);\n      } else {\n        showNotification('error', 'Report Status Update Error', 'Failed to update report status. Please try again.');\n      }\n    } catch (error) {\n      // eslint-disable-next-line no-console\n      console.error('Error updating report status:', error);\n      showNotification('error', 'Report Status Update Error', 'Error updating report status. Please try again.');\n    } finally {\n      setStatusUpdateLoading(false);\n    }\n  }, [report_id, showNotification]);\n\n  // Save\n  const handleSave = useCallback(async () => {\n    if (!reportData) return;\n    try {\n      setSaveLoading(true);\n      const updatedData = {\n        ...reportData,\n        ...sectionData\n      };\n      const response = await updateProgramReport(report_id, updatedData);\n\n      // Use the response data from backend to ensure we have the latest state\n      const savedData = response.success ? response.data : updatedData;\n      setReportData(savedData);\n      setCurrentData(savedData);\n      setSectionData({});\n      await updatePreview(savedData);\n      showNotification('success', 'Report Saved', 'Report saved successfully!');\n    } catch (error) {\n      // eslint-disable-next-line no-console\n      console.error('Error saving report:', error);\n      showNotification('error', 'Report Save Error', 'Error saving report. Please try again.');\n    } finally {\n      setSaveLoading(false);\n    }\n  }, [report_id, reportData, sectionData, updatePreview, showNotification]);\n\n  // Approve\n  const handleApprove = useCallback(async () => {\n    if (!reportData) return;\n    try {\n      setApproveLoading(true);\n      // Always save first, even if there are no unsaved changes\n      const updatedData = {\n        ...reportData,\n        ...sectionData\n      };\n      // Step 1: Save the report data\n      const response = await updateProgramReport(report_id, updatedData);\n      const savedData = response.success ? response.data : updatedData;\n      setReportData(savedData);\n      await updatePreview(savedData);\n      setSectionData({});\n      // Step 2: Determine the new status based on role and current status\n      let newStatus = null;\n      let roleStr = '';\n      if (typeof role === 'number') {\n        roleStr = {\n          1: 'RESEARCHER',\n          2: 'BUSINESS',\n          3: 'ADMIN',\n          4: 'QA',\n          5: 'ADMIN_MANAGER',\n          6: 'DEVELOPER',\n          7: 'BUSINESS_MANAGER',\n          8: 'BUSINESS_ADMINISTRATOR',\n          9: 'SUB_ADMIN'\n        }[role] || '';\n      } else if (typeof role === 'string') {\n        roleStr = role;\n      }\n      roleStr = roleStr.toUpperCase();\n      const statusStr = (reportData.status || '').toLowerCase();\n      if (roleStr === 'QA') {\n        if (statusStr === 'draft' || statusStr === 'qa_review') {\n          newStatus = 'admin_review';\n        } else if (statusStr === 'business_requested_changes') {\n          newStatus = 'changes_added';\n        }\n      } else if (['ADMIN', 'SUB_ADMIN', 'ADMIN_MANAGER'].includes(roleStr)) {\n        if (statusStr === 'admin_review' || statusStr === 'business_requested_changes') {\n          newStatus = 'approved';\n        } else if (statusStr === 'changes_added') {\n          newStatus = 'report_updated';\n        }\n      }\n      // Step 3: Update the status only after successful save\n      if (newStatus) {\n        await updateReportStatusHandler(newStatus);\n      }\n      showNotification('success', 'Report Approved', 'Report saved and approved successfully!');\n    } catch (error) {\n      // eslint-disable-next-line no-console\n      console.error('Error approving report:', error);\n      showNotification('error', 'Report Approval Error', 'Error saving or approving report. Please try again.');\n    } finally {\n      setApproveLoading(false);\n    }\n  }, [report_id, reportData, sectionData, updatePreview, updateReportStatusHandler, role, showNotification]);\n\n  // Reject\n  const handleReject = useCallback(async () => {\n    if (!reportData) return;\n    try {\n      setRejectLoading(true);\n      const updatedData = {\n        ...reportData,\n        ...sectionData\n      };\n      const response = await updateProgramReport(report_id, updatedData);\n      const savedData = response.success ? response.data : updatedData;\n      setReportData(savedData);\n      await updatePreview(savedData);\n      setSectionData({});\n      await updateReportStatusHandler('draft');\n      showNotification('success', 'Report Rejected', 'Report rejected and sent back to QA for editing!');\n    } catch (error) {\n      // eslint-disable-next-line no-console\n      console.error('Error rejecting report:', error);\n      showNotification('error', 'Report Rejection Error', 'Error rejecting report. Please try again.');\n    } finally {\n      setRejectLoading(false);\n    }\n  }, [report_id, reportData, sectionData, updatePreview, updateReportStatusHandler, showNotification]);\n\n  // Permissions\n  const canApprove = useMemo(() => {\n    if (!reportData) return false;\n    let roleStr = '';\n    if (typeof role === 'number') {\n      roleStr = {\n        1: 'RESEARCHER',\n        2: 'BUSINESS',\n        3: 'ADMIN',\n        4: 'QA',\n        5: 'ADMIN_MANAGER',\n        6: 'DEVELOPER',\n        7: 'BUSINESS_MANAGER',\n        8: 'BUSINESS_ADMINISTRATOR',\n        9: 'SUB_ADMIN'\n      }[role] || '';\n    } else if (typeof role === 'string') {\n      roleStr = role;\n    }\n    roleStr = roleStr.toUpperCase();\n    const statusStr = (reportData.status || '').toLowerCase();\n    if (roleStr === 'QA') {\n      return statusStr === 'draft' || statusStr === 'qa_review' || statusStr === 'business_requested_changes';\n    }\n    if (['ADMIN', 'SUB_ADMIN', 'ADMIN_MANAGER'].includes(roleStr)) {\n      return statusStr === 'admin_review' || statusStr === 'changes_added' || statusStr === 'business_requested_changes';\n    }\n    return false;\n  }, [role, reportData]);\n  const canReject = useMemo(() => {\n    if (!reportData) return false;\n    let roleStr = '';\n    if (typeof role === 'number') {\n      roleStr = {\n        1: 'RESEARCHER',\n        2: 'BUSINESS',\n        3: 'ADMIN',\n        4: 'QA',\n        5: 'ADMIN_MANAGER',\n        6: 'DEVELOPER',\n        7: 'BUSINESS_MANAGER',\n        8: 'BUSINESS_ADMINISTRATOR',\n        9: 'SUB_ADMIN'\n      }[role] || '';\n    } else if (typeof role === 'string') {\n      roleStr = role;\n    }\n    return ['ADMIN', 'SUB_ADMIN', 'ADMIN_MANAGER'].includes(roleStr.toUpperCase()) && reportData.status === 'admin_review';\n  }, [role, reportData]);\n\n  // Section handlers (pass-through)\n  const handleInputChange = useCallback((field, value) => {\n    handleDataUpdate({\n      [field]: value\n    });\n  }, [handleDataUpdate]);\n  const handleHtmlChange = useCallback((field, value) => {\n    handleDataUpdate({\n      [field]: value\n    });\n  }, [handleDataUpdate]);\n  const handleDataChange = useCallback((field, value) => {\n    handleDataUpdate({\n      [field]: value\n    });\n  }, [handleDataUpdate]);\n  const handleFindingChange = useCallback((index, field, value, severity) => {\n    if (!reportData) return;\n    const updatedFindings = [...(reportData.detailed_findings || [])];\n    const severityFindings = updatedFindings.filter(f => f.severity_category === severity);\n    if (index >= 0 && index < severityFindings.length) {\n      const findingToUpdate = severityFindings[index];\n      const findingIndex = updatedFindings.findIndex(f => f === findingToUpdate);\n      if (findingIndex !== -1) {\n        updatedFindings[findingIndex] = {\n          ...updatedFindings[findingIndex],\n          [field]: value\n        };\n        handleDataUpdate({\n          detailed_findings: updatedFindings\n        });\n      }\n    }\n  }, [reportData, handleDataUpdate]);\n  const handleAddFinding = useCallback(severity => {\n    if (!reportData) return;\n    const severityAbbreviations = {\n      Critical: 'C',\n      High: 'H',\n      Medium: 'M',\n      Low: 'L'\n    };\n    // Only count findings of this severity\n    const findingsOfThisSeverity = (reportData.detailed_findings || []).filter(f => (f.severity_category || '').toLowerCase() === severity.toLowerCase());\n    const newCount = findingsOfThisSeverity.length + 1;\n    const abbreviation = `${severityAbbreviations[severity]}${newCount}`;\n    const newFinding = {\n      abbreviation: abbreviation,\n      title: '',\n      severity_category: severity,\n      status: 'Open',\n      scope: '',\n      description: '',\n      instructions: '',\n      impact: '',\n      fix: '',\n      submitted_date: new Date().toISOString()\n    };\n    const updatedFindings = [...(reportData.detailed_findings || []), newFinding];\n    handleDataUpdate({\n      detailed_findings: updatedFindings\n    });\n  }, [reportData, handleDataUpdate]);\n  const handleRemoveFinding = useCallback((index, severity) => {\n    if (!reportData) return;\n    const updatedFindings = [...(reportData.detailed_findings || [])];\n    const severityFindings = updatedFindings.filter(f => f.severity_category === severity);\n    if (index >= 0 && index < severityFindings.length) {\n      const findingToRemove = severityFindings[index];\n      const findingIndex = updatedFindings.findIndex(f => f === findingToRemove);\n      if (findingIndex !== -1) {\n        updatedFindings.splice(findingIndex, 1);\n        handleDataUpdate({\n          detailed_findings: updatedFindings\n        });\n      }\n    }\n  }, [reportData, handleDataUpdate]);\n  const handleTargetDetailsChange = useCallback((index, field, value) => {\n    if (!reportData) return;\n    const newTargets = (reportData.target_details || []).map((target, i) => i === index ? {\n      ...target,\n      [field]: value\n    } : target);\n    handleDataUpdate({\n      target_details: newTargets\n    });\n  }, [reportData, handleDataUpdate]);\n  const handleAddTarget = useCallback(() => {\n    if (!reportData) return;\n    const newTargets = [...(reportData.target_details || []), {\n      type: '',\n      url: ''\n    }];\n    handleDataUpdate({\n      target_details: newTargets\n    });\n  }, [reportData, handleDataUpdate]);\n  const handleRemoveTarget = useCallback(index => {\n    if (!reportData) return;\n    const newTargets = (reportData.target_details || []).filter((_, i) => i !== index);\n    handleDataUpdate({\n      target_details: newTargets\n    });\n  }, [reportData, handleDataUpdate]);\n  const handleTableChange = useCallback((field, value) => {\n    // ...table change logic (copy from PDFEditor)\n  }, [reportData, handleDataUpdate]);\n  const handleKeyFindingChange = useCallback((index, field, value) => {\n    if (!reportData) return;\n    const updatedReports = [...(reportData.reports_list || [])];\n    updatedReports[index] = {\n      ...updatedReports[index],\n      [field]: value\n    };\n    handleDataUpdate({\n      reports_list: updatedReports\n    });\n  }, [reportData, handleDataUpdate]);\n  const handleAddKeyFinding = useCallback(() => {\n    if (!reportData) return;\n    const severityAbbreviations = {\n      Critical: 'C',\n      High: 'H',\n      Medium: 'M',\n      Low: 'L'\n    };\n    const defaultSeverity = 'Medium';\n    const severityCounts = {\n      Critical: 0,\n      High: 0,\n      Medium: 0,\n      Low: 0\n    };\n    (reportData.reports_list || []).forEach(finding => {\n      if (finding.severity_category && finding.severity_category in severityCounts) {\n        severityCounts[finding.severity_category]++;\n      }\n    });\n    const newCount = severityCounts[defaultSeverity] + 1;\n    const abbreviation = `${severityAbbreviations[defaultSeverity]}${newCount}`;\n    const newFinding = {\n      abbreviation: abbreviation,\n      title: 'New Finding',\n      severity_category: defaultSeverity,\n      status: 'Open'\n    };\n    const updatedReports = [...(reportData.reports_list || []), newFinding];\n    handleDataUpdate({\n      reports_list: updatedReports\n    });\n  }, [reportData, handleDataUpdate]);\n  const handleRemoveKeyFinding = useCallback(index => {\n    var _reportData$reports_l;\n    if (!reportData) return;\n    const updatedReports = (_reportData$reports_l = reportData.reports_list) === null || _reportData$reports_l === void 0 ? void 0 : _reportData$reports_l.filter((_, i) => i !== index);\n    handleDataUpdate({\n      reports_list: updatedReports\n    });\n  }, [reportData, handleDataUpdate]);\n  const handleRecommendationsChange = useCallback(recommendations => {\n    setSectionData(prev => ({\n      ...prev,\n      recommendations_list: recommendations\n    }));\n  }, []);\n  const saveSectionChanges = useCallback(async () => {\n    if (!report_id || !reportData) return;\n    try {\n      const updatedData = {\n        ...reportData,\n        ...sectionData\n      };\n      const response = await updateProgramReport(report_id, updatedData);\n      const savedData = response.success ? response.data : updatedData;\n      setReportData(savedData);\n      await updatePreview(savedData);\n      setSectionData({});\n      showNotification('success', 'Section Changes Saved', 'Section changes saved successfully!');\n    } catch (error) {\n      // eslint-disable-next-line no-console\n      console.error('Error saving section changes:', error);\n      showNotification('error', 'Section Changes Error', 'Error saving section changes. Please try again.');\n    }\n  }, [report_id, reportData, sectionData, updatePreview, showNotification]);\n\n  // Print handler\n  const handlePrint = useReactToPrint({\n    content: () => previewRef.current,\n    documentTitle: (reportData === null || reportData === void 0 ? void 0 : reportData.report_title) || 'Security Report',\n    removeAfterPrint: true,\n    pageStyle: `\n      @page { size: A4; margin: 0; }\n      body { margin: 0; padding: 0; }\n      .page { page-break-after: always; min-height: 297mm; }\n      .page:last-child { page-break-after: avoid; }\n      .no-break, img, table, .finding-card { break-inside: avoid !important; page-break-inside: avoid !important; }\n      .page-break { break-after: page !important; page-break-after: always !important; }\n    `\n  });\n  return {\n    report_id,\n    role,\n    loading,\n    previewLoading,\n    previewHtml,\n    reportData,\n    activeSection,\n    setActiveSection,\n    sectionData,\n    statusUpdateLoading,\n    notification,\n    setNotification,\n    isFullViewOpen,\n    setIsFullViewOpen,\n    isChangeLogOpen,\n    setIsChangeLogOpen,\n    changeLog,\n    setChangeLog,\n    changeLogLoading,\n    setChangeLogLoading,\n    previewMode,\n    setPreviewMode,\n    editorWidth,\n    setEditorWidth,\n    dragging,\n    viewMode,\n    setViewMode,\n    previewRef,\n    handlePrint,\n    showNotification,\n    hideNotification,\n    openFullView,\n    closeFullView,\n    openChangeLog,\n    closeChangeLog,\n    updatePreview,\n    debouncedUpdatePreview,\n    handleDataUpdate,\n    fetchReportData,\n    updateReportStatus: updateReportStatusHandler,\n    handleSave,\n    handleApprove,\n    handleReject,\n    canApprove,\n    canReject,\n    handleInputChange,\n    handleHtmlChange,\n    handleDataChange,\n    handleFindingChange,\n    handleAddFinding,\n    handleRemoveFinding,\n    handleTargetDetailsChange,\n    handleAddTarget,\n    handleRemoveTarget,\n    handleTableChange,\n    handleKeyFindingChange,\n    handleAddKeyFinding,\n    handleRemoveKeyFinding,\n    handleRecommendationsChange,\n    saveSectionChanges,\n    currentData,\n    saveLoading,\n    approveLoading,\n    rejectLoading\n  };\n}\n_s(usePDFEditorLogic, \"X9pziqml1sYFUsDteetRG2HGQ38=\", false, function () {\n  return [useParams, useUserCredentials, useReactToPrint];\n});", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useMemo", "useRef", "useParams", "getProgramReportById", "getProgramReportChangeLog", "updateReportStatus", "updateProgramReport", "axios", "useUserCredentials", "useReactToPrint", "PREVIEW_UPDATE_DELAY", "SEVERITY_CATEGORIES", "STATUS_TYPES", "MIN_EDITOR_WIDTH", "MAX_EDITOR_WIDTH", "DEFAULT_EDITOR_WIDTH", "normalizeSeverityCategory", "category", "normalized", "char<PERSON>t", "toUpperCase", "slice", "toLowerCase", "getDefaultDisclaimer", "companyName", "getDefaultOpenCloseCounts", "Critical", "Open", "Closed", "Total", "High", "Medium", "Low", "usePDFEditorLogic", "_s", "report_id", "role", "loading", "setLoading", "previewLoading", "setPreviewLoading", "previewHtml", "setPreviewHtml", "reportData", "setReportData", "activeSection", "setActiveSection", "sectionData", "setSectionData", "statusUpdateLoading", "setStatusUpdateLoading", "notification", "setNotification", "type", "title", "message", "isVisible", "isFullViewOpen", "setIsFullViewOpen", "isChangeLogOpen", "setIsChangeLogOpen", "changeLog", "setChangeLog", "changeLogLoading", "setChangeLogLoading", "previewMode", "setPreviewMode", "<PERSON><PERSON><PERSON><PERSON>", "setE<PERSON>orWidth", "dragging", "viewMode", "setViewMode", "previewRef", "currentData", "setCurrentData", "saveLoading", "setSaveLoading", "approveLoading", "setApproveLoading", "rejectLoading", "setRejectLoading", "showNotification", "hideNotification", "prev", "openFullView", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openChangeLog", "res", "success", "data", "e", "closeChangeLog", "updatePreview", "response", "post", "headers", "responseType", "error", "console", "debouncedUpdatePreview", "timeoutId", "clearTimeout", "setTimeout", "handleDataUpdate", "updates", "updatedData", "fetchReportData", "status", "methodology", "parsed", "JSON", "parse", "split", "reduce", "acc", "key", "trim", "web", "network", "mobile", "patched", "updateReportStatusHandler", "newStatus", "handleSave", "savedData", "handleApprove", "roleStr", "statusStr", "includes", "handleReject", "canApprove", "canReject", "handleInputChange", "field", "value", "handleHtmlChange", "handleDataChange", "handleFindingChange", "index", "severity", "updatedFindings", "detailed_findings", "severityF<PERSON>ings", "filter", "f", "severity_category", "length", "findingToUpdate", "findingIndex", "findIndex", "handleAddFinding", "severityAbbreviations", "findingsOfThisSeverity", "newCount", "abbreviation", "newFinding", "scope", "description", "instructions", "impact", "fix", "submitted_date", "Date", "toISOString", "handleRemoveFinding", "finding<PERSON>oRemove", "splice", "handleTargetDetailsChange", "newTargets", "target_details", "map", "target", "i", "handleAddTarget", "url", "handleRemoveTarget", "_", "handleTableChange", "handleKeyFindingChange", "updatedReports", "reports_list", "handleAddKeyFinding", "defaultSeverity", "severityCounts", "for<PERSON>ach", "finding", "handleRemoveKeyFinding", "_reportData$reports_l", "handleRecommendationsChange", "recommendations", "recommendations_list", "saveSectionChanges", "handlePrint", "content", "current", "documentTitle", "report_title", "removeAfterPrint", "pageStyle"], "sources": ["C:/Users/<USER>/Desktop/jira-old/CTB-Frontend/src/pages/automated-report/hooks/usePDFEditorLogic.ts"], "sourcesContent": ["// usePDFEditorLogic.ts - Custom hook for all PDFEditor state, effects, and handlers\r\nimport { useState, useEffect, useCallback, useMemo, useRef } from 'react';\r\nimport { useParams } from 'react-router-dom';\r\nimport { getProgramReportById, getProgramReportChangeLog, updateReportStatus, updateProgramReport } from '../../../utils/api/endpoints/program-reports/program-reports';\r\nimport axios from '../../../utils/api/axios';\r\nimport useUserCredentials from '../../../utils/hooks/user/useUserCredentials';\r\nimport { useReactToPrint } from 'react-to-print';\r\nimport { UserRole } from '../../../utils/api/endpoints/user/credentials';\r\nimport { ReportData, DetailedFinding, Report } from '../types/report.types';\r\nimport { ProgramReportStatus } from '../types/report.types';\r\n\r\n// Constants\r\nconst PREVIEW_UPDATE_DELAY = 500;\r\nconst SEVERITY_CATEGORIES = ['Critical', 'High', 'Medium', 'Low'] as const;\r\nconst STATUS_TYPES = ['Open', 'Closed', 'Total'] as const;\r\nconst MIN_EDITOR_WIDTH = 320;\r\nconst MAX_EDITOR_WIDTH = 700;\r\nconst DEFAULT_EDITOR_WIDTH = '50%';\r\n\r\n// Utility functions (copy from PDFEditor)\r\nconst normalizeSeverityCategory = (category: string) => {\r\n  const normalized = category.charAt(0).toUpperCase() + category.slice(1).toLowerCase();\r\n  return normalized as (typeof SEVERITY_CATEGORIES)[number];\r\n};\r\n\r\nconst getDefaultDisclaimer = (companyName: string) => `\r\n  <p>\r\n    Capture The Bug Ltd. has prepared this document exclusively for ${companyName}.\r\n    Copying, or modification of this document is strictly prohibited without Capture The Bug Ltd.'s written consent, \r\n    except for specific purposes when such permission is granted. This document is confidential and proprietary material \r\n    of Capture The Bug Ltd. and must always be treated as such, not to be disclosed to third parties without prior consent.\r\n  </p>\r\n  \r\n  <p>\r\n    The information within is provided 'as-is,' without any warranty. Vulnerability assessments offer a snapshot of \r\n    security at a specific moment and are not exhaustive risk assessments. New vulnerabilities may emerge post-assessment. \r\n    Thus, this report is a guide, not a definitive risk analysis.\r\n  </p>\r\n  \r\n  <p>\r\n    Capture The Bug Ltd. assumes no liability for any changes, omissions, or errors in this document. \r\n    Capture The Bug Ltd. shall not be liable for any damages, financial or otherwise, arising out of the use or misuse \r\n    of this report by any current employee of ${companyName} or any member of the general public.\r\n  </p>\r\n`;\r\n\r\nconst getDefaultOpenCloseCounts = () => ({\r\n  Critical: { Open: 0, Closed: 0, Total: 0 },\r\n  High: { Open: 0, Closed: 0, Total: 0 },\r\n  Medium: { Open: 0, Closed: 0, Total: 0 },\r\n  Low: { Open: 0, Closed: 0, Total: 0 }\r\n});\r\n\r\ntype NotificationState = { type: string; title: string; message: string; isVisible: boolean };\r\n\r\nexport function usePDFEditorLogic() {\r\n  const { report_id } = useParams<{ report_id: string }>();\r\n  const { role } = useUserCredentials();\r\n\r\n  // State\r\n  const [loading, setLoading] = useState(true);\r\n  const [previewLoading, setPreviewLoading] = useState(false);\r\n  const [previewHtml, setPreviewHtml] = useState<string>('');\r\n  const [reportData, setReportData] = useState<ReportData | null>(null);\r\n  const [activeSection, setActiveSection] = useState<string>('cover');\r\n  const [sectionData, setSectionData] = useState<Partial<ReportData>>({});\r\n  const [statusUpdateLoading, setStatusUpdateLoading] = useState(false);\r\n  const [notification, setNotification] = useState<NotificationState>({ type: 'info', title: '', message: '', isVisible: false });\r\n  const [isFullViewOpen, setIsFullViewOpen] = useState(false);\r\n  const [isChangeLogOpen, setIsChangeLogOpen] = useState(false);\r\n  const [changeLog, setChangeLog] = useState<any[]>([]);\r\n  const [changeLogLoading, setChangeLogLoading] = useState(false);\r\n  const [previewMode, setPreviewMode] = useState<'full' | 'technical' | undefined>('full');\r\n  const [editorWidth, setEditorWidth] = useState<string>(DEFAULT_EDITOR_WIDTH);\r\n  const dragging = useRef(false);\r\n  const [viewMode, setViewMode] = useState(false);\r\n  const previewRef = useRef<HTMLDivElement>(null);\r\n  const [currentData, setCurrentData] = useState<ReportData | null>(null);\r\n  const [saveLoading, setSaveLoading] = useState(false);\r\n  const [approveLoading, setApproveLoading] = useState(false);\r\n  const [rejectLoading, setRejectLoading] = useState(false);\r\n\r\n  // Notification helpers\r\n  const showNotification = useCallback((type: 'success' | 'error' | 'info', title: string, message: string) => {\r\n    setNotification({ type, title, message, isVisible: true });\r\n  }, []);\r\n  const hideNotification = useCallback(() => {\r\n    setNotification((prev: NotificationState) => ({ ...prev, isVisible: false }));\r\n  }, []);\r\n\r\n  // Full view modal\r\n  const openFullView = useCallback(() => setIsFullViewOpen(true), []);\r\n  const closeFullView = useCallback(() => setIsFullViewOpen(false), []);\r\n\r\n  // Change log\r\n  const openChangeLog = useCallback(async () => {\r\n    if (!report_id) return;\r\n    setIsChangeLogOpen(true);\r\n    setChangeLogLoading(true);\r\n    try {\r\n      const res = await getProgramReportChangeLog(report_id);\r\n      if (res.success) {\r\n        setChangeLog(res.data);\r\n      } else {\r\n        setChangeLog([]);\r\n      }\r\n    } catch (e) {\r\n      setChangeLog([]);\r\n    } finally {\r\n      setChangeLogLoading(false);\r\n    }\r\n  }, [report_id]);\r\n  const closeChangeLog = useCallback(() => setIsChangeLogOpen(false), []);\r\n\r\n  // Preview update\r\n  const updatePreview = useCallback(async (data: ReportData) => {\r\n    if (!report_id) return;\r\n    try {\r\n      setPreviewLoading(true);\r\n      const response = await axios.post(\r\n        `/v2/program-reports/${report_id}/preview`,\r\n        { reportData: data },\r\n        { headers: { 'Accept': 'text/html' }, responseType: 'text' }\r\n      );\r\n      setPreviewHtml(response.data);\r\n    } catch (error) {\r\n      // eslint-disable-next-line no-console\r\n      console.error('Error updating preview:', error);\r\n    } finally {\r\n      setPreviewLoading(false);\r\n    }\r\n  }, [report_id]);\r\n\r\n  const debouncedUpdatePreview = useMemo(() => {\r\n    let timeoutId: NodeJS.Timeout;\r\n    return (data: ReportData) => {\r\n      clearTimeout(timeoutId);\r\n      timeoutId = setTimeout(() => updatePreview(data), PREVIEW_UPDATE_DELAY);\r\n    };\r\n  }, [updatePreview]);\r\n\r\n  // Data update\r\n  const handleDataUpdate = useCallback((updates: Partial<ReportData>) => {\r\n    if (!reportData) return;\r\n    let updatedData = { ...reportData, ...updates };\r\n    setReportData(updatedData);\r\n    setCurrentData(updatedData); // <-- ensure currentData is always up to date\r\n    setSectionData(prev => ({ ...prev, ...updates }));\r\n    debouncedUpdatePreview(updatedData);\r\n  }, [reportData, debouncedUpdatePreview]);\r\n\r\n  // Data fetching\r\n  const fetchReportData = useCallback(async () => {\r\n    if (!report_id) return;\r\n    try {\r\n      setLoading(true);\r\n      const response = await getProgramReportById(report_id);\r\n      if (response.status === 'success') {\r\n        let data = response.data;\r\n        // Robustly handle methodology as stringified JSON or object\r\n        if (typeof data.methodology === 'string') {\r\n          try {\r\n            // Try to parse as JSON\r\n            const parsed = JSON.parse(data.methodology);\r\n            if (typeof parsed === 'object' && parsed !== null) {\r\n              data.methodology = parsed;\r\n            } else {\r\n              // Fallback: old CSV logic\r\n              data.methodology = data.methodology.split(',').reduce((acc: any, key: string) => {\r\n                acc[key.trim()] = true;\r\n                return acc;\r\n              }, {});\r\n            }\r\n          } catch (e) {\r\n            // Fallback: old CSV logic\r\n            data.methodology = data.methodology.split(',').reduce((acc: any, key: string) => {\r\n              acc[key.trim()] = true;\r\n              return acc;\r\n            }, {});\r\n          }\r\n        }\r\n        // Patch: Ensure methodology default is always set if missing or empty\r\n        if (\r\n          !data.methodology ||\r\n          (typeof data.methodology === 'object' &&\r\n            !data.methodology.web &&\r\n            !data.methodology.network &&\r\n            !data.methodology.mobile)\r\n        ) {\r\n          data.methodology = { web: true, network: false, mobile: false };\r\n        }\r\n        setReportData(data);\r\n        await updatePreview(data);\r\n      }\r\n    } catch (error) {\r\n      // eslint-disable-next-line no-console\r\n      console.error('Error fetching report data:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [report_id, updatePreview]);\r\n\r\n  useEffect(() => {\r\n    if (report_id) {\r\n      fetchReportData();\r\n    }\r\n  }, [report_id, fetchReportData]);\r\n\r\n  useEffect(() => {\r\n    setCurrentData(reportData);\r\n  }, [reportData]);\r\n\r\n  useEffect(() => {\r\n    // If reportData is loaded and methodology.web is true, ensure preview is up to date\r\n    if (reportData && reportData.methodology && reportData.methodology.web) {\r\n      updatePreview(reportData);\r\n    }\r\n    // Only run when reportData changes\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [reportData]);\r\n\r\n  useEffect(() => {\r\n    // Patch: Ensure methodology default is always set if missing or empty for new reports (no report_id)\r\n    if (!report_id && reportData) {\r\n      if (\r\n        !reportData.methodology ||\r\n        (typeof reportData.methodology === 'object' &&\r\n          !reportData.methodology.web &&\r\n          !reportData.methodology.network &&\r\n          !reportData.methodology.mobile)\r\n      ) {\r\n        const patched = { ...reportData, methodology: { web: true, network: false, mobile: false } };\r\n        setReportData(patched);\r\n        setCurrentData(patched);\r\n        debouncedUpdatePreview(patched);\r\n      }\r\n    }\r\n  }, [report_id, reportData, debouncedUpdatePreview]);\r\n\r\n  // Status update\r\n  const updateReportStatusHandler = useCallback(async (newStatus: ProgramReportStatus) => {\r\n    if (!report_id) return;\r\n    try {\r\n      setStatusUpdateLoading(true);\r\n      const response = await updateReportStatus(report_id, newStatus);\r\n      if (response.success) {\r\n        setReportData(prev => prev ? { ...prev, status: newStatus } : null);\r\n        showNotification('success', 'Report Status Updated', `Report status updated to ${newStatus}`);\r\n      } else {\r\n        showNotification('error', 'Report Status Update Error', 'Failed to update report status. Please try again.');\r\n      }\r\n    } catch (error) {\r\n      // eslint-disable-next-line no-console\r\n      console.error('Error updating report status:', error);\r\n      showNotification('error', 'Report Status Update Error', 'Error updating report status. Please try again.');\r\n    } finally {\r\n      setStatusUpdateLoading(false);\r\n    }\r\n  }, [report_id, showNotification]);\r\n\r\n  // Save\r\n  const handleSave = useCallback(async () => {\r\n    if (!reportData) return;\r\n    try {\r\n      setSaveLoading(true);\r\n      const updatedData = { ...reportData, ...sectionData };\r\n      const response = await updateProgramReport(report_id!, updatedData);\r\n\r\n      // Use the response data from backend to ensure we have the latest state\r\n      const savedData = response.success ? response.data : updatedData;\r\n      setReportData(savedData);\r\n      setCurrentData(savedData);\r\n      setSectionData({});\r\n      await updatePreview(savedData);\r\n      showNotification('success', 'Report Saved', 'Report saved successfully!');\r\n    } catch (error) {\r\n      // eslint-disable-next-line no-console\r\n      console.error('Error saving report:', error);\r\n      showNotification('error', 'Report Save Error', 'Error saving report. Please try again.');\r\n    } finally {\r\n      setSaveLoading(false);\r\n    }\r\n  }, [report_id, reportData, sectionData, updatePreview, showNotification]);\r\n\r\n  // Approve\r\n  const handleApprove = useCallback(async () => {\r\n    if (!reportData) return;\r\n    try {\r\n      setApproveLoading(true);\r\n      // Always save first, even if there are no unsaved changes\r\n      const updatedData = { ...reportData, ...sectionData };\r\n      // Step 1: Save the report data\r\n      const response = await updateProgramReport(report_id!, updatedData);\r\n      const savedData = response.success ? response.data : updatedData;\r\n      setReportData(savedData);\r\n      await updatePreview(savedData);\r\n      setSectionData({});\r\n      // Step 2: Determine the new status based on role and current status\r\n      let newStatus: ProgramReportStatus | null = null;\r\n      let roleStr = '';\r\n      if (typeof role === 'number') {\r\n        roleStr = {\r\n          1: 'RESEARCHER', 2: 'BUSINESS', 3: 'ADMIN', 4: 'QA', 5: 'ADMIN_MANAGER', 6: 'DEVELOPER',\r\n          7: 'BUSINESS_MANAGER',\r\n          8: 'BUSINESS_ADMINISTRATOR',\r\n          9: 'SUB_ADMIN',\r\n        }[role] || '';\r\n      } else if (typeof role === 'string') {\r\n        roleStr = role;\r\n      }\r\n      roleStr = roleStr.toUpperCase();\r\n      const statusStr = (reportData.status || '').toLowerCase();\r\n      if (roleStr === 'QA') {\r\n        if (statusStr === 'draft' || statusStr === 'qa_review') {\r\n          newStatus = 'admin_review';\r\n        } else if (statusStr === 'business_requested_changes') {\r\n          newStatus = 'changes_added';\r\n        }\r\n      } else if ([\r\n        'ADMIN',\r\n        'SUB_ADMIN',\r\n        'ADMIN_MANAGER'\r\n      ].includes(roleStr)) {\r\n        if (statusStr === 'admin_review' || statusStr === 'business_requested_changes') {\r\n          newStatus = 'approved';\r\n        } else if (statusStr === 'changes_added') {\r\n          newStatus = 'report_updated';\r\n        }\r\n      }\r\n      // Step 3: Update the status only after successful save\r\n      if (newStatus) {\r\n        await updateReportStatusHandler(newStatus);\r\n      }\r\n      showNotification('success', 'Report Approved', 'Report saved and approved successfully!');\r\n    } catch (error) {\r\n      // eslint-disable-next-line no-console\r\n      console.error('Error approving report:', error);\r\n      showNotification('error', 'Report Approval Error', 'Error saving or approving report. Please try again.');\r\n    } finally {\r\n      setApproveLoading(false);\r\n    }\r\n  }, [report_id, reportData, sectionData, updatePreview, updateReportStatusHandler, role, showNotification]);\r\n\r\n  // Reject\r\n  const handleReject = useCallback(async () => {\r\n    if (!reportData) return;\r\n    try {\r\n      setRejectLoading(true);\r\n      const updatedData = { ...reportData, ...sectionData };\r\n      const response = await updateProgramReport(report_id!, updatedData);\r\n      const savedData = response.success ? response.data : updatedData;\r\n      setReportData(savedData);\r\n      await updatePreview(savedData);\r\n      setSectionData({});\r\n      await updateReportStatusHandler('draft');\r\n      showNotification('success', 'Report Rejected', 'Report rejected and sent back to QA for editing!');\r\n    } catch (error) {\r\n      // eslint-disable-next-line no-console\r\n      console.error('Error rejecting report:', error);\r\n      showNotification('error', 'Report Rejection Error', 'Error rejecting report. Please try again.');\r\n    } finally {\r\n      setRejectLoading(false);\r\n    }\r\n  }, [report_id, reportData, sectionData, updatePreview, updateReportStatusHandler, showNotification]);\r\n\r\n  // Permissions\r\n  const canApprove = useMemo(() => {\r\n    if (!reportData) return false;\r\n    let roleStr = '';\r\n    if (typeof role === 'number') {\r\n      roleStr = {\r\n        1: 'RESEARCHER', 2: 'BUSINESS', 3: 'ADMIN', 4: 'QA', 5: 'ADMIN_MANAGER', 6: 'DEVELOPER',\r\n        7: 'BUSINESS_MANAGER',\r\n        8: 'BUSINESS_ADMINISTRATOR',\r\n        9: 'SUB_ADMIN',\r\n      }[role] || '';\r\n    } else if (typeof role === 'string') {\r\n      roleStr = role;\r\n    }\r\n    roleStr = roleStr.toUpperCase();\r\n    const statusStr = (reportData.status || '').toLowerCase();\r\n    if (roleStr === 'QA') {\r\n      return statusStr === 'draft' || statusStr === 'qa_review' || statusStr === 'business_requested_changes';\r\n    }\r\n    if ([\r\n      'ADMIN',\r\n      'SUB_ADMIN',\r\n      'ADMIN_MANAGER'\r\n    ].includes(roleStr)) {\r\n      return statusStr === 'admin_review' || statusStr === 'changes_added' || statusStr === 'business_requested_changes';\r\n    }\r\n    return false;\r\n  }, [role, reportData]);\r\n\r\n  const canReject = useMemo(() => {\r\n    if (!reportData) return false;\r\n    let roleStr = '';\r\n    if (typeof role === 'number') {\r\n      roleStr = {\r\n        1: 'RESEARCHER', 2: 'BUSINESS', 3: 'ADMIN', 4: 'QA', 5: 'ADMIN_MANAGER', 6: 'DEVELOPER',\r\n        7: 'BUSINESS_MANAGER',\r\n        8: 'BUSINESS_ADMINISTRATOR',\r\n        9: 'SUB_ADMIN',\r\n      }[role] || '';\r\n    } else if (typeof role === 'string') {\r\n      roleStr = role;\r\n    }\r\n    return [\r\n      'ADMIN',\r\n      'SUB_ADMIN',\r\n      'ADMIN_MANAGER'\r\n    ].includes(roleStr.toUpperCase()) && reportData.status === 'admin_review';\r\n  }, [role, reportData]);\r\n\r\n  // Section handlers (pass-through)\r\n  const handleInputChange = useCallback((field: string, value: string) => {\r\n    handleDataUpdate({ [field]: value });\r\n  }, [handleDataUpdate]);\r\n  const handleHtmlChange = useCallback((field: string, value: string) => {\r\n    handleDataUpdate({ [field]: value });\r\n  }, [handleDataUpdate]);\r\n  const handleDataChange = useCallback((field: string, value: any) => {\r\n    handleDataUpdate({ [field]: value });\r\n  }, [handleDataUpdate]);\r\n  const handleFindingChange = useCallback((index: number, field: string | number | symbol, value: string, severity: string) => {\r\n    if (!reportData) return;\r\n    const updatedFindings = [...(reportData.detailed_findings || [])];\r\n    const severityFindings = updatedFindings.filter(f => f.severity_category === severity);\r\n    if (index >= 0 && index < severityFindings.length) {\r\n      const findingToUpdate = severityFindings[index];\r\n      const findingIndex = updatedFindings.findIndex(f => f === findingToUpdate);\r\n      if (findingIndex !== -1) {\r\n        updatedFindings[findingIndex] = {\r\n          ...updatedFindings[findingIndex],\r\n          [field]: value\r\n        };\r\n        handleDataUpdate({ detailed_findings: updatedFindings });\r\n      }\r\n    }\r\n  }, [reportData, handleDataUpdate]);\r\n  const handleAddFinding = useCallback((severity: string) => {\r\n    if (!reportData) return;\r\n    const severityAbbreviations: Record<string, string> = { Critical: 'C', High: 'H', Medium: 'M', Low: 'L' };\r\n    // Only count findings of this severity\r\n    const findingsOfThisSeverity = (reportData.detailed_findings || []).filter(\r\n      f => (f.severity_category || '').toLowerCase() === severity.toLowerCase()\r\n    );\r\n    const newCount = findingsOfThisSeverity.length + 1;\r\n    const abbreviation = `${severityAbbreviations[severity]}${newCount}`;\r\n    const newFinding: DetailedFinding = {\r\n      abbreviation: abbreviation,\r\n      title: '',\r\n      severity_category: severity as 'Critical' | 'High' | 'Medium' | 'Low',\r\n      status: 'Open',\r\n      scope: '',\r\n      description: '',\r\n      instructions: '',\r\n      impact: '',\r\n      fix: '',\r\n      submitted_date: new Date().toISOString()\r\n    };\r\n    const updatedFindings = [...(reportData.detailed_findings || []), newFinding];\r\n    handleDataUpdate({ detailed_findings: updatedFindings });\r\n  }, [reportData, handleDataUpdate]);\r\n  const handleRemoveFinding = useCallback((index: number, severity: string) => {\r\n    if (!reportData) return;\r\n    const updatedFindings = [...(reportData.detailed_findings || [])];\r\n    const severityFindings = updatedFindings.filter(f => f.severity_category === severity);\r\n    if (index >= 0 && index < severityFindings.length) {\r\n      const findingToRemove = severityFindings[index];\r\n      const findingIndex = updatedFindings.findIndex(f => f === findingToRemove);\r\n      if (findingIndex !== -1) {\r\n        updatedFindings.splice(findingIndex, 1);\r\n        handleDataUpdate({ detailed_findings: updatedFindings });\r\n      }\r\n    }\r\n  }, [reportData, handleDataUpdate]);\r\n  const handleTargetDetailsChange = useCallback((index: number, field: string, value: string) => {\r\n    if (!reportData) return;\r\n    const newTargets = (reportData.target_details || []).map((target, i) => i === index ? { ...target, [field]: value } : target);\r\n    handleDataUpdate({ target_details: newTargets });\r\n  }, [reportData, handleDataUpdate]);\r\n  const handleAddTarget = useCallback(() => {\r\n    if (!reportData) return;\r\n    const newTargets = [...(reportData.target_details || []), { type: '', url: '' }];\r\n    handleDataUpdate({ target_details: newTargets });\r\n  }, [reportData, handleDataUpdate]);\r\n  const handleRemoveTarget = useCallback((index: number) => {\r\n    if (!reportData) return;\r\n    const newTargets = (reportData.target_details || []).filter((_, i) => i !== index);\r\n    handleDataUpdate({ target_details: newTargets });\r\n  }, [reportData, handleDataUpdate]);\r\n  const handleTableChange = useCallback((field: string, value: number) => {\r\n    // ...table change logic (copy from PDFEditor)\r\n  }, [reportData, handleDataUpdate]);\r\n  const handleKeyFindingChange = useCallback((index: number, field: string, value: string) => {\r\n    if (!reportData) return;\r\n    const updatedReports = [...(reportData.reports_list || [])];\r\n    updatedReports[index] = { ...updatedReports[index], [field]: value };\r\n    handleDataUpdate({ reports_list: updatedReports });\r\n  }, [reportData, handleDataUpdate]);\r\n  const handleAddKeyFinding = useCallback(() => {\r\n    if (!reportData) return;\r\n    const severityAbbreviations: Record<string, string> = { Critical: 'C', High: 'H', Medium: 'M', Low: 'L' };\r\n    const defaultSeverity = 'Medium' as const;\r\n    const severityCounts: Record<string, number> = { Critical: 0, High: 0, Medium: 0, Low: 0 };\r\n    (reportData.reports_list || []).forEach(finding => {\r\n      if (finding.severity_category && finding.severity_category in severityCounts) {\r\n        severityCounts[finding.severity_category as keyof typeof severityCounts]++;\r\n      }\r\n    });\r\n    const newCount = severityCounts[defaultSeverity] + 1;\r\n    const abbreviation = `${severityAbbreviations[defaultSeverity]}${newCount}`;\r\n    const newFinding = {\r\n      abbreviation: abbreviation,\r\n      title: 'New Finding',\r\n      severity_category: defaultSeverity as 'Critical' | 'High' | 'Medium' | 'Low',\r\n      status: 'Open'\r\n    };\r\n    const updatedReports = [...(reportData.reports_list || []), newFinding];\r\n    handleDataUpdate({ reports_list: updatedReports });\r\n  }, [reportData, handleDataUpdate]);\r\n  const handleRemoveKeyFinding = useCallback((index: number) => {\r\n    if (!reportData) return;\r\n    const updatedReports = reportData.reports_list?.filter((_, i) => i !== index);\r\n    handleDataUpdate({ reports_list: updatedReports });\r\n  }, [reportData, handleDataUpdate]);\r\n  const handleRecommendationsChange = useCallback((recommendations: { title: string; description: string }[]) => {\r\n    setSectionData(prev => ({ ...prev, recommendations_list: recommendations }));\r\n  }, []);\r\n  const saveSectionChanges = useCallback(async () => {\r\n    if (!report_id || !reportData) return;\r\n    try {\r\n      const updatedData = { ...reportData, ...sectionData };\r\n      const response = await updateProgramReport(report_id!, updatedData);\r\n      const savedData = response.success ? response.data : updatedData;\r\n      setReportData(savedData);\r\n      await updatePreview(savedData);\r\n      setSectionData({});\r\n      showNotification('success', 'Section Changes Saved', 'Section changes saved successfully!');\r\n    } catch (error) {\r\n      // eslint-disable-next-line no-console\r\n      console.error('Error saving section changes:', error);\r\n      showNotification('error', 'Section Changes Error', 'Error saving section changes. Please try again.');\r\n    }\r\n  }, [report_id, reportData, sectionData, updatePreview, showNotification]);\r\n\r\n  // Print handler\r\n  const handlePrint = useReactToPrint({\r\n    content: (): HTMLDivElement | null => previewRef.current,\r\n    documentTitle: reportData?.report_title || 'Security Report',\r\n    removeAfterPrint: true,\r\n    pageStyle: `\r\n      @page { size: A4; margin: 0; }\r\n      body { margin: 0; padding: 0; }\r\n      .page { page-break-after: always; min-height: 297mm; }\r\n      .page:last-child { page-break-after: avoid; }\r\n      .no-break, img, table, .finding-card { break-inside: avoid !important; page-break-inside: avoid !important; }\r\n      .page-break { break-after: page !important; page-break-after: always !important; }\r\n    `,\r\n  } as any);\r\n\r\n  return {\r\n    report_id,\r\n    role,\r\n    loading,\r\n    previewLoading,\r\n    previewHtml,\r\n    reportData,\r\n    activeSection,\r\n    setActiveSection,\r\n    sectionData,\r\n    statusUpdateLoading,\r\n    notification,\r\n    setNotification,\r\n    isFullViewOpen,\r\n    setIsFullViewOpen,\r\n    isChangeLogOpen,\r\n    setIsChangeLogOpen,\r\n    changeLog,\r\n    setChangeLog,\r\n    changeLogLoading,\r\n    setChangeLogLoading,\r\n    previewMode,\r\n    setPreviewMode,\r\n    editorWidth,\r\n    setEditorWidth,\r\n    dragging,\r\n    viewMode,\r\n    setViewMode,\r\n    previewRef,\r\n    handlePrint,\r\n    showNotification,\r\n    hideNotification,\r\n    openFullView,\r\n    closeFullView,\r\n    openChangeLog,\r\n    closeChangeLog,\r\n    updatePreview,\r\n    debouncedUpdatePreview,\r\n    handleDataUpdate,\r\n    fetchReportData,\r\n    updateReportStatus: updateReportStatusHandler,\r\n    handleSave,\r\n    handleApprove,\r\n    handleReject,\r\n    canApprove,\r\n    canReject,\r\n    handleInputChange,\r\n    handleHtmlChange,\r\n    handleDataChange,\r\n    handleFindingChange,\r\n    handleAddFinding,\r\n    handleRemoveFinding,\r\n    handleTargetDetailsChange,\r\n    handleAddTarget,\r\n    handleRemoveTarget,\r\n    handleTableChange,\r\n    handleKeyFindingChange,\r\n    handleAddKeyFinding,\r\n    handleRemoveKeyFinding,\r\n    handleRecommendationsChange,\r\n    saveSectionChanges,\r\n    currentData,\r\n    saveLoading,\r\n    approveLoading,\r\n    rejectLoading,\r\n  };\r\n} "], "mappings": ";AAAA;AACA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,QAAQ,OAAO;AACzE,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,oBAAoB,EAAEC,yBAAyB,EAAEC,kBAAkB,EAAEC,mBAAmB,QAAQ,8DAA8D;AACvK,OAAOC,KAAK,MAAM,0BAA0B;AAC5C,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,SAASC,eAAe,QAAQ,gBAAgB;AAKhD;AACA,MAAMC,oBAAoB,GAAG,GAAG;AAChC,MAAMC,mBAAmB,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAU;AAC1E,MAAMC,YAAY,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAU;AACzD,MAAMC,gBAAgB,GAAG,GAAG;AAC5B,MAAMC,gBAAgB,GAAG,GAAG;AAC5B,MAAMC,oBAAoB,GAAG,KAAK;;AAElC;AACA,MAAMC,yBAAyB,GAAIC,QAAgB,IAAK;EACtD,MAAMC,UAAU,GAAGD,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGH,QAAQ,CAACI,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EACrF,OAAOJ,UAAU;AACnB,CAAC;AAED,MAAMK,oBAAoB,GAAIC,WAAmB,IAAM;AACvD;AACA,sEAAsEA,WAAY;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgDA,WAAY;AAC5D;AACA,CAAC;AAED,MAAMC,yBAAyB,GAAGA,CAAA,MAAO;EACvCC,QAAQ,EAAE;IAAEC,IAAI,EAAE,CAAC;IAAEC,MAAM,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAE,CAAC;EAC1CC,IAAI,EAAE;IAAEH,IAAI,EAAE,CAAC;IAAEC,MAAM,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAE,CAAC;EACtCE,MAAM,EAAE;IAAEJ,IAAI,EAAE,CAAC;IAAEC,MAAM,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAE,CAAC;EACxCG,GAAG,EAAE;IAAEL,IAAI,EAAE,CAAC;IAAEC,MAAM,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAE;AACtC,CAAC,CAAC;AAIF,OAAO,SAASI,iBAAiBA,CAAA,EAAG;EAAAC,EAAA;EAClC,MAAM;IAAEC;EAAU,CAAC,GAAGjC,SAAS,CAAwB,CAAC;EACxD,MAAM;IAAEkC;EAAK,CAAC,GAAG5B,kBAAkB,CAAC,CAAC;;EAErC;EACA,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0C,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAAC8C,UAAU,EAAEC,aAAa,CAAC,GAAG/C,QAAQ,CAAoB,IAAI,CAAC;EACrE,MAAM,CAACgD,aAAa,EAAEC,gBAAgB,CAAC,GAAGjD,QAAQ,CAAS,OAAO,CAAC;EACnE,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAsB,CAAC,CAAC,CAAC;EACvE,MAAM,CAACoD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACsD,YAAY,EAAEC,eAAe,CAAC,GAAGvD,QAAQ,CAAoB;IAAEwD,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE,EAAE;IAAEC,OAAO,EAAE,EAAE;IAAEC,SAAS,EAAE;EAAM,CAAC,CAAC;EAC/H,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8D,eAAe,EAAEC,kBAAkB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACgE,SAAS,EAAEC,YAAY,CAAC,GAAGjE,QAAQ,CAAQ,EAAE,CAAC;EACrD,MAAM,CAACkE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoE,WAAW,EAAEC,cAAc,CAAC,GAAGrE,QAAQ,CAAmC,MAAM,CAAC;EACxF,MAAM,CAACsE,WAAW,EAAEC,cAAc,CAAC,GAAGvE,QAAQ,CAASkB,oBAAoB,CAAC;EAC5E,MAAMsD,QAAQ,GAAGpE,MAAM,CAAC,KAAK,CAAC;EAC9B,MAAM,CAACqE,QAAQ,EAAEC,WAAW,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM2E,UAAU,GAAGvE,MAAM,CAAiB,IAAI,CAAC;EAC/C,MAAM,CAACwE,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAoB,IAAI,CAAC;EACvE,MAAM,CAAC8E,WAAW,EAAEC,cAAc,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgF,cAAc,EAAEC,iBAAiB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACkF,aAAa,EAAEC,gBAAgB,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAMoF,gBAAgB,GAAGlF,WAAW,CAAC,CAACsD,IAAkC,EAAEC,KAAa,EAAEC,OAAe,KAAK;IAC3GH,eAAe,CAAC;MAAEC,IAAI;MAAEC,KAAK;MAAEC,OAAO;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;EAC5D,CAAC,EAAE,EAAE,CAAC;EACN,MAAM0B,gBAAgB,GAAGnF,WAAW,CAAC,MAAM;IACzCqD,eAAe,CAAE+B,IAAuB,KAAM;MAAE,GAAGA,IAAI;MAAE3B,SAAS,EAAE;IAAM,CAAC,CAAC,CAAC;EAC/E,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM4B,YAAY,GAAGrF,WAAW,CAAC,MAAM2D,iBAAiB,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;EACnE,MAAM2B,aAAa,GAAGtF,WAAW,CAAC,MAAM2D,iBAAiB,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;;EAErE;EACA,MAAM4B,aAAa,GAAGvF,WAAW,CAAC,YAAY;IAC5C,IAAI,CAACoC,SAAS,EAAE;IAChByB,kBAAkB,CAAC,IAAI,CAAC;IACxBI,mBAAmB,CAAC,IAAI,CAAC;IACzB,IAAI;MACF,MAAMuB,GAAG,GAAG,MAAMnF,yBAAyB,CAAC+B,SAAS,CAAC;MACtD,IAAIoD,GAAG,CAACC,OAAO,EAAE;QACf1B,YAAY,CAACyB,GAAG,CAACE,IAAI,CAAC;MACxB,CAAC,MAAM;QACL3B,YAAY,CAAC,EAAE,CAAC;MAClB;IACF,CAAC,CAAC,OAAO4B,CAAC,EAAE;MACV5B,YAAY,CAAC,EAAE,CAAC;IAClB,CAAC,SAAS;MACRE,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC,EAAE,CAAC7B,SAAS,CAAC,CAAC;EACf,MAAMwD,cAAc,GAAG5F,WAAW,CAAC,MAAM6D,kBAAkB,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;;EAEvE;EACA,MAAMgC,aAAa,GAAG7F,WAAW,CAAC,MAAO0F,IAAgB,IAAK;IAC5D,IAAI,CAACtD,SAAS,EAAE;IAChB,IAAI;MACFK,iBAAiB,CAAC,IAAI,CAAC;MACvB,MAAMqD,QAAQ,GAAG,MAAMtF,KAAK,CAACuF,IAAI,CAC9B,uBAAsB3D,SAAU,UAAS,EAC1C;QAAEQ,UAAU,EAAE8C;MAAK,CAAC,EACpB;QAAEM,OAAO,EAAE;UAAE,QAAQ,EAAE;QAAY,CAAC;QAAEC,YAAY,EAAE;MAAO,CAC7D,CAAC;MACDtD,cAAc,CAACmD,QAAQ,CAACJ,IAAI,CAAC;IAC/B,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACd;MACAC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACRzD,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC,EAAE,CAACL,SAAS,CAAC,CAAC;EAEf,MAAMgE,sBAAsB,GAAGnG,OAAO,CAAC,MAAM;IAC3C,IAAIoG,SAAyB;IAC7B,OAAQX,IAAgB,IAAK;MAC3BY,YAAY,CAACD,SAAS,CAAC;MACvBA,SAAS,GAAGE,UAAU,CAAC,MAAMV,aAAa,CAACH,IAAI,CAAC,EAAE/E,oBAAoB,CAAC;IACzE,CAAC;EACH,CAAC,EAAE,CAACkF,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMW,gBAAgB,GAAGxG,WAAW,CAAEyG,OAA4B,IAAK;IACrE,IAAI,CAAC7D,UAAU,EAAE;IACjB,IAAI8D,WAAW,GAAG;MAAE,GAAG9D,UAAU;MAAE,GAAG6D;IAAQ,CAAC;IAC/C5D,aAAa,CAAC6D,WAAW,CAAC;IAC1B/B,cAAc,CAAC+B,WAAW,CAAC,CAAC,CAAC;IAC7BzD,cAAc,CAACmC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,GAAGqB;IAAQ,CAAC,CAAC,CAAC;IACjDL,sBAAsB,CAACM,WAAW,CAAC;EACrC,CAAC,EAAE,CAAC9D,UAAU,EAAEwD,sBAAsB,CAAC,CAAC;;EAExC;EACA,MAAMO,eAAe,GAAG3G,WAAW,CAAC,YAAY;IAC9C,IAAI,CAACoC,SAAS,EAAE;IAChB,IAAI;MACFG,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMuD,QAAQ,GAAG,MAAM1F,oBAAoB,CAACgC,SAAS,CAAC;MACtD,IAAI0D,QAAQ,CAACc,MAAM,KAAK,SAAS,EAAE;QACjC,IAAIlB,IAAI,GAAGI,QAAQ,CAACJ,IAAI;QACxB;QACA,IAAI,OAAOA,IAAI,CAACmB,WAAW,KAAK,QAAQ,EAAE;UACxC,IAAI;YACF;YACA,MAAMC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACtB,IAAI,CAACmB,WAAW,CAAC;YAC3C,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,EAAE;cACjDpB,IAAI,CAACmB,WAAW,GAAGC,MAAM;YAC3B,CAAC,MAAM;cACL;cACApB,IAAI,CAACmB,WAAW,GAAGnB,IAAI,CAACmB,WAAW,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAQ,EAAEC,GAAW,KAAK;gBAC/ED,GAAG,CAACC,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;gBACtB,OAAOF,GAAG;cACZ,CAAC,EAAE,CAAC,CAAC,CAAC;YACR;UACF,CAAC,CAAC,OAAOxB,CAAC,EAAE;YACV;YACAD,IAAI,CAACmB,WAAW,GAAGnB,IAAI,CAACmB,WAAW,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAQ,EAAEC,GAAW,KAAK;cAC/ED,GAAG,CAACC,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;cACtB,OAAOF,GAAG;YACZ,CAAC,EAAE,CAAC,CAAC,CAAC;UACR;QACF;QACA;QACA,IACE,CAACzB,IAAI,CAACmB,WAAW,IAChB,OAAOnB,IAAI,CAACmB,WAAW,KAAK,QAAQ,IACnC,CAACnB,IAAI,CAACmB,WAAW,CAACS,GAAG,IACrB,CAAC5B,IAAI,CAACmB,WAAW,CAACU,OAAO,IACzB,CAAC7B,IAAI,CAACmB,WAAW,CAACW,MAAO,EAC3B;UACA9B,IAAI,CAACmB,WAAW,GAAG;YAAES,GAAG,EAAE,IAAI;YAAEC,OAAO,EAAE,KAAK;YAAEC,MAAM,EAAE;UAAM,CAAC;QACjE;QACA3E,aAAa,CAAC6C,IAAI,CAAC;QACnB,MAAMG,aAAa,CAACH,IAAI,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACd;MACAC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,SAAS;MACR3D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACH,SAAS,EAAEyD,aAAa,CAAC,CAAC;EAE9B9F,SAAS,CAAC,MAAM;IACd,IAAIqC,SAAS,EAAE;MACbuE,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACvE,SAAS,EAAEuE,eAAe,CAAC,CAAC;EAEhC5G,SAAS,CAAC,MAAM;IACd4E,cAAc,CAAC/B,UAAU,CAAC;EAC5B,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB7C,SAAS,CAAC,MAAM;IACd;IACA,IAAI6C,UAAU,IAAIA,UAAU,CAACiE,WAAW,IAAIjE,UAAU,CAACiE,WAAW,CAACS,GAAG,EAAE;MACtEzB,aAAa,CAACjD,UAAU,CAAC;IAC3B;IACA;IACA;EACF,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB7C,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACqC,SAAS,IAAIQ,UAAU,EAAE;MAC5B,IACE,CAACA,UAAU,CAACiE,WAAW,IACtB,OAAOjE,UAAU,CAACiE,WAAW,KAAK,QAAQ,IACzC,CAACjE,UAAU,CAACiE,WAAW,CAACS,GAAG,IAC3B,CAAC1E,UAAU,CAACiE,WAAW,CAACU,OAAO,IAC/B,CAAC3E,UAAU,CAACiE,WAAW,CAACW,MAAO,EACjC;QACA,MAAMC,OAAO,GAAG;UAAE,GAAG7E,UAAU;UAAEiE,WAAW,EAAE;YAAES,GAAG,EAAE,IAAI;YAAEC,OAAO,EAAE,KAAK;YAAEC,MAAM,EAAE;UAAM;QAAE,CAAC;QAC5F3E,aAAa,CAAC4E,OAAO,CAAC;QACtB9C,cAAc,CAAC8C,OAAO,CAAC;QACvBrB,sBAAsB,CAACqB,OAAO,CAAC;MACjC;IACF;EACF,CAAC,EAAE,CAACrF,SAAS,EAAEQ,UAAU,EAAEwD,sBAAsB,CAAC,CAAC;;EAEnD;EACA,MAAMsB,yBAAyB,GAAG1H,WAAW,CAAC,MAAO2H,SAA8B,IAAK;IACtF,IAAI,CAACvF,SAAS,EAAE;IAChB,IAAI;MACFe,sBAAsB,CAAC,IAAI,CAAC;MAC5B,MAAM2C,QAAQ,GAAG,MAAMxF,kBAAkB,CAAC8B,SAAS,EAAEuF,SAAS,CAAC;MAC/D,IAAI7B,QAAQ,CAACL,OAAO,EAAE;QACpB5C,aAAa,CAACuC,IAAI,IAAIA,IAAI,GAAG;UAAE,GAAGA,IAAI;UAAEwB,MAAM,EAAEe;QAAU,CAAC,GAAG,IAAI,CAAC;QACnEzC,gBAAgB,CAAC,SAAS,EAAE,uBAAuB,EAAG,4BAA2ByC,SAAU,EAAC,CAAC;MAC/F,CAAC,MAAM;QACLzC,gBAAgB,CAAC,OAAO,EAAE,4BAA4B,EAAE,mDAAmD,CAAC;MAC9G;IACF,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACd;MACAC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDhB,gBAAgB,CAAC,OAAO,EAAE,4BAA4B,EAAE,iDAAiD,CAAC;IAC5G,CAAC,SAAS;MACR/B,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC,EAAE,CAACf,SAAS,EAAE8C,gBAAgB,CAAC,CAAC;;EAEjC;EACA,MAAM0C,UAAU,GAAG5H,WAAW,CAAC,YAAY;IACzC,IAAI,CAAC4C,UAAU,EAAE;IACjB,IAAI;MACFiC,cAAc,CAAC,IAAI,CAAC;MACpB,MAAM6B,WAAW,GAAG;QAAE,GAAG9D,UAAU;QAAE,GAAGI;MAAY,CAAC;MACrD,MAAM8C,QAAQ,GAAG,MAAMvF,mBAAmB,CAAC6B,SAAS,EAAGsE,WAAW,CAAC;;MAEnE;MACA,MAAMmB,SAAS,GAAG/B,QAAQ,CAACL,OAAO,GAAGK,QAAQ,CAACJ,IAAI,GAAGgB,WAAW;MAChE7D,aAAa,CAACgF,SAAS,CAAC;MACxBlD,cAAc,CAACkD,SAAS,CAAC;MACzB5E,cAAc,CAAC,CAAC,CAAC,CAAC;MAClB,MAAM4C,aAAa,CAACgC,SAAS,CAAC;MAC9B3C,gBAAgB,CAAC,SAAS,EAAE,cAAc,EAAE,4BAA4B,CAAC;IAC3E,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACd;MACAC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5ChB,gBAAgB,CAAC,OAAO,EAAE,mBAAmB,EAAE,wCAAwC,CAAC;IAC1F,CAAC,SAAS;MACRL,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,EAAE,CAACzC,SAAS,EAAEQ,UAAU,EAAEI,WAAW,EAAE6C,aAAa,EAAEX,gBAAgB,CAAC,CAAC;;EAEzE;EACA,MAAM4C,aAAa,GAAG9H,WAAW,CAAC,YAAY;IAC5C,IAAI,CAAC4C,UAAU,EAAE;IACjB,IAAI;MACFmC,iBAAiB,CAAC,IAAI,CAAC;MACvB;MACA,MAAM2B,WAAW,GAAG;QAAE,GAAG9D,UAAU;QAAE,GAAGI;MAAY,CAAC;MACrD;MACA,MAAM8C,QAAQ,GAAG,MAAMvF,mBAAmB,CAAC6B,SAAS,EAAGsE,WAAW,CAAC;MACnE,MAAMmB,SAAS,GAAG/B,QAAQ,CAACL,OAAO,GAAGK,QAAQ,CAACJ,IAAI,GAAGgB,WAAW;MAChE7D,aAAa,CAACgF,SAAS,CAAC;MACxB,MAAMhC,aAAa,CAACgC,SAAS,CAAC;MAC9B5E,cAAc,CAAC,CAAC,CAAC,CAAC;MAClB;MACA,IAAI0E,SAAqC,GAAG,IAAI;MAChD,IAAII,OAAO,GAAG,EAAE;MAChB,IAAI,OAAO1F,IAAI,KAAK,QAAQ,EAAE;QAC5B0F,OAAO,GAAG;UACR,CAAC,EAAE,YAAY;UAAE,CAAC,EAAE,UAAU;UAAE,CAAC,EAAE,OAAO;UAAE,CAAC,EAAE,IAAI;UAAE,CAAC,EAAE,eAAe;UAAE,CAAC,EAAE,WAAW;UACvF,CAAC,EAAE,kBAAkB;UACrB,CAAC,EAAE,wBAAwB;UAC3B,CAAC,EAAE;QACL,CAAC,CAAC1F,IAAI,CAAC,IAAI,EAAE;MACf,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QACnC0F,OAAO,GAAG1F,IAAI;MAChB;MACA0F,OAAO,GAAGA,OAAO,CAAC1G,WAAW,CAAC,CAAC;MAC/B,MAAM2G,SAAS,GAAG,CAACpF,UAAU,CAACgE,MAAM,IAAI,EAAE,EAAErF,WAAW,CAAC,CAAC;MACzD,IAAIwG,OAAO,KAAK,IAAI,EAAE;QACpB,IAAIC,SAAS,KAAK,OAAO,IAAIA,SAAS,KAAK,WAAW,EAAE;UACtDL,SAAS,GAAG,cAAc;QAC5B,CAAC,MAAM,IAAIK,SAAS,KAAK,4BAA4B,EAAE;UACrDL,SAAS,GAAG,eAAe;QAC7B;MACF,CAAC,MAAM,IAAI,CACT,OAAO,EACP,WAAW,EACX,eAAe,CAChB,CAACM,QAAQ,CAACF,OAAO,CAAC,EAAE;QACnB,IAAIC,SAAS,KAAK,cAAc,IAAIA,SAAS,KAAK,4BAA4B,EAAE;UAC9EL,SAAS,GAAG,UAAU;QACxB,CAAC,MAAM,IAAIK,SAAS,KAAK,eAAe,EAAE;UACxCL,SAAS,GAAG,gBAAgB;QAC9B;MACF;MACA;MACA,IAAIA,SAAS,EAAE;QACb,MAAMD,yBAAyB,CAACC,SAAS,CAAC;MAC5C;MACAzC,gBAAgB,CAAC,SAAS,EAAE,iBAAiB,EAAE,yCAAyC,CAAC;IAC3F,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACd;MACAC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/ChB,gBAAgB,CAAC,OAAO,EAAE,uBAAuB,EAAE,qDAAqD,CAAC;IAC3G,CAAC,SAAS;MACRH,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC,EAAE,CAAC3C,SAAS,EAAEQ,UAAU,EAAEI,WAAW,EAAE6C,aAAa,EAAE6B,yBAAyB,EAAErF,IAAI,EAAE6C,gBAAgB,CAAC,CAAC;;EAE1G;EACA,MAAMgD,YAAY,GAAGlI,WAAW,CAAC,YAAY;IAC3C,IAAI,CAAC4C,UAAU,EAAE;IACjB,IAAI;MACFqC,gBAAgB,CAAC,IAAI,CAAC;MACtB,MAAMyB,WAAW,GAAG;QAAE,GAAG9D,UAAU;QAAE,GAAGI;MAAY,CAAC;MACrD,MAAM8C,QAAQ,GAAG,MAAMvF,mBAAmB,CAAC6B,SAAS,EAAGsE,WAAW,CAAC;MACnE,MAAMmB,SAAS,GAAG/B,QAAQ,CAACL,OAAO,GAAGK,QAAQ,CAACJ,IAAI,GAAGgB,WAAW;MAChE7D,aAAa,CAACgF,SAAS,CAAC;MACxB,MAAMhC,aAAa,CAACgC,SAAS,CAAC;MAC9B5E,cAAc,CAAC,CAAC,CAAC,CAAC;MAClB,MAAMyE,yBAAyB,CAAC,OAAO,CAAC;MACxCxC,gBAAgB,CAAC,SAAS,EAAE,iBAAiB,EAAE,kDAAkD,CAAC;IACpG,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACd;MACAC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/ChB,gBAAgB,CAAC,OAAO,EAAE,wBAAwB,EAAE,2CAA2C,CAAC;IAClG,CAAC,SAAS;MACRD,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC,EAAE,CAAC7C,SAAS,EAAEQ,UAAU,EAAEI,WAAW,EAAE6C,aAAa,EAAE6B,yBAAyB,EAAExC,gBAAgB,CAAC,CAAC;;EAEpG;EACA,MAAMiD,UAAU,GAAGlI,OAAO,CAAC,MAAM;IAC/B,IAAI,CAAC2C,UAAU,EAAE,OAAO,KAAK;IAC7B,IAAImF,OAAO,GAAG,EAAE;IAChB,IAAI,OAAO1F,IAAI,KAAK,QAAQ,EAAE;MAC5B0F,OAAO,GAAG;QACR,CAAC,EAAE,YAAY;QAAE,CAAC,EAAE,UAAU;QAAE,CAAC,EAAE,OAAO;QAAE,CAAC,EAAE,IAAI;QAAE,CAAC,EAAE,eAAe;QAAE,CAAC,EAAE,WAAW;QACvF,CAAC,EAAE,kBAAkB;QACrB,CAAC,EAAE,wBAAwB;QAC3B,CAAC,EAAE;MACL,CAAC,CAAC1F,IAAI,CAAC,IAAI,EAAE;IACf,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MACnC0F,OAAO,GAAG1F,IAAI;IAChB;IACA0F,OAAO,GAAGA,OAAO,CAAC1G,WAAW,CAAC,CAAC;IAC/B,MAAM2G,SAAS,GAAG,CAACpF,UAAU,CAACgE,MAAM,IAAI,EAAE,EAAErF,WAAW,CAAC,CAAC;IACzD,IAAIwG,OAAO,KAAK,IAAI,EAAE;MACpB,OAAOC,SAAS,KAAK,OAAO,IAAIA,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK,4BAA4B;IACzG;IACA,IAAI,CACF,OAAO,EACP,WAAW,EACX,eAAe,CAChB,CAACC,QAAQ,CAACF,OAAO,CAAC,EAAE;MACnB,OAAOC,SAAS,KAAK,cAAc,IAAIA,SAAS,KAAK,eAAe,IAAIA,SAAS,KAAK,4BAA4B;IACpH;IACA,OAAO,KAAK;EACd,CAAC,EAAE,CAAC3F,IAAI,EAAEO,UAAU,CAAC,CAAC;EAEtB,MAAMwF,SAAS,GAAGnI,OAAO,CAAC,MAAM;IAC9B,IAAI,CAAC2C,UAAU,EAAE,OAAO,KAAK;IAC7B,IAAImF,OAAO,GAAG,EAAE;IAChB,IAAI,OAAO1F,IAAI,KAAK,QAAQ,EAAE;MAC5B0F,OAAO,GAAG;QACR,CAAC,EAAE,YAAY;QAAE,CAAC,EAAE,UAAU;QAAE,CAAC,EAAE,OAAO;QAAE,CAAC,EAAE,IAAI;QAAE,CAAC,EAAE,eAAe;QAAE,CAAC,EAAE,WAAW;QACvF,CAAC,EAAE,kBAAkB;QACrB,CAAC,EAAE,wBAAwB;QAC3B,CAAC,EAAE;MACL,CAAC,CAAC1F,IAAI,CAAC,IAAI,EAAE;IACf,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MACnC0F,OAAO,GAAG1F,IAAI;IAChB;IACA,OAAO,CACL,OAAO,EACP,WAAW,EACX,eAAe,CAChB,CAAC4F,QAAQ,CAACF,OAAO,CAAC1G,WAAW,CAAC,CAAC,CAAC,IAAIuB,UAAU,CAACgE,MAAM,KAAK,cAAc;EAC3E,CAAC,EAAE,CAACvE,IAAI,EAAEO,UAAU,CAAC,CAAC;;EAEtB;EACA,MAAMyF,iBAAiB,GAAGrI,WAAW,CAAC,CAACsI,KAAa,EAAEC,KAAa,KAAK;IACtE/B,gBAAgB,CAAC;MAAE,CAAC8B,KAAK,GAAGC;IAAM,CAAC,CAAC;EACtC,CAAC,EAAE,CAAC/B,gBAAgB,CAAC,CAAC;EACtB,MAAMgC,gBAAgB,GAAGxI,WAAW,CAAC,CAACsI,KAAa,EAAEC,KAAa,KAAK;IACrE/B,gBAAgB,CAAC;MAAE,CAAC8B,KAAK,GAAGC;IAAM,CAAC,CAAC;EACtC,CAAC,EAAE,CAAC/B,gBAAgB,CAAC,CAAC;EACtB,MAAMiC,gBAAgB,GAAGzI,WAAW,CAAC,CAACsI,KAAa,EAAEC,KAAU,KAAK;IAClE/B,gBAAgB,CAAC;MAAE,CAAC8B,KAAK,GAAGC;IAAM,CAAC,CAAC;EACtC,CAAC,EAAE,CAAC/B,gBAAgB,CAAC,CAAC;EACtB,MAAMkC,mBAAmB,GAAG1I,WAAW,CAAC,CAAC2I,KAAa,EAAEL,KAA+B,EAAEC,KAAa,EAAEK,QAAgB,KAAK;IAC3H,IAAI,CAAChG,UAAU,EAAE;IACjB,MAAMiG,eAAe,GAAG,CAAC,IAAIjG,UAAU,CAACkG,iBAAiB,IAAI,EAAE,CAAC,CAAC;IACjE,MAAMC,gBAAgB,GAAGF,eAAe,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,iBAAiB,KAAKN,QAAQ,CAAC;IACtF,IAAID,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGI,gBAAgB,CAACI,MAAM,EAAE;MACjD,MAAMC,eAAe,GAAGL,gBAAgB,CAACJ,KAAK,CAAC;MAC/C,MAAMU,YAAY,GAAGR,eAAe,CAACS,SAAS,CAACL,CAAC,IAAIA,CAAC,KAAKG,eAAe,CAAC;MAC1E,IAAIC,YAAY,KAAK,CAAC,CAAC,EAAE;QACvBR,eAAe,CAACQ,YAAY,CAAC,GAAG;UAC9B,GAAGR,eAAe,CAACQ,YAAY,CAAC;UAChC,CAACf,KAAK,GAAGC;QACX,CAAC;QACD/B,gBAAgB,CAAC;UAAEsC,iBAAiB,EAAED;QAAgB,CAAC,CAAC;MAC1D;IACF;EACF,CAAC,EAAE,CAACjG,UAAU,EAAE4D,gBAAgB,CAAC,CAAC;EAClC,MAAM+C,gBAAgB,GAAGvJ,WAAW,CAAE4I,QAAgB,IAAK;IACzD,IAAI,CAAChG,UAAU,EAAE;IACjB,MAAM4G,qBAA6C,GAAG;MAAE7H,QAAQ,EAAE,GAAG;MAAEI,IAAI,EAAE,GAAG;MAAEC,MAAM,EAAE,GAAG;MAAEC,GAAG,EAAE;IAAI,CAAC;IACzG;IACA,MAAMwH,sBAAsB,GAAG,CAAC7G,UAAU,CAACkG,iBAAiB,IAAI,EAAE,EAAEE,MAAM,CACxEC,CAAC,IAAI,CAACA,CAAC,CAACC,iBAAiB,IAAI,EAAE,EAAE3H,WAAW,CAAC,CAAC,KAAKqH,QAAQ,CAACrH,WAAW,CAAC,CAC1E,CAAC;IACD,MAAMmI,QAAQ,GAAGD,sBAAsB,CAACN,MAAM,GAAG,CAAC;IAClD,MAAMQ,YAAY,GAAI,GAAEH,qBAAqB,CAACZ,QAAQ,CAAE,GAAEc,QAAS,EAAC;IACpE,MAAME,UAA2B,GAAG;MAClCD,YAAY,EAAEA,YAAY;MAC1BpG,KAAK,EAAE,EAAE;MACT2F,iBAAiB,EAAEN,QAAkD;MACrEhC,MAAM,EAAE,MAAM;MACdiD,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,MAAM,EAAE,EAAE;MACVC,GAAG,EAAE,EAAE;MACPC,cAAc,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACzC,CAAC;IACD,MAAMvB,eAAe,GAAG,CAAC,IAAIjG,UAAU,CAACkG,iBAAiB,IAAI,EAAE,CAAC,EAAEc,UAAU,CAAC;IAC7EpD,gBAAgB,CAAC;MAAEsC,iBAAiB,EAAED;IAAgB,CAAC,CAAC;EAC1D,CAAC,EAAE,CAACjG,UAAU,EAAE4D,gBAAgB,CAAC,CAAC;EAClC,MAAM6D,mBAAmB,GAAGrK,WAAW,CAAC,CAAC2I,KAAa,EAAEC,QAAgB,KAAK;IAC3E,IAAI,CAAChG,UAAU,EAAE;IACjB,MAAMiG,eAAe,GAAG,CAAC,IAAIjG,UAAU,CAACkG,iBAAiB,IAAI,EAAE,CAAC,CAAC;IACjE,MAAMC,gBAAgB,GAAGF,eAAe,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,iBAAiB,KAAKN,QAAQ,CAAC;IACtF,IAAID,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGI,gBAAgB,CAACI,MAAM,EAAE;MACjD,MAAMmB,eAAe,GAAGvB,gBAAgB,CAACJ,KAAK,CAAC;MAC/C,MAAMU,YAAY,GAAGR,eAAe,CAACS,SAAS,CAACL,CAAC,IAAIA,CAAC,KAAKqB,eAAe,CAAC;MAC1E,IAAIjB,YAAY,KAAK,CAAC,CAAC,EAAE;QACvBR,eAAe,CAAC0B,MAAM,CAAClB,YAAY,EAAE,CAAC,CAAC;QACvC7C,gBAAgB,CAAC;UAAEsC,iBAAiB,EAAED;QAAgB,CAAC,CAAC;MAC1D;IACF;EACF,CAAC,EAAE,CAACjG,UAAU,EAAE4D,gBAAgB,CAAC,CAAC;EAClC,MAAMgE,yBAAyB,GAAGxK,WAAW,CAAC,CAAC2I,KAAa,EAAEL,KAAa,EAAEC,KAAa,KAAK;IAC7F,IAAI,CAAC3F,UAAU,EAAE;IACjB,MAAM6H,UAAU,GAAG,CAAC7H,UAAU,CAAC8H,cAAc,IAAI,EAAE,EAAEC,GAAG,CAAC,CAACC,MAAM,EAAEC,CAAC,KAAKA,CAAC,KAAKlC,KAAK,GAAG;MAAE,GAAGiC,MAAM;MAAE,CAACtC,KAAK,GAAGC;IAAM,CAAC,GAAGqC,MAAM,CAAC;IAC7HpE,gBAAgB,CAAC;MAAEkE,cAAc,EAAED;IAAW,CAAC,CAAC;EAClD,CAAC,EAAE,CAAC7H,UAAU,EAAE4D,gBAAgB,CAAC,CAAC;EAClC,MAAMsE,eAAe,GAAG9K,WAAW,CAAC,MAAM;IACxC,IAAI,CAAC4C,UAAU,EAAE;IACjB,MAAM6H,UAAU,GAAG,CAAC,IAAI7H,UAAU,CAAC8H,cAAc,IAAI,EAAE,CAAC,EAAE;MAAEpH,IAAI,EAAE,EAAE;MAAEyH,GAAG,EAAE;IAAG,CAAC,CAAC;IAChFvE,gBAAgB,CAAC;MAAEkE,cAAc,EAAED;IAAW,CAAC,CAAC;EAClD,CAAC,EAAE,CAAC7H,UAAU,EAAE4D,gBAAgB,CAAC,CAAC;EAClC,MAAMwE,kBAAkB,GAAGhL,WAAW,CAAE2I,KAAa,IAAK;IACxD,IAAI,CAAC/F,UAAU,EAAE;IACjB,MAAM6H,UAAU,GAAG,CAAC7H,UAAU,CAAC8H,cAAc,IAAI,EAAE,EAAE1B,MAAM,CAAC,CAACiC,CAAC,EAAEJ,CAAC,KAAKA,CAAC,KAAKlC,KAAK,CAAC;IAClFnC,gBAAgB,CAAC;MAAEkE,cAAc,EAAED;IAAW,CAAC,CAAC;EAClD,CAAC,EAAE,CAAC7H,UAAU,EAAE4D,gBAAgB,CAAC,CAAC;EAClC,MAAM0E,iBAAiB,GAAGlL,WAAW,CAAC,CAACsI,KAAa,EAAEC,KAAa,KAAK;IACtE;EAAA,CACD,EAAE,CAAC3F,UAAU,EAAE4D,gBAAgB,CAAC,CAAC;EAClC,MAAM2E,sBAAsB,GAAGnL,WAAW,CAAC,CAAC2I,KAAa,EAAEL,KAAa,EAAEC,KAAa,KAAK;IAC1F,IAAI,CAAC3F,UAAU,EAAE;IACjB,MAAMwI,cAAc,GAAG,CAAC,IAAIxI,UAAU,CAACyI,YAAY,IAAI,EAAE,CAAC,CAAC;IAC3DD,cAAc,CAACzC,KAAK,CAAC,GAAG;MAAE,GAAGyC,cAAc,CAACzC,KAAK,CAAC;MAAE,CAACL,KAAK,GAAGC;IAAM,CAAC;IACpE/B,gBAAgB,CAAC;MAAE6E,YAAY,EAAED;IAAe,CAAC,CAAC;EACpD,CAAC,EAAE,CAACxI,UAAU,EAAE4D,gBAAgB,CAAC,CAAC;EAClC,MAAM8E,mBAAmB,GAAGtL,WAAW,CAAC,MAAM;IAC5C,IAAI,CAAC4C,UAAU,EAAE;IACjB,MAAM4G,qBAA6C,GAAG;MAAE7H,QAAQ,EAAE,GAAG;MAAEI,IAAI,EAAE,GAAG;MAAEC,MAAM,EAAE,GAAG;MAAEC,GAAG,EAAE;IAAI,CAAC;IACzG,MAAMsJ,eAAe,GAAG,QAAiB;IACzC,MAAMC,cAAsC,GAAG;MAAE7J,QAAQ,EAAE,CAAC;MAAEI,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAE,CAAC;IAC1F,CAACW,UAAU,CAACyI,YAAY,IAAI,EAAE,EAAEI,OAAO,CAACC,OAAO,IAAI;MACjD,IAAIA,OAAO,CAACxC,iBAAiB,IAAIwC,OAAO,CAACxC,iBAAiB,IAAIsC,cAAc,EAAE;QAC5EA,cAAc,CAACE,OAAO,CAACxC,iBAAiB,CAAgC,EAAE;MAC5E;IACF,CAAC,CAAC;IACF,MAAMQ,QAAQ,GAAG8B,cAAc,CAACD,eAAe,CAAC,GAAG,CAAC;IACpD,MAAM5B,YAAY,GAAI,GAAEH,qBAAqB,CAAC+B,eAAe,CAAE,GAAE7B,QAAS,EAAC;IAC3E,MAAME,UAAU,GAAG;MACjBD,YAAY,EAAEA,YAAY;MAC1BpG,KAAK,EAAE,aAAa;MACpB2F,iBAAiB,EAAEqC,eAAyD;MAC5E3E,MAAM,EAAE;IACV,CAAC;IACD,MAAMwE,cAAc,GAAG,CAAC,IAAIxI,UAAU,CAACyI,YAAY,IAAI,EAAE,CAAC,EAAEzB,UAAU,CAAC;IACvEpD,gBAAgB,CAAC;MAAE6E,YAAY,EAAED;IAAe,CAAC,CAAC;EACpD,CAAC,EAAE,CAACxI,UAAU,EAAE4D,gBAAgB,CAAC,CAAC;EAClC,MAAMmF,sBAAsB,GAAG3L,WAAW,CAAE2I,KAAa,IAAK;IAAA,IAAAiD,qBAAA;IAC5D,IAAI,CAAChJ,UAAU,EAAE;IACjB,MAAMwI,cAAc,IAAAQ,qBAAA,GAAGhJ,UAAU,CAACyI,YAAY,cAAAO,qBAAA,uBAAvBA,qBAAA,CAAyB5C,MAAM,CAAC,CAACiC,CAAC,EAAEJ,CAAC,KAAKA,CAAC,KAAKlC,KAAK,CAAC;IAC7EnC,gBAAgB,CAAC;MAAE6E,YAAY,EAAED;IAAe,CAAC,CAAC;EACpD,CAAC,EAAE,CAACxI,UAAU,EAAE4D,gBAAgB,CAAC,CAAC;EAClC,MAAMqF,2BAA2B,GAAG7L,WAAW,CAAE8L,eAAyD,IAAK;IAC7G7I,cAAc,CAACmC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE2G,oBAAoB,EAAED;IAAgB,CAAC,CAAC,CAAC;EAC9E,CAAC,EAAE,EAAE,CAAC;EACN,MAAME,kBAAkB,GAAGhM,WAAW,CAAC,YAAY;IACjD,IAAI,CAACoC,SAAS,IAAI,CAACQ,UAAU,EAAE;IAC/B,IAAI;MACF,MAAM8D,WAAW,GAAG;QAAE,GAAG9D,UAAU;QAAE,GAAGI;MAAY,CAAC;MACrD,MAAM8C,QAAQ,GAAG,MAAMvF,mBAAmB,CAAC6B,SAAS,EAAGsE,WAAW,CAAC;MACnE,MAAMmB,SAAS,GAAG/B,QAAQ,CAACL,OAAO,GAAGK,QAAQ,CAACJ,IAAI,GAAGgB,WAAW;MAChE7D,aAAa,CAACgF,SAAS,CAAC;MACxB,MAAMhC,aAAa,CAACgC,SAAS,CAAC;MAC9B5E,cAAc,CAAC,CAAC,CAAC,CAAC;MAClBiC,gBAAgB,CAAC,SAAS,EAAE,uBAAuB,EAAE,qCAAqC,CAAC;IAC7F,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACd;MACAC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDhB,gBAAgB,CAAC,OAAO,EAAE,uBAAuB,EAAE,iDAAiD,CAAC;IACvG;EACF,CAAC,EAAE,CAAC9C,SAAS,EAAEQ,UAAU,EAAEI,WAAW,EAAE6C,aAAa,EAAEX,gBAAgB,CAAC,CAAC;;EAEzE;EACA,MAAM+G,WAAW,GAAGvL,eAAe,CAAC;IAClCwL,OAAO,EAAEA,CAAA,KAA6BzH,UAAU,CAAC0H,OAAO;IACxDC,aAAa,EAAE,CAAAxJ,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEyJ,YAAY,KAAI,iBAAiB;IAC5DC,gBAAgB,EAAE,IAAI;IACtBC,SAAS,EAAG;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;EACE,CAAQ,CAAC;EAET,OAAO;IACLnK,SAAS;IACTC,IAAI;IACJC,OAAO;IACPE,cAAc;IACdE,WAAW;IACXE,UAAU;IACVE,aAAa;IACbC,gBAAgB;IAChBC,WAAW;IACXE,mBAAmB;IACnBE,YAAY;IACZC,eAAe;IACfK,cAAc;IACdC,iBAAiB;IACjBC,eAAe;IACfC,kBAAkB;IAClBC,SAAS;IACTC,YAAY;IACZC,gBAAgB;IAChBC,mBAAmB;IACnBC,WAAW;IACXC,cAAc;IACdC,WAAW;IACXC,cAAc;IACdC,QAAQ;IACRC,QAAQ;IACRC,WAAW;IACXC,UAAU;IACVwH,WAAW;IACX/G,gBAAgB;IAChBC,gBAAgB;IAChBE,YAAY;IACZC,aAAa;IACbC,aAAa;IACbK,cAAc;IACdC,aAAa;IACbO,sBAAsB;IACtBI,gBAAgB;IAChBG,eAAe;IACfrG,kBAAkB,EAAEoH,yBAAyB;IAC7CE,UAAU;IACVE,aAAa;IACbI,YAAY;IACZC,UAAU;IACVC,SAAS;IACTC,iBAAiB;IACjBG,gBAAgB;IAChBC,gBAAgB;IAChBC,mBAAmB;IACnBa,gBAAgB;IAChBc,mBAAmB;IACnBG,yBAAyB;IACzBM,eAAe;IACfE,kBAAkB;IAClBE,iBAAiB;IACjBC,sBAAsB;IACtBG,mBAAmB;IACnBK,sBAAsB;IACtBE,2BAA2B;IAC3BG,kBAAkB;IAClBtH,WAAW;IACXE,WAAW;IACXE,cAAc;IACdE;EACF,CAAC;AACH;AAAC7C,EAAA,CA7jBeD,iBAAiB;EAAA,QACT/B,SAAS,EACdM,kBAAkB,EA2efC,eAAe;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}