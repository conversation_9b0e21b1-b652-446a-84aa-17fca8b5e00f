<!DOCTYPE html>
<html>
<head>
    <title>Test Rich Text Styling</title>
</head>
<body>
    <h1>Test Rich Text Content</h1>
    
    <!-- Test content that should be properly rendered in PDF -->
    <div id="test-content">
        <h2 style="font-size: 1.5em; font-weight: bold; margin: 0.75em 0;">Heading 2</h2>
        <h3 style="font-size: 1.17em; font-weight: bold; margin: 0.83em 0;">Heading 3</h3>
        
        <p>This is a paragraph with <strong style="font-weight: bold;">bold text</strong> and <em style="font-style: italic;">italic text</em>.</p>
        
        <ul style="list-style-type: disc; margin: 0; padding: 0 20px 10px;">
            <li>First item</li>
            <li>Second item</li>
            <li>Third item</li>
        </ul>
        
        <ol style="list-style-type: decimal; padding-left: 40px; margin-top: 1em; margin-bottom: 1em;">
            <li>Numbered item 1</li>
            <li>Numbered item 2</li>
        </ol>
        
        <pre style="display: inline-block; min-width: 200px; max-width: 100%; width: auto; background: linear-gradient(145deg, #1e293b, #0f172a); color: #e2e8f0; font-family: 'Fira Code', 'Consolas', monospace; padding: 1rem; margin: 1rem 0; border-radius: 8px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08); overflow-x: auto; position: relative; border-left: 4px solid #3b82f6;" data-language="javascript"><code>function example() {
    console.log("Hello World");
    return true;
}</code></pre>
        
        <blockquote style="border-left: 5px solid #eee; padding: 10px 20px; margin: 0 0 20px;">
            This is a blockquote that should be styled properly in the PDF.
        </blockquote>
        
        <p>Inline <code style="font-family: 'Fira Code', 'Consolas', monospace; background: rgba(59, 130, 246, 0.1); color: #3b82f6; padding: 0.2em 0.4em; border-radius: 3px; font-size: 0.9em; border: 1px solid rgba(59, 130, 246, 0.2);">code example</code> should also work.</p>
    </div>
</body>
</html>
