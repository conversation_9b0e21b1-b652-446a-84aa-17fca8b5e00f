import React, { useEffect, useRef, useState } from "react";

interface PDFViewerProps {
  pdfUrl: string;
  isOpen: boolean;
  onClose: () => void;
}

const PDFViewer: React.FC<PDFViewerProps> = React.memo(
  ({ pdfUrl, isOpen, onClose }) => {
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const modalRef = useRef<HTMLDivElement>(null);
    const closeButtonRef = useRef<HTMLButtonElement>(null);

    // Focus management for accessibility
    useEffect(() => {
      if (isOpen && closeButtonRef.current) {
        closeButtonRef.current.focus();
      }
    }, [isOpen]);

    // Handle keyboard escape key
    useEffect(() => {
      const handleEscape = (event: KeyboardEvent) => {
        if (event.key === "Escape" && isOpen) {
          onClose();
        }
      };
      window.addEventListener("keydown", handleEscape);
      return () => window.removeEventListener("keydown", handleEscape);
    }, [isOpen, onClose]);

    // Handle iframe loading
    const handleIframeLoad = () => {
      setIsLoading(false);
    };

    // Validate PDF URL
    useEffect(() => {
      if (pdfUrl && !pdfUrl.match(/\.(pdf)$/i)) {
        setError("Invalid PDF URL");
        setIsLoading(false);
      }
    }, [pdfUrl]);

    if (!isOpen) return null;

    return (
      <div
        className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 px-4 backdrop-blur-sm transition-opacity duration-200 sm:px-6"
        role="dialog"
        aria-modal="true"
        aria-labelledby="pdf-preview-title"
      >
        <div
          ref={modalRef}
          className="relative flex h-[85vh] w-full max-w-5xl scale-100 transform flex-col overflow-hidden rounded-lg bg-white shadow-lg transition-all duration-150 sm:h-[90vh]"
        >
          {/* Header */}
          <div className="flex items-center justify-between bg-gray-50 p-4 sm:p-5">
            <h3
              id="pdf-preview-title"
              className="text-lg font-semibold text-gray-900"
            >
              PDF Preview
            </h3>
            <div className="flex items-center gap-3">
              <a
                href={pdfUrl}
                target="_blank"
                rel="noopener noreferrer"
                className={`inline-flex items-center rounded-md bg-blue-50 px-3 py-1.5 text-sm font-medium text-blue-600 transition-colors duration-150 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  error ? "cursor-not-allowed opacity-50" : ""
                }`}
                aria-label="Open PDF in new tab"
                onClick={e => {
                  if (error) e.preventDefault();
                }}
              >
                Open in New Tab
              </a>
              <button
                ref={closeButtonRef}
                onClick={onClose}
                className="rounded-full p-2 text-gray-500 transition-colors duration-150 hover:bg-gray-100 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                aria-label="Close PDF preview"
              >
                <svg
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="relative flex-grow p-4 sm:p-5">
            {error ? (
              <div className="flex h-full items-center justify-center text-sm font-medium text-red-600">
                <p>{error}</p>
              </div>
            ) : (
              <>
                {isLoading && (
                  <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80">
                    <div className="border-3 h-7 w-7 animate-spin rounded-full border-blue-500 border-t-transparent" />
                  </div>
                )}
                <iframe
                  src={`${pdfUrl}#zoom=100`}
                  className="h-full w-full border-0"
                  title="PDF Preview"
                  onLoad={handleIframeLoad}
                  onError={() => {
                    setError("Failed to load PDF");
                    setIsLoading(false);
                  }}
                />
              </>
            )}
          </div>
        </div>
      </div>
    );
  }
);

export default PDFViewer;
