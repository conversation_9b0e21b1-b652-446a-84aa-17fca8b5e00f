import JSZip from "jszip";

/**
 * Compress a list of files into
 * a single ZIP file
 */
export const createZip = async (files: any[]) => {
  const zip = new JSZip();

  if (files.length === 0) return null;

  // Add the files to the zip
  const promises = files.map(
    file =>
      new Promise<void>((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = e => {
          const contents = e.target?.result;
          if (contents) zip.file(file.name, contents);
          resolve();
        };

        reader.onerror = error => reject(error);
        reader.readAsArrayBuffer(file);
      })
  );

  await Promise.all(promises);
  return zip.generateAsync({ type: "blob" });
};
