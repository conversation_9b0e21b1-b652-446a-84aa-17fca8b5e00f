import React from 'react';
import { Document, Page, Text, Font, View, StyleSheet } from '@react-pdf/renderer';
import CoverPage from './sections/CoverPage';
import DisclaimerPage from './sections/DisclaimerPage';
import FullTableOfContentsPage from './sections/FullTableOfContentsPage';
import DocumentReferencePage from './sections/DocumentReferencePage';
import ExecutiveSummaryPage from './sections/ExecutiveSummaryPage';
import KeyFindingsPage from './sections/KeyFindingsPage';
import RecommendationsPage from './sections/RecommendationsPage';
import ScopePage from './sections/ScopePage';
import ProjectObjectivesPage from './sections/ProjectObjectivesPage';
import SummaryOfFindingsPage from './sections/SummaryOfFindingsPage';
import VulnerabilityRatingDefinitionsPage from './sections/VulnerabilityRatingDefinitionsPage';
import KeyFindingsListPage from './sections/KeyFindingsListPage';
import DetailedFindingsCoverPage from './sections/DetailedFindingsCoverPage';
import DetailedFindingsBySeverityPage from './sections/DetailedFindingsBySeverityPage';
import AppendixOwaspRiskRatingPages from './sections/AppendixOwaspRiskRatingPages';
import MethodologyPage from './sections/MethodologyPage';
import NetworkMethodologyPage from './sections/NetworkMethodologyPage';
import MobileMethodologyPage from './sections/MobileMethodologyPage';
import { ReportData } from '../types/report.types';
import { SectionPageProvider, useSectionPages } from './SectionPageContext';

// Helper function for disclaimer content (copied from original)
const processDisclaimerContent = (content: string) => {
  if (!content) return [];
  let processed = content
  .replace(/\\n/g, '\n')
  .replace(/\\s+/g, ' ')
    .replace(/<p[^>]*>/gi, '')
    .replace(/<\/p>/gi, '\n\n')
  .replace(/<br\s*\/?/gi, ' ')
    .replace(/<[^>]*>/g, '')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
  .replace(/\s+/g, ' ')
    .trim();
  const sentences = processed.split(/\.\s+/).map(s => s.trim()).filter(s => s.length > 0);
  let paragraphs: string[] = [];
  let currentParagraph = '';
  for (let i = 0; i < sentences.length; i++) {
    const sentence = sentences[i];
    currentParagraph += sentence + '. ';
    if (sentence.includes('prior consent') || 
        sentence.includes('definitive risk analysis') || 
        sentence.includes('general public') ||
        i === sentences.length - 1) {
      paragraphs.push(currentParagraph.trim());
      currentParagraph = '';
    }
  }
  if (paragraphs.length !== 3) {
    const text = processed;
    const p1End = text.indexOf('prior consent.') + 'prior consent.'.length;
    const p2End = text.indexOf('definitive risk analysis.') + 'definitive risk analysis.'.length;
    if (p1End > 0 && p2End > p1End) {
      paragraphs = [
        text.substring(0, p1End).trim(),
        text.substring(p1End, p2End).trim(),
        text.substring(p2End).trim()
      ];
    }
  }
  return paragraphs;
};

interface FullReportTemplatePDFProps {
  reportData: ReportData;
  pieChartImage?: string;
  barChartImage?: string;
  processedFindings?: Record<string, any[]> | null;
  processing?: boolean;
}

const styles = StyleSheet.create({
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    textAlign: 'center',
    fontSize: 10,
    color: 'grey',
  },
});

const FullReportTemplatePDFContent: React.FC<FullReportTemplatePDFProps> = ({
  reportData,
  pieChartImage,
  barChartImage,
  processedFindings,
  processing,
}) => {
  const disclaimerParagraphs = processDisclaimerContent((reportData.disclaimer ?? '') || '');
  const { updateSectionPage } = useSectionPages();
  if (processing) {
    return (
      <Document>
        <Page>
          <Text>Loading...</Text>
        </Page>
      </Document>
    );
  }
  // Helper to render a page with footer
  const renderPageWithFooter = (children: React.ReactNode, key?: string | number) => (
    <React.Fragment key={key}>
      {React.cloneElement(children as React.ReactElement, {},
        <>
          {(children as React.ReactElement).props.children}
          <View style={styles.footer} fixed>
            <Text render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
          </View>
        </>
      )}
    </React.Fragment>
  );

  return (
    <Document>
      <CoverPage reportData={reportData} />
      <DisclaimerPage
        reportData={reportData}
        disclaimerParagraphs={disclaimerParagraphs}
        processDisclaimerContent={processDisclaimerContent}
        sectionId="Disclaimer"
      />
      <FullTableOfContentsPage reportData={reportData} />
      {/* Start adding footer from here */}
      {renderPageWithFooter(<DocumentReferencePage reportData={reportData} sectionId="DocumentReference" />, 'doc-ref')}
      {renderPageWithFooter(<ExecutiveSummaryPage
        reportData={reportData}
        pieChartImage={pieChartImage}
        barChartImage={barChartImage}
        sectionId="ExecutiveSummary"
      />, 'exec-summary')}
      {renderPageWithFooter(<KeyFindingsPage reportData={reportData} sectionId="KeyFindings" />, 'key-findings')}
      {renderPageWithFooter(<RecommendationsPage reportData={reportData} sectionId="Recommendations" />, 'recommendations')}
      {renderPageWithFooter(<ScopePage reportData={reportData} sectionId="Scope" />, 'scope')}
      {renderPageWithFooter(<ProjectObjectivesPage reportData={reportData} sectionId="ProjectObjectives" />, 'objectives')}
      {renderPageWithFooter(<SummaryOfFindingsPage
        reportData={reportData}
        pieChartImage={pieChartImage}
        barChartImage={barChartImage}
        sectionId="SummaryOfFindings"
      />, 'summary-findings')}
      {renderPageWithFooter(<VulnerabilityRatingDefinitionsPage reportData={reportData} sectionId="VulnerabilityRatingDefinitions" />, 'vuln-defs')}
      {renderPageWithFooter(<KeyFindingsListPage reportData={reportData} sectionId="KeyFindingsList" />, 'key-findings-list')}
      {/* Render these directly, as they have their own footers */}
      <DetailedFindingsCoverPage documentNumber={reportData.document_number} branding_logo={reportData.branding_logo} sectionId="DetailedFindingsCover" />
      {['CRITICAL', 'HIGH', 'MEDIUM', 'LOW'].map(severity =>
        <DetailedFindingsBySeverityPage
          key={severity}
          severity={severity.charAt(0) + severity.slice(1).toLowerCase()}
          findings={processedFindings?.[severity] || []}
          documentNumber={reportData.document_number}
          registerSectionPage={updateSectionPage}
          sectionId={`DetailedFindings_${severity.charAt(0) + severity.slice(1).toLowerCase()}`}
        />
      )}
      {AppendixOwaspRiskRatingPages({ documentNumber: reportData.document_number, registerSectionPage: updateSectionPage, sectionId: 'AppendixOwaspRiskRating_Page1' })
        .filter(React.isValidElement)
        .map((page, idx) => React.cloneElement(page, { key: `appendix-${idx}` }))}
      {(reportData.methodology?.web) && (
        <MethodologyPage documentNumber={reportData.document_number} registerSectionPage={updateSectionPage} sectionId="Methodology" />
      )}
      {(reportData.methodology?.network) && (
        <NetworkMethodologyPage documentNumber={reportData.document_number} registerSectionPage={updateSectionPage} sectionId="NetworkMethodology" />
      )}
      {(reportData.methodology?.mobile) && (
        <MobileMethodologyPage documentNumber={reportData.document_number} registerSectionPage={updateSectionPage} sectionId="MobileMethodology" />
      )}
    </Document>
  );
};

const FullReportTemplatePDF: React.FC<FullReportTemplatePDFProps> = (props) => (
  <SectionPageProvider>
    <FullReportTemplatePDFContent {...props} />
  </SectionPageProvider>
);

export default FullReportTemplatePDF; 