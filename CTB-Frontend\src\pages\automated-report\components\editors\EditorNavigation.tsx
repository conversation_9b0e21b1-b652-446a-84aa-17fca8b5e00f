import React, { useRef, useEffect } from 'react';

interface EditorNavigationProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

const sections = [
  { id: 'cover', label: 'Cover Page', icon: '📄' },
  { id: 'disclaimer', label: 'Disclaimer', icon: '🔍' },
  { id: 'document_reference', label: 'Document Reference', icon: '📋' },
  { id: 'executive_summary', label: 'Executive Summary', icon: '📝' },
  { id: 'scope', label: 'Scope', icon: '🎯' },
  { id: 'project_objectives', label: 'Project Objectives', icon: '🎯' },
  { id: 'findings_summary', label: 'Findings Summary', icon: '📊' },
  { id: 'vulnerability_ratings', label: 'Vulnerability Ratings', icon: '🔍' },
  { id: 'key_findings', label: 'Key Findings', icon: '🔑' },
  { id: 'critical_findings', label: 'Critical Findings', icon: '⚠️' },
  { id: 'high_findings', label: 'High Findings', icon: '🔴' },
  { id: 'medium_findings', label: 'Medium Findings', icon: '🟡' },
  { id: 'low_findings', label: 'Low Findings', icon: '🟢' },
  { id: 'methodology', label: 'Methodology', icon: '🔬' },
  { id: 'findings', label: 'Findings', icon: '🔍' },
  { id: 'recommendations', label: 'Recommendations', icon: '💡' },
  { id: 'conclusion', label: 'Conclusion', icon: '🏁' }
];

const EditorNavigation: React.FC<EditorNavigationProps> = ({ activeSection, onSectionChange }) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const activeButtonRef = useRef<HTMLButtonElement>(null);

  // Auto-scroll to active section
  useEffect(() => {
    if (activeButtonRef.current && scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const button = activeButtonRef.current;
      
      const containerWidth = container.offsetWidth;
      const buttonLeft = button.offsetLeft;
      const buttonWidth = button.offsetWidth;
      
      // Calculate the scroll position to center the active button
      const scrollLeft = buttonLeft - (containerWidth / 2) + (buttonWidth / 2);
      
      // Smooth scroll to the calculated position
      container.scrollTo({
        left: scrollLeft,
        behavior: 'smooth'
      });
    }
  }, [activeSection]);

  // Handle wheel events for horizontal scrolling
  const handleWheel = (e: React.WheelEvent) => {
    if (scrollContainerRef.current) {
      e.preventDefault();
      scrollContainerRef.current.scrollLeft += e.deltaY;
    }
  };

  return (
    <div className="bg-white border-b border-gray-200 shadow-sm">
      <div 
        ref={scrollContainerRef}
        onWheel={handleWheel}
        className="relative max-w-full overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent hover:scrollbar-thumb-gray-400 transition-all duration-300"
        style={{
          scrollbarWidth: 'thin',
          scrollbarColor: '#D1D5DB transparent'
        }}
      >
        <div className="flex space-x-2 p-3 min-w-max">
          {sections.map((section) => (
            <button
              key={section.id}
              ref={section.id === activeSection ? activeButtonRef : null}
              onClick={() => onSectionChange(section.id)}
              className={`group flex items-center px-4 py-2.5 rounded-lg transition-all duration-200 whitespace-nowrap text-sm ${
                activeSection === section.id
                  ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium shadow-md transform scale-105'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 border border-transparent hover:border-gray-200'
              }`}
            >
              <span className={`mr-2 text-lg transition-transform duration-200 ${
                activeSection === section.id ? 'transform scale-110' : 'group-hover:scale-110'
              }`}>
                {section.icon}
              </span>
              <span className={`font-medium ${
                activeSection === section.id ? 'text-white' : 'text-gray-700'
              }`}>
                {section.label}
              </span>
              {activeSection === section.id && (
                <span className="ml-2 w-1.5 h-1.5 bg-white rounded-full animate-pulse" />
              )}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default EditorNavigation; 