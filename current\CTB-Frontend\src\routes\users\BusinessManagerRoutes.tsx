import { Route, Routes } from "react-router-dom";
import ErrorPage from "../../pages/ErrorPage";
import BusinessProfile from "../../pages/business/BusinessProfile";
import EnhancedBusinessDashboard from "../../pages/business/EnhancedBusinessDashboard";
import Assistant from "../../pages/assistant/Assistant";
import SolidityScan from "../../pages/business/SolidityScan";
import ProgramEditor from "../../pages/programs/ProgramEditor";
import PaginatedTransactions from "../../pages/payments/PaginatedTransactions";
import PaginatedReports from "../../pages/reports/PaginatedReports";
import ReportPage from "../../pages/reports/ReportPage";
import PaginatedPrograms from "../../pages/programs/PaginatedPrograms";
import ProgramPage from "../../pages/programs/ProgramPage";
import RetestManagement from "../../pages/retests/RetestManagent";
import RetestPage from "../../pages/retests/RetestPage";

const BusinessManagerRoutes = () => {
  return (
    <Routes>
      <Route index element={<EnhancedBusinessDashboard />} />

      <Route path="programs/:id/edit" element={<ProgramEditor />} />
      <Route path="/create-program" element={<ProgramEditor />} />
      {/* <Route path="invitation" element={<InvitationsManager />} /> */}

      <Route path="profile/*" element={<BusinessProfile />} />
      <Route path="reports" element={<PaginatedReports />} />
      <Route path="reports/:id" element={<ReportPage />} />

      <Route path="programs" element={<PaginatedPrograms />} />
      <Route path="programs/:id" element={<ProgramPage />} />

      <Route path="/retests" element={<RetestManagement />} />
      <Route path="/retests/:retest_id" element={<RetestPage />} />

      <Route path="assistant/*" element={<Assistant />} />
      <Route path="solidity-scan" element={<SolidityScan />} />

      <Route path="payments/*" element={<PaginatedTransactions />} />

      <Route path="/*" element={<ErrorPage />} />
    </Routes>
  );
};

export default BusinessManagerRoutes;
