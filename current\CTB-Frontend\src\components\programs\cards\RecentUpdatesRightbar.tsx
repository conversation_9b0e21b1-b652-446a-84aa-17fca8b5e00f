import { useState } from "react";
import { BsClockHistory } from "react-icons/bs";

type ReportsSummary = {
  reports_received_90_days: number;
  reports_resolved: number;
  assets_in_scope: number;
} | null;

type RecentUpdatesProps = {
  expanded: boolean;
  toggleExpanded: () => void;
  loadingReports: boolean;
  reportSummary: ReportsSummary;
};

const RecentUpdates: React.FC<RecentUpdatesProps> = ({
  expanded,
  toggleExpanded,
  loadingReports,
  reportSummary,
}) => {
  const [highlight, setHighlight] = useState(false);

  const handleToggle = () => {
    toggleExpanded();
    setHighlight(true);
 

    setTimeout(() => {
      setHighlight(false);
    }, 1500);
  };

  return (
    <div className="flex flex-col mb-4">
      <div className="flex items-center   group relative"> 
        <button
        className={`rounded-full hover:bg-gray-100 transition-colors relative ${expanded ? "" : "ms-2.5 -mt-1"}`}
        onClick={handleToggle}
      >
        <BsClockHistory className="text-ctb-blue-600 text-xl cursor-pointer font-bolder ms-1" />
        <span
          className={`absolute right-7 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 whitespace-nowrap transition-opacity duration-200 pointer-events-none group-hover:opacity-100 ${expanded ? "hidden" : ""}`}
        >
          Recent Updates
        </span>
      </button>
        <h2
          className={`text-sm font-semibold text-blue-600 capitalize transition-all duration-[300ms] mt-0.5 ${
            expanded
              ? `opacity-100  px-2 py-1 rounded transition-all duration-500  ${
                  highlight ? "border-b-2 border-blue-600    text-lg" : "  "
                }`
              : "opacity-0 hidden"
          }`}
        >
          Recent Updates
        </h2>
      </div>
      {loadingReports ? (
        <p className="text-gray-600 text-xs">Loading...</p>
      ) : reportSummary ? (
        <ul className={`mt-2 text-gray-800 text-[12px] space-y-3 transition-opacity duration-300 ml-2 ${expanded ? "opacity-100" : "opacity-0 hidden"}`}>
          <li className="flex justify-between gap-12 mt-2">
            <span>Reports received | 90 </span>
            <span className="mr-4">{reportSummary.reports_received_90_days}</span>
          </li>
          <li className="flex justify-between">
            <span>Reports resolved</span>
            <span className="mr-4">{reportSummary.reports_resolved}</span>
          </li>
          <li className="flex justify-between">
            <span>Assets In Scope</span>
            <span className="mr-4">{reportSummary.assets_in_scope}</span>
          </li>
        </ul>
      ) : (
        <p className="text-gray-600 text-xs mt-2">No data available</p>
      )}
    </div>
  );
};

export default RecentUpdates;
