{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\jira-old\\\\CTB-Frontend\\\\src\\\\pages\\\\automated-report\\\\components\\\\sections\\\\DetailedFindingsBySeverityPage.tsx\";\nimport React from 'react';\nimport { Page, View, Text, StyleSheet } from '@react-pdf/renderer';\nimport { renderPdfElements } from '../pdfHtmlImageUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// Utility to sanitize HTML by removing only dangerous attributes while preserving styling\nfunction sanitizeHtml(html) {\n  // Remove only dangerous attributes, keep style attributes for formatting\n  return html.replace(/(on\\w+|javascript:|data-(?!language))=\"[^\"]*\"/gi, '');\n}\n\n// Helper to format date as dd-mm-yyyy\nfunction formatDateToDDMMYYYY(dateString) {\n  if (!dateString) return '';\n  const date = new Date(dateString);\n  if (isNaN(date.getTime())) return dateString;\n  const day = String(date.getDate()).padStart(2, '0');\n  const month = String(date.getMonth() + 1).padStart(2, '0');\n  const year = date.getFullYear();\n  return `${day}-${month}-${year}`;\n}\n\n// Helper to ensure the field is always an array of PdfElement\nfunction ensurePdfElements(field) {\n  if (!field) return [];\n  if (Array.isArray(field) && field.length > 0 && typeof field[0] === 'object' && 'type' in field[0]) {\n    // Already parsed\n    return field;\n  }\n  if (typeof field === 'string') {\n    // Handle HTML content properly\n    if (field.trim().startsWith('<')) {\n      const cleanHtml = sanitizeHtml(field);\n      // Use synchronous HTML parsing to preserve formatting\n      return parseHtmlToElementsSync(cleanHtml);\n    }\n    return [{\n      type: 'text',\n      content: field\n    }];\n  }\n  return [{\n    type: 'text',\n    content: String(field)\n  }];\n}\n\n// Synchronous HTML parser for PDF rendering\nfunction parseHtmlToElementsSync(htmlString) {\n  if (!htmlString) return [];\n  if (typeof window === 'undefined' || typeof DOMParser === 'undefined') {\n    // SSR fallback: return as text\n    return [{\n      type: 'text',\n      content: htmlString.replace(/<[^>]+>/g, '')\n    }];\n  }\n  const parser = new DOMParser();\n  const doc = parser.parseFromString(htmlString, 'text/html');\n  const elements = [];\n  const processNode = (node, container = elements) => {\n    var _el$textContent$trim, _el$textContent, _el$textContent$trim2, _el$textContent2, _el$textContent$trim3, _el$textContent3, _el$textContent$trim4, _el$textContent4, _el$textContent$trim5, _el$textContent5;\n    if (node.nodeType === Node.TEXT_NODE) {\n      var _node$textContent$tri, _node$textContent;\n      const text = (_node$textContent$tri = (_node$textContent = node.textContent) === null || _node$textContent === void 0 ? void 0 : _node$textContent.trim()) !== null && _node$textContent$tri !== void 0 ? _node$textContent$tri : '';\n      if (text) {\n        container.push({\n          type: 'text',\n          content: text\n        });\n      }\n    } else if (node.nodeType === Node.ELEMENT_NODE) {\n      const el = node;\n      switch (el.tagName.toLowerCase()) {\n        case 'p':\n          {\n            const paragraph = {\n              type: 'paragraph',\n              children: []\n            };\n            for (const child of Array.from(el.childNodes)) {\n              processNode(child, paragraph.children);\n            }\n            if (paragraph.children.length > 0) {\n              container.push(paragraph);\n            }\n            break;\n          }\n        case 'h1':\n        case 'h2':\n        case 'h3':\n        case 'h4':\n        case 'h5':\n        case 'h6':\n          container.push({\n            type: 'heading',\n            level: parseInt(el.tagName.substring(1)),\n            content: (_el$textContent$trim = (_el$textContent = el.textContent) === null || _el$textContent === void 0 ? void 0 : _el$textContent.trim()) !== null && _el$textContent$trim !== void 0 ? _el$textContent$trim : ''\n          });\n          break;\n        case 'strong':\n        case 'b':\n          container.push({\n            type: 'text',\n            content: (_el$textContent$trim2 = (_el$textContent2 = el.textContent) === null || _el$textContent2 === void 0 ? void 0 : _el$textContent2.trim()) !== null && _el$textContent$trim2 !== void 0 ? _el$textContent$trim2 : '',\n            style: 'bold'\n          });\n          break;\n        case 'em':\n        case 'i':\n          container.push({\n            type: 'text',\n            content: (_el$textContent$trim3 = (_el$textContent3 = el.textContent) === null || _el$textContent3 === void 0 ? void 0 : _el$textContent3.trim()) !== null && _el$textContent$trim3 !== void 0 ? _el$textContent$trim3 : '',\n            style: 'italic'\n          });\n          break;\n        case 'ul':\n        case 'ol':\n          {\n            const list = {\n              type: 'list',\n              ordered: el.tagName.toLowerCase() === 'ol',\n              items: []\n            };\n            for (const li of Array.from(el.querySelectorAll('li'))) {\n              var _li$textContent$trim, _li$textContent;\n              list.items.push((_li$textContent$trim = (_li$textContent = li.textContent) === null || _li$textContent === void 0 ? void 0 : _li$textContent.trim()) !== null && _li$textContent$trim !== void 0 ? _li$textContent$trim : '');\n            }\n            if (list.items.length > 0) {\n              container.push(list);\n            }\n            break;\n          }\n        case 'pre':\n        case 'code':\n          // Handle code blocks - preserve content but mark as code\n          container.push({\n            type: 'code',\n            content: (_el$textContent$trim4 = (_el$textContent4 = el.textContent) === null || _el$textContent4 === void 0 ? void 0 : _el$textContent4.trim()) !== null && _el$textContent$trim4 !== void 0 ? _el$textContent$trim4 : '',\n            language: el.getAttribute('data-language') || 'code'\n          });\n          break;\n        case 'blockquote':\n          container.push({\n            type: 'blockquote',\n            content: (_el$textContent$trim5 = (_el$textContent5 = el.textContent) === null || _el$textContent5 === void 0 ? void 0 : _el$textContent5.trim()) !== null && _el$textContent$trim5 !== void 0 ? _el$textContent$trim5 : ''\n          });\n          break;\n        case 'img':\n          {\n            const src = el.getAttribute('src');\n            const width = el.getAttribute('width');\n            const height = el.getAttribute('height');\n            container.push({\n              type: 'image',\n              src: src,\n              alt: el.getAttribute('alt') || '',\n              width: width !== null && width !== void 0 ? width : undefined,\n              height: height !== null && height !== void 0 ? height : undefined\n            });\n            break;\n          }\n        case 'br':\n          container.push({\n            type: 'break'\n          });\n          break;\n        default:\n          // For other elements, process children\n          for (const child of Array.from(el.childNodes)) {\n            processNode(child, container);\n          }\n          break;\n      }\n    }\n  };\n  for (const child of Array.from(doc.body.childNodes)) {\n    processNode(child);\n  }\n  return elements;\n}\nconst styles = StyleSheet.create({\n  footer: {\n    position: 'absolute',\n    bottom: 30,\n    left: 0,\n    right: 0,\n    fontSize: 10,\n    color: 'grey',\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    paddingHorizontal: 24\n  },\n  footerLeft: {\n    textAlign: 'left',\n    flex: 1\n  },\n  footerRight: {\n    textAlign: 'right',\n    flex: 1\n  }\n});\nconst DetailedFindingsBySeverityPage = ({\n  severity,\n  findings,\n  documentNumber,\n  registerSectionPage,\n  sectionId\n}) => {\n  var _severityColors$color, _severityColors$color2;\n  const severityColors = {\n    Critical: {\n      text: '#800000',\n      badge: '#800000'\n    },\n    // maroon\n    High: {\n      text: '#dc2626',\n      badge: '#dc2626'\n    },\n    // red\n    Medium: {\n      text: '#fbbf24',\n      badge: '#fbbf24'\n    },\n    // yellow\n    Low: {\n      text: '#10b981',\n      badge: '#10b981'\n    } // green\n  };\n  const colorKey = severity.charAt(0).toUpperCase() + severity.slice(1).toLowerCase();\n  const titleColor = ((_severityColors$color = severityColors[colorKey]) === null || _severityColors$color === void 0 ? void 0 : _severityColors$color.text) || '#2563eb';\n  const badgeColor = ((_severityColors$color2 = severityColors[colorKey]) === null || _severityColors$color2 === void 0 ? void 0 : _severityColors$color2.badge) || '#2563eb';\n\n  // If there are no findings, render nothing\n  if (!findings || findings.length === 0) {\n    return null;\n  }\n  // Render each finding on a new Page\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: findings.map((finding, idx) => {\n      // Generate default abbreviation if missing\n      const severityCounts = {};\n      findings.slice(0, idx + 1).forEach(f => {\n        const sev = (f.severity_category || severity || '').toLowerCase();\n        severityCounts[sev] = (severityCounts[sev] || 0) + 1;\n      });\n      const sev = (finding.severity_category || severity || '').toLowerCase();\n      let abbrPrefix = '';\n      if (sev === 'critical') abbrPrefix = 'C';else if (sev === 'high') abbrPrefix = 'H';else if (sev === 'medium') abbrPrefix = 'M';else if (sev === 'low') abbrPrefix = 'L';else abbrPrefix = 'X';\n      const defaultAbbr = abbrPrefix + severityCounts[sev];\n      const abbr = finding.abbreviation && finding.abbreviation.trim() ? finding.abbreviation : defaultAbbr;\n      return /*#__PURE__*/_jsxDEV(Page, {\n        size: \"A4\",\n        id: idx === 0 ? sectionId : undefined,\n        style: {\n          flexDirection: 'column',\n          backgroundColor: '#ffffff',\n          padding: '20mm 8mm',\n          fontFamily: 'Helvetica',\n          fontSize: 12\n        },\n        children: [registerSectionPage && idx === 0 && /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            display: 'none',\n            fontSize: 1\n          },\n          render: ({\n            pageNumber\n          }) => {\n            registerSectionPage(`DetailedFindings_${severity}`, pageNumber);\n            return '';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(View, {\n          style: {\n            paddingHorizontal: 24,\n            flexDirection: 'column',\n            flex: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(View, {\n            style: {\n              flex: 1\n            },\n            children: [idx === 0 && /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                fontSize: 16,\n                fontWeight: 'bold',\n                color: titleColor,\n                marginBottom: 12,\n                lineHeight: 1.4\n              },\n              children: [severity.toUpperCase(), \" SEVERITY FINDINGS\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(View, {\n              style: {\n                backgroundColor: '#fff',\n                borderWidth: 1,\n                borderColor: '#e5e7eb',\n                borderRadius: 8,\n                padding: 12,\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(View, {\n                style: {\n                  flexDirection: 'row',\n                  alignItems: 'center',\n                  marginBottom: 10\n                },\n                children: [/*#__PURE__*/_jsxDEV(View, {\n                  style: {\n                    backgroundColor: badgeColor,\n                    color: '#fff',\n                    borderRadius: 4,\n                    paddingHorizontal: 8,\n                    paddingVertical: 2,\n                    marginRight: 10\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    style: {\n                      color: '#fff',\n                      fontWeight: 'bold',\n                      fontSize: 12,\n                      lineHeight: 1.4\n                    },\n                    children: abbr\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    fontWeight: 'bold',\n                    fontSize: 14,\n                    color: '#1f2937',\n                    maxWidth: '80%',\n                    lineHeight: 1.4,\n                    textAlign: 'justify'\n                  },\n                  children: finding.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this), finding.scope && /*#__PURE__*/_jsxDEV(View, {\n                style: {\n                  marginBottom: 12\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    fontWeight: 'bold',\n                    fontSize: 13,\n                    color: '#374151',\n                    marginBottom: 6,\n                    lineHeight: 1.4\n                  },\n                  children: \"I. Vulnerable Asset / Target\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    fontSize: 12,\n                    color: '#6b7280',\n                    lineHeight: 1.4,\n                    textAlign: 'justify'\n                  },\n                  children: finding.scope\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 21\n              }, this), finding.description && /*#__PURE__*/_jsxDEV(View, {\n                style: {\n                  marginBottom: 12\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    fontWeight: 'bold',\n                    fontSize: 13,\n                    color: '#374151',\n                    marginBottom: 6,\n                    lineHeight: 1.4\n                  },\n                  children: \"II. Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(View, {\n                  style: {\n                    fontSize: 12,\n                    lineHeight: 1.4,\n                    textAlign: 'justify'\n                  },\n                  children: renderPdfElements(ensurePdfElements(finding.description))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 21\n              }, this), finding.instructions && /*#__PURE__*/_jsxDEV(View, {\n                style: {\n                  marginBottom: 12\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    fontWeight: 'bold',\n                    fontSize: 13,\n                    color: '#374151',\n                    marginBottom: 6,\n                    lineHeight: 1.4\n                  },\n                  children: \"III. Steps to Reproduce\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(View, {\n                  style: {\n                    fontSize: 12,\n                    lineHeight: 1.4,\n                    textAlign: 'justify'\n                  },\n                  children: renderPdfElements(ensurePdfElements(finding.instructions))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 21\n              }, this), finding.impact && /*#__PURE__*/_jsxDEV(View, {\n                style: {\n                  marginBottom: 12\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    fontWeight: 'bold',\n                    fontSize: 13,\n                    color: '#374151',\n                    marginBottom: 6,\n                    lineHeight: 1.4\n                  },\n                  children: \"IV. Impact\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(View, {\n                  style: {\n                    fontSize: 12,\n                    lineHeight: 1.4,\n                    textAlign: 'justify'\n                  },\n                  children: renderPdfElements(ensurePdfElements(finding.impact))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 21\n              }, this), finding.fix && /*#__PURE__*/_jsxDEV(View, {\n                style: {\n                  marginBottom: 12\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    fontWeight: 'bold',\n                    fontSize: 13,\n                    color: '#374151',\n                    marginBottom: 6,\n                    lineHeight: 1.4\n                  },\n                  children: \"V. Recommendation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(View, {\n                  style: {\n                    fontSize: 12,\n                    lineHeight: 1.4,\n                    textAlign: 'justify'\n                  },\n                  children: renderPdfElements(ensurePdfElements(finding.fix))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(View, {\n          style: styles.footer,\n          fixed: true,\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            style: styles.footerLeft,\n            children: documentNumber || 'Document Number'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            style: styles.footerRight,\n            render: ({\n              pageNumber,\n              totalPages\n            }) => `${pageNumber} / ${totalPages}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this)]\n      }, idx, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 11\n      }, this);\n    })\n  }, void 0, false);\n};\n_c = DetailedFindingsBySeverityPage;\nexport default DetailedFindingsBySeverityPage;\nvar _c;\n$RefreshReg$(_c, \"DetailedFindingsBySeverityPage\");", "map": {"version": 3, "names": ["React", "Page", "View", "Text", "StyleSheet", "renderPdfElements", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "sanitizeHtml", "html", "replace", "formatDateToDDMMYYYY", "dateString", "date", "Date", "isNaN", "getTime", "day", "String", "getDate", "padStart", "month", "getMonth", "year", "getFullYear", "ensurePdfElements", "field", "Array", "isArray", "length", "trim", "startsWith", "cleanHtml", "parseHtmlToElementsSync", "type", "content", "htmlString", "window", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parser", "doc", "parseFromString", "elements", "processNode", "node", "container", "_el$textContent$trim", "_el$textContent", "_el$textContent$trim2", "_el$textContent2", "_el$textContent$trim3", "_el$textContent3", "_el$textContent$trim4", "_el$textContent4", "_el$textContent$trim5", "_el$textContent5", "nodeType", "Node", "TEXT_NODE", "_node$textContent$tri", "_node$textContent", "text", "textContent", "push", "ELEMENT_NODE", "el", "tagName", "toLowerCase", "paragraph", "children", "child", "from", "childNodes", "level", "parseInt", "substring", "style", "list", "ordered", "items", "li", "querySelectorAll", "_li$textContent$trim", "_li$textContent", "language", "getAttribute", "src", "width", "height", "alt", "undefined", "body", "styles", "create", "footer", "position", "bottom", "left", "right", "fontSize", "color", "flexDirection", "justifyContent", "alignItems", "paddingHorizontal", "footerLeft", "textAlign", "flex", "footerRight", "DetailedFindingsBySeverityPage", "severity", "findings", "documentNumber", "registerSectionPage", "sectionId", "_severityColors$color", "_severityColors$color2", "severityColors", "Critical", "badge", "High", "Medium", "Low", "colorKey", "char<PERSON>t", "toUpperCase", "slice", "titleColor", "badgeColor", "map", "finding", "idx", "severityCounts", "for<PERSON>ach", "f", "sev", "severity_category", "abbrPrefix", "defaultAbbr", "abbr", "abbreviation", "size", "id", "backgroundColor", "padding", "fontFamily", "display", "render", "pageNumber", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontWeight", "marginBottom", "lineHeight", "borderWidth", "borderColor", "borderRadius", "paddingVertical", "marginRight", "max<PERSON><PERSON><PERSON>", "title", "scope", "description", "instructions", "impact", "fix", "fixed", "totalPages", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/jira-old/CTB-Frontend/src/pages/automated-report/components/sections/DetailedFindingsBySeverityPage.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Page, View, Text, StyleSheet } from '@react-pdf/renderer';\r\nimport { renderPdfElements, renderPdfElementsPlainText, parseHtmlToElements, PdfElement } from '../pdfHtmlImageUtils';\r\n\r\ninterface DetailedFindingsBySeverityPageProps {\r\n  severity: string;\r\n  findings: any[];\r\n  documentNumber?: string;\r\n}\r\n\r\n// Utility to sanitize HTML by removing only dangerous attributes while preserving styling\r\nfunction sanitizeHtml(html: string): string {\r\n  // Remove only dangerous attributes, keep style attributes for formatting\r\n  return html.replace(/(on\\w+|javascript:|data-(?!language))=\"[^\"]*\"/gi, '');\r\n}\r\n\r\n// Helper to format date as dd-mm-yyyy\r\nfunction formatDateToDDMMYYYY(dateString: string): string {\r\n  if (!dateString) return '';\r\n  const date = new Date(dateString);\r\n  if (isNaN(date.getTime())) return dateString;\r\n  const day = String(date.getDate()).padStart(2, '0');\r\n  const month = String(date.getMonth() + 1).padStart(2, '0');\r\n  const year = date.getFullYear();\r\n  return `${day}-${month}-${year}`;\r\n}\r\n\r\n// Helper to ensure the field is always an array of PdfElement\r\nfunction ensurePdfElements(field: any): PdfElement[] {\r\n  if (!field) return [];\r\n  if (Array.isArray(field) && field.length > 0 && typeof field[0] === 'object' && 'type' in field[0]) {\r\n    // Already parsed\r\n    return field;\r\n  }\r\n  if (typeof field === 'string') {\r\n    // Handle HTML content properly\r\n    if (field.trim().startsWith('<')) {\r\n      const cleanHtml = sanitizeHtml(field);\r\n      // Use synchronous HTML parsing to preserve formatting\r\n      return parseHtmlToElementsSync(cleanHtml);\r\n    }\r\n    return [{ type: 'text', content: field }];\r\n  }\r\n  return [{ type: 'text', content: String(field) }];\r\n}\r\n\r\n// Synchronous HTML parser for PDF rendering\r\nfunction parseHtmlToElementsSync(htmlString: string): PdfElement[] {\r\n  if (!htmlString) return [];\r\n  if (typeof window === 'undefined' || typeof DOMParser === 'undefined') {\r\n    // SSR fallback: return as text\r\n    return [{ type: 'text', content: htmlString.replace(/<[^>]+>/g, '') }];\r\n  }\r\n\r\n  const parser = new DOMParser();\r\n  const doc = parser.parseFromString(htmlString, 'text/html');\r\n  const elements: PdfElement[] = [];\r\n\r\n  const processNode = (node: Node, container: PdfElement[] = elements): void => {\r\n    if (node.nodeType === Node.TEXT_NODE) {\r\n      const text = node.textContent?.trim() ?? '';\r\n      if (text) {\r\n        container.push({ type: 'text', content: text });\r\n      }\r\n    } else if (node.nodeType === Node.ELEMENT_NODE) {\r\n      const el = node as HTMLElement;\r\n      switch (el.tagName.toLowerCase()) {\r\n        case 'p': {\r\n          const paragraph: PdfElement = { type: 'paragraph', children: [] };\r\n          for (const child of Array.from(el.childNodes)) {\r\n            processNode(child, paragraph.children);\r\n          }\r\n          if (paragraph.children.length > 0) {\r\n            container.push(paragraph);\r\n          }\r\n          break;\r\n        }\r\n        case 'h1':\r\n        case 'h2':\r\n        case 'h3':\r\n        case 'h4':\r\n        case 'h5':\r\n        case 'h6':\r\n          container.push({\r\n            type: 'heading',\r\n            level: parseInt(el.tagName.substring(1)),\r\n            content: el.textContent?.trim() ?? ''\r\n          });\r\n          break;\r\n        case 'strong':\r\n        case 'b':\r\n          container.push({\r\n            type: 'text',\r\n            content: el.textContent?.trim() ?? '',\r\n            style: 'bold'\r\n          });\r\n          break;\r\n        case 'em':\r\n        case 'i':\r\n          container.push({\r\n            type: 'text',\r\n            content: el.textContent?.trim() ?? '',\r\n            style: 'italic'\r\n          });\r\n          break;\r\n        case 'ul':\r\n        case 'ol': {\r\n          const list: PdfElement = {\r\n            type: 'list',\r\n            ordered: el.tagName.toLowerCase() === 'ol',\r\n            items: []\r\n          };\r\n          for (const li of Array.from(el.querySelectorAll('li'))) {\r\n            list.items.push(li.textContent?.trim() ?? '');\r\n          }\r\n          if (list.items.length > 0) {\r\n            container.push(list);\r\n          }\r\n          break;\r\n        }\r\n        case 'pre':\r\n        case 'code':\r\n          // Handle code blocks - preserve content but mark as code\r\n          container.push({\r\n            type: 'code',\r\n            content: el.textContent?.trim() ?? '',\r\n            language: el.getAttribute('data-language') || 'code'\r\n          } as any);\r\n          break;\r\n        case 'blockquote':\r\n          container.push({\r\n            type: 'blockquote',\r\n            content: el.textContent?.trim() ?? ''\r\n          } as any);\r\n          break;\r\n        case 'img': {\r\n          const src = el.getAttribute('src');\r\n          const width = el.getAttribute('width');\r\n          const height = el.getAttribute('height');\r\n          container.push({\r\n            type: 'image',\r\n            src: src,\r\n            alt: el.getAttribute('alt') || '',\r\n            width: width ?? undefined,\r\n            height: height ?? undefined,\r\n          });\r\n          break;\r\n        }\r\n        case 'br':\r\n          container.push({ type: 'break' });\r\n          break;\r\n        default:\r\n          // For other elements, process children\r\n          for (const child of Array.from(el.childNodes)) {\r\n            processNode(child, container);\r\n          }\r\n          break;\r\n      }\r\n    }\r\n  };\r\n\r\n  for (const child of Array.from(doc.body.childNodes)) {\r\n    processNode(child);\r\n  }\r\n  return elements;\r\n}\r\n\r\nconst styles = StyleSheet.create({\r\n  footer: {\r\n    position: 'absolute',\r\n    bottom: 30,\r\n    left: 0,\r\n    right: 0,\r\n    fontSize: 10,\r\n    color: 'grey',\r\n    flexDirection: 'row',\r\n    justifyContent: 'space-between',\r\n    alignItems: 'center',\r\n    paddingHorizontal: 24,\r\n  },\r\n  footerLeft: {\r\n    textAlign: 'left',\r\n    flex: 1,\r\n  },\r\n  footerRight: {\r\n    textAlign: 'right',\r\n    flex: 1,\r\n  },\r\n});\r\n\r\nconst DetailedFindingsBySeverityPage: React.FC<DetailedFindingsBySeverityPageProps & { registerSectionPage?: (section: string, page: number) => void, sectionId?: string }> = ({ severity, findings, documentNumber, registerSectionPage, sectionId }) => {\r\n  const severityColors: Record<string, { text: string; badge: string }> = {\r\n    Critical: { text: '#800000', badge: '#800000' }, // maroon\r\n    High: { text: '#dc2626', badge: '#dc2626' },     // red\r\n    Medium: { text: '#fbbf24', badge: '#fbbf24' },   // yellow\r\n    Low: { text: '#10b981', badge: '#10b981' },      // green\r\n  };\r\n  const colorKey = (severity.charAt(0).toUpperCase() + severity.slice(1).toLowerCase()) as keyof typeof severityColors;\r\n  const titleColor = severityColors[colorKey]?.text || '#2563eb';\r\n  const badgeColor = severityColors[colorKey]?.badge || '#2563eb';\r\n\r\n  // If there are no findings, render nothing\r\n  if (!findings || findings.length === 0) {\r\n    return null;\r\n  }\r\n  // Render each finding on a new Page\r\n  return (\r\n    <>\r\n      {findings.map((finding, idx) => {\r\n        // Generate default abbreviation if missing\r\n        const severityCounts: Record<string, number> = {};\r\n        findings.slice(0, idx + 1).forEach((f) => {\r\n          const sev = (f.severity_category || severity || '').toLowerCase();\r\n          severityCounts[sev] = (severityCounts[sev] || 0) + 1;\r\n        });\r\n        const sev = (finding.severity_category || severity || '').toLowerCase();\r\n        let abbrPrefix = '';\r\n        if (sev === 'critical') abbrPrefix = 'C';\r\n        else if (sev === 'high') abbrPrefix = 'H';\r\n        else if (sev === 'medium') abbrPrefix = 'M';\r\n        else if (sev === 'low') abbrPrefix = 'L';\r\n        else abbrPrefix = 'X';\r\n        const defaultAbbr = abbrPrefix + severityCounts[sev];\r\n        const abbr = finding.abbreviation && finding.abbreviation.trim() ? finding.abbreviation : defaultAbbr;\r\n        return (\r\n          <Page key={idx} size=\"A4\" id={idx === 0 ? sectionId : undefined} style={{\r\n            flexDirection: 'column',\r\n            backgroundColor: '#ffffff',\r\n            padding: '20mm 8mm',\r\n            fontFamily: 'Helvetica',\r\n            fontSize: 12,\r\n          }}>\r\n            {/* Hidden Text to register section page for TOC (only on first finding) */}\r\n            {registerSectionPage && idx === 0 && (\r\n              <Text style={{ display: 'none', fontSize: 1 }} render={({ pageNumber }) => {\r\n                registerSectionPage(`DetailedFindings_${severity}`, pageNumber);\r\n                return '';\r\n              }} />\r\n            )}\r\n            <View style={{ paddingHorizontal: 24, flexDirection: 'column', flex: 1 }}>\r\n              <View style={{ flex: 1}}>\r\n                {idx === 0 && (\r\n                  <Text style={{ fontSize: 16, fontWeight: 'bold', color: titleColor, marginBottom: 12, lineHeight: 1.4 }}>\r\n                    {severity.toUpperCase()} SEVERITY FINDINGS\r\n                  </Text>\r\n                )}\r\n                <View style={{ backgroundColor: '#fff', borderWidth: 1, borderColor: '#e5e7eb', borderRadius: 8, padding: 12, marginBottom: 16 }}>\r\n                  <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}>\r\n                    <View style={{ backgroundColor: badgeColor, color: '#fff', borderRadius: 4, paddingHorizontal: 8, paddingVertical: 2, marginRight: 10 }}>\r\n                      <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 12, lineHeight: 1.4 }}>{abbr}</Text>\r\n                    </View>\r\n                    <Text style={{\r\n                      fontWeight: 'bold',\r\n                      fontSize: 14,\r\n                      color: '#1f2937',\r\n                      maxWidth: '80%',\r\n                      lineHeight: 1.4,\r\n                      textAlign: 'justify'\r\n                    }}>{finding.title}</Text>\r\n                  </View>\r\n                  {finding.scope && (\r\n                    <View style={{ marginBottom: 12 }}>\r\n                      <Text style={{ fontWeight: 'bold', fontSize: 13, color: '#374151', marginBottom: 6, lineHeight: 1.4 }}>I. Vulnerable Asset / Target</Text>\r\n                      <Text style={{\r\n                        fontSize: 12,\r\n                        color: '#6b7280',\r\n                        lineHeight: 1.4,\r\n                        textAlign: 'justify'\r\n                      }}>{finding.scope}</Text>\r\n                    </View>\r\n                  )}\r\n                  {finding.description && (\r\n                    <View style={{ marginBottom: 12 }}>\r\n                      <Text style={{ fontWeight: 'bold', fontSize: 13, color: '#374151', marginBottom: 6, lineHeight: 1.4 }}>II. Description</Text>\r\n                      <View style={{\r\n                        fontSize: 12,\r\n                        lineHeight: 1.4,\r\n                        textAlign: 'justify'\r\n                      }}>{renderPdfElements(ensurePdfElements(finding.description))}</View>\r\n                    </View>\r\n                  )}\r\n                  {finding.instructions && (\r\n                    <View style={{ marginBottom: 12 }}>\r\n                      <Text style={{ fontWeight: 'bold', fontSize: 13, color: '#374151', marginBottom: 6, lineHeight: 1.4 }}>III. Steps to Reproduce</Text>\r\n                      <View style={{\r\n                        fontSize: 12,\r\n                        lineHeight: 1.4,\r\n                        textAlign: 'justify'\r\n                      }}>{renderPdfElements(ensurePdfElements(finding.instructions))}</View>\r\n                    </View>\r\n                  )}\r\n                  {finding.impact && (\r\n                    <View style={{ marginBottom: 12 }}>\r\n                      <Text style={{ fontWeight: 'bold', fontSize: 13, color: '#374151', marginBottom: 6, lineHeight: 1.4 }}>IV. Impact</Text>\r\n                      <View style={{\r\n                        fontSize: 12,\r\n                        lineHeight: 1.4,\r\n                        textAlign: 'justify'\r\n                      }}>{renderPdfElements(ensurePdfElements(finding.impact))}</View>\r\n                    </View>\r\n                  )}\r\n                  {finding.fix && (\r\n                    <View style={{ marginBottom: 12 }}>\r\n                      <Text style={{ fontWeight: 'bold', fontSize: 13, color: '#374151', marginBottom: 6, lineHeight: 1.4 }}>V. Recommendation</Text>\r\n                      <View style={{\r\n                        fontSize: 12,\r\n                        lineHeight: 1.4,\r\n                        textAlign: 'justify'\r\n                      }}>{renderPdfElements(ensurePdfElements(finding.fix))}</View>\r\n                    </View>\r\n                  )}\r\n                  {/* <View>\r\n                    <Text style={{ fontWeight: 'bold', fontSize: 15, color: '#374151', marginBottom: 6 }}>VI. Submission Date</Text>\r\n                    <Text style={{ fontSize: 12, color: '#6b7280' }}>{formatDateToDDMMYYYY(finding.submitted_date)}</Text>\r\n                  </View> */}\r\n                </View>\r\n              </View>\r\n            </View>\r\n            <View style={styles.footer} fixed>\r\n              <Text style={styles.footerLeft}>{documentNumber || 'Document Number'}</Text>\r\n              <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />\r\n            </View>\r\n          </Page>\r\n        );\r\n      })}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default DetailedFindingsBySeverityPage; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,UAAU,QAAQ,qBAAqB;AAClE,SAASC,iBAAiB,QAAqE,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAQtH;AACA,SAASC,YAAYA,CAACC,IAAY,EAAU;EAC1C;EACA,OAAOA,IAAI,CAACC,OAAO,CAAC,iDAAiD,EAAE,EAAE,CAAC;AAC5E;;AAEA;AACA,SAASC,oBAAoBA,CAACC,UAAkB,EAAU;EACxD,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;EAC1B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;EACjC,IAAIG,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE,OAAOJ,UAAU;EAC5C,MAAMK,GAAG,GAAGC,MAAM,CAACL,IAAI,CAACM,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EACnD,MAAMC,KAAK,GAAGH,MAAM,CAACL,IAAI,CAACS,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAC1D,MAAMG,IAAI,GAAGV,IAAI,CAACW,WAAW,CAAC,CAAC;EAC/B,OAAQ,GAAEP,GAAI,IAAGI,KAAM,IAAGE,IAAK,EAAC;AAClC;;AAEA;AACA,SAASE,iBAAiBA,CAACC,KAAU,EAAgB;EACnD,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;EACrB,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAAIA,KAAK,CAACG,MAAM,GAAG,CAAC,IAAI,OAAOH,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,MAAM,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;IAClG;IACA,OAAOA,KAAK;EACd;EACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B;IACA,IAAIA,KAAK,CAACI,IAAI,CAAC,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;MAChC,MAAMC,SAAS,GAAGxB,YAAY,CAACkB,KAAK,CAAC;MACrC;MACA,OAAOO,uBAAuB,CAACD,SAAS,CAAC;IAC3C;IACA,OAAO,CAAC;MAAEE,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAET;IAAM,CAAC,CAAC;EAC3C;EACA,OAAO,CAAC;IAAEQ,IAAI,EAAE,MAAM;IAAEC,OAAO,EAAEjB,MAAM,CAACQ,KAAK;EAAE,CAAC,CAAC;AACnD;;AAEA;AACA,SAASO,uBAAuBA,CAACG,UAAkB,EAAgB;EACjE,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;EAC1B,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOC,SAAS,KAAK,WAAW,EAAE;IACrE;IACA,OAAO,CAAC;MAAEJ,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAEC,UAAU,CAAC1B,OAAO,CAAC,UAAU,EAAE,EAAE;IAAE,CAAC,CAAC;EACxE;EAEA,MAAM6B,MAAM,GAAG,IAAID,SAAS,CAAC,CAAC;EAC9B,MAAME,GAAG,GAAGD,MAAM,CAACE,eAAe,CAACL,UAAU,EAAE,WAAW,CAAC;EAC3D,MAAMM,QAAsB,GAAG,EAAE;EAEjC,MAAMC,WAAW,GAAGA,CAACC,IAAU,EAAEC,SAAuB,GAAGH,QAAQ,KAAW;IAAA,IAAAI,oBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA;IAC5E,IAAIX,IAAI,CAACY,QAAQ,KAAKC,IAAI,CAACC,SAAS,EAAE;MAAA,IAAAC,qBAAA,EAAAC,iBAAA;MACpC,MAAMC,IAAI,IAAAF,qBAAA,IAAAC,iBAAA,GAAGhB,IAAI,CAACkB,WAAW,cAAAF,iBAAA,uBAAhBA,iBAAA,CAAkB9B,IAAI,CAAC,CAAC,cAAA6B,qBAAA,cAAAA,qBAAA,GAAI,EAAE;MAC3C,IAAIE,IAAI,EAAE;QACRhB,SAAS,CAACkB,IAAI,CAAC;UAAE7B,IAAI,EAAE,MAAM;UAAEC,OAAO,EAAE0B;QAAK,CAAC,CAAC;MACjD;IACF,CAAC,MAAM,IAAIjB,IAAI,CAACY,QAAQ,KAAKC,IAAI,CAACO,YAAY,EAAE;MAC9C,MAAMC,EAAE,GAAGrB,IAAmB;MAC9B,QAAQqB,EAAE,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC;QAC9B,KAAK,GAAG;UAAE;YACR,MAAMC,SAAqB,GAAG;cAAElC,IAAI,EAAE,WAAW;cAAEmC,QAAQ,EAAE;YAAG,CAAC;YACjE,KAAK,MAAMC,KAAK,IAAI3C,KAAK,CAAC4C,IAAI,CAACN,EAAE,CAACO,UAAU,CAAC,EAAE;cAC7C7B,WAAW,CAAC2B,KAAK,EAAEF,SAAS,CAACC,QAAQ,CAAC;YACxC;YACA,IAAID,SAAS,CAACC,QAAQ,CAACxC,MAAM,GAAG,CAAC,EAAE;cACjCgB,SAAS,CAACkB,IAAI,CAACK,SAAS,CAAC;YAC3B;YACA;UACF;QACA,KAAK,IAAI;QACT,KAAK,IAAI;QACT,KAAK,IAAI;QACT,KAAK,IAAI;QACT,KAAK,IAAI;QACT,KAAK,IAAI;UACPvB,SAAS,CAACkB,IAAI,CAAC;YACb7B,IAAI,EAAE,SAAS;YACfuC,KAAK,EAAEC,QAAQ,CAACT,EAAE,CAACC,OAAO,CAACS,SAAS,CAAC,CAAC,CAAC,CAAC;YACxCxC,OAAO,GAAAW,oBAAA,IAAAC,eAAA,GAAEkB,EAAE,CAACH,WAAW,cAAAf,eAAA,uBAAdA,eAAA,CAAgBjB,IAAI,CAAC,CAAC,cAAAgB,oBAAA,cAAAA,oBAAA,GAAI;UACrC,CAAC,CAAC;UACF;QACF,KAAK,QAAQ;QACb,KAAK,GAAG;UACND,SAAS,CAACkB,IAAI,CAAC;YACb7B,IAAI,EAAE,MAAM;YACZC,OAAO,GAAAa,qBAAA,IAAAC,gBAAA,GAAEgB,EAAE,CAACH,WAAW,cAAAb,gBAAA,uBAAdA,gBAAA,CAAgBnB,IAAI,CAAC,CAAC,cAAAkB,qBAAA,cAAAA,qBAAA,GAAI,EAAE;YACrC4B,KAAK,EAAE;UACT,CAAC,CAAC;UACF;QACF,KAAK,IAAI;QACT,KAAK,GAAG;UACN/B,SAAS,CAACkB,IAAI,CAAC;YACb7B,IAAI,EAAE,MAAM;YACZC,OAAO,GAAAe,qBAAA,IAAAC,gBAAA,GAAEc,EAAE,CAACH,WAAW,cAAAX,gBAAA,uBAAdA,gBAAA,CAAgBrB,IAAI,CAAC,CAAC,cAAAoB,qBAAA,cAAAA,qBAAA,GAAI,EAAE;YACrC0B,KAAK,EAAE;UACT,CAAC,CAAC;UACF;QACF,KAAK,IAAI;QACT,KAAK,IAAI;UAAE;YACT,MAAMC,IAAgB,GAAG;cACvB3C,IAAI,EAAE,MAAM;cACZ4C,OAAO,EAAEb,EAAE,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,IAAI;cAC1CY,KAAK,EAAE;YACT,CAAC;YACD,KAAK,MAAMC,EAAE,IAAIrD,KAAK,CAAC4C,IAAI,CAACN,EAAE,CAACgB,gBAAgB,CAAC,IAAI,CAAC,CAAC,EAAE;cAAA,IAAAC,oBAAA,EAAAC,eAAA;cACtDN,IAAI,CAACE,KAAK,CAAChB,IAAI,EAAAmB,oBAAA,IAAAC,eAAA,GAACH,EAAE,CAAClB,WAAW,cAAAqB,eAAA,uBAAdA,eAAA,CAAgBrD,IAAI,CAAC,CAAC,cAAAoD,oBAAA,cAAAA,oBAAA,GAAI,EAAE,CAAC;YAC/C;YACA,IAAIL,IAAI,CAACE,KAAK,CAAClD,MAAM,GAAG,CAAC,EAAE;cACzBgB,SAAS,CAACkB,IAAI,CAACc,IAAI,CAAC;YACtB;YACA;UACF;QACA,KAAK,KAAK;QACV,KAAK,MAAM;UACT;UACAhC,SAAS,CAACkB,IAAI,CAAC;YACb7B,IAAI,EAAE,MAAM;YACZC,OAAO,GAAAiB,qBAAA,IAAAC,gBAAA,GAAEY,EAAE,CAACH,WAAW,cAAAT,gBAAA,uBAAdA,gBAAA,CAAgBvB,IAAI,CAAC,CAAC,cAAAsB,qBAAA,cAAAA,qBAAA,GAAI,EAAE;YACrCgC,QAAQ,EAAEnB,EAAE,CAACoB,YAAY,CAAC,eAAe,CAAC,IAAI;UAChD,CAAQ,CAAC;UACT;QACF,KAAK,YAAY;UACfxC,SAAS,CAACkB,IAAI,CAAC;YACb7B,IAAI,EAAE,YAAY;YAClBC,OAAO,GAAAmB,qBAAA,IAAAC,gBAAA,GAAEU,EAAE,CAACH,WAAW,cAAAP,gBAAA,uBAAdA,gBAAA,CAAgBzB,IAAI,CAAC,CAAC,cAAAwB,qBAAA,cAAAA,qBAAA,GAAI;UACrC,CAAQ,CAAC;UACT;QACF,KAAK,KAAK;UAAE;YACV,MAAMgC,GAAG,GAAGrB,EAAE,CAACoB,YAAY,CAAC,KAAK,CAAC;YAClC,MAAME,KAAK,GAAGtB,EAAE,CAACoB,YAAY,CAAC,OAAO,CAAC;YACtC,MAAMG,MAAM,GAAGvB,EAAE,CAACoB,YAAY,CAAC,QAAQ,CAAC;YACxCxC,SAAS,CAACkB,IAAI,CAAC;cACb7B,IAAI,EAAE,OAAO;cACboD,GAAG,EAAEA,GAAG;cACRG,GAAG,EAAExB,EAAE,CAACoB,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE;cACjCE,KAAK,EAAEA,KAAK,aAALA,KAAK,cAALA,KAAK,GAAIG,SAAS;cACzBF,MAAM,EAAEA,MAAM,aAANA,MAAM,cAANA,MAAM,GAAIE;YACpB,CAAC,CAAC;YACF;UACF;QACA,KAAK,IAAI;UACP7C,SAAS,CAACkB,IAAI,CAAC;YAAE7B,IAAI,EAAE;UAAQ,CAAC,CAAC;UACjC;QACF;UACE;UACA,KAAK,MAAMoC,KAAK,IAAI3C,KAAK,CAAC4C,IAAI,CAACN,EAAE,CAACO,UAAU,CAAC,EAAE;YAC7C7B,WAAW,CAAC2B,KAAK,EAAEzB,SAAS,CAAC;UAC/B;UACA;MACJ;IACF;EACF,CAAC;EAED,KAAK,MAAMyB,KAAK,IAAI3C,KAAK,CAAC4C,IAAI,CAAC/B,GAAG,CAACmD,IAAI,CAACnB,UAAU,CAAC,EAAE;IACnD7B,WAAW,CAAC2B,KAAK,CAAC;EACpB;EACA,OAAO5B,QAAQ;AACjB;AAEA,MAAMkD,MAAM,GAAG1F,UAAU,CAAC2F,MAAM,CAAC;EAC/BC,MAAM,EAAE;IACNC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,MAAM;IACbC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBC,iBAAiB,EAAE;EACrB,CAAC;EACDC,UAAU,EAAE;IACVC,SAAS,EAAE,MAAM;IACjBC,IAAI,EAAE;EACR,CAAC;EACDC,WAAW,EAAE;IACXF,SAAS,EAAE,OAAO;IAClBC,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AAEF,MAAME,8BAAqK,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,cAAc;EAAEC,mBAAmB;EAAEC;AAAU,CAAC,KAAK;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EACxP,MAAMC,cAA+D,GAAG;IACtEC,QAAQ,EAAE;MAAEzD,IAAI,EAAE,SAAS;MAAE0D,KAAK,EAAE;IAAU,CAAC;IAAE;IACjDC,IAAI,EAAE;MAAE3D,IAAI,EAAE,SAAS;MAAE0D,KAAK,EAAE;IAAU,CAAC;IAAM;IACjDE,MAAM,EAAE;MAAE5D,IAAI,EAAE,SAAS;MAAE0D,KAAK,EAAE;IAAU,CAAC;IAAI;IACjDG,GAAG,EAAE;MAAE7D,IAAI,EAAE,SAAS;MAAE0D,KAAK,EAAE;IAAU,CAAC,CAAO;EACnD,CAAC;EACD,MAAMI,QAAQ,GAAIb,QAAQ,CAACc,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGf,QAAQ,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAC3D,WAAW,CAAC,CAAiC;EACpH,MAAM4D,UAAU,GAAG,EAAAZ,qBAAA,GAAAE,cAAc,CAACM,QAAQ,CAAC,cAAAR,qBAAA,uBAAxBA,qBAAA,CAA0BtD,IAAI,KAAI,SAAS;EAC9D,MAAMmE,UAAU,GAAG,EAAAZ,sBAAA,GAAAC,cAAc,CAACM,QAAQ,CAAC,cAAAP,sBAAA,uBAAxBA,sBAAA,CAA0BG,KAAK,KAAI,SAAS;;EAE/D;EACA,IAAI,CAACR,QAAQ,IAAIA,QAAQ,CAAClF,MAAM,KAAK,CAAC,EAAE;IACtC,OAAO,IAAI;EACb;EACA;EACA,oBACExB,OAAA,CAAAE,SAAA;IAAA8D,QAAA,EACG0C,QAAQ,CAACkB,GAAG,CAAC,CAACC,OAAO,EAAEC,GAAG,KAAK;MAC9B;MACA,MAAMC,cAAsC,GAAG,CAAC,CAAC;MACjDrB,QAAQ,CAACe,KAAK,CAAC,CAAC,EAAEK,GAAG,GAAG,CAAC,CAAC,CAACE,OAAO,CAAEC,CAAC,IAAK;QACxC,MAAMC,GAAG,GAAG,CAACD,CAAC,CAACE,iBAAiB,IAAI1B,QAAQ,IAAI,EAAE,EAAE3C,WAAW,CAAC,CAAC;QACjEiE,cAAc,CAACG,GAAG,CAAC,GAAG,CAACH,cAAc,CAACG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;MACtD,CAAC,CAAC;MACF,MAAMA,GAAG,GAAG,CAACL,OAAO,CAACM,iBAAiB,IAAI1B,QAAQ,IAAI,EAAE,EAAE3C,WAAW,CAAC,CAAC;MACvE,IAAIsE,UAAU,GAAG,EAAE;MACnB,IAAIF,GAAG,KAAK,UAAU,EAAEE,UAAU,GAAG,GAAG,CAAC,KACpC,IAAIF,GAAG,KAAK,MAAM,EAAEE,UAAU,GAAG,GAAG,CAAC,KACrC,IAAIF,GAAG,KAAK,QAAQ,EAAEE,UAAU,GAAG,GAAG,CAAC,KACvC,IAAIF,GAAG,KAAK,KAAK,EAAEE,UAAU,GAAG,GAAG,CAAC,KACpCA,UAAU,GAAG,GAAG;MACrB,MAAMC,WAAW,GAAGD,UAAU,GAAGL,cAAc,CAACG,GAAG,CAAC;MACpD,MAAMI,IAAI,GAAGT,OAAO,CAACU,YAAY,IAAIV,OAAO,CAACU,YAAY,CAAC9G,IAAI,CAAC,CAAC,GAAGoG,OAAO,CAACU,YAAY,GAAGF,WAAW;MACrG,oBACErI,OAAA,CAACN,IAAI;QAAW8I,IAAI,EAAC,IAAI;QAACC,EAAE,EAAEX,GAAG,KAAK,CAAC,GAAGjB,SAAS,GAAGxB,SAAU;QAACd,KAAK,EAAE;UACtEyB,aAAa,EAAE,QAAQ;UACvB0C,eAAe,EAAE,SAAS;UAC1BC,OAAO,EAAE,UAAU;UACnBC,UAAU,EAAE,WAAW;UACvB9C,QAAQ,EAAE;QACZ,CAAE;QAAA9B,QAAA,GAEC4C,mBAAmB,IAAIkB,GAAG,KAAK,CAAC,iBAC/B9H,OAAA,CAACJ,IAAI;UAAC2E,KAAK,EAAE;YAAEsE,OAAO,EAAE,MAAM;YAAE/C,QAAQ,EAAE;UAAE,CAAE;UAACgD,MAAM,EAAEA,CAAC;YAAEC;UAAW,CAAC,KAAK;YACzEnC,mBAAmB,CAAE,oBAAmBH,QAAS,EAAC,EAAEsC,UAAU,CAAC;YAC/D,OAAO,EAAE;UACX;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACL,eACDnJ,OAAA,CAACL,IAAI;UAAC4E,KAAK,EAAE;YAAE4B,iBAAiB,EAAE,EAAE;YAAEH,aAAa,EAAE,QAAQ;YAAEM,IAAI,EAAE;UAAE,CAAE;UAAAtC,QAAA,eACvEhE,OAAA,CAACL,IAAI;YAAC4E,KAAK,EAAE;cAAE+B,IAAI,EAAE;YAAC,CAAE;YAAAtC,QAAA,GACrB8D,GAAG,KAAK,CAAC,iBACR9H,OAAA,CAACJ,IAAI;cAAC2E,KAAK,EAAE;gBAAEuB,QAAQ,EAAE,EAAE;gBAAEsD,UAAU,EAAE,MAAM;gBAAErD,KAAK,EAAE2B,UAAU;gBAAE2B,YAAY,EAAE,EAAE;gBAAEC,UAAU,EAAE;cAAI,CAAE;cAAAtF,QAAA,GACrGyC,QAAQ,CAACe,WAAW,CAAC,CAAC,EAAC,oBAC1B;YAAA;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP,eACDnJ,OAAA,CAACL,IAAI;cAAC4E,KAAK,EAAE;gBAAEmE,eAAe,EAAE,MAAM;gBAAEa,WAAW,EAAE,CAAC;gBAAEC,WAAW,EAAE,SAAS;gBAAEC,YAAY,EAAE,CAAC;gBAAEd,OAAO,EAAE,EAAE;gBAAEU,YAAY,EAAE;cAAG,CAAE;cAAArF,QAAA,gBAC/HhE,OAAA,CAACL,IAAI;gBAAC4E,KAAK,EAAE;kBAAEyB,aAAa,EAAE,KAAK;kBAAEE,UAAU,EAAE,QAAQ;kBAAEmD,YAAY,EAAE;gBAAG,CAAE;gBAAArF,QAAA,gBAC5EhE,OAAA,CAACL,IAAI;kBAAC4E,KAAK,EAAE;oBAAEmE,eAAe,EAAEf,UAAU;oBAAE5B,KAAK,EAAE,MAAM;oBAAE0D,YAAY,EAAE,CAAC;oBAAEtD,iBAAiB,EAAE,CAAC;oBAAEuD,eAAe,EAAE,CAAC;oBAAEC,WAAW,EAAE;kBAAG,CAAE;kBAAA3F,QAAA,eACtIhE,OAAA,CAACJ,IAAI;oBAAC2E,KAAK,EAAE;sBAAEwB,KAAK,EAAE,MAAM;sBAAEqD,UAAU,EAAE,MAAM;sBAAEtD,QAAQ,EAAE,EAAE;sBAAEwD,UAAU,EAAE;oBAAI,CAAE;oBAAAtF,QAAA,EAAEsE;kBAAI;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F,CAAC,eACPnJ,OAAA,CAACJ,IAAI;kBAAC2E,KAAK,EAAE;oBACX6E,UAAU,EAAE,MAAM;oBAClBtD,QAAQ,EAAE,EAAE;oBACZC,KAAK,EAAE,SAAS;oBAChB6D,QAAQ,EAAE,KAAK;oBACfN,UAAU,EAAE,GAAG;oBACfjD,SAAS,EAAE;kBACb,CAAE;kBAAArC,QAAA,EAAE6D,OAAO,CAACgC;gBAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,EACNtB,OAAO,CAACiC,KAAK,iBACZ9J,OAAA,CAACL,IAAI;gBAAC4E,KAAK,EAAE;kBAAE8E,YAAY,EAAE;gBAAG,CAAE;gBAAArF,QAAA,gBAChChE,OAAA,CAACJ,IAAI;kBAAC2E,KAAK,EAAE;oBAAE6E,UAAU,EAAE,MAAM;oBAAEtD,QAAQ,EAAE,EAAE;oBAAEC,KAAK,EAAE,SAAS;oBAAEsD,YAAY,EAAE,CAAC;oBAAEC,UAAU,EAAE;kBAAI,CAAE;kBAAAtF,QAAA,EAAC;gBAA4B;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1InJ,OAAA,CAACJ,IAAI;kBAAC2E,KAAK,EAAE;oBACXuB,QAAQ,EAAE,EAAE;oBACZC,KAAK,EAAE,SAAS;oBAChBuD,UAAU,EAAE,GAAG;oBACfjD,SAAS,EAAE;kBACb,CAAE;kBAAArC,QAAA,EAAE6D,OAAO,CAACiC;gBAAK;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CACP,EACAtB,OAAO,CAACkC,WAAW,iBAClB/J,OAAA,CAACL,IAAI;gBAAC4E,KAAK,EAAE;kBAAE8E,YAAY,EAAE;gBAAG,CAAE;gBAAArF,QAAA,gBAChChE,OAAA,CAACJ,IAAI;kBAAC2E,KAAK,EAAE;oBAAE6E,UAAU,EAAE,MAAM;oBAAEtD,QAAQ,EAAE,EAAE;oBAAEC,KAAK,EAAE,SAAS;oBAAEsD,YAAY,EAAE,CAAC;oBAAEC,UAAU,EAAE;kBAAI,CAAE;kBAAAtF,QAAA,EAAC;gBAAe;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7HnJ,OAAA,CAACL,IAAI;kBAAC4E,KAAK,EAAE;oBACXuB,QAAQ,EAAE,EAAE;oBACZwD,UAAU,EAAE,GAAG;oBACfjD,SAAS,EAAE;kBACb,CAAE;kBAAArC,QAAA,EAAElE,iBAAiB,CAACsB,iBAAiB,CAACyG,OAAO,CAACkC,WAAW,CAAC;gBAAC;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CACP,EACAtB,OAAO,CAACmC,YAAY,iBACnBhK,OAAA,CAACL,IAAI;gBAAC4E,KAAK,EAAE;kBAAE8E,YAAY,EAAE;gBAAG,CAAE;gBAAArF,QAAA,gBAChChE,OAAA,CAACJ,IAAI;kBAAC2E,KAAK,EAAE;oBAAE6E,UAAU,EAAE,MAAM;oBAAEtD,QAAQ,EAAE,EAAE;oBAAEC,KAAK,EAAE,SAAS;oBAAEsD,YAAY,EAAE,CAAC;oBAAEC,UAAU,EAAE;kBAAI,CAAE;kBAAAtF,QAAA,EAAC;gBAAuB;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrInJ,OAAA,CAACL,IAAI;kBAAC4E,KAAK,EAAE;oBACXuB,QAAQ,EAAE,EAAE;oBACZwD,UAAU,EAAE,GAAG;oBACfjD,SAAS,EAAE;kBACb,CAAE;kBAAArC,QAAA,EAAElE,iBAAiB,CAACsB,iBAAiB,CAACyG,OAAO,CAACmC,YAAY,CAAC;gBAAC;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CACP,EACAtB,OAAO,CAACoC,MAAM,iBACbjK,OAAA,CAACL,IAAI;gBAAC4E,KAAK,EAAE;kBAAE8E,YAAY,EAAE;gBAAG,CAAE;gBAAArF,QAAA,gBAChChE,OAAA,CAACJ,IAAI;kBAAC2E,KAAK,EAAE;oBAAE6E,UAAU,EAAE,MAAM;oBAAEtD,QAAQ,EAAE,EAAE;oBAAEC,KAAK,EAAE,SAAS;oBAAEsD,YAAY,EAAE,CAAC;oBAAEC,UAAU,EAAE;kBAAI,CAAE;kBAAAtF,QAAA,EAAC;gBAAU;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxHnJ,OAAA,CAACL,IAAI;kBAAC4E,KAAK,EAAE;oBACXuB,QAAQ,EAAE,EAAE;oBACZwD,UAAU,EAAE,GAAG;oBACfjD,SAAS,EAAE;kBACb,CAAE;kBAAArC,QAAA,EAAElE,iBAAiB,CAACsB,iBAAiB,CAACyG,OAAO,CAACoC,MAAM,CAAC;gBAAC;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CACP,EACAtB,OAAO,CAACqC,GAAG,iBACVlK,OAAA,CAACL,IAAI;gBAAC4E,KAAK,EAAE;kBAAE8E,YAAY,EAAE;gBAAG,CAAE;gBAAArF,QAAA,gBAChChE,OAAA,CAACJ,IAAI;kBAAC2E,KAAK,EAAE;oBAAE6E,UAAU,EAAE,MAAM;oBAAEtD,QAAQ,EAAE,EAAE;oBAAEC,KAAK,EAAE,SAAS;oBAAEsD,YAAY,EAAE,CAAC;oBAAEC,UAAU,EAAE;kBAAI,CAAE;kBAAAtF,QAAA,EAAC;gBAAiB;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/HnJ,OAAA,CAACL,IAAI;kBAAC4E,KAAK,EAAE;oBACXuB,QAAQ,EAAE,EAAE;oBACZwD,UAAU,EAAE,GAAG;oBACfjD,SAAS,EAAE;kBACb,CAAE;kBAAArC,QAAA,EAAElE,iBAAiB,CAACsB,iBAAiB,CAACyG,OAAO,CAACqC,GAAG,CAAC;gBAAC;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACPnJ,OAAA,CAACL,IAAI;UAAC4E,KAAK,EAAEgB,MAAM,CAACE,MAAO;UAAC0E,KAAK;UAAAnG,QAAA,gBAC/BhE,OAAA,CAACJ,IAAI;YAAC2E,KAAK,EAAEgB,MAAM,CAACa,UAAW;YAAApC,QAAA,EAAE2C,cAAc,IAAI;UAAiB;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5EnJ,OAAA,CAACJ,IAAI;YAAC2E,KAAK,EAAEgB,MAAM,CAACgB,WAAY;YAACuC,MAAM,EAAEA,CAAC;cAAEC,UAAU;cAAEqB;YAAW,CAAC,KAAM,GAAErB,UAAW,MAAKqB,UAAW;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxG,CAAC;MAAA,GAhGErB,GAAG;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiGR,CAAC;IAEX,CAAC;EAAC,gBACF,CAAC;AAEP,CAAC;AAACkB,EAAA,GAzII7D,8BAAqK;AA2I3K,eAAeA,8BAA8B;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}