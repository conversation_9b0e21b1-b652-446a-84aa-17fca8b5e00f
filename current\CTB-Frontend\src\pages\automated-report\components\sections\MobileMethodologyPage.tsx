import React from 'react';
import { Page, View, Text, StyleSheet } from '@react-pdf/renderer';

const accentColor = '#2563eb';
const cardBg = '#f8fafc';
const dividerColor = '#e5e7eb';

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: '20mm 15mm',
    fontFamily: 'Helvetica',
    fontSize: 12,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: accentColor,
    letterSpacing: 1.2,
    marginBottom: 6,
    textAlign: 'left',
  },
  divider: {
    height: 2,
    backgroundColor: dividerColor,
    marginBottom: 18,
    opacity: 0.7,
    borderRadius: 1,
  },
  intro: {
    fontSize: 13,
    color: '#374151',
    marginBottom: 28,
    lineHeight: 1.6,
    textAlign: 'justify',
    paddingHorizontal: 8,
  },
  phaseBlock: {
    backgroundColor: cardBg,
    borderRadius: 12,
    padding: 18,
    marginBottom: 28,
    borderWidth: 1,
    borderColor: dividerColor,
  },
  phaseName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: accentColor,
    marginBottom: 8,
    letterSpacing: 0.5,
  },
  phaseDesc: {
    fontSize: 13,
    color: '#374151',
    marginBottom: 10,
    lineHeight: 1.5,
    textAlign: 'justify',
  },
  phaseTechniques: {
    fontSize: 13,
    color: '#374151',
    fontWeight: 'bold',
    marginBottom: 4,
    marginTop: 2,
  },
  bulletList: {
    marginLeft: 12,
    marginBottom: 0,
    marginTop: 0,
  },
  bulletItem: {
    fontSize: 12,
    color: '#374151',
    marginBottom: 4,
    lineHeight: 1.5,
    paddingLeft: 4,
  },
  phaseSummary: {
    fontSize: 12,
    color: '#374151',
    marginTop: 10,
    textAlign: 'justify',
    fontStyle: 'italic',
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    fontSize: 10,
    color: 'grey',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  footerLeft: {
    textAlign: 'left',
    flex: 1,
  },
  footerRight: {
    textAlign: 'right',
    flex: 1,
  },
});

const phases = [
  {
    name: 'Pre-Engagement Analysis',
    description: `We gather background information and align with the client to ensure the engagement is well-defined and focused on the mobile app’s critical components. This phase helps tailor our testing to the app’s functionality and business context.`,
    techniques: [
      `Review the app’s documentation, such as API specifications, user guides, and architecture diagrams, to understand its functionality, data flows, and backend dependencies.`,
      `Conduct stakeholder interviews to identify critical features (e.g., payment processing, user authentication) and sensitive data (e.g., PII, financial details) that require focused testing.`,
      `Analyze the app’s deployment environment, such as iOS/Android versions, backend APIs, or cloud services (e.g., Firebase), to identify potential external dependencies or third-party integrations.`,
      `Assess previous security reports or audits (if provided) to identify known vulnerabilities, such as insecure storage or weak API authentication, and recurring issues.`,
      `Establish success criteria with the client, such as accessing premium features without payment, extracting user data, or identifying a set number of critical vulnerabilities.`,
    ],
    summary: `This phase ensures a strategic approach, aligning testing with the client’s priorities and the app’s risk profile.`,
  },
  {
    name: 'Planning and Scoping',
    description: `We define the scope, objectives, and rules of engagement to ensure testing aligns with client requirements and avoids disruptions. This phase establishes a controlled and compliant testing framework.`,
    techniques: [
      `Identify target apps, platforms (iOS/Android), versions, and backend APIs to be tested, ensuring all in-scope assets are documented (e.g., com.example.app, api.example.com).`,
      `Define constraints, such as avoiding user data modification, excluding production APIs, or limiting testing to specific app features (e.g., excluding payment gateways).`,
      `Establish communication protocols for reporting issues, escalations, or incidents, including points of contact and regular status updates.`,
      `Review legal agreements, such as NDAs or app store policies, to ensure compliance with regulations (e.g., GDPR for EU users) and obtain written permission for testing.`,
      `Set up tools like MobSF, Frida, Burp Suite, and Drozer, ensuring they are configured for the target app’s platform and backend infrastructure (e.g., setting up Frida for iOS hooking).`,
    ],
    summary: `This phase ensures a clear scope and prepares the team for effective, compliant testing.`,
  },
  {
    name: 'Application Analysis',
    description: `We analyze the mobile app’s structure, functionality, and backend interactions to map its attack surface, using both static and dynamic analysis to identify potential vulnerabilities and understand the app’s behavior.`,
    techniques: [
      `Decompile the app (APK with apktool for Android, IPA with otool for iOS) to review source code for vulnerabilities like hardcoded secrets (e.g., API keys, passwords), insecure APIs, or deprecated cryptographic functions (e.g., MD5), using MobSF for automated analysis.`,
      `Run the app in an emulator (e.g., Android Studio for Android, Xcode Simulator for iOS) to observe runtime behavior, focusing on memory usage, API calls, and interactions with the file system, using Frida to hook into sensitive functions.`,
      `Review app permissions in the manifest (Android) or Info.plist (iOS) to identify over-privileged access, such as requesting location, camera, or contacts access without a clear need, assessing the risk of permission abuse.`,
      `Identify outdated or vulnerable third-party libraries (e.g., an old version of OkHttp with known CVEs) using MobSF or Dependency-Check, cross-referencing with CVE databases to pinpoint exploitable issues.`,
      `Map backend APIs with Burp Suite by intercepting network traffic, focusing on mobile-specific endpoints (e.g., /api/mobile/login), and analyzing request/response structures, headers, and authentication mechanisms (e.g., OAuth, JWT).`,
      `Analyze client-side logic in JavaScript (for hybrid apps) or native code for vulnerabilities, such as client-side validation that can be bypassed (e.g., enabling a premium feature by modifying a Boolean flag), using tools like JSDetox for JavaScript analysis.`,
      `Examine the app’s interaction with external services, such as push notifications, analytics, or ad networks, to identify potential data leakage points or insecure third-party integrations (e.g., Firebase misconfigurations).`,
    ],
    summary: `This phase provides a detailed blueprint of the app’s attack surface, identifying potential vulnerabilities for targeted testing and understanding its operational context.`,
  },
  {
    name: 'Local Storage Testing',
    description: `We assess how the app handles sensitive data on the device, focusing on storage practices and the app’s resilience to attacks on a compromised device, ensuring data protection even under adversarial conditions.`,
    techniques: [
      `Check for plaintext storage of sensitive data (e.g., credentials, tokens, PII) in SharedPreferences (Android), Keychain (iOS), or app sandbox files, using Drozer (Android) or idb (iOS) to extract and analyze stored data.`,
      `Analyze SQLite databases for unencrypted sensitive data, such as user PII or session tokens, using sqlite3 to query databases (e.g., SELECT * FROM users) and check for weak encryption practices (e.g., no encryption or weak keys).`,
      `Test for improper use of external storage (e.g., SD card on Android) that could expose data to other apps or users, checking for files written to shared directories without proper permissions (e.g., world-readable files).`,
      `Extract app data through device backups (e.g., ADB backups for Android, iTunes backups for iOS) to identify unprotected data, such as unencrypted backups containing API keys, user data, or authentication tokens.`,
      `Use a rooted/jailbroken device to access app directories (e.g., /data/data/ on Android, /var/mobile/Containers/Data/Application/ on iOS), checking for world-readable files, insecure permissions, or sensitive data in logs.`,
      `Assess the app’s handling of temporary files, such as cached images or session data, to ensure they are securely deleted and not accessible to other apps or users, using tools like find on a rooted device to locate remnants.`,
      `Test for clipboard vulnerabilities by copying sensitive data (e.g., a password) and checking if the app accesses it without user consent, and evaluate autofill mechanisms for potential data leakage (e.g., exposing credentials to other apps).`,
    ],
    summary: `This phase ensures that sensitive data is protected on the device, mitigating risks of data leakage on a compromised device and ensuring compliance with privacy standards.`,
  },
  {
    name: 'Network Testing',
    description: `We evaluate the app’s communication with backend services, focusing on the security of data in transit and the resilience of APIs to mobile-specific attacks, ensuring that network interactions are secure and resistant to interception.`,
    techniques: [
      `Intercept network traffic with Burp Suite to verify that all communication uses HTTPS with modern TLS versions (e.g., TLS 1.2 or 1.3), checking for weak cipher suites, expired certificates, or improper configurations using testssl.sh.`,
      `Test certificate pinning implementation by attempting to bypass it with tools like Frida or SSL Kill Switch, simulating man-in-the-middle (MITM) attacks to assess the app’s response to untrusted certificates (e.g., rejecting or accepting them).`,
      `Analyze network requests for leakage of sensitive data, such as API keys, session tokens, or PII, in headers, query parameters, or responses, ensuring data is encrypted and not exposed in plaintext or easily decodable formats (e.g., Base64).`,
      `Test backend API endpoints for vulnerabilities like SQL injection (e.g., id=1 OR 1=1), broken authentication (e.g., accepting expired tokens), or excessive data exposure (e.g., returning full user profiles instead of limited fields), using Burp Suite’s Repeater.`,
      `Perform MITM attacks to intercept and modify traffic, ensuring the app detects and rejects unauthorized certificates, and test for downgrade attacks (e.g., forcing TLS 1.0) to exploit weak configurations.`,
      `Assess rate-limiting on API endpoints by sending excessive requests with Burp Intruder or ffuf, checking for DoS vulnerabilities or error responses that might reveal sensitive information (e.g., stack traces in 500 errors).`,
      `Evaluate the app’s handling of network failures, such as retry mechanisms or caching, to ensure sensitive data isn’t exposed during errors (e.g., caching API responses with tokens in plaintext), using Charles Proxy to simulate network interruptions.`,
    ],
    summary: `This phase ensures that data in transit is secure, APIs are resistant to attacks, and the app handles network interactions safely, protecting user data from interception and abuse.`,
  },
  {
    name: 'Runtime Testing',
    description: `We test the app’s runtime behavior and logic on a compromised device, focusing on vulnerabilities that could be exploited through runtime manipulation, logic flaws, or insecure inter-process communication, simulating attacks by a malicious user or app.`,
    techniques: [
      `Bypass root/jailbreak detection mechanisms with Frida or Magisk (Android) and unc0ver (iOS), ensuring the app continues to function on a compromised device, potentially exposing sensitive data or functionality.`,
      `Hook into app functions with Frida or Objection to bypass security checks, such as forcing an authentication function to return true (e.g., hooking isAuthenticated()), or skipping payment validation by modifying return values.`,
      `Modify app logic to access premium features or restricted data, such as enabling a paid feature by changing a Boolean flag (e.g., isPremium=false to isPremium=true) or bypassing license checks with Frida scripts.`,
      `Test for insecure inter-process communication (IPC), such as exposed Android intents (e.g., sending an intent to launch a privileged activity) or iOS URL schemes (e.g., triggering myapp://reset-password to reset a password), using Drozer or Frida to exploit these mechanisms.`,
      `Inject malformed data into app inputs, such as oversized strings, invalid JSON, or null values, to cause crashes, errors, or unexpected behavior, ensuring proper input validation and error handling (e.g., crashing the app with a malformed API response).`,
      `Assess WebView security (if applicable) for vulnerabilities like JavaScript injection (e.g., javascript:alert(1)), file access (e.g., accessing local files via file://), or lack of proper sandboxing, using Burp Suite to intercept WebView traffic.`,
      `Test for runtime tampering by modifying app memory with tools like GameGuardian, attempting to alter variables (e.g., user balance in a gaming app) or bypass security checks, and evaluating the app’s integrity checks for detection.`,
    ],
    summary: `This phase evaluates the app’s resilience to tampering, logic-based attacks, and malicious interactions, ensuring it cannot be easily manipulated on a compromised device.`,
  },
  {
    name: 'Configuration Review',
    description: `We assess the app’s configuration and backend setup for security weaknesses, focusing on misconfigurations that could expose vulnerabilities or weaken defenses, ensuring the app adheres to secure configuration practices.`,
    techniques: [
      `Check for weak TLS configurations in API calls using testssl.sh, identifying outdated versions (e.g., TLS 1.0), weak cipher suites (e.g., RC4), or lack of certificate pinning that could allow MITM attacks.`,
      `Review app permissions for unnecessary access, such as requesting location, camera, or microphone access without a clear need, and assess the risk of permission abuse on a compromised device (e.g., using location to track users).`,
      `Identify hardcoded secrets in the app’s code, such as API keys, encryption keys, or passwords, using MobSF to search for patterns (e.g., api_key=) and ensuring secrets are stored securely (e.g., Android Keystore, iOS Keychain).`,
      `Assess WebView configurations for security issues, such as enabling JavaScript without proper sandboxing, allowing file access (e.g., setAllowFileAccess(true)), or lack of HTTPS enforcement, using Frida to inject malicious JavaScript.`,
      `Check for improper logging of sensitive data, such as tokens, passwords, or PII in debug logs (e.g., Logcat on Android), ensuring logs are sanitized and not accessible to unauthorized apps or users.`,
      `Evaluate the app’s use of secure storage mechanisms, such as ensuring encryption keys are not hardcoded and sensitive data is encrypted at rest (e.g., using AES-256), and test for weak cryptographic implementations (e.g., using ECB mode).`,
      `Assess backend configurations if accessible, such as checking for exposed admin interfaces (e.g., Firebase console), misconfigured cloud storage (e.g., public S3 buckets), or lack of API key rotation, using tools like firebase-scanner.`,
    ],
    summary: `This phase ensures the app and its backend are securely configured, mitigating risks from misconfigurations that could be exploited by attackers.`,
  },
  {
    name: 'Exploitation',
    description: `We exploit identified vulnerabilities to assess their real-world impact, focusing on demonstrating the severity of issues and their potential consequences for the app and its users, including chaining vulnerabilities for maximum impact.`,
    techniques: [
      `Extract sensitive data via insecure storage, such as plaintext tokens in SharedPreferences or unencrypted SQLite databases, using Drozer or idb to access and exfiltrate data (e.g., user credentials, API keys).`,
      `Escalate privileges through runtime manipulation, such as enabling admin features by hooking into functions with Frida (e.g., forcing isAdmin() to return true), or accessing premium features without payment by modifying logic.`,
      `Exploit API misconfigurations, such as broken authentication (e.g., accepting tampered JWTs), excessive data exposure (e.g., retrieving full user profiles), or IDOR (e.g., accessing another user’s data), using Burp Suite to manipulate requests.`,
      `Combine vulnerabilities to maximize impact, such as using insecure storage to steal a session token, then exploiting an API vulnerability to escalate privileges, and finally exfiltrating data via a network attack, documenting the attack chain.`,
      `Test for Denial of Service (DoS) by triggering crashes with malformed inputs (e.g., sending invalid JSON to crash the app) or exploiting resource-intensive API calls (e.g., recursive queries), using Burp Intruder to simulate DoS attacks.`,
      `Assess the app’s logging and monitoring by performing exploits and checking if they are detected, such as reviewing backend logs for unauthorized API access or device logs for runtime tampering attempts, and providing recommendations for improved visibility.`,
      `Evaluate the impact of exploits, such as data breaches exposing PII, financial loss from fraudulent transactions, or unauthorized access to user accounts, with scenarios illustrating potential attacker actions and business impact.`,
    ],
    summary: `This phase demonstrates the severity of vulnerabilities with proof-of-concept exploits, highlighting the potential impact on the app and its users, and identifying gaps in detection and response.`,
  },
  {
    name: 'Persistence and Post-Exploitation Analysis',
    description: `We evaluate the app’s resilience to persistent threats and analyze the impact of a successful breach, focusing on establishing persistence, exfiltrating data, and assessing detection capabilities on the device and backend, simulating a long-term attacker presence.`,
    techniques: [
      `Establish persistence on the device by modifying app data, such as injecting a malicious script into a WebView that executes on app launch, or creating a background service (Android) or background task (iOS) to maintain access.`,
      `Exfiltrate sensitive data from the device, such as user credentials or PII, by encoding data in HTTP requests (e.g., Base64 in query parameters) or using out-of-band techniques like DNS tunneling, ensuring data is encrypted to evade detection.`,
      `Test persistence mechanisms by creating a malicious intent (Android) or URL scheme handler (iOS) that triggers on specific events (e.g., app launch, network change), ensuring the attacker can regain access after app restart.`,
      `Assess the app’s logging and monitoring by performing actions like data exfiltration or runtime tampering, then reviewing device logs (e.g., Logcat on Android) and backend logs for detection, identifying blind spots (e.g., unlogged API calls).`,
      `Simulate attacker cleanup on the device by clearing app logs (e.g., deleting Logcat entries on Android), removing traces of malicious activity (e.g., deleting temporary files), and testing the app’s ability to preserve evidence for forensic analysis.`,
      `Evaluate the impact of a breach, such as data leakage exposing user PII, financial loss from unauthorized transactions, or reputational damage from app misuse, providing a detailed impact assessment for the client.`,
      `Test the backend’s incident response by triggering alerts (e.g., excessive API requests) and observing the client’s response time, communication, and mitigation efforts, providing feedback on their security operations and detection capabilities.`,
    ],
    summary: `This phase evaluates the app’s ability to detect, mitigate, and recover from persistent threats, providing insights into long-term security risks and detection gaps on both the device and backend.`,
  },
  {
    name: 'Reporting and Remediation Support',
    description: `We compile a detailed report of all findings, their impact, and remediation steps, ensuring the client can address vulnerabilities effectively. This phase also includes support for remediation and retesting to verify that fixes are successful, along with knowledge transfer to improve future security practices.`,
    techniques: [
      `Document each vulnerability with detailed evidence, including decompiled code snippets, network captures, Frida scripts, and screenshots of exploits, assigning severity using CVSS scoring to prioritize remediation.`,
      `Explain the real-world impact of each issue, such as data leakage exposing user PII, unauthorized access to premium features, or financial loss from API abuse, with scenarios illustrating potential attacker actions and business consequences.`,
      `Provide actionable remediation steps, such as implementing secure storage (e.g., Android Keystore, iOS Keychain), enforcing certificate pinning, updating vulnerable libraries, or adding rate-limiting to APIs, referencing standards like OWASP Mobile Top 10.`,
      `Include an executive summary for non-technical stakeholders, summarizing key risks, the overall security posture, and prioritized remediation actions, ensuring clarity for decision-makers and alignment with business goals.`,
      `Offer retesting support by revisiting exploited vulnerabilities after remediation, providing a follow-up report comparing pre- and post-remediation results to confirm that issues are resolved, and ensuring no new vulnerabilities are introduced.`,
      `Conduct a knowledge transfer session with the client’s team, explaining findings, demonstrating exploitation techniques (if requested), and providing guidance on secure mobile app development practices, such as OWASP MASVS for secure coding.`,
      `Provide a technical appendix for developers, including detailed exploit steps, code snippets for secure implementation (e.g., secure JWT validation in Java), and references to standards like OWASP ASVS for implementing secure coding practices.`,
    ],
    summary: `This phase ensures that findings are actionable, stakeholders are informed, and the app’s security is improved through effective remediation, retesting, and knowledge sharing.`,
  },
];

interface MobileMethodologyPageProps {
  documentNumber?: string;
  registerSectionPage?: (section: string, page: number) => void;
  sectionId?: string;
}

const MobileMethodologyPage: React.FC<MobileMethodologyPageProps> = ({ documentNumber, registerSectionPage, sectionId }) => (
  <Page size="A4" id={sectionId} style={styles.page}>
    {/* Register section page for TOC */}
    {registerSectionPage && (
      <Text style={{ display: 'none', fontSize: 1 }} render={({ pageNumber }) => {
        registerSectionPage('MobileMethodology', pageNumber);
        return '';
      }} />
    )}
    <Text style={styles.sectionTitle}>Mobile Penetration Testing Methodology</Text>
    <View style={styles.divider} />
    <Text style={styles.intro}>
      Our mobile penetration testing methodology identifies vulnerabilities in mobile applications (iOS and Android) and their backend services through a detailed, phased approach, ensuring a comprehensive evaluation of the app’s security posture.
    </Text>
    {phases.map((phase) => (
      <View key={phase.name} style={styles.phaseBlock}>
        <Text style={styles.phaseName}>{phase.name}</Text>
        <Text style={styles.phaseDesc}><Text style={{ fontWeight: 'bold' }}>Description:</Text> {phase.description}</Text>
        <Text style={styles.phaseTechniques}>Techniques:</Text>
        <View style={styles.bulletList}>
          {phase.techniques.map((tech, i) => (
            <Text key={i} style={styles.bulletItem}>• {tech}</Text>
          ))}
        </View>
        {phase.summary && <Text style={styles.phaseSummary}>{phase.summary}</Text>}
      </View>
    ))}
    <View style={styles.footer} fixed>
      <Text style={styles.footerLeft}>{documentNumber || 'Document Number'}</Text>
      <Text style={styles.footerRight} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
    </View>
  </Page>
);

export default MobileMethodologyPage; 