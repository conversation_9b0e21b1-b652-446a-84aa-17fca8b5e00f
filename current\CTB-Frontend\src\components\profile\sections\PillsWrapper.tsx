import PillList from "../../pills/PillList";
import ProfileSection from "../ProfileSection";

const PillsWrapper = ({
  id,
  profileTitle,
  end,
  item,
  addItem,
  removeItem
}: {
  id: string;
  profileTitle: string;
  end?: boolean;
  item?: string[];
  addItem?: (item: string) => void;
  removeItem?: (index: number) => void;
}) => {
  return (
    <div id={id}>
      <ProfileSection header={profileTitle} end={end}>
        <PillList
          items={item || []}
          iconClassName="fill-white"
          pillClassName="bg-ctb-blue-50 !text-white"
          addItem={addItem}
          removeItem={removeItem}
          modifiable
        />
      </ProfileSection>
    </div>
  );
};

export default PillsWrapper;
