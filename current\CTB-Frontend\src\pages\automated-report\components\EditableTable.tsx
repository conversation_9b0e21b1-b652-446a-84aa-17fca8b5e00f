import React from 'react';

interface EditableTableProps {
  data: any[];
  columns: {
    key: string;
    label: string;
    type?: 'text' | 'select';
    options?: string[];
  }[];
  onDataChange: (newData: any[]) => void;
}

const EditableTable: React.FC<EditableTableProps> = ({
  data,
  columns,
  onDataChange
}) => {
  const handleCellChange = (rowIndex: number, columnKey: string, value: string) => {
    const newData = [...data];
    newData[rowIndex] = {
      ...newData[rowIndex],
      [columnKey]: value
    };
    onDataChange(newData);
  };

  const addRow = () => {
    const newRow = columns.reduce((acc, col) => ({
      ...acc,
      [col.key]: ''
    }), {});
    onDataChange([...data, newRow]);
  };

  const deleteRow = (index: number) => {
    const newData = data.filter((_, i) => i !== index);
    onDataChange(newData);
  };

  return (
    <div className="table-container">
      <div className="table-header">
        <button
          onClick={addRow}
          className="add-row-button"
        >
          Add Row
        </button>
      </div>
      <table className="editable-table">
        <thead>
          <tr>
            {columns.map(column => (
              <th key={column.key}>{column.label}</th>
            ))}
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {data.map((row, rowIndex) => (
            <tr key={rowIndex}>
              {columns.map(column => (
                <td key={column.key}>
                  {column.type === 'select' ? (
                    <select
                      value={row[column.key] || ''}
                      onChange={(e) => handleCellChange(rowIndex, column.key, e.target.value)}
                      className="table-select"
                    >
                      <option value="">Select {column.label}</option>
                      {column.options?.map(option => (
                        <option key={option} value={option}>
                          {option}
                        </option>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      value={row[column.key] || ''}
                      onChange={(e) => handleCellChange(rowIndex, column.key, e.target.value)}
                      className="table-input"
                      placeholder={`Enter ${column.label.toLowerCase()}`}
                    />
                  )}
                </td>
              ))}
              <td>
                <button
                  onClick={() => deleteRow(rowIndex)}
                  className="delete-row-button"
                >
                  Delete
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default EditableTable; 