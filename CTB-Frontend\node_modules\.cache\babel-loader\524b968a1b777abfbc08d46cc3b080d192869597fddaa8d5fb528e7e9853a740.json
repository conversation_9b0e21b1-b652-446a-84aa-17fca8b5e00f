{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\jira-old\\\\CTB-Frontend\\\\src\\\\pages\\\\programs\\\\ProgramEditor.tsx\",\n  _s = $RefreshSig$();\nimport { useParams, useNavigate } from \"react-router-dom\";\nimport EditorLayout from \"../../layouts/EditorLayout\";\nimport EditorInput from \"../../components/editor/inputs/EditorInput\";\nimport usePageTitle from \"../../utils/hooks/usePageTitle\";\nimport { ProgramType, NotificationMethods } from \"../../utils/api/endpoints/programs/parsePrograms\";\nimport EditorSection from \"../../components/editor/EditorSection\";\nimport TargetsInput from \"../../components/editor/inputs/programs/TargetsInput\";\nimport DropDownEditor from \"../../components/editor/inputs/programs/DropDownEditor\";\nimport DatePicker from \"../../components/editor/inputs/programs/DatePicker\";\nimport EditorRichTextInput from \"../../components/editor/inputs/EditorRichTextInput\";\nimport ProgramRewardsInput from \"../../components/editor/inputs/programs/ProgramRewardsInput\";\nimport useProgram from \"../../utils/hooks/programs/useProgram\";\nimport EditorTextInput from \"../../components/editor/inputs/EditorTextInput\";\nimport { useEffect, useMemo, useState } from \"react\";\nimport { testingTypeOptions, environmentTypeOptions, complianceTypeOptions, complianceTypes } from \"../../utils/constants\";\nimport FormDropdown from \"../../components/forms/inputs/FormDropdown\";\nimport { FiHelpCircle } from \"react-icons/fi\";\nimport JiraSetupGuideModal from \"../../components/modals/JiraSetupGuideModal\";\nimport FormProgressTimeline from \"./FormProgressTimeline\";\nimport ProgramEditorLogo from \"../../components/editor/inputs/ProgramEditorLogo\";\nimport ProgramEditorAttachments from \"../../components/editor/inputs/ProgramEditorAttachments\";\n\n// Define CSS keyframe animations using a style element\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnimationStyles = () => /*#__PURE__*/_jsxDEV(\"style\", {\n  children: `\n      @keyframes pulse {\n        0%, 100% {\n          transform: scale(1);\n        }\n        50% {\n          transform: scale(1.02);\n          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\n        }\n      }\n      \n      @keyframes checkmark-appear {\n        0% {\n          transform: scale(0) rotate(-45deg);\n          opacity: 0;\n        }\n        100% {\n          transform: scale(1) rotate(-45deg);\n          opacity: 1;\n        }\n      }\n      \n      @keyframes slide-in {\n        0% {\n          transform: translateX(16px);\n        }\n        100% {\n          transform: translateX(0);\n        }\n      }\n    `\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 41,\n  columnNumber: 3\n}, this);\n_c = AnimationStyles;\nconst ProgramEditor = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const [selectedPogramType, setSelectedPogramType] = useState(ProgramType.PTAAS);\n  const [privateVisibility, setPrivateVisibility] = useState(undefined); // 0 = Public, 1 = Private\n  // Always include CTB as a notification method\n  const [notificationMethods, setNotificationMethods] = useState([NotificationMethods.CTB]);\n\n  // Track which integrations are selected (Slack, Jira, or both)\n  const [slackSelected, setSlackSelected] = useState(false);\n  const [jiraSelected, setJiraSelected] = useState(false);\n\n  // JIRA testing state\n  const [jiraTestResult, setJiraTestResult] = useState(null);\n\n  // Jira setup guide modal state\n  const [showJiraGuide, setShowJiraGuide] = useState(false);\n  const [testing, setTesting] = useState(false);\n\n  // Keep track of integration selections and notification methods\n  const [selectedComplianceType, setSelectedComplianceType] = useState(\"\");\n  const {\n    program,\n    saveProgram\n  } = useProgram(id !== undefined ? parseInt(id) : undefined);\n  usePageTitle(id ? \"Editing Program\" : \"Create Program\");\n\n  // Test JIRA connection function\n  const testJiraConnection = async formData => {\n    setTesting(true);\n    setJiraTestResult(null);\n    try {\n      const config = {\n        url: formData.jira_url,\n        email: formData.jira_email,\n        api_token: formData.jira_api_token,\n        project_key: formData.jira_project_key\n      };\n\n      // Validate all fields are provided\n      if (!config.url || !config.email || !config.api_token || !config.project_key) {\n        setJiraTestResult({\n          success: false,\n          error: \"Please fill in all JIRA configuration fields before testing\"\n        });\n        return;\n      }\n      console.log(\"Testing JIRA connection with config:\", {\n        ...config,\n        api_token: config.api_token ? \"[HIDDEN]\" : \"MISSING\"\n      });\n\n      // Import axios instance\n      const axios = (await import(\"../../utils/api/axios\")).default;\n      console.log(\"Making request to:\", '/v2/programs/jira/validate');\n      console.log(\"Backend base URL:\", process.env.REACT_APP_BACKEND_BASE_URL);\n      const response = await axios.post('/v2/programs/jira/validate', config);\n      console.log(\"JIRA test response:\", response.data);\n      setJiraTestResult(response.data);\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3, _error$response4, _error$response5, _error$response6, _error$response6$data;\n      console.error(\"JIRA test error details:\", {\n        message: error.message,\n        status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n        data: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data,\n        config: error.config\n      });\n      let errorMessage = \"Failed to connect to server\";\n      let suggestions = [\"Please check your internet connection and try again\"];\n      if (((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status) === 401) {\n        errorMessage = \"Authentication failed - Please log in again\";\n        suggestions = [\"Try refreshing the page and logging in again\"];\n      } else if (((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.status) === 403) {\n        errorMessage = \"Access denied - Insufficient permissions\";\n        suggestions = [\"Contact your administrator for access to JIRA testing\"];\n      } else if (((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.status) === 404) {\n        errorMessage = \"API endpoint not found\";\n        suggestions = [\"Please check that the backend server is running\", \"Verify the API endpoint is properly configured\"];\n      } else if ((_error$response6 = error.response) !== null && _error$response6 !== void 0 && (_error$response6$data = _error$response6.data) !== null && _error$response6$data !== void 0 && _error$response6$data.message) {\n        errorMessage = error.response.data.message;\n        suggestions = error.response.data.suggestions || suggestions;\n      }\n      setJiraTestResult({\n        success: false,\n        error: errorMessage,\n        suggestions: suggestions\n      });\n    } finally {\n      setTesting(false);\n    }\n  };\n\n  // Only showing the relevant part where we define defaultValues\n\n  const defaultValues = useMemo(() => ({\n    title: (program === null || program === void 0 ? void 0 : program.title) || \"\",\n    type: (program === null || program === void 0 ? void 0 : program.type) || ProgramType.PTAAS,\n    private: (program === null || program === void 0 ? void 0 : program.private) === undefined ? undefined : program.private ? 1 : 0,\n    description: (program === null || program === void 0 ? void 0 : program.description) || \"\",\n    targets: (program === null || program === void 0 ? void 0 : program.targets) || [],\n    vpn: program === null || program === void 0 ? void 0 : program.vpn,\n    credentials: program === null || program === void 0 ? void 0 : program.credentials,\n    scope: (program === null || program === void 0 ? void 0 : program.scope) || \"\",\n    outOfScope: (program === null || program === void 0 ? void 0 : program.outOfScope) || \"\",\n    knownVulnerabilities: program === null || program === void 0 ? void 0 : program.knownVulnerabilities,\n    rewards: (program === null || program === void 0 ? void 0 : program.payoutRange) || {},\n    rewardPolicy: (program === null || program === void 0 ? void 0 : program.rewardPolicy) || \"\",\n    termsOfService: (program === null || program === void 0 ? void 0 : program.termsOfService) || \"\",\n    other: program === null || program === void 0 ? void 0 : program.other,\n    profilePicture: undefined,\n    testingType: (program === null || program === void 0 ? void 0 : program.testing_type) || \"\",\n    environmentType: (program === null || program === void 0 ? void 0 : program.environment_type) || \"\",\n    complianceType: (program === null || program === void 0 ? void 0 : program.compliance_type) || \"\",\n    otherComplianceType: (program === null || program === void 0 ? void 0 : program.other_compliance_type) || \"\",\n    expectedStartDate: program !== null && program !== void 0 && program.expected_start_date ? new Date(program === null || program === void 0 ? void 0 : program.expected_start_date) : undefined,\n    expectedEndDate: program !== null && program !== void 0 && program.expected_end_date ? new Date(program === null || program === void 0 ? void 0 : program.expected_end_date) : undefined,\n    notification_methods: program === null || program === void 0 ? void 0 : program.notification_methods,\n    attachments: [],\n    existingAttachments: (program === null || program === void 0 ? void 0 : program.attachments) || []\n  }), [program]);\n  useEffect(() => {\n    if (program !== null && program !== void 0 && program.type) setSelectedPogramType(program.type);\n    if (program !== null && program !== void 0 && program.compliance_type) setSelectedComplianceType(program === null || program === void 0 ? void 0 : program.compliance_type);\n  }, [program]);\n  useEffect(() => {\n    if (selectedPogramType === ProgramType.VDP) {\n      setPrivateVisibility(0);\n    } else if (selectedPogramType === ProgramType.PTAAS) {\n      setPrivateVisibility(1);\n    } else {\n      setPrivateVisibility(undefined);\n    }\n  }, [selectedPogramType]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(AnimationStyles, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(EditorLayout, {\n      defaultValues: defaultValues,\n      actions: [{\n        label: id ? \"Save\" : \"Create\",\n        type: \"submit\"\n      }],\n      onSubmit: data => {\n        // Clone the data to avoid modifying the original\n        const formData = {\n          ...data\n        };\n\n        // Set private visibility and notification methods\n        formData.private = privateVisibility;\n        formData.notification_methods = notificationMethods;\n\n        // Validate integration configurations\n\n        // Validate Jira configuration if selected\n        if (jiraSelected) {\n          // Check all required Jira fields are filled\n          const missingFields = [];\n          if (!formData.jira_url) missingFields.push(\"Jira URL\");\n          if (!formData.jira_email) missingFields.push(\"Jira Email\");\n          if (!formData.jira_api_token) missingFields.push(\"Jira API Token\");\n          if (!formData.jira_project_key) missingFields.push(\"Jira Project Key\");\n          if (missingFields.length > 0) {\n            alert(`Please fill in all required Jira fields: ${missingFields.join(\", \")}`);\n            return;\n          }\n        }\n\n        // Validate Slack configuration if selected\n        if (slackSelected && !formData.slack_channel_link) {\n          alert(\"Please provide a Slack channel URL\");\n          return;\n        }\n\n        // Save the program and navigate back\n        saveProgram(formData);\n        navigate(-1);\n      },\n      children: [/*#__PURE__*/_jsxDEV(FormProgressTimeline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EditorSection, {\n        sectionTitle: \"Program Details\",\n        children: [/*#__PURE__*/_jsxDEV(EditorTextInput, {\n          name: \"title\",\n          inputTitle: \"Program Name\",\n          details: \"Specify the name of this bug bounty (E.G. [Brand Name] Vulnerability Program).\",\n          placeholder: \" \",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(EditorInput, {\n          name: \"type\",\n          inputTitle: \"Type\",\n          required: true,\n          children: [/*#__PURE__*/_jsxDEV(FormDropdown, {\n            name: \"type\",\n            placeholder: \"Select Program Type\",\n            options: [{\n              value: ProgramType.PTAAS,\n              label: \"PTaaS\"\n            }, {\n              value: ProgramType.VDP,\n              label: \"\"\n            }, {\n              value: ProgramType.BugBounty,\n              label: \"\"\n            }],\n            rules: {\n              required: true\n            },\n            className: \" m-0 flex w-full flex-col p-0  \",\n            onChangeCallback: value => setSelectedPogramType(value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"details mt-1 text-sm text-gray-600\",\n            children: \"Select the program's approach for researchers to process vulnerabilities.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(EditorSection, {\n          sectionTitle: \"App Integrations\",\n          className: \"mt-4 mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6 max-w-full overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-blue-600 to-indigo-700 rounded-lg shadow-lg p-6 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-white mb-2\",\n                children: \"Connect External Services\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-blue-100 mb-0\",\n                children: \"Enhance your workflow with seamless third-party integrations for real-time notifications and issue tracking.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 sm:grid-cols-2 gap-6 mb-6 max-w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `rounded-xl border-2 p-5 max-w-full ${slackSelected ? \"border-green-500 bg-gradient-to-br from-green-50 to-green-100\" : \"border-gray-200 bg-white\"} hover:shadow-lg transition-all duration-300 cursor-pointer relative overflow-hidden`,\n                style: {\n                  animation: slackSelected ? 'pulse 0.6s cubic-bezier(0.4, 0, 0.6, 1)' : 'none'\n                },\n                onClick: () => {\n                  // Toggle the selection when the card is clicked\n                  const newValue = !slackSelected;\n                  setSlackSelected(newValue);\n\n                  // Update notification methods\n                  setNotificationMethods(prevMethods => {\n                    const methods = [...prevMethods];\n                    // Always include CTB\n                    if (!methods.includes(NotificationMethods.CTB)) {\n                      methods.push(NotificationMethods.CTB);\n                    }\n\n                    // Add or remove Slack\n                    if (newValue && !methods.includes(NotificationMethods.SLACK)) {\n                      methods.push(NotificationMethods.SLACK);\n                    } else if (!newValue) {\n                      return methods.filter(m => m !== NotificationMethods.SLACK);\n                    }\n                    return methods;\n                  });\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `absolute top-0 right-0 transition-transform duration-500 ease-in-out ${slackSelected ? 'translate-x-0' : 'translate-x-16'}`,\n                  style: {\n                    transitionDelay: slackSelected ? '0.1s' : '0s'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-500 text-white h-8 w-8 flex items-center justify-center transform rotate-45 translate-y-[-16px] translate-x-[-16px] shadow-md\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: `h-4 w-4 transform -rotate-45 transition-opacity duration-300 ${slackSelected ? 'opacity-100' : 'opacity-0'}`,\n                      viewBox: \"0 0 20 20\",\n                      fill: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 380,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 374,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gradient-to-br from-green-400 to-green-600 text-white rounded-xl h-12 w-12 flex items-center justify-center mr-4 shadow-md\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        className: \"h-6 w-6\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 2,\n                          d: \"M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 388,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 387,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 386,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-bold\",\n                        children: \"Slack\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 392,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: \"Real-time notifications in your workspace\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 393,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 391,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `rounded-xl border-2 p-5 max-w-full ${jiraSelected ? \"border-blue-500 bg-gradient-to-br from-blue-50 to-blue-100\" : \"border-gray-200 bg-white\"} hover:shadow-lg transition-all duration-300 cursor-pointer relative overflow-hidden`,\n                style: {\n                  animation: jiraSelected ? 'pulse 0.6s cubic-bezier(0.4, 0, 0.6, 1)' : 'none'\n                },\n                onClick: () => {\n                  // Toggle the selection when the card is clicked\n                  const newValue = !jiraSelected;\n                  setJiraSelected(newValue);\n\n                  // Update notification methods\n                  setNotificationMethods(prevMethods => {\n                    const methods = [...prevMethods];\n                    // Always include CTB\n                    if (!methods.includes(NotificationMethods.CTB)) {\n                      methods.push(NotificationMethods.CTB);\n                    }\n\n                    // Add or remove Jira\n                    if (newValue && !methods.includes(NotificationMethods.JIRA)) {\n                      methods.push(NotificationMethods.JIRA);\n                    } else if (!newValue) {\n                      return methods.filter(m => m !== NotificationMethods.JIRA);\n                    }\n                    return methods;\n                  });\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `absolute top-0 right-0 transition-transform duration-500 ease-in-out ${jiraSelected ? 'translate-x-0' : 'translate-x-16'}`,\n                  style: {\n                    transitionDelay: jiraSelected ? '0.1s' : '0s'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-500 text-white h-8 w-8 flex items-center justify-center transform rotate-45 translate-y-[-16px] translate-x-[-16px] shadow-md\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: `h-4 w-4 transform -rotate-45 transition-opacity duration-300 ${jiraSelected ? 'opacity-100' : 'opacity-0'}`,\n                      viewBox: \"0 0 20 20\",\n                      fill: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 441,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 435,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gradient-to-br from-blue-400 to-blue-600 text-white rounded-xl h-12 w-12 flex items-center justify-center mr-4 shadow-md\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      className: \"h-6 w-6\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M12 3l8 4.5v9L12 21l-8-4.5v-9L12 3z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 448,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M12 12l8-4.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 449,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M12 12v9\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 450,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M12 12L4 7.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 451,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 447,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 446,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-bold\",\n                      children: \"Jira\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 455,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600\",\n                      children: \"Seamless issue tracking integration\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 456,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 border border-gray-200 rounded-lg p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"h-5 w-5 text-blue-400\",\n                    viewBox: \"0 0 20 20\",\n                    fill: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 466,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 465,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"ml-3 text-sm text-gray-700\",\n                  children: \"You can select multiple integrations for your program. Each integration requires specific configuration.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), slackSelected && /*#__PURE__*/_jsxDEV(EditorSection, {\n          sectionTitle: \"Slack Configuration\",\n          className: \"mt-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white p-6 rounded-xl shadow-md border-2 border-green-200 overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-5 pb-4 border-b border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-br from-green-400 to-green-600 text-white rounded-xl h-12 w-12 flex items-center justify-center mr-4 shadow-md\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  className: \"h-6 w-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M14.5 10c-.83 0-1.5-.67-1.5-1.5v-5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M20.5 10H19V8.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 484,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M9.5 14c.83 0 1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5S8 21.33 8 20.5v-5c0-.83.67-1.5 1.5-1.5z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M3.5 14H5v1.5c0 .83-.67 1.5-1.5 1.5S2 16.33 2 15.5 2.67 14 3.5 14z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 486,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M14 14.5c0-.83.67-1.5 1.5-1.5h5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-5c-.83 0-1.5-.67-1.5-1.5z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M15.5 19H14v1.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5-.67-1.5-1.5-1.5z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M10 9.5C10 8.67 9.33 8 8.5 8h-5C2.67 8 2 8.67 2 9.5S2.67 11 3.5 11h5c.83 0 1.5-.67 1.5-1.5z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 489,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M8.5 5H10V3.5C10 2.67 9.33 2 8.5 2S7 2.67 7 3.5 7.67 5 8.5 5z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold text-xl text-gray-900\",\n                  children: \"Slack Integration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600\",\n                  children: \"Configure real-time notifications for your Slack workspace\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-100 p-4 mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"h-6 w-6 text-green-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 503,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 502,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-green-800 font-medium\",\n                    children: \"Integration Guide\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-700 mt-1\",\n                    children: [\"To integrate with Slack, you'll need to create an incoming webhook for your channel. Visit the \", /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"https://api.slack.com/messaging/webhooks\",\n                      target: \"_blank\",\n                      rel: \"noopener noreferrer\",\n                      className: \"font-medium text-green-600 hover:text-green-800 underline\",\n                      children: \"Slack API documentation\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 509,\n                      columnNumber: 120\n                    }, this), \" to learn how.\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 gap-6 max-w-full\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-5 border border-green-200 shadow-md transition-all duration-300 hover:shadow-lg max-w-full overflow-hidden\",\n                children: [/*#__PURE__*/_jsxDEV(EditorTextInput, {\n                  name: \"slack_channel_link\",\n                  inputTitle: \"Slack Webhook URL\",\n                  details: \"Enter your Slack webhook URL to receive notifications\",\n                  placeholder: \"https://hooks.slack.com/services/XXXXX/XXXXX/XXXXX\",\n                  required: slackSelected\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-2 text-sm text-green-700 flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-4 w-4 mr-1\",\n                    viewBox: \"0 0 20 20\",\n                    fill: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 527,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 526,\n                    columnNumber: 23\n                  }, this), \"The incoming webhook URL from your Slack workspace settings\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 13\n        }, this), jiraSelected && /*#__PURE__*/_jsxDEV(EditorSection, {\n          sectionTitle: \"Jira Configuration\",\n          className: \"mt-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-200 max-w-full overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-4 pb-3 border-b border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-500 text-white rounded-lg h-10 w-10 flex items-center justify-center mr-3 flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  className: \"h-5 w-5\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 3l8 4.5v9L12 21l-8-4.5v-9L12 3z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 543,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 12l8-4.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 544,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 12v9\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 545,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 12L4 7.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 546,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"min-w-0 flex-1\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-semibold text-lg text-gray-900\",\n                      children: \"Jira Integration\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 552,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 text-sm\",\n                      children: \"Connect your project with Jira for issue tracking\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 553,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    onClick: () => setShowJiraGuide(true),\n                    className: \"flex items-center gap-2 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(FiHelpCircle, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 560,\n                      columnNumber: 25\n                    }, this), \"Setup Guide\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6 max-w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(EditorTextInput, {\n                  name: \"jira_url\",\n                  inputTitle: \"Jira Site URL\",\n                  details: \"The base URL for your Jira site (e.g., https://company.atlassian.net)\",\n                  placeholder: \"https://yourdomain.atlassian.net\",\n                  required: jiraSelected\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(EditorTextInput, {\n                  name: \"jira_email\",\n                  inputTitle: \"Jira Email\",\n                  details: \"The email associated with your Jira account\",\n                  placeholder: \"<EMAIL>\",\n                  required: jiraSelected\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(EditorTextInput, {\n                  name: \"jira_api_token\",\n                  inputTitle: \"Jira API Token\",\n                  details: \"Your Jira API token for authentication\",\n                  placeholder: \"Enter your Jira API token\",\n                  required: jiraSelected\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(EditorTextInput, {\n                  name: \"jira_project_key\",\n                  inputTitle: \"Jira Project Key\",\n                  details: \"The project key for your Jira project (e.g., PROJECT)\",\n                  placeholder: \"e.g., PROJECT\",\n                  required: jiraSelected\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 605,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6 pt-6 border-t border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: \"Connection Test\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => {\n                    const form = document.querySelector('form');\n                    if (form) {\n                      const formData = new FormData(form);\n                      const data = {\n                        jira_url: formData.get('jira_url'),\n                        jira_email: formData.get('jira_email'),\n                        jira_api_token: formData.get('jira_api_token'),\n                        jira_project_key: formData.get('jira_project_key')\n                      };\n                      testJiraConnection(data);\n                    }\n                  },\n                  disabled: testing,\n                  className: `px-6 py-2 rounded-lg font-medium transition-all duration-200 ${testing ? 'bg-gray-400 text-white cursor-not-allowed' : 'bg-blue-500 hover:bg-blue-600 text-white hover:shadow-lg transform hover:scale-105'}`,\n                  children: testing ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 644,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 645,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 643,\n                      columnNumber: 27\n                    }, this), \"Testing...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 642,\n                    columnNumber: 25\n                  }, this) : 'Test Connection'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 19\n              }, this), jiraTestResult && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `p-4 rounded-lg border max-w-full overflow-hidden ${jiraTestResult.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `flex-shrink-0 ${jiraTestResult.success ? 'text-green-400' : 'text-red-400'}`,\n                    children: jiraTestResult.success ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"h-5 w-5\",\n                      viewBox: \"0 0 20 20\",\n                      fill: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 666,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 665,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"h-5 w-5\",\n                      viewBox: \"0 0 20 20\",\n                      fill: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 670,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 669,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 663,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-3 flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: `font-medium ${jiraTestResult.success ? 'text-green-800' : 'text-red-800'}`,\n                      children: jiraTestResult.success ? 'Connection Successful!' : 'Connection Failed'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 675,\n                      columnNumber: 27\n                    }, this), jiraTestResult.success ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-2\",\n                      children: jiraTestResult.projectInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-green-700 text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Project:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 682,\n                            columnNumber: 38\n                          }, this), \" \", jiraTestResult.projectInfo.name, \" (\", jiraTestResult.projectInfo.key, \")\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 682,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Available Issue Types:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 683,\n                            columnNumber: 38\n                          }, this), \" \", jiraTestResult.projectInfo.issueTypes.join(', ')]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 683,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 681,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 679,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-red-700 text-sm\",\n                        children: jiraTestResult.error\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 689,\n                        columnNumber: 31\n                      }, this), jiraTestResult.suggestions && jiraTestResult.suggestions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-red-700 text-sm font-medium\",\n                          children: \"Suggestions:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 692,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                          className: \"text-red-600 text-sm mt-1 list-disc list-inside\",\n                          children: jiraTestResult.suggestions.map((suggestion, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: suggestion\n                          }, index, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 695,\n                            columnNumber: 39\n                          }, this))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 693,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 691,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 688,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 674,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 662,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 13\n        }, this), selectedPogramType === ProgramType.BugBounty && /*#__PURE__*/_jsxDEV(EditorInput, {\n          name: \"private\",\n          inputTitle: \"Program Access\",\n          required: true,\n          children: [/*#__PURE__*/_jsxDEV(FormDropdown, {\n            name: \"private\",\n            options: [{\n              value: 0,\n              label: \"Public\",\n              isDisabled: false\n            }, {\n              value: 1,\n              label: \"Private\",\n              isDisabled: false\n            }],\n            value: privateVisibility !== undefined ? privateVisibility.toString() : \"0\",\n            onChangeCallback: value => setPrivateVisibility(value ? Number(value) : 0),\n            rules: {\n              required: true\n            },\n            className: \"py-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 713,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"details mt-1 text-sm text-gray-600\",\n            children: \"Select the visibility and access type of this program.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 730,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 712,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(EditorRichTextInput, {\n          name: \"description\",\n          inputTitle: \"Description\",\n          details: \"Provide a description about the company and the program.\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 736,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EditorSection, {\n        sectionTitle: \"Targets & Credentitals\",\n        className: \"mt-6\",\n        children: [/*#__PURE__*/_jsxDEV(TargetsInput, {\n          name: \"targets\",\n          inputTitle: \"Targets\",\n          details: \"Specify digital assets and their types that are eligible for testing and reporting in this program.\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 745,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ProgramEditorAttachments, {\n          name: \"attachments\",\n          inputTitle: \"Target Attachments\",\n          details: \"Optionally add attachments to this program.\",\n          note: \"Supports any file type\",\n          placeholder: \"No Attachments\",\n          accept: \"*\" // Allow all files\n          ,\n          multiple: true // Enable multiple selection\n          ,\n          defaultLinks: (program === null || program === void 0 ? void 0 : program.attachments) || []\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 752,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(EditorRichTextInput, {\n          name: \"vpn\",\n          inputTitle: \"VPN Details\",\n          details: \"Provide information about any required VPNs (virtual private networks) to access the target assets\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 763,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(EditorRichTextInput, {\n          name: \"credentials\",\n          inputTitle: \"Credentials\",\n          details: \"Provide necessary access information for testing the target assets (if applicable)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 769,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 744,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EditorSection, {\n        sectionTitle: \"Scope Description\",\n        className: \"mt-6\",\n        children: [/*#__PURE__*/_jsxDEV(EditorRichTextInput, {\n          name: \"scope\",\n          inputTitle: \"Scope\",\n          details: \"For a thorough understanding of the testing scope and assets, please provide the necessary documentation and any relevant walkthrough video links.\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 777,\n          columnNumber: 11\n        }, this), !!(selectedPogramType !== ProgramType.PTAAS) && /*#__PURE__*/_jsxDEV(EditorRichTextInput, {\n          name: \"outOfScope\",\n          inputTitle: \"Out of Scope\",\n          details: \"Provide details about the digital assets that are NOT open for examination and reporting\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 785,\n          columnNumber: 13\n        }, this), !!(selectedPogramType !== ProgramType.PTAAS) && /*#__PURE__*/_jsxDEV(EditorRichTextInput, {\n          name: \"knownVulnerabilities\",\n          inputTitle: \"Known Vulnerabilities\",\n          details: \"Provide details about any addressed vulnerabilites (from previous experience or approved reports)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 794,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 776,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EditorSection, {\n        sectionTitle: selectedPogramType === ProgramType.PTAAS ? \"Service Level Agreement\" : \"Rewards\",\n        className: \"mt-6\",\n        children: [!!(selectedPogramType === ProgramType.BugBounty) && /*#__PURE__*/_jsxDEV(ProgramRewardsInput, {\n          name: \"rewards\",\n          inputTitle: \"Reward Tiers\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 811,\n          columnNumber: 13\n        }, this), !!(selectedPogramType === ProgramType.BugBounty) && /*#__PURE__*/_jsxDEV(EditorRichTextInput, {\n          name: \"rewardPolicy\",\n          inputTitle: \"Reward Policy\",\n          details: \"Provide rules and guidelines outlining how researchers are compenstated for discovering and responsibly disclosing vulnerabilities\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 819,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(EditorRichTextInput, {\n          name: \"termsOfService\",\n          inputTitle: selectedPogramType === ProgramType.PTAAS ? \"Service Level Agreement\" : \"Terms and Conditions\",\n          details: selectedPogramType === ProgramType.PTAAS ? \"SLA will be automatically updated once the program is approved by our admins\" : \"Provide an outline of the terms and conditions of how this program will be conducted\",\n          required: selectedPogramType !== ProgramType.PTAAS,\n          disabled: selectedPogramType === ProgramType.PTAAS\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 827,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 802,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EditorSection, {\n        sectionTitle: \"Additional Information\",\n        className: \"mt-6\",\n        children: [!!(selectedPogramType === ProgramType.PTAAS) && /*#__PURE__*/_jsxDEV(DropDownEditor, {\n          name: \"testingType\",\n          inputTitle: \"Testing Type\",\n          placeholder: \"Select Testing Type\",\n          options: testingTypeOptions,\n          value: \"\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 846,\n          columnNumber: 13\n        }, this), !!(selectedPogramType === ProgramType.PTAAS) && /*#__PURE__*/_jsxDEV(DropDownEditor, {\n          name: \"environmentType\",\n          inputTitle: \"Environment Type\",\n          placeholder: \"Select Environment Type\",\n          options: environmentTypeOptions,\n          value: \"\",\n          required: true,\n          className: \" mt-5 !bg-ctb-blue-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 856,\n          columnNumber: 13\n        }, this), !!(selectedPogramType === ProgramType.PTAAS) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex w-full gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(DatePicker, {\n            name: \"expectedStartDate\",\n            inputTitle: \"Expected Start Date\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 868,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(DatePicker, {\n            name: \"expectedEndDate\",\n            inputTitle: \"Expected End Date\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 874,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 867,\n          columnNumber: 13\n        }, this), !!(selectedPogramType === ProgramType.PTAAS) && /*#__PURE__*/_jsxDEV(DropDownEditor, {\n          name: \"complianceType\",\n          placeholder: \"Select Compliance Type\",\n          inputTitle: \"Compliance Type\",\n          options: complianceTypeOptions,\n          value: \"\",\n          required: true,\n          onChangeCallback: value => {\n            setSelectedComplianceType(value);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 882,\n          columnNumber: 13\n        }, this), !!(selectedPogramType === ProgramType.PTAAS) && selectedComplianceType === complianceTypes.Other && /*#__PURE__*/_jsxDEV(EditorTextInput, {\n          name: \"otherComplianceType\",\n          inputTitle: \"Specify Compliance Type\",\n          details: \"Mention the type of compliance, mention NA if not applicable\",\n          placeholder: \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 896,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(EditorRichTextInput, {\n          name: \"other\",\n          inputTitle: \"Other\",\n          details: \"Provide any essential information that has not been disclosed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 904,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ProgramEditorLogo, {\n          name: \"profilePicture\",\n          inputTitle: \"Logo Image\",\n          details: \"Optionally add a logo image to this program.\",\n          note: \"Only support .jpg, .png\",\n          placeholder: \"No Logo\",\n          accept: \"image/*\",\n          cropImage: {\n            title: \"New Logo\",\n            cropSize: {\n              width: 300,\n              height: 300\n            },\n            previewSize: {\n              width: 330,\n              height: 390\n            },\n            minZoom: 50,\n            maxZoom: 300\n          },\n          defaultLinks: program !== null && program !== void 0 && program.profilePicture ? [program.profilePicture] : []\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 910,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 844,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(JiraSetupGuideModal, {\n      isOpen: showJiraGuide,\n      onClose: () => setShowJiraGuide(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 938,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 238,\n    columnNumber: 5\n  }, this);\n};\n_s(ProgramEditor, \"YJ+REeFpW2dCTE0GyPxXMnICXLM=\", false, function () {\n  return [useParams, useNavigate, useProgram, usePageTitle];\n});\n_c2 = ProgramEditor;\nexport default ProgramEditor;\nvar _c, _c2;\n$RefreshReg$(_c, \"AnimationStyles\");\n$RefreshReg$(_c2, \"ProgramEditor\");", "map": {"version": 3, "names": ["useParams", "useNavigate", "EditorLayout", "EditorInput", "usePageTitle", "ProgramType", "NotificationMethods", "EditorSection", "TargetsInput", "DropDownEditor", "DatePicker", "EditorRichTextInput", "ProgramRewardsInput", "useProgram", "EditorTextInput", "useEffect", "useMemo", "useState", "testingTypeOptions", "environmentTypeOptions", "complianceTypeOptions", "complianceTypes", "FormDropdown", "FiHelpCircle", "JiraSetupGuideModal", "FormProgressTimeline", "ProgramEditorLogo", "ProgramEditorAttachments", "jsxDEV", "_jsxDEV", "AnimationStyles", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "ProgramEditor", "_s", "id", "navigate", "selectedPogramType", "setSelectedPogramType", "PTAAS", "privateVisibility", "setPrivateVisibility", "undefined", "notificationMethods", "setNotificationMethods", "CTB", "slackSelected", "setSlackSelected", "jiraSelected", "setJiraSelected", "jiraTestResult", "setJiraTestResult", "showJiraGuide", "setShowJiraGuide", "testing", "setTesting", "selectedComplianceType", "setSelectedComplianceType", "program", "saveProgram", "parseInt", "testJiraConnection", "formData", "config", "url", "jira_url", "email", "jira_email", "api_token", "jira_api_token", "project_key", "jira_project_key", "success", "error", "console", "log", "axios", "default", "process", "env", "REACT_APP_BACKEND_BASE_URL", "response", "post", "data", "_error$response", "_error$response2", "_error$response3", "_error$response4", "_error$response5", "_error$response6", "_error$response6$data", "message", "status", "errorMessage", "suggestions", "defaultValues", "title", "type", "private", "description", "targets", "vpn", "credentials", "scope", "outOfScope", "knownVulnerabilities", "rewards", "payoutRange", "rewardPolicy", "termsOfService", "other", "profilePicture", "testingType", "testing_type", "environmentType", "environment_type", "complianceType", "compliance_type", "otherComplianceType", "other_compliance_type", "expectedStartDate", "expected_start_date", "Date", "expectedEndDate", "expected_end_date", "notification_methods", "attachments", "existingAttachments", "VDP", "className", "actions", "label", "onSubmit", "missingFields", "push", "length", "alert", "join", "slack_channel_link", "sectionTitle", "name", "inputTitle", "details", "placeholder", "required", "options", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rules", "onChangeCallback", "style", "animation", "onClick", "newValue", "prevMethods", "methods", "includes", "SLACK", "filter", "m", "transitionDelay", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "JIRA", "href", "target", "rel", "form", "document", "querySelector", "FormData", "get", "disabled", "cx", "cy", "r", "projectInfo", "key", "issueTypes", "map", "suggestion", "index", "isDisabled", "toString", "Number", "note", "accept", "multiple", "defaultLinks", "Other", "cropImage", "cropSize", "width", "height", "previewSize", "minZoom", "max<PERSON><PERSON>", "isOpen", "onClose", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/jira-old/CTB-Frontend/src/pages/programs/ProgramEditor.tsx"], "sourcesContent": ["import { use<PERSON>ara<PERSON>, useNavigate } from \"react-router-dom\";\r\nimport EditorLayout from \"../../layouts/EditorLayout\";\r\nimport EditorInput from \"../../components/editor/inputs/EditorInput\";\r\nimport usePageTitle from \"../../utils/hooks/usePageTitle\";\r\n\r\nimport {\r\n  ProgramType,\r\n  NotificationMethods\r\n} from \"../../utils/api/endpoints/programs/parsePrograms\";\r\nimport EditorSection from \"../../components/editor/EditorSection\";\r\nimport TargetsInput from \"../../components/editor/inputs/programs/TargetsInput\";\r\nimport DropDownEditor from \"../../components/editor/inputs/programs/DropDownEditor\";\r\nimport DatePicker from \"../../components/editor/inputs/programs/DatePicker\";\r\nimport EditorRichTextInput from \"../../components/editor/inputs/EditorRichTextInput\";\r\nimport ProgramRewardsInput from \"../../components/editor/inputs/programs/ProgramRewardsInput\";\r\n\r\nimport useProgram, {\r\n  ProgramUpdateValues\r\n} from \"../../utils/hooks/programs/useProgram\";\r\nimport EditorTextInput from \"../../components/editor/inputs/EditorTextInput\";\r\nimport { useEffect, useMemo, useState } from \"react\";\r\nimport {\r\n  testingTypeOptions,\r\n  environmentTypeOptions,\r\n  complianceTypeOptions,\r\n  complianceTypes\r\n} from \"../../utils/constants\";\r\n\r\n\r\nimport FormDropdown from \"../../components/forms/inputs/FormDropdown\";\r\nimport { FiHelpCircle } from \"react-icons/fi\";\r\nimport JiraSetupGuideModal from \"../../components/modals/JiraSetupGuideModal\";\r\n\r\nimport FormProgressTimeline from \"./FormProgressTimeline\";\r\nimport ProgramEditorLogo from \"../../components/editor/inputs/ProgramEditorLogo\";\r\nimport ProgramEditorAttachments from \"../../components/editor/inputs/ProgramEditorAttachments\";\r\n\r\n\r\n// Define CSS keyframe animations using a style element\r\nconst AnimationStyles = () => (\r\n  <style>\r\n    {`\r\n      @keyframes pulse {\r\n        0%, 100% {\r\n          transform: scale(1);\r\n        }\r\n        50% {\r\n          transform: scale(1.02);\r\n          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\r\n        }\r\n      }\r\n      \r\n      @keyframes checkmark-appear {\r\n        0% {\r\n          transform: scale(0) rotate(-45deg);\r\n          opacity: 0;\r\n        }\r\n        100% {\r\n          transform: scale(1) rotate(-45deg);\r\n          opacity: 1;\r\n        }\r\n      }\r\n      \r\n      @keyframes slide-in {\r\n        0% {\r\n          transform: translateX(16px);\r\n        }\r\n        100% {\r\n          transform: translateX(0);\r\n        }\r\n      }\r\n    `}\r\n  </style>\r\n);\r\n\r\nconst ProgramEditor = () => {\r\n  const { id } = useParams();\r\n  const navigate = useNavigate();\r\n  const [selectedPogramType, setSelectedPogramType] = useState<ProgramType>(\r\n    ProgramType.PTAAS\r\n  );\r\n  const [privateVisibility, setPrivateVisibility] = useState<0 | 1 | undefined>(\r\n    undefined\r\n  ); // 0 = Public, 1 = Private\r\n  // Always include CTB as a notification method\r\n  const [notificationMethods, setNotificationMethods] = useState<String[]>([\r\n    NotificationMethods.CTB\r\n  ]);\r\n  \r\n  // Track which integrations are selected (Slack, Jira, or both)\r\n  const [slackSelected, setSlackSelected] = useState<boolean>(false);\r\n  const [jiraSelected, setJiraSelected] = useState<boolean>(false);\r\n  \r\n  // JIRA testing state\r\n  const [jiraTestResult, setJiraTestResult] = useState<any>(null);\r\n\r\n  // Jira setup guide modal state\r\n  const [showJiraGuide, setShowJiraGuide] = useState<boolean>(false);\r\n  const [testing, setTesting] = useState<boolean>(false);\r\n  \r\n  // Keep track of integration selections and notification methods\r\n  const [selectedComplianceType, setSelectedComplianceType] = useState(\"\");\r\n  const { program, saveProgram } = useProgram(\r\n    id !== undefined ? parseInt(id) : undefined\r\n  );\r\n\r\n  usePageTitle(id ? \"Editing Program\" : \"Create Program\");\r\n\r\n  // Test JIRA connection function\r\n  const testJiraConnection = async (formData: any) => {\r\n    setTesting(true);\r\n    setJiraTestResult(null);\r\n    \r\n    try {\r\n      const config = {\r\n        url: formData.jira_url,\r\n        email: formData.jira_email,\r\n        api_token: formData.jira_api_token,\r\n        project_key: formData.jira_project_key\r\n      };\r\n\r\n      // Validate all fields are provided\r\n      if (!config.url || !config.email || !config.api_token || !config.project_key) {\r\n        setJiraTestResult({\r\n          success: false,\r\n          error: \"Please fill in all JIRA configuration fields before testing\"\r\n        });\r\n        return;\r\n      }\r\n\r\n      console.log(\"Testing JIRA connection with config:\", {\r\n        ...config,\r\n        api_token: config.api_token ? \"[HIDDEN]\" : \"MISSING\"\r\n      });\r\n\r\n      // Import axios instance\r\n      const axios = (await import(\"../../utils/api/axios\")).default;\r\n      \r\n      console.log(\"Making request to:\", '/v2/programs/jira/validate');\r\n      console.log(\"Backend base URL:\", process.env.REACT_APP_BACKEND_BASE_URL);\r\n      \r\n      const response = await axios.post('/v2/programs/jira/validate', config);\r\n      console.log(\"JIRA test response:\", response.data);\r\n      setJiraTestResult(response.data);\r\n    } catch (error: any) {\r\n      console.error(\"JIRA test error details:\", {\r\n        message: error.message,\r\n        status: error.response?.status,\r\n        data: error.response?.data,\r\n        config: error.config\r\n      });\r\n      \r\n      let errorMessage = \"Failed to connect to server\";\r\n      let suggestions = [\"Please check your internet connection and try again\"];\r\n      \r\n      if (error.response?.status === 401) {\r\n        errorMessage = \"Authentication failed - Please log in again\";\r\n        suggestions = [\"Try refreshing the page and logging in again\"];\r\n      } else if (error.response?.status === 403) {\r\n        errorMessage = \"Access denied - Insufficient permissions\";\r\n        suggestions = [\"Contact your administrator for access to JIRA testing\"];\r\n      } else if (error.response?.status === 404) {\r\n        errorMessage = \"API endpoint not found\";\r\n        suggestions = [\"Please check that the backend server is running\", \"Verify the API endpoint is properly configured\"];\r\n      } else if (error.response?.data?.message) {\r\n        errorMessage = error.response.data.message;\r\n        suggestions = error.response.data.suggestions || suggestions;\r\n      }\r\n      \r\n      setJiraTestResult({ \r\n        success: false, \r\n        error: errorMessage,\r\n        suggestions: suggestions\r\n      });\r\n    } finally {\r\n      setTesting(false);\r\n    }\r\n  };\r\n\r\n  // Only showing the relevant part where we define defaultValues\r\n\r\n  const defaultValues = useMemo(\r\n    () =>\r\n      ({\r\n        title: program?.title || \"\",\r\n        type: program?.type || ProgramType.PTAAS,\r\n        private:\r\n          program?.private === undefined ? undefined : program.private ? 1 : 0,\r\n        description: program?.description || \"\",\r\n        targets: program?.targets || [],\r\n        vpn: program?.vpn,\r\n        credentials: program?.credentials,\r\n        scope: program?.scope || \"\",\r\n        outOfScope: program?.outOfScope || \"\",\r\n        knownVulnerabilities: program?.knownVulnerabilities,\r\n        rewards: program?.payoutRange || {},\r\n        rewardPolicy: program?.rewardPolicy || \"\",\r\n        termsOfService: program?.termsOfService || \"\",\r\n        other: program?.other,\r\n        profilePicture: undefined,\r\n        testingType: program?.testing_type || \"\",\r\n        environmentType: program?.environment_type || \"\",\r\n        complianceType: program?.compliance_type || \"\",\r\n        otherComplianceType: program?.other_compliance_type || \"\",\r\n        expectedStartDate: program?.expected_start_date\r\n          ? new Date(program?.expected_start_date)\r\n          : undefined,\r\n        expectedEndDate: program?.expected_end_date\r\n          ? new Date(program?.expected_end_date)\r\n          : undefined,\r\n        notification_methods: program?.notification_methods,\r\n        attachments: [],\r\n        existingAttachments: program?.attachments || []\r\n      }) as Omit<ProgramUpdateValues, \"private\"> & {\r\n        private?: 0 | 1;\r\n        existingAttachments?: string[];\r\n      },\r\n    [program]\r\n  );\r\n\r\n  useEffect(() => {\r\n    if (program?.type) setSelectedPogramType(program.type);\r\n    if (program?.compliance_type)\r\n      setSelectedComplianceType(program?.compliance_type);\r\n  }, [program]);\r\n\r\n  useEffect(() => {\r\n    if (selectedPogramType === ProgramType.VDP) {\r\n      setPrivateVisibility(0);\r\n    } else if (selectedPogramType === ProgramType.PTAAS) {\r\n      setPrivateVisibility(1);\r\n    } else {\r\n      setPrivateVisibility(undefined);\r\n    }\r\n  }, [selectedPogramType]);\r\n\r\n  return (\r\n    <div className=\"min-h-screen\">\r\n      {/* Inject our CSS animations */}\r\n      <AnimationStyles />\r\n      <EditorLayout\r\n        defaultValues={defaultValues}\r\n        actions={[\r\n          {\r\n            label: id ? \"Save\" : \"Create\",\r\n            type: \"submit\"\r\n          }\r\n        ]}\r\n        onSubmit={(data: any) => {\r\n          // Clone the data to avoid modifying the original\r\n          const formData = { ...data };\r\n          \r\n          // Set private visibility and notification methods\r\n          formData.private = privateVisibility;\r\n          formData.notification_methods = notificationMethods;\r\n          \r\n          // Validate integration configurations\r\n          \r\n          // Validate Jira configuration if selected\r\n          if (jiraSelected) {\r\n            // Check all required Jira fields are filled\r\n            const missingFields = [];\r\n            \r\n            if (!formData.jira_url) missingFields.push(\"Jira URL\");\r\n            if (!formData.jira_email) missingFields.push(\"Jira Email\");\r\n            if (!formData.jira_api_token) missingFields.push(\"Jira API Token\");\r\n            if (!formData.jira_project_key) missingFields.push(\"Jira Project Key\");\r\n            \r\n            if (missingFields.length > 0) {\r\n              alert(`Please fill in all required Jira fields: ${missingFields.join(\", \")}`);\r\n              return;\r\n            }\r\n          }\r\n          \r\n          // Validate Slack configuration if selected\r\n          if (slackSelected && !formData.slack_channel_link) {\r\n            alert(\"Please provide a Slack channel URL\");\r\n            return;\r\n          }\r\n          \r\n          // Save the program and navigate back\r\n          saveProgram(formData);\r\n          navigate(-1);\r\n        }}\r\n      >\r\n        <FormProgressTimeline />\r\n        <EditorSection sectionTitle=\"Program Details\">\r\n          <EditorTextInput\r\n            name=\"title\"\r\n            inputTitle=\"Program Name\"\r\n            details=\"Specify the name of this bug bounty (E.G. [Brand Name] Vulnerability Program).\"\r\n            placeholder=\" \"\r\n            required\r\n          />\r\n\r\n          <EditorInput name=\"type\" inputTitle=\"Type\" required>\r\n            <FormDropdown\r\n              name=\"type\"\r\n              placeholder=\"Select Program Type\"\r\n              options={[\r\n                {\r\n                  value: ProgramType.PTAAS,\r\n                  label: \"PTaaS\"\r\n                },\r\n                {\r\n                  value: ProgramType.VDP,\r\n                  label: \"\"\r\n                },\r\n                {\r\n                  value: ProgramType.BugBounty,\r\n                  label: \"\"\r\n                }\r\n              ]}\r\n              rules={{\r\n                required: true\r\n              }}\r\n              className=\" m-0 flex w-full flex-col p-0  \"\r\n              onChangeCallback={value =>\r\n                setSelectedPogramType(value as ProgramType)\r\n              }\r\n            />\r\n            <p className=\"details mt-1 text-sm text-gray-600\">\r\n              Select the program's approach for researchers to process\r\n              vulnerabilities.\r\n            </p>\r\n          </EditorInput>\r\n\r\n          <EditorSection sectionTitle=\"App Integrations\" className=\"mt-4 mb-4\">\r\n            <div className=\"mb-6 max-w-full overflow-hidden\">\r\n              <div className=\"bg-gradient-to-r from-blue-600 to-indigo-700 rounded-lg shadow-lg p-6 mb-6\">\r\n                <h3 className=\"text-xl font-bold text-white mb-2\">Connect External Services</h3>\r\n                <p className=\"text-blue-100 mb-0\">\r\n                  Enhance your workflow with seamless third-party integrations for real-time notifications and issue tracking.\r\n                </p>\r\n              </div>\r\n              \r\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6 mb-6 max-w-full\">\r\n                {/* Slack Integration Card */}\r\n                <div \r\n                  className={`rounded-xl border-2 p-5 max-w-full ${slackSelected ? \"border-green-500 bg-gradient-to-br from-green-50 to-green-100\" : \"border-gray-200 bg-white\"} hover:shadow-lg transition-all duration-300 cursor-pointer relative overflow-hidden`}\r\n                  style={{\r\n                    animation: slackSelected ? 'pulse 0.6s cubic-bezier(0.4, 0, 0.6, 1)' : 'none'\r\n                  }}\r\n                  onClick={() => {\r\n                    // Toggle the selection when the card is clicked\r\n                    const newValue = !slackSelected;\r\n                    setSlackSelected(newValue);\r\n                    \r\n                    // Update notification methods\r\n                    setNotificationMethods(prevMethods => {\r\n                      const methods = [...prevMethods] as String[];\r\n                      // Always include CTB\r\n                      if (!methods.includes(NotificationMethods.CTB)) {\r\n                        methods.push(NotificationMethods.CTB);\r\n                      }\r\n                      \r\n                      // Add or remove Slack\r\n                      if (newValue && !methods.includes(NotificationMethods.SLACK)) {\r\n                        methods.push(NotificationMethods.SLACK);\r\n                      } else if (!newValue) {\r\n                        return methods.filter(m => m !== NotificationMethods.SLACK) as String[];\r\n                      }\r\n                      \r\n                      return methods;\r\n                    });\r\n                  }}\r\n                >\r\n                  {/* Animated Corner Ribbon */}\r\n                  <div \r\n                    className={`absolute top-0 right-0 transition-transform duration-500 ease-in-out ${slackSelected ? 'translate-x-0' : 'translate-x-16'}`}\r\n                    style={{ transitionDelay: slackSelected ? '0.1s' : '0s' }}\r\n                  >\r\n                    <div className=\"bg-green-500 text-white h-8 w-8 flex items-center justify-center transform rotate-45 translate-y-[-16px] translate-x-[-16px] shadow-md\">\r\n                      <svg \r\n                        xmlns=\"http://www.w3.org/2000/svg\" \r\n                        className={`h-4 w-4 transform -rotate-45 transition-opacity duration-300 ${slackSelected ? 'opacity-100' : 'opacity-0'}`} \r\n                        viewBox=\"0 0 20 20\" \r\n                        fill=\"currentColor\"\r\n                      >\r\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                      </svg>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div className=\"flex items-center\">\r\n                      <div className=\"bg-gradient-to-br from-green-400 to-green-600 text-white rounded-xl h-12 w-12 flex items-center justify-center mr-4 shadow-md\">\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z\" />\r\n                        </svg>\r\n                      </div>\r\n                      <div>\r\n                        <h3 className=\"text-lg font-bold\">Slack</h3>\r\n                        <p className=\"text-sm text-gray-600\">Real-time notifications in your workspace</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Jira Integration Card */}\r\n                <div \r\n                  className={`rounded-xl border-2 p-5 max-w-full ${jiraSelected ? \"border-blue-500 bg-gradient-to-br from-blue-50 to-blue-100\" : \"border-gray-200 bg-white\"} hover:shadow-lg transition-all duration-300 cursor-pointer relative overflow-hidden`}\r\n                  style={{\r\n                    animation: jiraSelected ? 'pulse 0.6s cubic-bezier(0.4, 0, 0.6, 1)' : 'none'\r\n                  }}\r\n                  onClick={() => {\r\n                    // Toggle the selection when the card is clicked\r\n                    const newValue = !jiraSelected;\r\n                    setJiraSelected(newValue);\r\n                    \r\n                    // Update notification methods\r\n                    setNotificationMethods(prevMethods => {\r\n                      const methods = [...prevMethods] as String[];\r\n                      // Always include CTB\r\n                      if (!methods.includes(NotificationMethods.CTB)) {\r\n                        methods.push(NotificationMethods.CTB);\r\n                      }\r\n                      \r\n                      // Add or remove Jira\r\n                      if (newValue && !methods.includes(NotificationMethods.JIRA)) {\r\n                        methods.push(NotificationMethods.JIRA);\r\n                      } else if (!newValue) {\r\n                        return methods.filter(m => m !== NotificationMethods.JIRA) as String[];\r\n                      }\r\n                      \r\n                      return methods;\r\n                    });\r\n                  }}\r\n                >\r\n                  {/* Animated Corner Ribbon */}\r\n                  <div \r\n                    className={`absolute top-0 right-0 transition-transform duration-500 ease-in-out ${jiraSelected ? 'translate-x-0' : 'translate-x-16'}`}\r\n                    style={{ transitionDelay: jiraSelected ? '0.1s' : '0s' }}\r\n                  >\r\n                    <div className=\"bg-blue-500 text-white h-8 w-8 flex items-center justify-center transform rotate-45 translate-y-[-16px] translate-x-[-16px] shadow-md\">\r\n                      <svg \r\n                        xmlns=\"http://www.w3.org/2000/svg\" \r\n                        className={`h-4 w-4 transform -rotate-45 transition-opacity duration-300 ${jiraSelected ? 'opacity-100' : 'opacity-0'}`} \r\n                        viewBox=\"0 0 20 20\" \r\n                        fill=\"currentColor\"\r\n                      >\r\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                      </svg>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"bg-gradient-to-br from-blue-400 to-blue-600 text-white rounded-xl h-12 w-12 flex items-center justify-center mr-4 shadow-md\">\r\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"h-6 w-6\">\r\n                        <path d=\"M12 3l8 4.5v9L12 21l-8-4.5v-9L12 3z\"></path>\r\n                        <path d=\"M12 12l8-4.5\"></path>\r\n                        <path d=\"M12 12v9\"></path>\r\n                        <path d=\"M12 12L4 7.5\"></path>\r\n                      </svg>\r\n                    </div>\r\n                    <div>\r\n                      <h3 className=\"text-lg font-bold\">Jira</h3>\r\n                      <p className=\"text-sm text-gray-600\">Seamless issue tracking integration</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-4\">\r\n                <div className=\"flex\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <svg className=\"h-5 w-5 text-blue-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                      <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                    </svg>\r\n                  </div>\r\n                  <p className=\"ml-3 text-sm text-gray-700\">\r\n                    You can select multiple integrations for your program. Each integration requires specific configuration.\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </EditorSection>\r\n          \r\n          {slackSelected && (\r\n            <EditorSection sectionTitle=\"Slack Configuration\" className=\"mt-6\">\r\n              <div className=\"bg-white p-6 rounded-xl shadow-md border-2 border-green-200 overflow-hidden\">\r\n                <div className=\"flex items-center mb-5 pb-4 border-b border-gray-100\">\r\n                  <div className=\"bg-gradient-to-br from-green-400 to-green-600 text-white rounded-xl h-12 w-12 flex items-center justify-center mr-4 shadow-md\">\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"h-6 w-6\">\r\n                      <path d=\"M14.5 10c-.83 0-1.5-.67-1.5-1.5v-5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5z\"></path>\r\n                      <path d=\"M20.5 10H19V8.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z\"></path>\r\n                      <path d=\"M9.5 14c.83 0 1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5S8 21.33 8 20.5v-5c0-.83.67-1.5 1.5-1.5z\"></path>\r\n                      <path d=\"M3.5 14H5v1.5c0 .83-.67 1.5-1.5 1.5S2 16.33 2 15.5 2.67 14 3.5 14z\"></path>\r\n                      <path d=\"M14 14.5c0-.83.67-1.5 1.5-1.5h5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-5c-.83 0-1.5-.67-1.5-1.5z\"></path>\r\n                      <path d=\"M15.5 19H14v1.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5-.67-1.5-1.5-1.5z\"></path>\r\n                      <path d=\"M10 9.5C10 8.67 9.33 8 8.5 8h-5C2.67 8 2 8.67 2 9.5S2.67 11 3.5 11h5c.83 0 1.5-.67 1.5-1.5z\"></path>\r\n                      <path d=\"M8.5 5H10V3.5C10 2.67 9.33 2 8.5 2S7 2.67 7 3.5 7.67 5 8.5 5z\"></path>\r\n                    </svg>\r\n                  </div>\r\n                  <div>\r\n                    <h3 className=\"font-bold text-xl text-gray-900\">Slack Integration</h3>\r\n                    <p className=\"text-gray-600\">Configure real-time notifications for your Slack workspace</p>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-100 p-4 mb-6\">\r\n                  <div className=\"flex\">\r\n                    <div className=\"flex-shrink-0\">\r\n                      <svg className=\"h-6 w-6 text-green-500\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                        <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                      </svg>\r\n                    </div>\r\n                    <div className=\"ml-3\">\r\n                      <h4 className=\"text-green-800 font-medium\">Integration Guide</h4>\r\n                      <p className=\"text-sm text-gray-700 mt-1\">\r\n                        To integrate with Slack, you'll need to create an incoming webhook for your channel. Visit the <a href=\"https://api.slack.com/messaging/webhooks\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"font-medium text-green-600 hover:text-green-800 underline\">Slack API documentation</a> to learn how.\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div className=\"grid grid-cols-1 gap-6 max-w-full\">\r\n                  {/* Slack Webhook URL Field with custom styling */}\r\n                  <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-5 border border-green-200 shadow-md transition-all duration-300 hover:shadow-lg max-w-full overflow-hidden\">\r\n                    <EditorTextInput\r\n                      name=\"slack_channel_link\"\r\n                      inputTitle=\"Slack Webhook URL\"\r\n                      details=\"Enter your Slack webhook URL to receive notifications\"\r\n                      placeholder=\"https://hooks.slack.com/services/XXXXX/XXXXX/XXXXX\"\r\n                      required={slackSelected}\r\n                    />\r\n                    <p className=\"mt-2 text-sm text-green-700 flex items-center\">\r\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 mr-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                        <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                      </svg>\r\n                      The incoming webhook URL from your Slack workspace settings\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </EditorSection>\r\n          )}\r\n\r\n          {jiraSelected && (\r\n            <EditorSection sectionTitle=\"Jira Configuration\" className=\"mt-6\">\r\n              <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200 max-w-full overflow-hidden\">\r\n                <div className=\"flex items-center mb-4 pb-3 border-b border-gray-100\">\r\n                  <div className=\"bg-blue-500 text-white rounded-lg h-10 w-10 flex items-center justify-center mr-3 flex-shrink-0\">\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"h-5 w-5\">\r\n                      <path d=\"M12 3l8 4.5v9L12 21l-8-4.5v-9L12 3z\"></path>\r\n                      <path d=\"M12 12l8-4.5\"></path>\r\n                      <path d=\"M12 12v9\"></path>\r\n                      <path d=\"M12 12L4 7.5\"></path>\r\n                    </svg>\r\n                  </div>\r\n                  <div className=\"min-w-0 flex-1\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div>\r\n                        <h3 className=\"font-semibold text-lg text-gray-900\">Jira Integration</h3>\r\n                        <p className=\"text-gray-600 text-sm\">Connect your project with Jira for issue tracking</p>\r\n                      </div>\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => setShowJiraGuide(true)}\r\n                        className=\"flex items-center gap-2 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors\"\r\n                      >\r\n                        <FiHelpCircle className=\"w-4 h-4\" />\r\n                        Setup Guide\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                \r\n\r\n                \r\n                <div className=\"space-y-6 max-w-full\">\r\n                  {/* Jira Site URL Field */}\r\n                  <div>\r\n                    <EditorTextInput\r\n                      name=\"jira_url\"\r\n                      inputTitle=\"Jira Site URL\"\r\n                      details=\"The base URL for your Jira site (e.g., https://company.atlassian.net)\"\r\n                      placeholder=\"https://yourdomain.atlassian.net\"\r\n                      required={jiraSelected}\r\n                    />\r\n                  </div>\r\n                  \r\n                  {/* Jira Email Field */}\r\n                  <div>\r\n                    <EditorTextInput\r\n                      name=\"jira_email\"\r\n                      inputTitle=\"Jira Email\"\r\n                      details=\"The email associated with your Jira account\"\r\n                      placeholder=\"<EMAIL>\"\r\n                      required={jiraSelected}\r\n                    />\r\n                  </div>\r\n                  \r\n                  {/* Jira API Token Field */}\r\n                  <div>\r\n                    <EditorTextInput\r\n                      name=\"jira_api_token\"\r\n                      inputTitle=\"Jira API Token\"\r\n                      details=\"Your Jira API token for authentication\"\r\n                      placeholder=\"Enter your Jira API token\"\r\n                      required={jiraSelected}\r\n                    />\r\n                  </div>\r\n                  \r\n                  {/* Jira Project Key Field */}\r\n                  <div>\r\n                    <EditorTextInput\r\n                      name=\"jira_project_key\"\r\n                      inputTitle=\"Jira Project Key\"\r\n                      details=\"The project key for your Jira project (e.g., PROJECT)\"\r\n                      placeholder=\"e.g., PROJECT\"\r\n                      required={jiraSelected}\r\n                    />\r\n                  </div>\r\n                </div>\r\n                \r\n                {/* Test Connection Section */}\r\n                <div className=\"mt-6 pt-6 border-t border-gray-200\">\r\n                  <div className=\"flex items-center justify-between mb-4\">\r\n                    <h4 className=\"text-lg font-semibold text-gray-900\">Connection Test</h4>\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => {\r\n                        const form = document.querySelector('form');\r\n                        if (form) {\r\n                          const formData = new FormData(form);\r\n                          const data = {\r\n                            jira_url: formData.get('jira_url') as string,\r\n                            jira_email: formData.get('jira_email') as string,\r\n                            jira_api_token: formData.get('jira_api_token') as string,\r\n                            jira_project_key: formData.get('jira_project_key') as string,\r\n                          };\r\n                          testJiraConnection(data);\r\n                        }\r\n                      }}\r\n                      disabled={testing}\r\n                      className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 ${\r\n                        testing\r\n                          ? 'bg-gray-400 text-white cursor-not-allowed'\r\n                          : 'bg-blue-500 hover:bg-blue-600 text-white hover:shadow-lg transform hover:scale-105'\r\n                      }`}\r\n                    >\r\n                      {testing ? (\r\n                        <span className=\"flex items-center\">\r\n                          <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                          </svg>\r\n                          Testing...\r\n                        </span>\r\n                      ) : (\r\n                        'Test Connection'\r\n                      )}\r\n                    </button>\r\n                  </div>\r\n                  \r\n                  {/* Test Results */}\r\n                  {jiraTestResult && (\r\n                    <div className={`p-4 rounded-lg border max-w-full overflow-hidden ${\r\n                      jiraTestResult.success \r\n                        ? 'bg-green-50 border-green-200' \r\n                        : 'bg-red-50 border-red-200'\r\n                    }`}>\r\n                      <div className=\"flex items-start\">\r\n                        <div className={`flex-shrink-0 ${jiraTestResult.success ? 'text-green-400' : 'text-red-400'}`}>\r\n                          {jiraTestResult.success ? (\r\n                            <svg className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\r\n                            </svg>\r\n                          ) : (\r\n                            <svg className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n                            </svg>\r\n                          )}\r\n                        </div>\r\n                        <div className=\"ml-3 flex-1\">\r\n                          <h5 className={`font-medium ${jiraTestResult.success ? 'text-green-800' : 'text-red-800'}`}>\r\n                            {jiraTestResult.success ? 'Connection Successful!' : 'Connection Failed'}\r\n                          </h5>\r\n                          {jiraTestResult.success ? (\r\n                            <div className=\"mt-2\">\r\n                              {jiraTestResult.projectInfo && (\r\n                                <div className=\"text-green-700 text-sm\">\r\n                                  <p><strong>Project:</strong> {jiraTestResult.projectInfo.name} ({jiraTestResult.projectInfo.key})</p>\r\n                                  <p><strong>Available Issue Types:</strong> {jiraTestResult.projectInfo.issueTypes.join(', ')}</p>\r\n                                </div>\r\n                              )}\r\n                            </div>\r\n                          ) : (\r\n                            <div className=\"mt-2\">\r\n                              <p className=\"text-red-700 text-sm\">{jiraTestResult.error}</p>\r\n                              {jiraTestResult.suggestions && jiraTestResult.suggestions.length > 0 && (\r\n                                <div className=\"mt-2\">\r\n                                  <p className=\"text-red-700 text-sm font-medium\">Suggestions:</p>\r\n                                  <ul className=\"text-red-600 text-sm mt-1 list-disc list-inside\">\r\n                                    {jiraTestResult.suggestions.map((suggestion: string, index: number) => (\r\n                                      <li key={index}>{suggestion}</li>\r\n                                    ))}\r\n                                  </ul>\r\n                                </div>\r\n                              )}\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </EditorSection>\r\n          )}\r\n\r\n          {selectedPogramType === ProgramType.BugBounty && (\r\n            <EditorInput name=\"private\" inputTitle=\"Program Access\" required>\r\n              <FormDropdown\r\n                name=\"private\"\r\n                options={[\r\n                  { value: 0, label: \"Public\", isDisabled: false },\r\n                  { value: 1, label: \"Private\", isDisabled: false }\r\n                ]}\r\n                value={\r\n                  privateVisibility !== undefined\r\n                    ? privateVisibility.toString()\r\n                    : \"0\"\r\n                }\r\n                onChangeCallback={value =>\r\n                  setPrivateVisibility(value ? (Number(value) as 0 | 1) : 0)\r\n                }\r\n                rules={{ required: true }}\r\n                className=\"py-3\"\r\n              />\r\n              <p className=\"details mt-1 text-sm text-gray-600\">\r\n                Select the visibility and access type of this program.\r\n              </p>\r\n            </EditorInput>\r\n          )}\r\n\r\n          <EditorRichTextInput\r\n            name=\"description\"\r\n            inputTitle=\"Description\"\r\n            details=\"Provide a description about the company and the program.\"\r\n            required\r\n          />\r\n        </EditorSection>\r\n\r\n        <EditorSection sectionTitle=\"Targets & Credentitals\" className=\"mt-6\">\r\n          <TargetsInput\r\n            name=\"targets\"\r\n            inputTitle=\"Targets\"\r\n            details=\"Specify digital assets and their types that are eligible for testing and reporting in this program.\"\r\n            required\r\n          />\r\n\r\n          <ProgramEditorAttachments\r\n            name=\"attachments\"\r\n            inputTitle=\"Target Attachments\"\r\n            details=\"Optionally add attachments to this program.\"\r\n            note=\"Supports any file type\"\r\n            placeholder=\"No Attachments\"\r\n            accept=\"*\" // Allow all files\r\n            multiple={true} // Enable multiple selection\r\n            defaultLinks={program?.attachments || []}\r\n          />\r\n\r\n          <EditorRichTextInput\r\n            name=\"vpn\"\r\n            inputTitle=\"VPN Details\"\r\n            details=\"Provide information about any required VPNs (virtual private networks) to access the target assets\"\r\n          />\r\n\r\n          <EditorRichTextInput\r\n            name=\"credentials\"\r\n            inputTitle=\"Credentials\"\r\n            details=\"Provide necessary access information for testing the target assets (if applicable)\"\r\n          />\r\n        </EditorSection>\r\n\r\n        <EditorSection sectionTitle=\"Scope Description\" className=\"mt-6\">\r\n          <EditorRichTextInput\r\n            name=\"scope\"\r\n            inputTitle=\"Scope\"\r\n            details=\"For a thorough understanding of the testing scope and assets, please provide the necessary documentation and any relevant walkthrough video links.\"\r\n            required\r\n          />\r\n\r\n          {!!(selectedPogramType !== ProgramType.PTAAS) && (\r\n            <EditorRichTextInput\r\n              name=\"outOfScope\"\r\n              inputTitle=\"Out of Scope\"\r\n              details=\"Provide details about the digital assets that are NOT open for examination and reporting\"\r\n              required\r\n            />\r\n          )}\r\n\r\n          {!!(selectedPogramType !== ProgramType.PTAAS) && (\r\n            <EditorRichTextInput\r\n              name=\"knownVulnerabilities\"\r\n              inputTitle=\"Known Vulnerabilities\"\r\n              details=\"Provide details about any addressed vulnerabilites (from previous experience or approved reports)\"\r\n            />\r\n          )}\r\n        </EditorSection>\r\n\r\n        <EditorSection\r\n          sectionTitle={\r\n            selectedPogramType === ProgramType.PTAAS\r\n              ? \"Service Level Agreement\"\r\n              : \"Rewards\"\r\n          }\r\n          className=\"mt-6\"\r\n        >\r\n          {!!(selectedPogramType === ProgramType.BugBounty) && (\r\n            <ProgramRewardsInput\r\n              name=\"rewards\"\r\n              inputTitle=\"Reward Tiers\"\r\n              required\r\n            />\r\n          )}\r\n\r\n          {!!(selectedPogramType === ProgramType.BugBounty) && (\r\n            <EditorRichTextInput\r\n              name=\"rewardPolicy\"\r\n              inputTitle=\"Reward Policy\"\r\n              details=\"Provide rules and guidelines outlining how researchers are compenstated for discovering and responsibly disclosing vulnerabilities\"\r\n              required\r\n            />\r\n          )}\r\n\r\n          <EditorRichTextInput\r\n            name=\"termsOfService\"\r\n            inputTitle={\r\n              selectedPogramType === ProgramType.PTAAS\r\n                ? \"Service Level Agreement\"\r\n                : \"Terms and Conditions\"\r\n            }\r\n            details={\r\n              selectedPogramType === ProgramType.PTAAS\r\n                ? \"SLA will be automatically updated once the program is approved by our admins\"\r\n                : \"Provide an outline of the terms and conditions of how this program will be conducted\"\r\n            }\r\n            required={selectedPogramType !== ProgramType.PTAAS}\r\n            disabled={selectedPogramType === ProgramType.PTAAS}\r\n          />\r\n        </EditorSection>\r\n\r\n        <EditorSection sectionTitle=\"Additional Information\" className=\"mt-6\">\r\n          {!!(selectedPogramType === ProgramType.PTAAS) && (\r\n            <DropDownEditor\r\n              name=\"testingType\"\r\n              inputTitle=\"Testing Type\"\r\n              placeholder=\"Select Testing Type\"\r\n              options={testingTypeOptions}\r\n              value=\"\"\r\n              required\r\n            />\r\n          )}\r\n          {!!(selectedPogramType === ProgramType.PTAAS) && (\r\n            <DropDownEditor\r\n              name=\"environmentType\"\r\n              inputTitle=\"Environment Type\"\r\n              placeholder=\"Select Environment Type\"\r\n              options={environmentTypeOptions}\r\n              value=\"\"\r\n              required\r\n              className=\" mt-5 !bg-ctb-blue-600\"\r\n            />\r\n          )}\r\n          {!!(selectedPogramType === ProgramType.PTAAS) && (\r\n            <div className=\"flex w-full gap-6\">\r\n              <DatePicker\r\n                name=\"expectedStartDate\"\r\n                inputTitle=\"Expected Start Date\"\r\n                required\r\n              />\r\n\r\n              <DatePicker\r\n                name=\"expectedEndDate\"\r\n                inputTitle=\"Expected End Date\"\r\n                required\r\n              />\r\n            </div>\r\n          )}\r\n          {!!(selectedPogramType === ProgramType.PTAAS) && (\r\n            <DropDownEditor\r\n              name=\"complianceType\"\r\n              placeholder=\"Select Compliance Type\"\r\n              inputTitle=\"Compliance Type\"\r\n              options={complianceTypeOptions}\r\n              value=\"\"\r\n              required\r\n              onChangeCallback={value => {\r\n                setSelectedComplianceType(value);\r\n              }}\r\n            />\r\n          )}\r\n          {!!(selectedPogramType === ProgramType.PTAAS) &&\r\n            selectedComplianceType === complianceTypes.Other && (\r\n              <EditorTextInput\r\n                name=\"otherComplianceType\"\r\n                inputTitle=\"Specify Compliance Type\"\r\n                details=\"Mention the type of compliance, mention NA if not applicable\"\r\n                placeholder=\"\"\r\n              />\r\n            )}\r\n\r\n          <EditorRichTextInput\r\n            name=\"other\"\r\n            inputTitle=\"Other\"\r\n            details=\"Provide any essential information that has not been disclosed\"\r\n          />\r\n\r\n          <ProgramEditorLogo\r\n            name=\"profilePicture\"\r\n            inputTitle=\"Logo Image\"\r\n            details=\"Optionally add a logo image to this program.\"\r\n            note=\"Only support .jpg, .png\"\r\n            placeholder=\"No Logo\"\r\n            accept=\"image/*\"\r\n            cropImage={{\r\n              title: \"New Logo\",\r\n              cropSize: {\r\n                width: 300,\r\n                height: 300\r\n              },\r\n              previewSize: {\r\n                width: 330,\r\n                height: 390\r\n              },\r\n              minZoom: 50,\r\n              maxZoom: 300\r\n            }}\r\n            defaultLinks={\r\n              program?.profilePicture ? [program.profilePicture] : []\r\n            }\r\n          />\r\n        </EditorSection>\r\n      </EditorLayout>\r\n\r\n      {/* Jira Setup Guide Modal */}\r\n      <JiraSetupGuideModal\r\n        isOpen={showJiraGuide}\r\n        onClose={() => setShowJiraGuide(false)}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProgramEditor;\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,WAAW,MAAM,4CAA4C;AACpE,OAAOC,YAAY,MAAM,gCAAgC;AAEzD,SACEC,WAAW,EACXC,mBAAmB,QACd,kDAAkD;AACzD,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,YAAY,MAAM,sDAAsD;AAC/E,OAAOC,cAAc,MAAM,wDAAwD;AACnF,OAAOC,UAAU,MAAM,oDAAoD;AAC3E,OAAOC,mBAAmB,MAAM,oDAAoD;AACpF,OAAOC,mBAAmB,MAAM,6DAA6D;AAE7F,OAAOC,UAAU,MAEV,uCAAuC;AAC9C,OAAOC,eAAe,MAAM,gDAAgD;AAC5E,SAASC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,OAAO;AACpD,SACEC,kBAAkB,EAClBC,sBAAsB,EACtBC,qBAAqB,EACrBC,eAAe,QACV,uBAAuB;AAG9B,OAAOC,YAAY,MAAM,4CAA4C;AACrE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,OAAOC,mBAAmB,MAAM,6CAA6C;AAE7E,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,iBAAiB,MAAM,kDAAkD;AAChF,OAAOC,wBAAwB,MAAM,yDAAyD;;AAG9F;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,eAAe,GAAGA,CAAA,kBACtBD,OAAA;EAAAE,QAAA,EACI;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAK;EAAAC,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACI,CACR;AAACC,EAAA,GAlCIN,eAAe;AAoCrB,MAAMO,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC;EAAG,CAAC,GAAGvC,SAAS,CAAC,CAAC;EAC1B,MAAMwC,QAAQ,GAAGvC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACwC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzB,QAAQ,CAC1DZ,WAAW,CAACsC,KACd,CAAC;EACD,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5B,QAAQ,CACxD6B,SACF,CAAC,CAAC,CAAC;EACH;EACA,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/B,QAAQ,CAAW,CACvEX,mBAAmB,CAAC2C,GAAG,CACxB,CAAC;;EAEF;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAU,KAAK,CAAC;EAClE,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAU,KAAK,CAAC;;EAEhE;EACA,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAM,IAAI,CAAC;;EAE/D;EACA,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAU,KAAK,CAAC;EAClE,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAU,KAAK,CAAC;;EAEtD;EACA,MAAM,CAAC2C,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACxE,MAAM;IAAE6C,OAAO;IAAEC;EAAY,CAAC,GAAGlD,UAAU,CACzC0B,EAAE,KAAKO,SAAS,GAAGkB,QAAQ,CAACzB,EAAE,CAAC,GAAGO,SACpC,CAAC;EAED1C,YAAY,CAACmC,EAAE,GAAG,iBAAiB,GAAG,gBAAgB,CAAC;;EAEvD;EACA,MAAM0B,kBAAkB,GAAG,MAAOC,QAAa,IAAK;IAClDP,UAAU,CAAC,IAAI,CAAC;IAChBJ,iBAAiB,CAAC,IAAI,CAAC;IAEvB,IAAI;MACF,MAAMY,MAAM,GAAG;QACbC,GAAG,EAAEF,QAAQ,CAACG,QAAQ;QACtBC,KAAK,EAAEJ,QAAQ,CAACK,UAAU;QAC1BC,SAAS,EAAEN,QAAQ,CAACO,cAAc;QAClCC,WAAW,EAAER,QAAQ,CAACS;MACxB,CAAC;;MAED;MACA,IAAI,CAACR,MAAM,CAACC,GAAG,IAAI,CAACD,MAAM,CAACG,KAAK,IAAI,CAACH,MAAM,CAACK,SAAS,IAAI,CAACL,MAAM,CAACO,WAAW,EAAE;QAC5EnB,iBAAiB,CAAC;UAChBqB,OAAO,EAAE,KAAK;UACdC,KAAK,EAAE;QACT,CAAC,CAAC;QACF;MACF;MAEAC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;QAClD,GAAGZ,MAAM;QACTK,SAAS,EAAEL,MAAM,CAACK,SAAS,GAAG,UAAU,GAAG;MAC7C,CAAC,CAAC;;MAEF;MACA,MAAMQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC,uBAAuB,CAAC,EAAEC,OAAO;MAE7DH,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,4BAA4B,CAAC;MAC/DD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEG,OAAO,CAACC,GAAG,CAACC,0BAA0B,CAAC;MAExE,MAAMC,QAAQ,GAAG,MAAML,KAAK,CAACM,IAAI,CAAC,4BAA4B,EAAEnB,MAAM,CAAC;MACvEW,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEM,QAAQ,CAACE,IAAI,CAAC;MACjDhC,iBAAiB,CAAC8B,QAAQ,CAACE,IAAI,CAAC;IAClC,CAAC,CAAC,OAAOV,KAAU,EAAE;MAAA,IAAAW,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACnBhB,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAE;QACxCkB,OAAO,EAAElB,KAAK,CAACkB,OAAO;QACtBC,MAAM,GAAAR,eAAA,GAAEX,KAAK,CAACQ,QAAQ,cAAAG,eAAA,uBAAdA,eAAA,CAAgBQ,MAAM;QAC9BT,IAAI,GAAAE,gBAAA,GAAEZ,KAAK,CAACQ,QAAQ,cAAAI,gBAAA,uBAAdA,gBAAA,CAAgBF,IAAI;QAC1BpB,MAAM,EAAEU,KAAK,CAACV;MAChB,CAAC,CAAC;MAEF,IAAI8B,YAAY,GAAG,6BAA6B;MAChD,IAAIC,WAAW,GAAG,CAAC,qDAAqD,CAAC;MAEzE,IAAI,EAAAR,gBAAA,GAAAb,KAAK,CAACQ,QAAQ,cAAAK,gBAAA,uBAAdA,gBAAA,CAAgBM,MAAM,MAAK,GAAG,EAAE;QAClCC,YAAY,GAAG,6CAA6C;QAC5DC,WAAW,GAAG,CAAC,8CAA8C,CAAC;MAChE,CAAC,MAAM,IAAI,EAAAP,gBAAA,GAAAd,KAAK,CAACQ,QAAQ,cAAAM,gBAAA,uBAAdA,gBAAA,CAAgBK,MAAM,MAAK,GAAG,EAAE;QACzCC,YAAY,GAAG,0CAA0C;QACzDC,WAAW,GAAG,CAAC,uDAAuD,CAAC;MACzE,CAAC,MAAM,IAAI,EAAAN,gBAAA,GAAAf,KAAK,CAACQ,QAAQ,cAAAO,gBAAA,uBAAdA,gBAAA,CAAgBI,MAAM,MAAK,GAAG,EAAE;QACzCC,YAAY,GAAG,wBAAwB;QACvCC,WAAW,GAAG,CAAC,iDAAiD,EAAE,gDAAgD,CAAC;MACrH,CAAC,MAAM,KAAAL,gBAAA,GAAIhB,KAAK,CAACQ,QAAQ,cAAAQ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBN,IAAI,cAAAO,qBAAA,eAApBA,qBAAA,CAAsBC,OAAO,EAAE;QACxCE,YAAY,GAAGpB,KAAK,CAACQ,QAAQ,CAACE,IAAI,CAACQ,OAAO;QAC1CG,WAAW,GAAGrB,KAAK,CAACQ,QAAQ,CAACE,IAAI,CAACW,WAAW,IAAIA,WAAW;MAC9D;MAEA3C,iBAAiB,CAAC;QAChBqB,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEoB,YAAY;QACnBC,WAAW,EAAEA;MACf,CAAC,CAAC;IACJ,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;;EAEA,MAAMwC,aAAa,GAAGnF,OAAO,CAC3B,OACG;IACCoF,KAAK,EAAE,CAAAtC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsC,KAAK,KAAI,EAAE;IAC3BC,IAAI,EAAE,CAAAvC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuC,IAAI,KAAIhG,WAAW,CAACsC,KAAK;IACxC2D,OAAO,EACL,CAAAxC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwC,OAAO,MAAKxD,SAAS,GAAGA,SAAS,GAAGgB,OAAO,CAACwC,OAAO,GAAG,CAAC,GAAG,CAAC;IACtEC,WAAW,EAAE,CAAAzC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEyC,WAAW,KAAI,EAAE;IACvCC,OAAO,EAAE,CAAA1C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE0C,OAAO,KAAI,EAAE;IAC/BC,GAAG,EAAE3C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2C,GAAG;IACjBC,WAAW,EAAE5C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE4C,WAAW;IACjCC,KAAK,EAAE,CAAA7C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6C,KAAK,KAAI,EAAE;IAC3BC,UAAU,EAAE,CAAA9C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE8C,UAAU,KAAI,EAAE;IACrCC,oBAAoB,EAAE/C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE+C,oBAAoB;IACnDC,OAAO,EAAE,CAAAhD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiD,WAAW,KAAI,CAAC,CAAC;IACnCC,YAAY,EAAE,CAAAlD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEkD,YAAY,KAAI,EAAE;IACzCC,cAAc,EAAE,CAAAnD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEmD,cAAc,KAAI,EAAE;IAC7CC,KAAK,EAAEpD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoD,KAAK;IACrBC,cAAc,EAAErE,SAAS;IACzBsE,WAAW,EAAE,CAAAtD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuD,YAAY,KAAI,EAAE;IACxCC,eAAe,EAAE,CAAAxD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEyD,gBAAgB,KAAI,EAAE;IAChDC,cAAc,EAAE,CAAA1D,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2D,eAAe,KAAI,EAAE;IAC9CC,mBAAmB,EAAE,CAAA5D,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6D,qBAAqB,KAAI,EAAE;IACzDC,iBAAiB,EAAE9D,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE+D,mBAAmB,GAC3C,IAAIC,IAAI,CAAChE,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE+D,mBAAmB,CAAC,GACtC/E,SAAS;IACbiF,eAAe,EAAEjE,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEkE,iBAAiB,GACvC,IAAIF,IAAI,CAAChE,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEkE,iBAAiB,CAAC,GACpClF,SAAS;IACbmF,oBAAoB,EAAEnE,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEmE,oBAAoB;IACnDC,WAAW,EAAE,EAAE;IACfC,mBAAmB,EAAE,CAAArE,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoE,WAAW,KAAI;EAC/C,CAAC,CAGA,EACH,CAACpE,OAAO,CACV,CAAC;EAED/C,SAAS,CAAC,MAAM;IACd,IAAI+C,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEuC,IAAI,EAAE3D,qBAAqB,CAACoB,OAAO,CAACuC,IAAI,CAAC;IACtD,IAAIvC,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE2D,eAAe,EAC1B5D,yBAAyB,CAACC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2D,eAAe,CAAC;EACvD,CAAC,EAAE,CAAC3D,OAAO,CAAC,CAAC;EAEb/C,SAAS,CAAC,MAAM;IACd,IAAI0B,kBAAkB,KAAKpC,WAAW,CAAC+H,GAAG,EAAE;MAC1CvF,oBAAoB,CAAC,CAAC,CAAC;IACzB,CAAC,MAAM,IAAIJ,kBAAkB,KAAKpC,WAAW,CAACsC,KAAK,EAAE;MACnDE,oBAAoB,CAAC,CAAC,CAAC;IACzB,CAAC,MAAM;MACLA,oBAAoB,CAACC,SAAS,CAAC;IACjC;EACF,CAAC,EAAE,CAACL,kBAAkB,CAAC,CAAC;EAExB,oBACEZ,OAAA;IAAKwG,SAAS,EAAC,cAAc;IAAAtG,QAAA,gBAE3BF,OAAA,CAACC,eAAe;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnBN,OAAA,CAAC3B,YAAY;MACXiG,aAAa,EAAEA,aAAc;MAC7BmC,OAAO,EAAE,CACP;QACEC,KAAK,EAAEhG,EAAE,GAAG,MAAM,GAAG,QAAQ;QAC7B8D,IAAI,EAAE;MACR,CAAC,CACD;MACFmC,QAAQ,EAAGjD,IAAS,IAAK;QACvB;QACA,MAAMrB,QAAQ,GAAG;UAAE,GAAGqB;QAAK,CAAC;;QAE5B;QACArB,QAAQ,CAACoC,OAAO,GAAG1D,iBAAiB;QACpCsB,QAAQ,CAAC+D,oBAAoB,GAAGlF,mBAAmB;;QAEnD;;QAEA;QACA,IAAIK,YAAY,EAAE;UAChB;UACA,MAAMqF,aAAa,GAAG,EAAE;UAExB,IAAI,CAACvE,QAAQ,CAACG,QAAQ,EAAEoE,aAAa,CAACC,IAAI,CAAC,UAAU,CAAC;UACtD,IAAI,CAACxE,QAAQ,CAACK,UAAU,EAAEkE,aAAa,CAACC,IAAI,CAAC,YAAY,CAAC;UAC1D,IAAI,CAACxE,QAAQ,CAACO,cAAc,EAAEgE,aAAa,CAACC,IAAI,CAAC,gBAAgB,CAAC;UAClE,IAAI,CAACxE,QAAQ,CAACS,gBAAgB,EAAE8D,aAAa,CAACC,IAAI,CAAC,kBAAkB,CAAC;UAEtE,IAAID,aAAa,CAACE,MAAM,GAAG,CAAC,EAAE;YAC5BC,KAAK,CAAE,4CAA2CH,aAAa,CAACI,IAAI,CAAC,IAAI,CAAE,EAAC,CAAC;YAC7E;UACF;QACF;;QAEA;QACA,IAAI3F,aAAa,IAAI,CAACgB,QAAQ,CAAC4E,kBAAkB,EAAE;UACjDF,KAAK,CAAC,oCAAoC,CAAC;UAC3C;QACF;;QAEA;QACA7E,WAAW,CAACG,QAAQ,CAAC;QACrB1B,QAAQ,CAAC,CAAC,CAAC,CAAC;MACd,CAAE;MAAAT,QAAA,gBAEFF,OAAA,CAACJ,oBAAoB;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxBN,OAAA,CAACtB,aAAa;QAACwI,YAAY,EAAC,iBAAiB;QAAAhH,QAAA,gBAC3CF,OAAA,CAACf,eAAe;UACdkI,IAAI,EAAC,OAAO;UACZC,UAAU,EAAC,cAAc;UACzBC,OAAO,EAAC,gFAAgF;UACxFC,WAAW,EAAC,GAAG;UACfC,QAAQ;QAAA;UAAApH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEFN,OAAA,CAAC1B,WAAW;UAAC6I,IAAI,EAAC,MAAM;UAACC,UAAU,EAAC,MAAM;UAACG,QAAQ;UAAArH,QAAA,gBACjDF,OAAA,CAACP,YAAY;YACX0H,IAAI,EAAC,MAAM;YACXG,WAAW,EAAC,qBAAqB;YACjCE,OAAO,EAAE,CACP;cACEC,KAAK,EAAEjJ,WAAW,CAACsC,KAAK;cACxB4F,KAAK,EAAE;YACT,CAAC,EACD;cACEe,KAAK,EAAEjJ,WAAW,CAAC+H,GAAG;cACtBG,KAAK,EAAE;YACT,CAAC,EACD;cACEe,KAAK,EAAEjJ,WAAW,CAACkJ,SAAS;cAC5BhB,KAAK,EAAE;YACT,CAAC,CACD;YACFiB,KAAK,EAAE;cACLJ,QAAQ,EAAE;YACZ,CAAE;YACFf,SAAS,EAAC,iCAAiC;YAC3CoB,gBAAgB,EAAEH,KAAK,IACrB5G,qBAAqB,CAAC4G,KAAoB;UAC3C;YAAAtH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFN,OAAA;YAAGwG,SAAS,EAAC,oCAAoC;YAAAtG,QAAA,EAAC;UAGlD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEdN,OAAA,CAACtB,aAAa;UAACwI,YAAY,EAAC,kBAAkB;UAACV,SAAS,EAAC,WAAW;UAAAtG,QAAA,eAClEF,OAAA;YAAKwG,SAAS,EAAC,iCAAiC;YAAAtG,QAAA,gBAC9CF,OAAA;cAAKwG,SAAS,EAAC,4EAA4E;cAAAtG,QAAA,gBACzFF,OAAA;gBAAIwG,SAAS,EAAC,mCAAmC;gBAAAtG,QAAA,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChFN,OAAA;gBAAGwG,SAAS,EAAC,oBAAoB;gBAAAtG,QAAA,EAAC;cAElC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENN,OAAA;cAAKwG,SAAS,EAAC,uDAAuD;cAAAtG,QAAA,gBAEpEF,OAAA;gBACEwG,SAAS,EAAG,sCAAqCnF,aAAa,GAAG,+DAA+D,GAAG,0BAA2B,sFAAsF;gBACpPwG,KAAK,EAAE;kBACLC,SAAS,EAAEzG,aAAa,GAAG,yCAAyC,GAAG;gBACzE,CAAE;gBACF0G,OAAO,EAAEA,CAAA,KAAM;kBACb;kBACA,MAAMC,QAAQ,GAAG,CAAC3G,aAAa;kBAC/BC,gBAAgB,CAAC0G,QAAQ,CAAC;;kBAE1B;kBACA7G,sBAAsB,CAAC8G,WAAW,IAAI;oBACpC,MAAMC,OAAO,GAAG,CAAC,GAAGD,WAAW,CAAa;oBAC5C;oBACA,IAAI,CAACC,OAAO,CAACC,QAAQ,CAAC1J,mBAAmB,CAAC2C,GAAG,CAAC,EAAE;sBAC9C8G,OAAO,CAACrB,IAAI,CAACpI,mBAAmB,CAAC2C,GAAG,CAAC;oBACvC;;oBAEA;oBACA,IAAI4G,QAAQ,IAAI,CAACE,OAAO,CAACC,QAAQ,CAAC1J,mBAAmB,CAAC2J,KAAK,CAAC,EAAE;sBAC5DF,OAAO,CAACrB,IAAI,CAACpI,mBAAmB,CAAC2J,KAAK,CAAC;oBACzC,CAAC,MAAM,IAAI,CAACJ,QAAQ,EAAE;sBACpB,OAAOE,OAAO,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK7J,mBAAmB,CAAC2J,KAAK,CAAC;oBAC7D;oBAEA,OAAOF,OAAO;kBAChB,CAAC,CAAC;gBACJ,CAAE;gBAAAhI,QAAA,gBAGFF,OAAA;kBACEwG,SAAS,EAAG,wEAAuEnF,aAAa,GAAG,eAAe,GAAG,gBAAiB,EAAE;kBACxIwG,KAAK,EAAE;oBAAEU,eAAe,EAAElH,aAAa,GAAG,MAAM,GAAG;kBAAK,CAAE;kBAAAnB,QAAA,eAE1DF,OAAA;oBAAKwG,SAAS,EAAC,wIAAwI;oBAAAtG,QAAA,eACrJF,OAAA;sBACEwI,KAAK,EAAC,4BAA4B;sBAClChC,SAAS,EAAG,gEAA+DnF,aAAa,GAAG,aAAa,GAAG,WAAY,EAAE;sBACzHoH,OAAO,EAAC,WAAW;sBACnBC,IAAI,EAAC,cAAc;sBAAAxI,QAAA,eAEnBF,OAAA;wBAAM2I,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,oHAAoH;wBAACC,QAAQ,EAAC;sBAAS;wBAAA1I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClK;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNN,OAAA;kBAAKwG,SAAS,EAAC,mCAAmC;kBAAAtG,QAAA,eAChDF,OAAA;oBAAKwG,SAAS,EAAC,mBAAmB;oBAAAtG,QAAA,gBAChCF,OAAA;sBAAKwG,SAAS,EAAC,+HAA+H;sBAAAtG,QAAA,eAC5IF,OAAA;wBAAKwI,KAAK,EAAC,4BAA4B;wBAAChC,SAAS,EAAC,SAAS;wBAACkC,IAAI,EAAC,MAAM;wBAACD,OAAO,EAAC,WAAW;wBAACK,MAAM,EAAC,cAAc;wBAAA5I,QAAA,eAC/GF,OAAA;0BAAM+I,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,WAAW,EAAE,CAAE;0BAACL,CAAC,EAAC;wBAA+J;0BAAAzI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpO;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNN,OAAA;sBAAAE,QAAA,gBACEF,OAAA;wBAAIwG,SAAS,EAAC,mBAAmB;wBAAAtG,QAAA,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC5CN,OAAA;wBAAGwG,SAAS,EAAC,uBAAuB;wBAAAtG,QAAA,EAAC;sBAAyC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/E,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNN,OAAA;gBACEwG,SAAS,EAAG,sCAAqCjF,YAAY,GAAG,4DAA4D,GAAG,0BAA2B,sFAAsF;gBAChPsG,KAAK,EAAE;kBACLC,SAAS,EAAEvG,YAAY,GAAG,yCAAyC,GAAG;gBACxE,CAAE;gBACFwG,OAAO,EAAEA,CAAA,KAAM;kBACb;kBACA,MAAMC,QAAQ,GAAG,CAACzG,YAAY;kBAC9BC,eAAe,CAACwG,QAAQ,CAAC;;kBAEzB;kBACA7G,sBAAsB,CAAC8G,WAAW,IAAI;oBACpC,MAAMC,OAAO,GAAG,CAAC,GAAGD,WAAW,CAAa;oBAC5C;oBACA,IAAI,CAACC,OAAO,CAACC,QAAQ,CAAC1J,mBAAmB,CAAC2C,GAAG,CAAC,EAAE;sBAC9C8G,OAAO,CAACrB,IAAI,CAACpI,mBAAmB,CAAC2C,GAAG,CAAC;oBACvC;;oBAEA;oBACA,IAAI4G,QAAQ,IAAI,CAACE,OAAO,CAACC,QAAQ,CAAC1J,mBAAmB,CAACyK,IAAI,CAAC,EAAE;sBAC3DhB,OAAO,CAACrB,IAAI,CAACpI,mBAAmB,CAACyK,IAAI,CAAC;oBACxC,CAAC,MAAM,IAAI,CAAClB,QAAQ,EAAE;sBACpB,OAAOE,OAAO,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK7J,mBAAmB,CAACyK,IAAI,CAAC;oBAC5D;oBAEA,OAAOhB,OAAO;kBAChB,CAAC,CAAC;gBACJ,CAAE;gBAAAhI,QAAA,gBAGFF,OAAA;kBACEwG,SAAS,EAAG,wEAAuEjF,YAAY,GAAG,eAAe,GAAG,gBAAiB,EAAE;kBACvIsG,KAAK,EAAE;oBAAEU,eAAe,EAAEhH,YAAY,GAAG,MAAM,GAAG;kBAAK,CAAE;kBAAArB,QAAA,eAEzDF,OAAA;oBAAKwG,SAAS,EAAC,uIAAuI;oBAAAtG,QAAA,eACpJF,OAAA;sBACEwI,KAAK,EAAC,4BAA4B;sBAClChC,SAAS,EAAG,gEAA+DjF,YAAY,GAAG,aAAa,GAAG,WAAY,EAAE;sBACxHkH,OAAO,EAAC,WAAW;sBACnBC,IAAI,EAAC,cAAc;sBAAAxI,QAAA,eAEnBF,OAAA;wBAAM2I,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,oHAAoH;wBAACC,QAAQ,EAAC;sBAAS;wBAAA1I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClK;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNN,OAAA;kBAAKwG,SAAS,EAAC,mBAAmB;kBAAAtG,QAAA,gBAChCF,OAAA;oBAAKwG,SAAS,EAAC,6HAA6H;oBAAAtG,QAAA,eAC1IF,OAAA;sBAAKwI,KAAK,EAAC,4BAA4B;sBAACC,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,MAAM;sBAACI,MAAM,EAAC,cAAc;sBAACG,WAAW,EAAC,GAAG;sBAACF,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACxC,SAAS,EAAC,SAAS;sBAAAtG,QAAA,gBAC5KF,OAAA;wBAAM4I,CAAC,EAAC;sBAAqC;wBAAAzI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACrDN,OAAA;wBAAM4I,CAAC,EAAC;sBAAc;wBAAAzI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC9BN,OAAA;wBAAM4I,CAAC,EAAC;sBAAU;wBAAAzI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC1BN,OAAA;wBAAM4I,CAAC,EAAC;sBAAc;wBAAAzI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNN,OAAA;oBAAAE,QAAA,gBACEF,OAAA;sBAAIwG,SAAS,EAAC,mBAAmB;sBAAAtG,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3CN,OAAA;sBAAGwG,SAAS,EAAC,uBAAuB;sBAAAtG,QAAA,EAAC;oBAAmC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENN,OAAA;cAAKwG,SAAS,EAAC,kDAAkD;cAAAtG,QAAA,eAC/DF,OAAA;gBAAKwG,SAAS,EAAC,MAAM;gBAAAtG,QAAA,gBACnBF,OAAA;kBAAKwG,SAAS,EAAC,eAAe;kBAAAtG,QAAA,eAC5BF,OAAA;oBAAKwG,SAAS,EAAC,uBAAuB;oBAACiC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,cAAc;oBAAAxI,QAAA,eAC5EF,OAAA;sBAAM2I,QAAQ,EAAC,SAAS;sBAACC,CAAC,EAAC,kIAAkI;sBAACC,QAAQ,EAAC;oBAAS;sBAAA1I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNN,OAAA;kBAAGwG,SAAS,EAAC,4BAA4B;kBAAAtG,QAAA,EAAC;gBAE1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,EAEfe,aAAa,iBACZrB,OAAA,CAACtB,aAAa;UAACwI,YAAY,EAAC,qBAAqB;UAACV,SAAS,EAAC,MAAM;UAAAtG,QAAA,eAChEF,OAAA;YAAKwG,SAAS,EAAC,6EAA6E;YAAAtG,QAAA,gBAC1FF,OAAA;cAAKwG,SAAS,EAAC,sDAAsD;cAAAtG,QAAA,gBACnEF,OAAA;gBAAKwG,SAAS,EAAC,+HAA+H;gBAAAtG,QAAA,eAC5IF,OAAA;kBAAKwI,KAAK,EAAC,4BAA4B;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACI,MAAM,EAAC,cAAc;kBAACG,WAAW,EAAC,GAAG;kBAACF,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACxC,SAAS,EAAC,SAAS;kBAAAtG,QAAA,gBAC5KF,OAAA;oBAAM4I,CAAC,EAAC;kBAAiG;oBAAAzI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACjHN,OAAA;oBAAM4I,CAAC,EAAC;kBAAsE;oBAAAzI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtFN,OAAA;oBAAM4I,CAAC,EAAC;kBAA8F;oBAAAzI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9GN,OAAA;oBAAM4I,CAAC,EAAC;kBAAoE;oBAAAzI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACpFN,OAAA;oBAAM4I,CAAC,EAAC;kBAAkG;oBAAAzI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAClHN,OAAA;oBAAM4I,CAAC,EAAC;kBAAuE;oBAAAzI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACvFN,OAAA;oBAAM4I,CAAC,EAAC;kBAA6F;oBAAAzI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7GN,OAAA;oBAAM4I,CAAC,EAAC;kBAA+D;oBAAAzI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNN,OAAA;gBAAAE,QAAA,gBACEF,OAAA;kBAAIwG,SAAS,EAAC,iCAAiC;kBAAAtG,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtEN,OAAA;kBAAGwG,SAAS,EAAC,eAAe;kBAAAtG,QAAA,EAAC;gBAA0D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENN,OAAA;cAAKwG,SAAS,EAAC,0FAA0F;cAAAtG,QAAA,eACvGF,OAAA;gBAAKwG,SAAS,EAAC,MAAM;gBAAAtG,QAAA,gBACnBF,OAAA;kBAAKwG,SAAS,EAAC,eAAe;kBAAAtG,QAAA,eAC5BF,OAAA;oBAAKwG,SAAS,EAAC,wBAAwB;oBAACkC,IAAI,EAAC,cAAc;oBAACD,OAAO,EAAC,WAAW;oBAAAvI,QAAA,eAC7EF,OAAA;sBAAM2I,QAAQ,EAAC,SAAS;sBAACC,CAAC,EAAC,kIAAkI;sBAACC,QAAQ,EAAC;oBAAS;sBAAA1I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNN,OAAA;kBAAKwG,SAAS,EAAC,MAAM;kBAAAtG,QAAA,gBACnBF,OAAA;oBAAIwG,SAAS,EAAC,4BAA4B;oBAAAtG,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjEN,OAAA;oBAAGwG,SAAS,EAAC,4BAA4B;oBAAAtG,QAAA,GAAC,iGACuD,eAAAF,OAAA;sBAAGmJ,IAAI,EAAC,0CAA0C;sBAACC,MAAM,EAAC,QAAQ;sBAACC,GAAG,EAAC,qBAAqB;sBAAC7C,SAAS,EAAC,2DAA2D;sBAAAtG,QAAA,EAAC;oBAAuB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,kBAC/R;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENN,OAAA;cAAKwG,SAAS,EAAC,mCAAmC;cAAAtG,QAAA,eAEhDF,OAAA;gBAAKwG,SAAS,EAAC,sKAAsK;gBAAAtG,QAAA,gBACnLF,OAAA,CAACf,eAAe;kBACdkI,IAAI,EAAC,oBAAoB;kBACzBC,UAAU,EAAC,mBAAmB;kBAC9BC,OAAO,EAAC,uDAAuD;kBAC/DC,WAAW,EAAC,oDAAoD;kBAChEC,QAAQ,EAAElG;gBAAc;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACFN,OAAA;kBAAGwG,SAAS,EAAC,+CAA+C;kBAAAtG,QAAA,gBAC1DF,OAAA;oBAAKwI,KAAK,EAAC,4BAA4B;oBAAChC,SAAS,EAAC,cAAc;oBAACiC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,cAAc;oBAAAxI,QAAA,eACtGF,OAAA;sBAAM2I,QAAQ,EAAC,SAAS;sBAACC,CAAC,EAAC,kIAAkI;sBAACC,QAAQ,EAAC;oBAAS;sBAAA1I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChL,CAAC,+DAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAChB,EAEAiB,YAAY,iBACXvB,OAAA,CAACtB,aAAa;UAACwI,YAAY,EAAC,oBAAoB;UAACV,SAAS,EAAC,MAAM;UAAAtG,QAAA,eAC/DF,OAAA;YAAKwG,SAAS,EAAC,qFAAqF;YAAAtG,QAAA,gBAClGF,OAAA;cAAKwG,SAAS,EAAC,sDAAsD;cAAAtG,QAAA,gBACnEF,OAAA;gBAAKwG,SAAS,EAAC,iGAAiG;gBAAAtG,QAAA,eAC9GF,OAAA;kBAAKwI,KAAK,EAAC,4BAA4B;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACI,MAAM,EAAC,cAAc;kBAACG,WAAW,EAAC,GAAG;kBAACF,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACxC,SAAS,EAAC,SAAS;kBAAAtG,QAAA,gBAC5KF,OAAA;oBAAM4I,CAAC,EAAC;kBAAqC;oBAAAzI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACrDN,OAAA;oBAAM4I,CAAC,EAAC;kBAAc;oBAAAzI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9BN,OAAA;oBAAM4I,CAAC,EAAC;kBAAU;oBAAAzI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC1BN,OAAA;oBAAM4I,CAAC,EAAC;kBAAc;oBAAAzI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNN,OAAA;gBAAKwG,SAAS,EAAC,gBAAgB;gBAAAtG,QAAA,eAC7BF,OAAA;kBAAKwG,SAAS,EAAC,mCAAmC;kBAAAtG,QAAA,gBAChDF,OAAA;oBAAAE,QAAA,gBACEF,OAAA;sBAAIwG,SAAS,EAAC,qCAAqC;sBAAAtG,QAAA,EAAC;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzEN,OAAA;sBAAGwG,SAAS,EAAC,uBAAuB;sBAAAtG,QAAA,EAAC;oBAAiD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvF,CAAC,eACNN,OAAA;oBACEwE,IAAI,EAAC,QAAQ;oBACbuD,OAAO,EAAEA,CAAA,KAAMnG,gBAAgB,CAAC,IAAI,CAAE;oBACtC4E,SAAS,EAAC,+HAA+H;oBAAAtG,QAAA,gBAEzIF,OAAA,CAACN,YAAY;sBAAC8G,SAAS,EAAC;oBAAS;sBAAArG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAEtC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAINN,OAAA;cAAKwG,SAAS,EAAC,sBAAsB;cAAAtG,QAAA,gBAEnCF,OAAA;gBAAAE,QAAA,eACEF,OAAA,CAACf,eAAe;kBACdkI,IAAI,EAAC,UAAU;kBACfC,UAAU,EAAC,eAAe;kBAC1BC,OAAO,EAAC,uEAAuE;kBAC/EC,WAAW,EAAC,kCAAkC;kBAC9CC,QAAQ,EAAEhG;gBAAa;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNN,OAAA;gBAAAE,QAAA,eACEF,OAAA,CAACf,eAAe;kBACdkI,IAAI,EAAC,YAAY;kBACjBC,UAAU,EAAC,YAAY;kBACvBC,OAAO,EAAC,6CAA6C;kBACrDC,WAAW,EAAC,mBAAmB;kBAC/BC,QAAQ,EAAEhG;gBAAa;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNN,OAAA;gBAAAE,QAAA,eACEF,OAAA,CAACf,eAAe;kBACdkI,IAAI,EAAC,gBAAgB;kBACrBC,UAAU,EAAC,gBAAgB;kBAC3BC,OAAO,EAAC,wCAAwC;kBAChDC,WAAW,EAAC,2BAA2B;kBACvCC,QAAQ,EAAEhG;gBAAa;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNN,OAAA;gBAAAE,QAAA,eACEF,OAAA,CAACf,eAAe;kBACdkI,IAAI,EAAC,kBAAkB;kBACvBC,UAAU,EAAC,kBAAkB;kBAC7BC,OAAO,EAAC,uDAAuD;kBAC/DC,WAAW,EAAC,eAAe;kBAC3BC,QAAQ,EAAEhG;gBAAa;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNN,OAAA;cAAKwG,SAAS,EAAC,oCAAoC;cAAAtG,QAAA,gBACjDF,OAAA;gBAAKwG,SAAS,EAAC,wCAAwC;gBAAAtG,QAAA,gBACrDF,OAAA;kBAAIwG,SAAS,EAAC,qCAAqC;kBAAAtG,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxEN,OAAA;kBACEwE,IAAI,EAAC,QAAQ;kBACbuD,OAAO,EAAEA,CAAA,KAAM;oBACb,MAAMuB,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;oBAC3C,IAAIF,IAAI,EAAE;sBACR,MAAMjH,QAAQ,GAAG,IAAIoH,QAAQ,CAACH,IAAI,CAAC;sBACnC,MAAM5F,IAAI,GAAG;wBACXlB,QAAQ,EAAEH,QAAQ,CAACqH,GAAG,CAAC,UAAU,CAAW;wBAC5ChH,UAAU,EAAEL,QAAQ,CAACqH,GAAG,CAAC,YAAY,CAAW;wBAChD9G,cAAc,EAAEP,QAAQ,CAACqH,GAAG,CAAC,gBAAgB,CAAW;wBACxD5G,gBAAgB,EAAET,QAAQ,CAACqH,GAAG,CAAC,kBAAkB;sBACnD,CAAC;sBACDtH,kBAAkB,CAACsB,IAAI,CAAC;oBAC1B;kBACF,CAAE;kBACFiG,QAAQ,EAAE9H,OAAQ;kBAClB2E,SAAS,EAAG,gEACV3E,OAAO,GACH,2CAA2C,GAC3C,oFACL,EAAE;kBAAA3B,QAAA,EAEF2B,OAAO,gBACN7B,OAAA;oBAAMwG,SAAS,EAAC,mBAAmB;oBAAAtG,QAAA,gBACjCF,OAAA;sBAAKwG,SAAS,EAAC,4CAA4C;sBAACgC,KAAK,EAAC,4BAA4B;sBAACE,IAAI,EAAC,MAAM;sBAACD,OAAO,EAAC,WAAW;sBAAAvI,QAAA,gBAC5HF,OAAA;wBAAQwG,SAAS,EAAC,YAAY;wBAACoD,EAAE,EAAC,IAAI;wBAACC,EAAE,EAAC,IAAI;wBAACC,CAAC,EAAC,IAAI;wBAAChB,MAAM,EAAC,cAAc;wBAACG,WAAW,EAAC;sBAAG;wBAAA9I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC,eACrGN,OAAA;wBAAMwG,SAAS,EAAC,YAAY;wBAACkC,IAAI,EAAC,cAAc;wBAACE,CAAC,EAAC;sBAAiH;wBAAAzI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzK,CAAC,cAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,GAEP;gBACD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EAGLmB,cAAc,iBACbzB,OAAA;gBAAKwG,SAAS,EAAG,oDACf/E,cAAc,CAACsB,OAAO,GAClB,8BAA8B,GAC9B,0BACL,EAAE;gBAAA7C,QAAA,eACDF,OAAA;kBAAKwG,SAAS,EAAC,kBAAkB;kBAAAtG,QAAA,gBAC/BF,OAAA;oBAAKwG,SAAS,EAAG,iBAAgB/E,cAAc,CAACsB,OAAO,GAAG,gBAAgB,GAAG,cAAe,EAAE;oBAAA7C,QAAA,EAC3FuB,cAAc,CAACsB,OAAO,gBACrB/C,OAAA;sBAAKwG,SAAS,EAAC,SAAS;sBAACiC,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,cAAc;sBAAAxI,QAAA,eAC9DF,OAAA;wBAAM2I,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,uIAAuI;wBAACC,QAAQ,EAAC;sBAAS;wBAAA1I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrL,CAAC,gBAENN,OAAA;sBAAKwG,SAAS,EAAC,SAAS;sBAACiC,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,cAAc;sBAAAxI,QAAA,eAC9DF,OAAA;wBAAM2I,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,yNAAyN;wBAACC,QAAQ,EAAC;sBAAS;wBAAA1I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvQ;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNN,OAAA;oBAAKwG,SAAS,EAAC,aAAa;oBAAAtG,QAAA,gBAC1BF,OAAA;sBAAIwG,SAAS,EAAG,eAAc/E,cAAc,CAACsB,OAAO,GAAG,gBAAgB,GAAG,cAAe,EAAE;sBAAA7C,QAAA,EACxFuB,cAAc,CAACsB,OAAO,GAAG,wBAAwB,GAAG;oBAAmB;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC,EACJmB,cAAc,CAACsB,OAAO,gBACrB/C,OAAA;sBAAKwG,SAAS,EAAC,MAAM;sBAAAtG,QAAA,EAClBuB,cAAc,CAACsI,WAAW,iBACzB/J,OAAA;wBAAKwG,SAAS,EAAC,wBAAwB;wBAAAtG,QAAA,gBACrCF,OAAA;0BAAAE,QAAA,gBAAGF,OAAA;4BAAAE,QAAA,EAAQ;0BAAQ;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACmB,cAAc,CAACsI,WAAW,CAAC5C,IAAI,EAAC,IAAE,EAAC1F,cAAc,CAACsI,WAAW,CAACC,GAAG,EAAC,GAAC;wBAAA;0BAAA7J,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACrGN,OAAA;0BAAAE,QAAA,gBAAGF,OAAA;4BAAAE,QAAA,EAAQ;0BAAsB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACmB,cAAc,CAACsI,WAAW,CAACE,UAAU,CAACjD,IAAI,CAAC,IAAI,CAAC;wBAAA;0BAAA7G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9F;oBACN;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,gBAENN,OAAA;sBAAKwG,SAAS,EAAC,MAAM;sBAAAtG,QAAA,gBACnBF,OAAA;wBAAGwG,SAAS,EAAC,sBAAsB;wBAAAtG,QAAA,EAAEuB,cAAc,CAACuB;sBAAK;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EAC7DmB,cAAc,CAAC4C,WAAW,IAAI5C,cAAc,CAAC4C,WAAW,CAACyC,MAAM,GAAG,CAAC,iBAClE9G,OAAA;wBAAKwG,SAAS,EAAC,MAAM;wBAAAtG,QAAA,gBACnBF,OAAA;0BAAGwG,SAAS,EAAC,kCAAkC;0BAAAtG,QAAA,EAAC;wBAAY;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eAChEN,OAAA;0BAAIwG,SAAS,EAAC,iDAAiD;0BAAAtG,QAAA,EAC5DuB,cAAc,CAAC4C,WAAW,CAAC6F,GAAG,CAAC,CAACC,UAAkB,EAAEC,KAAa,kBAChEpK,OAAA;4BAAAE,QAAA,EAAiBiK;0BAAU,GAAlBC,KAAK;4BAAAjK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAkB,CACjC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAChB,EAEAM,kBAAkB,KAAKpC,WAAW,CAACkJ,SAAS,iBAC3C1H,OAAA,CAAC1B,WAAW;UAAC6I,IAAI,EAAC,SAAS;UAACC,UAAU,EAAC,gBAAgB;UAACG,QAAQ;UAAArH,QAAA,gBAC9DF,OAAA,CAACP,YAAY;YACX0H,IAAI,EAAC,SAAS;YACdK,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,CAAC;cAAEf,KAAK,EAAE,QAAQ;cAAE2D,UAAU,EAAE;YAAM,CAAC,EAChD;cAAE5C,KAAK,EAAE,CAAC;cAAEf,KAAK,EAAE,SAAS;cAAE2D,UAAU,EAAE;YAAM,CAAC,CACjD;YACF5C,KAAK,EACH1G,iBAAiB,KAAKE,SAAS,GAC3BF,iBAAiB,CAACuJ,QAAQ,CAAC,CAAC,GAC5B,GACL;YACD1C,gBAAgB,EAAEH,KAAK,IACrBzG,oBAAoB,CAACyG,KAAK,GAAI8C,MAAM,CAAC9C,KAAK,CAAC,GAAa,CAAC,CAC1D;YACDE,KAAK,EAAE;cAAEJ,QAAQ,EAAE;YAAK,CAAE;YAC1Bf,SAAS,EAAC;UAAM;YAAArG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACFN,OAAA;YAAGwG,SAAS,EAAC,oCAAoC;YAAAtG,QAAA,EAAC;UAElD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CACd,eAEDN,OAAA,CAAClB,mBAAmB;UAClBqI,IAAI,EAAC,aAAa;UAClBC,UAAU,EAAC,aAAa;UACxBC,OAAO,EAAC,0DAA0D;UAClEE,QAAQ;QAAA;UAAApH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAEhBN,OAAA,CAACtB,aAAa;QAACwI,YAAY,EAAC,wBAAwB;QAACV,SAAS,EAAC,MAAM;QAAAtG,QAAA,gBACnEF,OAAA,CAACrB,YAAY;UACXwI,IAAI,EAAC,SAAS;UACdC,UAAU,EAAC,SAAS;UACpBC,OAAO,EAAC,qGAAqG;UAC7GE,QAAQ;QAAA;UAAApH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEFN,OAAA,CAACF,wBAAwB;UACvBqH,IAAI,EAAC,aAAa;UAClBC,UAAU,EAAC,oBAAoB;UAC/BC,OAAO,EAAC,6CAA6C;UACrDmD,IAAI,EAAC,wBAAwB;UAC7BlD,WAAW,EAAC,gBAAgB;UAC5BmD,MAAM,EAAC,GAAG,CAAC;UAAA;UACXC,QAAQ,EAAE,IAAK,CAAC;UAAA;UAChBC,YAAY,EAAE,CAAA1I,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoE,WAAW,KAAI;QAAG;UAAAlG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eAEFN,OAAA,CAAClB,mBAAmB;UAClBqI,IAAI,EAAC,KAAK;UACVC,UAAU,EAAC,aAAa;UACxBC,OAAO,EAAC;QAAoG;UAAAlH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7G,CAAC,eAEFN,OAAA,CAAClB,mBAAmB;UAClBqI,IAAI,EAAC,aAAa;UAClBC,UAAU,EAAC,aAAa;UACxBC,OAAO,EAAC;QAAoF;UAAAlH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAEhBN,OAAA,CAACtB,aAAa;QAACwI,YAAY,EAAC,mBAAmB;QAACV,SAAS,EAAC,MAAM;QAAAtG,QAAA,gBAC9DF,OAAA,CAAClB,mBAAmB;UAClBqI,IAAI,EAAC,OAAO;UACZC,UAAU,EAAC,OAAO;UAClBC,OAAO,EAAC,oJAAoJ;UAC5JE,QAAQ;QAAA;UAAApH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,EAED,CAAC,EAAEM,kBAAkB,KAAKpC,WAAW,CAACsC,KAAK,CAAC,iBAC3Cd,OAAA,CAAClB,mBAAmB;UAClBqI,IAAI,EAAC,YAAY;UACjBC,UAAU,EAAC,cAAc;UACzBC,OAAO,EAAC,0FAA0F;UAClGE,QAAQ;QAAA;UAAApH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF,EAEA,CAAC,EAAEM,kBAAkB,KAAKpC,WAAW,CAACsC,KAAK,CAAC,iBAC3Cd,OAAA,CAAClB,mBAAmB;UAClBqI,IAAI,EAAC,sBAAsB;UAC3BC,UAAU,EAAC,uBAAuB;UAClCC,OAAO,EAAC;QAAmG;UAAAlH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5G,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAEhBN,OAAA,CAACtB,aAAa;QACZwI,YAAY,EACVtG,kBAAkB,KAAKpC,WAAW,CAACsC,KAAK,GACpC,yBAAyB,GACzB,SACL;QACD0F,SAAS,EAAC,MAAM;QAAAtG,QAAA,GAEf,CAAC,EAAEU,kBAAkB,KAAKpC,WAAW,CAACkJ,SAAS,CAAC,iBAC/C1H,OAAA,CAACjB,mBAAmB;UAClBoI,IAAI,EAAC,SAAS;UACdC,UAAU,EAAC,cAAc;UACzBG,QAAQ;QAAA;UAAApH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF,EAEA,CAAC,EAAEM,kBAAkB,KAAKpC,WAAW,CAACkJ,SAAS,CAAC,iBAC/C1H,OAAA,CAAClB,mBAAmB;UAClBqI,IAAI,EAAC,cAAc;UACnBC,UAAU,EAAC,eAAe;UAC1BC,OAAO,EAAC,oIAAoI;UAC5IE,QAAQ;QAAA;UAAApH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF,eAEDN,OAAA,CAAClB,mBAAmB;UAClBqI,IAAI,EAAC,gBAAgB;UACrBC,UAAU,EACRxG,kBAAkB,KAAKpC,WAAW,CAACsC,KAAK,GACpC,yBAAyB,GACzB,sBACL;UACDuG,OAAO,EACLzG,kBAAkB,KAAKpC,WAAW,CAACsC,KAAK,GACpC,8EAA8E,GAC9E,sFACL;UACDyG,QAAQ,EAAE3G,kBAAkB,KAAKpC,WAAW,CAACsC,KAAM;UACnD6I,QAAQ,EAAE/I,kBAAkB,KAAKpC,WAAW,CAACsC;QAAM;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAEhBN,OAAA,CAACtB,aAAa;QAACwI,YAAY,EAAC,wBAAwB;QAACV,SAAS,EAAC,MAAM;QAAAtG,QAAA,GAClE,CAAC,EAAEU,kBAAkB,KAAKpC,WAAW,CAACsC,KAAK,CAAC,iBAC3Cd,OAAA,CAACpB,cAAc;UACbuI,IAAI,EAAC,aAAa;UAClBC,UAAU,EAAC,cAAc;UACzBE,WAAW,EAAC,qBAAqB;UACjCE,OAAO,EAAEnI,kBAAmB;UAC5BoI,KAAK,EAAC,EAAE;UACRF,QAAQ;QAAA;UAAApH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF,EACA,CAAC,EAAEM,kBAAkB,KAAKpC,WAAW,CAACsC,KAAK,CAAC,iBAC3Cd,OAAA,CAACpB,cAAc;UACbuI,IAAI,EAAC,iBAAiB;UACtBC,UAAU,EAAC,kBAAkB;UAC7BE,WAAW,EAAC,yBAAyB;UACrCE,OAAO,EAAElI,sBAAuB;UAChCmI,KAAK,EAAC,EAAE;UACRF,QAAQ;UACRf,SAAS,EAAC;QAAwB;UAAArG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CACF,EACA,CAAC,EAAEM,kBAAkB,KAAKpC,WAAW,CAACsC,KAAK,CAAC,iBAC3Cd,OAAA;UAAKwG,SAAS,EAAC,mBAAmB;UAAAtG,QAAA,gBAChCF,OAAA,CAACnB,UAAU;YACTsI,IAAI,EAAC,mBAAmB;YACxBC,UAAU,EAAC,qBAAqB;YAChCG,QAAQ;UAAA;YAAApH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEFN,OAAA,CAACnB,UAAU;YACTsI,IAAI,EAAC,iBAAiB;YACtBC,UAAU,EAAC,mBAAmB;YAC9BG,QAAQ;UAAA;YAAApH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACA,CAAC,EAAEM,kBAAkB,KAAKpC,WAAW,CAACsC,KAAK,CAAC,iBAC3Cd,OAAA,CAACpB,cAAc;UACbuI,IAAI,EAAC,gBAAgB;UACrBG,WAAW,EAAC,wBAAwB;UACpCF,UAAU,EAAC,iBAAiB;UAC5BI,OAAO,EAAEjI,qBAAsB;UAC/BkI,KAAK,EAAC,EAAE;UACRF,QAAQ;UACRK,gBAAgB,EAAEH,KAAK,IAAI;YACzBzF,yBAAyB,CAACyF,KAAK,CAAC;UAClC;QAAE;UAAAtH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF,EACA,CAAC,EAAEM,kBAAkB,KAAKpC,WAAW,CAACsC,KAAK,CAAC,IAC3CiB,sBAAsB,KAAKvC,eAAe,CAACoL,KAAK,iBAC9C5K,OAAA,CAACf,eAAe;UACdkI,IAAI,EAAC,qBAAqB;UAC1BC,UAAU,EAAC,yBAAyB;UACpCC,OAAO,EAAC,8DAA8D;UACtEC,WAAW,EAAC;QAAE;UAAAnH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CACF,eAEHN,OAAA,CAAClB,mBAAmB;UAClBqI,IAAI,EAAC,OAAO;UACZC,UAAU,EAAC,OAAO;UAClBC,OAAO,EAAC;QAA+D;UAAAlH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC,eAEFN,OAAA,CAACH,iBAAiB;UAChBsH,IAAI,EAAC,gBAAgB;UACrBC,UAAU,EAAC,YAAY;UACvBC,OAAO,EAAC,8CAA8C;UACtDmD,IAAI,EAAC,yBAAyB;UAC9BlD,WAAW,EAAC,SAAS;UACrBmD,MAAM,EAAC,SAAS;UAChBI,SAAS,EAAE;YACTtG,KAAK,EAAE,UAAU;YACjBuG,QAAQ,EAAE;cACRC,KAAK,EAAE,GAAG;cACVC,MAAM,EAAE;YACV,CAAC;YACDC,WAAW,EAAE;cACXF,KAAK,EAAE,GAAG;cACVC,MAAM,EAAE;YACV,CAAC;YACDE,OAAO,EAAE,EAAE;YACXC,OAAO,EAAE;UACX,CAAE;UACFR,YAAY,EACV1I,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEqD,cAAc,GAAG,CAACrD,OAAO,CAACqD,cAAc,CAAC,GAAG;QACtD;UAAAnF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGfN,OAAA,CAACL,mBAAmB;MAClByL,MAAM,EAAEzJ,aAAc;MACtB0J,OAAO,EAAEA,CAAA,KAAMzJ,gBAAgB,CAAC,KAAK;IAAE;MAAAzB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACG,EAAA,CAp2BID,aAAa;EAAA,QACFrC,SAAS,EACPC,WAAW,EAyBKY,UAAU,EAI3CT,YAAY;AAAA;AAAA+M,GAAA,GA/BR9K,aAAa;AAs2BnB,eAAeA,aAAa;AAAC,IAAAD,EAAA,EAAA+K,GAAA;AAAAC,YAAA,CAAAhL,EAAA;AAAAgL,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}