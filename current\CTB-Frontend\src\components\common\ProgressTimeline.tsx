import React from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { BsCheckLg } from 'react-icons/bs';

interface Section {
  title: string;
  requiredFields: string[];
}

type SectionStatus = "completed" | "inProgress" | "notStarted";

interface ProgressTimelineProps {
  sections: Section[];
}

const ProgressTimeline: React.FC<ProgressTimelineProps> = ({ sections }) => {
  const { control } = useFormContext();
  const watchedFields = useWatch({ control, name: sections.flatMap(section => section.requiredFields) });
  
  const fieldValues = Object.fromEntries(
    sections.flatMap(section => section.requiredFields).map((field, index) => [field, watchedFields[index]])
  );
  
  const isFieldValid = (value: any): boolean => {
    if (typeof value === 'string') return value.trim() !== '';
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'number') return !isNaN(value);
    if (value instanceof Date) return !isNaN(value.getTime());
    if (typeof value === 'object' && value !== null) {
      return Object.keys(value).length > 0;
    }
    return !!value;
  };

  const isSectionComplete = (section: Section): boolean => {
    return section.requiredFields.every(field => isFieldValid(fieldValues[field]));
  };

  const getSectionStatus = (index: number): SectionStatus => {
    if (isSectionComplete(sections[index])) {
      return "completed";
    }
    const firstIncompleteIndex = sections.findIndex(section => !isSectionComplete(section));
    return index === firstIncompleteIndex ? "inProgress" : "notStarted";
  };

  return (
    <div className="w-full flex items-center sticky top-0 space-x-4 py-3 z-10 bg-white">
      {sections.map((section, index) => (
        <div key={index} className="flex items-center ml-4 px-2 py-2 rounded-md transition-all duration-500"
          style={{
            background: getSectionStatus(index) === 'completed' || getSectionStatus(index) === 'inProgress'
              ? 'linear-gradient(90deg, #007DBC47, #D9D9D900)'
              : 'linear-gradient(90deg, #BAB8B880, #D9D9D900)'
          }}>
          <div className={`w-10 h-10 flex items-center justify-center rounded-full font-semibold text-white 
            ${getSectionStatus(index) === 'completed' ? 'bg-blue-600' :
              getSectionStatus(index) === 'inProgress' ? 'bg-blue-600' : 'bg-gray-400 border border-gray-500'}`}>
            {getSectionStatus(index) === 'completed' ? <BsCheckLg className="text-white" /> : index + 1}
          </div>
          <div className="ml-2 flex flex-col">
            <span className="text-sm font-semibold text-gray-900 whitespace-nowrap">{section.title}</span>
            <div className="flex items-center space-x-1">
              <div className={`w-2 h-2 rounded-full 
                ${getSectionStatus(index) === 'completed' ? 'bg-green-500' :
                  getSectionStatus(index) === 'inProgress' ? 'bg-blue-600' : 'bg-transparent'}`}></div>
              <span className={`text-xs whitespace-nowrap ${getSectionStatus(index) === 'completed' ? 'text-green-600' : 'text-gray-500'}`}>
                {getSectionStatus(index) === 'completed' ? 'Completed' : getSectionStatus(index) === 'inProgress' ? 'In Progress' : ''}
              </span>
            </div>
          </div>
          {index < sections.length - 1 && <div className="w-16 h-[2px] bg-gray-300 ml-6 -mr-6"></div>}
        </div>
      ))}
    </div>
  );
};

export default ProgressTimeline;
