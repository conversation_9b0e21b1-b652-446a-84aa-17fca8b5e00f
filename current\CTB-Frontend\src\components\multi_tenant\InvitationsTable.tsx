import React from "react";
import { UserRole } from "../../utils/api/endpoints/user/credentials";
import {
  Invitation,
  formatRole,
  getRoleStyle
} from "../../utils/hooks/multi-tenant/invitation";
import { MdOutlineMail } from "react-icons/md";

interface InvitationsTableProps {
  invitations: Invitation[];
  isLoading: boolean;
  isError: boolean;
  availableRoles: { value: UserRole; label: string }[];
  onRoleChange: (invitationId: number, newRole: UserRole) => void;
  onDeleteClick: (id: number) => void;
  onResendInvitation: (email: string, role: UserRole) => void;
}

const InvitationsTable: React.FC<InvitationsTableProps> = ({
  invitations,
  isLoading,
  isError,
  availableRoles,
  onRoleChange,
  onDeleteClick,
  onResendInvitation
}) => {
  return (
    <div className="overflow-x-auto rounded-lg border border-gray-200">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-blue-700">
          <tr>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-white"
            >
              Email
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-white"
            >
              Role
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-white"
            >
              Status
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-white"
            >
              Expires
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-white"
            >
              Manage role
            </th>
            <th
              scope="col"
              className="py-3 text-left text-xs font-medium uppercase text-white"
            >
              Action
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 bg-white">
          {isLoading ? (
            <tr>
              <td
                colSpan={6}
                className="px-6 py-4 text-center text-sm text-gray-500"
              >
                Loading invitations...
              </td>
            </tr>
          ) : isError ? (
            <tr>
              <td
                colSpan={6}
                className="px-6 py-4 text-center text-sm text-red-500"
              >
                Failed to load invitations
              </td>
            </tr>
          ) : invitations.length === 0 ? (
            <tr>
              <td
                colSpan={6}
                className="px-6 py-4 text-center text-sm text-gray-500"
              >
                No invitations found
              </td>
            </tr>
          ) : (
            invitations.map(invitation => (
              <tr
                key={invitation.id}
                className="transition-colors duration-200 hover:bg-gray-50"
              >
                <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                  {invitation.invitee_email}
                </td>
                <td className="whitespace-nowrap px-6 py-4">
                  <span
                    className={`rounded-full px-3 py-1 text-xs ${getRoleStyle(
                      invitation.role
                    )}`}
                  >
                    {formatRole(invitation.role)}
                  </span>
                </td>
                <td className="whitespace-nowrap px-6 py-4">
                  <span
                    className={`rounded-full px-3 py-1 text-xs ${
                      invitation.status === "pending"
                        ? "bg-yellow-100 text-yellow-800"
                        : invitation.status === "expired"
                        ? "bg-red-100 text-red-800"
                        : "bg-green-100 text-green-800"
                    }`}
                  >
                    {invitation.status}
                  </span>
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                  {new Date(invitation.expires_at).toLocaleDateString()}
                </td>
                <td className="whitespace-nowrap py-4 text-sm">
                  <select
                    className="rounded-md border border-gray-300 p-2 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                    value={invitation.role}
                    onChange={e =>
                      onRoleChange(invitation.id, Number(e.target.value))
                    }
                  >
                    {availableRoles.map(({ value, label }) => (
                      <option key={value} value={value}>
                        {label}
                      </option>
                    ))}
                  </select>
                </td>
                <td className="whitespace-nowrap py-4 text-sm">
                  {invitation.status === "expired" ? (
                    <button
                      onClick={() =>
                        onResendInvitation(
                          invitation.invitee_email,
                          invitation.role
                        )
                      }
                      className="text-blue-600 transition-colors hover:text-blue-900 focus:outline-none"
                      title="Resend invitation"
                    >
                      <MdOutlineMail className="h-5 w-5" />
                    </button>
                  ) : (
                    <button
                      onClick={() => onDeleteClick(invitation.id)}
                      className="text-red-600 transition-colors hover:text-red-900 focus:outline-none"
                      title="Deactivate user"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </button>
                  )}
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
};

export default InvitationsTable;
