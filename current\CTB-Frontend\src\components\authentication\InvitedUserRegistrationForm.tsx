import { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { MdLockOutline, MdPermIdentity } from "react-icons/md";
import Ma<PERSON>head from "./Masthead";
import { imgRegister } from "../../assets";
import Form from "../forms/Form";
import toast from "react-hot-toast";
import AuthTextBox from "./inputs/AuthTextBox";
import AuthButton from "./inputs/AuthButton";
import { UseFormSetError } from "react-hook-form";
import {
  verifyInvitation,
  completeInvitedUserRegistration
} from "../../utils/api/endpoints/user/invitation";

type RegisterFields = {
  username: string;
  password: string;
  confirmPassword: string;
  display_name?: string;
  country?: string;
};

const InvitedUserRegistrationForm = () => {
  const navigate = useNavigate();
  const { token } = useParams();
  const [invitationData, setInvitationData] = useState<{
    email: string;
    role: number;
  } | null>(null);
  const [loading, setLoading] = useState(false); // Handle loading state

  useEffect(() => {
    const validateInvitation = async () => {
      if (token) {
        try {
          const result = await verifyInvitation(token);
          if (result.valid) {
            setInvitationData(result);
          } else {
            toast.error("Invalid or expired invitation");
            navigate("/invalid-invitation");
          }
        } catch (error) {
          toast.error("Something went wrong while verifying the invitation.");
          navigate("/invalid-invitation");
        }
      }
    };
    validateInvitation();
  }, [token, navigate]);

  const handleSubmit = async (
    data: RegisterFields,
    setError: UseFormSetError<RegisterFields>
  ) => {
    if (data.password !== data.confirmPassword) {
      setError("confirmPassword", {
        message: "Passwords do not match"
      });
      return;
    }

    if (token) {
      setLoading(true);
      try {
        const registerResult = await completeInvitedUserRegistration(token, {
          username: data.username,
          password: data.password,
          display_name: data.display_name,
          country: data.country
        });

        if (registerResult) {
          toast.success("Registration successful! Redirecting to login...");
          setTimeout(() => navigate("/login"), 2000);
        }
      } catch (error: any) {
        console.error("Registration failed:", error);

        if (error.response) {
          const errorMessage = error.response.data.message;

          // Handle specific errors from backend
          if (errorMessage.includes("Invitation has expired")) {
            toast.error("This invitation link has expired.");
            navigate("/invalid-invitation");
          } else if (errorMessage.includes("Username already taken")) {
            setError("username", { message: "Username is already taken" });
          } else if (errorMessage.includes("Password must have at least")) {
            setError("password", { message: errorMessage });
          } else {
            toast.error(
              errorMessage || "Registration failed. Please try again."
            );
          }
        } else {
          toast.error("Network error. Please check your connection.");
        }
      } finally {
        setLoading(false);
      }
    }
  };

  const defaultValues = useMemo(
    () => ({
      username: "",
      password: "",
      confirmPassword: "",
      display_name: "",
      country: ""
    }),
    []
  );

  if (!invitationData) {
    return <div>Loading...</div>;
  }

  return (
    <>
      <article className="max-w-[350px]">
        <h1>Complete Your Registration</h1>
        <p>
          Welcome {invitationData.email}! Please complete your registration to
          get started.
        </p>

        <Form
          defaultValues={defaultValues}
          onSubmit={handleSubmit}
          className="mt-5"
        >
          <div className="flex flex-col gap-8">
            <AuthTextBox
              label="Username"
              name="username"
              placeholder="Choose your username"
              rules={{ required: true }}
              Icon={MdPermIdentity}
            />

            <AuthTextBox
              label="Display Name"
              name="display_name"
              placeholder="Enter your display name"
              Icon={MdPermIdentity}
            />

            <AuthTextBox
              label="Country"
              name="country"
              placeholder="Enter your country"
              Icon={MdPermIdentity}
            />

            <AuthTextBox
              label="Password"
              name="password"
              placeholder="Enter your password"
              password={true}
              rules={{
                required: true,
                pattern: {
                  value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/,
                  message:
                    "Password should contain at least 8 characters, including uppercase, lowercase, number, and special character."
                }
              }}
              Icon={MdLockOutline}
            />

            <AuthTextBox
              label="Confirm Password"
              name="confirmPassword"
              placeholder="Confirm your password"
              password={true}
              rules={{ required: true }}
              Icon={MdLockOutline}
            />
          </div>

          <AuthButton disabled={loading}>
            {loading ? "Processing..." : "Complete Registration"}
          </AuthButton>
        </Form>
      </article>

      <Masthead label="Welcome To" backgroundImg={imgRegister} />
    </>
  );
};

export default InvitedUserRegistrationForm;
