import toast from "react-hot-toast";
import {
  ProgramTarget,
  ProgramType,
  prepareProgramFormData
} from "../../api/endpoints/programs/parsePrograms";
import { RewardTiers } from "../../../components/editor/inputs/programs/ProgramRewardsInput";
import { useNavigate } from "react-router-dom";
import {
  useDeleteProgramMutation,
  useGetProgramQuery,
  usePostProgramActivationMutation,
  useSetPrivateAccessUsersMutation,
  useUpdateProgramMutation
} from "../../api/endpoints/programsApi";

export type ProgramUpdateValues = {
  title: string;
  description: string;
  scope: string;
  outOfScope: string;
  targets: ProgramTarget[];
  type: ProgramType;
  rewards?: RewardTiers;
  knownVulnerabilities?: string;
  other?: string;
  vpn?: string;
  termsOfService: string;
  credentials?: string;
  rewardPolicy: string;
  private?: boolean;
  profilePicture?: Blob[];
  attachments?: Blob[];
  testingType?: string;
  environmentType?: string;
  complianceType?: string;
  otherComplianceType?: string;
  expectedStartDate?: Date;
  expectedEndDate?: Date;
  notification_methods?: String[];
  existingAttachments?: string[];
  testLead?: string;
  preparedBy?: string;
  reviewedBy?: string;
  approvedBy?: string;
};

/**
 * Provides access to a single program
 */
const useProgram = (id?: number) => {
  const navigate = useNavigate();
  const {
    data: program,
    isError,
    isLoading
  } = useGetProgramQuery(id as number, {
    skip: id === undefined || id === null || isNaN(id)
  });
  const [updateProgramMutation] = useUpdateProgramMutation();
  const [setPrivateAccessUsersMutation] = useSetPrivateAccessUsersMutation();
  const [postProgramActivationMutataion] = usePostProgramActivationMutation();
  const [deleteProgramMutation] = useDeleteProgramMutation();

  /**
   * Provides error & success messages when updating private access users
   */
  const setPrivateAccessUsers = (users: number[]) => {
    if (id) {
      const loader = toast.loading("Updating private access users...");

      setPrivateAccessUsersMutation({ id, users }).then(res => {
        toast.remove(loader);

        if ("error" in res) {
          toast.error("Failed to update private access users...");
        } else {
          toast.success("Updated private access users!");
        }
      });
    } else {
      toast.error("Failed to update private access users...");
    }
  };

  /**
   * Adds the given user to the list of private access users for this program
   */
  const addPrivateAccessUser = (userId: number) => {
    if (id && program)
      setPrivateAccessUsers([
        ...(program.privateAccessUsers?.filter(id => id !== userId) || []),
        userId
      ]);
  };

  /**
   * Removes the given user from the list of private access users for this program
   */
  const removePrivateAccessUser = (userId: number) => {
    if (id && program)
      setPrivateAccessUsers(
        (program.privateAccessUsers || []).filter(id => id !== userId)
      );
  };

  /**
   * Makes a request to save the program in the backend
   * (creating it if it does not exist)
   */
  const saveProgram = (
    data: Omit<ProgramUpdateValues, "private"> & {
      private?: 0 | 1;
      existingAttachments?: string[];
    }
  ) => {
    const loader = toast.loading("Saving...");

    // Extract file attachments from the data
    const attachments = Array.isArray(data.attachments)
      ? data.attachments.filter(item => item instanceof File)
      : [];

    const details = {
      ...data,
      payoutRange: [
        data.rewards?.low || 0,
        data.rewards?.medium || 0,
        data.rewards?.high || 0,
        data.rewards?.critical || 0
      ],
      private: data.private === 1 ? true : data.private === 0 ? false : false,
      triaged: id ? undefined : true,
      profilePicture: undefined,
      attachments: undefined,
      rewards: undefined,
      notification_methods: data.notification_methods,
      // Include existing attachments that weren't deleted
      existingAttachments: data.existingAttachments || []
    };

    const profilePicture =
      data.profilePicture && data.profilePicture.length > 0
        ? data.profilePicture[0]
        : undefined;

    // Validation checks
    if (details.type === ProgramType.VDP && details.private) {
      toast.error("VDP cannot be private");
      toast.remove(loader);
      return;
    }
    if (details.type === ProgramType.PTAAS && details.private === false) {
      toast.error("PTAAS cannot be public");
      toast.remove(loader);
      return;
    }

    updateProgramMutation({
      id,
      data: prepareProgramFormData({
        details,
        icon: profilePicture,
        attachments: attachments
      })
    }).then(res => {
      toast.remove(loader);
      if ("error" in res) {
        toast.error("Failed to save program");
      } else {
        toast.success("Program saved");
        if (res?.data) {
          navigate(`/dashboard/programs/${(res as { data: number }).data}`);
        } else if (id) {
          navigate(`/dashboard/programs/${id}`);
        } else {
          navigate(`/dashboard/programs`);
        }
      }
    });
  };

  return {
    program,
    isLoading,
    isError,
    addPrivateAccessUser,
    removePrivateAccessUser,
    saveProgram,
    toggleProgramActivation: (isActivated: boolean) => {
      if (id) {
        const loader = toast.loading("Processing...");

        postProgramActivationMutataion({ id, isActivated }).then(res => {
          toast.remove(loader);

          if ("error" in res) {
            toast.error(
              `Failed to ${isActivated ? "activate" : "deactivate"} program`
            );
          } else {
            toast.success(
              `Program ${isActivated ? "activated!" : "deactivated."}`
            );
          }
        });
      }
    },
    deleteProgram: () => {
      if (id) {
        const loader = toast.loading("Deleting...");

        deleteProgramMutation(id).then(res => {
          toast.remove(loader);

          if ("error" in res) {
            toast.error("Failed to delete program...");
          } else {
            toast.success("Program deleted!");
          }
        });
      }
    }
  };
};

export default useProgram;
