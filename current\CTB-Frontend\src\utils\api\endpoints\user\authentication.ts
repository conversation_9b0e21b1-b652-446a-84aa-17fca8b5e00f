import { UserCredentials } from "../../../store/reducer/slices/userReducer";
import { UserRole, setCredentials } from "./credentials";
import axios from "../../axios";
import toast from "react-hot-toast";

export type LoginCredentials = {
  email: string;
  password: string;
};

export type RegisterCredentials = {
  username: string;
} & LoginCredentials;

type UserAPIResponse = {
  data: {
    username: string;
    user_id: number;
    role: number;
    accessToken: string;
    refreshToken: string;
    pfp: string;
  };
  message?: string;
};

/**
 * Convert a given user object from an API response to match
 * the UserCredentials type used in this application
 */
const parseCredentials = (
  userEmail: string,
  data: UserAPIResponse["data"]
): UserCredentials => ({
  userId: data.user_id,
  username: data.username,
  userRole: data.role,
  userEmail: userEmail,
  accessToken: data.accessToken,
  refreshToken: data.refreshToken
});

/**
 * Make a request to the authentication server to
 * register a new user with the given credentials.
 *
 * @returns Returns `true` if the registration was successful,
 * otherwise returns `undefined`
 */
export const register = async ({
  role,
  email,
  username,
  password
}: {
  role: UserRole.RESEARCHER | UserRole.BUSINESS;
  email: string;
  username: string;
  password: string;
}) => {
  try {
    await axios.post(
      "/v2/register",
      JSON.stringify({
        role,
        email,
        username,
        password
      }),
      {
        headers: { "Content-Type": "Application/json" },
        withCredentials: false
      }
    );

    return true;
  } catch (e: any) {
    console.log("in catch", e);
    if (e?.response?.data?.message) {
      toast.error(e.response.data.message);
    }
    return undefined;
  }
};

/**
 * Make a login request to the authentication server,
 * updating the local user credentials if successful.
 *
 * @returns Returns `true` if the login was successful, `false` if
 * further action is required (i.e. 2FA) and `undefined` if the login
 * failed.
 */
export const postLogin = async ({ email, password }: LoginCredentials) => {
  const response = (await axios.post(
    "/login",
    JSON.stringify({
      email,
      password
    }),
    {
      headers: { "Content-Type": "Application/json" },
      withCredentials: false
    }
  )) as UserAPIResponse;

  const credentials = parseCredentials(email, response.data);

  // Save the relevant user credentials
  if (
    response.data.accessToken === null ||
    response.data.accessToken === undefined
  ) {
    setCredentials({ userEmail: email });
  } else {
    // Otherwise, update the credentials to log the user in
    setCredentials(credentials);
  }

  return credentials;
};

/**
 * Sends an OTP code to the authentication server
 * to get verified - completing the login process
 * if successful.
 *
 * @returns Returns `true` if the code was valid,
 * `false` if the code was invalid. If too many attempts
 * were made (or there was an error validating the OTP)
 * then the user is logged out and returned to the landing
 * page.
 */
export const requestOTPVerfication = async (email: string, code: string) => {
  try {
    const response = await axios.post(
      "/verify",
      JSON.stringify({
        code,
        email
      }),
      {
        headers: { "Content-Type": "Application/json" },
        withCredentials: false
      }
    );

    // Successful response == valid code. User can be logged in
    const credentials = parseCredentials(email, response.data);
    setCredentials(credentials);

    return credentials;
  } catch (error) {
    console.log(error);
    return error;
  }
};

export const requestPasswordReset = async (identity: string) => {
  await axios.post(
    "/sendPasswordReset",
    JSON.stringify({
      email: identity
    }),
    {
      headers: { "Content-Type": "application/json" },
      withCredentials: false
    }
  );
};

export const submitPasswordReset = async (
  password: string,
  email: string,
  token: string
) => {
  await axios.post(
    "submitPasswordToken",
    JSON.stringify({
      newPassword: password,
      email,
      token
    }),
    {
      headers: { "Content-Type": "application/json" },
      withCredentials: false
    }
  );
};
