import React, { useState } from "react";

interface ChangeLogEntry {
  id: string;
  field: string;
  old_value: string | null;
  new_value: string | null;
  section: string | null;
  role: string;
  created_at: string;
  user_id: number;
  visibility: 'qa_admin' | 'admin_only';
}

interface ChangeLogSidebarProps {
  changeLog: ChangeLogEntry[];
  onClose: () => void;
  isOpen: boolean;
  loading?: boolean;
}

const fieldLabel = (field: string) => {
  // Optionally map field names to user-friendly labels
  return field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

const roleBadgeColor = (role: string) => {
  switch (role.toUpperCase()) {
    case 'ADMIN': return 'bg-blue-100 text-blue-700';
    case 'SUB_ADMIN': return 'bg-purple-100 text-purple-700';
    case 'ADMIN_MANAGER': return 'bg-indigo-100 text-indigo-700';
    case 'QA': return 'bg-amber-100 text-amber-700';
    default: return 'bg-gray-100 text-gray-700';
  }
};

const isJsonString = (str: string | null) => {
  if (!str) return false;
  try {
    const trimmed = str.trim();
    if ((trimmed.startsWith('{') && trimmed.endsWith('}')) || (trimmed.startsWith('[') && trimmed.endsWith(']'))) {
      JSON.parse(trimmed);
      return true;
    }
    return false;
  } catch {
    return false;
  }
};

const prettyPrint = (str: string) => {
  try {
    return JSON.stringify(JSON.parse(str), null, 2);
  } catch {
    return str;
  }
};

const MAX_INLINE_LENGTH = 80;

const ChangeLogSidebar: React.FC<ChangeLogSidebarProps> = ({ changeLog, onClose, isOpen, loading }) => {
  const [modalContent, setModalContent] = useState<string | null>(null);
  const [modalTitle, setModalTitle] = useState<string>('');
  const [showModal, setShowModal] = useState(false);

  const openModal = (title: string, value: string) => {
    setModalTitle(title);
    setModalContent(value);
    setShowModal(true);
  };
  const closeModal = () => setShowModal(false);

  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 z-50 flex justify-end animate-slide-in">
      {/* Overlay */}
      <div className="fixed inset-0 bg-black bg-opacity-30 transition-opacity" onClick={onClose} />
      {/* Sidebar */}
      <aside className="relative w-full max-w-xl h-full bg-gradient-to-br from-white to-slate-50 shadow-2xl border-l border-slate-200 flex flex-col transition-transform duration-300 ease-in-out animate-fade-in">
        {/* Sticky Header */}
        <div className="sticky top-0 z-10 flex items-center justify-between px-8 py-5 bg-white bg-opacity-90 border-b border-slate-200 shadow-sm">
          <h2 className="text-2xl font-bold tracking-tight text-slate-800 font-display">Edit History</h2>
          <button onClick={onClose} className="p-2 rounded-full hover:bg-slate-100 transition-colors" aria-label="Close">
            <svg className="w-6 h-6 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        {/* Content */}
        <div className="flex-1 overflow-y-auto p-8 space-y-6 bg-gradient-to-b from-white to-slate-50">
          {loading ? (
            <div className="flex flex-col items-center justify-center h-64">
              <svg className="animate-spin h-10 w-10 text-blue-500 mb-4" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
              <span className="text-slate-500 text-lg font-medium">Loading edit history...</span>
            </div>
          ) : changeLog.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-slate-400">
              <svg className="w-14 h-14 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6 0a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-lg font-semibold">No edit history found.</span>
            </div>
          ) : (
            <ul className="space-y-6">
              {changeLog.map(log => (
                <li
                  key={log.id}
                  className="relative bg-white/95 rounded-2xl shadow-sm border-l-4 border-blue-400 p-0 flex flex-col hover:shadow-lg transition-shadow group overflow-hidden"
                  style={{ boxShadow: '0 2px 8px 0 rgba(30, 64, 175, 0.04)' }}
                >
                  {/* Top meta row */}
                  <div className="flex items-center justify-between px-6 pt-5 pb-2">
                    <div className="flex items-center gap-2">
                      <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 20h9" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16.5 3.5a2.121 2.121 0 113 3L7 19.5 3 21l1.5-4L16.5 3.5z" />
                      </svg>
                      <span className="font-semibold text-slate-800 text-base font-display truncate" title={fieldLabel(log.field)}>
                        {fieldLabel(log.field)}
                      </span>
                      <span className="ml-2 text-xs text-slate-400 font-medium">Changed</span>
                    </div>
                    <span className="text-xs text-slate-400 flex items-center gap-1">
                      <svg className="w-4 h-4 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {new Date(log.created_at).toLocaleString()}
                    </span>
                  </div>
                  {/* User/role row */}
                  <div className="flex items-center gap-3 px-6 pb-2">
                    <span className="flex items-center gap-1 text-xs text-slate-500">
                      <svg className="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5.121 17.804A13.937 13.937 0 0112 15c2.5 0 4.847.655 6.879 1.804M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      By: <span className="font-medium text-slate-700 ml-1">{log.user_id}</span>
                    </span>
                    <span className={`px-2 py-0.5 rounded-full text-xs font-semibold ${roleBadgeColor(log.role)}`}>{log.role}</span>
                    <span className="ml-auto px-2 py-0.5 rounded bg-slate-100 text-slate-500 text-xs" title={log.visibility === 'qa_admin' ? 'Visible to QA & Admin' : 'Admin Only'}>
                      {log.visibility === 'qa_admin' ? 'QA & Admin' : 'Admin Only'}
                    </span>
                  </div>
                  {/* Divider */}
                  <div className="border-t border-slate-100 my-2" />
                  {/* Value diff row */}
                  <div className="grid grid-cols-2 gap-4 px-6 pb-5">
                    {/* Old value */}
                    <div className="flex flex-col">
                      <span className="text-xs text-slate-400 mb-1">Before</span>
                      <span className="break-all font-mono px-2 py-1 rounded bg-red-50 text-red-700 min-h-[32px] max-h-32 overflow-y-auto text-sm shadow-inner">
                        {log.old_value && log.old_value.length > MAX_INLINE_LENGTH ? (
                          <>
                            {log.old_value.slice(0, MAX_INLINE_LENGTH)}...
                            <button
                              className="ml-1 text-xs underline text-blue-400 hover:text-blue-700"
                              onClick={() => openModal('Old Value', isJsonString(log.old_value || '') ? prettyPrint(log.old_value || '') : (log.old_value || ''))}
                              title="Show full value"
                            >
                              <svg className="inline w-4 h-4 align-text-bottom" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            </button>
                          </>
                        ) : (
                          log.old_value || <span className="italic text-slate-300">(empty)</span>
                        )}
                      </span>
                    </div>
                    {/* New value */}
                    <div className="flex flex-col">
                      <span className="text-xs text-slate-400 mb-1">After</span>
                      <span className="break-all font-mono px-2 py-1 rounded bg-green-50 text-green-700 min-h-[32px] max-h-32 overflow-y-auto text-sm shadow-inner">
                        {log.new_value && log.new_value.length > MAX_INLINE_LENGTH ? (
                          <>
                            {log.new_value.slice(0, MAX_INLINE_LENGTH)}...
                            <button
                              className="ml-1 text-xs underline text-blue-400 hover:text-blue-700"
                              onClick={() => openModal('New Value', isJsonString(log.new_value || '') ? prettyPrint(log.new_value || '') : (log.new_value || ''))}
                              title="Show full value"
                            >
                              <svg className="inline w-4 h-4 align-text-bottom" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            </button>
                          </>
                        ) : (
                          log.new_value || <span className="italic text-slate-300">(empty)</span>
                        )}
                      </span>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
        {/* Modal for full value */}
        {showModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
            <div className="bg-white rounded-2xl shadow-2xl max-w-3xl w-full p-8 relative animate-fade-in border border-slate-200">
              <button
                className="absolute top-3 right-3 p-2 rounded-full hover:bg-slate-100"
                onClick={closeModal}
                aria-label="Close"
              >
                <svg className="w-6 h-6 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
              <h3 className="text-xl font-bold mb-4 font-display">{modalTitle}</h3>
              <pre className="bg-slate-50 rounded-lg p-4 text-slate-800 overflow-x-auto whitespace-pre-wrap text-base max-h-[70vh] border border-slate-100">
                {modalContent}
              </pre>
            </div>
          </div>
        )}
      </aside>
      <style>{`
        .font-display {
          font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
        }
        @keyframes fade-in {
          from { opacity: 0; transform: translateX(40px); }
          to { opacity: 1; transform: translateX(0); }
        }
        .animate-fade-in {
          animation: fade-in 0.4s cubic-bezier(0.4,0,0.2,1);
        }
        @keyframes slide-in {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        .animate-slide-in {
          animation: slide-in 0.2s cubic-bezier(0.4,0,0.2,1);
        }
      `}</style>
    </div>
  );
};

export default ChangeLogSidebar; 