import axios from "../../axios";
import { parseProgramOverview, ProgramOverviewResponse } from "../programs/parsePrograms";  

const BASE_URL = "/v2/programs";

export const getProgramOverview = async (programId: number): Promise<ProgramOverviewResponse> => {
  try {
    console.log(`Fetching Program Overview for ID: ${programId}`);

    const response = await axios.get(`${BASE_URL}/get-program-overview/${programId}`);
  
    const parsedData: ProgramOverviewResponse = parseProgramOverview(response.data);

    return parsedData;
  } catch (error) {
    console.error("Error fetching program overview:", error);
    throw error;
  }
};

