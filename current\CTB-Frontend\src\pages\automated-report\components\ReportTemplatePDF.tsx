import React from 'react';
import { Document, Page, Text, Font } from '@react-pdf/renderer';
import CoverPage from './sections/CoverPage';
import DisclaimerPage from './sections/DisclaimerPage';
import TableOfContentsPage from './sections/TableOfContentsPage';
import DocumentReferencePage from './sections/DocumentReferencePage';
import ExecutiveSummaryPage from './sections/ExecutiveSummaryPage';
import KeyFindingsPage from './sections/KeyFindingsPage';
import ScopePage from './sections/ScopePage';
import ProjectObjectivesPage from './sections/ProjectObjectivesPage';
import SummaryOfFindingsPage from './sections/SummaryOfFindingsPage';
import VulnerabilityRatingDefinitionsPage from './sections/VulnerabilityRatingDefinitionsPage';
import KeyFindingsListPage from './sections/KeyFindingsListPage';
import AppendixOwaspRiskRatingPages from './sections/AppendixOwaspRiskRatingPages';
import MethodologyPage from './sections/MethodologyPage';
import NetworkMethodologyPage from './sections/NetworkMethodologyPage';
import MobileMethodologyPage from './sections/MobileMethodologyPage';
import { ReportData } from '../types/report.types';
import { SectionPageProvider, useSectionPages } from './SectionPageContext';

// Helper function for disclaimer content (copied from original)
  const processDisclaimerContent = (content: string) => {
    if (!content) return [];
    let processed = content
    .replace(/\\n/g, '\n')
    .replace(/\\s+/g, ' ')
      .replace(/<p[^>]*>/gi, '')
      .replace(/<\/p>/gi, '\n\n')
    .replace(/<br\s*\/?/gi, ' ')
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
    .replace(/\s+/g, ' ')
      .trim();
    const sentences = processed.split(/\.\s+/).map(s => s.trim()).filter(s => s.length > 0);
    let paragraphs: string[] = [];
    let currentParagraph = '';
    for (let i = 0; i < sentences.length; i++) {
      const sentence = sentences[i];
      currentParagraph += sentence + '. ';
      if (sentence.includes('prior consent') || 
          sentence.includes('definitive risk analysis') || 
          sentence.includes('general public') ||
          i === sentences.length - 1) {
        paragraphs.push(currentParagraph.trim());
        currentParagraph = '';
      }
    }
    if (paragraphs.length !== 3) {
      const text = processed;
      const p1End = text.indexOf('prior consent.') + 'prior consent.'.length;
      const p2End = text.indexOf('definitive risk analysis.') + 'definitive risk analysis.'.length;
      if (p1End > 0 && p2End > p1End) {
        paragraphs = [
          text.substring(0, p1End).trim(),
          text.substring(p1End, p2End).trim(),
          text.substring(p2End).trim()
        ];
      }
    }
    return paragraphs;
  };

interface ReportTemplatePDFProps {
  reportData: ReportData;
  pieChartImage?: string;
  barChartImage?: string;
  processedFindings?: Record<string, any[]> | null;
  processing?: boolean;
}

const ReportTemplatePDFContent: React.FC<ReportTemplatePDFProps> = ({
  reportData,
  pieChartImage,
  barChartImage,
  processedFindings,
  processing,
}) => {
  const disclaimerParagraphs = processDisclaimerContent((reportData.disclaimer ?? '') || '');
  const { updateSectionPage } = useSectionPages();
  if (processing) {
    return (
      <Document>
        <Page>
          <Text>Loading...</Text>
        </Page>
      </Document>
    );
  }
  return (
    <Document>
      <CoverPage reportData={reportData} />
      <DisclaimerPage
        reportData={reportData}
        disclaimerParagraphs={disclaimerParagraphs}
        processDisclaimerContent={processDisclaimerContent}
        sectionId="Disclaimer"
      />
      <DocumentReferencePage reportData={reportData} sectionId="DocumentReference" />
      <TableOfContentsPage reportData={reportData} />
      <ExecutiveSummaryPage
        reportData={reportData}
        pieChartImage={pieChartImage}
        barChartImage={barChartImage}
        sectionId="ExecutiveSummary"
      />
      <KeyFindingsPage reportData={reportData} sectionId="KeyFindings" />
      <ScopePage reportData={reportData} sectionId="Scope" />
      <ProjectObjectivesPage reportData={reportData} sectionId="ProjectObjectives" />
      <SummaryOfFindingsPage
        reportData={reportData}
        pieChartImage={pieChartImage}
        barChartImage={barChartImage}
        sectionId="SummaryOfFindings"
      />
      <VulnerabilityRatingDefinitionsPage reportData={reportData} sectionId="VulnerabilityRatingDefinitions" />
      <KeyFindingsListPage reportData={reportData} sectionId="KeyFindingsList" />
      {AppendixOwaspRiskRatingPages({ documentNumber: reportData.document_number || 'Document Number', registerSectionPage: updateSectionPage, sectionId: 'AppendixOwaspRiskRating_Page1' })
        .filter(React.isValidElement)
        .map((page, idx) => React.cloneElement(page, { key: `appendix-${idx}` }))}
      {(reportData.methodology?.web) && (
        <MethodologyPage documentNumber={reportData.document_number} registerSectionPage={updateSectionPage} sectionId="Methodology" />
      )}
      {(reportData.methodology?.network) && (
        <NetworkMethodologyPage documentNumber={reportData.document_number} registerSectionPage={updateSectionPage} sectionId="NetworkMethodology" />
      )}
      {(reportData.methodology?.mobile) && (
        <MobileMethodologyPage documentNumber={reportData.document_number} registerSectionPage={updateSectionPage} sectionId="MobileMethodology" />
      )}
    </Document>
  );
};

const ReportTemplatePDF: React.FC<ReportTemplatePDFProps> = (props) => (
  <SectionPageProvider>
    <ReportTemplatePDFContent {...props} />
  </SectionPageProvider>
);

export default ReportTemplatePDF; 