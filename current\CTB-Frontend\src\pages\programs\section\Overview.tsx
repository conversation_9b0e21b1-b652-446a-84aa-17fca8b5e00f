import moment from "moment";

interface ProgramOverview {
  status?: string;
  createdAt?: string;
}

interface Program {
  description?: string;
}

interface OverviewProps {
  program?: Program;
  programOverview?: ProgramOverview;
}

const Overview: React.FC<OverviewProps> = ({ program, programOverview }) => {
  return (
    <section>
      <h2 className="text-xl font-semibold text-gray-900 py-2">Program Overview</h2>
      <p className="text-gray-600 mt-2 text-sm">{program?.description || "No description available"}</p>
      
      <div className="p-4 grid grid-cols-4 gap-6 text-lg">
        <div className="flex flex-col items-start border-r-2 border-gray-300 pr-6">
          <span className="text-black mb-2 text-sm">Status</span>
          <span className="bg-green-600 text-white px-6 py-2 text-sm">
            {programOverview?.status || "Unknown"}
          </span>
          <span className="text-xs text-black mt-2">
            {programOverview?.createdAt 
              ? moment(programOverview.createdAt).utc().format("DD MMM YYYY HH:mm:ss") 
              : "No date available"
            } UTC
          </span>
        </div>
      </div>
    </section>
  );
};

export default Overview;
