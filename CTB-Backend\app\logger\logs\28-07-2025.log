2025-07-28T05:54:58.190Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-28T05:56:08.797Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-28T05:56:08.804Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T05:56:08.805Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T05:56:08.806Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T05:56:08.807Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T05:56:08.808Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:08.809Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T05:56:08.810Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:08.812Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:08.813Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T05:56:08.814Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:08.815Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T05:56:08.815Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:08.840Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T05:56:08.842Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T05:56:08.861Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:08.903Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-28T05:56:08.937Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T05:56:08.944Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T05:56:08.944Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T05:56:08.945Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T05:56:08.945Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T05:56:08.946Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:08.947Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T05:56:08.947Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:08.948Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:08.948Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T05:56:08.949Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:08.949Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T05:56:08.949Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:08.953Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:08.959Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T05:56:08.960Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T05:56:08.997Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T05:56:09.003Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T05:56:09.003Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T05:56:09.004Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T05:56:09.004Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T05:56:09.005Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.005Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T05:56:09.005Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.006Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.006Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T05:56:09.007Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.007Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T05:56:09.007Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.010Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.019Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T05:56:09.020Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T05:56:09.051Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T05:56:09.060Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T05:56:09.061Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T05:56:09.061Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T05:56:09.061Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T05:56:09.062Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.062Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T05:56:09.063Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.064Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.065Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T05:56:09.065Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.065Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T05:56:09.066Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.069Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.074Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T05:56:09.075Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T05:56:09.105Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T05:56:09.111Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T05:56:09.112Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T05:56:09.112Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T05:56:09.112Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T05:56:09.113Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.114Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T05:56:09.114Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.115Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.115Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T05:56:09.115Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.116Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T05:56:09.116Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.119Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.125Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T05:56:09.125Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T05:56:09.153Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T05:56:09.356Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T05:56:09.357Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T05:56:09.358Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T05:56:09.358Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T05:56:09.359Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.359Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T05:56:09.360Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.360Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.361Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T05:56:09.361Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.362Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T05:56:09.362Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.366Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.371Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T05:56:09.371Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T05:56:09.399Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T05:56:09.405Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T05:56:09.405Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T05:56:09.406Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T05:56:09.406Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T05:56:09.407Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.407Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T05:56:09.407Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.408Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.408Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T05:56:09.408Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.409Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T05:56:09.409Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.412Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:09.418Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T05:56:09.419Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T05:56:09.449Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T05:56:14.137Z  [automated-report.controller | getAllPentestReports] info: Admin 2 requesting pentest reports with filters: {"page":"1","limit":"12","sort_by":"createdAt","sort_order":"DESC"} 
2025-07-28T05:56:14.151Z  [automated-report.controller | getAllPentestReports] info: Retrieved 0 reports in 13.52ms for role 2 
2025-07-28T05:56:14.160Z  [automated-report.controller | getAllPentestReports] info: Admin 2 requesting pentest reports with filters: {"page":"1","limit":"12","sort_by":"createdAt","sort_order":"DESC"} 
2025-07-28T05:56:14.166Z  [automated-report.controller | getAllPentestReports] info: Retrieved 0 reports in 6.52ms for role 2 
2025-07-28T05:56:16.317Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-28T05:56:16.333Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 2 
2025-07-28T05:56:17.934Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T05:56:17.935Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T05:56:17.936Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T05:56:17.936Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T05:56:17.936Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:17.937Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T05:56:17.938Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:17.939Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:17.939Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T05:56:17.940Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:17.942Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T05:56:17.942Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:17.946Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:17.954Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T05:56:17.955Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T05:56:18.038Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T05:56:18.046Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T05:56:18.047Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T05:56:18.047Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T05:56:18.048Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T05:56:18.049Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.049Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T05:56:18.050Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.050Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.051Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T05:56:18.051Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.052Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T05:56:18.052Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.058Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.063Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T05:56:18.064Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T05:56:18.100Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T05:56:18.110Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T05:56:18.111Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T05:56:18.112Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T05:56:18.114Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T05:56:18.114Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.116Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T05:56:18.117Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.120Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.121Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T05:56:18.122Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.123Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T05:56:18.124Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.128Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.133Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T05:56:18.134Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T05:56:18.161Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T05:56:18.167Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T05:56:18.167Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T05:56:18.168Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T05:56:18.168Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T05:56:18.168Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.169Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T05:56:18.169Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.170Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.170Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T05:56:18.171Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.171Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T05:56:18.171Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.175Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.181Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T05:56:18.182Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T05:56:18.217Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T05:56:18.225Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T05:56:18.226Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T05:56:18.226Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T05:56:18.226Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T05:56:18.227Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.227Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T05:56:18.228Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.228Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.229Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T05:56:18.229Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.229Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T05:56:18.230Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.235Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.239Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T05:56:18.240Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T05:56:18.275Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T05:56:18.301Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T05:56:18.302Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T05:56:18.302Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T05:56:18.302Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T05:56:18.302Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.303Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T05:56:18.303Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.304Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.304Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T05:56:18.305Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.305Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T05:56:18.305Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.308Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.314Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T05:56:18.314Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T05:56:18.352Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T05:56:18.359Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T05:56:18.360Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T05:56:18.360Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T05:56:18.361Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T05:56:18.361Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.362Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T05:56:18.363Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.363Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.363Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T05:56:18.364Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.364Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T05:56:18.364Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.367Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T05:56:18.375Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T05:56:18.376Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T05:56:18.400Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T05:56:35.186Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-28T05:56:35.355Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T05:56:35.368Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-28T05:56:35.373Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-28T05:56:35.417Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T05:56:35.427Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-28T05:56:35.450Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T05:56:35.464Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T05:56:35.475Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T05:56:35.479Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-28T05:56:35.488Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T05:56:35.491Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-28T05:56:35.508Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-28T05:56:36.857Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-28T05:56:36.860Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-28T05:56:36.871Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-28T05:56:36.873Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-28T05:56:38.482Z  [undefined] info: Fetching program details for program ID: 5, User ID: 1 
2025-07-28T05:56:38.542Z  [undefined] info: Fetching program details for program ID: 5, User ID: 1 
2025-07-28T05:56:38.578Z  [undefined] info: Fetching program details for program ID: 5, User ID: 1 
2025-07-28T05:56:38.596Z  [undefined] info: Fetching program details for program ID: 5, User ID: 1 
2025-07-28T05:56:51.286Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-28T05:56:51.406Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T05:56:51.422Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T05:56:51.434Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-28T05:56:51.464Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T05:56:51.471Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-28T05:56:51.472Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-28T05:56:51.498Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T05:56:51.510Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T05:56:51.521Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T05:56:51.526Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-28T05:56:51.533Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-28T05:56:51.553Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-28T05:56:52.841Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-28T05:56:52.843Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-28T05:56:52.852Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-28T05:56:52.854Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-28T05:56:58.629Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T05:56:58.634Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-28T05:56:58.649Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T05:56:58.651Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-28T05:56:58.725Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-28T05:56:58.734Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-28T05:57:04.282Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-28T05:57:04.286Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-28T05:57:40.931Z  [POST | Report] info: <EMAIL> saving report... 
2025-07-28T05:57:40.946Z  [POST | Report] info: Parsed form data succesfully... 
2025-07-28T05:57:41.016Z  [POST | Report] info: Creating new <NAME_EMAIL> 
2025-07-28T05:57:41.045Z  [POST | Report] info: Successfully created new <NAME_EMAIL> 
2025-07-28T05:57:41.045Z  [POST | Report | Jira Integration] info: Jira integration disabled for program 5 
2025-07-28T05:57:41.088Z  [POST | Submit Report | Jira Integration] info: Jira integration enabled for report 15 
2025-07-28T05:57:41.100Z  [POST | Submit Report] info: Submitted report 15 
2025-07-28T05:57:41.103Z  [notifications.controller | sendNotification] info: notification: {"type":7,"payload":"15","message":"hiii22 waiting review for program mee"}, userIDs: 3 
2025-07-28T05:57:41.183Z  [GET | Reports] info: Retrieving report 15 for user 1... 
2025-07-28T05:57:41.192Z  [report.controller | getComments] info: User 1 requested comments for report 15 
2025-07-28T05:57:41.192Z  [report.controller | getComments] info: No comments found for report 15 
2025-07-28T05:57:41.221Z  [GET | Report] info: Retrieved report 15 for user 1. 
2025-07-28T05:57:41.223Z  [report.controller | getComments] info: User 1 requested comments for report 15 
2025-07-28T05:57:41.223Z  [report.controller | getComments] info: No comments found for report 15 
2025-07-28T05:57:48.248Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-28T05:57:48.321Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-28T05:57:48.336Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-28T05:57:50.227Z  [automated-report.controller | getAllPentestReports] info: Admin 3 requesting pentest reports with filters: {"page":"1","limit":"12","sort_by":"createdAt","sort_order":"DESC"} 
2025-07-28T05:57:50.232Z  [automated-report.controller | getAllPentestReports] info: Retrieved 0 reports in 4.99ms for role 3 
2025-07-28T05:57:50.241Z  [automated-report.controller | getAllPentestReports] info: Admin 3 requesting pentest reports with filters: {"page":"1","limit":"12","sort_by":"createdAt","sort_order":"DESC"} 
2025-07-28T05:57:50.248Z  [automated-report.controller | getAllPentestReports] info: Retrieved 0 reports in 6.79ms for role 3 
2025-07-28T05:57:52.977Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-28T05:57:52.979Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-28T05:57:52.986Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-28T05:57:52.988Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-28T05:57:56.916Z  [POST | Report State] info: Updated report 15 state 
2025-07-28T05:57:56.931Z  [notifications.controller | sendNotification] info: notification: {"type":1,"payload":"15","message":"hiii22 has been approved by the CTB team"}, userIDs: 1,2 
2025-07-28T05:57:57.013Z  [POST | Report State | Jira Integration] info: Admin approved report 15, checking Jira configuration 
2025-07-28T05:57:57.015Z  [Jira Integration | Detailed] info: Starting Jira issue creation for report 15 - Config check 
2025-07-28T05:57:57.015Z  [Jira Integration | Detailed] info: Jira URL: Provided, Email: Provided, API Token: Provided, Project Key: Provided 
2025-07-28T05:57:57.016Z  [Jira Integration] info: Attempting to create Jira issue for report 15 in project CTB 
2025-07-28T05:57:57.016Z  [Jira Integration | Detailed] info: Using Jira API URL: https://prateekdp2877.atlassian.net/rest/api/2/issue 
2025-07-28T05:57:57.017Z  [Jira Integration | Detailed] info: Formatted description for Jira issue: 
*Report ID:* 15
*Severity:* CRITICAL
*Category:* Blockchain Infrastructure Misconfiguration > Impro... 
2025-07-28T05:57:57.017Z  [Jira Integration | Detailed] info: Fetching issue types for project CTB 
2025-07-28T05:57:57.916Z  [Jira Integration | Detailed] info: Received metaResponse: Success 
2025-07-28T05:57:57.916Z  [Jira Integration | Detailed] info: Project data: Available, Issue types: 3 
2025-07-28T05:57:57.917Z  [Jira Integration | Detailed] info: Using issue type: Task 
2025-07-28T05:57:57.917Z  [Jira Integration | Detailed] info: Prepared issue data: {"fields":{"project":{"key":"CTB"},"summary":"[CTB Report] hiii22","description":"\n*Report ID:* 15\n*Severity:* CRITICAL\n*Category:* Blockchain Infrastructure Misconfiguration > Improper Bridge Validation And Verification Logic\n*Target:* fgg\n*Program:* mee\n*Report Link:* http://localhost:3000/dashboard/reports/15\n\n*Description:*\nfff\n\n---\nThis issue was automatically created from CTB platform.\n","issuetype":{"name":"Task"},"assignee":{"name":"prateekdp2877"},"labels":["CTB","Security","reports"]}} 
2025-07-28T05:57:57.917Z  [Jira Integration | Detailed] info: Sending POST request to Jira API at https://prateekdp2877.atlassian.net/rest/api/2/issue 
2025-07-28T05:57:58.787Z  [Jira Integration | Detailed] info: Received response from Jira API: {"id":"10018","key":"CTB-2","self":"https://prateekdp2877.atlassian.net/rest/api/2/issue/10018"} 
2025-07-28T05:57:58.788Z  [Jira Integration] info: Successfully created Jira issue CTB-2 for report 15 
2025-07-28T05:57:58.802Z  [POST | Report State | Jira Integration] info: Successfully created Jira issue CTB-2 for approved report 15 and stored in additional_info 
2025-07-28T06:05:21.267Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-28T06:05:21.282Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-28T06:05:21.367Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-28T06:05:21.369Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-28T06:05:21.376Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-28T06:05:21.377Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-28T06:06:22.356Z  [User | Login] error: Invalid login details | additional_data - email - <EMAIL>.
2025-07-28T06:06:25.690Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-28T06:06:25.766Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T06:06:25.770Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-28T06:06:25.771Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-28T06:06:25.790Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T06:06:25.792Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-28T06:06:25.816Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T06:06:25.842Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T06:06:25.852Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T06:06:25.857Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T06:06:25.858Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-28T06:06:25.863Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-28T06:06:25.875Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-28T06:06:42.045Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-28T06:06:42.046Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-28T06:06:42.049Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-28T06:06:42.050Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-28T06:06:45.666Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T06:06:45.669Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-28T06:06:45.677Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-28T06:06:45.678Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-28T06:06:45.719Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-28T06:06:45.724Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-28T06:06:55.321Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-28T06:06:55.325Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-28T06:07:31.171Z  [POST | Report] info: <EMAIL> saving report... 
2025-07-28T06:07:31.173Z  [POST | Report] info: Parsed form data succesfully... 
2025-07-28T06:07:31.181Z  [POST | Report] info: Creating new <NAME_EMAIL> 
2025-07-28T06:07:31.200Z  [POST | Report] info: Successfully created new <NAME_EMAIL> 
2025-07-28T06:07:31.200Z  [POST | Report | Jira Integration] info: Jira integration disabled for program 5 
2025-07-28T06:07:31.224Z  [POST | Submit Report | Jira Integration] info: Jira integration enabled for report 16 
2025-07-28T06:07:31.228Z  [POST | Submit Report] info: Submitted report 16 
2025-07-28T06:07:31.230Z  [notifications.controller | sendNotification] info: notification: {"type":7,"payload":"16","message":"hii3 waiting review for program mee"}, userIDs: 3 
2025-07-28T06:07:31.262Z  [GET | Reports] info: Retrieving report 16 for user 1... 
2025-07-28T06:07:31.266Z  [report.controller | getComments] info: User 1 requested comments for report 16 
2025-07-28T06:07:31.266Z  [report.controller | getComments] info: No comments found for report 16 
2025-07-28T06:07:31.279Z  [GET | Report] info: Retrieved report 16 for user 1. 
2025-07-28T06:07:31.280Z  [report.controller | getComments] info: User 1 requested comments for report 16 
2025-07-28T06:07:31.280Z  [report.controller | getComments] info: No comments found for report 16 
2025-07-28T06:07:37.955Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-28T06:07:38.017Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-28T06:07:38.026Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-28T06:07:39.775Z  [automated-report.controller | getAllPentestReports] info: Admin 3 requesting pentest reports with filters: {"page":"1","limit":"12","sort_by":"createdAt","sort_order":"DESC"} 
2025-07-28T06:07:39.779Z  [automated-report.controller | getAllPentestReports] info: Retrieved 0 reports in 3.25ms for role 3 
2025-07-28T06:07:39.783Z  [automated-report.controller | getAllPentestReports] info: Admin 3 requesting pentest reports with filters: {"page":"1","limit":"12","sort_by":"createdAt","sort_order":"DESC"} 
2025-07-28T06:07:39.786Z  [automated-report.controller | getAllPentestReports] info: Retrieved 0 reports in 3.06ms for role 3 
2025-07-28T06:07:41.875Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-28T06:07:41.877Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-28T06:07:41.880Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-28T06:07:41.881Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-28T06:07:46.287Z  [POST | Report State] info: Updated report 16 state 
2025-07-28T06:07:46.292Z  [notifications.controller | sendNotification] info: notification: {"type":1,"payload":"16","message":"hii3 has been approved by the CTB team"}, userIDs: 1,2 
2025-07-28T06:07:46.300Z  [POST | Report State | Jira Integration] info: Admin approved report 16, checking Jira configuration 
2025-07-28T06:07:46.300Z  [Jira Integration | Detailed] info: Starting Jira issue creation for report 16 - Config check 
2025-07-28T06:07:46.301Z  [Jira Integration | Detailed] info: Jira URL: Provided, Email: Provided, API Token: Provided, Project Key: Provided 
2025-07-28T06:07:46.301Z  [Jira Integration] info: Attempting to create Jira issue for report 16 in project CTB 
2025-07-28T06:07:46.301Z  [Jira Integration | Detailed] info: Using Jira API URL: https://prateekdp2877.atlassian.net/rest/api/2/issue 
2025-07-28T06:07:46.302Z  [Jira Integration | Detailed] info: Formatted description for Jira issue: 
*Report ID:* 16
*Severity:* HIGH
*Category:* Automotive Security Misconfiguration > Immobilizer > E... 
2025-07-28T06:07:46.302Z  [Jira Integration | Detailed] info: Fetching issue types for project CTB 
2025-07-28T06:07:46.832Z  [Jira Integration | Detailed] info: Received metaResponse: Success 
2025-07-28T06:07:46.833Z  [Jira Integration | Detailed] info: Project data: Available, Issue types: 3 
2025-07-28T06:07:46.833Z  [Jira Integration | Detailed] info: Using issue type: Task 
2025-07-28T06:07:46.833Z  [Jira Integration | Detailed] info: Prepared issue data: {"fields":{"project":{"key":"CTB"},"summary":"[CTB Report] hii3","description":"\n*Report ID:* 16\n*Severity:* HIGH\n*Category:* Automotive Security Misconfiguration > Immobilizer > Engine Start\n*Target:* fgg\n*Program:* mee\n*Report Link:* http://localhost:3000/dashboard/reports/16\n\n*Description:*\nvbv\n\n---\nThis issue was automatically created from CTB platform.\n","issuetype":{"name":"Task"},"assignee":{"name":"prateekdp2877"},"labels":["CTB","Security","reports"]}} 
2025-07-28T06:07:46.833Z  [Jira Integration | Detailed] info: Sending POST request to Jira API at https://prateekdp2877.atlassian.net/rest/api/2/issue 
2025-07-28T06:07:47.752Z  [Jira Integration | Detailed] info: Received response from Jira API: {"id":"10019","key":"CTB-3","self":"https://prateekdp2877.atlassian.net/rest/api/2/issue/10019"} 
2025-07-28T06:07:47.752Z  [Jira Integration] info: Successfully created Jira issue CTB-3 for report 16 
2025-07-28T06:07:47.764Z  [POST | Report State | Jira Integration] info: Successfully created Jira issue CTB-3 for approved report 16 and stored in additional_info 
2025-07-28T06:11:10.066Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-28T06:11:10.134Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-28T06:11:10.137Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T06:11:10.137Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T06:11:10.137Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T06:11:10.138Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T06:11:10.138Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.138Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T06:11:10.138Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.138Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.139Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T06:11:10.139Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.139Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T06:11:10.139Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.143Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.146Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T06:11:10.147Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T06:11:10.176Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-28T06:11:10.192Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T06:11:10.199Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T06:11:10.199Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T06:11:10.201Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T06:11:10.201Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T06:11:10.202Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.202Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T06:11:10.202Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.203Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.203Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T06:11:10.203Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.203Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T06:11:10.204Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.206Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.211Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T06:11:10.211Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T06:11:10.233Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T06:11:10.238Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T06:11:10.239Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T06:11:10.239Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T06:11:10.239Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T06:11:10.239Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.240Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T06:11:10.240Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.240Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.240Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T06:11:10.240Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.241Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T06:11:10.241Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.243Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.247Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T06:11:10.248Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T06:11:10.266Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T06:11:10.272Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T06:11:10.273Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T06:11:10.273Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T06:11:10.273Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T06:11:10.273Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.274Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T06:11:10.274Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.274Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.274Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T06:11:10.275Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.275Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T06:11:10.275Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.278Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.282Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T06:11:10.282Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T06:11:10.300Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T06:11:10.304Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T06:11:10.304Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T06:11:10.305Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T06:11:10.305Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T06:11:10.305Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.305Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T06:11:10.305Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.306Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.306Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T06:11:10.306Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.306Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T06:11:10.307Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.308Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.312Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T06:11:10.312Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T06:11:10.330Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T06:11:10.334Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T06:11:10.334Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T06:11:10.335Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T06:11:10.335Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T06:11:10.335Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.335Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T06:11:10.336Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.336Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.336Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T06:11:10.336Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.336Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T06:11:10.337Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.338Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.342Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T06:11:10.342Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T06:11:10.359Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T06:11:10.364Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T06:11:10.365Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T06:11:10.365Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T06:11:10.365Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T06:11:10.365Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.366Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T06:11:10.366Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.366Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.367Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T06:11:10.368Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.368Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T06:11:10.368Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.371Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T06:11:10.372Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T06:11:10.380Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:11:10.393Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T06:11:11.879Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-28T06:11:11.884Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 2 
2025-07-28T06:11:14.269Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-28T06:11:14.275Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-28T06:52:24.492Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-28T06:53:30.328Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-28T06:53:30.467Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-28T06:53:30.477Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T06:53:30.478Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T06:53:30.479Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T06:53:30.479Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T06:53:30.480Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.481Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T06:53:30.482Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.483Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.484Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T06:53:30.484Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.485Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T06:53:30.486Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.505Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.518Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T06:53:30.519Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T06:53:30.529Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-28T06:53:30.577Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T06:53:30.582Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T06:53:30.583Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T06:53:30.583Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T06:53:30.583Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T06:53:30.583Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.584Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T06:53:30.584Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.585Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.585Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T06:53:30.585Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.586Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T06:53:30.586Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.588Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.592Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T06:53:30.594Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T06:53:30.625Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T06:53:30.632Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T06:53:30.633Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T06:53:30.633Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T06:53:30.634Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T06:53:30.634Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.634Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T06:53:30.635Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.635Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.635Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T06:53:30.636Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.636Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T06:53:30.636Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.638Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.642Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T06:53:30.645Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T06:53:30.696Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T06:53:30.702Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T06:53:30.703Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T06:53:30.703Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T06:53:30.704Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T06:53:30.704Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.705Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T06:53:30.705Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.706Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.706Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T06:53:30.707Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.707Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T06:53:30.708Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.715Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.721Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T06:53:30.722Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T06:53:30.749Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T06:53:30.754Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T06:53:30.754Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T06:53:30.755Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T06:53:30.755Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T06:53:30.755Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.756Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T06:53:30.756Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.757Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.757Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T06:53:30.757Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.758Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T06:53:30.758Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.761Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.766Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T06:53:30.767Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T06:53:30.796Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T06:53:30.899Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T06:53:30.900Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T06:53:30.900Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T06:53:30.900Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T06:53:30.900Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.901Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T06:53:30.901Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.901Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.902Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T06:53:30.902Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.902Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T06:53:30.902Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.905Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.908Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T06:53:30.909Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T06:53:30.931Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T06:53:30.937Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-28T06:53:30.937Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-28T06:53:30.938Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-28T06:53:30.939Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-28T06:53:30.939Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.940Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-28T06:53:30.940Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.941Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.941Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-28T06:53:30.942Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.942Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-28T06:53:30.942Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.946Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-28T06:53:30.951Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-28T06:53:30.951Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-28T06:53:30.976Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-28T06:53:32.505Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-28T06:53:32.515Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 2 
2025-07-28T06:53:35.969Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-28T06:53:35.978Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-28T07:24:12.356Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-28T07:24:12.380Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-28T07:25:50.344Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-28T07:25:50.351Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
