import { useEffect, useState } from "react";
import { getRetestWithLogs } from "../../../utils/api/endpoints/retests/retests";
import { CTBRetestLog } from "../../../utils/api/endpoints/retests/parseRetests";

const useRetestLogs = (retest_id: string | undefined, refetch: boolean) => {
  const [logs, setLogs] = useState<CTBRetestLog[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchLogs = async () => {
      if (!retest_id) {
        setError("Retest ID is undefined");
        setLoading(false);
        return;
      }

      try {
        const response = await getRetestWithLogs(retest_id);
        setLogs(response.data.comments);
      } catch (err) {
        console.error("Failed to fetch logs:", err);
        setError("Failed to fetch logs");
      } finally {
        setLoading(false);
      }
    };

    fetchLogs();
  }, [retest_id, refetch]);

  return { logs, loading, error };
};

export default useRetestLogs;
