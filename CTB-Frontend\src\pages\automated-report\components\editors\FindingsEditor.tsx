import React from 'react';
import { ReportData } from '../../types/report.types';
import HtmlEditor from '../HtmlEditor';

interface FindingsEditorProps {
  reportData: ReportData;
  onHtmlChange: (field: string, value: string) => void;
  onDataChange: (field: string, value: any) => void;
  onTableChange: (field: string, value: number) => void;
}

const FindingsEditor: React.FC<FindingsEditorProps> = ({
  reportData,
  onHtmlChange,
  onDataChange,
  onTableChange
}) => {
  return (
    <div className="space-y-8">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
          <h3 className="text-xl font-semibold text-gray-800">Findings</h3>
          <p className="text-sm text-gray-600 mt-1">Edit the findings content below</p>
        </div>
        <div className="p-6">
          <HtmlEditor
            field="findings"
            title="Findings"
            reportData={reportData}
            onHtmlChange={onHtmlChange as any}
          />
        </div>
      </div>

      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
          <h3 className="text-xl font-semibold text-gray-800">Severity Counts</h3>
          <p className="text-sm text-gray-600 mt-1">Manage the severity distribution of findings</p>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-2 gap-6">
            {Object.entries(reportData.open_close_counts_by_severity).map(([severity, counts]) => (
              <div key={severity} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <h4 className="font-semibold text-lg mb-4 text-gray-800">{severity}</h4>
                <div className="space-y-4">
                  {Object.entries(counts).map(([type, value]) => (
                    <div key={type} className="flex justify-between items-center">
                      <span className="text-gray-600">{type}:</span>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => onTableChange(`open_close_counts_by_severity.${severity}.${type}`, Math.max(0, value - 1))}
                          className="w-8 h-8 flex items-center justify-center bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                        >
                          -
                        </button>
                        <input
                          type="number"
                          value={value}
                          onChange={(e) => onTableChange(`open_close_counts_by_severity.${severity}.${type}`, parseInt(e.target.value) || 0)}
                          className="w-20 border border-gray-300 rounded-lg px-3 py-1 text-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          min="0"
                        />
                        <button
                          onClick={() => onTableChange(`open_close_counts_by_severity.${severity}.${type}`, value + 1)}
                          className="w-8 h-8 flex items-center justify-center bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                        >
                          +
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 grid grid-cols-3 gap-6">
            {[
              { label: 'Total Open', value: reportData.total_open, field: 'total_open' },
              { label: 'Total Closed', value: reportData.total_closed, field: 'total_closed' },
              { label: 'Total Findings', value: reportData.total_findings, field: 'total_findings' }
            ].map(({ label, value, field }) => (
              <div key={field} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <h4 className="font-semibold text-lg mb-4 text-gray-800">{label}</h4>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => onTableChange(field, Math.max(0, value - 1))}
                    className="w-8 h-8 flex items-center justify-center bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                  >
                    -
                  </button>
                  <input
                    type="number"
                    value={value}
                    onChange={(e) => onTableChange(field, parseInt(e.target.value) || 0)}
                    className="flex-1 border border-gray-300 rounded-lg px-3 py-1 text-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    min="0"
                  />
                  <button
                    onClick={() => onTableChange(field, value + 1)}
                    className="w-8 h-8 flex items-center justify-center bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                  >
                    +
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FindingsEditor; 