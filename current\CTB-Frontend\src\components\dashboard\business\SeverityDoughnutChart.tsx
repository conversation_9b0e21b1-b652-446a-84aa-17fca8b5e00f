import React, { useState, useMemo } from "react";
import { Doughnut } from "react-chartjs-2";
import { ParsedBusinessDashboard } from "../../../utils/api/endpoints/dashboard/businessDashboardParser";

type SeverityDoughnutChartProps = {
  details: ParsedBusinessDashboard | null;
};

const severityColorMap = {
  critical: "#EF4444",
  high: "#F97316",
  medium: "#FBBF24",
  low: "#34D399"
};

const SeverityDoughnutChart: React.FC<SeverityDoughnutChartProps> = ({
  details
}) => {
  const [selectedProgram, setSelectedProgram] = useState<string>("All");

  const { chartData, totalCount } = useMemo(() => {
    if (!details) {
      return {
        chartData: {
          labels: [],
          datasets: [
            {
              data: [],
              backgroundColor: [],
              borderWidth: 2,
              borderColor: "#ffffff",
              hoverBorderColor: "#ffffff",
              hoverOffset: 4
            }
          ]
        },
        totalCount: 0
      };
    }

    const { overallSeverityCategories, programSeverityCategories } = details;
    const validSeverities = ["low", "medium", "high", "critical"];

    const getData = () => {
      if (selectedProgram === "All") {
        return overallSeverityCategories
          .filter(
            category =>
              category?.severity &&
              validSeverities.includes(category.severity.toLowerCase())
          )
          .map(category => ({
            severity: category.severity,
            count: category.count || 0
          }));
      }

      const program = programSeverityCategories?.find(
        p => p.programName === selectedProgram
      );

      return program
        ? program.severities
            .filter(
              severity =>
                severity?.severity &&
                validSeverities.includes(severity.severity.toLowerCase())
            )
            .map(severity => ({
              severity: severity.severity,
              count: severity.count || 0
            }))
        : [];
    };

    const data = getData();
    const total = data.reduce((sum, item) => sum + item.count, 0);

    return {
      chartData: {
        labels: data.map(item => item.severity),
        datasets: [
          {
            data: data.map(item => item.count),
            backgroundColor: data.map(item => {
              const severity = item.severity?.toLowerCase() || "";
              return (
                severityColorMap[severity as keyof typeof severityColorMap] ||
                "#94A3B8"
              );
            }),
            borderWidth: 2,
            borderColor: "#ffffff",
            hoverBorderColor: "#ffffff",
            hoverOffset: 4
          }
        ]
      },
      totalCount: total
    };
  }, [selectedProgram, details]);

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: "65%",
    plugins: {
      legend: {
        position: "bottom" as const,
        labels: {
          padding: 20,
          usePointStyle: true,
          pointStyle: "circle",
          font: {
            size: 12,
            family: "Inter, sans-serif"
          }
        }
      },
      tooltip: {
        enabled: true,
        position: "nearest" as const,
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        padding: 12,
        titleColor: "white",
        bodyColor: "white",
        borderColor: "rgba(255, 255, 255, 0.1)",
        borderWidth: 1,
        callbacks: {
          label: function (context: any) {
            const value = context.raw;
            const percentage = ((value / totalCount) * 100).toFixed(1);
            return `${context.label}: ${value} (${percentage}%)`;
          }
        }
      }
    }
  };

  const programOptions = [
    "All",
    ...(details?.programSeverityCategories?.map(p => p.programName) || [])
  ];

  return (
    <div className="rounded-xl border border-gray-100 bg-white shadow-sm">
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h3 className="bg-[#0B45DB] px-[10px] py-[7px] text-lg font-semibold text-white">
            Severity Overview
          </h3>
        </div>

        <div className="relative">
          <select
            value={selectedProgram}
            onChange={e => setSelectedProgram(e.target.value)}
            className="appearance-none border border-gray-200 bg-gray-50 px-6 py-[11px] pr-8 text-sm font-medium text-gray-600 transition-colors hover:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20"
            style={{
              width: "180px",
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "normal"
            }}
          >
            {programOptions.map(name => (
              <option key={name} value={name} className="whitespace-normal">
                {name}
              </option>
            ))}
          </select>
          <div className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2">
            <svg
              className="h-4 w-4 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>
        </div>
      </div>

      <div className="relative mb-6 h-[282px]" style={{ minHeight: "282px" }}>
        {/* Chart container */}
        <div className="absolute inset-0 z-10">
          <Doughnut data={chartData} options={options} />
        </div>

        {/* Center stats - with fixed positioning */}
        <div
          style={{
            position: "absolute",
            left: "50%",
            top: "42%", // Adjusted to account for the legend
            transform: "translate(-50%, -50%)",
            zIndex: 0
          }}
        >
          <div className="rounded-full bg-white/90 px-6 py-4 text-center">
            <div className="text-3xl font-bold text-gray-900">{totalCount}</div>
            <div className="text-sm text-gray-500">Total Reports</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SeverityDoughnutChart;
