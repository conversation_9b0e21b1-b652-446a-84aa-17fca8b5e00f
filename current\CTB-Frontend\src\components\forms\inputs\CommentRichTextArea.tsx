import React, { useMem<PERSON>, useCallback } from "react";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "jodit-react";
import { Controller, useFormContext } from "react-hook-form";
import { Jodit } from "jodit";
import "jodit/es2021/jodit.min.css";

// Unique class prefix to prevent global style leaks
const EDITOR_CLASS_PREFIX = "jodit-comment-editor";

type CommentRichTextAreaProps = {
  name: string;
  rules?: any;
  value: string;
  onChange: (value: string) => void;
};

const CommentRichTextArea = ({
  name,
  rules,
  value,
  onChange
}: CommentRichTextAreaProps) => {
  const { control } = useFormContext();

  // Styles for editor display (adapted from original ReactQuill styling)
  const editorStyles = useMemo(
    () => `
      .${EDITOR_CLASS_PREFIX} h1 { font-size: 2em; font-weight: bold; margin: 0.67em 0; }
      .${EDITOR_CLASS_PREFIX} h2 { font-size: 1.5em; font-weight: bold; margin: 0.75em 0; }
      .${EDITOR_CLASS_PREFIX} ul { list-style-type: disc; margin: 0; padding: 0 20px; }
      .${EDITOR_CLASS_PREFIX} ol { list-style-type: decimal; padding-left: 40px; margin: 1em 0; }
      .${EDITOR_CLASS_PREFIX} a { color: #0000EE; text-decoration: underline; }
      .${EDITOR_CLASS_PREFIX} img { max-width: 100%; height: auto; }
      .${EDITOR_CLASS_PREFIX} .prose { max-width: none; }
    `,
    []
  );

  const config = useMemo(
    () => ({
      readonly: false,
      toolbar: true,
      spellcheck: true,
      language: "en",
      theme: "default",
      toolbarButtonSize: "small" as const,
      toolbarSticky: true,
      toolbarAdaptive: true,
      showCharsCounter: false,
      showWordsCounter: false,
      showXPathInStatusbar: false,
      enterMode: "p",
      defaultMode: 1,
      indentMargin: 10,
      allowTabNavigation: true,
      tabNavigation: true,
      askBeforePasteHTML: true,
      askBeforePasteFromWord: true,
      buttons: [
        "bold",
        "italic",
        "underline",
        "strikethrough",
        "|",
        "ul",
        "ol",
        "indent",
        "outdent",
        "|",
        "font",
        "brush",
        "paragraph",
        "|",
        "align",
        "|",
        "link",
        "image",
        "|",
        "undo",
        "redo"
      ],
      uploader: {
        insertImageAsBase64URI: true,
        imagesExtensions: ["jpg", "png", "jpeg", "gif", "webp"],
        processImageBeforeUpload: (img: File) => img
      },
      cleanHTML: {
        allowTags: {
          p: true,
          br: true,
          strong: true,
          em: true,
          u: true,
          strike: true,
          ul: true,
          ol: true,
          li: true,
          a: true,
          img: true,
          h1: true,
          h2: true,
          div: true,
          span: true
        },
        denyTags: {
          script: true,
          iframe: true,
          form: true,
          input: true,
          style: true
        },
        fullyQualifiedLinks: true,
        removeEmptyAttributes: true,
        safeJavaScriptLink: true
      },
      events: {
        afterInit: (editor: Jodit) => {
          editor.editor.classList.add(EDITOR_CLASS_PREFIX);
          const styleTag = editor.createInside.element("style");
          styleTag.innerHTML = editorStyles;
          editor.container.appendChild(styleTag);
        }
      },
      height: 200,
      minHeight: 150,
      maxHeight: 400,
      width: "auto",
      className: `${EDITOR_CLASS_PREFIX}-wrapper`
    }),
    [editorStyles]
  );

  // Process content to handle empty states and formatting
  const processContent = useCallback((content: string) => {
    if (!content || content === "<p><br></p>" || content.trim() === "")
      return "";
    return content;
  }, []);

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field: { onBlur }, fieldState: { error } }) => (
        <div className={`${EDITOR_CLASS_PREFIX}-container`}>
          <style>{`
            .${EDITOR_CLASS_PREFIX}-container .jodit-container {
              border: 1px solid ${error ? "#ef4444" : "#d1d5db"};
              border-radius: 0.5rem;
              max-width: none;
            }
            .${EDITOR_CLASS_PREFIX}-container .jodit-wysiwyg {
              padding: 0.5rem;
            }
            .${EDITOR_CLASS_PREFIX}-container .jodit-error-message {
              color: #ef4444;
              font-size: 0.875rem;
              margin-top: 0.25rem;
            }
          `}</style>
          <JoditEditor
            value={value || ""}
            //@ts-ignore
            config={config}
            onChange={content => {
              const cleaned = processContent(content);
              onChange(cleaned === "<p><br></p>" ? "" : cleaned);
            }}
            onBlur={() => {
              if (value) {
                const cleaned = processContent(value);
                onChange(cleaned === "<p><br></p>" ? "" : cleaned);
              }
              onBlur();
            }}
            className={error ? "jodit-container-error" : ""}
          />
          {error && <p className="jodit-error-message">{error.message}</p>}
        </div>
      )}
    />
  );
};

export default CommentRichTextArea;
