import { useState, useEffect, useCallback, useRef } from "react";
import { getDashboardSummary } from "../../api/endpoints/dashboard/dashboardApi";

export interface DashboardSummary {
  totalReports: {
    current: number;
    changePercent: number | null;
  };
  totalPrograms: {
    current: number;
    changePercent: number | null;
  };
  activeIssues?: {  // Represents active reports
    current: number;
    changePercent: number | null;
  };
  resolvedIssues: {
    current: number;
    changePercent: number | null;
  };
  retestsRemaining: number;
}

/**
 * Hook to fetch dashboard summary data
 * @returns Dashboard summary metrics with loading and error states
 */
export default function useDashboardSummary() {
  const [data, setData] = useState<DashboardSummary | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const isFetchingRef = useRef(false);
  const mountedRef = useRef(true);

  const fetchData = useCallback(async () => {
    // Prevent duplicate API calls
    if (isFetchingRef.current) return;
    try {
      isFetchingRef.current = true;
      setLoading(true);
      setError(null);
      
      const response = await getDashboardSummary();
      
      // Prevent updates if component unmounted
      if (!mountedRef.current) return;
      
      // Validate response has the expected shape - handle nested data structure
      if (
        response &&
        typeof response === 'object' &&
        'success' in response &&
        response.success === true &&
        'data' in response
      ) {
        const dashboardData = response.data;
        
        if (
          dashboardData &&
          typeof dashboardData === 'object' &&
          'totalReports' in dashboardData &&
          // Support both new and old API responses
          (('totalPrograms' in dashboardData) || ('activeIssues' in dashboardData)) &&
          'resolvedIssues' in dashboardData &&
          'retestsRemaining' in dashboardData
        ) {
          // If we have activeIssues in the response, use it directly
          if ('activeIssues' in dashboardData) {
            // For backwards compatibility - if totalPrograms is not present but activeIssues is
            if (!('totalPrograms' in dashboardData)) {
              setData({
                ...dashboardData,
                totalPrograms: dashboardData.activeIssues,
              } as DashboardSummary);
            } else {
              // Both are present, use as is
              setData(dashboardData as DashboardSummary);
            }
          } else {
            // Only totalPrograms is present (old format) - set it as data
            setData(dashboardData as DashboardSummary);
          }
        } else {
          console.error("Invalid dashboard data structure:", dashboardData);
          setError("The dashboard data format is invalid");
        }
      } else {
        console.error("Unexpected dashboard summary response format:", response);
        setError("The dashboard API response format is invalid");
      }
    } catch (err) {
      // Prevent updates if component unmounted
      if (!mountedRef.current) return;
      
      console.error("Error fetching dashboard summary:", err);
      setError("Failed to load dashboard summary data");
    } finally {
      // Prevent updates if component unmounted
      if (mountedRef.current) {
        setLoading(false);
      }
      isFetchingRef.current = false;
    }
  }, []);

  useEffect(() => {
    // Set up mounted ref
    mountedRef.current = true;
    
    // Initial fetch
    fetchData();
    
    // Cleanup function to prevent state updates after unmount
    return () => {
      mountedRef.current = false;
    };
  }, [fetchData]);

  const retryFetch = () => {
    fetchData();
  };

  return { data, loading, error, refetch: fetchData };
} 