import { Request, Response } from "express";
import { ProgramReportService } from "../services/program-report.service";
import {
  ProgramReportAttributes,
  ProgramReportStatus
} from "../models/program_report.model";
import { ProgramReportChangeLog } from "../models/program_report_change_log.model";
import { database } from "../models/db";
import { ProgramReportChatMessage } from "../models/program_report_chat_message.model";
import { Op } from "sequelize";
import sendEmail from "../utils/send-email";
import { UserRole } from "../utils/auth";

// Helper for minimal HTML email
function reportStatusEmail({
  subject,
  programTitle,
  reportTitle,
  message,
  actionBy
}) {
  return `
    <html><body style="font-family:Inter,Arial,sans-serif;background:#f9f9f9;padding:0;margin:0;">
      <div style="max-width:480px;margin:40px auto;background:#fff;border-radius:16px;border:1px solid #e0e0e0;box-shadow:0 2px 8px #0001;overflow:hidden;">
        <div style="padding:32px 32px 16px 32px;text-align:center;">
          <img src="https://c8270c56-7594-4100-9b34-7589297588fa.b-cdn.net/e/bac89f4c-a951-463b-81dd-86c0e12c3427/28695d5f-3f29-41fa-a53d-2f93062026e6.jpeg" alt="Capture The Bug" style="height:48px;margin-bottom:24px;"/>
          <h1 style="font-size:20px;color:#111;margin:0 0 12px 0;">${subject}</h1>
          <table style="width:100%;margin:0 auto 24px auto;font-size:15px;color:#222;text-align:left;">
            <tr><td style="padding:4px 0;font-weight:600;">Program:</td><td style="padding:4px 0;">${programTitle}</td></tr>
            <tr><td style="padding:4px 0;font-weight:600;">Report:</td><td style="padding:4px 0;">${reportTitle}</td></tr>
            <tr><td style="padding:4px 0;font-weight:600;">Action By:</td><td style="padding:4px 0;">${actionBy}</td></tr>
          </table>
          <div style="margin:24px 0 0 0;font-size:15px;color:#555;">${message}</div>
        </div>
        <div style="background:#f5f5f5;padding:16px 32px;text-align:center;font-size:13px;color:#888;">Capture The Bug Platform</div>
      </div>
    </body></html>
  `;
}

export class ProgramReportController {
  /**
   * Create a new program report
   */
  static async createReport(req: Request, res: Response) {
    try {
      const userId = (req as any).user?.user_id;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated"
        });
      }

      const reportData: ProgramReportAttributes = {
        ...req.body,
        user_id: userId,
        role: (req as any).user?.role || "user"
      };

      const report = await ProgramReportService.createReport(reportData);

      res.status(201).json({
        success: true,
        data: report,
        message: "Program report created successfully"
      });
    } catch (error) {
      console.error("Error creating program report:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to create program report"
      });
    }
  }

  /**
   * Get a program report by ID
   */
  static async getReportById(req: Request, res: Response) {
    console.log("getReportById called", {
      params: req.params,
      user: (req as any).user
    });
    const ROLE_MAP = {
      1: "RESEARCHER",
      2: "BUSINESS",
      3: "ADMIN",
      4: "QA",
      5: "SUB_ADMIN",
      6: "ADMIN_MANAGER"
    };
    try {
      const { id } = req.params;
      const user = (req as any).user;
      let userRole = user?.role || "user";
      const userId = user?.user_id;
      if (typeof userRole === "number") {
        userRole = ROLE_MAP[userRole] || "user";
      }

      if (!user) {
        return res.status(401).json({
          status: "error",
          message: "User not authenticated"
        });
      }

      // Try to find by program_report id first
      let report = await database.program_reports.findOne({ where: { id } });
      if (!report) {
        // If not found, try as automated_report_id
        report = await ProgramReportService.getReportByAutomatedId(id);
      }

      // Business user: enforce ownership
      if (userRole === "BUSINESS") {
        const automatedReportId = report?.automated_report_id || id;
        const automatedReport = await database.automated_reports.findOne({
          where: { id: automatedReportId }
        });
        console.log({
          userId,
          userRole,
          automatedReportId,
          automatedReportBusinessId: automatedReport?.business_id,
          reportId: report?.id,
          reportAutomatedReportId: report?.automated_report_id
        });
        if (!automatedReport || automatedReport.business_id !== userId) {
          return res.status(403).json({
            status: "error",
            message: "You do not have access to this report"
          });
        }
      }

      if (report) {
        // Convert the report data to match the expected format from automated-report controller
        const reportData = {
          company_name: report.company_name || "",
          program_name: report.program_name || "",
          report_title: report.report_title || "",
          document_number: report.document_number || "",
          version_number: report.version_number || 1,
          current_date:
            report.current_date || new Date().toISOString().split("T")[0],
          revision_date: report.revision_date || "",
          prepared_by: report.prepared_by || "",
          reviewed_by: report.reviewed_by || "",
          approved_by: report.approved_by || "",
          test_lead: report.test_lead || "",
          version_description: report.version_description || "",
          executive_summary: report.executive_summary || "",
          key_findings: report.key_findings || "",
          scope: report.scope || "",
          methodology: report.methodology || "",
          findings: report.findings || "",
          recommendations: report.recommendations || "",
          recommendations_list: report.recommendations_list || [],
          conclusion: report.conclusion || "",
          disclaimer: report.disclaimer || "",
          target_details: report.target_details || [],
          reports_list: report.reports_list || [],
          open_close_counts_by_severity:
            report.open_close_counts_by_severity || {},
          total_open: report.total_open || 0,
          total_closed: report.total_closed || 0,
          total_findings: report.total_findings || 0,
          date_of_request: report.date_of_request || "",
          detailed_findings: report.detailed_findings || [],
          status: report.status || "draft",
          // Include program names and IDs if available
          program_names: (report as any).program_names || {},
          program_ids: (report as any).program_ids || [],
          program_details: report.program_details || [],
          branding_logo:
            report.branding_logo ||
            "https://i.postimg.cc/QVPV9d0x/Full-name-logo-blue.png",
          branding_company: report.branding_company || "Capture The Bug Ltd."
        };
        return res.status(200).json({
          status: "success",
          message: "Report retrieved successfully",
          data: reportData
        });
      }

      // If not found, compute initial data dynamically and create the program report
      const initialData =
        await ProgramReportService.getInitialProgramReportData(id, user);
      console.log("getReportById - User:", {
        user_id: user.user_id,
        role: user.role
      });
      console.log(
        "getReportById - Initial data result:",
        initialData ? "Found" : "Not found"
      );

      if (initialData) {
        // Create a new program report with the initial data
        const createData: ProgramReportAttributes = {
          ...initialData,
          user_id: user.user_id,
          role: user.role || "user",
          automated_report_id: id,
          status: "draft" as ProgramReportStatus // Set initial status to draft
        };

        const newReport = await ProgramReportService.createReport(createData);

        return res.status(200).json({
          status: "success",
          message: "Program report created and retrieved successfully",
          data: initialData
        });
      }

      // If getInitialProgramReportData fails, create a basic program report
      console.log("getReportById - Creating basic program report as fallback");

      // Get basic automated report info
      const AutomatedReports = database.automated_reports;
      const automatedReport = await AutomatedReports.findOne({
        where: { id: id }
      });

      if (!automatedReport) {
        return res.status(404).json({
          status: "error",
          message: "Automated report not found"
        });
      }

      // Create a basic program report with minimal data
      const basicData: ProgramReportAttributes = {
        user_id: user.user_id,
        role: user.role || "user",
        automated_report_id: id,
        status: "draft" as ProgramReportStatus,
        company_name: automatedReport.company_name || "Unknown Company",
        program_name: automatedReport.program_name || "",
        report_title: automatedReport.title || "Untitled Report",
        version_number: "V1",
        document_number: `DOC-${Date.now()}`,
        current_date: new Date().toISOString().split("T")[0],
        revision_date: new Date().toISOString().split("T")[0],
        executive_summary: "",
        key_findings: "",
        scope: "",
        methodology: "",
        findings: "",
        recommendations: "",
        recommendations_list: [],
        conclusion: "",
        disclaimer: "",
        program_details: [],
        target_details: [],
        severity_counts: {},
        status_counts: {},
        open_close_counts_by_severity: {},
        total_open: 0,
        total_closed: 0,
        total_findings: 0,
        reports_list: [],
        detailed_findings: [],
        branding_logo: "https://i.postimg.cc/QVPV9d0x/Full-name-logo-blue.png",
        branding_company: "Capture The Bug Ltd."
      };

      const newReport = await ProgramReportService.createReport(basicData);

      return res.status(200).json({
        status: "success",
        message: "Basic program report created successfully",
        data: basicData
      });
    } catch (error) {
      console.error("Error getting program report:", error);
      res.status(500).json({
        status: "error",
        message: error.message || "Failed to get program report"
      });
    }
  }

  /**
   * Get all program reports with pagination and filters
   */
  static async getReports(req: Request, res: Response) {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        userId,
        companyName,
        search
      } = req.query;

      const user = (req as any).user;
      const userRole = user?.role || "user";
      const currentUserId = user?.user_id;
      const businessId = user?.user_id; // For business users, their user_id is their business_id

      const options: any = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        status: status as ProgramReportStatus,
        userId: userId ? parseInt(userId as string) : undefined,
        companyName: companyName as string,
        search: search as string
      };

      // BUSINESS role: only see their own reports, and only if automated_report is in allowed statuses
      if (userRole === "BUSINESS") {
        options.userId = currentUserId;
        options.automatedReportStatus = true; // Use as a flag for business user status filtering
        options.businessId = businessId; // Add business_id filter for data isolation
      } else if (
        userRole === "ADMIN" ||
        userRole === "ADMIN_MANAGER" ||
        userRole === "SUB_ADMIN" ||
        userRole === "QA"
      ) {
        // Admin and QA roles: see all workflow statuses
        options.automatedReportStatus = true;
        if (req.query.businessId) {
          options.businessId = parseInt(req.query.businessId as string);
        }
      }

      const result = await ProgramReportService.getReports(options);

      res.status(200).json({
        success: true,
        data: result.reports,
        pagination: {
          page: result.page,
          limit: options.limit,
          total: result.total,
          totalPages: result.totalPages
        }
      });
    } catch (error) {
      console.error("Error getting program reports:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to get program reports"
      });
    }
  }

  /**
   * Update a program report (or create if it doesn't exist)
   */
  static async updateReport(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.user_id;
      const userRole = (req as any).user?.role || "user";
      if (!userId) {
        return res
          .status(401)
          .json({ success: false, message: "User not authenticated" });
      }
      const updateData = {
        ...req.body,
        user_id: userId,
        role: userRole,
        automated_report_id: id
      };
      // Find the existing report
      const existingReport = await ProgramReportService.getReportByAutomatedId(
        id
      );
      const isNewReport = !existingReport;
      let report;
      let companyNameChanged = false;
      let newCompanyName = undefined;
      let programNameChanged = false;
      let newProgramName = undefined;
      // Track if version_number, document_number, or report_title changed
      let versionNumberChanged = false;
      let newVersionNumber = undefined;
      let documentNumberChanged = false;
      let newDocumentNumber = undefined;
      let reportTitleChanged = false;
      let newReportTitle = undefined;
      if (existingReport) {
        // --- Granular Change Log Logic ---
        const changedFields: any[] = [];
        const oldData = existingReport.toJSON();
        for (const key in updateData) {
          if (
            key === "user_id" ||
            key === "role" ||
            key === "automated_report_id"
          )
            continue;
          const oldValue = JSON.stringify(oldData[key] ?? null);
          const newValue = JSON.stringify(updateData[key] ?? null);
          if (oldValue !== newValue) {
            changedFields.push({
              field: key,
              old_value: oldValue,
              new_value: newValue,
              section: null // Optionally, map key to section if needed
            });
            // Track company_name change
            if (key === "company_name") {
              companyNameChanged = true;
              newCompanyName = updateData[key];
            }
            // Track program_name change
            if (key === "program_name") {
              programNameChanged = true;
              newProgramName = updateData[key];
            }
            // Track version_number, document_number, report_title changes
            if (key === "version_number") {
              versionNumberChanged = true;
              newVersionNumber = updateData[key];
            }
            if (key === "document_number") {
              documentNumberChanged = true;
              newDocumentNumber = updateData[key];
            }
            if (key === "report_title") {
              reportTitleChanged = true;
              newReportTitle = updateData[key];
            }
          }
        }
        // Determine visibility
        let visibility: "qa_admin" | "admin_only" = "qa_admin";
        if (["ADMIN", "SUB_ADMIN", "ADMIN_MANAGER"].includes(userRole)) {
          visibility = "admin_only";
        }
        // Save change logs
        for (const change of changedFields) {
          await database.program_report_change_log.create({
            program_report_id: existingReport.id,
            user_id: userId,
            role: userRole,
            field: change.field,
            old_value: change.old_value,
            new_value: change.new_value,
            section: change.section,
            visibility
          });
        }
        // --- End Change Log Logic ---
        // If company_name or version_number/document_number/report_title changed, update in automated_reports as well
        const updateAutomatedReportFields: Record<string, any> = {};
        if (companyNameChanged && newCompanyName) {
          updateAutomatedReportFields.company_name = newCompanyName;
        }
        if (programNameChanged && newProgramName !== undefined) {
          updateAutomatedReportFields.program_name = newProgramName;
        }
        if (versionNumberChanged && newVersionNumber) {
          updateAutomatedReportFields.version_number = newVersionNumber;
        }
        if (documentNumberChanged && newDocumentNumber) {
          updateAutomatedReportFields.document_number = newDocumentNumber;
        }
        if (reportTitleChanged && newReportTitle) {
          updateAutomatedReportFields.title = newReportTitle;
        }
        if (
          Object.keys(updateAutomatedReportFields).length > 0 &&
          existingReport.automated_report_id
        ) {
          await database.automated_reports.update(updateAutomatedReportFields, {
            where: { id: existingReport.automated_report_id }
          });
          console.log(
            `Synchronized fields to automated_reports for id ${existingReport.automated_report_id}:`,
            updateAutomatedReportFields
          );
        }
        report = await ProgramReportService.updateReport(
          existingReport.id,
          updateData
        );
      } else {
        report = await ProgramReportService.updateReport(id, updateData);
      }
      res.status(200).json({
        success: true,
        data: report,
        message: isNewReport
          ? "Program report created successfully"
          : "Program report updated successfully"
      });
    } catch (error) {
      console.error("Error updating program report:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to update program report"
      });
    }
  }

  /**
   * Update report status
   */
  static async updateStatus(req: Request, res: Response) {
    try {
      const { id } = req.params; // This is the automated_report_id
      const { status } = req.body;
      const userId = (req as any).user?.user_id;
      const role = (req as any).user?.role || "user";

      console.log("updateStatus - Request params:", {
        id,
        status,
        userId,
        role
      });

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated"
        });
      }

      if (
        !status ||
        ![
          "draft",
          "qa_review",
          "admin_review",
          "approved",
          "rejected",
          "business_review",
          "business_requested_changes",
          "changes_added",
          "report_updated",
          "business_approved"
        ].includes(status)
      ) {
        return res.status(400).json({
          success: false,
          message:
            "Invalid status. Must be one of: draft, qa_review, admin_review, approved, rejected, business_review, business_requested_changes, changes_added, report_updated, business_approved"
        });
      }

      // Role-based status transition logic
      let nextStatus = null;
      let userRoleStr = role;
      if (typeof userRoleStr === "number") {
        const ROLE_MAP = {
          1: "RESEARCHER",
          2: "BUSINESS",
          3: "ADMIN",
          4: "QA",
          5: "ADMIN_MANAGER",
          6: "DEVELOPER",
          7: "BUSINESS_MANAGER",
          8: "BUSINESS_ADMINISTRATOR",
          9: "SUB_ADMIN"
        };
        userRoleStr = ROLE_MAP[userRoleStr] || "user";
      }
      userRoleStr = String(userRoleStr).toUpperCase();

      // Get current report for transition logic
      let report = await ProgramReportService.getReportByAutomatedId(id);
      const currentStatus = report ? report.status : null;

      // Admin/subadmin/manager approval: move to business_review
      if (
        ["ADMIN", "SUB_ADMIN", "ADMIN_MANAGER"].includes(userRoleStr) &&
        status === "approved"
      ) {
        nextStatus = "business_review";
      }
      // Business approves: final approval
      else if (userRoleStr === "BUSINESS" && status === "business_approved") {
        nextStatus = "business_approved";
      }
      // Business requests changes
      else if (
        userRoleStr === "BUSINESS" &&
        status === "business_requested_changes"
      ) {
        nextStatus = "business_requested_changes";
      }
      // QA adds changes after business requested changes
      else if (
        userRoleStr === "QA" &&
        status === "changes_added" &&
        currentStatus === "business_requested_changes"
      ) {
        nextStatus = "changes_added";
      }
      // Admin/subadmin/manager marks report as updated after QA changes
      else if (
        ["ADMIN", "SUB_ADMIN", "ADMIN_MANAGER"].includes(userRoleStr) &&
        status === "report_updated" &&
        currentStatus === "changes_added"
      ) {
        nextStatus = "report_updated";
      }
      // After report updated, business reviews again
      else if (
        userRoleStr === "BUSINESS" &&
        status === "business_review" &&
        currentStatus === "report_updated"
      ) {
        nextStatus = "business_review";
      }
      // Standard transitions (draft, qa_review, admin_review, rejected)
      else if (
        ["draft", "qa_review", "admin_review", "rejected"].includes(status)
      ) {
        nextStatus = status;
      }
      // Fallback: block invalid transitions
      else {
        console.error("Status transition failed", {
          userRoleStr,
          status,
          currentStatus,
          reportFound: !!report,
          id
        });
        return res.status(400).json({
          success: false,
          message:
            "Invalid status transition for your role or current report state."
        });
      }

      // Update report status
      if (!report) {
        // If no report, create one with the initial data
        const initialData =
          await ProgramReportService.getInitialProgramReportData(
            id,
            (req as any).user
          );
        if (!initialData) {
          return res.status(404).json({
            success: false,
            message: "Automated report not found"
          });
        }
        const reportData = {
          ...initialData,
          automated_report_id: id,
          user_id: userId,
          role,
          status: nextStatus
        };
        report = await ProgramReportService.createReport(reportData);
      } else {
        report = await ProgramReportService.updateStatus(
          report.id,
          nextStatus,
          userId,
          role
        );
      }

      // Also update the automated report status for legacy compatibility
      const AutomatedReports = database.automated_reports;
      await AutomatedReports.update(
        { status: nextStatus },
        { where: { id: id } }
      );

      res.status(200).json({
        success: true,
        data: report,
        message: `Report status updated to ${nextStatus}`
      });

      // After status update, send notifications as needed
      try {
        // Fetch report, program, and business user for context
        const fullReport =
          report || (await ProgramReportService.getReportByAutomatedId(id));
        let programTitle = "";
        let reportTitle = "";
        let businessUser = null;
        if (fullReport) {
          reportTitle = fullReport.report_title || "";
          // Try to get program title from program_details array
          if (
            fullReport.program_details &&
            fullReport.program_details.length > 0
          ) {
            programTitle = fullReport.program_details[0].program_title || "";
          }
          // Fallback: fetch from DB
          if (
            !programTitle &&
            fullReport.program_details &&
            fullReport.program_details[0]?.program_id
          ) {
            const prog = await database.program.findOne({
              where: { program_id: fullReport.program_details[0].program_id },
              attributes: ["program_title"]
            });
            if (prog) programTitle = prog.program_title;
          }
          // Get business user
          if (fullReport.automated_report_id) {
            const autoReport = await database.automated_reports.findOne({
              where: { id: fullReport.automated_report_id }
            });
            if (autoReport && autoReport.business_id) {
              businessUser = await database.user.findOne({
                where: { user_id: autoReport.business_id }
              });
            }
          }
        }
        // Get actionBy user
        const actionByUser = await database.user.findOne({
          where: { user_id: userId },
          attributes: ["display_name", "username", "email"]
        });
        const actionBy =
          actionByUser?.display_name ||
          actionByUser?.username ||
          actionByUser?.email ||
          `User ${userId}`;
        // 1. QA → admin_review (robust check)
        console.log("Transition:", {
          userRoleStr,
          status,
          currentStatus,
          nextStatus
        });
        if (
          userRoleStr === "QA" &&
          (nextStatus === "admin_review" ||
            status === "admin_review" ||
            (currentStatus === "qa_review" && nextStatus === "admin_review"))
        ) {
          const admins = await database.user.findAll({
            where: { role: [UserRole.ADMIN, UserRole.SUB_ADMIN] },
            attributes: ["email"]
          });
          const adminEmails = admins.map(a => a.email).filter(Boolean);
          console.log("Sending admin_review email to:", adminEmails);
          if (adminEmails.length === 0) {
            console.warn(
              "No ADMIN or SUB_ADMIN users found for admin_review notification"
            );
          }
          const subject = `Report Ready for Admin Review`;
          const message = `The QA has reviewed the pentest report and it is ready for admin review and approval for business.`;
          const html = reportStatusEmail({
            subject,
            programTitle,
            reportTitle,
            message,
            actionBy
          });
          for (const admin of admins) {
            if (admin.email) {
              try {
                await sendEmail(admin.email, subject, html);
                console.log("Sent email to", admin.email);
              } catch (e) {
                console.error("Failed to send email to", admin.email, e);
              }
            }
          }
        }
        // 2. ADMIN/SUBADMIN → business_review
        if (
          ["ADMIN", "SUB_ADMIN", "ADMIN_MANAGER"].includes(userRoleStr) &&
          nextStatus === "business_review" &&
          businessUser
        ) {
          const subject = `Your Pentest Report is Ready for Review`;
          const message = `Your pentest report has been approved by admin and is now ready for your review.`;
          const html = reportStatusEmail({
            subject,
            programTitle,
            reportTitle,
            message,
            actionBy
          });
          if (businessUser.email)
            await sendEmail(businessUser.email, subject, html);
        }
        // 3. BUSINESS → business_approved
        if (userRoleStr === "BUSINESS" && nextStatus === "business_approved") {
          const admins = await database.user.findAll({
            where: { role: [UserRole.ADMIN, UserRole.SUB_ADMIN] },
            attributes: ["email"]
          });
          const subject = `Business Approved the Pentest Report`;
          const message = `The business has approved the pentest report.`;
          const html = reportStatusEmail({
            subject,
            programTitle,
            reportTitle,
            message,
            actionBy
          });
          for (const admin of admins) {
            if (admin.email) {
              try {
                await sendEmail(admin.email, subject, html);
                console.log("Sent email to", admin.email);
              } catch (e) {
                console.error("Failed to send email to", admin.email, e);
              }
            }
          }
        }
        // 4. BUSINESS → business_requested_changes
        if (
          userRoleStr === "BUSINESS" &&
          nextStatus === "business_requested_changes"
        ) {
          const notifyRoles = [UserRole.ADMIN, UserRole.SUB_ADMIN, UserRole.QA];
          const notifyUsers = await database.user.findAll({
            where: { role: notifyRoles },
            attributes: ["email"]
          });
          const subject = `Business Requested Changes to Pentest Report`;
          const message = `The business has requested changes to the pentest report. Please review and address the feedback.`;
          const html = reportStatusEmail({
            subject,
            programTitle,
            reportTitle,
            message,
            actionBy
          });
          for (const user of notifyUsers) {
            if (user.email) {
              try {
                await sendEmail(user.email, subject, html);
                console.log("Sent email to", user.email);
              } catch (e) {
                console.error("Failed to send email to", user.email, e);
              }
            }
          }
        }
        // 5. ADMIN/SUBADMIN/ADMIN_MANAGER → rejected (notify QA)
        if (
          [UserRole.ADMIN, UserRole.SUB_ADMIN, UserRole.ADMIN_MANAGER].includes(
            role
          ) &&
          nextStatus === "rejected"
        ) {
          const qas = await database.user.findAll({
            where: { role: UserRole.QA },
            attributes: ["email"]
          });
          const qaEmails = qas.map(q => q.email).filter(Boolean);
          console.log("Sending rejected notification to QA:", qaEmails);
          if (qaEmails.length === 0) {
            console.warn("No QA users found for rejected notification");
          }
          const subject = `Report Rejected by Admin`;
          const message = `The admin has rejected the pentest report. Please review and fix the issues as per feedback.`;
          const html = reportStatusEmail({
            subject,
            programTitle,
            reportTitle,
            message,
            actionBy
          });
          for (const qa of qas) {
            if (qa.email) {
              try {
                await sendEmail(qa.email, subject, html);
                console.log("Sent email to", qa.email);
              } catch (e) {
                console.error("Failed to send email to", qa.email, e);
              }
            }
          }
        }
      } catch (err) {
        console.error("Error sending status transition emails:", err);
      }
    } catch (error) {
      console.error("Error updating report status:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to update report status"
      });
    }
  }

  /**
   * Get program report preview
   */
  static async getReportPreview(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { reportData } = req.body;
      const user = (req as any).user;

      // Get the report from database by automated_report_id
      let report = await ProgramReportService.getReportByAutomatedId(id);

      // If not found, compute initial data dynamically
      if (!report) {
        const initialData =
          await ProgramReportService.getInitialProgramReportData(id, user);
        if (initialData) {
          // Create a mock report object with the initial data
          report = {
            id: id,
            ...initialData,
            user_id: user?.user_id,
            role: user?.role || "user",
            status: "draft",
            created_at: new Date(),
            updated_at: new Date()
          } as any;
        } else {
          return res.status(404).json({
            status: "error",
            message: "Program report not found"
          });
        }
      }

      // Prepare template data exactly like automated-report controller
      const templateData = {
        isPdf: false,
        reportData: {
          ...reportData,
          report_id: report.id,
          report_title: reportData?.report_title || report.report_title,
          company_name: reportData?.company_name || report.company_name,
          document_number:
            reportData?.document_number || report.document_number,
          version_number:
            reportData?.version_number || report.version_number || 1,
          current_date:
            reportData?.current_date ||
            new Date().toLocaleDateString("en-GB", {
              day: "2-digit",
              month: "2-digit",
              year: "numeric"
            }),
          revision_date:
            reportData?.revision_date || report.revision_date || "",
          prepared_by: reportData?.prepared_by || report.prepared_by || "",
          reviewed_by: reportData?.reviewed_by || report.reviewed_by || "",
          approved_by: reportData?.approved_by || report.approved_by || "",
          test_lead: reportData?.test_lead || report.test_lead || "",
          version_description:
            reportData?.version_description || report.version_description || "",
          executive_summary:
            reportData?.executive_summary || report.executive_summary || "",
          key_findings: reportData?.key_findings || report.key_findings || "",
          scope: reportData?.scope || report.scope || "",
          methodology: reportData?.methodology || report.methodology || "",
          findings: reportData?.findings || report.findings || "",
          recommendations:
            reportData?.recommendations || report.recommendations || "",
          recommendations_list:
            reportData?.recommendations_list ||
            report.recommendations_list ||
            [],
          conclusion: reportData?.conclusion || report.conclusion || "",
          disclaimer: reportData?.disclaimer || report.disclaimer || "",
          target_details:
            reportData?.target_details || report.target_details || [],
          reports_list: reportData?.reports_list || report.reports_list || [],
          open_close_counts_by_severity:
            reportData?.open_close_counts_by_severity ||
            report.open_close_counts_by_severity ||
            {},
          total_open: reportData?.total_open || report.total_open || 0,
          total_closed: reportData?.total_closed || report.total_closed || 0,
          total_findings:
            reportData?.total_findings || report.total_findings || 0,
          date_of_request:
            reportData?.date_of_request || report.date_of_request || "",
          detailed_findings:
            reportData?.detailed_findings || report.detailed_findings || []
        }
      };

      // Render HTML for preview using the same template as automated-report
      const html = await new Promise<string>((resolve, reject) => {
        res.render("report-template", templateData, (err, html) => {
          if (err) reject(err);
          else resolve(html);
        });
      });

      // Return the rendered HTML directly (not wrapped in JSON)
      return res.send(html);
    } catch (error) {
      console.error("Error generating report preview:", error);
      res.status(500).json({
        status: "error",
        message: error.message || "Failed to generate report preview"
      });
    }
  }

  /**
   * Delete a program report
   */
  static async deleteReport(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.user_id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated"
        });
      }

      await ProgramReportService.deleteReport(id);

      res.status(200).json({
        success: true,
        message: "Program report deleted successfully"
      });
    } catch (error) {
      console.error("Error deleting program report:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to delete program report"
      });
    }
  }

  /**
   * Get reports by automated report ID
   */
  static async getReportsByAutomatedId(req: Request, res: Response) {
    try {
      const { automatedReportId } = req.params;
      const user = (req as any).user;
      const userRole = user?.role || "user";
      const businessId = user?.user_id; // For business users, their user_id is their business_id

      // For business users, only show their own reports
      // For admin roles, show all reports
      const reports = await ProgramReportService.getReportsByAutomatedId(
        automatedReportId,
        userRole === "BUSINESS" ? businessId : undefined
      );

      res.status(200).json({
        success: true,
        data: reports
      });
    } catch (error) {
      console.error("Error getting reports by automated ID:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to get reports by automated ID"
      });
    }
  }

  /**
   * Get report statistics
   */
  static async getReportStatistics(req: Request, res: Response) {
    try {
      const user = (req as any).user;
      const userId = user?.user_id;
      const userRole = user?.role || "user";
      const businessId = user?.user_id; // For business users, their user_id is their business_id

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated"
        });
      }

      // For business users, only show statistics for their own reports
      // For admin roles, show statistics for all reports
      const statistics = await ProgramReportService.getReportStatistics(
        userRole === "BUSINESS" ? businessId : undefined
      );

      res.status(200).json({
        success: true,
        data: statistics
      });
    } catch (error) {
      console.error("Error getting report statistics:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to get report statistics"
      });
    }
  }

  /**
   * Create report from automated report data
   */
  static async createFromAutomatedReport(req: Request, res: Response) {
    try {
      const { automatedReportId } = req.params;
      const userId = (req as any).user?.user_id;
      const role = (req as any).user?.role || "user";

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated"
        });
      }

      const reportData = req.body;

      const report = await ProgramReportService.createFromAutomatedReport(
        automatedReportId,
        userId,
        role,
        reportData
      );

      res.status(201).json({
        success: true,
        data: report,
        message: "Program report created from automated report successfully"
      });
    } catch (error) {
      console.error("Error creating report from automated data:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to create report from automated data"
      });
    }
  }

  /**
   * Get reports that need review
   */
  static async getReportsForReview(req: Request, res: Response) {
    try {
      const user = (req as any).user;
      const userId = user?.user_id;
      const userRole = user?.role || "user";
      const businessId = user?.user_id; // For business users, their user_id is their business_id

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated"
        });
      }

      // For business users, only show their own reports for review
      // For admin roles, show all reports for review
      const reports = await ProgramReportService.getReportsForReview(
        userRole === "BUSINESS" ? businessId : undefined
      );

      res.status(200).json({
        success: true,
        data: reports
      });
    } catch (error) {
      console.error("Error getting reports for review:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to get reports for review"
      });
    }
  }

  /**
   * Bulk update report statuses
   */
  static async bulkUpdateStatus(req: Request, res: Response) {
    try {
      const { reportIds, status } = req.body;
      const userId = (req as any).user?.user_id;
      const role = (req as any).user?.role || "user";

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated"
        });
      }

      if (!reportIds || !Array.isArray(reportIds) || reportIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: "Report IDs array is required"
        });
      }

      if (
        !status ||
        ![
          "draft",
          "qa_review",
          "admin_review",
          "approved",
          "rejected"
        ].includes(status)
      ) {
        return res.status(400).json({
          success: false,
          message:
            "Invalid status. Must be one of: draft, qa_review, admin_review, approved, rejected"
        });
      }

      const updatedReports = [];
      const errors = [];

      for (const reportId of reportIds) {
        try {
          const report = await ProgramReportService.updateStatus(
            reportId,
            status as ProgramReportStatus,
            userId,
            role
          );
          updatedReports.push(report);
        } catch (error) {
          errors.push({ reportId, error: error.message });
        }
      }

      res.status(200).json({
        success: true,
        data: {
          updatedReports,
          errors,
          totalUpdated: updatedReports.length,
          totalErrors: errors.length
        },
        message: `Updated ${updatedReports.length} reports to ${status}`
      });
    } catch (error) {
      console.error("Error bulk updating report statuses:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to bulk update report statuses"
      });
    }
  }

  /**
   * Get change log for a program report (filtered by user role)
   */
  static async getChangeLog(req: Request, res: Response) {
    try {
      const { id } = req.params; // This could be automated_report id or program_report id
      const user = (req as any).user;
      if (!user) {
        return res
          .status(401)
          .json({ success: false, message: "User not authenticated" });
      }
      const role = user.role;
      const allowedRoles = ["ADMIN", "SUB_ADMIN", "ADMIN_MANAGER", "QA"];
      let roleStr = "";
      if (typeof role === "number") {
        const ROLE_MAP = {
          1: "RESEARCHER",
          2: "BUSINESS",
          3: "ADMIN",
          4: "QA",
          5: "ADMIN_MANAGER",
          6: "DEVELOPER",
          7: "BUSINESS_MANAGER",
          8: "BUSINESS_ADMINISTRATOR",
          9: "SUB_ADMIN"
        };
        roleStr = ROLE_MAP[role] || "";
      } else if (typeof role === "string") {
        roleStr = role;
      }
      if (!allowedRoles.includes(roleStr.toUpperCase())) {
        return res.status(403).json({
          success: false,
          message: "Not authorized to view change log"
        });
      }
      // If the id is not a program_report id, try to find the program_report by automated_report id
      const { ProgramReport } = require("../models/program_report.model");
      let programReportId = id;
      // Try to find a program_report with this id; if not found, try as automated_report_id
      let programReport = await ProgramReport.findOne({ where: { id } });
      if (!programReport) {
        // Try as automated_report_id
        programReport = await ProgramReport.findOne({
          where: { automated_report_id: id }
        });
        if (programReport) {
          programReportId = programReport.id;
        }
      }
      // If still not found, return empty
      if (!programReport && !programReportId) {
        return res.json({ success: true, data: [] });
      }
      // Only show logs according to the rules
      let visibilityFilter: any = {};
      if (roleStr.toUpperCase() === "QA") {
        visibilityFilter = { visibility: "qa_admin" };
      } else if (
        ["ADMIN", "SUB_ADMIN", "ADMIN_MANAGER"].includes(roleStr.toUpperCase())
      ) {
        visibilityFilter = {};
      }
      const logs = await ProgramReportChangeLog.findAll({
        where: {
          program_report_id: programReportId,
          ...visibilityFilter
        },
        order: [["created_at", "DESC"]]
      });
      // Only include logs where old_value and new_value are both not null and different
      // Exclude logs where old_value is null and new_value is empty, whitespace, or '""'
      const filteredLogs = logs.filter(log => {
        const oldVal = log.old_value;
        const newVal = log.new_value;
        // Exclude if old is null, 'null', or '""', and new is null, empty, whitespace, or '""'
        const isOldNullish =
          oldVal === null ||
          oldVal === "null" ||
          (typeof oldVal === "string" && oldVal.trim() === '""');
        const isNewNullish =
          newVal === null ||
          (typeof newVal === "string" &&
            (newVal.trim() === "" || newVal.trim() === '""'));
        if (isOldNullish && isNewNullish) {
          return false;
        }
        // Only include if both not null and different
        if (oldVal !== null && newVal !== null && oldVal !== newVal) {
          return true;
        }
        // Also include if oldVal is not null and newVal is null or empty/whitespace (i.e., value was cleared)
        if (
          oldVal !== null &&
          (newVal === null ||
            (typeof newVal === "string" && newVal.trim() === ""))
        ) {
          return true;
        }
        return false;
      });
      // Final sanitize: remove any log where old_value is null, 'null', or '""', and new_value is null, empty, whitespace, or '""'
      const sanitizedLogs = filteredLogs.filter(log => {
        const isOldNullish =
          log.old_value === null ||
          log.old_value === "null" ||
          (typeof log.old_value === "string" && log.old_value.trim() === '""');
        const isNewNullish =
          log.new_value === null ||
          (typeof log.new_value === "string" &&
            (log.new_value.trim() === "" || log.new_value.trim() === '""'));
        return !(isOldNullish && isNewNullish);
      });
      return res.json({ success: true, data: sanitizedLogs });
    } catch (err) {
      return res.status(500).json({
        success: false,
        message: "Error fetching change log",
        error: err
      });
    }
  }

  // Helper: is user admin
  static isAdmin(user: any) {
    const adminRoles = ["ADMIN", "SUB_ADMIN", "ADMIN_MANAGER"];
    let roleStr = "";
    if (typeof user.role === "number") {
      const ROLE_MAP = {
        1: "RESEARCHER",
        2: "BUSINESS",
        3: "ADMIN",
        4: "QA",
        5: "ADMIN_MANAGER",
        6: "DEVELOPER",
        7: "BUSINESS_MANAGER",
        8: "BUSINESS_ADMINISTRATOR",
        9: "SUB_ADMIN"
      };
      roleStr = ROLE_MAP[user.role] || "";
    } else if (typeof user.role === "string") {
      roleStr = user.role;
    }
    return adminRoles.includes(roleStr.toUpperCase());
  }

  // Helper: is user QA
  static isQA(user: any) {
    let roleStr = "";
    if (typeof user.role === "number") {
      const ROLE_MAP = {
        1: "RESEARCHER",
        2: "BUSINESS",
        3: "ADMIN",
        4: "QA",
        5: "ADMIN_MANAGER",
        6: "DEVELOPER",
        7: "BUSINESS_MANAGER",
        8: "BUSINESS_ADMINISTRATOR",
        9: "SUB_ADMIN"
      };
      roleStr = ROLE_MAP[user.role] || "";
    } else if (typeof user.role === "string") {
      roleStr = user.role;
    }
    return roleStr.toUpperCase() === "QA";
  }

  // Helper: is user business
  static isBusiness(user: any) {
    let roleStr = "";
    if (typeof user.role === "number") {
      const ROLE_MAP = {
        1: "RESEARCHER",
        2: "BUSINESS",
        3: "ADMIN",
        4: "QA",
        5: "ADMIN_MANAGER",
        6: "DEVELOPER",
        7: "BUSINESS_MANAGER",
        8: "BUSINESS_ADMINISTRATOR",
        9: "SUB_ADMIN"
      };
      roleStr = ROLE_MAP[user.role] || "";
    } else if (typeof user.role === "string") {
      roleStr = user.role;
    }
    return roleStr.toUpperCase() === "BUSINESS";
  }

  // Helper: get program report and check assignments
  static async getReportAndCheckAssignment(reportId: string, user: any) {
    // Use the correct model name 'program_reports'
    const report = await database.program_reports.findOne({
      where: { id: reportId }
    });
    if (!report) return { report: null, isBusinessOwner: false };
    // Business owner: user_id matches
    const isBusinessOwner = report.user_id === user.user_id;
    return { report, isBusinessOwner };
  }

  static async getReportIdFromParam(id: string) {
    // Try to find by program_report id first
    let report = await database.program_reports.findOne({ where: { id } });
    if (report) return report;
    // If not found, try as automated_report_id
    report = await database.program_reports.findOne({
      where: { automated_report_id: id }
    });
    return report;
  }

  /**
   * Get chat messages for a program report and chat type
   */
  static async getChatMessages(req: Request, res: Response) {
    try {
      const { id, chatType } = req.params;
      const user = (req as any).user;
      const chatTypeTyped = chatType as
        | "admin_business"
        | "admin_qa"
        | "admin_subadmin";
      if (
        !["admin_business", "admin_qa", "admin_subadmin"].includes(
          chatTypeTyped
        )
      ) {
        return res
          .status(400)
          .json({ success: false, message: "Invalid chat type" });
      }
      if (!user || !user.user_id) {
        return res
          .status(401)
          .json({ success: false, message: "User not authenticated" });
      }
      // Try to resolve report by id (program_report.id or automated_report.id)
      let report = await ProgramReportController.getReportIdFromParam(id);
      let resolvedBy = report
        ? report.id === id
          ? "program_report.id"
          : "automated_report.id"
        : "not found";
      let isBusinessOwner = false;
      let businessCheckReason = "";
      const isBusiness = ProgramReportController.isBusiness(user);
      if (isBusiness) {
        // Try to find the automated report by id (param or from program_report)
        let automatedReport = null;
        if (report) {
          automatedReport = await database.automated_reports.findOne({
            where: { id: report.automated_report_id }
          });
        } else {
          automatedReport = await database.automated_reports.findOne({
            where: { id }
          });
        }
        if (!automatedReport) {
          businessCheckReason = "No automated_report found for id";
        } else {
          // Add logging for automated report details
          console.log("[AutomatedReport]", {
            automatedReport_id: automatedReport.id,
            automatedReport_business_id: automatedReport.business_id,
            currentUser_id: user.user_id
          });
          // Find the business user by business_id
          const businessUser = await database.user.findOne({
            where: { user_id: automatedReport.business_id }
          });
          let roleStr = "";
          if (businessUser) {
            if (typeof businessUser.role === "number") {
              const ROLE_MAP = {
                1: "RESEARCHER",
                2: "BUSINESS",
                3: "ADMIN",
                4: "QA",
                5: "ADMIN_MANAGER",
                6: "DEVELOPER",
                7: "BUSINESS_MANAGER",
                8: "BUSINESS_ADMINISTRATOR",
                9: "SUB_ADMIN"
              };
              roleStr = ROLE_MAP[businessUser.role] || "";
            } else if (typeof businessUser.role === "string") {
              roleStr = businessUser.role;
            }
            isBusinessOwner =
              businessUser.user_id === user.user_id &&
              roleStr.toUpperCase() === "BUSINESS";
            if (!isBusinessOwner) {
              businessCheckReason = `User is not business owner (expected user_id ${businessUser.user_id}, got ${user.user_id}; expected role BUSINESS, got ${roleStr})`;
            }
            // Add detailed logging for debugging
            console.log("[BusinessOwnerCheck]", {
              businessUser_id: businessUser.user_id,
              currentUser_id: user.user_id,
              businessUser_role: businessUser.role,
              businessUser_roleStr: roleStr,
              isBusinessOwner,
              businessCheckReason
            });
          } else {
            businessCheckReason =
              "No business user found for automated_report.business_id";
          }
        }
        // If no program_report exists for this automated_report, create it on the fly
        if (!report && automatedReport && isBusinessOwner) {
          // Create a minimal program_report
          const ProgramReport = database.program_reports;
          report = await ProgramReport.create({
            user_id: user.user_id,
            role: "BUSINESS",
            automated_report_id: automatedReport.id,
            status: "draft",
            company_name: automatedReport.company_name || "Unknown Company",
            report_title: automatedReport.title || "Untitled Report",
            version_number: "V1",
            document_number: `DOC-${Date.now()}`,
            current_date: new Date().toISOString().split("T")[0],
            revision_date: new Date().toISOString().split("T")[0],
            executive_summary: "",
            key_findings: "",
            scope: "",
            methodology: "",
            findings: "",
            recommendations: "",
            recommendations_list: [],
            conclusion: "",
            disclaimer: "",
            program_details: [],
            target_details: [],
            severity_counts: {},
            status_counts: {},
            open_close_counts_by_severity: {},
            total_open: 0,
            total_closed: 0,
            total_findings: 0,
            reports_list: [],
            detailed_findings: [],
            branding_logo:
              "https://i.postimg.cc/QVPV9d0x/Full-name-logo-blue.png",
            branding_company: "Capture The Bug Ltd."
          });
          resolvedBy = "created program_report for automated_report.id";
        }
      } else {
        isBusinessOwner =
          report &&
          report.user_id === user.user_id &&
          ProgramReportController.isBusiness({ role: report.role });
        if (!isBusinessOwner && report) {
          businessCheckReason = `User is not business owner (expected user_id ${report.user_id}, got ${user.user_id}; expected role BUSINESS, got ${report.role})`;
        }
      }
      // Access rules
      let accessGranted = false;
      let canSend = false;
      let accessReason = "";
      if (chatTypeTyped === "admin_business") {
        if (ProgramReportController.isAdmin(user) || isBusinessOwner) {
          accessGranted = true;
          canSend = true;
        } else if (ProgramReportController.isQA(user)) {
          accessGranted = true; // QA can view
          canSend = false;
        } else {
          accessReason = isBusiness
            ? businessCheckReason
            : "Not admin or business owner";
        }
      } else if (chatTypeTyped === "admin_qa") {
        if (
          ProgramReportController.isAdmin(user) ||
          ProgramReportController.isQA(user)
        ) {
          accessGranted = true;
          canSend = true;
        } else if (ProgramReportController.isBusiness(user)) {
          accessGranted = true; // Business can view
          canSend = false;
        } else {
          accessReason = "Not admin or QA";
        }
      } else if (chatTypeTyped === "admin_subadmin") {
        if (ProgramReportController.isAdmin(user)) {
          accessGranted = true;
          canSend = true;
        } else {
          accessReason = "Not admin or subadmin";
        }
      }
      // Logging for debugging
      console.log("[ChatAccess] getChatMessages", {
        user_id: user.user_id,
        role: user.role,
        chatType: chatTypeTyped,
        id_param: id,
        resolvedBy,
        report_id: report ? report.id : null,
        isBusinessOwner,
        accessGranted,
        canSend,
        accessReason
      });
      if (!accessGranted) {
        return res.status(403).json({
          success: false,
          message: `Not authorized for this chat. Reason: ${accessReason}`
        });
      }
      if (!report) {
        return res.status(404).json({
          success: false,
          message: "Report not found (after resolution/creation)"
        });
      }
      // Fetch messages without association
      const messages = await ProgramReportChatMessage.findAll({
        where: { program_report_id: report.id, chat_type: chatTypeTyped },
        order: [["created_at", "ASC"]]
      });
      // Manually fetch sender usernames
      const userIds = [...new Set(messages.map(msg => msg.sender_id))];
      let userMap = {};
      if (userIds.length > 0) {
        const users = await database.user.findAll({
          where: { user_id: { [Op.in]: userIds } },
          attributes: ["user_id", "username"]
        });
        userMap = users.reduce(
          (acc, user) => {
            acc[String(user.get("user_id"))] = user.get("username");
            return acc;
          },
          {} as Record<string, string>
        );
      }
      const result = messages.map(msg => ({
        id: msg.id,
        sender_id: msg.sender_id,
        sender_username: userMap[String(msg.sender_id)] || "",
        message: msg.message,
        created_at: msg.created_at
      }));
      return res.json({ success: true, data: result, canSend });
    } catch (error) {
      console.error("Error fetching chat messages:", error);
      return res.status(500).json({
        success: false,
        message: error.message || "Failed to fetch chat messages"
      });
    }
  }

  /**
   * Post a chat message for a program report and chat type
   */
  static async postChatMessage(req: Request, res: Response) {
    try {
      const { id, chatType } = req.params;
      const { message } = req.body;
      const user = (req as any).user;
      if (!user || !user.user_id) {
        return res
          .status(401)
          .json({ success: false, message: "User not authenticated" });
      }
      const chatTypeTyped = chatType as
        | "admin_business"
        | "admin_qa"
        | "admin_subadmin";
      if (
        !["admin_business", "admin_qa", "admin_subadmin"].includes(
          chatTypeTyped
        )
      ) {
        return res
          .status(400)
          .json({ success: false, message: "Invalid chat type" });
      }
      if (!message || typeof message !== "string" || !message.trim()) {
        return res
          .status(400)
          .json({ success: false, message: "Message is required" });
      }
      // Try to resolve report by id (program_report.id or automated_report.id)
      let report = await ProgramReportController.getReportIdFromParam(id);
      let resolvedBy = report
        ? report.id === id
          ? "program_report.id"
          : "automated_report.id"
        : "not found";
      let isBusinessOwner = false;
      let businessCheckReason = "";
      const isBusiness = ProgramReportController.isBusiness(user);
      if (isBusiness) {
        // Try to find the automated report by id (param or from program_report)
        let automatedReport = null;
        if (report) {
          automatedReport = await database.automated_reports.findOne({
            where: { id: report.automated_report_id }
          });
        } else {
          automatedReport = await database.automated_reports.findOne({
            where: { id }
          });
        }
        if (!automatedReport) {
          businessCheckReason = "No automated_report found for id";
        } else {
          // Add logging for automated report details
          console.log("[AutomatedReport]", {
            automatedReport_id: automatedReport.id,
            automatedReport_business_id: automatedReport.business_id,
            currentUser_id: user.user_id
          });
          // Find the business user by business_id
          const businessUser = await database.user.findOne({
            where: { user_id: automatedReport.business_id }
          });
          let roleStr = "";
          if (businessUser) {
            if (typeof businessUser.role === "number") {
              const ROLE_MAP = {
                1: "RESEARCHER",
                2: "BUSINESS",
                3: "ADMIN",
                4: "QA",
                5: "ADMIN_MANAGER",
                6: "DEVELOPER",
                7: "BUSINESS_MANAGER",
                8: "BUSINESS_ADMINISTRATOR",
                9: "SUB_ADMIN"
              };
              roleStr = ROLE_MAP[businessUser.role] || "";
            } else if (typeof businessUser.role === "string") {
              roleStr = businessUser.role;
            }
            isBusinessOwner =
              businessUser.user_id === user.user_id &&
              roleStr.toUpperCase() === "BUSINESS";
            if (!isBusinessOwner) {
              businessCheckReason = `User is not business owner (expected user_id ${businessUser.user_id}, got ${user.user_id}; expected role BUSINESS, got ${roleStr})`;
            }
            // Add detailed logging for debugging
            console.log("[BusinessOwnerCheck]", {
              businessUser_id: businessUser.user_id,
              currentUser_id: user.user_id,
              businessUser_role: businessUser.role,
              businessUser_roleStr: roleStr,
              isBusinessOwner,
              businessCheckReason
            });
          } else {
            businessCheckReason =
              "No business user found for automated_report.business_id";
          }
        }
        // If no program_report exists for this automated_report, create it on the fly
        if (!report && automatedReport && isBusinessOwner) {
          // Create a minimal program_report
          const ProgramReport = database.program_reports;
          report = await ProgramReport.create({
            user_id: user.user_id,
            role: "BUSINESS",
            automated_report_id: automatedReport.id,
            status: "draft",
            company_name: automatedReport.company_name || "Unknown Company",
            report_title: automatedReport.title || "Untitled Report",
            version_number: "V1",
            document_number: `DOC-${Date.now()}`,
            current_date: new Date().toISOString().split("T")[0],
            revision_date: new Date().toISOString().split("T")[0],
            executive_summary: "",
            key_findings: "",
            scope: "",
            methodology: "",
            findings: "",
            recommendations: "",
            recommendations_list: [],
            conclusion: "",
            disclaimer: "",
            program_details: [],
            target_details: [],
            severity_counts: {},
            status_counts: {},
            open_close_counts_by_severity: {},
            total_open: 0,
            total_closed: 0,
            total_findings: 0,
            reports_list: [],
            detailed_findings: [],
            branding_logo:
              "https://i.postimg.cc/QVPV9d0x/Full-name-logo-blue.png",
            branding_company: "Capture The Bug Ltd."
          });
          resolvedBy = "created program_report for automated_report.id";
        }
      } else {
        isBusinessOwner =
          report &&
          report.user_id === user.user_id &&
          ProgramReportController.isBusiness({ role: report.role });
        if (!isBusinessOwner && report) {
          businessCheckReason = `User is not business owner (expected user_id ${report.user_id}, got ${user.user_id}; expected role BUSINESS, got ${report.role})`;
        }
      }
      // Access rules for sending
      let accessGranted = false;
      let accessReason = "";
      if (chatTypeTyped === "admin_business") {
        if (ProgramReportController.isAdmin(user) || isBusinessOwner) {
          accessGranted = true;
        } else {
          accessReason = isBusiness
            ? businessCheckReason
            : "Not admin or business owner";
        }
      } else if (chatTypeTyped === "admin_qa") {
        if (
          ProgramReportController.isAdmin(user) ||
          ProgramReportController.isQA(user)
        ) {
          accessGranted = true;
        } else {
          accessReason = "Not admin or QA";
        }
      } else if (chatTypeTyped === "admin_subadmin") {
        if (ProgramReportController.isAdmin(user)) {
          accessGranted = true;
        } else {
          accessReason = "Not admin or subadmin";
        }
      }
      // Logging for debugging
      console.log("[ChatAccess] postChatMessage", {
        user_id: user.user_id,
        role: user.role,
        chatType: chatTypeTyped,
        id_param: id,
        resolvedBy,
        report_id: report ? report.id : null,
        isBusinessOwner,
        accessGranted,
        accessReason
      });
      if (!accessGranted) {
        return res.status(403).json({
          success: false,
          message: `Not authorized to send message in this chat. Reason: ${accessReason}`
        });
      }
      if (!report) {
        return res.status(404).json({
          success: false,
          message: "Report not found (after resolution/creation)"
        });
      }
      const newMsg = await ProgramReportChatMessage.create({
        program_report_id: report.id,
        chat_type: chatTypeTyped,
        sender_id: user.user_id,
        message: message.trim(),
        created_at: new Date()
      });
      // Fetch sender username
      const sender = await database.user.findByPk(user.user_id);
      // Send email notification to ADMIN/SUBADMIN if business user (in background)
      if (ProgramReportController.isBusiness(user)) {
        setImmediate(async () => {
          try {
            const admins = await database.user.findAll({
              where: { role: [UserRole.ADMIN, UserRole.SUB_ADMIN] },
              attributes: ["email"]
            });
            const adminEmails = admins.map(a => a.email).filter(Boolean);
            if (adminEmails.length === 0) {
              console.warn(
                "No ADMIN or SUB_ADMIN users found for business chat notification"
              );
            }
            // Fetch program/report context for email
            let programTitle = "";
            let reportTitle = "";
            if (report) {
              reportTitle = report.report_title || "";
              if (report.program_details && report.program_details.length > 0) {
                programTitle = report.program_details[0].program_title || "";
              }
            }
            const subject = `New Chat Message from Business User`;
            const html = `
              <html><body style=\"font-family:Inter,Arial,sans-serif;background:#f9f9f9;padding:0;margin:0;\">
                <div style=\"max-width:480px;margin:40px auto;background:#fff;border-radius:16px;border:1px solid #e0e0e0;box-shadow:0 2px 8px #0001;overflow:hidden;\">
                  <div style=\"padding:32px 32px 16px 32px;text-align:center;\">
                    <img src=\"https://c8270c56-7594-4100-9b34-7589297588fa.b-cdn.net/e/bac89f4c-a951-463b-81dd-86c0e12c3427/28695d5f-3f29-41fa-a53d-2f93062026e6.jpeg\" alt=\"Capture The Bug\" style=\"height:48px;margin-bottom:24px;\"/>
                    <h1 style=\"font-size:20px;color:#111;margin:0 0 12px 0;\">New Chat Message from Business User</h1>
                    <table style=\"width:100%;margin:0 auto 24px auto;font-size:15px;color:#222;text-align:left;\">
                      <tr><td style=\"padding:4px 0;font-weight:600;\">Program:</td><td style=\"padding:4px 0;\">${programTitle}</td></tr>
                      <tr><td style=\"padding:4px 0;font-weight:600;\">Report:</td><td style=\"padding:4px 0;\">${reportTitle}</td></tr>
                      <tr><td style=\"padding:4px 0;font-weight:600;\">Sender:</td><td style=\"padding:4px 0;\">${
                        sender?.username ||
                        sender?.display_name ||
                        sender?.email ||
                        user.user_id
                      }</td></tr>
                    </table>
                    <div style=\"margin:24px 0 0 0;font-size:15px;color:#555;text-align:left;\"><b>Message:</b><br>${message.trim()}</div>
                  </div>
                  <div style=\"background:#f5f5f5;padding:16px 32px;text-align:center;font-size:13px;color:#888;\">Capture The Bug Platform</div>
                </div>
              </body></html>
            `;
            for (const admin of admins) {
              if (admin.email) {
                try {
                  await sendEmail(admin.email, subject, html);
                  console.log("Sent business chat email to", admin.email);
                } catch (e) {
                  console.error(
                    "Failed to send business chat email to",
                    admin.email,
                    e
                  );
                }
              }
            }
          } catch (err) {
            console.error(
              "Error in background business chat email notification:",
              err
            );
          }
        });
      }
      // Send email notification to business owner if ADMIN/SUBADMIN sends a message (in background)
      if (user.role === UserRole.ADMIN || user.role === UserRole.SUB_ADMIN) {
        setImmediate(async () => {
          try {
            // Find business user for this report
            let businessUser = null;
            if (report && report.automated_report_id) {
              const autoReport = await database.automated_reports.findOne({
                where: { id: report.automated_report_id }
              });
              if (autoReport && autoReport.business_id) {
                businessUser = await database.user.findOne({
                  where: { user_id: autoReport.business_id }
                });
              }
            }
            if (!businessUser || !businessUser.email) {
              console.warn(
                "No business user found for admin chat notification"
              );
              return;
            }
            // Fetch program/report context for email
            let programTitle = "";
            let reportTitle = "";
            if (report) {
              reportTitle = report.report_title || "";
              if (report.program_details && report.program_details.length > 0) {
                programTitle = report.program_details[0].program_title || "";
              }
            }
            const subject = `New Chat Message from Admin`;
            const html = `
              <html><body style=\"font-family:Inter,Arial,sans-serif;background:#f9f9f9;padding:0;margin:0;\">
                <div style=\"max-width:480px;margin:40px auto;background:#fff;border-radius:16px;border:1px solid #e0e0e0;box-shadow:0 2px 8px #0001;overflow:hidden;\">
                  <div style=\"padding:32px 32px 16px 32px;text-align:center;\">
                    <img src=\"https://c8270c56-7594-4100-9b34-7589297588fa.b-cdn.net/e/bac89f4c-a951-463b-81dd-86c0e12c3427/28695d5f-3f29-41fa-a53d-2f93062026e6.jpeg\" alt=\"Capture The Bug\" style=\"height:48px;margin-bottom:24px;\"/>
                    <h1 style=\"font-size:20px;color:#111;margin:0 0 12px 0;\">New Chat Message from Admin</h1>
                    <table style=\"width:100%;margin:0 auto 24px auto;font-size:15px;color:#222;text-align:left;\">
                      <tr><td style=\"padding:4px 0;font-weight:600;\">Program:</td><td style=\"padding:4px 0;\">${programTitle}</td></tr>
                      <tr><td style=\"padding:4px 0;font-weight:600;\">Report:</td><td style=\"padding:4px 0;\">${reportTitle}</td></tr>
                      <tr><td style=\"padding:4px 0;font-weight:600;\">Sender:</td><td style=\"padding:4px 0;\">${
                        sender?.username ||
                        sender?.display_name ||
                        sender?.email ||
                        user.user_id
                      }</td></tr>
                    </table>
                    <div style=\"margin:24px 0 0 0;font-size:15px;color:#555;text-align:left;\"><b>Message:</b><br>${message.trim()}</div>
                  </div>
                  <div style=\"background:#f5f5f5;padding:16px 32px;text-align:center;font-size:13px;color:#888;\">Capture The Bug Platform</div>
                </div>
              </body></html>
            `;
            try {
              await sendEmail(businessUser.email, subject, html);
              console.log(
                "Sent admin chat email to business user",
                businessUser.email
              );
            } catch (e) {
              console.error(
                "Failed to send admin chat email to business user",
                businessUser.email,
                e
              );
            }
          } catch (err) {
            console.error(
              "Error in background admin chat email notification:",
              err
            );
          }
        });
      }
      return res.status(201).json({
        success: true,
        data: {
          id: newMsg.id,
          sender_id: newMsg.sender_id,
          sender_username: sender?.username || "",
          message: newMsg.message,
          created_at: newMsg.created_at
        }
      });
    } catch (error) {
      console.error("Error posting chat message:", error);
      return res.status(500).json({
        success: false,
        message: error.message || "Failed to post chat message"
      });
    }
  }
}
