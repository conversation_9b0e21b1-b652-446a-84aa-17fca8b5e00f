import { useState } from "react";
import {
  ReportFilters,
  useGetReportsQuery
} from "../../api/endpoints/reportsApi";

const useReports = (initialFilters: ReportFilters) => {
  const [filters, setFilters] = useState<ReportFilters>(initialFilters);
  const { data, isError, isLoading } = useGetReportsQuery(filters);

  const setPage = (page: number) =>
    setFilters(filters => ({
      ...filters,
      page
    }));

  return {
    reports: data?.reports || [],
    count: data?.results || 0,
    filters,
    isError,
    isLoading,
    setPage,
    setFilters
  };
};

export default useReports;
