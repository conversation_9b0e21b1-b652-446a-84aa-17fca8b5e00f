import React from 'react';
import { ReportData } from '../../types/report.types';

interface BrandingEditorProps {
  reportData: ReportData;
  onInputChange: (field: string, value: string) => void;
}

const DEFAULT_LOGO = 'https://i.postimg.cc/QVPV9d0x/Full-name-logo-blue.png';
const DEFAULT_COMPANY = 'Capture The Bug Ltd.';

const BrandingEditor: React.FC<BrandingEditorProps> = ({ reportData, onInputChange }) => {
  const brandingLogo = reportData.branding_logo || DEFAULT_LOGO;
  const brandingCompany = reportData.branding_company || DEFAULT_COMPANY;

  return (
    <div className="h-full overflow-y-auto">
      <div className="bg-white rounded-xl shadow-lg border border-slate-100 p-6 mb-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-1 h-8 bg-gradient-to-b from-blue-500 to-blue-700 rounded-full" />
          <div>
            <h3 className="text-base font-bold text-blue-900 tracking-tight">Branding</h3>
            <p className="text-xs text-blue-700/80 font-medium mt-0.5">Edit logo and company name for white labeling</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-2">
          <div className="flex flex-col gap-2">
            <label className="block text-xs font-medium text-gray-700 mb-0.5" htmlFor="branding_logo">
              Logo URL
            </label>
            <input
              id="branding_logo"
              type="text"
              value={brandingLogo}
              onChange={e => onInputChange('branding_logo', e.target.value)}
              className="w-full px-2.5 py-1.5 text-sm bg-white border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200 placeholder:text-slate-400"
              placeholder="Enter logo URL"
            />
            <div className="mt-2">
              <span className="block text-xs text-gray-500 mb-1">Logo Preview:</span>
              <div className="bg-gray-50 border border-slate-200 rounded-md p-2 flex items-center justify-center min-h-[60px]">
                <img
                  src={brandingLogo}
                  alt="Branding Logo Preview"
                  className="max-h-16 max-w-full object-contain"
                  onError={e => (e.currentTarget.src = DEFAULT_LOGO)}
                />
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-2">
            <label className="block text-xs font-medium text-gray-700 mb-0.5" htmlFor="branding_company">
              Company Name
            </label>
            <input
              id="branding_company"
              type="text"
              value={brandingCompany}
              onChange={e => onInputChange('branding_company', e.target.value)}
              className="w-full px-2.5 py-1.5 text-sm bg-white border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-200 placeholder:text-slate-400"
              placeholder="Enter company name"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BrandingEditor; 