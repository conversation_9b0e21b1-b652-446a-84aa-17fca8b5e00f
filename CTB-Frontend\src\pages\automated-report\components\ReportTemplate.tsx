import React, { useState } from 'react';
import { ReportData } from '../types/report.types';
import { PDFViewer } from '@react-pdf/renderer';
import ReportTemplatePDF from './ReportTemplatePDF';
import ChartImageGenerator from './ChartImageGenerator';

const ReportTemplate: React.FC<{ reportData: ReportData }> = ({ reportData }) => {
  const [pieChartImage, setPieChartImage] = useState<string | undefined>();
  const [barChartImage, setBarChartImage] = useState<string | undefined>();

  // Prepare data for charts
  const pieData = {
    labels: ['Critical', 'High', 'Medium', 'Low'],
    datasets: [{
      data: [
        reportData.open_close_counts_by_severity?.Critical?.Total || 0,
        reportData.open_close_counts_by_severity?.High?.Total || 0,
        reportData.open_close_counts_by_severity?.Medium?.Total || 0,
        reportData.open_close_counts_by_severity?.Low?.Total || 0,
      ],
      backgroundColor: ['#8b0000', '#ff4500', '#ffd700', '#32cd32'],
    }],
  };

  const barData = {
    labels: ['Critical', 'High', 'Medium', 'Low'],
    datasets: [{
      label: 'Number of Findings',
      data: [
        reportData.open_close_counts_by_severity?.Critical?.Total || 0,
        reportData.open_close_counts_by_severity?.High?.Total || 0,
        reportData.open_close_counts_by_severity?.Medium?.Total || 0,
        reportData.open_close_counts_by_severity?.Low?.Total || 0,
      ],
      backgroundColor: ['#8b0000', '#ff4500', '#ffd700', '#32cd32'],
    }],
  };

  // Only render PDF when both images are ready
  const chartsReady = pieChartImage && barChartImage;

  return (
    <div className="w-full h-full">
      {/* Hidden chart generators */}
      {!pieChartImage && (
        <ChartImageGenerator
          type="pie"
          data={pieData}
          onImageReady={setPieChartImage}
        />
      )}
      {!barChartImage && (
        <ChartImageGenerator
          type="bar"
          data={barData}
          options={{
            scales: { y: { beginAtZero: true, ticks: { precision: 0 } } },
            plugins: { legend: { display: false } },
          }}
          onImageReady={setBarChartImage}
        />
      )}

      {/* PDF Viewer */}
      {chartsReady && (
        <PDFViewer
          style={{
            width: '100%',
            height: '100vh',
            border: 'none'
          }}
        >
          <ReportTemplatePDF
            reportData={reportData}
            pieChartImage={pieChartImage}
            barChartImage={barChartImage}
          />
        </PDFViewer>
      )}
    </div>
  );
};

export default ReportTemplate; 