import { Op, Sequelize } from "sequelize";
import { Request, Response } from "express";
import { CTBQueryRequest, CTBRequest, CTBParamsRequest } from "../../server";
import { logger } from "../logger/index";
import { database } from "../models/db";
import { Transaction } from "sequelize";
import { performance } from "perf_hooks";
import * as crypto from "crypto";
import {
  severityOrder,
  SeverityTypes,
  NotificationMethod,
  AZURE_BLOB_STORAGE_REPORT_ATTACHMENTS_INLINE_NAME
} from "../utils/constants";
import { ReportState } from "./report.controller";
import { UserAttributes } from "../models/user.model";
import { getRecommendationsSummary } from "../utils/openAIModel";
import { UserRole } from "../utils/auth";
import { sendNotification } from "./notifications.controller";
import { NotificationType } from "../models/notifications.model";
import path from "path";
import fs from "fs";
import * as ejs from "ejs";
import { generateAndUploadPDF } from "../services/pdf.service";
import sendEmail from "../utils/send-email";

const Users = database.user;
const Programs = database.program;
const Reports = database.report;
const AutomatedReports = database.automated_reports;
const AutomatedReportVersions = database.automated_report_versions;
const RetestLog = database.retest;

export const generateDocumentNumber = (
  programId: number,
  domains: string[] | string = ["Web"],
  existingNumbers: string[] = []
): string => {
  // Get current date for MMYY format
  const now = new Date();
  const month = (now.getMonth() + 1).toString().padStart(2, "0");
  const year = now.getFullYear().toString().slice(-2);
  const dateCode = `${month}${year}`;

  const docType = "TECH";
  const department = "CYBER";

  // 🔍 Log raw domains input
  console.log("Raw domains input:", domains);

  // Normalize and deduplicate domain names
  const normalizedDomains = Array.isArray(domains)
    ? [
        ...new Set(
          domains.map(d => {
            const clean = d.charAt(0).toUpperCase() + d.slice(1).toLowerCase();
            return clean;
          })
        )
      ]
    : [domains.charAt(0).toUpperCase() + domains.slice(1).toLowerCase()];

  // 🔍 Log normalized + deduplicated domains
  console.log("Normalized and unique domains:", normalizedDomains);

  // Sort alphabetically for consistency
  const finalDomainPart = normalizedDomains.sort().join("-");

  // 🔍 Log final domain part to be added in document number
  console.log("Final domain part for doc number:", finalDomainPart);

  // Construct base document number
  const baseNumber = `CTB-${docType}-${department}-${dateCode}-${programId}`;

  // Find highest existing version number
  let highestVersion = 0;
  existingNumbers.forEach(number => {
    if (number.startsWith(baseNumber)) {
      const versionMatch = number.match(/-V\.(\d+)-/);
      if (versionMatch && versionMatch[1]) {
        const version = parseInt(versionMatch[1], 10);
        if (!isNaN(version) && version > highestVersion) {
          highestVersion = version;
        }
      }
    }
  });

  const nextVersion = (highestVersion + 1).toString().padStart(2, "0");

  const finalDocNumber = `${baseNumber}-V.${nextVersion}-${finalDomainPart}`;

  // 🔍 Log final generated document number
  console.log("Generated Document Number:", finalDocNumber);

  return finalDocNumber;
};

// Add transaction support to previewReport
export const previewReport = async (
  req: CTBParamsRequest<{ report_id: string }>,
  res: Response
) => {
  const startTime = performance.now();
  try {
    const { report_id } = req.params;
    const { user_id, role } = req.user as UserAttributes;
    const reportData = req.query.reportData
      ? JSON.parse(req.query.reportData as string)
      : {};

    logger.log({
      level: "info",
      label: "automated-report.controller | previewReport",
      message: `Previewing report ${report_id} for user ${user_id}`
    });

    // Get report data with a single query including all needed associations
    const report = (await AutomatedReports.findOne({
      where: { id: report_id },
      include: [
        {
          model: AutomatedReportVersions,
          as: "versions",
          required: false,
          order: [["version_number", "DESC"]],
          limit: 1
        }
      ]
    })) as any;

    if (!report) {
      return res.status(404).json({
        status: "error",
        message: "Report not found"
      });
    }

    // Prepare template data
    const templateData = {
      isPdf: false,
      reportData: {
        ...reportData,
        report_id: report.id,
        report_title: reportData.report_title || report.title,
        company_name: reportData.company_name || report.company_name,
        document_number: reportData.document_number || report.document_number,
        version_number:
          reportData.version_number ||
          report.versions?.[0]?.version_number ||
          1,
        current_date:
          reportData.current_date ||
          new Date().toLocaleDateString("en-GB", {
            day: "2-digit",
            month: "2-digit",
            year: "numeric"
          })
      }
    };

    // Render HTML for preview
    const html = await new Promise<string>((resolve, reject) => {
      res.render("report-template", templateData, (err, html) => {
        if (err) reject(err);
        else resolve(html);
      });
    });

    const endTime = performance.now();
    logger.log({
      level: "info",
      label: "automated-report.controller | previewReport",
      message: `Report preview completed in ${(endTime - startTime).toFixed(
        2
      )}ms`
    });

    // Return the rendered HTML directly
    return res.json({
      status: "success",
      previewHtml: html
    });
  } catch (error) {
    logger.log({
      level: "error",
      label: "automated-report.controller | previewReport",
      message: `Error previewing report: ${error.message}`
    });
    return res.status(500).json({
      status: "error",
      message: "500 INTERNAL ERROR: Unable to preview report"
    });
  }
};

// Direct data retrieval function to replace mock request/response pattern
async function getProgramReportsData(
  report_id: string,
  user: any,
  transaction?: Transaction
) {
  try {
    const { user_id, role } = user as UserAttributes;
    console.log("getProgramReportsData - Input:", { report_id, user_id, role });

    // Check authorization - only admin roles allowed
    const allowedRoles = [
      UserRole.ADMIN,
      UserRole.ADMIN_MANAGER,
      UserRole.SUB_ADMIN,
      UserRole.QA,
      UserRole.BUSINESS
    ];

    if (!allowedRoles.includes(role)) {
      console.log("getProgramReportsData - Unauthorized access:", {
        user_id,
        role
      });
      logger.log({
        level: "warn",
        label: "automated-report.controller | getProgramReportsData",
        message: `Unauthorized access attempt by user ${user_id} with role ${role}`
      });
      return null;
    }

    // Get automated report details with a single query
    const automatedReport = await AutomatedReports.findOne({
      where: { id: report_id },
      include: [
        {
          model: AutomatedReportVersions,
          as: "versions",
          attributes: ["version_number"]
        }
      ],
      transaction
    });

    console.log(
      "getProgramReportsData - Automated report:",
      JSON.stringify(automatedReport, null, 2)
    );

    if (!automatedReport) {
      console.log("getProgramReportsData - Report not found:", { report_id });
      logger.log({
        level: "error",
        label: "automated-report.controller | getProgramReportsData",
        message: `Automated report ${report_id} not found`
      });
      return null;
    }

    // Determine the version number
    const versionNumber =
      (automatedReport as any).versions?.length > 0
        ? (automatedReport as any).versions[0].version_number
        : 1; // Default to 1 if no versions exist
    const formattedVersionNumber = `V${versionNumber}`;

    // Parse program_ids
    const programIds = Array.isArray(automatedReport.program_ids)
      ? automatedReport.program_ids
      : JSON.parse(automatedReport.program_ids || "[]");

    // Input validation
    if (!Array.isArray(programIds) || programIds.length === 0) {
      logger.log({
        level: "warn",
        label: "automated-report.controller | getProgramReportsData",
        message: `Invalid program_ids in report ${report_id}`
      });
      return null;
    }

    // Add debug logging
    logger.log({
      level: "info",
      label: "automated-report.controller | getProgramReportsData",
      message: `Fetching programs for report ${report_id} with program IDs: ${programIds.join(
        ", "
      )}`
    });

    // Parallel database queries for better performance
    const [userPrograms, existingReports] = await Promise.all([
      // Get user programs - modified for admin access
      Programs.findAll({
        where: {
          program_id: { [Op.in]: programIds },
          ...(role !== UserRole.ADMIN && {
            user_id: automatedReport.business_id
          }) // Only check user_id for non-admin users
          // Removed is_delete and is_activated filters to allow inactive/deleted programs
        },
        attributes: [
          "program_id",
          "private_access_users",
          "targets",
          "testing_type",
          "expected_start_date",
          "expected_end_date",
          "program_title",
          "scope",
          "test_lead",
          "prepared_by",
          "reviewed_by",
          "approved_by"
        ],
        transaction
      }),

      // Get existing reports for document number generation
      AutomatedReports.findAll({
        where: Sequelize.literal(
          `JSON_CONTAINS(program_ids, '[${programIds[0]}]')`
        ),
        transaction
      })
    ]);

    // Add debug logging for program query results
    logger.log({
      level: "info",
      label: "automated-report.controller | getProgramReportsData",
      message: `Found ${userPrograms.length} programs for report ${report_id}`
    });

    if (userPrograms.length === 0) {
      logger.log({
        level: "warn",
        label: "automated-report.controller | getProgramReportsData",
        message: `No valid programs found for report ${report_id}`
      });
      return null;
    }

    const validProgramIds = userPrograms.map(program => program.program_id);

    // More parallel queries
    const [reports, reportIds, testerUsernames] = await Promise.all([
      // Get all reports
      Reports.findAll({
        where: {
          program_id: { [Op.in]: validProgramIds },
          state: {
            [Op.notIn]: [ReportState.UNDER_REVIEW, ReportState.DELETED]
          }
        },
        include: [
          {
            model: Programs,
            attributes: ["program_title", "scope", "targets"]
          }
        ],
        order: [["submitted_date", "ASC"]],
        transaction
      }),

      // Get report IDs for existing reports
      (async () => {
        const ids = existingReports.map(report => report.id);
        return ids.length > 0 ? ids : [0];
      })(),

      // Get all tester IDs from programs
      (async () => {
        const allTesterIds = new Set<number>();
        userPrograms.forEach(program => {
          try {
            let testerIds: number[] = [];
            if (program.private_access_users) {
              const parsed =
                typeof program.private_access_users === "string"
                  ? JSON.parse(program.private_access_users)
                  : program.private_access_users;
              testerIds = Array.isArray(parsed) ? (parsed as number[]) : [];
            }
            if (Array.isArray(testerIds)) {
              testerIds.forEach(id => {
                if (typeof id === "number") {
                  allTesterIds.add(id);
                }
              });
            }
          } catch (e) {
            logger.log({
              level: "error",
              label: "automated-report.controller | getProgramReportsData",
              message: `Error parsing private_access_users for program ${program.program_id}`
            });
          }
        });

        const testerIds = Array.from(allTesterIds);
        if (testerIds.length === 0) return [];

        return Users.findAll({
          where: {
            user_id: { [Op.in]: testerIds }
          },
          attributes: ["username", "user_id"],
          transaction
        });
      })()
    ]);

    // Get retest data and existing versions in parallel
    const [retestData, existingVersions] = await Promise.all([
      RetestLog.findAll({
        where: {
          report_id: {
            [Op.in]: reports.map(report => report.report_id)
          }
        },
        order: [["created_at", "DESC"]],
        transaction
      }),

      AutomatedReportVersions.findAll({
        where: {
          report_id: { [Op.in]: reportIds }
        },
        attributes: ["id", "pdf_url"],
        transaction
      })
    ]);

    // Process retest data
    const retestStatusByReportId = {};
    retestData.forEach(retest => {
      if (!retestStatusByReportId[retest.report_id]) {
        retestStatusByReportId[retest.report_id] = retest.status;
      }
    });

    // Extract document numbers from PDF URLs
    const existingDocNumbers = existingVersions
      .map(version => {
        if (version.pdf_url) {
          const match = version.pdf_url.match(
            /CTB-TECH-CYBER-\d{4}-\d+-V\.\d+-[A-Z]+/
          );
          return match ? match[0] : null;
        }
        return null;
      })
      .filter(Boolean);

    // Determine domains based on target types and testing type
    const domains = determineDomains(userPrograms);

    // Generate document number
    const documentNumber = generateDocumentNumber(
      programIds[0],
      domains,
      existingDocNumbers
    );

    // Process all fix fields for recommendation summary
    const allFixFields = reports
      .map(r => r.fix)
      .filter(Boolean)
      .map(f => String(f));

    // Create recommendations summary directly from the HTML content
    let recommendationsSummary = "";
    try {
      recommendationsSummary = allFixFields.join("\n\n");
    } catch (e) {
      logger.log({
        level: "error",
        label: "automated-report.controller | getProgramReportsData",
        message: "Failed to process recommendations content"
      });
    }

    // Process contributor details
    const contributorDetails = testerUsernames.map(user => ({
      user_id: user.user_id,
      username: user.username
    }));

    // Calculate severity counts
    const severitySummary = {
      Critical: reports.filter(
        r => r.severity_category === SeverityTypes.CRITICAL
      ).length,
      High: reports.filter(r => r.severity_category === SeverityTypes.HIGH)
        .length,
      Medium: reports.filter(r => r.severity_category === SeverityTypes.MEDIUM)
        .length,
      Low: reports.filter(r => r.severity_category === SeverityTypes.LOW)
        .length,
      Total: reports.length
    };
    const closedStatuses = ["Fix Verified", "Fix Approved"];

    // Calculate closed and open counts
    const closedCount = reports.filter(r =>
      closedStatuses.includes(retestStatusByReportId[r.report_id])
    ).length;

    const openClosedCounts = {
      Closed: closedCount,
      Open: reports.length - closedCount
    };

    // Per-severity open/closed counts
    const openCloseBySeverity = {
      Critical: { Open: 0, Closed: 0 },
      High: { Open: 0, Closed: 0 },
      Medium: { Open: 0, Closed: 0 },
      Low: { Open: 0, Closed: 0 }
    };

    reports.forEach(report => {
      let severity = report.severity_category;
      const status = retestStatusByReportId[report.report_id];
      const isClosed = closedStatuses.includes(status);

      if (severity) {
        severity =
          severity.charAt(0).toUpperCase() + severity.slice(1).toLowerCase();

        if (
          severity === "Critical" ||
          severity === "High" ||
          severity === "Medium" ||
          severity === "Low"
        ) {
          if (isClosed) {
            openCloseBySeverity[severity].Closed++;
          } else {
            openCloseBySeverity[severity].Open++;
          }
        } else {
          console.log(`Unexpected severity category: ${severity}`);
        }
      }
    });

    Object.entries(openCloseBySeverity).forEach(([severity, counts]) => {
      openCloseBySeverity[severity].Total = counts.Open + counts.Closed;
    });

    // Process reports for display
    const { sortedReports } = processReportsForDisplay(
      reports,
      retestStatusByReportId
    );

    // Extract program details
    const programDetails = userPrograms.map(program => ({
      program_id: program.program_id,
      program_title: program.program_title,
      testing_type: program.testing_type,
      targets: program.targets
        ? typeof program.targets === "string"
          ? program.targets.split(",").map(t => t.trim())
          : program.targets
        : [],
      expected_start_date: program.expected_start_date,
      test_lead: program.test_lead,
      prepared_by: program.prepared_by,
      reviewed_by: program.reviewed_by,
      approved_by: program.approved_by
    }));

    // Extract target details
    const targetDetails = extractTargetDetails(userPrograms);

    // Get date of request
    const dateOfRequest = new Date().toISOString().split("T")[0];

    logger.log({
      level: "info",
      label: "automated-report.controller | getProgramReportsData",
      message: `Successfully retrieved program reports for report ${report_id}`
    });

    // Add console log before returning final data
    const finalData = {
      company_name: automatedReport.company_name,
      report_title: automatedReport.title,
      version_number: formattedVersionNumber,
      document_number: documentNumber,
      date_of_request: dateOfRequest,
      test_lead: programDetails.length > 0 ? programDetails[0].test_lead : null,
      prepared_by:
        programDetails.length > 0 ? programDetails[0].prepared_by : null,
      reviewed_by:
        programDetails.length > 0 ? programDetails[0].reviewed_by : null,
      approved_by:
        programDetails.length > 0 ? programDetails[0].approved_by : null,
      program_details: programDetails,
      target_details: targetDetails,
      severity_counts: severitySummary,
      status_counts: openClosedCounts,
      open_close_counts_by_severity: openCloseBySeverity,
      reports_list: sortedReports.map(report => ({
        abbreviation: report.abbreviation,
        title: report.title,
        severity_category: report.severity_category,
        status: report.status
      })),
      recommendations_summary: recommendationsSummary,
      detailed_findings: sortedReports.map(report => ({
        abbreviation: report.abbreviation,
        title: report.title,
        scope: report.scope,
        description: report.description,
        instructions: report.instructions,
        impact: report.impact,
        fix: report.fix,
        submitted_date: report.submitted_date,
        severity_category: report.severity_category
      })),
      contributor_details: contributorDetails,
      automated_report_id: report_id
    };

    console.log(
      "getProgramReportsData - Final data:",
      JSON.stringify(finalData, null, 2)
    );
    return finalData;
  } catch (error) {
    console.error("getProgramReportsData - Error:", error);
    logger.log({
      level: "error",
      label: "automated-report.controller | getProgramReportsData",
      message: `Error retrieving program reports: ${error.message}`
    });
    return null;
  }
}

// Helper function to process reports for display
function processReportsForDisplay(reports, retestStatusByReportId) {
  // Generate abbreviations and sort reports
  const severityAbbreviations = {
    [SeverityTypes.CRITICAL]: "C",
    [SeverityTypes.HIGH]: "H",
    [SeverityTypes.MEDIUM]: "M",
    [SeverityTypes.LOW]: "L"
  };

  const severityCounts = {
    [SeverityTypes.CRITICAL]: 0,
    [SeverityTypes.HIGH]: 0,
    [SeverityTypes.MEDIUM]: 0,
    [SeverityTypes.LOW]: 0
  };

  const sortedReports = reports
    .filter(
      report =>
        report.severity_category !== undefined &&
        report.severity_category !== null
    )
    .sort((a, b) => {
      const dateA = new Date(a.submitted_date).getTime();
      const dateB = new Date(b.submitted_date).getTime();
      if (dateA !== dateB) {
        return dateA - dateB;
      }
      const severityOrder = {
        [SeverityTypes.CRITICAL]: 0,
        [SeverityTypes.HIGH]: 1,
        [SeverityTypes.MEDIUM]: 2,
        [SeverityTypes.LOW]: 3
      };
      const severityA = severityOrder[a.severity_category] || 999;
      const severityB = severityOrder[b.severity_category] || 999;
      return severityA - severityB;
    })
    .map(report => {
      severityCounts[report.severity_category]++;
      const abbr =
        severityAbbreviations[report.severity_category] +
        severityCounts[report.severity_category];
      const status = retestStatusByReportId[report.report_id];
      const isOpen = !["Fix Verified", "Fix Approved"].includes(status);
      return {
        abbreviation: abbr,
        title: report.report_title,
        severity_category: report.severity_category,
        status: isOpen ? "Open" : "Closed",
        scope: report.scope,
        description: report.description,
        instructions: report.instructions,
        impact: report.impact,
        fix: report.fix,
        submitted_date: report.submitted_date
      };
    });

  return { sortedReports };
}

// Helper function to extract target details
function extractTargetDetails(userPrograms) {
  const targetDetails = [];
  userPrograms.forEach(program => {
    if (program.targets) {
      try {
        let parsedTargets;
        if (typeof program.targets === "string") {
          try {
            parsedTargets = JSON.parse(program.targets);
          } catch (e) {
            parsedTargets = program.targets.split(",").map(t => t.trim());
          }
        } else {
          parsedTargets = program.targets;
        }

        if (Array.isArray(parsedTargets)) {
          parsedTargets.forEach(target => {
            if (
              target &&
              typeof target === "object" &&
              "targetName" in target &&
              "targetType" in target
            ) {
              const targetType = target.targetType;
              const targetUrl = target.targetName;
              const exists = targetDetails.some(
                t => t.type === targetType && t.url === targetUrl
              );
              if (!exists) {
                targetDetails.push({
                  type: targetType,
                  url: targetUrl
                });
              }
            } else if (typeof target === "string") {
              const parts = target.split(":");
              if (parts.length >= 2) {
                const targetType = parts[0].trim();
                const targetUrl = parts.slice(1).join(":").trim();
                const exists = targetDetails.some(
                  t => t.type === targetType && t.url === targetUrl
                );
                if (!exists) {
                  targetDetails.push({
                    type: targetType,
                    url: targetUrl
                  });
                }
              }
            }
          });
        }
      } catch (error) {
        logger.log({
          level: "error",
          label: "automated-report.controller | extractTargetDetails",
          message: `Error parsing targets for program ${program.program_id}: ${error.message}`
        });
      }
    }
  });

  return targetDetails;
}

// Helper function to determine domains
function determineDomains(userPrograms) {
  const domains: string[] = [];

  if (userPrograms.length > 0) {
    if (userPrograms[0].testing_type) {
      const testingType =
        typeof userPrograms[0].testing_type === "string"
          ? userPrograms[0].testing_type.toLowerCase()
          : "";
      if (testingType.includes("network")) {
        domains.push("NET");
      } else if (testingType.includes("mobile")) {
        domains.push("MOB");
      } else if (testingType.includes("api")) {
        domains.push("API");
      } else if (testingType.includes("iot")) {
        domains.push("IOT");
      } else if (testingType.includes("cloud")) {
        domains.push("CLOUD");
      } else {
        domains.push("WEB");
      }
    }

    if (userPrograms[0].targets) {
      try {
        let parsedTargets = [];
        if (typeof userPrograms[0].targets === "string") {
          try {
            parsedTargets = JSON.parse(userPrograms[0].targets);
          } catch (e) {
            parsedTargets = userPrograms[0].targets
              .split(",")
              .map(t => t.trim());
          }
        } else if (Array.isArray(userPrograms[0].targets)) {
          parsedTargets = userPrograms[0].targets;
          if (
            parsedTargets.length > 0 &&
            typeof parsedTargets[0] === "string" &&
            parsedTargets[0].includes("targetName")
          ) {
            try {
              const joinedString = parsedTargets.join("");
              parsedTargets = JSON.parse(joinedString);
            } catch (e) {
              // Keep as is if parsing fails
            }
          }
        }

        const targetTypes = new Set<string>();
        if (Array.isArray(parsedTargets)) {
          parsedTargets.forEach(target => {
            if (typeof target === "string") {
              const parts = target.split(":");
              if (parts.length >= 2) {
                targetTypes.add(parts[0].trim().toLowerCase());
              }
            } else if (target && typeof target === "object") {
              const type = target.targetType || target.targetTyoe;
              if (type) {
                targetTypes.add(String(type).toLowerCase());
              }
            }
          });
        }

        if (targetTypes.size > 0) {
          const networkTypes = [TargetType.NETWORK.toLowerCase()];
          const mobileTypes = [
            TargetType.IOS.toLowerCase(),
            TargetType.ANDROID.toLowerCase()
          ];
          const apiTypes = [TargetType.API.toLowerCase()];
          const web3Types = [
            TargetType.WEB3.toLowerCase(),
            TargetType.CONTRACT.toLowerCase()
          ];
          const codeTypes = [TargetType.CODE.toLowerCase()];
          const dbTypes = [TargetType.DATABASE.toLowerCase()];
          const webTypes = [TargetType.WEB.toLowerCase()];

          networkTypes.push("network", "infrastructure");
          mobileTypes.push("mobile", "ios", "android");
          apiTypes.push("api");
          web3Types.push("web3", "smart contract", "blockchain");
          codeTypes.push("code", "source");
          dbTypes.push("database", "db");
          webTypes.push("web", "website");

          for (const type of targetTypes) {
            if (networkTypes.some(t => type.includes(t))) {
              if (!domains.includes("NET")) domains.push("NET");
            } else if (mobileTypes.some(t => type.includes(t))) {
              if (!domains.includes("MOB")) domains.push("MOB");
            } else if (apiTypes.some(t => type.includes(t))) {
              if (!domains.includes("API")) domains.push("API");
            } else if (web3Types.some(t => type.includes(t))) {
              if (!domains.includes("WEB3")) domains.push("WEB3");
            } else if (codeTypes.some(t => type.includes(t))) {
              if (!domains.includes("CODE")) domains.push("CODE");
            } else if (dbTypes.some(t => type.includes(t))) {
              if (!domains.includes("DB")) domains.push("DB");
            } else if (webTypes.some(t => type.includes(t))) {
              if (!domains.includes("WEB")) domains.push("WEB");
            }
          }
        }
      } catch (error) {
        logger.log({
          level: "warn",
          label: "automated-report.controller | determineDomains",
          message: `Error parsing targets for domain determination: ${error.message}`
        });
        if (domains.length === 0) {
          domains.push("WEB");
        }
      }
    }
  }

  if (domains.length === 0) {
    domains.push("WEB");
  }

  return domains;
}

// Helper to generate a clean, minimal HTML email for pentest report request
function pentestReportRequestedEmail({ programTitle, requestedBy, createdAt }) {
  return `
  <html>
    <body style="font-family:Inter,Arial,sans-serif;background:#f9f9f9;padding:0;margin:0;">
      <div style="max-width:480px;margin:40px auto;background:#fff;border-radius:16px;border:1px solid #e0e0e0;box-shadow:0 2px 8px #0001;overflow:hidden;">
        <div style="padding:32px 32px 16px 32px;text-align:center;">
          <img src="https://c8270c56-7594-4100-9b34-7589297588fa.b-cdn.net/e/bac89f4c-a951-463b-81dd-86c0e12c3427/28695d5f-3f29-41fa-a53d-2f93062026e6.jpeg" alt="Capture The Bug" style="height:48px;margin-bottom:24px;"/>
          <h1 style="font-size:22px;color:#111;margin:0 0 12px 0;">New Pentest Report Request</h1>
          <p style="font-size:16px;color:#444;margin:0 0 24px 0;">A business user has requested a new pentest report.</p>
          <table style="width:100%;margin:0 auto 24px auto;font-size:15px;color:#222;text-align:left;">
            <tr><td style="padding:4px 0;font-weight:600;">Program Title:</td><td style="padding:4px 0;">${programTitle}</td></tr>
            <tr><td style="padding:4px 0;font-weight:600;">Requested By:</td><td style="padding:4px 0;">${requestedBy}</td></tr>
            <tr><td style="padding:4px 0;font-weight:600;">Requested At:</td><td style="padding:4px 0;">${createdAt}</td></tr>
          </table>
          <div style="margin:24px 0 0 0;font-size:15px;color:#555;">Please review and assign this report for QA and admin review.</div>
        </div>
        <div style="background:#f5f5f5;padding:16px 32px;text-align:center;font-size:13px;color:#888;">Capture The Bug Platform</div>
      </div>
    </body>
  </html>
  `;
}

// Optimized createAutomatedReport with transaction support
export const createAutomatedReport = async (
  req: CTBRequest<{
    company_name?: string;
    program_ids: number[];
  }>,
  res: Response
) => {
  const transaction = await database.sequelize.transaction();
  try {
    const { user_id, role } = req.user as UserAttributes;
    const { company_name = null, program_ids } = req.body;

    const startTime = performance.now();
    logger.log({
      level: "info",
      label: "automated-report.controller | createAutomatedReport",
      message: `Creating automated report for user ${user_id}`
    });

    // Input validation
    if (!Array.isArray(program_ids) || program_ids.length === 0) {
      await transaction.rollback();
      logger.log({
        level: "warn",
        label: "automated-report.controller | createAutomatedReport",
        message: `Invalid report data provided by user ${user_id}`
      });
      return res.status(400).json({
        status: "error",
        message: "400 BAD REQUEST: Missing required fields: program_ids"
      });
    }

    // Add debug logging
    logger.log({
      level: "info",
      label: "automated-report.controller | createAutomatedReport",
      message: `Attempting to find programs with IDs: ${program_ids.join(
        ", "
      )} for user ${user_id} with role ${role}`
    });

    // Authorization check - only admin roles allowed
    const allowedRoles = [
      UserRole.ADMIN,
      UserRole.ADMIN_MANAGER,
      UserRole.SUB_ADMIN,
      UserRole.QA,
      UserRole.BUSINESS
    ];
    if (!allowedRoles.includes(role)) {
      await transaction.rollback();
      logger.log({
        level: "warn",
        label: "automated-report.controller | createAutomatedReport",
        message: `Unauthorized access attempt by user ${user_id} with role ${role}`
      });
      return res.status(403).json({
        status: "error",
        message:
          "403 FORBIDDEN: You don't have permission to create automated reports"
      });
    }

    // Fetch programs to determine domains
    const userPrograms = await Programs.findAll({
      where: {
        program_id: { [Op.in]: program_ids },
        ...(role !== UserRole.ADMIN && { user_id }) // Only check user_id for non-admin users
        // Removed is_delete and is_activated filters to allow inactive/deleted programs
      },
      attributes: ["program_id", "targets", "testing_type"],
      transaction
    });

    // Add debug logging for query results
    logger.log({
      level: "info",
      label: "automated-report.controller | createAutomatedReport",
      message: `Found ${
        userPrograms.length
      } programs. Program IDs: ${userPrograms
        .map(p => p.program_id)
        .join(", ")}`
    });

    if (userPrograms.length === 0) {
      await transaction.rollback();
      logger.log({
        level: "warn",
        label: "automated-report.controller | createAutomatedReport",
        message: `No valid programs found for program_ids ${program_ids}`
      });
      return res.status(400).json({
        status: "error",
        message:
          "400 BAD REQUEST: No valid programs found for the provided program_ids"
      });
    }

    // Determine domains from programs
    const domains = determineDomains(userPrograms);
    const reportTitle = `Penetration Testing Report ${domains.join(" + ")}`;

    // Get program name from the first program for the cover page
    const programName =
      userPrograms.length > 0 ? userPrograms[0].program_title : null;

    // Generate document number for new reports
    const existingDocNumbers = [];
    const documentNumber = generateDocumentNumber(
      program_ids[0],
      domains,
      existingDocNumbers
    );

    // Sort program_ids for consistent comparison
    const sortedProgramIds = [...program_ids].sort((a, b) => a - b);

    // Find all reports with the same program_ids for this user
    const allReports = await AutomatedReports.findAll({
      where: {
        business_id: user_id.toString()
      },
      order: [["createdAt", "DESC"]],
      transaction
    });

    // Find all reports with matching program_ids
    const matchingReports = [];
    for (const report of allReports) {
      const existingProgramIds = Array.isArray(report.program_ids)
        ? report.program_ids
        : JSON.parse(report.program_ids || "[]");
      const sortedExistingProgramIds = [...existingProgramIds].sort(
        (a, b) => a - b
      );
      if (
        JSON.stringify(sortedProgramIds) ===
        JSON.stringify(sortedExistingProgramIds)
      ) {
        matchingReports.push(report);
      }
    }

    // Find the latest matching report (by createdAt)
    let latestMatchingReport = null;
    if (matchingReports.length > 0) {
      latestMatchingReport = matchingReports.reduce((latest, curr) => {
        return new Date(curr.createdAt) > new Date(latest.createdAt)
          ? curr
          : latest;
      }, matchingReports[0]);
    }

    let automatedReport;
    let isNewVersion = false;
    let versionNumber = 1;
    let notificationMessage;
    let skipHashCheck = false;

    // If no matching report, create a new one
    if (!latestMatchingReport) {
      // Calculate severity counts directly from programs data
      const validProgramIds = userPrograms.map(program => program.program_id);
      const reports = await Reports.findAll({
        where: {
          program_id: { [Op.in]: validProgramIds },
          state: {
            [Op.notIn]: [ReportState.UNDER_REVIEW, ReportState.DELETED]
          }
        },
        attributes: ["severity_category"],
        transaction
      });
      const severityCounts = {
        Critical: reports.filter(
          r => r.severity_category === SeverityTypes.CRITICAL
        ).length,
        High: reports.filter(r => r.severity_category === SeverityTypes.HIGH)
          .length,
        Medium: reports.filter(
          r => r.severity_category === SeverityTypes.MEDIUM
        ).length,
        Low: reports.filter(r => r.severity_category === SeverityTypes.LOW)
          .length
      };
      const totalFindings = reports.length;
      automatedReport = await AutomatedReports.create(
        {
          business_id: Number(user_id),
          title: reportTitle,
          company_name,
          program_name: programName,
          program_ids: sortedProgramIds,
          document_number: documentNumber,
          status: "draft",
          total_findings: totalFindings,
          critical_count: severityCounts.Critical,
          high_count: severityCounts.High,
          medium_count: severityCounts.Medium,
          low_count: severityCounts.Low
        },
        { transaction }
      );
      notificationMessage = `New automated report "${reportTitle}" has been created by a business user and requires review.`;
    } else if (latestMatchingReport.status === "approved") {
      // If the latest report is approved, create a new AutomatedReports row with incremented version number
      // Find the highest version number among all previous matching reports
      let highestVersion = 1;
      for (const report of matchingReports) {
        // Find the highest version number from AutomatedReportVersions for this report
        const lastVersion = await AutomatedReportVersions.findOne({
          where: { report_id: report.id },
          order: [["version_number", "DESC"]],
          transaction
        });
        if (lastVersion && lastVersion.version_number > highestVersion) {
          highestVersion = lastVersion.version_number;
        }
      }
      versionNumber = highestVersion + 1;
      // Calculate severity counts directly from programs data
      const validProgramIds = userPrograms.map(program => program.program_id);
      const reports = await Reports.findAll({
        where: {
          program_id: { [Op.in]: validProgramIds },
          state: {
            [Op.notIn]: [ReportState.UNDER_REVIEW, ReportState.DELETED]
          }
        },
        attributes: ["severity_category"],
        transaction
      });
      const severityCounts = {
        Critical: reports.filter(
          r => r.severity_category === SeverityTypes.CRITICAL
        ).length,
        High: reports.filter(r => r.severity_category === SeverityTypes.HIGH)
          .length,
        Medium: reports.filter(
          r => r.severity_category === SeverityTypes.MEDIUM
        ).length,
        Low: reports.filter(r => r.severity_category === SeverityTypes.LOW)
          .length
      };
      const totalFindings = reports.length;
      automatedReport = await AutomatedReports.create(
        {
          business_id: Number(user_id),
          title: reportTitle,
          company_name,
          program_name: programName,
          program_ids: sortedProgramIds,
          document_number: documentNumber,
          status: "draft",
          total_findings: totalFindings,
          critical_count: severityCounts.Critical,
          high_count: severityCounts.High,
          medium_count: severityCounts.Medium,
          low_count: severityCounts.Low
        },
        { transaction }
      );
      notificationMessage = `New automated report (version ${versionNumber}) for previously approved program has been created by a business user and requires review.`;
      skipHashCheck = true;
    } else {
      // If the latest report is not approved, create a new version in the same row (current logic)
      automatedReport = latestMatchingReport;
      isNewVersion = true;
      notificationMessage = `New version request for automated report "${reportTitle}" has been submitted by a business user and requires review.`;
    }

    // Get program reports data using the actual report ID
    const programReportsData = await getProgramReportsData(
      automatedReport.id,
      req.user,
      transaction
    );

    if (!programReportsData) {
      await transaction.rollback();
      logger.log({
        level: "error",
        label: "automated-report.controller | createAutomatedReport",
        message: "Failed to get program reports data"
      });
      return res.status(500).json({
        status: "error",
        message: "500 INTERNAL ERROR: Failed to get program reports data"
      });
    }

    // Update existing report with new severity counts if it's a new version
    if (isNewVersion) {
      const severityCounts = programReportsData.severity_counts || {};
      const totalFindings = programReportsData.detailed_findings?.length || 0;
      await automatedReport.update(
        {
          total_findings: totalFindings,
          critical_count: (severityCounts as any).Critical || 0,
          high_count: (severityCounts as any).High || 0,
          medium_count: (severityCounts as any).Medium || 0,
          low_count: (severityCounts as any).Low || 0
        },
        { transaction }
      );
    }

    // Generate hash of content to check for changes
    const contentToHash = {
      test_lead: programReportsData.test_lead || null,
      prepared_by: programReportsData.prepared_by || null,
      reviewed_by: programReportsData.reviewed_by || null,
      approved_by: programReportsData.approved_by || null,
      program_details: programReportsData.program_details || [],
      target_details: programReportsData.target_details || [],
      severity_counts: programReportsData.severity_counts || {},
      status_counts: programReportsData.status_counts || {},
      open_close_counts_by_severity:
        programReportsData.open_close_counts_by_severity || {},
      reports_list: programReportsData.reports_list || [],
      recommendations_summary: programReportsData.recommendations_summary || "",
      detailed_findings: programReportsData.detailed_findings || []
    };

    const contentHash = crypto
      .createHash("sha256")
      .update(JSON.stringify(contentToHash))
      .digest("hex");

    // Check for changes if it's an existing report, unless skipHashCheck is true
    if (isNewVersion && !skipHashCheck) {
      const latestVersion = await AutomatedReportVersions.findOne({
        where: { report_id: automatedReport.id },
        order: [["version_number", "DESC"]],
        transaction
      });
      if (latestVersion && latestVersion.hash === contentHash) {
        await transaction.rollback();
        logger.log({
          level: "warn",
          label: "automated-report.controller | createAutomatedReport",
          message: `No changes detected for report ${automatedReport.id}`
        });
        return res.status(400).json({
          status: "error",
          message:
            "400 BAD REQUEST: No changes detected since last report version. Cannot create new report."
        });
      }
      versionNumber = latestVersion ? latestVersion.version_number + 1 : 1;
    }

    // Create new report version row
    const newVersion = await AutomatedReportVersions.create(
      {
        report_id: automatedReport.id,
        version_number: versionNumber,
        status: "pending",
        approved_by: null,
        approved_at: null,
        summary:
          isNewVersion || skipHashCheck
            ? `Version ${versionNumber} of automated report`
            : "Initial version of automated report",
        pdf_url: null,
        hash: contentHash
      },
      { transaction }
    );

    // Send notifications to admin users
    try {
      const adminUsers = await Users.findAll({
        where: { role: UserRole.ADMIN },
        attributes: ["user_id"],
        transaction
      });

      const adminUserIds = adminUsers.map(admin => admin.user_id);

      if (adminUserIds.length > 0) {
        await sendNotification(
          {
            type: NotificationType.Program,
            payload: JSON.stringify({ report_id: automatedReport.id }),
            message: notificationMessage
          },
          adminUserIds,
          [NotificationMethod.CTB]
        );
      }
    } catch (notificationError) {
      logger.log({
        level: "warn",
        label: "automated-report.controller | createAutomatedReport",
        message: `Failed to send notifications: ${notificationError.message}`
      });
    }

    // After report creation and before return response:
    // Send email notification to all ADMIN, SUBADMIN, and QA users if business user
    if (role === UserRole.BUSINESS) {
      // Fetch all admin, subadmin, and QA users
      const notifyRoles = [UserRole.ADMIN, UserRole.SUB_ADMIN, UserRole.QA];
      const notifyUsers = await Users.findAll({
        where: { role: notifyRoles },
        attributes: ["email", "display_name", "username"]
      });
      // Get business user info
      const businessUser = await Users.findOne({
        where: { user_id },
        attributes: ["display_name", "username", "email"]
      });
      // Get program title (first program)
      let programTitle = "";
      if (userPrograms && userPrograms.length > 0) {
        programTitle = userPrograms[0].program_title || "";
      }
      // If still blank, fetch from DB
      if (!programTitle && program_ids && program_ids.length > 0) {
        const program = await Programs.findOne({
          where: { program_id: program_ids[0] },
          attributes: ["program_title"]
        });
        if (program) programTitle = program.program_title;
      }
      // Compose email
      const emailSubject = `New Pentest Report Request: ${
        programTitle || "(No Title)"
      }`;
      const emailBody = pentestReportRequestedEmail({
        programTitle: programTitle || "(No Title)",
        requestedBy:
          businessUser?.display_name ||
          businessUser?.username ||
          businessUser?.email ||
          `User ${user_id}`,
        createdAt: new Date().toLocaleString()
      });
      // Send to all admins, subadmins, QAs
      for (const admin of notifyUsers) {
        if (admin.email) {
          sendEmail(admin.email, emailSubject, emailBody);
        }
      }
    }

    // Commit the transaction
    await transaction.commit();

    const endTime = performance.now();
    logger.log({
      level: "info",
      label: "automated-report.controller | createAutomatedReport",
      message: `Report creation completed in ${(endTime - startTime).toFixed(
        2
      )}ms`
    });

    // Send final response
    return res.status(201).json({
      status: "success",
      message: isNewVersion
        ? "New version of the report created successfully. You'll be notified once it's reviewed and ready."
        : "Report request created successfully. You'll be notified by email once it's reviewed and ready.",
      data: {
        report_id: automatedReport.id,
        title: automatedReport.title,
        company_name: automatedReport.company_name,
        program_ids: automatedReport.program_ids,
        is_new_version: isNewVersion,
        version_number: versionNumber,
        pdf_url: null
      }
    });
  } catch (error) {
    await transaction.rollback();
    logger.log({
      level: "error",
      label: "automated-report.controller | createAutomatedReport",
      message: `Error creating automated report: ${error.message}`
    });
    return res.status(500).json({
      status: "error",
      message: `500 INTERNAL ERROR: Unable to create automated report - ${error.message}`
    });
  }
};

// Export the getProgramReports API endpoint function
export const getProgramReports = async (
  req: CTBParamsRequest<{ report_id: string }>,
  res: Response
) => {
  try {
    const { report_id } = req.params;
    const { user_id, role } = req.user as UserAttributes;

    console.log("getProgramReports - Request params:", {
      report_id,
      user_id,
      role
    });

    // Check if user has admin role
    const allowedRoles = [
      UserRole.ADMIN,
      UserRole.ADMIN_MANAGER,
      UserRole.SUB_ADMIN,
      UserRole.QA,
      UserRole.BUSINESS
    ];

    if (!allowedRoles.includes(role)) {
      console.log("getProgramReports - Unauthorized access attempt:", {
        user_id,
        role
      });
      logger.log({
        level: "warn",
        label: "automated-report.controller | getProgramReports",
        message: `Unauthorized access attempt by user ${user_id} with role ${role}`
      });
      return res.status(403).json({
        status: "error",
        message: "403 FORBIDDEN: Only admin users can access this endpoint"
      });
    }

    logger.log({
      level: "info",
      label: "automated-report.controller | getProgramReports",
      message: `Retrieving program reports for report ${report_id} by user ${user_id}`
    });

    const programReportsData = await getProgramReportsData(report_id, req.user);
    console.log(
      "getProgramReports - Program reports data:",
      JSON.stringify(programReportsData, null, 2)
    );

    if (!programReportsData) {
      console.log("getProgramReports - No program reports found:", {
        report_id
      });
      logger.log({
        level: "warn",
        label: "automated-report.controller | getProgramReports",
        message: `No program reports found for report ${report_id}`
      });
      return res.status(404).json({
        status: "error",
        message: "404 NOT FOUND: Unable to retrieve program reports"
      });
    }

    logger.log({
      level: "info",
      label: "automated-report.controller | getProgramReports",
      message: `Successfully retrieved program reports for report ${report_id}`
    });

    const response = {
      status: "success",
      message: "Reports retrieved successfully",
      data: programReportsData
    };
    console.log(
      "getProgramReports - Final response:",
      JSON.stringify(response, null, 2)
    );

    return res.status(200).json(response);
  } catch (error) {
    console.error("getProgramReports - Error:", error);
    logger.log({
      level: "error",
      label: "automated-report.controller | getProgramReports",
      message: `Error retrieving program reports: ${error.message}`
    });

    return res.status(500).json({
      status: "error",
      message: "500 INTERNAL ERROR: Unable to retrieve program reports"
    });
  }
};

export enum TargetType {
  WEB = "Web",
  IOS = "iOS",
  ANDROID = "Android",
  API = "API",
  WEB3 = "Web3",
  CODE = "Code",
  PROTOCOL = "Protocol",
  DATABASE = "Database",
  NETWORK = "Network/Infrastructure",
  CONTRACT = "Smart Contract",
  OTHER = "Other"
}

/**
 * Generates a document number for automated reports
 * Format: "CTB-TECH-CYBER-MMYY-ProgramID-V.XX-DOMAIN"
 *
 * @param programId The program ID
 * @param domain The testing domain (e.g., "WEB", "NET") or array of domains
 * @param existingNumbers Optional array of existing document numbers to avoid duplicates
 * @returns A unique document number
 */

export const getUserPrograms = async (req: CTBQueryRequest, res: Response) => {
  try {
    const { user_id, role } = req.user as UserAttributes;

    const log = {
      level: "info",
      label: "automated-report.controller | getUserPrograms",
      message: `Retrieving programs for user ${user_id}`
    };

    const allowedRoles = [
      UserRole.ADMIN,
      UserRole.ADMIN_MANAGER,
      UserRole.SUB_ADMIN,
      UserRole.QA
    ];

    if (!allowedRoles.includes(role)) {
      log.level = "warn";
      log.message = `Unauthorized access attempt by user ${user_id} with role ${role}`;
      logger.log(log);

      return res.status(403).json({
        status: "error",
        message: "403 FORBIDDEN: Only admin users can access this endpoint"
      });
    }

    logger.log(log);

    // All admin roles can see all programs
    const userPrograms = await Programs.findAll({
      where: {
        is_delete: false,
        is_activated: true
      },
      attributes: [
        "program_id",
        "program_title",
        "targets",
        "scope",
        "expected_start_date",
        "expected_end_date"
      ],
      order: [["program_id", "ASC"]]
    });

    if (userPrograms.length === 0) {
      logger.log({
        level: "info",
        label: "automated-report.controller | getUserPrograms",
        message: `No programs found for user ${user_id}`
      });

      return res.status(404).json({
        status: "error",
        message: "404 NOT FOUND: No programs found for this user",
        data: []
      });
    }

    const formattedPrograms = userPrograms.map(program => ({
      program_id: program.program_id,
      program_title: program.program_title,
      targets: program.targets,
      scope: program.scope,
      expected_start_date: program.expected_start_date,
      expected_end_date: program.expected_end_date
    }));

    logger.log({
      level: "info",
      label: "automated-report.controller | getUserPrograms",
      message: `Successfully retrieved ${userPrograms.length} programs for user ${user_id}`
    });

    return res.status(200).json({
      status: "success",
      message: "Programs retrieved successfully",
      data: formattedPrograms
    });
  } catch (error) {
    logger.log({
      level: "error",
      label: "automated-report.controller | getUserPrograms",
      message: `Error retrieving programs: ${error.message}`
    });

    return res.status(500).json({
      status: "error",
      message: "500 INTERNAL ERROR: Unable to retrieve programs"
    });
  }
};

export const getPendingAutomatedReportVersions = async (
  req: CTBQueryRequest,
  res: Response
) => {
  try {
    logger.log({
      level: "info",
      label: "automated-report.controller | getPendingAutomatedReportVersions",
      message: "Fetching pending automated report versions"
    });
    const { role } = req.user as UserAttributes;

    if (role !== UserRole.ADMIN) {
      return res.status(403).json({
        message: "You don't have permission to access pending reports"
      });
    }

    const reportVersions = await AutomatedReportVersions.findAll({
      where: { status: ["pending", "approved"] },
      order: [["version_number", "DESC"]]
    });

    const reportIds = reportVersions.map(version => version.report_id);

    const reports = await AutomatedReports.findAll({
      where: { id: reportIds },
      attributes: ["id", "title", "program_ids", "company_name", "business_id"]
    });

    const reportMap = {};
    reports.forEach(report => {
      // Ensure program_ids is properly parsed from JSON if it's a string
      let programIds = report.program_ids;
      if (typeof programIds === "string") {
        try {
          programIds = JSON.parse(programIds);
        } catch (e) {
          logger.error(`Error parsing program_ids for report ${report.id}:`, e);
          programIds = [];
        }
      } else if (!Array.isArray(programIds)) {
        programIds = [];
      }

      reportMap[report.id] = {
        title: report.title,
        program_ids: programIds,
        company_name: report.company_name,
        business_id: report.business_id
      };
    });

    const formattedResponse = await Promise.all(
      reportVersions.map(async (version: any) => {
        const reportData = reportMap[version.report_id] || {
          title: "Untitled Report",
          program_ids: [],
          company_name: "Unknown Company"
        };

        const programsData = [];

        if (
          Array.isArray(reportData.program_ids) &&
          reportData.program_ids.length > 0
        ) {
          for (const programId of reportData.program_ids) {
            const numericProgramId =
              typeof programId === "string"
                ? parseInt(programId, 10)
                : programId;

            const program = await Programs.findOne({
              where: { program_id: numericProgramId },
              attributes: ["program_id", "program_title"]
            });

            programsData.push({
              program_id: numericProgramId,
              program_title: program ? program.program_title : "Unknown Program"
            });
          }
        }

        return {
          version_id: version.id,
          report_id: version.report_id,
          version_number: version.version_number,
          status: version.status,
          report_title: reportData.title,
          company_name: reportData.company_name,
          business_id: reportData.business_id,
          programs: programsData
        };
      })
    );

    // Separate pending and approved reports
    const pendingReports = formattedResponse.filter(
      report => report.status === "pending"
    );
    const approvedReports = formattedResponse.filter(
      report => report.status === "approved"
    );

    logger.log({
      level: "info",
      label: "automated-report.controller | getPendingAutomatedReportVersions",
      message: `Successfully retrieved ${pendingReports.length} pending and ${approvedReports.length} approved automated report versions`
    });

    return res.status(200).json({
      message: "Automated report versions retrieved successfully",
      data: {
        pending: pendingReports,
        approved: approvedReports
      }
    });
  } catch (error) {
    logger.log({
      level: "error",
      label: "automated-report.controller | getAutomatedReportVersions",
      message: `Error retrieving automated report versions: ${error.message}`
    });

    return res.status(500).json({ message: "Internal server error" });
  }
};

export const checkPendingCompanyReport = async (
  req: CTBRequest<{
    program_ids: number[];
  }>,
  res: Response
) => {
  try {
    const { user_id, role } = req.user as UserAttributes;
    const { program_ids } = req.body;

    const log = {
      level: "info",
      label: "automated-report.controller | checkPendingCompanyReport",
      message: `Checking for pending reports with program IDs for user ${user_id}`
    };

    const allowedRoles = [
      UserRole.ADMIN,
      UserRole.ADMIN_MANAGER,
      UserRole.SUB_ADMIN,
      UserRole.QA
    ];

    if (!allowedRoles.includes(role)) {
      log.level = "warn";
      log.message = `Unauthorized access attempt by user ${user_id} with role ${role}`;
      logger.log(log);

      return res.status(403).json({
        status: "error",
        message: "403 FORBIDDEN: Only admin users can access this endpoint"
      });
    }

    if (
      !program_ids ||
      !Array.isArray(program_ids) ||
      program_ids.length === 0
    ) {
      log.level = "warn";
      log.message = `Invalid program_ids provided by user ${user_id}`;
      logger.log(log);

      return res.status(400).json({
        status: "error",
        message:
          "400 BAD REQUEST: Program IDs must be provided as a non-empty array",
        hasPendingReport: false
      });
    }

    logger.log(log);

    const pendingReports = await AutomatedReports.findAll({
      where: { business_id: user_id },
      attributes: ["id", "program_ids", "title", "company_name"],
      include: [
        {
          model: AutomatedReportVersions,
          as: "versions",
          where: { status: "pending" },
          required: true
        }
      ]
    });

    logger.log({
      level: "info",
      label: "automated-report.controller | checkPendingCompanyReport",
      message: `Found ${pendingReports.length} pending reports for user ${user_id}`
    });

    const programIdNumbers = program_ids.map(id => Number(id));

    let hasPendingReport = false;
    let pendingReport = null;

    for (const report of pendingReports) {
      let reportProgramIds = report.program_ids;

      if (typeof reportProgramIds === "string") {
        try {
          reportProgramIds = JSON.parse(reportProgramIds);
        } catch (e) {
          logger.log({
            level: "error",
            label: "automated-report.controller | checkPendingCompanyReport",
            message: `Error parsing program_ids for report ${report.id}`
          });
          continue;
        }
      }

      if (!Array.isArray(reportProgramIds)) {
        logger.log({
          level: "warn",
          label: "automated-report.controller | checkPendingCompanyReport",
          message: `Invalid program_ids format for report ${report.id}`
        });
        continue;
      }

      if (reportProgramIds.length === programIdNumbers.length) {
        const allMatch =
          programIdNumbers.every(id => reportProgramIds.includes(id)) &&
          reportProgramIds.every(id => programIdNumbers.includes(id));

        if (allMatch) {
          hasPendingReport = true;
          pendingReport = {
            report_id: report.id,
            report_title: report.title,
            company_name: report.company_name,
            program_ids: reportProgramIds
          };

          logger.log({
            level: "info",
            label: "automated-report.controller | checkPendingCompanyReport",
            message: `Found matching pending report ${
              report.id
            } for program IDs: ${JSON.stringify(programIdNumbers)}`
          });

          break;
        }
      }
    }

    logger.log({
      level: "info",
      label: "automated-report.controller | checkPendingCompanyReport",
      message: `Check completed for user ${user_id}. Pending report found: ${hasPendingReport}`
    });

    return res.status(200).json({
      status: "success",
      message: hasPendingReport
        ? `Programs ${program_ids.join(", ")} have a pending report`
        : `Programs ${program_ids.join(", ")} have no pending reports`,
      hasPendingReport,
      pendingReport
    });
  } catch (error) {
    logger.log({
      level: "error",
      label: "automated-report.controller | checkPendingCompanyReport",
      message: `Error checking pending reports: ${error.message}`
    });

    return res.status(500).json({
      status: "error",
      message: "500 INTERNAL ERROR: Unable to check pending reports"
    });
  }
};

export const getBusinessReports = async (
  req: CTBQueryRequest,
  res: Response
) => {
  try {
    const { user_id, role } = req.user as UserAttributes;
    const log = {
      level: "info",
      label: "automated-report.controller | getBusinessReports",
      message: `Retrieving reports for user ${user_id} with role ${role}`
    };

    // Check if user has admin role
    const allowedRoles = [
      UserRole.ADMIN,
      UserRole.ADMIN_MANAGER,
      UserRole.SUB_ADMIN,
      UserRole.QA
    ];

    if (!allowedRoles.includes(role)) {
      log.level = "warn";
      log.message = `Unauthorized access attempt by user ${user_id} with role ${role}`;
      logger.log(log);
      return res.status(403).json({
        status: "error",
        message:
          "403 FORBIDDEN: You don't have permission to access this endpoint"
      });
    }

    logger.log(log);

    // All admin-related roles can see all reports
    const whereCondition = {};

    const reports = (await AutomatedReports.findAll({
      where: whereCondition,
      attributes: [
        "id",
        "title",
        "company_name",
        "program_ids",
        "business_id",
        "createdAt",
        "updatedAt"
      ],
      include: [
        {
          model: AutomatedReportVersions,
          as: "versions",
          attributes: [
            "id",
            "version_number",
            "status",
            "summary",
            "pdf_url",
            "approved_at",
            "createdAt",
            "updatedAt"
          ]
        }
      ],
      order: [["createdAt", "DESC"]]
    })) as any[];

    // Fetch business user details for all admin roles
    let businessUsers = {};
    const users = await Users.findAll({
      where: {
        user_id: {
          [Op.in]: reports.map(report => report.business_id).filter(Boolean)
        }
      },
      attributes: ["user_id", "display_name", "username", "email"]
    });

    businessUsers = Object.fromEntries(
      users.map(user => [
        user.user_id,
        {
          name: `${user.display_name || user.username}`,
          email: user.email
        }
      ])
    );

    // Safely parse and flatten program_ids
    const programIds = reports.flatMap(report => {
      try {
        return Array.isArray(report.program_ids)
          ? report.program_ids
          : JSON.parse(report.program_ids || "[]");
      } catch (error) {
        logger.error(
          `Error parsing program_ids for report ${report.id}: ${error.message}`
        );
        return [];
      }
    });

    // Fetch program titles for all program_ids
    const programMap = await Programs.findAll({
      where: { program_id: { [Op.in]: programIds } },
      attributes: ["program_id", "program_title"]
    }).then(res =>
      Object.fromEntries(res.map(p => [p.program_id, p.program_title]))
    );

    // Initialize arrays to store version-based data
    const pendingVersions: any[] = [];
    const approvedVersions: any[] = [];

    for (const report of reports) {
      const reportProgramIds = (() => {
        try {
          return Array.isArray(report.program_ids)
            ? report.program_ids
            : JSON.parse(report.program_ids || "[]");
        } catch (error) {
          logger.error(
            `Error parsing program_ids for report ${report.id}: ${error.message}`
          );
          return [];
        }
      })();

      // Create a map of only the program titles relevant to this report
      const reportProgramTitles = {};
      reportProgramIds.forEach(programId => {
        if (programMap[programId]) {
          reportProgramTitles[programId] = programMap[programId];
        }
      });

      // Base report data
      const reportData = {
        id: report.id,
        title: report.title,
        company_name: report.company_name,
        program_titles: reportProgramTitles,
        created_at: report.get("createdAt"),
        updated_at: report.get("updatedAt")
      };

      // Add business owner info for admin users
      if (
        role === UserRole.ADMIN &&
        report.business_id &&
        businessUsers[report.business_id]
      ) {
        reportData["business_owner"] = businessUsers[report.business_id];
      }

      const versions = report.get("versions") as any[];
      if (versions && versions.length > 0) {
        for (const version of versions) {
          const versionData = {
            id: version.id,
            version_number: version.version_number,
            status: version.status,
            summary: version.summary,
            pdf_url: version.pdf_url,
            approved_at: version.approved_at,
            created_at: version.get("createdAt"),
            updated_at: version.get("updatedAt"),
            approver: version.approver
              ? {
                  id: version.approver.user_id,
                  name: `${version.approver.firstname} ${version.approver.lastname}`,
                  email: version.approver.email
                }
              : null,
            report: reportData // Attach report details to each version
          };

          // Push to respective arrays based on version status
          if (version.status === "pending") {
            pendingVersions.push(versionData);
          }
          if (version.status === "approved") {
            approvedVersions.push(versionData);
          }
        }
      }
    }

    const logMessage = `Admin user ${user_id} (${role}) retrieved ${pendingVersions.length} pending and ${approvedVersions.length} approved report versions across all business users`;

    logger.log({
      level: "info",
      label: "automated-report.controller | getBusinessReports",
      message: logMessage
    });

    return res.status(200).json({
      status: "success",
      message: "Reports retrieved successfully",
      data: {
        pending_reports: pendingVersions,
        approved_reports: approvedVersions,
        total_pending: pendingVersions.length,
        total_approved: approvedVersions.length,
        is_admin: true // All admin roles are considered admin for this purpose
      }
    });
  } catch (error) {
    logger.log({
      level: "error",
      label: "automated-report.controller | getBusinessReports",
      message: `Error retrieving reports: ${error.message}`
    });
    return res.status(500).json({
      status: "error",
      message: "500 INTERNAL ERROR: Unable to retrieve reports"
    });
  }
};

export const previewTemplate = async (
  req: CTBParamsRequest<{ report_id: string }> & { body: { reportData?: any } },
  res: Response
) => {
  try {
    const { report_id } = req.params;
    const { role } = req.user as UserAttributes;
    const { reportData } = req.body;

    // Check if user has admin role
    const allowedRoles = [
      UserRole.ADMIN,
      UserRole.ADMIN_MANAGER,
      UserRole.SUB_ADMIN,
      UserRole.QA
    ];

    if (!allowedRoles.includes(role)) {
      return res.status(403).json({
        status: "error",
        message: "403 FORBIDDEN: Only admin users can access this endpoint"
      });
    }

    // If no reportData in request body, fetch it from the database
    let templateData;
    if (!reportData) {
      const programReportsData = await getProgramReportsData(
        report_id,
        req.user
      );
      if (!programReportsData) {
        return res.status(404).json({
          status: "error",
          message: "Report not found"
        });
      }
      templateData = {
        isPdf: false,
        reportData: {
          ...programReportsData,
          current_date: new Date().toLocaleDateString("en-GB", {
            day: "2-digit",
            month: "2-digit",
            year: "numeric"
          })
        }
      };
    } else {
      templateData = {
        isPdf: false,
        reportData: {
          ...reportData,
          current_date: new Date().toLocaleDateString("en-GB", {
            day: "2-digit",
            month: "2-digit",
            year: "numeric"
          })
        }
      };
    }

    // Render using report-template.hbs
    const html = await new Promise<string>((resolve, reject) => {
      res.render("report-template", templateData, (err, html) => {
        if (err) reject(err);
        else resolve(html);
      });
    });

    res.send(html);
  } catch (error) {
    logger.error({
      label: "automated-report.controller | previewTemplate",
      message: `Error in preview template: ${error.message}`
    });
    res.status(500).json({
      status: "error",
      message: "500 INTERNAL ERROR: Unable to process preview request"
    });
  }
};

// Get all pentest reports for admin with filtering and pagination
export const getAllPentestReports = async (
  req: CTBQueryRequest,
  res: Response
) => {
  const startTime = performance.now();
  try {
    const { user_id, role } = req.user as UserAttributes;
    const {
      page = 1,
      limit = 10,
      status,
      company_name,
      date_from,
      date_to,
      sort_by = "createdAt",
      sort_order = "DESC"
    } = req.query;

    logger.log({
      level: "info",
      label: "automated-report.controller | getAllPentestReports",
      message: `Admin ${user_id} requesting pentest reports with filters: ${JSON.stringify(
        req.query
      )}`
    });

    // Build where clause
    const whereClause: any = {};

    // Filter by status based on user role
    let allowedStatuses: string[] = [];
    switch (role) {
      case UserRole.QA:
        // QA can see draft, qa_review, business_requested_changes, and rejected
        allowedStatuses = [
          "draft",
          "qa_review",
          "business_requested_changes",
          "rejected"
        ];
        break;
      case UserRole.ADMIN:
      case UserRole.SUB_ADMIN:
      case UserRole.ADMIN_MANAGER:
        // Admin roles can see all workflow statuses
        allowedStatuses = [
          "qa_review",
          "admin_review",
          "approved",
          "rejected",
          "business_review",
          "business_requested_changes",
          "changes_added",
          "report_updated",
          "business_approved"
        ];
        break;
      case UserRole.BUSINESS:
        // BUSINESS can see all workflow statuses
        allowedStatuses = [
          "approved",
          "business_review",
          "business_requested_changes",
          "changes_added",
          "report_updated",
          "business_approved"
        ];
        break;
      default:
        // Other roles see all reports
        allowedStatuses = [
          "draft",
          "qa_review",
          "admin_review",
          "approved",
          "rejected",
          "business_review",
          "business_requested_changes",
          "changes_added",
          "report_updated",
          "business_approved"
        ];
    }

    // Apply status filter based on user role
    if (status) {
      // If user requests a specific status, check if they're allowed to see it
      if (allowedStatuses.includes(status as string)) {
        whereClause.status = status;
      } else {
        // If not allowed, filter to only show allowed statuses
        whereClause.status = { [Op.in]: allowedStatuses };
      }
    } else {
      // If no specific status requested, filter to only show allowed statuses
      whereClause.status = { [Op.in]: allowedStatuses };
    }

    // Restrict business users to their own reports only
    if (role === UserRole.BUSINESS) {
      whereClause.business_id = user_id;
    }

    if (company_name) {
      whereClause.company_name = {
        [Op.like]: `%${company_name}%`
      };
    }

    if (date_from || date_to) {
      whereClause.createdAt = {};
      if (date_from) {
        whereClause.createdAt[Op.gte] = new Date(date_from as string);
      }
      if (date_to) {
        whereClause.createdAt[Op.lte] = new Date(date_to as string);
      }
    }

    // Calculate offset
    const offset = (parseInt(page as string) - 1) * parseInt(limit as string);

    // Get reports with pagination
    const { count, rows: reports } = await AutomatedReports.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: AutomatedReportVersions,
          as: "versions",
          required: false,
          order: [["version_number", "DESC"]],
          limit: 1
        },
        {
          model: Users,
          as: "user",
          attributes: ["user_id", "display_name", "email"]
        }
      ],
      order: [[sort_by as string, sort_order as string]],
      limit: parseInt(limit as string),
      offset,
      distinct: true
    });

    // Process reports for display
    const processedReports = await Promise.all(
      reports.map(async (report: any) => {
        // Get program names for this report
        let programNames = {};
        let programIds = [];

        if (report.program_ids) {
          try {
            const parsedProgramIds = Array.isArray(report.program_ids)
              ? report.program_ids
              : JSON.parse(report.program_ids || "[]");

            if (
              Array.isArray(parsedProgramIds) &&
              parsedProgramIds.length > 0
            ) {
              programIds = parsedProgramIds;

              // Fetch program names from programs table
              const programs = await Programs.findAll({
                where: {
                  program_id: { [Op.in]: parsedProgramIds }
                },
                attributes: ["program_id", "program_title"]
              });

              // Create a map of program_id to program_title
              programNames = programs.reduce(
                (acc, program) => {
                  acc[program.program_id] = program.program_title;
                  return acc;
                },
                {} as Record<number, string>
              );
            }
          } catch (error) {
            console.error(
              "Error parsing program_ids for report",
              report.id,
              error
            );
          }
        }

        return {
          id: report.id,
          title: report.title,
          company_name: report.company_name,
          document_number: report.document_number,
          status: report.status,
          created_at: report.createdAt,
          updated_at: report.updatedAt,
          current_version: report.versions?.[0]?.version_number || 1,
          total_versions: report.versions?.length || 0,
          user: report.user
            ? {
                id: report.user.user_id,
                name: `${report.user.display_name}`,
                email: report.user.email
              }
            : null,
          // Add summary statistics
          total_findings: report.total_findings || 0,
          critical_count: report.critical_count || 0,
          high_count: report.high_count || 0,
          medium_count: report.medium_count || 0,
          low_count: report.low_count || 0,
          // Add program names and IDs
          program_names: programNames,
          program_ids: programIds
        };
      })
    );

    const totalPages = Math.ceil(count / parseInt(limit as string));

    const endTime = performance.now();
    logger.log({
      level: "info",
      label: "automated-report.controller | getAllPentestReports",
      message: `Retrieved ${processedReports.length} reports in ${(
        endTime - startTime
      ).toFixed(2)}ms for role ${role}`
    });

    return res.json({
      status: "success",
      data: {
        reports: processedReports,
        pagination: {
          current_page: parseInt(page as string),
          total_pages: totalPages,
          total_count: count,
          limit: parseInt(limit as string)
        }
      }
    });
  } catch (error) {
    logger.log({
      level: "error",
      label: "automated-report.controller | getAllPentestReports",
      message: `Error retrieving pentest reports: ${error.message}`
    });
    return res.status(500).json({
      status: "error",
      message: "500 INTERNAL ERROR: Unable to retrieve pentest reports"
    });
  }
};

// New endpoint to check if a program has an automated report
export const checkProgramAutomatedReport = async (
  req: CTBParamsRequest<{ program_id: string }>,
  res: Response
) => {
  try {
    const { program_id } = req.params;
    const { user_id, role } = req.user as UserAttributes;

    const log = {
      level: "info",
      label: "automated-report.controller | checkProgramAutomatedReport",
      message: `Checking automated report for program ${program_id} by user ${user_id}`
    };

    // Check if user has permission
    const allowedRoles = [
      UserRole.ADMIN,
      UserRole.ADMIN_MANAGER,
      UserRole.SUB_ADMIN,
      UserRole.QA,
      UserRole.BUSINESS
    ];

    if (!allowedRoles.includes(role)) {
      log.level = "warn";
      log.message = `Unauthorized access attempt by user ${user_id} with role ${role}`;
      logger.log(log);
      return res.status(403).json({
        status: "error",
        message:
          "403 FORBIDDEN: You don't have permission to access this endpoint"
      });
    }

    logger.log(log);

    // Build where condition based on user role
    const whereCondition: any = {};
    if (role === UserRole.BUSINESS) {
      whereCondition.business_id = user_id;
    }

    // Find automated reports that contain this program_id
    const reports = await AutomatedReports.findAll({
      where: {
        ...whereCondition,
        program_ids: Sequelize.literal(
          `JSON_CONTAINS(program_ids, '[${program_id}]')`
        )
      },
      attributes: [
        "id",
        "title",
        "company_name",
        "program_ids",
        "business_id",
        "status",
        "createdAt",
        "updatedAt"
      ],
      order: [["createdAt", "DESC"]]
    });

    // Check if there's an approved automated report
    const approvedReport = reports.find(report => report.status === "approved");

    // Check if there's a pending automated report (not approved)
    const pendingReport = reports.find(
      report =>
        report.status !== "approved" &&
        ["pending", "draft", "qa_review", "admin_review", "rejected"].includes(
          report.status
        )
    );

    logger.log({
      level: "info",
      label: "automated-report.controller | checkProgramAutomatedReport",
      message: `Check completed for program ${program_id}. Approved: ${!!approvedReport}, Pending: ${!!pendingReport}`
    });

    return res.status(200).json({
      status: "success",
      message: "Program automated report check completed",
      data: {
        hasApprovedReport: !!approvedReport,
        hasPendingReport: !!pendingReport,
        approvedReport: approvedReport
          ? {
              id: approvedReport.id,
              title: approvedReport.title,
              status: approvedReport.status
            }
          : null,
        pendingReport: pendingReport
          ? {
              id: pendingReport.id,
              title: pendingReport.title,
              status: pendingReport.status
            }
          : null,
        totalReports: reports.length
      }
    });
  } catch (error) {
    logger.log({
      level: "error",
      label: "automated-report.controller | checkProgramAutomatedReport",
      message: `Error checking program automated report: ${error.message}`
    });

    return res.status(500).json({
      status: "error",
      message: "500 INTERNAL ERROR: Unable to check program automated report"
    });
  }
};
