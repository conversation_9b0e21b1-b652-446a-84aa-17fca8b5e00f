import {
  ModalType,
  setModal,
  setPreventClose
} from "../store/reducer/slices/modalReducer";
import { useStoreDispatch, useStoreSelector } from "./hooks";

/**
 * Provides a layer of abstraction to the global modal
 * state for including a modal
 */
const useModal = (type: ModalType, id?: number | string) => {
  const dispatch = useStoreDispatch();
  const selectedModal = useStoreSelector(state => state.modal.selected);
  const selectedId = useStoreSelector(state => state.modal.id);

  return {
    modal: selectedModal === type && (id === undefined || selectedId === id),
    setModal: (show: boolean) => {
      if (show) {
        dispatch(setModal({ selected: type, id }));
      } else {
        dispatch(setModal({ selected: undefined }));
      }
    },
    preventClose: () => dispatch(setPreventClose(true))
  };
};

export default useModal;
