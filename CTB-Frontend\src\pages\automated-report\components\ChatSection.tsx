import React, { useState, useRef, useEffect } from 'react';
import axios from '../../../utils/api/axios';
import { getChatMessages, postChatMessage } from '../../../utils/api/endpoints/chat/chat';
import useUserCredentials from '../../../utils/hooks/user/useUserCredentials';

interface Message {
  id: number;
  sender_id: number;
  sender_username: string;
  message: string;
  created_at: string;
}

interface ChatSectionProps {
  programReportId: string;
  chatType: 'admin_business' | 'admin_qa' | 'admin_subadmin';
}

// Utility to get initials from username
function getInitials(name: string) {
  return name
    .split(' ')
    .map(part => part[0]?.toUpperCase() || '')
    .join('')
    .slice(0, 2);
}

// Utility to pick a color from a palette based on username
const avatarColors = [
  'bg-blue-400', 'bg-green-400', 'bg-pink-400', 'bg-yellow-400', 'bg-purple-400', 'bg-teal-400', 'bg-orange-400', 'bg-indigo-400', 'bg-red-400', 'bg-violet-400'
];
function getAvatarColor(name: string) {
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  return avatarColors[Math.abs(hash) % avatarColors.length];
}

const ChatSection: React.FC<ChatSectionProps> = ({ programReportId, chatType }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Use user credentials hook
  const { id: currentUserId, role: currentUserRole } = useUserCredentials();

  useEffect(() => {
    setLoading(true);
    setError(null);
    getChatMessages(programReportId, chatType, (currentUserRole || '').toString())
      .then((data) => {
        if (data.success) {
          setMessages(data.data);
        } else {
          setError(data.message || 'Failed to load messages');
        }
        setLoading(false);
      })
      .catch(() => {
        setError('Failed to load messages');
        setLoading(false);
      });
  }, [programReportId, chatType, currentUserRole]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // For BUSINESS users, programReportId should be the automated_report.id
  React.useEffect(() => {
    if ((currentUserRole || '').toString().toUpperCase() === 'BUSINESS' && programReportId && programReportId.includes('-')) {
      // UUIDs are usually program_report.id, not automated_report.id (if your system uses different formats)
      // You may want to warn or ensure the correct id is passed
      // eslint-disable-next-line no-console
      console.warn('BUSINESS user: programReportId should be automated_report.id, not program_report.id');
    }
  }, [currentUserRole, programReportId]);

  const handleSend = async () => {
    if (!input.trim()) return;
    setSending(true);
    setError(null);
    try {
      const data = await postChatMessage(programReportId, chatType, input.trim(), (currentUserRole || '').toString());
      if (data.success) {
        setMessages(prev => [...prev, data.data]);
        setInput('');
      } else {
        setError(data.message || 'Failed to send message');
      }
    } catch {
      setError('Failed to send message');
    }
    setSending(false);
  };

  return (
    <div className="flex flex-col h-full border rounded-lg bg-white shadow-md">
      <div className="flex-1 min-h-0 overflow-y-auto p-4 space-y-2 custom-scrollbar hover:scrollbar-thumb-blue-200 transition-all duration-200 bg-gradient-to-br from-blue-50 via-white to-gray-50 rounded-b-lg">
        {loading ? (
          <div className="text-center text-gray-400">Loading messages...</div>
        ) : error ? (
          <div className="text-center text-red-500">{error}</div>
        ) : messages.length === 0 ? (
          <div className="text-center text-gray-400">No messages yet.</div>
        ) : (
          messages.map((msg, idx) => {
            const initials = getInitials(msg.sender_username || 'U');
            const avatarColor = getAvatarColor(msg.sender_username || 'U');
            const isCurrentUser = msg.sender_id === currentUserId;
            return (
              <div key={msg.id || idx} className={`flex items-end gap-2 ${isCurrentUser ? 'justify-end' : 'justify-start'}`}>
                {/* Avatar for other user on left, for self on right */}
                {!isCurrentUser && (
                  <div className={`w-7 h-7 flex items-center justify-center rounded-full text-xs font-bold text-white shadow-sm select-none ${avatarColor}`} title={msg.sender_username}>
                    {initials}
                  </div>
                )}
                <div
                  className={`max-w-xs px-3 py-1.5 text-xs font-normal rounded-xl transition-all duration-200
                    ${isCurrentUser
                      ? 'bg-blue-100 text-blue-900'
                      : 'bg-violet-100 text-violet-900'}
                  `}
                  style={{ wordBreak: 'break-word', lineHeight: 1.4 }}
                >
                  <div className="text-[10px] mb-0.5 opacity-60">{msg.sender_username}</div>
                  <div>{msg.message}</div>
                  <div className="text-[9px] text-gray-400 mt-1 text-right font-mono">
                    {new Date(msg.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                </div>
                {isCurrentUser && (
                  <div className={`w-7 h-7 flex items-center justify-center rounded-full text-xs font-bold text-white shadow-sm select-none ${avatarColor}`} title={msg.sender_username}>
                    {initials}
                  </div>
                )}
              </div>
            );
          })
        )}
        <div ref={messagesEndRef} />
      </div>
      {/* Input area is outside the scrollable message area, always visible at the bottom */}
      <div className="flex items-center border-t p-2 bg-gray-50 shrink-0">
        <input
          className="flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring"
          type="text"
          placeholder="Type a message..."
          value={input}
          onChange={e => setInput(e.target.value)}
          onKeyDown={e => { if (e.key === 'Enter' && !sending) handleSend(); }}
          disabled={sending}
        />
        <button
          className="ml-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition disabled:opacity-50"
          onClick={handleSend}
          disabled={sending}
        >
          Send
        </button>
      </div>
    </div>
  );
};

export default ChatSection; 