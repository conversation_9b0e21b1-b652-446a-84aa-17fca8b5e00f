import toast from "react-hot-toast";
import axios from "../axios";
import { ReportCommentAPIresponse } from "./reports/parseReports";

export const getReportComments = async (reportId: number | undefined) => {
  try {
    if (!reportId) {
      return;
    }
    const res = await axios.get(`/v2/reports/comments/?reportId=${reportId}`);
    if (res.status === 200) {
      const data: object[] = [];
      res.data.forEach((item: ReportCommentAPIresponse) => {
        data.push(item);
      });
      return data;
    }
  } catch (error) {
    console.log("error at /v2/reports/comments");
    console.log(error);
    toast.error("An error occured, please try again");
  }
};

export const createReportComment = async (
  reportId: number,
  userComment: string
) => {
  try {
    if (!reportId) {
      toast.error("ERROR TRY AGAIN");
      return;
    } else {
      if (userComment.length === 0) {
        toast.error("Cannot send empty as comment");
      } else {
        const res = await axios.post(`/v2/reports/comments`, {
          id: reportId,
          comment: userComment
        });
        res.status === 200
          ? toast.success("Added new comment")
          : toast.error("Server side error, try again");
      }
    }
  } catch (error) {
    console.log("error at /v2/reports/comments -  post comment");
    console.log(error);
    toast.error("An error occured, please try again");
  }
};

export const deleteReportComment = async (commentId: number | undefined) => {
  try {
    if (!commentId) {
      toast.error("something went wrong try again");
    } else {
      const res = await axios.delete(`/v2/reports/comments/delete-comment`, {
        data: { comment_id: commentId }
      });
      res.status === 200
        ? toast.success("Deleted comment")
        : toast.error("server side error, try again");
    }
  } catch (error) {
    console.log("error at /v2/reports/comments/delete-comments");
    console.log(error);
    toast.error("An error occured, please try again");
  }
};

export const editReportComment = async (commentId: number, comment: string) => {
  try {
    if (!commentId) {
      toast.error("Something went wrong, Please try again");
      return;
    } else {
      if (comment.length === 0) {
        toast.error("Cannot edit comment to empty. Did you mean to Delete? ");
      } else {
        const res = await axios.post("v2/reports/comments/edit-comment", {
          comment_id: commentId,
          newComment: comment
        });
        res.status === 200
          ? toast.success("Comment edited")
          : toast.error("Server side error, please try again");
      }
    }
  } catch (error) {
    console.log("error at /v2/reports/comments/edit-comment");
    console.log(error);
    toast.error("An error occured, please try again");
  }
};
