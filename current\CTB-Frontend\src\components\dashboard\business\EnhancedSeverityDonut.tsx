import React, { useState, useCallback, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { PieChart, Pie, Cell, ResponsiveContainer, Sector } from "recharts";
import { SeverityOverview, ProgramSeverityData } from "../../../utils/hooks/dashboard/useSeverityAndTrend";
import useFilteredSeverityOverview from "../../../utils/hooks/dashboard/useFilteredSeverityOverview";

// Utility function to correct potentially inconsistent data
const normalizeSeverityData = (severityData: SeverityOverview): SeverityOverview => {
  const correctedData = { ...severityData };
  
  // Ensure businessResolved never exceeds total for any category
  (["critical", "high", "medium", "low"] as const).forEach(category => {
    if (correctedData[category].businessResolved > correctedData[category].total) {
      correctedData[category] = {
        ...correctedData[category],
        businessResolved: correctedData[category].total
      };
    }
  });
  
  return correctedData;
};

// Define tooltip state interface
interface TooltipInfo {
  activeIndex: number | null;
  category?: string;
  isVisible: boolean;
  position: { x: number; y: number };
}

interface EnhancedSeverityDonutProps {
  data: {
    total: SeverityOverview;
    programs: ProgramSeverityData[];
  };
}

// Define chart data type
interface ChartDataItem {
  name: string;
  value: number;
  color: string;
  type: "main" | "businessResolved";
  category: "critical" | "high" | "medium" | "low";
}

// Colors for severity levels with premium gradients
const COLORS = {
  critical: {
    main: "rgb(220, 38, 38)", // Red-600
    businessResolved: "rgb(254, 202, 202)" // Red-200
  },
  high: {
    main: "rgb(234, 88, 12)", // Orange-600
    businessResolved: "rgb(254, 215, 170)" // Orange-200
  },
  medium: {
    main: "rgb(202, 138, 4)", // Yellow-600
    businessResolved: "rgb(254, 240, 138)" // Yellow-200
  },
  low: {
    main: "rgb(22, 163, 74)", // Green-600
    businessResolved: "rgb(187, 247, 208)" // Green-200
  }
};

// View type dropdown options
const viewOptions = [
  { value: "all", label: "All Issues" },
  { value: "active", label: "Active Only" },
  { value: "businessResolved", label: "Accepted Risk" }
];

const EnhancedSeverityDonut: React.FC<EnhancedSeverityDonutProps> = ({ data: initialData }) => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [activeProgramIndex, setActiveProgramIndex] = useState(0);
  const [chartData, setChartData] = useState<ChartDataItem[]>(() => processData(initialData.total));
  const [totalCount, setTotalCount] = useState(() => 
    Object.values(initialData.total).reduce((sum, item) => sum + item.total, 0)
  );
  const [currentView, setCurrentView] = useState("all");
  const [viewDropdownOpen, setViewDropdownOpen] = useState(false);
  const [programDropdownOpen, setProgramDropdownOpen] = useState(false);
  const [tooltipInfo, setTooltipInfo] = useState<TooltipInfo>({
    activeIndex: null,
    isVisible: false,
    position: { x: 0, y: 0 }
  });
  
  // Use refs to avoid re-renders when updating position
  const tooltipPositionRef = useRef({ x: 0, y: 0 });
  const tooltipElementRef = useRef<HTMLDivElement | null>(null);
  const viewDropdownRef = useRef<HTMLDivElement>(null);
  const programDropdownRef = useRef<HTMLDivElement>(null);
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const programsRef = useRef<HTMLDivElement>(null);
  
  // Get filtered data for accepted risk view
  const { data: acceptedRiskData, loading: acceptedRiskLoading } = useFilteredSeverityOverview(
    currentView === "businessResolved" ? 'accepted-risk' : 'all'
  );
  
  // Use the appropriate data based on the current view
  const data = currentView === "businessResolved" && acceptedRiskData ? acceptedRiskData : initialData;

  // Update tooltip position without state changes
  const updateTooltipPosition = useCallback((x: number, y: number) => {
    if (tooltipElementRef.current && tooltipInfo.isVisible) {
      tooltipPositionRef.current = { x, y };
      tooltipElementRef.current.style.transform = `translate(calc(${x}px - 50%), calc(${y - 10}px - 100%))`;
    }
  }, [tooltipInfo.isVisible]);

  // Get data for the current program selection
  const getCurrentProgramData = useCallback(() => {
    // If there are no programs, use the total
    if (!data.programs || data.programs.length === 0) {
      return normalizeSeverityData(data.total);
    }
    
    // If "all programs" view is selected (index 0), use the total data
    if (activeProgramIndex === 0) {
      return normalizeSeverityData(data.total);
    }
    
    // Otherwise, return the selected program's data (adjusted for 0-based index)
    return normalizeSeverityData(data.programs[activeProgramIndex - 1].severityOverview);
  }, [data, activeProgramIndex]);

  // Filter data based on current view
  const getFilteredData = useCallback(() => {
    const currentProgram = getCurrentProgramData();
    
    if (currentView === "businessResolved") {
      // For accepted risk view, only include businessResolved segments with value > 0
      const filteredData = processData(currentProgram).filter(item => 
        item.type === "businessResolved" && item.value > 0
      );
      return filteredData.length ? filteredData : [
        {
          name: "No Data",
          value: 1,
          color: "#CBD5E1",
          type: "main" as const,
          category: "low" as const
        }
      ];
    } else if (currentView === "active") {
      // For active view, only show main items (not business resolved)
      const filteredData = processData(currentProgram).filter(item => item.type === "main" && item.value > 0);
      return filteredData.length ? filteredData : [
        {
          name: "No Data",
          value: 1,
          color: "#CBD5E1",
          type: "main" as const,
          category: "low" as const
        }
      ];
    } else {
      // For all view, show everything with value > 0
      return processData(currentProgram);
    }
  }, [currentView, getCurrentProgramData]);

  // Update chart data when program or view changes
  useEffect(() => {
    const filtered = getFilteredData();
    setChartData(filtered);
    
    // Also update total count for display in center
    const currentProgram = getCurrentProgramData();
    
    // Calculate total count based on the view
    if (currentView === "businessResolved") {
      // For accepted risk view, we only want to count the business resolved items
      const newTotal = Object.values(currentProgram).reduce((sum, item) => sum + item.businessResolved, 0);
      setTotalCount(newTotal);
    } else if (currentView === "active") {
      // For active view, we need to exclude both business resolved AND truly resolved items
      const newTotal = Object.values(currentProgram).reduce(
        (sum, item) => {
          // Calculate active issues: total - (businessResolved + realResolved)
          const businessResolved = item.businessResolved || 0;
          // Use realResolved if available, otherwise default to 0
          const realResolved = item.realResolved || 0;
          // Active = Total - (BusinessResolved + RealResolved)
          return sum + Math.max(0, item.total - businessResolved - realResolved);
        }, 0
      );
      setTotalCount(newTotal);
    } else {
      // For all view, we count everything
      const newTotal = Object.values(currentProgram).reduce((sum, item) => sum + item.total, 0);
      setTotalCount(newTotal);
    }
  }, [getCurrentProgramData, getFilteredData, data, currentView, activeProgramIndex]);
  
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (viewDropdownRef.current && !viewDropdownRef.current.contains(event.target as Node)) {
        setViewDropdownOpen(false);
      }
      if (programDropdownRef.current && !programDropdownRef.current.contains(event.target as Node)) {
        setProgramDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Handle view change
  const handleViewChange = (view: string) => {
    setCurrentView(view);
    setViewDropdownOpen(false);
    
    // Reset tooltip and active segment
    setTooltipInfo({
      activeIndex: null,
      isVisible: false,
      position: { x: 0, y: 0 }
    });
    setActiveIndex(null);
    
    // Update the chart data and total count immediately for better UX
    // If a specific program is selected, use that program's data instead of total
    const currentProgram = activeProgramIndex === 0 
      ? data.total
      : data.programs[activeProgramIndex - 1]?.severityOverview || data.total;
    
    if (view === "businessResolved") {
      // For accepted risk view, we want to count only business resolved items
      const newTotal = Object.values(currentProgram).reduce((sum, item) => sum + item.businessResolved, 0);
      setTotalCount(newTotal);
    } else if (view === "active") {
      // For active view, we need to exclude both business resolved AND actual resolved items
      const newTotal = Object.values(currentProgram).reduce(
        (sum, item) => {
          // Calculate active issues: total - (businessResolved + realResolved)
          const businessResolved = item.businessResolved || 0;
          // Use realResolved if available, otherwise default to 0
          const realResolved = item.realResolved || 0;
          // Active = Total - (BusinessResolved + RealResolved)
          return sum + Math.max(0, item.total - businessResolved - realResolved);
        }, 0
      );
      setTotalCount(newTotal);
    } else {
      // For all view, we count everything
      const newTotal = Object.values(currentProgram).reduce((sum, item) => sum + item.total, 0);
      setTotalCount(newTotal);
    }
  };
  
  // Handle program change from dropdown
  const handleProgramChange = (index: number) => {
    setActiveProgramIndex(index);
    setProgramDropdownOpen(false);
    
    // Reset tooltip and active segment
    setTooltipInfo({
      activeIndex: null,
      isVisible: false,
      position: { x: 0, y: 0 }
    });
    setActiveIndex(null);
  };

  // Handle mouse movement
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (chartContainerRef.current) {
      const rect = chartContainerRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      updateTooltipPosition(x, y);
    }
  }, [updateTooltipPosition]);

  // Handle pie segment hover
  const onPieEnter = useCallback((_: any, index: number) => {
    setActiveIndex(index);
    if (index !== null && index >= 0 && chartData[index]) {
      // Only update the tooltip info when we hover on a new segment
      if (chartContainerRef.current) {
        const rect = chartContainerRef.current.getBoundingClientRect();
        // Initial position at the center of the chart
        const x = rect.width / 2;
        const y = rect.height / 2;
        
        setTooltipInfo({
          activeIndex: index,
          category: chartData[index].category,
          isVisible: true,
          position: { x, y }
        });
        
        // Also update the ref
        tooltipPositionRef.current = { x, y };
      }
    }
  }, [chartData]);

  const onPieLeave = useCallback(() => {
    setActiveIndex(null);
    setTooltipInfo(prev => ({
      ...prev,
      activeIndex: null,
      isVisible: false
    }));
  }, []);

  // Custom tooltip component with premium styling
  const CustomTooltip = ({ index }: { index: number | null }) => {
    if (index === null || !tooltipInfo.isVisible || index >= chartData.length) return null;
    
    const data = chartData[index];
    
    // Don't show tooltip for "No Data" placeholder
    if (data.name === "No Data") return null;
  
    // Get current program data to access the raw counts
    const currentProgram = getCurrentProgramData();
    const categoryData = currentProgram[data.category];
  
    // Calculate the different counts
    const activeCount = Math.max(0, categoryData.total - (categoryData.businessResolved || 0) - (categoryData.realResolved || 0));
    const resolvedCount = categoryData.realResolved || 0;
    const acceptedRiskCount = categoryData.businessResolved || 0;
  
    return (
      <div 
        ref={tooltipElementRef}
        className="absolute pointer-events-none z-[100] left-0 top-0"
        style={{
          transform: `translate(calc(${tooltipInfo.position.x}px - 50%), calc(${tooltipInfo.position.y - 10}px - 100%))`,
          transition: 'transform 0.05s ease-out'
        }}
      >
        <motion.div 
          className="backdrop-blur-sm rounded-lg bg-gray-900/90 p-4 text-white shadow-xl border border-gray-700"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ type: "spring", damping: 20, stiffness: 300 }}
          style={{ maxWidth: '250px' }}
          layoutId={`tooltip-${data.category}`}
        >
          <p className="text-lg font-semibold border-b border-gray-700 pb-2 mb-2">
            {data.category.charAt(0).toUpperCase() + data.category.slice(1)}
          </p>
          <div className="space-y-1">
            <p className="flex justify-between text-sm">
              <span>Total:</span>
              <span className="font-bold ml-4">{categoryData.total}</span>
            </p>
            {activeCount > 0 && (
              <p className="flex justify-between text-sm text-gray-300">
                <span>Active:</span>
                <span className="font-bold ml-4">{activeCount}</span>
              </p>
            )}
            {resolvedCount > 0 && (
              <p className="flex justify-between text-sm text-gray-300">
                <span>Resolved:</span>
                <span className="font-bold ml-4">{resolvedCount}</span>
              </p>
            )}
            {acceptedRiskCount > 0 && (
              <p className="flex justify-between text-sm text-gray-300">
                <span>Accepted Risk:</span>
                <span className="font-bold ml-4">{acceptedRiskCount}</span>
              </p>
            )}
          </div>
        </motion.div>
      </div>
    );
  };
  
  return (
    <motion.div
      className="relative w-full"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="mb-6 flex flex-col justify-between gap-3 sm:flex-row sm:items-center">
        <div className="flex-1 min-w-0 overflow-hidden pr-2">
          <h3 className="text-xl font-semibold text-gray-900 mb-2 relative group">
            <span className="truncate block pr-4" title={activeProgramIndex === 0 
              ? "Severity Distribution" 
              : `${data.programs[activeProgramIndex - 1]?.name || "Unknown Program"} Severity`}>
              {activeProgramIndex === 0 
                ? "Severity Distribution" 
                : `${data.programs[activeProgramIndex - 1]?.name || "Unknown Program"} Severity`}
            </span>
            <span className="absolute right-0 top-0 h-full w-8 bg-gradient-to-l from-white to-transparent pointer-events-none"></span>
          </h3>
          <p className="text-sm text-gray-500 truncate" title={currentView === "businessResolved"
              ? "Distribution of accepted risk issues by severity"
              : currentView === "active"
                ? "Distribution of active issues by severity"
                : "Distribution of all issues by severity"}>
            {currentView === "businessResolved"
              ? "Distribution of accepted risk issues by severity"
              : currentView === "active"
                ? "Distribution of active issues by severity"
                : "Distribution of all issues by severity"
            }
          </p>
        </div>
        
        <div className="flex gap-2">
          {/* Program selection dropdown */}
          {data.programs && data.programs.length > 0 && (
            <div className="relative z-10" ref={programDropdownRef}>
              <button
                onClick={() => setProgramDropdownOpen(!programDropdownOpen)}
                className="flex items-center gap-2 rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-all"
              >
                <span className="truncate max-w-[120px]" title={activeProgramIndex === 0 
                  ? "All Programs" 
                  : data.programs[activeProgramIndex - 1]?.name || "Unknown Program"}>
                  {activeProgramIndex === 0 
                    ? "All Programs" 
                    : data.programs[activeProgramIndex - 1]?.name || "Unknown Program"}
                </span>
                <svg
                  className={`h-4 w-4 flex-shrink-0 transition-transform ${programDropdownOpen ? 'rotate-180' : ''}`}
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              
              <AnimatePresence>
                {programDropdownOpen && (
                  <motion.div
                    initial={{ opacity: 0, y: -10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -10, scale: 0.95 }}
                    transition={{ type: "spring", damping: 20, stiffness: 300 }}
                    className="absolute right-0 mt-2 w-80 rounded-lg border border-gray-200 bg-white shadow-lg backdrop-blur-sm max-h-96 overflow-y-auto"
                  >
                    <div className="py-1">
                      {/* All Programs option */}
                      <button
                        key="all-programs"
                        onClick={() => handleProgramChange(0)}
                        className={`w-full px-4 py-2 text-left text-sm ${
                          activeProgramIndex === 0
                            ? "bg-blue-50 text-blue-700 font-medium"
                            : "text-gray-700 hover:bg-gray-50"
                        } transition-colors`}
                      >
                        {activeProgramIndex === 0 && (
                          <span className="mr-2">✓</span>
                        )}
                        All Programs
                      </button>
                      
                      {/* Individual program options */}
                      {data.programs.map((program, idx) => (
                        <button
                          key={program.program_id}
                          onClick={() => handleProgramChange(idx + 1)}
                          className={`w-full px-4 py-2 text-left text-sm flex items-start ${
                            activeProgramIndex === idx + 1
                              ? "bg-blue-50 text-blue-700 font-medium"
                              : "text-gray-700 hover:bg-gray-50"
                          } transition-colors`}
                          title={program.name}
                        >
                          <span className="mr-2 flex-shrink-0 mt-0.5">
                            {activeProgramIndex === idx + 1 && "✓"}
                          </span>
                          <span className="line-clamp-2 break-all truncate">{program.name}</span>
                        </button>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          )}
          
          {/* View type dropdown */}
          <div className="relative z-10" ref={viewDropdownRef}>
            <button
              onClick={() => setViewDropdownOpen(!viewDropdownOpen)}
              className="flex items-center gap-2 rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-all"
            >
              {viewOptions.find(opt => opt.value === currentView)?.label}
              <svg
                className={`h-4 w-4 transition-transform ${viewDropdownOpen ? 'rotate-180' : ''}`}
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            
            <AnimatePresence>
              {viewDropdownOpen && (
                <motion.div
                  initial={{ opacity: 0, y: -10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -10, scale: 0.95 }}
                  transition={{ type: "spring", damping: 20, stiffness: 300 }}
                  className="absolute right-0 mt-2 w-48 rounded-lg border border-gray-200 bg-white shadow-lg backdrop-blur-sm"
                >
                  <div className="py-1">
                    {viewOptions.map((option) => (
                      <button
                        key={option.value}
                        onClick={() => handleViewChange(option.value)}
                        className={`w-full px-4 py-2 text-left text-sm ${
                          currentView === option.value
                            ? "bg-blue-50 text-blue-700 font-medium"
                            : "text-gray-700 hover:bg-gray-50"
                        } transition-colors`}
                      >
                        {currentView === option.value && (
                          <span className="mr-2">✓</span>
                        )}
                        {option.label}
                      </button>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Program carousel - only show if there are programs */}
      {data.programs && data.programs.length > 0 && (
        <motion.div 
          className="mb-4 relative"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.2 }}
          ref={programsRef}
        >
          <motion.div className="flex items-center justify-between px-2">
            <button 
              onClick={() => setActiveProgramIndex(prev => (prev === 0 ? data.programs.length : prev - 1))}
              className="flex items-center justify-center h-8 w-8 rounded-full bg-gray-200 hover:bg-gray-300 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex-shrink-0 mr-1"
              disabled={!data.programs || data.programs.length <= 0}
            >
              <svg className="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            
            <AnimatePresence mode="wait">
              <motion.div
                key={activeProgramIndex}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="text-center font-medium text-gray-800 mx-2 flex-1 min-w-0 relative"
              >
                <div 
                  className="truncate px-1 py-1" 
                  title={activeProgramIndex === 0 ? "All Programs" : data.programs[activeProgramIndex - 1]?.name || "Unknown Program"}
                >
                  {activeProgramIndex === 0 ? "All Programs" : data.programs[activeProgramIndex - 1]?.name || "Unknown Program"}
                </div>
                <div className="absolute top-0 right-0 h-full w-8 bg-gradient-to-l from-white to-transparent pointer-events-none"></div>
                <div className="absolute top-0 left-0 h-full w-8 bg-gradient-to-r from-white to-transparent pointer-events-none"></div>
              </motion.div>
            </AnimatePresence>
            
            <button 
              onClick={() => setActiveProgramIndex(prev => (prev + 1) % (data.programs.length + 1))}
              className="flex items-center justify-center h-8 w-8 rounded-full bg-gray-200 hover:bg-gray-300 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex-shrink-0 ml-1"
              disabled={!data.programs || data.programs.length <= 0}
            >
              <svg className="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </motion.div>
          
          {/* Program indicators */}
          {data.programs && data.programs.length > 0 && (
            <div className="flex justify-center mt-3 space-x-1">
              {/* Add indicator for "All Programs" view */}
              <motion.button
                key="all"
                onClick={() => setActiveProgramIndex(0)}
                className={`h-2 w-2 rounded-full transition-colors ${
                  activeProgramIndex === 0 ? 'bg-blue-500' : 'bg-gray-300'
                }`}
                whileHover={{ scale: 1.2 }}
                whileTap={{ scale: 0.9 }}
              />
              
              {data.programs.map((_, idx) => (
                <motion.button
                  key={idx}
                  onClick={() => setActiveProgramIndex(idx + 1)}
                  className={`h-2 w-2 rounded-full transition-colors ${
                    idx + 1 === activeProgramIndex ? 'bg-blue-500' : 'bg-gray-300'
                  }`}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                />
              ))}
            </div>
          )}
        </motion.div>
      )}

      {/* Legend - Moved to the top */}
      <div className="flex flex-wrap justify-center gap-4 mb-4">
        {chartData.length > 0 && [...new Set(chartData.map(item => item.category))].map(category => (
          <div key={category} className="flex items-center text-sm">
            <div 
              className="h-3 w-3 rounded-full mr-1.5"
              style={{ backgroundColor: COLORS[category as keyof typeof COLORS].main }}
            />
            <span className="text-gray-700 capitalize">{category}</span>
          </div>
        ))}
      </div>

      <motion.div 
        className="relative h-[320px]"
        ref={chartContainerRef}
      >
        {acceptedRiskLoading && currentView === "businessResolved" ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <div 
            className="relative w-full h-full"
            onMouseMove={handleMouseMove}
            onMouseLeave={onPieLeave}
          >
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={chartData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={1}
                  dataKey="value"
                  startAngle={90}
                  endAngle={-270}
                  onMouseEnter={onPieEnter}
                  onMouseLeave={onPieLeave}
                  activeIndex={activeIndex !== null ? activeIndex : undefined}
                  activeShape={renderActiveShape}
                  animationBegin={0}
                  animationDuration={1200}
                  animationEasing="ease-out"
                >
                  {chartData.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`}
                      fill={`url(#radial-gradient-${entry.category}-${entry.type})`}
                      stroke="none"
                    />
                  ))}
                </Pie>
                
                {/* Add gradient definitions */}
                <defs>
                  {Object.keys(COLORS).map(category => (
                    <React.Fragment key={category}>
                      <radialGradient
                        id={`radial-gradient-${category}-main`}
                        cx="50%"
                        cy="50%"
                        r="70%"
                        fx="50%"
                        fy="50%"
                      >
                        <stop
                          offset="0%"
                          stopColor={COLORS[category as keyof typeof COLORS].main}
                          stopOpacity={0.9}
                        />
                        <stop
                          offset="100%"
                          stopColor={COLORS[category as keyof typeof COLORS].main}
                          stopOpacity={0.7}
                        />
                      </radialGradient>
                      <radialGradient
                        id={`radial-gradient-${category}-businessResolved`}
                        cx="50%"
                        cy="50%"
                        r="70%"
                        fx="50%"
                        fy="50%"
                      >
                        <stop
                          offset="0%"
                          stopColor={COLORS[category as keyof typeof COLORS].businessResolved}
                          stopOpacity={0.9}
                        />
                        <stop
                          offset="100%"
                          stopColor={COLORS[category as keyof typeof COLORS].businessResolved}
                          stopOpacity={0.7}
                        />
                      </radialGradient>
                    </React.Fragment>
                  ))}
                </defs>
              </PieChart>
            </ResponsiveContainer>
            
            {/* Total issues display in center of donut */}
            <div 
              className="absolute inset-0 flex flex-col items-center justify-center pointer-events-none"
              style={{ top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }}
            >
              <AnimatePresence mode="wait">
                <motion.div
                  key={`${activeProgramIndex}-${totalCount}-${currentView}`}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.4 }}
                  className="text-center w-[120px]"
                >
                  <div className="text-4xl font-bold text-gray-800">{totalCount}</div>
                  <div className="text-xs sm:text-sm text-gray-500 truncate px-1" title={
                    currentView === "businessResolved" 
                      ? "Accepted Risk" 
                      : currentView === "active"
                        ? "Active Issues"
                        : "Total Issues"
                  }>
                    {currentView === "businessResolved" 
                      ? "Accepted Risk" 
                      : currentView === "active"
                        ? "Active Issues"
                        : "Total Issues"
                    }
                  </div>
                </motion.div>
              </AnimatePresence>
            </div>
            
            {/* Render tooltip that follows cursor without flickering */}
            <AnimatePresence>
              {tooltipInfo.isVisible && (
                <CustomTooltip index={tooltipInfo.activeIndex} />
              )}
            </AnimatePresence>
          </div>
        )}
      </motion.div>
    </motion.div>
  );
};

// Process data for the chart with the new structure
const processData = (severityData: SeverityOverview): ChartDataItem[] => {
  const result: ChartDataItem[] = [];
  const categories = ["critical", "high", "medium", "low"] as const;

  categories.forEach(category => {
    const data = severityData[category];
    
    // Calculate the active count (total - businessResolved - realResolved)
    const businessResolved = data.businessResolved || 0;
    const realResolved = data.realResolved || 0;
    
    // Active issues - using main color
    const activeCount = Math.max(0, data.total - businessResolved - realResolved);
    
    // Combine active and realResolved into the main segment with the same color
    const mainCount = activeCount + realResolved;
    
    // Only add segments with value > 0
    if (mainCount > 0) {
      result.push({
        name: `${category.charAt(0).toUpperCase() + category.slice(1)}`,
        value: mainCount,
        color: COLORS[category].main,
        type: "main",
        category
      });
    }

    // Add business resolved segment (accepted risk) only if > 0 with different shade
    if (businessResolved > 0) {
      result.push({
        name: `${category.charAt(0).toUpperCase() + category.slice(1)} (Accepted Risk)`,
        value: businessResolved,
        color: COLORS[category].businessResolved,
        type: "businessResolved",
        category
      });
    }
  });

  // If no data, add a placeholder
  if (result.length === 0) {
    result.push({
      name: "No Data",
      value: 1,
      color: "#CBD5E1", // slate-300
      type: "main",
      category: "low"
    });
  }

  return result;
};

// Enhanced active shape with glow effect
const renderActiveShape = (props: any) => {
  const { cx, cy, innerRadius, outerRadius, startAngle, endAngle, fill } = props;
  
  // Add gradient definition for the glow effect
  const gradientId = `activeSliceGradient-${fill.replace('#', '')}`;

  return (
    <g>
      <defs>
        <filter id="glow">
          <feGaussianBlur stdDeviation="3.5" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor={fill} stopOpacity={0.9}/>
          <stop offset="100%" stopColor={fill} stopOpacity={0.6}/>
        </linearGradient>
      </defs>
      
      {/* Outer glow effect */}
      <Sector
        cx={cx}
        cy={cy}
        innerRadius={innerRadius - 1}
        outerRadius={outerRadius + 8}
        startAngle={startAngle}
        endAngle={endAngle}
        fill={`url(#${gradientId})`}
        filter="url(#glow)"
      />
      
      {/* Main sector with slightly increased size */}
      <Sector
        cx={cx}
        cy={cy}
        innerRadius={innerRadius}
        outerRadius={outerRadius + 4}
        startAngle={startAngle}
        endAngle={endAngle}
        fill={fill}
      />
      
      {/* Inner ring highlight */}
      <Sector
        cx={cx}
        cy={cy}
        startAngle={startAngle}
        endAngle={endAngle}
        innerRadius={innerRadius - 3}
        outerRadius={innerRadius - 1}
        fill={fill}
      />
    </g>
  );
};

export default EnhancedSeverityDonut; 