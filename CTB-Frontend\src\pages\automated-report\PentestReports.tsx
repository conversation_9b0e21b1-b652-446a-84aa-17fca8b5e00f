import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { PentestReport, PentestReportsFilters } from './types';
import { getPentestReports } from '../../utils/api/endpoints/automated-report/automated-report';
import useUserCredentials from '../../utils/hooks/user/useUserCredentials';
import { UserRole } from '../../utils/api/endpoints/user/credentials';

const PentestReports: React.FC = () => {
  const navigate = useNavigate();
  const { role } = useUserCredentials();
  const [reports, setReports] = useState<PentestReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [pagination, setPagination] = useState({
    current_page: 1,
    total_pages: 1,
    total_count: 0,
    limit: 12
  });

  const [filters, setFilters] = useState<PentestReportsFilters>({
    page: 1,
    limit: 12,
    sort_by: 'createdAt',
    sort_order: 'DESC'
  });

  // Dynamic status options
  const [availableStatuses, setAvailableStatuses] = useState<string[]>([]);

  // Count active filters
  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.status) count++;
    if (filters.company_name) count++;
    if (filters.date_from) count++;
    if (filters.date_to) count++;
    return count;
  };

  // Filter reports based on user role and status
  const getVisibleReports = (allReports: PentestReport[]): PentestReport[] => {
    if (!allReports) return [];
    
    switch (role) {
      case UserRole.QA:
        return allReports.filter(report =>
          [
            'draft',
            'qa_review',
            'business_requested_changes',
            'rejected'
          ].includes(report.status)
        );
      case UserRole.ADMIN:
      case UserRole.SUB_ADMIN:
      case UserRole.ADMIN_MANAGER:
        return allReports.filter(report =>
          [
            'qa_review',
            'admin_review',
            'approved',
            'rejected',
            'business_review',
            'business_requested_changes',
            'changes_added',
            'report_updated',
            'business_approved'
          ].includes(report.status)
        );
      default:
        return allReports;
    }
  };

  const formatDate = (dateString: string): string => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };

  const fetchReports = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getPentestReports(filters);
      if (response.status === 'success') {
        const visibleReports = getVisibleReports(response.data.reports);
        setReports(visibleReports);
        setPagination(response.data.pagination);
        // Extract unique statuses from visible reports
        const uniqueStatuses = Array.from(
          new Set(visibleReports.map(r => r.status).filter(Boolean))
        );
        setAvailableStatuses(uniqueStatuses);
      } else {
        setError(response.message || 'Failed to fetch reports');
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch reports');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReports();
  }, [filters, role]);

  const handleFilterChange = (newFilters: Partial<PentestReportsFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: 1
    }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const clearAllFilters = () => {
    setFilters(prev => ({
      ...prev,
      status: undefined,
      company_name: undefined,
      date_from: undefined,
      date_to: undefined,
      page: 1
    }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gradient-to-r from-slate-100 to-slate-200 text-slate-700 border-slate-300';
      case 'qa_review':
        return 'bg-gradient-to-r from-yellow-50 to-amber-100 text-amber-700 border-amber-300';
      case 'admin_review':
        return 'bg-gradient-to-r from-blue-50 to-blue-100 text-blue-800 border-blue-300';
      case 'approved':
        return 'bg-gradient-to-r from-emerald-50 to-green-100 text-emerald-800 border-emerald-300';
      case 'rejected':
        return 'bg-gradient-to-r from-red-50 to-rose-100 text-red-800 border-red-300';
      case 'business_review':
        return 'bg-gradient-to-r from-indigo-50 to-indigo-100 text-indigo-800 border-indigo-300';
      case 'business_requested_changes':
        return 'bg-gradient-to-r from-pink-50 to-pink-100 text-pink-700 border-pink-300';
      case 'changes_added':
        return 'bg-gradient-to-r from-orange-50 to-orange-100 text-orange-700 border-orange-300';
      case 'report_updated':
        return 'bg-gradient-to-r from-cyan-50 to-cyan-100 text-cyan-800 border-cyan-300';
      case 'business_approved':
        return 'bg-gradient-to-r from-purple-50 to-purple-100 text-purple-800 border-purple-300';
      default:
        return 'bg-gradient-to-r from-slate-100 to-slate-200 text-slate-700 border-slate-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        );
      case 'qa_review':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'admin_review':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        );
      case 'approved':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'rejected':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'draft':
        return 'Draft';
      case 'qa_review':
        return 'QA Review';
      case 'admin_review':
        return 'Admin Review';
      case 'approved':
        return 'Approved';
      case 'rejected':
        return 'Rejected';
      case 'business_review':
        return 'Business Review';
      case 'business_requested_changes':
        return 'Business Requested Changes';
      case 'changes_added':
        return 'Changes Added (QA)';
      case 'report_updated':
        return 'Report Updated (Admin)';
      case 'business_approved':
        return 'Business Approved';
      default:
        return status;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'text-red-600 bg-red-50';
      case 'high':
        return 'text-orange-600 bg-orange-50';
      case 'medium':
        return 'text-amber-600 bg-amber-50';
      case 'low':
        return 'text-emerald-600 bg-emerald-50';
      default:
        return 'text-slate-600 bg-slate-50';
    }
  };

  if (loading && reports.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-10 bg-slate-200 rounded-lg w-1/3 mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
                  <div className="p-6">
                    <div className="h-6 bg-slate-200 rounded w-3/4 mb-4"></div>
                    <div className="h-4 bg-slate-200 rounded w-1/2 mb-3"></div>
                    <div className="h-4 bg-slate-200 rounded w-2/3 mb-4"></div>
                    <div className="space-y-2">
                      <div className="h-3 bg-slate-200 rounded w-full"></div>
                      <div className="h-3 bg-slate-200 rounded w-3/4"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <div className="flex items-center gap-3 mb-2">
                <div className="p-2 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
                  Pentest Reports
                </h1>
              </div>
              <p className="text-slate-600 text-sm">Comprehensive security assessment reports and analytics</p>
            </div>
      
          </div>
        </div>

        {/* Enhanced Filter Section - Always show for all roles, but hide company filter for BUSINESS */}
        <div className="mb-8">
          <div className="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
            {/* Filter Header */}
            <div className="px-6 py-4 bg-gradient-to-r from-slate-50 to-blue-50 border-b border-slate-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-white rounded-xl shadow-sm">
                    <svg className="w-5 h-5 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-slate-900">Filter Reports</h3>
                    <p className="text-sm text-slate-600">
                      {getActiveFiltersCount() > 0 ? `${getActiveFiltersCount()} active filter${getActiveFiltersCount() > 1 ? 's' : ''}` : 'No filters applied'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getActiveFiltersCount() > 0 && (
                    <button
                      onClick={clearAllFilters}
                      className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100 border border-red-200 rounded-xl transition-all duration-200"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                      Clear All
                    </button>
                  )}
                  <button
                    onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                    className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-xl transition-all duration-200"
                  >
                    <svg className={`w-4 h-4 transition-transform duration-200 ${showAdvancedFilters ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                    {showAdvancedFilters ? 'Hide' : 'Show'} Filters
                  </button>
                </div>
              </div>
            </div>

            {/* Filter Content */}
            <div className={`transition-all duration-300 ease-in-out ${showAdvancedFilters ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'} overflow-hidden`}>
              <div className="p-6 space-y-6">
                {/* Primary Filters Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
                  {/* Status Filter */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-slate-700">Status</label>
                    <div className="relative">
                      <select
                        value={filters.status || ''}
                        onChange={e => handleFilterChange({ status: e.target.value || undefined })}
                        className="w-full appearance-none bg-white border border-slate-300 rounded-xl px-4 py-3 pr-10 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                      >
                        <option value="">All Statuses</option>
                        {availableStatuses.map((status: string) => (
                          <option key={status} value={status}>{getStatusText(status)}</option>
                        ))}
                      </select>
                      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-slate-400">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  {/* Company Name Filter - Only for non-BUSINESS roles */}
                  {role !== UserRole.BUSINESS && (
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-slate-700">Company</label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <svg className="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                          </svg>
                        </div>
                        <input
                          type="text"
                          value={filters.company_name || ''}
                          onChange={e => handleFilterChange({ company_name: e.target.value || undefined })}
                          placeholder="Search company..."
                          className="w-full bg-white border border-slate-300 rounded-xl pl-10 pr-4 py-3 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                        />
                      </div>
                    </div>
                  )}

                  {/* Date From Filter */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-slate-700">From Date</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg className="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <input
                        type="date"
                        value={filters.date_from || ''}
                        onChange={e => handleFilterChange({ date_from: e.target.value || undefined })}
                        className="w-full bg-white border border-slate-300 rounded-xl pl-10 pr-4 py-3 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                      />
                    </div>
                  </div>

                  {/* Date To Filter */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-slate-700">To Date</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg className="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <input
                        type="date"
                        value={filters.date_to || ''}
                        onChange={e => handleFilterChange({ date_to: e.target.value || undefined })}
                        className="w-full bg-white border border-slate-300 rounded-xl pl-10 pr-4 py-3 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                      />
                    </div>
                  </div>
                </div>

                {/* Sort Controls */}
                <div className="flex flex-col sm:flex-row gap-4 pt-4 border-t border-slate-200">
                  <div className="flex-1 space-y-2">
                    <label className="block text-sm font-medium text-slate-700">Sort By</label>
                    <div className="relative">
                      <select
                        value={filters.sort_by || 'createdAt'}
                        onChange={e => handleFilterChange({ sort_by: e.target.value })}
                        className="w-full appearance-none bg-white border border-slate-300 rounded-xl px-4 py-3 pr-10 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                      >
                        <option value="createdAt">Created Date</option>
                        <option value="updatedAt">Updated Date</option>
                        <option value="company_name">Company Name</option>
                        <option value="status">Status</option>
                      </select>
                      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-slate-400">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-slate-700">Order</label>
                    <button
                      onClick={() => handleFilterChange({ sort_order: filters.sort_order === 'ASC' ? 'DESC' : 'ASC' })}
                      className="flex items-center gap-2 px-4 py-3 bg-white border border-slate-300 rounded-xl text-sm font-medium text-slate-700 hover:bg-slate-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    >
                      <svg className={`w-4 h-4 transition-transform duration-200 ${filters.sort_order === 'ASC' ? 'rotate-0' : 'rotate-180'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4" />
                      </svg>
                      {filters.sort_order === 'ASC' ? 'Ascending' : 'Descending'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-3 mb-4">
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-red-800 font-medium text-sm">{error}</p>
            </div>
          </div>
        )}

        {/* Reports Grid */}
        {reports.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
              {reports.map((report) => (
                <div
                  key={report.id}
                  className="group bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer border border-slate-200 overflow-hidden transform hover:-translate-y-0.5"
                  onClick={() => navigate(`/dashboard/pentest-reports/${report.id}/edit`)}
                >
                  {/* Header */}
                  <div className="p-4 border-b border-slate-100">
                    <div className="flex flex-col mb-3 gap-1">
                      <h3 className="text-base font-bold text-slate-900 leading-tight min-w-0 break-words">
                        {report.title}
                      </h3>
                      <div className='flex'>
                       
                      <p className="text-xs text-slate-500 font-mono bg-slate-50 px-2 py-1 rounded w-fit mt-1 flex text-center items-center gap-2">
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        {formatDate(report.created_at)}
                      </p>
                      </div>
                    </div>
                    <p className="text-sm text-slate-600 mb-1 font-medium">{report.company_name}</p>
                    <p className="text-xs text-slate-500 font-mono bg-slate-50 px-2 py-1 rounded">{report.document_number}</p>
                    {/* Program Names */}
                    {report.program_names && Object.keys(report.program_names).length > 0 && (
                      <div className="mt-2">
                        <p className="text-xs text-slate-500 mb-1 font-medium">Programs:</p>
                        <div className="flex flex-wrap gap-1">
                          {Object.entries(report.program_names).map(([programId, programName]) => (
                            <span
                              key={programId}
                              className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-md"
                            >
                              {programName}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Content */}
                  <div className="p-4">
                    {/* Findings Summary */}
                    <div className="mb-4">
                      <h4 className="text-xs font-bold text-slate-700 mb-2 flex items-center gap-1">
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        Findings
                      </h4>
                      <div className="grid grid-cols-2 gap-2">
                        <div className={`flex items-center justify-between p-1.5 rounded-md ${getSeverityColor('critical')}`}>
                          <span className="text-xs font-semibold">Critical</span>
                          <span className="text-xs font-bold">{report.critical_count}</span>
                        </div>
                        <div className={`flex items-center justify-between p-1.5 rounded-md ${getSeverityColor('high')}`}>
                          <span className="text-xs font-semibold">High</span>
                          <span className="text-xs font-bold">{report.high_count}</span>
                        </div>
                        <div className={`flex items-center justify-between p-1.5 rounded-md ${getSeverityColor('medium')}`}>
                          <span className="text-xs font-semibold">Medium</span>
                          <span className="text-xs font-bold">{report.medium_count}</span>
                        </div>
                        <div className={`flex items-center justify-between p-1.5 rounded-md ${getSeverityColor('low')}`}>
                          <span className="text-xs font-semibold">Low</span>
                          <span className="text-xs font-bold">{report.low_count}</span>
                        </div>
                      </div>
                    </div>

                    {/* Version Info */}
                    <div className="mb-4">
                      <div className="flex items-center justify-between text-xs text-slate-500 bg-slate-50 p-2 rounded-lg">
                        <span className="font-semibold">Version {report.current_version}</span>
                        <span>{report.total_versions} total</span>
                      </div>
                    </div>

                    {/* Footer */}
                    <div className="border-t border-slate-100 pt-3 flex items-center justify-between text-xs text-slate-500">
                      <div className="space-y-1">
                      <span className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-semibold rounded-full border flex-shrink-0 ${getStatusColor(report.status)}`}>
                          {getStatusIcon(report.status)}
                          {getStatusText(report.status)}
                        </span>
                      </div>
                      <div className="text-right space-y-1 flex flex-col items-end">
                       
                        <p className="font-semibold text-slate-700">Total: {report.total_findings}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                <div className="text-sm text-slate-600">
                  Showing <span className="font-semibold">{((pagination.current_page - 1) * pagination.limit) + 1}</span> to{' '}
                  <span className="font-semibold">{Math.min(pagination.current_page * pagination.limit, pagination.total_count)}</span> of{' '}
                  <span className="font-semibold">{pagination.total_count}</span> results
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handlePageChange(pagination.current_page - 1)}
                    disabled={pagination.current_page <= 1}
                    className="inline-flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-lg hover:bg-slate-50 hover:border-slate-400 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                    Previous
                  </button>
                  <span className="px-3 py-1.5 text-sm font-semibold text-slate-700 bg-slate-50 rounded-lg">
                    Page {pagination.current_page} of {pagination.total_pages}
                  </span>
                  <button
                    onClick={() => handlePageChange(pagination.current_page + 1)}
                    disabled={pagination.current_page >= pagination.total_pages}
                    className="inline-flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-lg hover:bg-slate-50 hover:border-slate-400 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    Next
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </>
        ) : (
          role === UserRole.BUSINESS ? (
            <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-12 text-center">
              <div className="text-blue-400 mb-4">
                <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-bold text-slate-900 mb-2">No reports found</h3>
              <p className="text-slate-600 mb-6 max-w-md mx-auto">You haven't requested any pentest reports yet. Follow these steps to request your first report:</p>
              <ol className="text-left max-w-lg mx-auto space-y-4">
                <li className="flex items-start gap-3">
                  <span className="inline-flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 text-blue-700 font-bold">1</span>
                  <span>Go to the <strong>Programs</strong> section using the side navigation bar.</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="inline-flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 text-blue-700 font-bold">2</span>
                  <span>Choose the program for which you want to request a report.</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="inline-flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 text-blue-700 font-bold">3</span>
                  <span>Click the <strong>Full Details</strong> button in the right-side Program Quick View component.</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="inline-flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 text-blue-700 font-bold">4</span>
                  <span>In the full view of the program, you will see the <strong>Request Report</strong> button.</span>
                </li>
              </ol>
            </div>
          ) : (
            <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-12 text-center">
              <div className="text-slate-400 mb-4">
                <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-bold text-slate-900 mb-2">No reports found</h3>
              <p className="text-slate-600 mb-4 max-w-md mx-auto">Try adjusting your filters or navigate to a program to request a pentest report.</p>
            </div>
          )
        )}
      </div>
    </div>
  );
};

export default PentestReports; 