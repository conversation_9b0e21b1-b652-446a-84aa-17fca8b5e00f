import { Route, Routes } from "react-router-dom";
import ErrorPage from "../../pages/ErrorPage";
import AdminDashboard from "../../pages/admin/AdminDashboard";
import AdminProfile from "../../pages/admin/AdminProfile";
import Assistant from "../../pages/assistant/Assistant";
import ProgramEditor from "../../pages/programs/ProgramEditor";
import PaginatedTransactions from "../../pages/payments/PaginatedTransactions";
import InvitationsManager from "../../components/users/InvitationManager";
import PaginatedReports from "../../pages/reports/PaginatedReports";
import ReportPage from "../../pages/reports/ReportPage";
import PaginatedPrograms from "../../pages/programs/PaginatedPrograms";
import ProgramPage from "../../pages/programs/ProgramPage";
import RetestManagement from "../../pages/retests/RetestManagent";
import RetestPage from "../../pages/retests/RetestPage";
import PentestReports from "../../pages/automated-report/PentestReports";
import PDFEditor from "../../pages/automated-report/PDFEditor";
import PaginatedUsers from "../../pages/users/PaginatedUsers";

const SubAdminRoutes = () => {
  return (
    <Routes>
      <Route index element={<AdminDashboard />} />

      <Route path="programs/:id/edit" element={<ProgramEditor />} />
      <Route path="/create-program" element={<ProgramEditor />} />
      <Route path="invitation" element={<InvitationsManager />} />

      <Route path="profile/*" element={<AdminProfile />} />
      <Route path="reports" element={<PaginatedReports />} />
      <Route path="reports/:id" element={<ReportPage />} />

      <Route path="programs" element={<PaginatedPrograms />} />
      <Route path="programs/:id" element={<ProgramPage />} />

      <Route path="/retests" element={<RetestManagement />} />
      <Route path="/retests/:retest_id" element={<RetestPage />} />

      <Route path="users" element={<PaginatedUsers />} />

      <Route path="assistant/*" element={<Assistant />} />

      <Route path="payments/*" element={<PaginatedTransactions />} />
      <Route path="pentest-reports" element={<PentestReports />} />
      <Route path="pentest-reports/:report_id/edit" element={<PDFEditor />} />

      <Route path="/*" element={<ErrorPage />} />
    </Routes>
  );
};

export default SubAdminRoutes;
