import axios from "../../axios";
import { parseRetestDetails, RetestDetailsResponse } from "../retests/parseRetests";

const BASE_URL = "/v2/retests";
 
const getUserRole = () => {
  return localStorage.getItem("userRole") || "GUEST";  
};

export const getRetestDetails = async (programId: number): Promise<RetestDetailsResponse | null> => {
  try {
    const userRole = getUserRole();

    if (userRole === "BUSINESS_MANAGER" || userRole === "ADMIN_MANAGER") {
      console.warn("Skipping API call for BUSINESS_MANAGER or ADMIN_MANAGER.");
      return null; 
    }

    console.log(`Fetching Retest Details for Program ID: ${programId}`);

    const response = await axios.get(`${BASE_URL}/program/${programId}`);
    console.log("Raw API Response:", response.data);

    const parsedData: RetestDetailsResponse = parseRetestDetails(response.data);
    console.log("Parsed Retest Details:", parsedData);

    return parsedData;
  } catch (error) {
    console.error("Error fetching retest details:", error);
    throw error;
  }
};
