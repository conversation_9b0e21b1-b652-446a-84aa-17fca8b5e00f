import { useEffect } from "react";
import { useStoreDispatch, useStoreSelector } from "../hooks";
import { fetchUserDetails } from "../../store/reducer/slices/userReducerForAdmin";
import { LoadingState } from "../../store/reducer/reducer";

/**
 * Provide access to the user details
 * stored in the global state
 */
const useUserDetailsForAdmin = (user_id: string | undefined) => {
  const dispatch = useStoreDispatch();

  // Setup selectors for user details state
  const loadingStatus = useStoreSelector(
    state => state.other_user.details.status
  );
  const details = useStoreSelector(state => state.other_user.details);

  // Retrieve the list of user details if none exist
  useEffect(() => {
    console.log(details.user_id);
    console.log(user_id);
    if (loadingStatus !== LoadingState.LOADING && details.user_id !== user_id) {
      console.log("caling");
      dispatch(fetchUserDetails(user_id ?? ""));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [details.user_id, user_id]);

  return {
    user: details,
    ...details
  };
};

export default useUserDetailsForAdmin;
