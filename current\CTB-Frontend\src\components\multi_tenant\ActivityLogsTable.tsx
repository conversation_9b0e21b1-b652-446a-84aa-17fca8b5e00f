import React, { useState } from "react";
import {
  ActivityLog,
  formatRole
} from "../../utils/hooks/multi-tenant/invitation";

interface ActivityLogsTableProps {
  logs: ActivityLog[];
  isLoading: boolean;
  isError: boolean;
}

const ActivityLogsTable: React.FC<ActivityLogsTableProps> = ({
  logs,
  isLoading,
  isError
}) => {
  const [expandedLogId, setExpandedLogId] = useState<number | null>(null);

  return (
    <div className="overflow-x-auto rounded-lg border border-gray-200">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-blue-700">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
              Username
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
              Email
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
              Role
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
              Action
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
              Module
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
              Date
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 bg-white">
          {isLoading ? (
            <tr>
              <td
                colSpan={6}
                className="px-6 py-4 text-center text-sm text-gray-500"
              >
                Loading activity logs...
              </td>
            </tr>
          ) : isError ? (
            <tr>
              <td
                colSpan={6}
                className="px-6 py-4 text-center text-sm text-red-500"
              >
                Failed to load activity logs
              </td>
            </tr>
          ) : logs.length === 0 ? (
            <tr>
              <td
                colSpan={6}
                className="px-6 py-4 text-center text-sm text-gray-500"
              >
                No activity logs found
              </td>
            </tr>
          ) : (
            logs.map(log => (
              <tr
                key={log.id}
                className="transition-colors duration-200 hover:bg-gray-50"
              >
                <td className="whitespace-nowrap px-6 py-4 text-[12px] text-gray-500">
                  {log.user.username}
                </td>
                <td
                  className="cursor-pointer whitespace-nowrap px-6 py-4 text-[12px] text-gray-500"
                  style={{
                    maxWidth: "250px",
                    whiteSpace: expandedLogId === log.id ? "normal" : "nowrap",
                    overflow: "hidden",
                    textOverflow: "ellipsis"
                  }}
                  onClick={() =>
                    setExpandedLogId(prev => (prev === log.id ? null : log.id))
                  }
                  title={log.user.email}
                >
                  {expandedLogId === log.id
                    ? log.user.email
                    : log.user.email.length > 20
                    ? `${log.user.email.substring(0, 20)}...`
                    : log.user.email}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-[12px] text-gray-500">
                  {formatRole(log.role)}
                </td>
                <td
                  className="cursor-pointer px-6 py-4 text-[12px] text-gray-900"
                  style={{
                    maxWidth: "300px",
                    whiteSpace: expandedLogId === log.id ? "normal" : "nowrap",
                    overflow: "hidden",
                    textOverflow: "ellipsis"
                  }}
                  onClick={() =>
                    setExpandedLogId(prev => (prev === log.id ? null : log.id))
                  }
                  title={log.action}
                >
                  {expandedLogId === log.id ? (
                    <span dangerouslySetInnerHTML={{ __html: log.action }} />
                  ) : log.action.length > 100 ? (
                    `${log.action.substring(0, 100)}...`
                  ) : (
                    <span dangerouslySetInnerHTML={{ __html: log.action }} />
                  )}
                </td>

                <td className="whitespace-nowrap px-6 py-4 text-[12px] text-gray-500">
                  {log.module}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-[12px] text-gray-500">
                  {new Date(log.created_at)
                    .toLocaleString("en-GB", {
                      day: "2-digit",
                      month: "2-digit",
                      year: "numeric",
                      hour: "2-digit",
                      minute: "2-digit",
                      second: "2-digit",
                      hour12: false
                    })
                    .replace(",", "")}
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
};

export default ActivityLogsTable;
