import React from "react";
import { Link } from "react-router-dom";
import { CgProfile } from "react-icons/cg";

interface RetestDetailsRightSectionProps {
  reportTitle: string;
  researcherUsername: string;
  researcherPfp: string;
  businessPfp: string;
  reportId: number;
  targetName: string;
}

const RetestDetailsRightSection: React.FC<RetestDetailsRightSectionProps> = ({
  reportTitle,
  researcherUsername,
  researcherPfp,
  businessPfp,
  reportId,
  targetName
}) => {
  // Parse the targetName to extract the URL
  const targetUrl = JSON.parse(targetName)[0]?.targetName || "N/A";

  return (
    <div className="w-[30%] flex-shrink-0 flex-col gap-4">
      <div className="rounded-lg bg-white p-4 shadow-md">
        <h2 className="mb-4 text-xl font-bold capitalize">{reportTitle}</h2>
        <p className="mb-2 text-gray-600">Requested to</p>
        <div className="mb-8 flex items-center">
          {researcherPfp ? (
            <img
              src={researcherPfp}
              alt={researcherUsername}
              className="mr-1 h-10 w-10 rounded-full border-2 border-black object-cover"
            />
          ) : (
            <CgProfile size={42} className="mr-1 text-gray-500" />
          )}
          <span className="font-semibold text-blue-600">
            @{researcherUsername}
          </span>
        </div>
        <p className="mb-2 text-gray-600">Participants</p>
        <div className="mb-8 flex items-center">
          {researcherPfp ? (
            <img
              src={researcherPfp}
              alt={researcherUsername}
              className="mr-1 h-10 w-10 rounded-full border-2 border-black object-cover"
            />
          ) : (
            <CgProfile size={42} className="mr-1 text-gray-500" />
          )}
          {businessPfp ? (
            <img
              src={businessPfp}
              alt={"business pfp"}
              className="h-10 w-10 rounded-full border-2 border-black object-cover"
            />
          ) : (
            <CgProfile size={42} className="mr-1 text-gray-500" />
          )}
        </div>
        <div className="flex flex-col gap-10">
          <div>
            <p className="mb-2 text-gray-600">Asset</p>
            <span className="block rounded-lg bg-ctb-grey-100 px-3 py-1.5 text-sm font-medium text-blue-700">
              <a
                href={targetUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="font-semibold text-blue-600 break-all"
              >
                {targetUrl}
              </a>
            </span>
          </div>
          <div>
            <Link
              to={`/dashboard/reports/${reportId}`}
              className="rounded-lg border-2 border-blue-600 px-4 py-2 font-bold text-blue-600 hover:border-blue-700 hover:bg-blue-500 hover:text-white"
            >
              View Report
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RetestDetailsRightSection;
