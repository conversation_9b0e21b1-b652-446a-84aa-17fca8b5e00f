const CircleChevron = ({
  className,
  strokeWidth = 1.5
}: {
  className?: string;
  strokeWidth?: number;
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    width="50" // Reduced size
    height="30" // Reduced size
    strokeWidth={strokeWidth}
    stroke="currentColor"
    className={className}
  >
    {/* Circle */}
    <circle cx="12" cy="12" r="10.5" fill="none" stroke="#d3d3d3" strokeWidth="1.5" />

    {/* Left Chevron */}
    <polyline
      points="13.5,8.5 10,12 13.5,15.5"
      fill="none"
      stroke="#000"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default CircleChevron;
