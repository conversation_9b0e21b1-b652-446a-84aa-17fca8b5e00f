// PreviewOnly.tsx - Presentational component for BUSINESS preview-only mode in PDFEditor
import React, { useState } from 'react';
import PreviewSection from './components/PreviewSection';
import PDFDownloader from './components/PDFDownloader';
import ReportTemplate from './components/ReportTemplate';
import ChangeLogSidebar from './components/ChangeLogSidebar';
import ConfirmationModal from '../../components/common/ConfirmationModal';
import { FaCheckCircle, FaComments } from 'react-icons/fa';

interface PreviewOnlyProps {
  reportData: any;
  currentData: any;
  openFullView: () => void;
  previewMode: string;
  setPreviewMode: (mode: string) => void;
  previewHtml: string;
  previewRef: React.RefObject<HTMLDivElement>;
  isFullViewOpen: boolean;
  closeFullView: () => void;
  notification: any;
  hideNotification: () => void;
  isChangeLogOpen: boolean;
  closeChangeLog: () => void;
  changeLog: any[];
  changeLogLoading: boolean;
  previewLoading: boolean;
  onBusinessApprove?: () => void;
  onBusinessRequestChanges?: () => void;
  businessActionLoading?: boolean;
  isBusinessUser?: boolean;
}

const PreviewOnly: React.FC<PreviewOnlyProps> = ({
  reportData,
  currentData,
  openFullView,
  previewMode,
  setPreviewMode,
  previewHtml,
  previewRef,
  isFullViewOpen,
  closeFullView,
  notification,
  hideNotification,
  isChangeLogOpen,
  closeChangeLog,
  changeLog,
  changeLogLoading,
  previewLoading,
  onBusinessApprove,
  onBusinessRequestChanges,
  businessActionLoading,
  isBusinessUser
}) => {
  const showBusinessActions = isBusinessUser && ["approved", "report_updated", "business_review"].includes(reportData?.status);
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRequestModal, setShowRequestModal] = useState(false);

  const handleApproveClick = () => setShowApproveModal(true);
  const handleRequestClick = () => setShowRequestModal(true);
  const handleApproveConfirm = () => {
    setShowApproveModal(false);
    onBusinessApprove && onBusinessApprove();
  };
  const handleRequestConfirm = () => {
    setShowRequestModal(false);
    // Optionally focus chat or scroll to chat section
    onBusinessRequestChanges && onBusinessRequestChanges();
  };
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="flex h-screen overflow-hidden select-none">
        <div className="flex-1 h-full overflow-hidden bg-gradient-to-br from-blue-50/80 to-blue-100/60">
          <div className="h-full flex flex-col">
            {/* Floating Business Action Buttons UI */}
            {showBusinessActions && (
              <div className="absolute bottom-10 ml-10 z-50 flex flex-col gap-2 bg-white/90 shadow-xl rounded-xl p-2 border border-slate-200 min-w-[180px] max-w-[220px]">
                <button
                  className="flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-green-500 to-emerald-600 text-white font-bold text-base shadow-lg hover:from-green-600 hover:to-emerald-700 transition disabled:opacity-60 focus:outline-none focus:ring-2 focus:ring-green-400"
                  onClick={handleApproveClick}
                  disabled={businessActionLoading}
                >
                  <FaCheckCircle className="w-4 h-4" />
                  {businessActionLoading ? 'Approving...' : 'Approve'}
                </button>
                <button
                  className="flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-red-500 to-pink-600 text-white font-bold text-base shadow-lg hover:from-red-600 hover:to-pink-700 transition disabled:opacity-60 focus:outline-none focus:ring-2 focus:ring-red-400"
                  onClick={handleRequestClick}
                  disabled={businessActionLoading}
                >
                  <FaComments className="w-4 h-4" />
                  {businessActionLoading ? 'Requesting...' : 'Request Changes'}
                </button>
                {/* Approve Confirmation Modal */}
                <ConfirmationModal
                  isOpen={showApproveModal}
                  title="Confirm Approval"
                  description="Are you sure you want to approve this report? This action cannot be reverted."
                  confirmText="Approve"
                  cancelText="Cancel"
                  onConfirm={handleApproveConfirm}
                  onClose={() => setShowApproveModal(false)}
                />
                {/* Request Changes Modal */}
                <ConfirmationModal
                  isOpen={showRequestModal}
                  title="Request Changes"
                  description="To request changes, please use the chat section in the bottom right of the page to communicate your feedback to the team."
                  confirmText="OK, Got it"
                  cancelText="Cancel"
                  onConfirm={handleRequestConfirm}
                  onClose={() => setShowRequestModal(false)}
                />
              </div>
            )}
            {/* End Floating Business Action Buttons UI */}
            <div className="flex-1 overflow-y-auto">
              <PreviewSection 
                reportData={currentData}
                onFullView={openFullView}
                initialPreviewMode={['full', 'technical'].includes(previewMode) ? previewMode as 'full' | 'technical' : 'full'}
                onPreviewModeChange={setPreviewMode}
                previewHtml={previewHtml}
              >
                <div ref={previewRef} className="report-template-print">
                  <ReportTemplate reportData={currentData} />
                </div>
              </PreviewSection>
            </div>
          </div>
        </div>
      </div>
      <ChangeLogSidebar
        isOpen={isChangeLogOpen}
        onClose={closeChangeLog}
        changeLog={changeLog}
        loading={changeLogLoading}
      />
    </div>
  );
};

export default PreviewOnly; 