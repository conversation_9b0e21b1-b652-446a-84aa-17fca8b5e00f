import React from "react";
import { CgProfile } from "react-icons/cg";

// Define the type for a researcher
type Researcher = {
  researcherUsername: string;
  researcherPfp?: string;
};

// Define the props for the component
type ResearcherTeamListProps = {
  researchers: Researcher[];
};

const ResearcherTeamList: React.FC<ResearcherTeamListProps> = ({
  researchers
}) => {
  return (
    <div className="rounded-lg bg-white p-4">
      <h2 className="mb-4 text-xl font-semibold">Pentest Team</h2>
      {researchers.length === 0 ? (
        <p className="text-center text-gray-500">No researchers found</p>
      ) : (
        <div className="grid grid-cols-2 gap-4">
          {researchers.map((researcher, index) => (
            <div key={index} className="flex items-center space-x-2">
              {researcher.researcherPfp ? (
                <img
                  src={researcher.researcherPfp}
                  alt={researcher.researcherUsername}
                  className="h-10 w-10 rounded-full border-2 border-black object-cover"
                />
              ) : (
                <CgProfile size={35} className="text-gray-500" />
              )}
              <div>
                <p className="text-sm font-medium text-blue-700">
                  @{researcher.researcherUsername}
                </p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ResearcherTeamList;
