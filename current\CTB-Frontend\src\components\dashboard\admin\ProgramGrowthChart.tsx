import React, { useState, useEffect } from "react";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import moment from "moment";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

// Update the Program interface to allow expectedStartDate to be string | null
interface Program {
  programId: number;
  programTitle: string;
  expectedStartDate: string | null; // Allow null values
}

interface ProgramGrowthChartProps {
  programs: Program[];
  title: string;
}

const ProgramGrowthChart: React.FC<ProgramGrowthChartProps> = ({
  programs,
  title,
}) => {
  const [filter, setFilter] = useState("month"); // Options: "week", "month", "year"

  // Filter out programs where expectedStartDate is null
  const filteredPrograms = programs.filter(
    (program) => program.expectedStartDate !== null
  );

  // Aggregate programs by the selected filter (week, month, year)
  const aggregatePrograms = (filter: string) => {
    const groupedPrograms: { [key: string]: Program[] } = {};

    filteredPrograms.forEach((program) => {
      let groupKey: string;
      switch (filter) {
        case "week":
          groupKey = moment(program.expectedStartDate).format("YYYY-WW"); // Week format
          break;
        case "month":
          groupKey = moment(program.expectedStartDate).format("YYYY-MM"); // Month format
          break;
        case "year":
          groupKey = moment(program.expectedStartDate).format("YYYY"); // Year format
          break;
        default:
          groupKey = moment(program.expectedStartDate).format("YYYY-MM");
      }

      if (!groupedPrograms[groupKey]) {
        groupedPrograms[groupKey] = [];
      }
      groupedPrograms[groupKey].push(program);
    });

    return groupedPrograms;
  };

  const groupedPrograms = aggregatePrograms(filter);

  // Prepare chart data
  const chartData = {
    labels: Object.keys(groupedPrograms),
    datasets: [
      {
        label: "Programs Created",
        data: Object.keys(groupedPrograms).map(
          (key) => groupedPrograms[key].length
        ),
        backgroundColor: "rgba(75, 192, 192, 0.2)",
        borderColor: "rgba(75, 192, 192, 1)",
        borderWidth: 2,
        pointRadius: 5,
        pointBackgroundColor: "rgba(75, 192, 192, 1)",
      },
    ],
  };

  // Custom tooltip to display program names
  const customTooltip = {
    callbacks: {
      label: (context: any) => {
        const label = context.label || "";
        const programsInGroup = groupedPrograms[label];
        const programNames = programsInGroup
          .map((p) => p.programTitle)
          .join(", ");
        return `${label}: ${context.raw} programs\n${programNames}`;
      },
    },
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
        labels: {
          font: {
            size: 14,
            family: "'Inter', sans-serif",
            weight: "600",
          },
          padding: 20,
          usePointStyle: true,
          pointStyle: "circle",
        },
      },
      title: {
        display: false,
        text: title,
        font: {
          size: 20,
          family: "'Inter', sans-serif",
          weight: "700",
        },
        padding: 10,
      },
      tooltip: customTooltip,
    },
    scales: {
      x: {
        title: {
          display: true,
          text: `Time (${filter})`,
          font: {
            size: 16,
            family: "'Inter', sans-serif",
            weight: "600",
          },
          color: "#333",
          padding: 10,
        },
        ticks: {
          font: {
            family: "'Inter', sans-serif",
          },
        },
      },
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: "No. of Programs",
          font: {
            size: 16,
            family: "'Inter', sans-serif",
            weight: "600",
          },
          color: "#333",
          padding: 10,
        },
        ticks: {
          font: {
            family: "'Inter', sans-serif",
          },
        },
      },
    },
  };

  return (
  <div className="flex flex-col rounded-xl bg-white shadow-lg">
   
    <div className="bg-gradient-to-r from-blue-700 to-blue-600 p-4 rounded-t-xl">
      <h2 className="text-xl font-semibold text-white">{title}</h2>
    </div>

      <div className="flex justify-end p-4">
        <select
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          className="rounded-md border p-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
        >
          <option value="week">Week</option>
          <option value="month">Month</option>
          <option value="year">Year</option>
        </select>
      </div>

      {/* Chart */}
      <div className="h-[375px] p-2">
        <Line data={chartData} options={options} />
      </div>
    </div>
  );
};


export default ProgramGrowthChart;