import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { getProgramReportById } from '../../utils/api/endpoints/program-reports/program-reports';
import { ReportData } from './types/report.types';
import ReportTemplate from './components/ReportTemplate';

const ReportTemplatePage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) return;
    setLoading(true);
    getProgramReportById(id)
      .then((data) => {
        setReportData(data);
        setLoading(false);
      })
      .catch((err) => {
        setError('Failed to fetch report data');
        setLoading(false);
      });
  }, [id]);

  if (loading) return <div className="p-8 text-center">Loading...</div>;
  if (error) return <div className="p-8 text-center text-red-600">{error}</div>;
  if (!reportData) return <div className="p-8 text-center">No report found.</div>;

  return <ReportTemplate reportData={reportData} />;
};

export default ReportTemplatePage; 