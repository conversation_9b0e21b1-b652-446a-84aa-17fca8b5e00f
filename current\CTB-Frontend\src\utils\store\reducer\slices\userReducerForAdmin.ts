import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { LoadingState as LoadingStatus } from "../reducer";
import {
  getUserDetailsAdmin,
} from "../../../api/endpoints/user/user";

export type UserDetails = {
  user_id?: number
  username?: string;
  displayName?: string;
  about?: string;
  country?: string;
  links?: string[];
  skills?: string[];
  profilePicture?: string;
  bannerPicture?: string;
  otpEnabled?: boolean;
  firstTimeUXP?: boolean;
  paymentDetails?: {
    mode: string;
    details: unknown;
  };
  languages?: string[];
  hobbies?: string[];
  education?: string[];
  work_experience?: string[];
  achievements?: string[];
  tools_used?: string[];
  community_engagement?: string[];
  testimonials?: string[];
  publications?: string[];
  availability?: boolean;
  security_clearance?: string[];
};

type UserState = {

  details: {
    status: LoadingStatus;
  } & UserDetails;
  error?: string;
};


/**
 * Fetch the latest user details from the backend API
 */
export const fetchUserDetails = createAsyncThunk(
  "other_user/details",
  async (user_id: string) => getUserDetailsAdmin(user_id)
);

// Define the initial user state
const initialState: UserState = {
  details: {
    status: LoadingStatus.IDLE
  }
};

const userReducerForAdmin = createSlice({
  name: "other_user",
  initialState,
  reducers: {},
  extraReducers(builder) {
    builder
      // States while fetching user details
      .addCase(fetchUserDetails.pending, state => {
        state.details.status = LoadingStatus.LOADING;
      })
      .addCase(fetchUserDetails.fulfilled, (state, action): UserState => {
        return {
          ...state,
          details: {
            status: LoadingStatus.SUCCEEDED,
            profilePicture: action.payload.profilePicture || "",
            ...action.payload
          },
        };
      })
      .addCase(fetchUserDetails.rejected, (state, action) => {
        state.details.status = LoadingStatus.FAILED;
        state.error = action.error.message;
      })

  }
});

export default userReducerForAdmin.reducer;
