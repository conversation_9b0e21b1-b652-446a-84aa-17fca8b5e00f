import React, { useState, useMemo, useRef } from "react";

interface Category {
  category: string;
  count: number;
}

interface ProgramCategory {
  programName: string;
  categories: Category[];
}

interface ParsedBusinessDashboard {
  overallCategories: Category[];
  programCategories: ProgramCategory[];
}

const ReportTypeRadarChart = ({
  details
}: {
  details: ParsedBusinessDashboard;
}) => {
  const [selectedProgram, setSelectedProgram] = useState<string>("All");
  const [hoveredPoint, setHoveredPoint] = useState<{
    x: number;
    y: number;
    data: Category;
  } | null>(null);

  const { overallCategories, programCategories } = details;
  const chartContainerRef = useRef<HTMLDivElement>(null);

  const { data, totalCount } = useMemo(() => {
    const chartData =
      selectedProgram === "All"
        ? overallCategories
        : programCategories.find(p => p.programName === selectedProgram)
            ?.categories || [];

    // Sort by count in descending order and take top 5
    const sortedData = [...chartData].sort((a, b) => b.count - a.count);
    const topCategories = sortedData.slice(0, 5);
    const othersCount = sortedData
      .slice(5)
      .reduce((sum, item) => sum + item.count, 0);

    // Prepare final data with "Others" category
    const finalData =
      othersCount > 0
        ? [...topCategories, { category: "Others", count: othersCount }]
        : topCategories;

    return {
      data: finalData,
      totalCount: finalData.reduce((sum, item) => sum + item.count, 0)
    };
  }, [selectedProgram, overallCategories, programCategories]);

  const maxValue = data.length ? Math.max(...data.map(d => d.count)) : 0;
  const numPoints = data.length;
  const centerX = 150;
  const centerY = 150;
  const radius = 120;

  const getPolygonPoints = (scale: number = 1) => {
    return data
      .map((_, i) => {
        const angle = (i / numPoints) * 2 * Math.PI - Math.PI / 2;
        const value = maxValue
          ? (data[i].count / maxValue) * radius * scale
          : 0;
        const x = centerX + value * Math.cos(angle);
        const y = centerY + value * Math.sin(angle);
        return `${x},${y}`;
      })
      .join(" ");
  };

  const dataPolygonPoints = getPolygonPoints();
  const dataValues = dataPolygonPoints.split(" ").map(point => {
    const [x, y] = point.split(",").map(Number);
    return { x, y };
  });

  const gridLines = [0.2, 0.4, 0.6, 0.8, 1].map(scale => {
    const points = getPolygonPoints(scale);
    const gridPoints = points.split(" ").map(point => {
      const [x, y] = point.split(",").map(Number);
      return { x, y };
    });

    const isInsideData =
      scale <= Math.max(...data.map(d => d.count / maxValue));

    return (
      <polygon
        key={scale}
        points={points}
        fill="none"
        stroke={isInsideData ? "#6274fd" : "#D1D5DB"}
        strokeWidth="1"
        strokeDasharray={isInsideData ? "2 2" : "none"}
        className="transition-colors duration-300"
      />
    );
  });

  const outerBorder = (
    <polygon
      points={getPolygonPoints(1)}
      fill="none"
      stroke="#6B7280"
      strokeWidth="2"
    />
  );

  const handleMouseEnter = (x: number, y: number, data: Category) => {
    const chartContainer = chartContainerRef.current;
    if (chartContainer) {
      const containerRect = chartContainer.getBoundingClientRect();
      setHoveredPoint({
        x: x + containerRect.left,
        y: y + containerRect.top,
        data
      });
    }
  };

  return (
    <div
      className="relative rounded-xl border border-gray-200 bg-white pb-2"
      ref={chartContainerRef}
    >
      <div className="mb-6 flex items-center justify-between">
        <h3 className="bg-[#0B45DB] px-[10px] py-[7px] text-lg font-semibold text-white">
          Report Types
        </h3>
        <div className="relative">
          <select
            value={selectedProgram}
            onChange={e => setSelectedProgram(e.target.value)}
            className="appearance-none border border-gray-300 bg-gray-50 px-4 py-[10px] pr-8 text-sm font-medium text-gray-600 transition-colors hover:border-blue-600"
            style={{
              width: "200px",
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "normal"
            }}
          >
            {["All", ...programCategories.map(p => p.programName)].map(name => (
              <option key={name} value={name} className="whitespace-normal">
                {name}
              </option>
            ))}
          </select>
          <div className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2">
            <svg
              className="h-4 w-4 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>
        </div>
      </div>

      <div className="flex w-full justify-center">
        <svg viewBox="0 0 300 280" className="mb-[2px] w-[320px]">
          {outerBorder}
          {gridLines}

          <polygon
            points={dataPolygonPoints}
            className="fill-blue-500/20 stroke-blue-500"
            strokeWidth="2"
            style={{ transition: "all 0.3s ease" }}
          />

          {data.map((_, i) => {
            const angle = (i / numPoints) * 2 * Math.PI - Math.PI / 2;
            const x = centerX + radius * Math.cos(angle);
            const y = centerY + radius * Math.sin(angle);

            return (
              <line
                key={i}
                x1={centerX}
                y1={centerY}
                x2={x}
                y2={y}
                stroke="#D1D5DB"
                strokeWidth="1"
              />
            );
          })}

          {data.map((d, i) => {
            const angle = (i / numPoints) * 2 * Math.PI - Math.PI / 2;
            const value = maxValue ? (d.count / maxValue) * radius : 0;
            const x = centerX + value * Math.cos(angle);
            const y = centerY + value * Math.sin(angle);

            return (
              <circle
                key={i}
                cx={x}
                cy={y}
                r="4"
                className="cursor-pointer fill-blue-500 transition-all duration-300 hover:fill-blue-700"
                onMouseEnter={() => handleMouseEnter(x, y, d)}
                onMouseLeave={() => setHoveredPoint(null)}
              />
            );
          })}

          {[0.2, 0.4, 0.6, 0.8, 1].map((scale, i) => (
            <text
              key={i}
              x={centerX - 25}
              y={centerY - radius * scale}
              className="fill-gray-500 text-xs"
            >
              {Math.round(maxValue * scale)}
            </text>
          ))}
        </svg>
      </div>

      {hoveredPoint && (
        <div
          className="pointer-events-none absolute z-10 rounded bg-black px-2 py-1 text-xs text-white shadow-md"
          style={{
            left: hoveredPoint.x - 1000,
            top: hoveredPoint.y - 180,
            transform: "translate(-50%, -100%)"
          }}
        >
          {hoveredPoint.data.category}: {hoveredPoint.data.count} (
          {((hoveredPoint.data.count / totalCount) * 100).toFixed(1)}%)
        </div>
      )}
    </div>
  );
};

export default ReportTypeRadarChart;
