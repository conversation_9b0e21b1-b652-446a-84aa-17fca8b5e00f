import {
  ProgramReport,
  ProgramReportAttributes,
  ProgramReportStatus
} from "../models/program_report.model";
import { Op } from "sequelize";
import * as ejs from "ejs";
import * as path from "path";
import { database } from "../models/db";
import { UserRole } from "../utils/auth";
import { logger } from "../logger/index";
import { Sequelize, Transaction } from "sequelize";

// Helper functions from automated-report.controller.ts
function processReportsForDisplay(reports, retestStatusByReportId) {
  const severityAbbreviations = {
    Critical: "C",
    High: "H",
    Medium: "M",
    Low: "L"
  };
  const severityCounts = { Critical: 0, High: 0, Medium: 0, Low: 0 };
  const sortedReports = reports
    .filter(
      report =>
        report.severity_category !== undefined &&
        report.severity_category !== null
    )
    .sort((a, b) => {
      const dateA = new Date(a.submitted_date).getTime();
      const dateB = new Date(b.submitted_date).getTime();
      if (dateA !== dateB) return dateA - dateB;
      const severityOrder = { Critical: 0, High: 1, Medium: 2, Low: 3 };
      const severityA = severityOrder[a.severity_category] || 999;
      const severityB = severityOrder[b.severity_category] || 999;
      return severityA - severityB;
    })
    .map(report => {
      severityCounts[report.severity_category]++;
      const abbr =
        severityAbbreviations[report.severity_category] +
        severityCounts[report.severity_category];
      const status = retestStatusByReportId[report.report_id];
      const isOpen = !["Fix Verified", "Fix Approved"].includes(status);
      return {
        abbreviation: abbr,
        title: report.report_title,
        severity_category: report.severity_category,
        status: isOpen ? "Open" : "Closed",
        scope: report.scope,
        description: report.description,
        instructions: report.instructions,
        impact: report.impact,
        fix: report.fix,
        submitted_date: report.submitted_date
      };
    });
  return { sortedReports };
}

function extractTargetDetails(userPrograms) {
  const targetDetails = [];
  userPrograms.forEach(program => {
    if (program.targets) {
      try {
        let parsedTargets;
        if (typeof program.targets === "string") {
          try {
            parsedTargets = JSON.parse(program.targets);
          } catch (e) {
            parsedTargets = program.targets.split(",").map(t => t.trim());
          }
        } else {
          parsedTargets = program.targets;
        }
        if (Array.isArray(parsedTargets)) {
          parsedTargets.forEach(target => {
            if (
              target &&
              typeof target === "object" &&
              "targetName" in target &&
              "targetType" in target
            ) {
              const targetType = target.targetType;
              const targetUrl = target.targetName;
              const exists = targetDetails.some(
                t => t.type === targetType && t.url === targetUrl
              );
              if (!exists) {
                targetDetails.push({ type: targetType, url: targetUrl });
              }
            } else if (typeof target === "string") {
              const parts = target.split(":");
              if (parts.length >= 2) {
                const targetType = parts[0].trim();
                const targetUrl = parts.slice(1).join(":").trim();
                const exists = targetDetails.some(
                  t => t.type === targetType && t.url === targetUrl
                );
                if (!exists) {
                  targetDetails.push({ type: targetType, url: targetUrl });
                }
              }
            }
          });
        }
      } catch (error) {
        logger.log({
          level: "error",
          label: "program-report.service | extractTargetDetails",
          message: `Error parsing targets for program ${program.program_id}: ${error.message}`
        });
      }
    }
  });
  return targetDetails;
}

function determineDomains(userPrograms: any[]): string[] {
  const domains: string[] = [];
  if (userPrograms.length > 0) {
    if (userPrograms[0].testing_type) {
      const testingType =
        typeof userPrograms[0].testing_type === "string"
          ? userPrograms[0].testing_type.toLowerCase()
          : "";
      if ((testingType as string).includes("network")) domains.push("NET");
      else if ((testingType as string).includes("mobile")) domains.push("MOB");
      else if ((testingType as string).includes("api")) domains.push("API");
      else if ((testingType as string).includes("iot")) domains.push("IOT");
      else if ((testingType as string).includes("cloud")) domains.push("CLOUD");
      else domains.push("WEB");
    }
    if (userPrograms[0].targets) {
      try {
        let parsedTargets: any[] = [];
        if (typeof userPrograms[0].targets === "string") {
          try {
            parsedTargets = JSON.parse(userPrograms[0].targets);
          } catch (e) {
            parsedTargets = (userPrograms[0].targets as string)
              .split(",")
              .map((t: string) => t.trim());
          }
        } else if (Array.isArray(userPrograms[0].targets)) {
          parsedTargets = userPrograms[0].targets;
        }
        const targetTypes = new Set<string>();
        if (Array.isArray(parsedTargets)) {
          parsedTargets.forEach(target => {
            if (typeof target === "string") {
              const parts = target.split(":");
              if (parts.length >= 2)
                targetTypes.add(parts[0].trim().toLowerCase());
            } else if (target && typeof target === "object") {
              const type =
                (target as any).targetType || (target as any).targetTyoe;
              if (type) targetTypes.add(String(type).toLowerCase());
            }
          });
        }
        if (targetTypes.size > 0) {
          const networkTypes = ["network", "infrastructure"];
          const mobileTypes = ["mobile", "ios", "android"];
          const apiTypes = ["api"];
          const web3Types = ["web3", "smart contract", "blockchain"];
          const codeTypes = ["code", "source"];
          const dbTypes = ["database", "db"];
          const webTypes = ["web", "website"];
          for (const type of targetTypes) {
            if (networkTypes.some(t => type.includes(t))) {
              if (!domains.includes("NET")) domains.push("NET");
            } else if (mobileTypes.some(t => type.includes(t))) {
              if (!domains.includes("MOB")) domains.push("MOB");
            } else if (apiTypes.some(t => type.includes(t))) {
              if (!domains.includes("API")) domains.push("API");
            } else if (web3Types.some(t => type.includes(t))) {
              if (!domains.includes("WEB3")) domains.push("WEB3");
            } else if (codeTypes.some(t => type.includes(t))) {
              if (!domains.includes("CODE")) domains.push("CODE");
            } else if (dbTypes.some(t => type.includes(t))) {
              if (!domains.includes("DB")) domains.push("DB");
            } else if (webTypes.some(t => type.includes(t))) {
              if (!domains.includes("WEB")) domains.push("WEB");
            }
          }
        }
      } catch (error) {
        logger.log({
          level: "warn",
          label: "program-report.service | determineDomains",
          message: `Error parsing targets for domain determination: ${error.message}`
        });
        if (domains.length === 0) domains.push("WEB");
      }
    }
  }
  if (domains.length === 0) domains.push("WEB");
  return domains;
}

function generateDocumentNumber(
  programId: number,
  domains: string[] | string = ["Web"],
  existingNumbers: string[] = []
): string {
  const now = new Date();
  const month = (now.getMonth() + 1).toString().padStart(2, "0");
  const year = now.getFullYear().toString().slice(-2);
  const dateCode = `${month}${year}`;
  const docType = "TECH";
  const department = "CYBER";
  const normalizedDomains = Array.isArray(domains)
    ? [
        ...new Set(
          (domains as string[]).map(
            d => d.charAt(0).toUpperCase() + d.slice(1).toLowerCase()
          )
        )
      ]
    : [
        (domains as string).charAt(0).toUpperCase() +
          (domains as string).slice(1).toLowerCase()
      ];
  const finalDomainPart = normalizedDomains.sort().join("-");
  const baseNumber = `CTB-${docType}-${department}-${dateCode}-${programId}`;
  let highestVersion = 0;
  existingNumbers.forEach(number => {
    if (number.startsWith(baseNumber)) {
      const versionMatch = number.match(/-V\.(\d+)-/);
      if (versionMatch && versionMatch[1]) {
        const version = parseInt(versionMatch[1], 10);
        if (!isNaN(version) && version > highestVersion)
          highestVersion = version;
      }
    }
  });
  const nextVersion = (highestVersion + 1).toString().padStart(2, "0");
  const finalDocNumber = `${baseNumber}-V.${nextVersion}-${finalDomainPart}`;
  return finalDocNumber;
}

export class ProgramReportService {
  /**
   * Create a new program report
   */
  static async createReport(
    data: ProgramReportAttributes
  ): Promise<ProgramReport> {
    try {
      const report = await ProgramReport.create(data);
      return report;
    } catch (error) {
      throw new Error(`Failed to create program report: ${error.message}`);
    }
  }

  /**
   * Get a program report by ID
   */
  static async getReportById(id: string): Promise<ProgramReport | null> {
    try {
      const report = await ProgramReport.findByPk(id);
      return report;
    } catch (error) {
      throw new Error(`Failed to get program report: ${error.message}`);
    }
  }

  /**
   * Get a program report by automated report ID
   */
  static async getReportByAutomatedId(
    automatedReportId: string
  ): Promise<ProgramReport | null> {
    try {
      const report = await ProgramReport.findOne({
        where: {
          automated_report_id: automatedReportId
        },
        order: [["created_at", "DESC"]] // Get the most recent one if multiple exist
      });
      return report;
    } catch (error) {
      throw new Error(
        `Failed to get program report by automated ID: ${error.message}`
      );
    }
  }

  /**
   * Get all program reports with pagination and filters
   */
  static async getReports(options: {
    page?: number;
    limit?: number;
    status?: ProgramReportStatus;
    userId?: number;
    companyName?: string;
    search?: string;
    automatedReportStatus?: string;
    businessId?: number; // Add businessId parameter for filtering
  }): Promise<{
    reports: ProgramReport[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        userId,
        companyName,
        search,
        automatedReportStatus,
        businessId
      } = options;

      const offset = (page - 1) * limit;
      const whereClause: any = {};
      const include: any[] = [
        {
          model: database.automated_reports,
          as: "automated_report",
          attributes: ["business_id", "status"],
          required: true // This ensures only reports with valid automated_report_id are returned
        }
      ];

      // Add filters
      if (status) {
        whereClause.status = status;
      }

      if (userId) {
        whereClause.user_id = userId;
      }

      if (companyName) {
        whereClause.company_name = {
          [Op.like]: `%${companyName}%`
        };
      }

      if (search) {
        whereClause[Op.or] = [
          {
            company_name: {
              [Op.like]: `%${search}%`
            }
          },
          {
            report_title: {
              [Op.like]: `%${search}%`
            }
          },
          {
            document_number: {
              [Op.like]: `%${search}%`
            }
          }
        ];
      }

      // Filter by business_id to ensure data isolation
      if (businessId) {
        include[0].where = {
          business_id: businessId
        };
      }

      // If automatedReportStatus is set (for business users), allow all workflow statuses
      if (automatedReportStatus) {
        if (!include[0].where) {
          include[0].where = {};
        }
        include[0].where.status = {
          [Op.in]: [
            "approved",
            "business_review",
            "business_requested_changes",
            "changes_added",
            "report_updated",
            "business_approved"
          ]
        };
      }

      const { count, rows } = await ProgramReport.findAndCountAll({
        where: whereClause,
        include,
        order: [["created_at", "DESC"]],
        limit,
        offset
      });

      return {
        reports: rows,
        total: count,
        page,
        totalPages: Math.ceil(count / limit)
      };
    } catch (error) {
      throw new Error(`Failed to get program reports: ${error.message}`);
    }
  }

  /**
   * Update a program report (or create if it doesn't exist)
   */
  static async updateReport(
    id: string,
    data: Partial<ProgramReportAttributes>
  ): Promise<ProgramReport> {
    try {
      let report = await ProgramReport.findByPk(id);

      // Helper function to convert string dates to Date objects
      const convertDates = (obj: any): any => {
        if (!obj || typeof obj !== "object") return obj;

        const converted = { ...obj };
        const dateFields = [
          "current_date",
          "assessment_date",
          "date_of_request",
          "revision_date",
          "created_at",
          "updated_at"
        ];

        dateFields.forEach(field => {
          if (converted[field]) {
            if (typeof converted[field] === "string") {
              const date = new Date(converted[field]);
              // Check if the date is valid
              if (isNaN(date.getTime())) {
                // If invalid date, set to null for optional fields or today for required fields
                if (field === "current_date") {
                  converted[field] = new Date(); // Required field, use today
                } else if (field === "revision_date") {
                  converted[field] = new Date(); // Set to today as requested
                } else {
                  converted[field] = null; // Other optional fields, set to null
                }
              } else {
                converted[field] = date;
              }
            }
          } else if (field === "current_date") {
            // Required field, set to today if not provided
            converted[field] = new Date();
          } else if (field === "revision_date") {
            // Set to today if not provided, as requested
            converted[field] = new Date();
          } else {
            // Other optional fields, set to null if not provided
            converted[field] = null;
          }
        });

        return converted;
      };

      if (!report) {
        // If report doesn't exist, create a new one
        // This handles the case where we're saving initial data for the first time
        const convertedData = convertDates(data);

        const reportData: ProgramReportAttributes = {
          // Don't set id here - let UUID be generated automatically
          ...convertedData,
          // Ensure all required fields have default values
          user_id: convertedData.user_id || 1, // Default user ID if not provided
          role: convertedData.role || "user",
          status: convertedData.status || "draft",
          company_name: convertedData.company_name || "Unknown Company",
          report_title: convertedData.report_title || "Untitled Report",
          version_number: convertedData.version_number || "V1",
          document_number: convertedData.document_number || `DOC-${Date.now()}`,
          current_date: convertedData.current_date || new Date(),
          // Set default values for JSON fields
          program_details: convertedData.program_details || [],
          target_details: convertedData.target_details || [],
          severity_counts: convertedData.severity_counts || {},
          status_counts: convertedData.status_counts || {},
          open_close_counts_by_severity:
            convertedData.open_close_counts_by_severity || {},
          total_open: convertedData.total_open || 0,
          total_closed: convertedData.total_closed || 0,
          total_findings: convertedData.total_findings || 0,
          reports_list: convertedData.reports_list || [],
          vulnerability_ratings: convertedData.vulnerability_ratings || {},
          detailed_findings: convertedData.detailed_findings || [],
          recommendations_list: convertedData.recommendations_list || [],
          created_at: new Date(),
          updated_at: new Date(),
          branding_logo:
            convertedData.branding_logo ||
            "https://i.postimg.cc/QVPV9d0x/Full-name-logo-blue.png",
          branding_company:
            convertedData.branding_company || "Capture The Bug Ltd."
        };

        report = await ProgramReport.create(reportData);
        logger.log({
          level: "info",
          label: "program-report.service | updateReport",
          message: `Created new program report with ID: ${report.id} for automated report: ${id}`
        });
      } else {
        // Update existing report
        const convertedData = convertDates(data);

        await report.update({
          ...convertedData,
          updated_at: new Date(),
          branding_logo:
            convertedData.branding_logo ||
            "https://i.postimg.cc/QVPV9d0x/Full-name-logo-blue.png",
          branding_company:
            convertedData.branding_company || "Capture The Bug Ltd."
        });
        logger.log({
          level: "info",
          label: "program-report.service | updateReport",
          message: `Updated existing program report with ID: ${id}`
        });
      }

      return report;
    } catch (error) {
      logger.log({
        level: "error",
        label: "program-report.service | updateReport",
        message: `Failed to update/create program report: ${error.message}`
      });
      throw new Error(`Failed to update program report: ${error.message}`);
    }
  }

  /**
   * Update report status
   */
  static async updateStatus(
    id: string,
    status: ProgramReportStatus,
    userId: number,
    role: string
  ): Promise<ProgramReport> {
    try {
      const report = await ProgramReport.findByPk(id);
      if (!report) {
        throw new Error("Program report not found");
      }

      // Allow all new statuses for the new workflow
      await report.update({
        status,
        user_id: userId,
        role
      });

      return report;
    } catch (error) {
      throw new Error(`Failed to update report status: ${error.message}`);
    }
  }

  /**
   * Generate HTML preview for a program report
   */
  static async generatePreview(id: string, reportData: any): Promise<string> {
    try {
      const report = await ProgramReport.findByPk(id);
      if (!report) {
        throw new Error("Program report not found");
      }

      // Merge the current report data with the provided updates
      const mergedData = {
        ...report.toJSON(),
        ...reportData
      };

      // Load the EJS template
      const templatePath = path.join(__dirname, "../report-template/index.ejs");

      // Render the template with the report data
      const html = await ejs.renderFile(templatePath, {
        report: mergedData,
        currentDate: new Date().toLocaleDateString(),
        formatDate: (date: string | Date) => {
          if (!date) return "";
          const d = new Date(date);
          return d.toLocaleDateString();
        }
      });

      return html;
    } catch (error) {
      throw new Error(`Failed to generate preview: ${error.message}`);
    }
  }

  /**
   * Delete a program report (soft delete)
   */
  static async deleteReport(id: string): Promise<boolean> {
    try {
      const report = await ProgramReport.findByPk(id);
      if (!report) {
        throw new Error("Program report not found");
      }

      await report.destroy();
      return true;
    } catch (error) {
      throw new Error(`Failed to delete program report: ${error.message}`);
    }
  }

  /**
   * Get reports by automated report ID
   */
  static async getReportsByAutomatedId(
    automatedReportId: string,
    businessId?: number
  ): Promise<ProgramReport[]> {
    try {
      const include: any[] = [
        {
          model: database.automated_reports,
          as: "automated_report",
          attributes: ["business_id"],
          required: true,
          where: {
            id: automatedReportId
          }
        }
      ];

      const whereClause: any = {};

      // Filter by business_id if provided
      if (businessId) {
        include[0].where.business_id = businessId;
      }

      const reports = await ProgramReport.findAll({
        where: whereClause,
        include,
        order: [["created_at", "DESC"]]
      });

      return reports;
    } catch (error) {
      throw new Error(
        `Failed to get reports by automated ID: ${error.message}`
      );
    }
  }

  /**
   * Get reports statistics
   */
  static async getReportStatistics(businessId?: number): Promise<{
    total: number;
    byStatus: Record<ProgramReportStatus, number>;
    byCompany: Array<{ company: string; count: number }>;
    recentActivity: Array<{ date: string; count: number }>;
  }> {
    try {
      const include: any[] = [
        {
          model: database.automated_reports,
          as: "automated_report",
          attributes: ["business_id"],
          required: true
        }
      ];

      const whereClause: any = {};

      // Filter by business_id if provided
      if (businessId) {
        include[0].where = {
          business_id: businessId
        };
      }

      // Total reports
      const total = await ProgramReport.count({
        include,
        where: whereClause
      });

      // Reports by status
      const statusCounts = await ProgramReport.findAll({
        attributes: [
          "status",
          [ProgramReport.sequelize.fn("COUNT", "*"), "count"]
        ],
        include,
        where: whereClause,
        group: ["status", "automated_report.business_id"]
      });

      const byStatus = {
        draft: 0,
        qa_review: 0,
        admin_review: 0,
        approved: 0,
        rejected: 0,
        business_review: 0,
        business_requested_changes: 0,
        changes_added: 0,
        report_updated: 0,
        business_approved: 0
      };

      statusCounts.forEach((item: any) => {
        byStatus[item.status] = parseInt(item.dataValues.count);
      });

      // Reports by company
      const companyCounts = await ProgramReport.findAll({
        attributes: [
          "company_name",
          [ProgramReport.sequelize.fn("COUNT", "*"), "count"]
        ],
        include,
        where: whereClause,
        group: ["company_name", "automated_report.business_id"],
        order: [[ProgramReport.sequelize.fn("COUNT", "*"), "DESC"]],
        limit: 10
      });

      const byCompany = companyCounts.map((item: any) => ({
        company: item.company_name,
        count: parseInt(item.dataValues.count)
      }));

      // Recent activity (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const recentActivity = await ProgramReport.findAll({
        attributes: [
          [
            ProgramReport.sequelize.fn(
              "DATE",
              ProgramReport.sequelize.col("created_at")
            ),
            "date"
          ],
          [ProgramReport.sequelize.fn("COUNT", "*"), "count"]
        ],
        include,
        where: {
          ...whereClause,
          created_at: {
            [Op.gte]: thirtyDaysAgo
          }
        },
        group: [
          ProgramReport.sequelize.fn(
            "DATE",
            ProgramReport.sequelize.col("created_at")
          ),
          "automated_report.business_id"
        ],
        order: [
          [
            ProgramReport.sequelize.fn(
              "DATE",
              ProgramReport.sequelize.col("created_at")
            ),
            "ASC"
          ]
        ]
      });

      const activityData = recentActivity.map((item: any) => ({
        date: item.dataValues.date,
        count: parseInt(item.dataValues.count)
      }));

      return {
        total,
        byStatus,
        byCompany,
        recentActivity: activityData
      };
    } catch (error) {
      throw new Error(`Failed to get report statistics: ${error.message}`);
    }
  }

  /**
   * Create report from automated report data
   */
  static async createFromAutomatedReport(
    automatedReportId: string,
    userId: number,
    role: string,
    reportData: any
  ): Promise<ProgramReport> {
    try {
      const report = await ProgramReport.create({
        ...reportData,
        automated_report_id: automatedReportId,
        user_id: userId,
        role,
        status: "draft"
      });

      return report;
    } catch (error) {
      throw new Error(
        `Failed to create report from automated data: ${error.message}`
      );
    }
  }

  /**
   * Get reports that need review
   */
  static async getReportsForReview(
    businessId?: number
  ): Promise<ProgramReport[]> {
    try {
      const include: any[] = [
        {
          model: database.automated_reports,
          as: "automated_report",
          attributes: ["business_id"],
          required: true
        }
      ];

      const whereClause: any = {
        status: {
          [Op.in]: ["qa_review", "admin_review"]
        }
      };

      // Filter by business_id if provided
      if (businessId) {
        include[0].where = {
          business_id: businessId
        };
      }

      const reports = await ProgramReport.findAll({
        where: whereClause,
        include,
        order: [["updated_at", "ASC"]]
      });

      return reports;
    } catch (error) {
      throw new Error(`Failed to get reports for review: ${error.message}`);
    }
  }

  static async getInitialProgramReportData(
    report_id: string,
    user: any,
    transaction?: Transaction
  ) {
    try {
      const { user_id, role } = user;
      console.log("getInitialProgramReportData - User:", {
        user_id,
        role,
        roleType: typeof role
      });

      // Only admin roles allowed (match automated-report logic)
      const allowedRoles = [
        UserRole.ADMIN,
        UserRole.ADMIN_MANAGER,
        UserRole.SUB_ADMIN,
        UserRole.QA
      ];
      console.log("getInitialProgramReportData - Allowed roles:", allowedRoles);
      console.log(
        "getInitialProgramReportData - User role in allowed roles:",
        allowedRoles.includes(role)
      );

      if (!allowedRoles.includes(role)) {
        console.log(
          "getInitialProgramReportData - User role not allowed:",
          role
        );
        return null;
      }

      // Get automated report details
      const AutomatedReports = database.automated_reports;
      const AutomatedReportVersions = database.automated_report_versions;
      const Programs = database.program;
      const Reports = database.report;
      const Users = database.user;
      const RetestLog = database.retest;

      console.log(
        "getInitialProgramReportData - Looking for automated report with ID:",
        report_id
      );
      const automatedReport = await AutomatedReports.findOne({
        where: { id: report_id },
        include: [
          {
            model: AutomatedReportVersions,
            as: "versions",
            attributes: ["version_number"]
          }
        ],
        transaction
      });

      console.log(
        "getInitialProgramReportData - Automated report found:",
        !!automatedReport
      );
      if (!automatedReport) {
        console.log(
          "getInitialProgramReportData - No automated report found with ID:",
          report_id
        );
        return null;
      }
      const versionNumber =
        (automatedReport as any).versions?.length > 0
          ? (automatedReport as any).versions[0].version_number
          : 1;
      const formattedVersionNumber = `V${versionNumber}`;
      const programIds = Array.isArray(automatedReport.program_ids)
        ? automatedReport.program_ids
        : JSON.parse(automatedReport.program_ids || "[]");

      console.log("getInitialProgramReportData - Program IDs:", programIds);
      console.log(
        "getInitialProgramReportData - Program IDs type:",
        typeof programIds
      );
      console.log(
        "getInitialProgramReportData - Program IDs is array:",
        Array.isArray(programIds)
      );
      console.log(
        "getInitialProgramReportData - Program IDs length:",
        programIds?.length
      );

      if (!Array.isArray(programIds) || programIds.length === 0) {
        console.log("getInitialProgramReportData - No valid program IDs found");
        return null;
      }

      console.log(
        "getInitialProgramReportData - Looking for programs with IDs:",
        programIds
      );
      const [userPrograms, existingReports] = await Promise.all([
        Programs.findAll({
          where: {
            program_id: { [Op.in]: programIds },
            ...(role !== UserRole.ADMIN && {
              user_id: automatedReport.business_id
            }) // Only check user_id for non-admin users
            // Removed is_delete and is_activated filters to allow inactive/deleted programs
          },
          attributes: [
            "program_id",
            "private_access_users",
            "targets",
            "testing_type",
            "expected_start_date",
            "expected_end_date",
            "program_title",
            "scope",
            "test_lead",
            "prepared_by",
            "reviewed_by",
            "approved_by"
          ],
          transaction
        }),
        AutomatedReports.findAll({
          where: Sequelize.literal(
            `JSON_CONTAINS(program_ids, '[${programIds[0]}]')`
          ),
          transaction
        })
      ]);

      console.log(
        "getInitialProgramReportData - Found user programs:",
        userPrograms.length
      );
      console.log("getInitialProgramReportData - User role:", role);
      console.log(
        "getInitialProgramReportData - Business ID:",
        automatedReport.business_id
      );

      if (userPrograms.length === 0) {
        console.log("getInitialProgramReportData - No user programs found");
        return null;
      }
      const validProgramIds = userPrograms.map(program => program.program_id);
      const [reports, reportIds, testerUsernames] = await Promise.all([
        Reports.findAll({
          where: {
            program_id: { [Op.in]: validProgramIds },
            state: { [Op.notIn]: ["under_review", "deleted"] }
          },
          include: [
            {
              model: Programs,
              attributes: ["program_title", "scope", "targets"]
            }
          ],
          order: [["submitted_date", "ASC"]],
          transaction
        }),
        (async () => {
          const ids = existingReports.map(report => report.id);
          return ids.length > 0 ? ids : [0];
        })(),
        (async () => {
          const allTesterIds = new Set();
          userPrograms.forEach(program => {
            try {
              let testerIds = [];
              if (program.private_access_users) {
                const parsed =
                  typeof program.private_access_users === "string"
                    ? JSON.parse(program.private_access_users)
                    : program.private_access_users;
                testerIds = Array.isArray(parsed) ? parsed : [];
              }
              if (Array.isArray(testerIds)) {
                testerIds.forEach(id => {
                  if (typeof id === "number") allTesterIds.add(id);
                });
              }
            } catch (e) {
              logger.log({
                level: "error",
                label: "program-report.service | getInitialProgramReportData",
                message: `Error parsing private_access_users for program ${program.program_id}`
              });
            }
          });
          const testerIds = Array.from(allTesterIds);
          if (testerIds.length === 0) return [];
          return Users.findAll({
            where: { user_id: { [Op.in]: testerIds as number[] } },
            attributes: ["username", "user_id"],
            transaction
          });
        })()
      ]);
      const [retestData, existingVersions] = await Promise.all([
        database.retest.findAll({
          where: {
            report_id: { [Op.in]: reports.map(report => report.report_id) }
          },
          order: [["created_at", "DESC"]],
          transaction
        }),
        database.automated_report_versions.findAll({
          where: { report_id: { [Op.in]: reportIds } },
          attributes: ["id", "pdf_url"],
          transaction
        })
      ]);
      const retestStatusByReportId = {};
      retestData.forEach(retest => {
        if (!retestStatusByReportId[retest.report_id])
          retestStatusByReportId[retest.report_id] = retest.status;
      });
      const existingDocNumbers = existingVersions
        .map(version => {
          if (version.pdf_url) {
            const match = version.pdf_url.match(
              /CTB-TECH-CYBER-\d{4}-\d+-V\.(\d+)-[A-Z]+/
            );
            return match ? match[0] : null;
          }
          return null;
        })
        .filter(Boolean);
      const domains = determineDomains(userPrograms);
      const documentNumber = generateDocumentNumber(
        programIds[0],
        domains,
        existingDocNumbers
      );
      const allFixFields = reports
        .map(r => r.fix)
        .filter(Boolean)
        .map(f => String(f));
      let recommendationsSummary = "";
      try {
        recommendationsSummary = allFixFields.join("\n\n");
      } catch (e) {
        logger.log({
          level: "error",
          label: "program-report.service | getInitialProgramReportData",
          message: "Failed to process recommendations content"
        });
      }
      const contributorDetails = testerUsernames.map(user => ({
        user_id: user.user_id,
        username: user.username
      }));
      const severitySummary = {
        Critical: reports.filter(r => r.severity_category === "Critical")
          .length,
        High: reports.filter(r => r.severity_category === "High").length,
        Medium: reports.filter(r => r.severity_category === "Medium").length,
        Low: reports.filter(r => r.severity_category === "Low").length,
        Total: reports.length
      };
      const closedStatuses = ["Fix Verified", "Fix Approved"];
      const closedCount = reports.filter(r =>
        closedStatuses.includes(retestStatusByReportId[r.report_id])
      ).length;
      const openClosedCounts = {
        Closed: closedCount,
        Open: reports.length - closedCount
      };
      const openCloseBySeverity = {
        Critical: { Open: 0, Closed: 0 },
        High: { Open: 0, Closed: 0 },
        Medium: { Open: 0, Closed: 0 },
        Low: { Open: 0, Closed: 0 }
      };
      reports.forEach(report => {
        let severity = report.severity_category;
        const status = retestStatusByReportId[report.report_id];
        const isClosed = closedStatuses.includes(status);
        if (severity) {
          severity =
            severity.charAt(0).toUpperCase() + severity.slice(1).toLowerCase();
          if (["Critical", "High", "Medium", "Low"].includes(severity)) {
            if (isClosed) openCloseBySeverity[severity].Closed++;
            else openCloseBySeverity[severity].Open++;
          }
        }
      });
      Object.entries(openCloseBySeverity).forEach(([severity, counts]) => {
        openCloseBySeverity[severity].Total = counts.Open + counts.Closed;
      });
      const { sortedReports } = processReportsForDisplay(
        reports,
        retestStatusByReportId
      );
      const programDetails = userPrograms.map(program => ({
        program_id: program.program_id,
        program_title: program.program_title,
        testing_type: program.testing_type,
        targets: program.targets
          ? typeof program.targets === "string"
            ? program.targets.split(",").map(t => t.trim())
            : program.targets
          : [],
        expected_start_date: program.expected_start_date,
        test_lead: program.test_lead,
        prepared_by: program.prepared_by,
        reviewed_by: program.reviewed_by,
        approved_by: program.approved_by
      }));
      const targetDetails = extractTargetDetails(userPrograms);
      const dateOfRequest = new Date().toISOString().split("T")[0];

      // Get program name from the first program or use automated report's program_name
      let programName = automatedReport.program_name;
      if (!programName && userPrograms.length > 0) {
        programName = userPrograms[0].program_title;
      }

      return {
        company_name: automatedReport.company_name,
        program_name: programName,
        report_title: automatedReport.title,
        version_number: formattedVersionNumber,
        document_number: documentNumber,
        date_of_request: dateOfRequest,
        test_lead:
          programDetails.length > 0 && programDetails[0].test_lead
            ? String(programDetails[0].test_lead)
            : null,
        prepared_by:
          programDetails.length > 0 && programDetails[0].prepared_by
            ? String(programDetails[0].prepared_by)
            : null,
        reviewed_by:
          programDetails.length > 0 && programDetails[0].reviewed_by
            ? String(programDetails[0].reviewed_by)
            : null,
        approved_by:
          programDetails.length > 0 && programDetails[0].approved_by
            ? String(programDetails[0].approved_by)
            : null,
        program_details: programDetails,
        target_details: targetDetails,
        severity_counts: severitySummary,
        status_counts: openClosedCounts,
        open_close_counts_by_severity: openCloseBySeverity,
        total_findings: reports.length,
        total_open: openClosedCounts.Open,
        total_closed: openClosedCounts.Closed,
        reports_list: sortedReports.map(report => ({
          abbreviation: report.abbreviation,
          title: report.title,
          severity_category: report.severity_category,
          status: report.status
        })),
        recommendations_summary: recommendationsSummary,
        detailed_findings: sortedReports.map(report => ({
          abbreviation: report.abbreviation,
          title: report.title,
          scope: report.scope,
          description: report.description,
          instructions: report.instructions,
          impact: report.impact,
          fix: report.fix,
          submitted_date: report.submitted_date,
          severity_category: report.severity_category
        })),
        contributor_details: contributorDetails,
        automated_report_id: report_id,
        status: automatedReport.status || "draft",
        // Add default content for editable fields
        executive_summary: "",
        key_findings: "",
        scope: "",
        methodology: { web: true, network: false, mobile: false },
        findings: "",
        recommendations: "",
        conclusion: "",
        disclaimer: "",
        current_date: new Date().toISOString().split("T")[0],
        revision_date: new Date().toISOString().split("T")[0],
        recommendations_list: []
      };
    } catch (error) {
      logger.log({
        level: "error",
        label: "program-report.service | getInitialProgramReportData",
        message: `Error retrieving program reports: ${error.message}`
      });
      return null;
    }
  }
}
