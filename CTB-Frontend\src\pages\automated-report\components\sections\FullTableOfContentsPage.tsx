import React from 'react';
import { Page, View, Text, Link } from '@react-pdf/renderer';
import { ReportData } from '../../types/report.types';
import type { DetailedFinding } from '../../types';
import { useSectionPages } from '../SectionPageContext';

interface FullTableOfContentsPageProps {
  reportData: ReportData;
}

// Color palette for severity levels
const severityColors: Record<string, string> = {
  Critical: '#800000', // Maroon
  High: '#dc2626',     // Red
  Medium: '#fbbf24',   // Yellow
  Low: '#10b981',      // Green
  Informational: '#3b82f6', // Blue or keep as is
};

const accentColor = '#2563eb';
const dividerColor = '#e5e7eb';
const appendixBg = '#f3f4f6';

const FullTableOfContentsPage: React.FC<FullTableOfContentsPageProps> = ({ reportData }) => {
  const { sectionPages } = useSectionPages();
  // Group findings by severity
  const findingsBySeverity = (reportData.detailed_findings || []).reduce(
    (acc: Record<string, DetailedFinding[]>, finding: DetailedFinding) => {
      const severity = finding.severity_category || 'Informational';
      if (!acc[severity]) {
        acc[severity] = [];
      }
      acc[severity].push(finding);
      return acc;
    },
    {} as Record<string, DetailedFinding[]>
  );

  // Helper for divider
  const Divider = () => (
    <View style={{ height: 1, backgroundColor: dividerColor, marginVertical: 8, opacity: 0.7 }} />
  );

  return (
    <Page size="A4" style={{ flexDirection: 'column', backgroundColor: '#ffffff', padding: '20mm 15mm', fontFamily: 'Helvetica', fontSize: 12 }}>
      {/* Premium Header */}
      <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 24 }}>
        <View style={{ width: 6, height: 32, backgroundColor: accentColor, borderRadius: 3, marginRight: 12 }} />
        <Text style={{ fontSize: 16, fontWeight: 'bold', color: accentColor, letterSpacing: 1.2, lineHeight: 1.4 }}>TABLE OF CONTENTS</Text>
      </View>

      {/* Main sections with accent bar */}
      <View style={{ marginBottom: 16 }}>
        {[
          { num: '1.', label: 'Disclaimer', key: 'Disclaimer' },
          { num: '2.', label: 'Document Reference', key: 'DocumentReference' },
          { num: '3.', label: 'Executive Summary', key: 'ExecutiveSummary' },
          { num: '4.', label: 'Key Findings', key: 'KeyFindings' },
          { num: '5.', label: 'Scope', key: 'Scope' },
          { num: '6.', label: 'Project Objectives', key: 'ProjectObjectives' },
          { num: '7.', label: 'Summary of Findings', key: 'SummaryOfFindings' },
          { num: '8.', label: 'Vulnerability Rating Definitions', key: 'VulnerabilityRatingDefinitions' },
          { num: '9.', label: 'Key Findings List', key: 'KeyFindingsList' },
        ].map((section, idx) => (
          <View key={section.key} style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
            <View style={{ width: 4, height: 16, backgroundColor: accentColor, borderRadius: 2, marginRight: 8, opacity: 0.7 }} />
            <Text style={{ fontSize: 12, width: 28, color: '#6b7280', fontWeight: 'bold', lineHeight: 1.4 }}>{section.num}</Text>
            <Link src={`#${section.key}`} style={{ fontSize: 12, flex: 1, color: '#1f2937', fontWeight: 500, lineHeight: 1.4, textDecoration: 'none' }}>{section.label}</Link>
            <Text style={{ fontSize: 12, width: 32, textAlign: 'right', color: accentColor, fontWeight: 'bold', lineHeight: 1.4 }}>{sectionPages[section.key] || ''}</Text>
          </View>
        ))}
      </View>

      <Divider />

      {/* Detailed Findings Section - Color Coded */}
      <View style={{ marginBottom: 16 }}>
        <Text style={{ fontSize: 14, fontWeight: 'bold', marginBottom: 8, color: accentColor, letterSpacing: 0.8, lineHeight: 1.4 }}>DETAILED FINDINGS BY SEVERITY</Text>
        {['Critical', 'High', 'Medium', 'Low', 'Informational'].map((severity) => {
          // Find the actual key in findingsBySeverity that matches this severity (case-insensitive)
          const actualKey = Object.keys(findingsBySeverity).find(
            k => k.toLowerCase() === severity.toLowerCase()
          );
          if (!actualKey) return null;
          const sectionKey = `DetailedFindings_${severity}`;
          const sectionPage = sectionPages[sectionKey] || '';
          // Severity initial for abbreviation
          const severityInitial = severity.charAt(0).toUpperCase();
          return (
            <View key={severity} style={{ marginBottom: 8 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 2 }}>
                <View style={{ width: 8, height: 14, backgroundColor: severityColors[severity], borderRadius: 2, marginRight: 8 }} />
                <Text style={{ fontSize: 12, flex: 1, fontWeight: 'bold', color: severityColors[severity], letterSpacing: 0.5, lineHeight: 1.4 }}>
                  <Link src={`#${sectionKey}`} style={{ color: severityColors[severity], textDecoration: 'none' }}>{severity.toUpperCase()} SEVERITY FINDINGS</Link>
                </Text>
                <Text style={{ fontSize: 12, width: 32, textAlign: 'right', color: severityColors[severity], fontWeight: 'bold', lineHeight: 1.4 }}>{sectionPage}</Text>
              </View>
              {findingsBySeverity[actualKey]?.map((finding: DetailedFinding, idx: number) => {
                // Generate abbreviation if not present
                const abbreviation = finding.abbreviation || `${severityInitial}${idx + 1}`;
                return (
                  <View key={String(abbreviation) + String(finding.title)} style={{ flexDirection: 'row', marginLeft: 32, marginBottom: 2, alignItems: 'center' }}>
                    {/* Abbreviation to far left, color-coded, bold, fixed width */}
                    <Text style={{
                      fontWeight: 'bold',
                      color: severityColors[severity],
                      fontSize: 12,
                      width: 32,
                      textAlign: 'left',
                      letterSpacing: 1,
                      marginRight: 8,
                      lineHeight: 1.4
                    }}>{abbreviation}</Text>
                    {/* Title next to abbreviation */}
                    <Text style={{ fontSize: 12, flex: 1, color: '#374151', lineHeight: 1.4 }}>{finding.title}</Text>
                  </View>
                );
              })}
            </View>
          );
        })}
      </View>

      <Divider />

      {/* Appendix - Soft background */}
      <View style={{ backgroundColor: appendixBg, borderRadius: 8, padding: 10, marginBottom: 8 }}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <View style={{ width: 4, height: 18, backgroundColor: accentColor, borderRadius: 2, marginRight: 8, opacity: 0.7 }} />
          <Text style={{ fontSize: 12, width: 28, color: '#6b7280', fontWeight: 'bold', lineHeight: 1.4 }}>10.</Text>
          <Text style={{ fontSize: 12, flex: 1, color: '#1f2937', fontWeight: 500, lineHeight: 1.4 }}>
            <Link src="#AppendixOwaspRiskRating_Page1" style={{ color: '#1f2937', textDecoration: 'none' }}>Appendix A: OWASP Risk Rating Methodology</Link>
          </Text>
          <Text style={{ fontSize: 12, width: 32, textAlign: 'right', color: accentColor, fontWeight: 'bold', lineHeight: 1.4 }}>{sectionPages['AppendixOwaspRiskRating_Page1'] || ''}</Text>
        </View>
      </View>
      {/* Conditionally render methodology sections */}
      {(() => {
        const meth = reportData.methodology || {};
        let num = 11;
        const rows = [];
        if (meth.web) {
          rows.push(
            <View key="web" style={{ backgroundColor: appendixBg, borderRadius: 8, padding: 10, marginBottom: 8 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <View style={{ width: 4, height: 18, backgroundColor: accentColor, borderRadius: 2, marginRight: 8, opacity: 0.7 }} />
                <Text style={{ fontSize: 12, width: 28, color: '#6b7280', fontWeight: 'bold', lineHeight: 1.4 }}>{num++ + '.'}</Text>
                <Text style={{ fontSize: 12, flex: 1, color: '#1f2937', fontWeight: 500, lineHeight: 1.4 }}>
                  <Link src="#Methodology" style={{ color: '#1f2937', textDecoration: 'none' }}>Methodology</Link>
                </Text>
                <Text style={{ fontSize: 12, width: 32, textAlign: 'right', color: accentColor, fontWeight: 'bold', lineHeight: 1.4 }}>{sectionPages['Methodology'] || ''}</Text>
              </View>
            </View>
          );
        }
        if (meth.network) {
          rows.push(
            <View key="network" style={{ backgroundColor: appendixBg, borderRadius: 8, padding: 10, marginBottom: 8 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <View style={{ width: 4, height: 18, backgroundColor: accentColor, borderRadius: 2, marginRight: 8, opacity: 0.7 }} />
                <Text style={{ fontSize: 12, width: 28, color: '#6b7280', fontWeight: 'bold', lineHeight: 1.4 }}>{num++ + '.'}</Text>
                <Text style={{ fontSize: 12, flex: 1, color: '#1f2937', fontWeight: 500, lineHeight: 1.4 }}>
                  <Link src="#NetworkMethodology" style={{ color: '#1f2937', textDecoration: 'none' }}>Network Methodology</Link>
                </Text>
                <Text style={{ fontSize: 12, width: 32, textAlign: 'right', color: accentColor, fontWeight: 'bold', lineHeight: 1.4 }}>{sectionPages['NetworkMethodology'] || ''}</Text>
              </View>
            </View>
          );
        }
        if (meth.mobile) {
          rows.push(
            <View key="mobile" style={{ backgroundColor: appendixBg, borderRadius: 8, padding: 10, marginBottom: 8 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <View style={{ width: 4, height: 18, backgroundColor: accentColor, borderRadius: 2, marginRight: 8, opacity: 0.7 }} />
                <Text style={{ fontSize: 12, width: 28, color: '#6b7280', fontWeight: 'bold', lineHeight: 1.4 }}>{num++ + '.'}</Text>
                <Text style={{ fontSize: 12, flex: 1, color: '#1f2937', fontWeight: 500, lineHeight: 1.4 }}>
                  <Link src="#MobileMethodology" style={{ color: '#1f2937', textDecoration: 'none' }}>Mobile Methodology</Link>
                </Text>
                <Text style={{ fontSize: 12, width: 32, textAlign: 'right', color: accentColor, fontWeight: 'bold', lineHeight: 1.4 }}>{sectionPages['MobileMethodology'] || ''}</Text>
              </View>
            </View>
          );
        }
        return rows;
      })()}
    </Page>
  );
};

export default FullTableOfContentsPage; 