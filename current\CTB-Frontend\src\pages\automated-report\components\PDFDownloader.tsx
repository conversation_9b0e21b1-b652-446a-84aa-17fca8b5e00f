import React, { forwardRef, useImperativeHandle } from 'react';
import { pdf } from '@react-pdf/renderer';
import ReportTemplatePDF from './ReportTemplatePDF';
import FullReportTemplatePDF from './FullReportTemplatePDF';
import { ReportData } from '../types/report.types';
import ChartImageGenerator from './ChartImageGenerator';

interface PDFDownloaderProps {
  reportData: ReportData;
  fileName?: string;
  compact?: boolean;
  onClick?: () => void;
  disabled?: boolean;
  processedFindings?: Record<string, any[]> | null;
}

const PDFDownloader = forwardRef<any, PDFDownloaderProps>(({ 
  reportData, 
  fileName = 'security-report.pdf',
  compact = false,
  onClick,
  disabled = false,
  processedFindings
}, ref) => {
  const [pieChartImage, setPieChartImage] = React.useState<string | undefined>();
  const [barChartImage, setBarChartImage] = React.useState<string | undefined>();

  // Ensure methodology sections are always included unless explicitly set to false
  const reportDataWithMethodology = {
    ...reportData,
    methodology: {
      web: true,
      network: true,
      mobile: true,
      ...reportData.methodology
    }
  };

  const pieData = {
    labels: ['Critical', 'High', 'Medium', 'Low'],
    datasets: [{
      data: [
        reportData.open_close_counts_by_severity?.Critical?.Total || 0,
        reportData.open_close_counts_by_severity?.High?.Total || 0,
        reportData.open_close_counts_by_severity?.Medium?.Total || 0,
        reportData.open_close_counts_by_severity?.Low?.Total || 0,
      ],
      backgroundColor: ['#8b0000', '#ff4500', '#ffd700', '#32cd32'],
    }],
  };

  const barData = {
    labels: ['Critical', 'High', 'Medium', 'Low'],
    datasets: [{
      label: 'Number of Findings',
      data: [
        reportData.open_close_counts_by_severity?.Critical?.Total || 0,
        reportData.open_close_counts_by_severity?.High?.Total || 0,
        reportData.open_close_counts_by_severity?.Medium?.Total || 0,
        reportData.open_close_counts_by_severity?.Low?.Total || 0,
      ],
      backgroundColor: ['#8b0000', '#ff4500', '#ffd700', '#32cd32'],
    }],
  };

  const chartsReady = pieChartImage && barChartImage;

  const downloadPDF = async () => {
    if (!chartsReady) return;
    try {
      const reportDataWithMethodology = {
        ...reportData,
        methodology: {
          web: true,
          network: true,
          mobile: true,
          ...reportData.methodology
        }
      };
      const blob = await pdf(
        // Try both templates, but apply the logic for both
        <>
          {typeof ReportTemplatePDF !== 'undefined' ? (
            <ReportTemplatePDF
              reportData={reportDataWithMethodology}
              processedFindings={processedFindings}
              pieChartImage={pieChartImage}
              barChartImage={barChartImage}
            />
          ) : null}
          {typeof FullReportTemplatePDF !== 'undefined' ? (
            <FullReportTemplatePDF
              reportData={reportDataWithMethodology}
              processedFindings={processedFindings}
              pieChartImage={pieChartImage}
              barChartImage={barChartImage}
            />
          ) : null}
        </>
      ).toBlob();
      
      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      
      // Cleanup
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please try again.');
    }
  };

  useImperativeHandle(ref, () => ({
    downloadPDF
  }));

  if (compact) {
    return (
      <>
        {!pieChartImage && (
          <ChartImageGenerator
            type="pie"
            data={pieData}
            onImageReady={setPieChartImage}
          />
        )}
        {!barChartImage && (
          <ChartImageGenerator
            type="bar"
            data={barData}
            options={{
              scales: { y: { beginAtZero: true, ticks: { precision: 0 } } },
              plugins: { legend: { display: false } },
            }}
            onImageReady={setBarChartImage}
          />
        )}
        <button
          onClick={onClick}
          title="Download PDF Report"
          className="flex items-center justify-center p-2 rounded-lg bg-white/20 hover:bg-white/30 transition-colors shadow-sm"
          disabled={disabled || !chartsReady}
        >
          <svg 
            className="w-5 h-5 text-white" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth="2" 
              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" 
            />
          </svg>
        </button>
      </>
    );
  }

  return (
    <>
      {!pieChartImage && (
        <ChartImageGenerator
          type="pie"
          data={pieData}
          onImageReady={setPieChartImage}
        />
      )}
      {!barChartImage && (
        <ChartImageGenerator
          type="bar"
          data={barData}
          options={{
            scales: { y: { beginAtZero: true, ticks: { precision: 0 } } },
            plugins: { legend: { display: false } },
          }}
          onImageReady={setBarChartImage}
        />
      )}
    
    </>
  );
});

export default PDFDownloader; 