import React from "react";
import parse from "html-react-parser";

const termsText = `Please refer to <a href="https://capturethebug.xyz/Useful-Links/Customer-Terms&Conditions" className="text-ctb-blue-600 hover:underline">CTB Terms & Conditions</a>`;

const TermsConditions: React.FC<{ termsContent?: string }> = ({ termsContent }) => {
  return (
    <div className="p-5 bg-white shadow-md rounded-lg">
      <h2 className="text-lg font-semibold text-ctb-blue-600 mb-3">Terms & Conditions</h2>
      <div className="text-sm text-gray-700">
        {termsContent ? parse(termsContent) : parse(termsText)}
      </div>
    </div>
  );
};

export default TermsConditions;
