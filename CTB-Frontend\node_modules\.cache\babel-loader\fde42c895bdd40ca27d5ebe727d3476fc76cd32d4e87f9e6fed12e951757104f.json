{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\jira-old\\\\CTB-Frontend\\\\src\\\\pages\\\\automated-report\\\\components\\\\sections\\\\DisclaimerPage.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Page, View, Text } from '@react-pdf/renderer';\nimport { useSectionPages } from '../SectionPageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DEFAULT_COMPANY = 'Capture The Bug Ltd.';\nconst DisclaimerPage = ({\n  reportData,\n  disclaimerParagraphs,\n  processDisclaimerContent,\n  sectionId\n}) => {\n  _s();\n  const {\n    updateSectionPage\n  } = useSectionPages();\n  return /*#__PURE__*/_jsxDEV(Page, {\n    size: \"A4\",\n    id: sectionId,\n    style: {\n      flexDirection: 'column',\n      backgroundColor: '#ffffff',\n      padding: '20mm 15mm',\n      fontFamily: 'Helvetica',\n      fontSize: 12\n    },\n    children: [/*#__PURE__*/_jsxDEV(View, {\n      style: {\n        paddingHorizontal: 24,\n        flexDirection: 'column',\n        flex: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(View, {\n        style: {\n          flex: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            fontSize: 16,\n            fontWeight: 'bold',\n            color: '#2563eb',\n            marginBottom: 16\n          },\n          children: \"DISCLAIMER\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), disclaimerParagraphs.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: disclaimerParagraphs.map((paragraph, index) => /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              fontSize: 12,\n              lineHeight: 1.4,\n              marginBottom: 18,\n              color: '#374151',\n              textAlign: 'justify',\n              fontFamily: 'Helvetica',\n              fontWeight: 400\n            },\n            children: paragraph.trim()\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 17\n          }, this))\n        }, void 0, false) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: processDisclaimerContent(`<p>${reportData.branding_company || DEFAULT_COMPANY} has prepared this document exclusively for ${reportData.company_name}. Copying, or modification of this document is strictly prohibited without ${reportData.branding_company || DEFAULT_COMPANY}'s written consent, except for specific purposes when such permission is granted. This document is confidential and proprietary material of ${reportData.branding_company || DEFAULT_COMPANY} and must always be treated as such, not to be disclosed to third parties without prior consent.</p>\n<p>The information within is provided 'as-is,' without any warranty. Vulnerability assessments offer a snapshot of security at a specific moment and are not exhaustive risk assessments. New vulnerabilities may emerge post-assessment. Thus, this report is a guide, not a definitive risk analysis.</p>\n<p>${reportData.branding_company || DEFAULT_COMPANY} assumes no liability for any changes, omissions, or errors in this document. ${reportData.branding_company || DEFAULT_COMPANY} shall not be liable for any damages, financial or otherwise, arising out of the use or misuse of this report by any current employee of ${reportData.company_name} or any member of the general public</p>`).map((paragraph, index) => /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              fontSize: 12,\n              lineHeight: 1.4,\n              marginBottom: 18,\n              color: '#374151',\n              textAlign: 'justify',\n              fontFamily: 'Helvetica',\n              fontWeight: 400\n            },\n            children: paragraph.trim()\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 17\n          }, this))\n        }, void 0, false)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      style: {\n        display: 'none'\n      },\n      render: ({\n        pageNumber\n      }) => {\n        updateSectionPage('Disclaimer', pageNumber);\n        return '';\n      },\n      fixed: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n};\n_s(DisclaimerPage, \"3rf3HaIqG9R7of1rm8wX9C3kpH0=\", false, function () {\n  return [useSectionPages];\n});\n_c = DisclaimerPage;\nexport default DisclaimerPage;\nvar _c;\n$RefreshReg$(_c, \"DisclaimerPage\");", "map": {"version": 3, "names": ["React", "Page", "View", "Text", "useSectionPages", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DEFAULT_COMPANY", "DisclaimerPage", "reportData", "disclaimerParagraphs", "processDisclaimerContent", "sectionId", "_s", "updateSectionPage", "size", "id", "style", "flexDirection", "backgroundColor", "padding", "fontFamily", "fontSize", "children", "paddingHorizontal", "flex", "fontWeight", "color", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "paragraph", "index", "lineHeight", "textAlign", "trim", "branding_company", "company_name", "display", "render", "pageNumber", "fixed", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/jira-old/CTB-Frontend/src/pages/automated-report/components/sections/DisclaimerPage.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Page, View, Text } from '@react-pdf/renderer';\r\nimport { ReportData } from '../../types/report.types';\r\nimport { useSectionPages } from '../SectionPageContext';\r\n\r\ninterface DisclaimerPageProps {\r\n  reportData: ReportData;\r\n  disclaimerParagraphs: string[];\r\n  processDisclaimerContent: (content: string) => string[];\r\n  sectionId?: string;\r\n}\r\n\r\nconst DEFAULT_COMPANY = 'Capture The Bug Ltd.';\r\n\r\nconst DisclaimerPage: React.FC<DisclaimerPageProps> = ({ reportData, disclaimerParagraphs, processDisclaimerContent, sectionId }) => {\r\n  const { updateSectionPage } = useSectionPages();\r\n  return (\r\n    <Page size=\"A4\" id={sectionId} style={{\r\n      flexDirection: 'column',\r\n      backgroundColor: '#ffffff',\r\n      padding: '20mm 15mm',\r\n      fontFamily: 'Helvetica',\r\n      fontSize: 12,\r\n    }}>\r\n      <View style={{\r\n        paddingHorizontal: 24,\r\n        flexDirection: 'column',\r\n        flex: 1,\r\n      }}>\r\n        <View style={{\r\n          flex: 1,\r\n        }}>\r\n          <Text style={{\r\n            fontSize: 16,\r\n            fontWeight: 'bold',\r\n            color: '#2563eb',\r\n            marginBottom: 16,\r\n          }}>\r\n            DISCLAIMER\r\n          </Text>\r\n          {disclaimerParagraphs.length > 0 ? (\r\n            <>\r\n              {disclaimerParagraphs.map((paragraph, index) => (\r\n                <Text key={index} style={{\r\n                  fontSize: 12,\r\n                  lineHeight: 1.4,\r\n                  marginBottom: 18,\r\n                  color: '#374151',\r\n                  textAlign: 'justify',\r\n                  fontFamily: 'Helvetica',\r\n                  fontWeight: 400,\r\n                }}>\r\n                  {paragraph.trim()}\r\n                </Text>\r\n              ))}\r\n            </>\r\n          ) : (\r\n            <>\r\n              {processDisclaimerContent(`<p>${reportData.branding_company || DEFAULT_COMPANY} has prepared this document exclusively for ${reportData.company_name}. Copying, or modification of this document is strictly prohibited without ${reportData.branding_company || DEFAULT_COMPANY}'s written consent, except for specific purposes when such permission is granted. This document is confidential and proprietary material of ${reportData.branding_company || DEFAULT_COMPANY} and must always be treated as such, not to be disclosed to third parties without prior consent.</p>\r\n<p>The information within is provided 'as-is,' without any warranty. Vulnerability assessments offer a snapshot of security at a specific moment and are not exhaustive risk assessments. New vulnerabilities may emerge post-assessment. Thus, this report is a guide, not a definitive risk analysis.</p>\r\n<p>${reportData.branding_company || DEFAULT_COMPANY} assumes no liability for any changes, omissions, or errors in this document. ${reportData.branding_company || DEFAULT_COMPANY} shall not be liable for any damages, financial or otherwise, arising out of the use or misuse of this report by any current employee of ${reportData.company_name} or any member of the general public</p>`).map((paragraph, index) => (\r\n                <Text key={index} style={{\r\n                  fontSize: 12,\r\n                  lineHeight: 1.4,\r\n                  marginBottom: 18,\r\n                  color: '#374151',\r\n                  textAlign: 'justify',\r\n                  fontFamily: 'Helvetica',\r\n                  fontWeight: 400,\r\n                }}>\r\n                  {paragraph.trim()}\r\n                </Text>\r\n              ))}\r\n            </>\r\n          )}\r\n        </View>\r\n      </View>\r\n      <Text style={{ display: 'none' }} render={({ pageNumber }) => { updateSectionPage('Disclaimer', pageNumber); return ''; }} fixed />\r\n    </Page>\r\n  );\r\n};\r\n\r\nexport default DisclaimerPage; "], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,IAAI,EAAEC,IAAI,QAAQ,qBAAqB;AAEtD,SAASC,eAAe,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AASxD,MAAMC,eAAe,GAAG,sBAAsB;AAE9C,MAAMC,cAA6C,GAAGA,CAAC;EAAEC,UAAU;EAAEC,oBAAoB;EAAEC,wBAAwB;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACnI,MAAM;IAAEC;EAAkB,CAAC,GAAGZ,eAAe,CAAC,CAAC;EAC/C,oBACEE,OAAA,CAACL,IAAI;IAACgB,IAAI,EAAC,IAAI;IAACC,EAAE,EAAEJ,SAAU;IAACK,KAAK,EAAE;MACpCC,aAAa,EAAE,QAAQ;MACvBC,eAAe,EAAE,SAAS;MAC1BC,OAAO,EAAE,WAAW;MACpBC,UAAU,EAAE,WAAW;MACvBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBACAnB,OAAA,CAACJ,IAAI;MAACiB,KAAK,EAAE;QACXO,iBAAiB,EAAE,EAAE;QACrBN,aAAa,EAAE,QAAQ;QACvBO,IAAI,EAAE;MACR,CAAE;MAAAF,QAAA,eACAnB,OAAA,CAACJ,IAAI;QAACiB,KAAK,EAAE;UACXQ,IAAI,EAAE;QACR,CAAE;QAAAF,QAAA,gBACAnB,OAAA,CAACH,IAAI;UAACgB,KAAK,EAAE;YACXK,QAAQ,EAAE,EAAE;YACZI,UAAU,EAAE,MAAM;YAClBC,KAAK,EAAE,SAAS;YAChBC,YAAY,EAAE;UAChB,CAAE;UAAAL,QAAA,EAAC;QAEH;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACNtB,oBAAoB,CAACuB,MAAM,GAAG,CAAC,gBAC9B7B,OAAA,CAAAE,SAAA;UAAAiB,QAAA,EACGb,oBAAoB,CAACwB,GAAG,CAAC,CAACC,SAAS,EAAEC,KAAK,kBACzChC,OAAA,CAACH,IAAI;YAAagB,KAAK,EAAE;cACvBK,QAAQ,EAAE,EAAE;cACZe,UAAU,EAAE,GAAG;cACfT,YAAY,EAAE,EAAE;cAChBD,KAAK,EAAE,SAAS;cAChBW,SAAS,EAAE,SAAS;cACpBjB,UAAU,EAAE,WAAW;cACvBK,UAAU,EAAE;YACd,CAAE;YAAAH,QAAA,EACCY,SAAS,CAACI,IAAI,CAAC;UAAC,GATRH,KAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUV,CACP;QAAC,gBACF,CAAC,gBAEH5B,OAAA,CAAAE,SAAA;UAAAiB,QAAA,EACGZ,wBAAwB,CAAE,MAAKF,UAAU,CAAC+B,gBAAgB,IAAIjC,eAAgB,+CAA8CE,UAAU,CAACgC,YAAa,8EAA6EhC,UAAU,CAAC+B,gBAAgB,IAAIjC,eAAgB,+IAA8IE,UAAU,CAAC+B,gBAAgB,IAAIjC,eAAgB;AAC5d;AACA,KAAKE,UAAU,CAAC+B,gBAAgB,IAAIjC,eAAgB,iFAAgFE,UAAU,CAAC+B,gBAAgB,IAAIjC,eAAgB,4IAA2IE,UAAU,CAACgC,YAAa,0CAAyC,CAAC,CAACP,GAAG,CAAC,CAACC,SAAS,EAAEC,KAAK,kBACtYhC,OAAA,CAACH,IAAI;YAAagB,KAAK,EAAE;cACvBK,QAAQ,EAAE,EAAE;cACZe,UAAU,EAAE,GAAG;cACfT,YAAY,EAAE,EAAE;cAChBD,KAAK,EAAE,SAAS;cAChBW,SAAS,EAAE,SAAS;cACpBjB,UAAU,EAAE,WAAW;cACvBK,UAAU,EAAE;YACd,CAAE;YAAAH,QAAA,EACCY,SAAS,CAACI,IAAI,CAAC;UAAC,GATRH,KAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUV,CACP;QAAC,gBACF,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACP5B,OAAA,CAACH,IAAI;MAACgB,KAAK,EAAE;QAAEyB,OAAO,EAAE;MAAO,CAAE;MAACC,MAAM,EAAEA,CAAC;QAAEC;MAAW,CAAC,KAAK;QAAE9B,iBAAiB,CAAC,YAAY,EAAE8B,UAAU,CAAC;QAAE,OAAO,EAAE;MAAE,CAAE;MAACC,KAAK;IAAA;MAAAhB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/H,CAAC;AAEX,CAAC;AAACnB,EAAA,CAlEIL,cAA6C;EAAA,QACnBN,eAAe;AAAA;AAAA4C,EAAA,GADzCtC,cAA6C;AAoEnD,eAAeA,cAAc;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}