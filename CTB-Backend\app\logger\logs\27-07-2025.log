2025-07-27T10:19:22.348Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T10:19:33.518Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T10:19:47.869Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T10:19:54.936Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-27T10:19:55.016Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-27T10:19:55.032Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:19:55.033Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:19:55.034Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:19:55.035Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:19:55.036Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.037Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:19:55.038Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.040Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.041Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:19:55.041Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.042Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:19:55.043Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.064Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.078Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:19:55.079Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:19:55.085Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-27T10:19:55.160Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:19:55.166Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:19:55.166Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:19:55.167Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:19:55.167Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:19:55.167Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.168Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:19:55.168Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.168Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.169Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:19:55.169Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.169Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:19:55.170Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.172Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.180Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:19:55.181Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:19:55.213Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:19:55.219Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:19:55.219Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:19:55.219Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:19:55.220Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:19:55.220Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.220Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:19:55.221Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.221Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.221Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:19:55.222Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.222Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:19:55.222Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.225Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.229Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:19:55.230Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:19:55.257Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:19:55.262Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:19:55.262Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:19:55.262Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:19:55.263Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:19:55.263Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.264Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:19:55.264Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.265Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.265Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:19:55.265Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.266Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:19:55.266Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.268Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.271Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:19:55.272Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:19:55.299Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:19:55.304Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:19:55.304Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:19:55.304Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:19:55.305Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:19:55.305Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.306Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:19:55.306Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.306Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.307Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:19:55.307Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.307Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:19:55.308Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.310Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.312Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:19:55.313Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:19:55.341Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:19:55.360Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:19:55.360Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:19:55.361Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:19:55.361Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:19:55.361Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.362Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:19:55.362Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.362Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.363Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:19:55.363Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.363Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:19:55.364Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.366Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.369Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:19:55.369Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:19:55.403Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:19:55.408Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:19:55.409Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:19:55.410Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:19:55.410Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:19:55.411Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.411Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:19:55.412Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.412Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.412Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:19:55.413Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.413Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:19:55.413Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.419Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:19:55.422Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:19:55.422Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:19:55.449Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:20:01.128Z  [automated-report.controller | getAllPentestReports] info: Admin 2 requesting pentest reports with filters: {"page":"1","limit":"12","sort_by":"createdAt","sort_order":"DESC"} 
2025-07-27T10:20:01.143Z  [automated-report.controller | getAllPentestReports] info: Retrieved 0 reports in 15.15ms for role 2 
2025-07-27T10:20:01.149Z  [automated-report.controller | getAllPentestReports] info: Admin 2 requesting pentest reports with filters: {"page":"1","limit":"12","sort_by":"createdAt","sort_order":"DESC"} 
2025-07-27T10:20:01.152Z  [automated-report.controller | getAllPentestReports] info: Retrieved 0 reports in 3.16ms for role 2 
2025-07-27T10:20:08.821Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-27T10:20:08.830Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 2 
2025-07-27T10:20:12.157Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-27T10:20:12.162Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 2 
2025-07-27T10:20:16.219Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:20:16.220Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:20:16.220Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:20:16.220Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:20:16.220Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.221Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:20:16.221Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.222Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.223Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:20:16.223Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.223Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:20:16.224Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.226Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.231Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:20:16.232Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:20:16.266Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:20:16.273Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:20:16.273Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:20:16.274Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:20:16.274Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:20:16.275Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.275Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:20:16.275Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.276Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.276Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:20:16.276Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.279Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:20:16.279Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.283Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.287Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:20:16.288Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:20:16.326Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:20:16.334Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:20:16.334Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:20:16.335Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:20:16.336Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:20:16.336Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.337Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:20:16.338Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.338Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.339Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:20:16.339Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.340Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:20:16.341Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.344Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.348Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:20:16.349Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:20:16.386Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:20:16.392Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:20:16.392Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:20:16.392Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:20:16.393Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:20:16.393Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.393Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:20:16.394Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.394Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.394Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:20:16.395Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.396Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:20:16.396Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.399Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.403Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:20:16.405Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:20:16.439Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:20:16.444Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:20:16.445Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:20:16.445Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:20:16.446Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:20:16.446Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.447Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:20:16.447Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.447Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.448Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:20:16.448Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.448Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:20:16.449Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.457Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.466Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:20:16.471Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:20:16.510Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:20:16.517Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:20:16.518Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:20:16.518Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:20:16.518Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:20:16.519Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.519Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:20:16.519Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.520Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.520Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:20:16.520Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.520Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:20:16.521Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.523Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.527Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:20:16.528Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:20:16.560Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:20:16.567Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:20:16.568Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:20:16.569Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:20:16.569Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:20:16.570Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.571Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:20:16.571Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.571Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.572Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:20:16.572Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.573Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:20:16.573Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.577Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:20:16.581Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:20:16.582Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:20:16.620Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:21:07.074Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-27T10:21:07.156Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:21:07.174Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:21:07.176Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-27T10:21:07.180Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-27T10:21:07.205Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:21:07.215Z  [program.controller | getPrograms] info: Successfully got 3 programs for user with id 1 
2025-07-27T10:21:07.227Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:21:07.235Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:21:07.243Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:21:07.246Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-27T10:21:07.251Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-27T10:21:07.262Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-27T10:21:10.854Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-27T10:21:10.858Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-27T10:21:13.821Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:21:13.825Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-27T10:21:13.834Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:21:13.835Z  [program.controller | getPrograms] info: Successfully got 3 programs for user with id 1 
2025-07-27T10:21:13.893Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-27T10:21:13.896Z  [program.controller | getPrograms] info: Successfully got 3 programs for user with id 1 
2025-07-27T10:21:27.748Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-27T10:21:27.751Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-27T10:21:41.309Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-27T10:21:41.390Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-27T10:21:41.394Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:21:41.395Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:21:41.395Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:21:41.395Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:21:41.395Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.396Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:21:41.396Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.396Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.396Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:21:41.397Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.397Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:21:41.397Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.401Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.405Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:21:41.406Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:21:41.428Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-27T10:21:41.448Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:21:41.453Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:21:41.454Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:21:41.454Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:21:41.454Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:21:41.454Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.455Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:21:41.455Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.455Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.456Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:21:41.456Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.456Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:21:41.457Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.460Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.463Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:21:41.464Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:21:41.484Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:21:41.488Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:21:41.488Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:21:41.489Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:21:41.489Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:21:41.489Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.489Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:21:41.490Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.490Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.490Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:21:41.490Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.491Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:21:41.491Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.493Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.496Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:21:41.496Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:21:41.519Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:21:41.523Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:21:41.524Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:21:41.524Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:21:41.524Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:21:41.524Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.525Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:21:41.525Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.525Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.525Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:21:41.526Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.526Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:21:41.526Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.528Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.530Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:21:41.531Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:21:41.554Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:21:41.558Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:21:41.558Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:21:41.558Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:21:41.559Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:21:41.559Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.559Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:21:41.559Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.560Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.560Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:21:41.560Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.561Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:21:41.561Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.563Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.565Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:21:41.565Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:21:41.591Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:21:41.607Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:21:41.607Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:21:41.607Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:21:41.608Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:21:41.608Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.608Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:21:41.608Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.609Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.609Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:21:41.609Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.609Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:21:41.610Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.612Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:21:41.612Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:21:41.618Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.635Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:21:41.640Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T10:21:41.641Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T10:21:41.641Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T10:21:41.641Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T10:21:41.641Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.642Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T10:21:41.642Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.642Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.642Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T10:21:41.643Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.643Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T10:21:41.643Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.645Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T10:21:41.648Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T10:21:41.648Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T10:21:41.667Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T10:21:58.925Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-27T10:21:58.994Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-27T10:21:59.006Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-27T10:22:03.497Z  [automated-report.controller | getAllPentestReports] info: Admin 3 requesting pentest reports with filters: {"page":"1","limit":"12","sort_by":"createdAt","sort_order":"DESC"} 
2025-07-27T10:22:03.500Z  [automated-report.controller | getAllPentestReports] info: Retrieved 0 reports in 2.36ms for role 3 
2025-07-27T10:22:03.504Z  [automated-report.controller | getAllPentestReports] info: Admin 3 requesting pentest reports with filters: {"page":"1","limit":"12","sort_by":"createdAt","sort_order":"DESC"} 
2025-07-27T10:22:03.506Z  [automated-report.controller | getAllPentestReports] info: Retrieved 0 reports in 2.38ms for role 3 
2025-07-27T10:22:07.854Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-27T10:22:07.858Z  [program.controller | getPrograms] info: Successfully got 5 programs for user with id 3 
2025-07-27T10:22:11.790Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-27T10:22:11.793Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-27T10:22:11.819Z  [undefined] info: Fetching program details for program ID: 5, User ID: 3 
2025-07-27T10:22:11.832Z  [undefined] info: Fetching program details for program ID: 5, User ID: 3 
2025-07-27T10:22:11.843Z  [undefined] info: Fetching program details for program ID: 5, User ID: 3 
2025-07-27T10:22:11.851Z  [undefined] info: Fetching program details for program ID: 5, User ID: 3 
2025-07-27T10:22:15.684Z  [user.controller | getAllUsers] info: All users requested by user 3  
2025-07-27T10:22:15.684Z  [user.controller | getAllUsers] info: 3 users retrieved by admin 3 
2025-07-27T10:22:15.711Z  [user.controller | getAllUsers] info: All users requested by user 3  
2025-07-27T10:22:15.711Z  [user.controller | getAllUsers] info: 1 users retrieved by admin 3 
2025-07-27T10:22:18.915Z  [program.controller | updateAndCreateProgram] info: Updating program status with id 5 for user with id 3 
2025-07-27T10:22:18.931Z  [notifications.controller | sendNotification] info: notification: {"type":2,"payload":"5","message":"mee activated by admin"}, userIDs: 2 
2025-07-27T10:22:18.984Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-27T10:22:18.987Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-27T10:22:47.752Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-27T10:22:47.848Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:22:47.875Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:22:47.879Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-27T10:22:47.880Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-27T10:22:47.908Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-27T10:22:47.912Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:22:47.923Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:22:47.929Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:22:47.937Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:22:47.940Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-27T10:22:47.945Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-27T10:22:47.955Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-27T10:22:54.197Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-27T10:22:54.199Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-27T10:22:57.133Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:22:57.136Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-27T10:22:57.146Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:22:57.147Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-27T10:22:57.195Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-27T10:22:57.199Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-27T10:23:07.490Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-27T10:23:07.493Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-27T10:24:03.643Z  [POST | Report] info: <EMAIL> saving report... 
2025-07-27T10:24:03.651Z  [POST | Report] info: Parsed form data succesfully... 
2025-07-27T10:24:03.668Z  [POST | Report] info: Creating new <NAME_EMAIL> 
2025-07-27T10:24:03.677Z  [POST | Report] info: Successfully created new <NAME_EMAIL> 
2025-07-27T10:24:03.677Z  [POST | Report | Jira Integration] info: Jira integration disabled for program 5 
2025-07-27T10:24:03.709Z  [POST | Submit Report | Jira Integration] info: Jira integration enabled for report 13 
2025-07-27T10:24:03.714Z  [POST | Submit Report] info: Submitted report 13 
2025-07-27T10:24:03.717Z  [notifications.controller | sendNotification] info: notification: {"type":7,"payload":"13","message":"hi how r u waiting review for program mee"}, userIDs: 3 
2025-07-27T10:24:03.753Z  [GET | Reports] info: Retrieving report 13 for user 1... 
2025-07-27T10:24:03.756Z  [report.controller | getComments] info: User 1 requested comments for report 13 
2025-07-27T10:24:03.756Z  [report.controller | getComments] info: No comments found for report 13 
2025-07-27T10:24:03.774Z  [GET | Report] info: Retrieved report 13 for user 1. 
2025-07-27T10:24:03.775Z  [report.controller | getComments] info: User 1 requested comments for report 13 
2025-07-27T10:24:03.775Z  [report.controller | getComments] info: No comments found for report 13 
2025-07-27T10:24:12.643Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-27T10:24:12.714Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-27T10:24:12.722Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-27T10:24:14.680Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-27T10:24:14.681Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-27T10:24:14.686Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-27T10:24:14.687Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-27T10:24:20.221Z  [POST | Report State] info: Updated report 13 state 
2025-07-27T10:24:20.225Z  [notifications.controller | sendNotification] info: notification: {"type":1,"payload":"13","message":"hi how r u has been approved by the CTB team"}, userIDs: 1,2 
2025-07-27T10:24:20.233Z  [POST | Report State | Jira Integration] info: Admin approved report 13, checking Jira configuration 
2025-07-27T10:24:20.234Z  [Jira Integration | Detailed] info: Starting Jira issue creation for report 13 - Config check 
2025-07-27T10:24:20.234Z  [Jira Integration | Detailed] info: Jira URL: Provided, Email: Provided, API Token: Provided, Project Key: Provided 
2025-07-27T10:24:20.235Z  [Jira Integration] info: Attempting to create Jira issue for report 13 in project MBA 
2025-07-27T10:24:20.235Z  [Jira Integration | Detailed] info: Using Jira API URL: https://prateekdp2877.atlassian.net/rest/api/2/issue 
2025-07-27T10:24:20.235Z  [Jira Integration | Detailed] info: Formatted description for Jira issue: 
*Report ID:* 13
*Severity:* CRITICAL
*Category:* Automotive Security Misconfiguration > Can > Injec... 
2025-07-27T10:24:20.236Z  [Jira Integration | Detailed] info: Fetching issue types for project MBA 
2025-07-27T10:24:21.022Z  [Jira Integration | Detailed] info: Received metaResponse: Success 
2025-07-27T10:24:21.023Z  [Jira Integration | Detailed] info: Project data: Available, Issue types: 4 
2025-07-27T10:24:21.023Z  [Jira Integration | Detailed] info: Using issue type: Epic 
2025-07-27T10:24:21.023Z  [Jira Integration | Detailed] info: Prepared issue data: {"fields":{"project":{"key":"MBA"},"summary":"[CTB Report] hi how r u","description":"\n*Report ID:* 13\n*Severity:* CRITICAL\n*Category:* Automotive Security Misconfiguration > Can > Injection Disallowed Messages\n*Target:* fgg\n*Program:* mee\n*Report Link:* http://localhost:3000/dashboard/reports/13\n\n*Description:*\nhi\n\n---\nThis issue was automatically created from CTB platform.\n","issuetype":{"name":"Epic"}}} 
2025-07-27T10:24:21.024Z  [Jira Integration | Detailed] info: Sending POST request to Jira API at https://prateekdp2877.atlassian.net/rest/api/2/issue 
2025-07-27T10:24:21.895Z  [Jira Integration | Detailed] info: Received response from Jira API: {"id":"10016","key":"MBA-7","self":"https://prateekdp2877.atlassian.net/rest/api/2/issue/10016"} 
2025-07-27T10:24:21.895Z  [Jira Integration] info: Successfully created Jira issue MBA-7 for report 13 
2025-07-27T10:24:21.900Z  [POST | Report State | Jira Integration] info: Successfully created Jira issue MBA-7 for approved report 13 and stored reference 
2025-07-27T10:35:29.278Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T10:35:39.953Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T10:48:39.271Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-27T10:48:39.376Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:48:39.384Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-27T10:48:39.390Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-27T10:48:39.427Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:48:39.438Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-27T10:48:39.448Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:48:39.458Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:48:39.466Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:48:39.469Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-27T10:48:39.477Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:48:39.479Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-27T10:48:39.489Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-27T10:48:40.998Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-27T10:48:41.000Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-27T10:48:41.005Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-27T10:48:41.006Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-27T10:48:43.665Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:48:43.667Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-27T10:48:43.684Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T10:48:43.685Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-27T10:48:43.726Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-27T10:48:43.731Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-27T10:48:52.474Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-27T10:48:52.477Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-27T10:49:20.307Z  [POST | Report] info: <EMAIL> saving report... 
2025-07-27T10:49:20.314Z  [POST | Report] info: Parsed form data succesfully... 
2025-07-27T10:49:20.332Z  [POST | Report] info: Creating new <NAME_EMAIL> 
2025-07-27T10:49:20.344Z  [POST | Report] info: Successfully created new <NAME_EMAIL> 
2025-07-27T10:49:20.344Z  [POST | Report | Jira Integration] info: Jira integration disabled for program 5 
2025-07-27T10:49:20.372Z  [POST | Submit Report | Jira Integration] info: Jira integration enabled for report 14 
2025-07-27T10:49:20.379Z  [POST | Submit Report] info: Submitted report 14 
2025-07-27T10:49:20.382Z  [notifications.controller | sendNotification] info: notification: {"type":7,"payload":"14","message":"hiii waiting review for program mee"}, userIDs: 3 
2025-07-27T10:49:20.405Z  [GET | Reports] info: Retrieving report 14 for user 1... 
2025-07-27T10:49:20.408Z  [report.controller | getComments] info: User 1 requested comments for report 14 
2025-07-27T10:49:20.408Z  [report.controller | getComments] info: No comments found for report 14 
2025-07-27T10:49:20.424Z  [GET | Report] info: Retrieved report 14 for user 1. 
2025-07-27T10:49:20.426Z  [report.controller | getComments] info: User 1 requested comments for report 14 
2025-07-27T10:49:20.426Z  [report.controller | getComments] info: No comments found for report 14 
2025-07-27T10:49:27.332Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-27T10:49:27.401Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-27T10:49:27.412Z  [user.controller | getUserDetails] info: Details retrieved for user 3 
2025-07-27T10:49:28.994Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-27T10:49:29.000Z  [program.controller | getPrograms] info: Successfully got 5 programs for user with id 3 
2025-07-27T10:49:31.958Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-27T10:49:31.959Z  [program.controller | getPrograms] info: Getting programs for user with id 3 | additional_data - [object Object]
2025-07-27T10:49:31.964Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-27T10:49:31.966Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 3 
2025-07-27T10:49:37.052Z  [POST | Report State] info: Updated report 14 state 
2025-07-27T10:49:37.057Z  [notifications.controller | sendNotification] info: notification: {"type":1,"payload":"14","message":"hiii has been approved by the CTB team"}, userIDs: 1,2 
2025-07-27T10:49:37.107Z  [POST | Report State | Jira Integration] info: Admin approved report 14, checking Jira configuration 
2025-07-27T10:49:37.108Z  [Jira Integration | Detailed] info: Starting Jira issue creation for report 14 - Config check 
2025-07-27T10:49:37.109Z  [Jira Integration | Detailed] info: Jira URL: Provided, Email: Provided, API Token: Provided, Project Key: Provided 
2025-07-27T10:49:37.109Z  [Jira Integration] info: Attempting to create Jira issue for report 14 in project CTB 
2025-07-27T10:49:37.109Z  [Jira Integration | Detailed] info: Using Jira API URL: https://prateekdp2877.atlassian.net/rest/api/2/issue 
2025-07-27T10:49:37.110Z  [Jira Integration | Detailed] info: Formatted description for Jira issue: 
*Report ID:* 14
*Severity:* HIGH
*Category:* Ai Application Security > Llm Security > Excessive Age... 
2025-07-27T10:49:37.110Z  [Jira Integration | Detailed] info: Fetching issue types for project CTB 
2025-07-27T10:49:37.841Z  [Jira Integration | Detailed] info: Received metaResponse: Success 
2025-07-27T10:49:37.842Z  [Jira Integration | Detailed] info: Project data: Available, Issue types: 3 
2025-07-27T10:49:37.842Z  [Jira Integration | Detailed] info: Using issue type: Task 
2025-07-27T10:49:37.843Z  [Jira Integration | Detailed] info: Prepared issue data: {"fields":{"project":{"key":"CTB"},"summary":"[CTB Report] hiii","description":"\n*Report ID:* 14\n*Severity:* HIGH\n*Category:* Ai Application Security > Llm Security > Excessive Agency Permission Manipulation\n*Target:* fgg\n*Program:* mee\n*Report Link:* http://localhost:3000/dashboard/reports/14\n\n*Description:*\ng\n\n---\nThis issue was automatically created from CTB platform.\n","issuetype":{"name":"Task"},"assignee":{"name":"prateekdp2877"},"labels":["CTB","Security","Bug-Bounty"]}} 
2025-07-27T10:49:37.843Z  [Jira Integration | Detailed] info: Sending POST request to Jira API at https://prateekdp2877.atlassian.net/rest/api/2/issue 
2025-07-27T10:49:38.758Z  [Jira Integration | Detailed] info: Received response from Jira API: {"id":"10017","key":"CTB-1","self":"https://prateekdp2877.atlassian.net/rest/api/2/issue/10017"} 
2025-07-27T10:49:38.759Z  [Jira Integration] info: Successfully created Jira issue CTB-1 for report 14 
2025-07-27T10:49:38.772Z  [POST | Report State | Jira Integration] info: Successfully created Jira issue CTB-1 for approved report 14 and stored reference 
2025-07-27T10:51:54.250Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T10:57:51.203Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T10:57:57.704Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:01:53.631Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:02:02.818Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:02:40.035Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:02:50.836Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:03:03.967Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:05:03.266Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:07:36.110Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:08:13.700Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:08:36.880Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:08:53.849Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:09:13.097Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:10:27.426Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:11:28.590Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:12:07.266Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:13:00.032Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: MBA-1 
2025-07-27T11:13:00.033Z  [Jira Webhook | Status Change] info: Issue MBA-1: In Progress → Done 
2025-07-27T11:13:00.034Z  [Jira Integration | Process Closure] info: Processing closure request for MBA-1 with status Done 
2025-07-27T11:13:00.076Z  [Jira Integration | Process Error] error: Error processing closure for MBA-1: SequelizeDatabaseError: Column 'description' in where clause is ambiguous 
2025-07-27T11:13:00.086Z  [Jira Integration | Process Closure] info: Processing closure request for MBA-1 with status Done 
2025-07-27T11:13:00.089Z  [Jira Integration | Process Error] error: Error processing closure for MBA-1: SequelizeDatabaseError: Column 'description' in where clause is ambiguous 
2025-07-27T11:13:00.095Z  [Jira Webhook | Authentication] warn: Unauthorized webhook request from ::1 
2025-07-27T11:13:25.133Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:13:33.335Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: MBA-1 
2025-07-27T11:13:33.336Z  [Jira Webhook | Status Change] info: Issue MBA-1: In Progress → Done 
2025-07-27T11:13:33.337Z  [Jira Integration | Process Closure] info: Processing closure request for MBA-1 with status Done 
2025-07-27T11:13:33.358Z  [Jira Integration | Report Not Found] warn: No CTB report found for Jira issue MBA-1 in project MBA 
2025-07-27T11:13:33.391Z  [Jira Integration | Process Closure] info: Processing closure request for MBA-1 with status Done 
2025-07-27T11:13:33.397Z  [Jira Integration | Report Not Found] warn: No CTB report found for Jira issue MBA-1 in project MBA 
2025-07-27T11:13:33.405Z  [Jira Webhook | Authentication] warn: Unauthorized webhook request from ::1 
2025-07-27T11:28:58.430Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:31:58.090Z  [Jira Webhook | Authentication] warn: Unauthorized webhook request from ::1 
2025-07-27T11:32:09.573Z  [Jira Webhook | Authentication] warn: Unauthorized webhook request from ::1 
2025-07-27T11:32:45.076Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:33:01.644Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:33:06.593Z  [Jira Webhook | Debug Headers] info: Headers received: {"host":"be7dc4db52ee.ngrok-free.app","user-agent":"Atlassian Webhook HTTP Client","content-length":"6682","accept":"*/*","accept-encoding":"gzip,deflate","content-type":"application/json; charset=UTF-8","x-atlassian-webhook-flow":"Primary","x-atlassian-webhook-identifier":"3290237139314195949","x-b3-sampled":"0","x-b3-spanid":"4de0684538e11c01","x-b3-traceid":"f4f6c97fa60fe2587b514e96e15716eb","x-forwarded-for":"2401:1d80:3214:3:bc71:b73:7f:28db","x-forwarded-host":"be7dc4db52ee.ngrok-free.app","x-forwarded-proto":"https"} 
2025-07-27T11:33:06.594Z  [Jira Webhook | Debug Auth] info: Received: "undefined", Expected: "Bearer ctb_jira_webhook_secret_2024" 
2025-07-27T11:33:06.595Z  [Jira Webhook | Authentication] warn: Unauthorized webhook request from ::1. Auth header: "undefined" 
2025-07-27T11:33:24.284Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:33:37.139Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:34:05.818Z  [Jira Webhook | Debug Headers] info: Headers received: {"host":"be7dc4db52ee.ngrok-free.app","user-agent":"Atlassian Webhook HTTP Client","content-length":"6867","accept":"*/*","accept-encoding":"gzip,deflate","content-type":"application/json; charset=UTF-8","x-atlassian-webhook-flow":"Primary","x-atlassian-webhook-identifier":"1468257816615538200","x-b3-sampled":"0","x-b3-spanid":"e9329ac7156f756c","x-b3-traceid":"068bd27935fb10f634de2c2ecb96f39e","x-forwarded-for":"2401:1d80:3214:3:bc71:b73:7f:28db","x-forwarded-host":"be7dc4db52ee.ngrok-free.app","x-forwarded-proto":"https"} 
2025-07-27T11:34:05.819Z  [Jira Webhook | Debug Auth] info: Received: "undefined", Expected: "Bearer ctb_jira_webhook_secret_2024" 
2025-07-27T11:34:05.819Z  [Jira Webhook | Security] warn: Authentication temporarily disabled for debugging 
2025-07-27T11:34:05.820Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T11:34:05.820Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T11:34:05.821Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T11:34:05.839Z  [Jira Integration | Validation Failed] info: Closure not allowed for CTB-1: Report cannot be closed. Current status: Business Review. Required status: awaiting_fix 
2025-07-27T11:34:05.840Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T11:34:06.473Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T11:34:06.473Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T11:34:07.139Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T11:34:07.493Z  [Jira Webhook | Debug Headers] info: Headers received: {"host":"be7dc4db52ee.ngrok-free.app","user-agent":"Atlassian Webhook HTTP Client","content-length":"6682","accept":"*/*","accept-encoding":"gzip,deflate","content-type":"application/json; charset=UTF-8","x-atlassian-webhook-flow":"Primary","x-atlassian-webhook-identifier":"6337267319719620101","x-b3-sampled":"0","x-b3-spanid":"fff7deec9a04a950","x-b3-traceid":"17acae1bc9567a4aadbae6d511630bbf","x-forwarded-for":"***************","x-forwarded-host":"be7dc4db52ee.ngrok-free.app","x-forwarded-proto":"https"} 
2025-07-27T11:34:07.494Z  [Jira Webhook | Debug Auth] info: Received: "undefined", Expected: "Bearer ctb_jira_webhook_secret_2024" 
2025-07-27T11:34:07.494Z  [Jira Webhook | Security] warn: Authentication temporarily disabled for debugging 
2025-07-27T11:34:07.495Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T11:34:07.495Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T11:34:55.507Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:35:26.712Z  [Jira Webhook | Debug Headers] info: Headers received: {"host":"be7dc4db52ee.ngrok-free.app","user-agent":"Atlassian Webhook HTTP Client","content-length":"6867","accept":"*/*","accept-encoding":"gzip,deflate","content-type":"application/json; charset=UTF-8","x-atlassian-webhook-flow":"Primary","x-atlassian-webhook-identifier":"9128732537338092322","x-b3-sampled":"0","x-b3-spanid":"6f69417154bcfc56","x-b3-traceid":"3a7492d51c72ab9e998b30d745fa686f","x-forwarded-for":"2401:1d80:3214:3:ea0b:27d1:886:8d2","x-forwarded-host":"be7dc4db52ee.ngrok-free.app","x-forwarded-proto":"https"} 
2025-07-27T11:35:26.713Z  [Jira Webhook | Debug Auth] info: Received: "undefined", Expected: "Bearer ctb_jira_webhook_secret_2024" 
2025-07-27T11:35:26.713Z  [Jira Webhook | Security] info: Authenticated request from ::1 with User-Agent: Atlassian Webhook HTTP Client 
2025-07-27T11:35:26.714Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T11:35:26.714Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T11:35:26.714Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T11:35:26.730Z  [Jira Integration | Validation Failed] info: Closure not allowed for CTB-1: Report cannot be closed. Current status: Business Review. Required status: awaiting_fix 
2025-07-27T11:35:26.732Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T11:35:27.137Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T11:35:27.137Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T11:35:27.701Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T11:35:28.050Z  [Jira Webhook | Debug Headers] info: Headers received: {"host":"be7dc4db52ee.ngrok-free.app","user-agent":"Atlassian Webhook HTTP Client","content-length":"6682","accept":"*/*","accept-encoding":"gzip,deflate","content-type":"application/json; charset=UTF-8","x-atlassian-webhook-flow":"Primary","x-atlassian-webhook-identifier":"8781926549393249390","x-b3-sampled":"0","x-b3-spanid":"f6628dc2901115b0","x-b3-traceid":"c342c97fc2ad38cc4100e77c82a0817f","x-forwarded-for":"2401:1d80:3214:3:ea0b:27d1:886:8d2","x-forwarded-host":"be7dc4db52ee.ngrok-free.app","x-forwarded-proto":"https"} 
2025-07-27T11:35:28.051Z  [Jira Webhook | Debug Auth] info: Received: "undefined", Expected: "Bearer ctb_jira_webhook_secret_2024" 
2025-07-27T11:35:28.051Z  [Jira Webhook | Security] info: Authenticated request from ::1 with User-Agent: Atlassian Webhook HTTP Client 
2025-07-27T11:35:28.051Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T11:35:28.051Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T11:37:08.175Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:37:35.444Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:39:25.318Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:39:46.481Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:40:37.616Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:40:54.138Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T11:40:54.139Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T11:40:54.140Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T11:40:54.158Z  [Jira Integration | Validation Failed] info: Closure not allowed for CTB-1: Report cannot be closed. Current status: Business Review. Required status: awaiting_fix 
2025-07-27T11:40:54.159Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T11:40:54.575Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T11:40:54.578Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T11:40:55.159Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T11:40:55.530Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T11:40:55.531Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T11:41:30.289Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:41:47.074Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-27T11:41:47.189Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-27T11:41:47.197Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T11:41:47.198Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T11:41:47.198Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T11:41:47.199Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T11:41:47.199Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.200Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T11:41:47.201Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.201Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.202Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T11:41:47.202Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.203Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T11:41:47.204Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.212Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.219Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T11:41:47.220Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T11:41:47.247Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-27T11:41:47.279Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T11:41:47.284Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T11:41:47.284Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T11:41:47.284Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T11:41:47.285Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T11:41:47.285Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.286Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T11:41:47.286Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.287Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.287Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T11:41:47.287Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.287Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T11:41:47.288Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.290Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.295Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T11:41:47.295Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T11:41:47.316Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T11:41:47.321Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T11:41:47.322Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T11:41:47.322Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T11:41:47.322Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T11:41:47.322Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.323Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T11:41:47.323Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.323Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.323Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T11:41:47.324Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.324Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T11:41:47.324Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.326Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.334Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T11:41:47.335Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T11:41:47.367Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T11:41:47.375Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T11:41:47.375Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T11:41:47.376Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T11:41:47.376Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T11:41:47.376Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.377Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T11:41:47.377Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.377Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.378Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T11:41:47.378Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.378Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T11:41:47.378Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.381Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.384Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T11:41:47.385Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T11:41:47.402Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T11:41:47.408Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T11:41:47.408Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T11:41:47.408Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T11:41:47.409Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T11:41:47.409Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.409Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T11:41:47.410Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.410Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.410Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T11:41:47.410Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.411Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T11:41:47.411Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.413Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.417Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T11:41:47.418Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T11:41:47.438Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T11:41:47.475Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T11:41:47.476Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T11:41:47.476Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T11:41:47.476Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T11:41:47.477Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.477Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T11:41:47.477Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.478Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.478Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T11:41:47.478Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.479Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T11:41:47.479Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.480Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.483Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T11:41:47.483Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T11:41:47.501Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T11:41:47.506Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T11:41:47.507Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T11:41:47.507Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T11:41:47.507Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T11:41:47.507Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.508Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T11:41:47.508Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.508Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.509Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T11:41:47.509Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.509Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T11:41:47.509Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.511Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:41:47.514Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T11:41:47.514Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T11:41:47.533Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T11:41:50.739Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-27T11:41:50.751Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 2 
2025-07-27T11:41:53.127Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-27T11:41:53.129Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-27T11:41:53.134Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 2 
2025-07-27T11:41:53.135Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 2 
2025-07-27T11:42:01.114Z  [GET | Reports] info: Retrieving report 14 for user 2... 
2025-07-27T11:42:01.116Z  [report.controller | getComments] info: User 2 requested comments for report 14 
2025-07-27T11:42:01.116Z  [report.controller | getComments] info: No comments found for report 14 
2025-07-27T11:42:01.130Z  [GET | Report] info: Retrieved report 14 for user 2. 
2025-07-27T11:42:01.132Z  [report.controller | getComments] info: User 2 requested comments for report 14 
2025-07-27T11:42:01.132Z  [report.controller | getComments] info: No comments found for report 14 
2025-07-27T11:43:56.602Z  [POST | Report State] info: Updated report 14 state 
2025-07-27T11:43:56.609Z  [notifications.controller | sendNotification] info: notification: {"type":1,"payload":"14","message":"hiii has been approved by ctbBusiness"}, userIDs: 1,3 
2025-07-27T11:44:06.501Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-27T11:44:06.570Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-27T11:44:06.572Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T11:44:06.573Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T11:44:06.573Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T11:44:06.573Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T11:44:06.573Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.574Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T11:44:06.574Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.574Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.575Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T11:44:06.576Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.576Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T11:44:06.576Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.580Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.589Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T11:44:06.590Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T11:44:06.599Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-27T11:44:06.612Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T11:44:06.617Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T11:44:06.618Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T11:44:06.618Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T11:44:06.618Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T11:44:06.618Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.619Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T11:44:06.619Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.619Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.620Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T11:44:06.620Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.620Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T11:44:06.621Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.623Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.627Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T11:44:06.627Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T11:44:06.647Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T11:44:06.651Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T11:44:06.652Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T11:44:06.652Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T11:44:06.652Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T11:44:06.652Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.653Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T11:44:06.653Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.653Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.654Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T11:44:06.654Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.654Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T11:44:06.654Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.656Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.660Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T11:44:06.660Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T11:44:06.676Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T11:44:06.681Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T11:44:06.682Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T11:44:06.682Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T11:44:06.682Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T11:44:06.682Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.683Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T11:44:06.683Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.683Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.683Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T11:44:06.684Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.684Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T11:44:06.684Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.686Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.689Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T11:44:06.690Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T11:44:06.704Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T11:44:06.709Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T11:44:06.709Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T11:44:06.710Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T11:44:06.710Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T11:44:06.711Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.711Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T11:44:06.711Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.712Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.712Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T11:44:06.713Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.713Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T11:44:06.713Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.716Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.719Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T11:44:06.720Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T11:44:06.742Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T11:44:06.763Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T11:44:06.763Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T11:44:06.764Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T11:44:06.764Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T11:44:06.764Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.764Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T11:44:06.765Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.765Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.765Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T11:44:06.765Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.766Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T11:44:06.766Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.767Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.771Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T11:44:06.771Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T11:44:06.786Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T11:44:06.789Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T11:44:06.790Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T11:44:06.790Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T11:44:06.791Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T11:44:06.791Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.791Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T11:44:06.791Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.792Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.792Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T11:44:06.792Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.793Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T11:44:06.793Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.794Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T11:44:06.797Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T11:44:06.797Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T11:44:06.812Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T11:44:08.335Z  [automated-report.controller | getAllPentestReports] info: Admin 2 requesting pentest reports with filters: {"page":"1","limit":"12","sort_by":"createdAt","sort_order":"DESC"} 
2025-07-27T11:44:08.339Z  [automated-report.controller | getAllPentestReports] info: Retrieved 0 reports in 4.31ms for role 2 
2025-07-27T11:44:08.343Z  [automated-report.controller | getAllPentestReports] info: Admin 2 requesting pentest reports with filters: {"page":"1","limit":"12","sort_by":"createdAt","sort_order":"DESC"} 
2025-07-27T11:44:08.346Z  [automated-report.controller | getAllPentestReports] info: Retrieved 0 reports in 2.67ms for role 2 
2025-07-27T11:44:10.061Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-27T11:44:10.062Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-27T11:44:10.066Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 2 
2025-07-27T11:44:10.067Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 2 
2025-07-27T11:44:14.405Z  [POST | Report State] info: Updated report 14 state 
2025-07-27T11:44:14.412Z  [notifications.controller | sendNotification] info: notification: {"type":1,"payload":"14","message":"hiii has been requestFix by ctbBusiness"}, userIDs: 1,3 
2025-07-27T11:44:18.751Z  [POST | Create Retest] info: Retest created for report 14 by user 2. 
2025-07-27T11:44:18.754Z  [PUT | Update Retest Status] info: No Slack channel link available for program ID 5. Notifications will proceed without Slack. 
2025-07-27T11:44:18.755Z  [notifications.controller | sendNotification] info: notification: {"type":7,"payload":"2","message":"A retest has been initiated for the report: hiii."}, userIDs: 1,3 
2025-07-27T11:44:24.147Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-27T11:44:24.239Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T11:44:24.241Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-27T11:44:24.243Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-27T11:44:24.264Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T11:44:24.274Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-27T11:44:24.283Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T11:44:24.292Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T11:44:24.299Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T11:44:24.301Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-27T11:44:24.307Z  [user.controller | getUserDetails] info: Details retrieved for user 1 
2025-07-27T11:44:24.309Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-27T11:44:24.318Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-27T11:44:26.012Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-27T11:44:26.014Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-27T11:44:26.017Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-27T11:44:26.018Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 1 
2025-07-27T11:44:41.469Z  [PUT | Update Retest Status] info: Retest 2 status updated to Retest In Process by user 1. 
2025-07-27T11:44:41.479Z  [PUT | Update Retest Status] info: No Slack channel link available for program ID 5. Notifications will proceed without Slack. 
2025-07-27T11:44:41.483Z  [notifications.controller | sendNotification] info: notification: {"type":7,"payload":"2","message":"Retest status for report \"hiii\" has been updated to Retest In Process. Comments: The retest process for this vulnerability has begun."}, userIDs: 2,3 
2025-07-27T11:45:06.522Z  [PUT | Update Retest Status] info: Retest 2 status updated to Fix Verified by user 1. 
2025-07-27T11:45:06.526Z  [PUT | Update Retest Status] info: No Slack channel link available for program ID 5. Notifications will proceed without Slack. 
2025-07-27T11:45:06.528Z  [notifications.controller | sendNotification] info: notification: {"type":7,"payload":"2","message":"Retest status for report \"hiii\" has been updated to Fix Verified. Comments: The fix for this vulnerability has been verified."}, userIDs: 2,3 
2025-07-27T11:46:02.121Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T11:46:02.121Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T11:46:02.121Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T11:46:02.129Z  [Jira Integration | Validation Failed] info: Closure not allowed for CTB-1: Report cannot be closed. Current status: Awaiting Fix. Required status: awaiting_fix 
2025-07-27T11:46:02.130Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T11:46:02.597Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T11:46:02.597Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T11:46:03.212Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T11:46:03.578Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T11:46:03.579Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T11:49:49.862Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:50:20.335Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:50:34.430Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:51:10.209Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T11:51:10.212Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T11:51:10.213Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T11:51:10.304Z  [Jira Integration | Process Error] error: Error processing closure for CTB-1: SequelizeDatabaseError: Unknown column 'retest.createdAt' in 'order clause' 
2025-07-27T11:54:03.008Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:54:17.306Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T11:54:38.224Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T12:00:34.262Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:00:34.263Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:08:05.984Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T12:09:36.182Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:09:36.183Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:09:36.184Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:09:36.229Z  [Jira Integration | Process Error] error: Error processing closure for CTB-1: SequelizeDatabaseError: Unknown column 'retest.createdAt' in 'order clause' 
2025-07-27T12:09:48.704Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:09:48.704Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:09:48.705Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:11:41.128Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T12:12:15.002Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:15.003Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:12:15.003Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:12:15.023Z  [Jira Integration | Validation Result] info: Validation for CTB-1: ALLOWED - All conditions met: Report status is 'Awaiting Fix', retest status is 'Fix Verified', Jira status is 'Done' 
2025-07-27T12:12:15.031Z  [Jira Integration | Report Closed] info: ✅ Successfully closed CTB report 14 for Jira issue CTB-1. Jira issue will remain 'Done'. 
2025-07-27T12:12:25.435Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:25.435Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:12:25.436Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:12:25.442Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:12:25.443Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:25.962Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:25.962Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:12:26.677Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:26.982Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:26.983Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:12:26.983Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:12:26.988Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:26.988Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:26.989Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:27.309Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:27.310Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:12:27.961Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:28.098Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:28.099Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:12:28.099Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:12:28.103Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:12:28.103Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:28.478Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:28.478Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:12:29.008Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:29.153Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:29.154Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:12:29.154Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:12:29.158Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:29.158Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:29.158Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:29.482Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:29.483Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:12:30.011Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:30.179Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:30.179Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:12:30.179Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:12:30.183Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:12:30.183Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:30.585Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:30.586Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:12:31.214Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:31.368Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:31.369Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:12:31.369Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:12:31.373Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:31.373Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:31.374Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:31.654Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:31.654Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:12:32.252Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:32.586Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:32.586Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:12:32.586Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:12:32.590Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:12:32.591Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:32.859Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:32.859Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:12:33.531Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:33.692Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:33.693Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:12:33.693Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:12:33.698Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:33.698Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:33.699Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:33.967Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:33.967Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:12:34.645Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:34.743Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:34.744Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:12:34.744Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:12:34.748Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:12:34.748Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:35.034Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:35.035Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:12:35.507Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:35.607Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:35.608Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:12:35.608Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:12:35.611Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:35.612Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:35.612Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:35.999Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:36.000Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:12:36.446Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:36.570Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:36.570Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:12:36.570Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:12:36.573Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:12:36.574Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:36.837Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:36.837Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:12:37.456Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:37.579Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:37.579Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:12:37.580Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:12:37.583Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:37.584Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:37.584Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:37.870Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:37.870Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:12:38.382Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:38.526Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:38.527Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:12:38.527Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:12:38.530Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:12:38.531Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:38.787Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:38.787Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:12:39.331Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:39.728Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:39.729Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:12:39.729Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:12:39.732Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:39.732Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:39.732Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:39.976Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:39.977Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:12:40.506Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:40.671Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:40.671Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:12:40.671Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:12:40.674Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:12:40.674Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:41.085Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:41.086Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:12:41.585Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:41.719Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:41.719Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:12:41.719Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:12:41.722Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:41.722Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:41.723Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:42.091Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:42.092Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:12:42.623Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:42.756Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:42.757Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:12:42.757Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:12:42.759Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:12:42.760Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:43.023Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:43.023Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:12:43.625Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:43.766Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:43.766Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:12:43.766Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:12:43.769Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:43.769Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:43.769Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:44.121Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:44.121Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:12:44.596Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:44.948Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:44.949Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:12:44.949Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:12:44.952Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:12:44.952Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:45.223Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:45.223Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:12:45.729Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:45.861Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:45.861Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:12:45.861Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:12:45.864Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:45.864Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:45.864Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:46.189Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:46.189Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:12:46.795Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:47.089Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:47.089Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:12:47.089Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:12:47.092Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:12:47.092Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:47.356Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:47.356Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:12:47.850Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:47.982Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:47.982Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:12:47.982Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:12:47.986Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:47.986Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:47.986Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:48.262Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:48.262Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:12:48.791Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:48.947Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:48.947Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:12:48.947Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:12:48.950Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:12:48.950Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:49.236Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:49.236Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:12:49.772Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:49.881Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:49.881Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:12:49.881Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:12:49.884Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:49.884Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:49.884Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:50.246Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:50.246Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:12:50.838Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:51.010Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:51.011Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:12:51.011Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:12:51.015Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:12:51.015Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:51.287Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:51.288Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:12:51.783Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:51.937Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:51.938Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:12:51.938Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:12:51.941Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:51.942Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:51.942Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:52.218Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:52.218Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:12:52.740Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:52.894Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:52.895Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:12:52.895Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:12:52.900Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:12:52.920Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:53.214Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:53.214Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:12:53.756Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:53.921Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:53.921Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:12:53.921Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:12:53.924Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:53.924Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:53.924Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:54.216Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:54.216Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:12:54.814Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:54.941Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:54.941Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:12:54.942Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:12:54.944Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:12:54.944Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:55.277Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:55.277Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:12:55.831Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:56.148Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:56.148Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:12:56.148Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:12:56.150Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:56.151Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:56.151Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:56.448Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:56.449Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:12:56.989Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:57.348Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:57.348Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:12:57.349Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:12:57.351Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:12:57.351Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:57.641Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:57.641Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:12:58.098Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:58.279Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:58.280Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:12:58.280Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:12:58.283Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:58.283Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:12:58.283Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:58.580Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:58.580Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:12:59.123Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:12:59.265Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:12:59.265Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:12:59.266Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:12:59.267Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:12:59.268Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:12:59.530Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:12:59.531Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:13:00.188Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:13:00.351Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:13:00.352Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:13:00.352Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:13:00.355Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:13:00.356Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:13:00.356Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:13:00.653Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:13:00.653Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:13:01.230Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:13:01.378Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:13:01.378Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:13:01.379Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:13:01.381Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:13:01.381Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:13:01.640Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:13:01.641Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:13:02.160Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:13:02.484Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:13:02.485Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:13:02.485Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:13:02.487Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:13:02.487Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:13:02.487Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:13:02.777Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:13:02.777Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:13:03.274Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:13:03.405Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:13:03.406Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:13:03.406Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:13:03.408Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:13:03.408Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:13:03.705Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:13:03.705Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:13:04.198Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:13:04.336Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:13:04.337Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:13:04.337Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:13:04.340Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:13:04.340Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:13:04.340Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:13:04.601Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:13:04.601Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:13:05.382Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:13:05.450Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:13:05.450Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:13:05.450Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:13:05.452Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:13:05.453Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:13:05.753Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:13:05.753Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:13:06.187Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:13:06.526Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:13:06.526Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:13:06.526Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:13:06.528Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:13:06.528Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:13:06.529Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:13:06.798Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:13:06.798Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:13:07.332Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:13:07.475Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:13:07.476Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:13:07.476Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:13:07.479Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:13:07.479Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:13:07.756Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:13:07.756Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:13:08.390Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:13:08.390Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:13:08.390Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:13:08.393Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:13:08.393Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:13:08.393Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:13:08.547Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:13:08.699Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:13:08.700Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:13:09.273Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:13:09.421Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:13:09.421Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:13:09.421Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:13:09.425Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:13:09.425Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:13:09.704Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:13:09.705Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:13:10.206Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:13:10.507Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:13:10.508Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:13:10.508Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:13:10.512Z  [Jira Integration | Validation Result] info: Validation for CTB-1: DENIED - Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:13:10.513Z  [Jira Integration | Reverting Issue] warn: Reverting CTB-1 to 'To Do' due to: Report cannot be closed. Current status: closed. Required status: Awaiting Fix 
2025-07-27T12:13:10.513Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:13:10.867Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:13:10.867Z  [Jira Integration | Revert Status] info: Found transition to 'To Do' (ID: 11) for issue CTB-1 
2025-07-27T12:13:11.412Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'To Do' status 
2025-07-27T12:14:02.316Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T12:14:23.087Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:14:23.088Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:14:23.089Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:14:23.106Z  [Jira Integration | Already Closed] info: Report for CTB-1 is already closed. Ignoring closure request to prevent infinite loop. 
2025-07-27T12:14:37.290Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:14:37.290Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:14:37.290Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:14:37.296Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:14:37.298Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:14:37.669Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:14:37.669Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:14:38.262Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:14:38.594Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:14:38.594Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:14:38.595Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:14:38.599Z  [Jira Integration | Already Closed] info: Report for CTB-1 is already closed. Ignoring closure request to prevent infinite loop. 
2025-07-27T12:15:48.521Z  [program.controller | getPrograms] info: Getting programs for user with id 1 | additional_data - [object Object]
2025-07-27T12:15:48.524Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-27T12:15:48.557Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 1 
2025-07-27T12:15:48.580Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-27T12:15:48.585Z  [GET | Report Summary] info: Generating report summary for user 1... 
2025-07-27T12:15:48.597Z  [GET | Report Summary] info: Generated report summary with 3 for user 1... 
2025-07-27T12:15:55.899Z  [User | Login] info: User logged in without OTP | additional_data - email - <EMAIL>
2025-07-27T12:15:55.990Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-27T12:15:56.005Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:15:56.006Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:15:56.006Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:15:56.007Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:15:56.008Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.009Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:15:56.010Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.013Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.014Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:15:56.015Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.016Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:15:56.016Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.021Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.041Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:15:56.042Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:15:56.059Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-27T12:15:56.085Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:15:56.090Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:15:56.091Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:15:56.091Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:15:56.092Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:15:56.092Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.092Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:15:56.092Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.093Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.093Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:15:56.093Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.093Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:15:56.094Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.098Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:15:56.098Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:15:56.102Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.125Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:15:56.130Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:15:56.131Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:15:56.132Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:15:56.132Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:15:56.132Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.133Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:15:56.133Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.133Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.134Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:15:56.134Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.134Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:15:56.134Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.136Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.141Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:15:56.142Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:15:56.165Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:15:56.170Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:15:56.171Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:15:56.171Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:15:56.171Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:15:56.171Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.172Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:15:56.173Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.173Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.173Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:15:56.174Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.174Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:15:56.174Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.177Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.180Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:15:56.180Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:15:56.199Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:15:56.204Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:15:56.205Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:15:56.205Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:15:56.205Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:15:56.205Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.206Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:15:56.206Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.206Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.207Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:15:56.207Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.207Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:15:56.207Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.210Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.212Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:15:56.213Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:15:56.232Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:15:56.259Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:15:56.259Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:15:56.259Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:15:56.259Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:15:56.260Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.260Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:15:56.260Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.260Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.261Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:15:56.261Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.261Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:15:56.262Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.263Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.266Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:15:56.267Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:15:56.288Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:15:56.293Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:15:56.293Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:15:56.294Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:15:56.294Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:15:56.294Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.294Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:15:56.295Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.295Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.295Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:15:56.295Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.296Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:15:56.296Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.299Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:15:56.302Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:15:56.303Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:15:56.322Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:15:57.714Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-27T12:15:57.723Z  [program.controller | getPrograms] info: Successfully got 4 programs for user with id 2 
2025-07-27T12:15:58.820Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-27T12:15:58.821Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-27T12:15:58.826Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 2 
2025-07-27T12:15:58.828Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 2 
2025-07-27T12:18:39.788Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T12:18:44.642Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T12:21:24.654Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T12:21:35.877Z  [dashboardMetrics] info: Optimised module loaded 
2025-07-27T12:22:34.504Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-27T12:22:34.535Z  [user.controller | getUserDetails] info: Details retrieved for user 2 
2025-07-27T12:22:34.615Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-27T12:22:34.618Z  [program.controller | getPrograms] info: Getting programs for user with id 2 | additional_data - [object Object]
2025-07-27T12:22:34.627Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 2 
2025-07-27T12:22:34.629Z  [program.controller | getPrograms] info: Successfully got 1 programs for user with id 2 
2025-07-27T12:22:36.237Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:22:36.238Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:22:36.239Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:22:36.239Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:22:36.240Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.240Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:22:36.241Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.242Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.242Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:22:36.243Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.243Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:22:36.244Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.247Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.252Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:22:36.253Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:22:36.288Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:22:36.293Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:22:36.294Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:22:36.294Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:22:36.294Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:22:36.294Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.295Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:22:36.295Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.295Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.296Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:22:36.296Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.296Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:22:36.296Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.298Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.301Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:22:36.302Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:22:36.324Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:22:36.328Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:22:36.329Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:22:36.329Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:22:36.329Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:22:36.330Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.330Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:22:36.330Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.331Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.331Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:22:36.331Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.331Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:22:36.332Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.334Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.338Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:22:36.338Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:22:36.364Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:22:36.369Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:22:36.369Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:22:36.369Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:22:36.370Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:22:36.370Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.370Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:22:36.371Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.371Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.371Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:22:36.372Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.372Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:22:36.372Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.375Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.379Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:22:36.379Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:22:36.400Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:22:36.404Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:22:36.404Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:22:36.404Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:22:36.405Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:22:36.405Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.405Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:22:36.406Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.406Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.406Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:22:36.407Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.407Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:22:36.407Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.409Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.412Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:22:36.412Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:22:36.434Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:22:36.462Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:22:36.463Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:22:36.463Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:22:36.463Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:22:36.463Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.464Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:22:36.464Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.464Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.465Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:22:36.465Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.465Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:22:36.465Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.467Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.471Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:22:36.471Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:22:36.494Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:22:36.498Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:22:36.499Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:22:36.499Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:22:36.499Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:22:36.500Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.500Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:22:36.500Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.501Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.501Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:22:36.501Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.501Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:22:36.502Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.504Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:36.507Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:22:36.508Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:22:36.528Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:22:57.034Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:22:57.035Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:22:57.036Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:22:57.036Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:22:57.036Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.036Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:22:57.037Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.037Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.037Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:22:57.037Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.038Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:22:57.038Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.041Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.044Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:22:57.044Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:22:57.070Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:22:57.075Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:22:57.076Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:22:57.076Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:22:57.076Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:22:57.077Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.077Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:22:57.077Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.077Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.078Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:22:57.078Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.078Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:22:57.078Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.080Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.083Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:22:57.083Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:22:57.101Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:22:57.106Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:22:57.107Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:22:57.107Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:22:57.107Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:22:57.107Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.108Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:22:57.108Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.108Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.109Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:22:57.109Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.109Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:22:57.109Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.112Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.115Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:22:57.116Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:22:57.135Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:22:57.140Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:22:57.141Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:22:57.141Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:22:57.141Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:22:57.142Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.142Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:22:57.143Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.143Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.143Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:22:57.143Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.144Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:22:57.144Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.146Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.150Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:22:57.150Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:22:57.169Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:22:57.174Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:22:57.175Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:22:57.175Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:22:57.175Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:22:57.176Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.176Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:22:57.176Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.176Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.177Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:22:57.177Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.177Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:22:57.177Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.180Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.183Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:22:57.183Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:22:57.204Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:22:57.209Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:22:57.210Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:22:57.210Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:22:57.210Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:22:57.210Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.210Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:22:57.211Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.211Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.211Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:22:57.211Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.212Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:22:57.212Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.214Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.217Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:22:57.217Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:22:57.236Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:22:57.240Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:22:57.240Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:22:57.240Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:22:57.241Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:22:57.241Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.241Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:22:57.242Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.242Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.242Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:22:57.242Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.243Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:22:57.243Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.244Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:22:57.249Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:22:57.250Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:22:57.281Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:23:36.170Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:23:36.171Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:23:36.171Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:23:36.171Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:23:36.172Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.172Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:23:36.172Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.172Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.173Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:23:36.173Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.173Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:23:36.174Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.177Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.179Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:23:36.180Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:23:36.199Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:23:36.205Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:23:36.206Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:23:36.206Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:23:36.206Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:23:36.206Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.207Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:23:36.207Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.207Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.207Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:23:36.208Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.208Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:23:36.208Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.210Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.213Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:23:36.214Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:23:36.228Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:23:36.233Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:23:36.233Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:23:36.233Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:23:36.233Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:23:36.234Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.234Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:23:36.234Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.234Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.235Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:23:36.235Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.235Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:23:36.236Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.238Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.241Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:23:36.241Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:23:36.259Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:23:36.263Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:23:36.264Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:23:36.264Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:23:36.264Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:23:36.265Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.265Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:23:36.265Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.265Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.266Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:23:36.266Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.266Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:23:36.266Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.268Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.269Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:23:36.270Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:23:36.288Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:23:36.293Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:23:36.293Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:23:36.293Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:23:36.294Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:23:36.294Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.294Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:23:36.294Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.295Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.295Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:23:36.295Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.295Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:23:36.296Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.298Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.302Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:23:36.303Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:23:36.361Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:23:36.379Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:23:36.379Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:23:36.379Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:23:36.380Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:23:36.380Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.380Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:23:36.380Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.381Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.381Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:23:36.381Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.381Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:23:36.382Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.384Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.386Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:23:36.386Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:23:36.407Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:23:36.412Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:23:36.413Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:23:36.413Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:23:36.413Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:23:36.413Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.414Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:23:36.414Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.414Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.414Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:23:36.415Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.415Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:23:36.415Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.418Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:23:36.420Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:23:36.420Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:23:36.434Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:24:07.599Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:24:07.600Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:24:07.600Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:24:07.600Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:24:07.600Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.601Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:24:07.601Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.601Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.601Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:24:07.601Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.602Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:24:07.602Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.605Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.608Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:24:07.609Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:24:07.630Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:24:07.634Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:24:07.634Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:24:07.634Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:24:07.634Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:24:07.635Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.635Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:24:07.635Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.635Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.636Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:24:07.636Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.636Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:24:07.636Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.639Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.640Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:24:07.641Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:24:07.656Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:24:07.660Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:24:07.660Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:24:07.661Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:24:07.661Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:24:07.661Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.662Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:24:07.662Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.662Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.662Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:24:07.662Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.663Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:24:07.663Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.665Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.667Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:24:07.667Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:24:07.681Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:24:07.689Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:24:07.690Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:24:07.690Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:24:07.691Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:24:07.691Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.691Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:24:07.692Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.692Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.692Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:24:07.693Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.693Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:24:07.693Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.695Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.702Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:24:07.703Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:24:07.723Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:24:07.730Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:24:07.730Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:24:07.730Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:24:07.731Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:24:07.731Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.731Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:24:07.731Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.732Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.732Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:24:07.732Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.732Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:24:07.733Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.734Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.737Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:24:07.737Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:24:07.753Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:24:07.783Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:24:07.783Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:24:07.784Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:24:07.784Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:24:07.784Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.784Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:24:07.784Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.785Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.785Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:24:07.785Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.785Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:24:07.785Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.787Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.789Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:24:07.790Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:24:07.805Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:24:07.810Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:24:07.810Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:24:07.811Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:24:07.811Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:24:07.811Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.811Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:24:07.812Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.812Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.812Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:24:07.812Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.813Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:24:07.813Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.814Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:07.817Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:24:07.817Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:24:07.834Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:24:12.503Z  [GET | Reports] info: Retrieving report 1 for user 2... 
2025-07-27T12:24:12.507Z  [report.controller | getComments] info: User 2 requested comments for report 1 
2025-07-27T12:24:12.511Z  [GET | Report] info: No report 1 found for user 2. 
2025-07-27T12:24:12.525Z  [report.controller | getComments] info: User 2 requested comments for report 1 
2025-07-27T12:24:17.086Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:24:17.086Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:24:17.086Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:24:17.087Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:24:17.087Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.088Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:24:17.088Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.088Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.088Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:24:17.088Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.089Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:24:17.089Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.091Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.096Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:24:17.096Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:24:17.113Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:24:17.116Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:24:17.116Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:24:17.117Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:24:17.117Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:24:17.117Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.117Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:24:17.118Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.118Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.118Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:24:17.118Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.119Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:24:17.119Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.121Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.123Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:24:17.124Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:24:17.139Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:24:17.143Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:24:17.143Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:24:17.144Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:24:17.144Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:24:17.144Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.144Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:24:17.145Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.145Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.145Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:24:17.145Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.146Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:24:17.146Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.148Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.153Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:24:17.154Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:24:17.171Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:24:17.176Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:24:17.176Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:24:17.176Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:24:17.176Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:24:17.177Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.177Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:24:17.177Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.177Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.177Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:24:17.178Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.178Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:24:17.178Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.180Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.182Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:24:17.183Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:24:17.197Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:24:17.202Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:24:17.203Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:24:17.203Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:24:17.203Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:24:17.203Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.204Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:24:17.204Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.204Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.204Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:24:17.205Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.205Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:24:17.205Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.207Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.210Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:24:17.211Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:24:17.225Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:24:17.241Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:24:17.242Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:24:17.242Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:24:17.242Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:24:17.242Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.243Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:24:17.243Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.243Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.243Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:24:17.244Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.244Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:24:17.244Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.245Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.247Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:24:17.248Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:24:17.269Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:24:17.276Z 2 [GET | Unified Business Dashboard] info: Retrieving unified dashboard data for user 2 
2025-07-27T12:24:17.276Z 2 [GET | Unified Business Dashboard] info: Calculating dashboard data for effective user ID: 2 
2025-07-27T12:24:17.277Z 2 [GET | Unified Business Dashboard] info: Fetching metrics data 
2025-07-27T12:24:17.278Z 2 [Dashboard Metrics] info: Getting all dashboard metrics for user 2 
2025-07-27T12:24:17.279Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.279Z 2 [Dashboard Metrics] info: Getting severity and trend metrics for user 2 
2025-07-27T12:24:17.280Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.281Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.281Z 2 [Dashboard Metrics] info: Calculating retest summary metrics for user 2 
2025-07-27T12:24:17.281Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.282Z 2 [Dashboard Metrics] info: Retrieving retest actions data for user 2 
2025-07-27T12:24:17.282Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.284Z 2 [Dashboard Metrics] info: Getting programs for user 2 
2025-07-27T12:24:17.294Z 2 [Dashboard Metrics] info: Calculating severity overview for user 2 
2025-07-27T12:24:17.295Z 2 [Dashboard Metrics] info: Calculating reports trend for user 2, all historical data 
2025-07-27T12:24:17.314Z 2 [GET | Unified Business Dashboard] info: Successfully retrieved unified dashboard data for user 2 
2025-07-27T12:24:47.566Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:24:47.567Z  [Jira Webhook | Status Change] info: Issue CTB-1: Done → To Do 
2025-07-27T12:24:47.567Z  [Jira Webhook | Prevent Reopening] warn: Attempt to move closed issue CTB-1 from 'Done' to 'To Do'. Checking if this should be prevented. 
2025-07-27T12:24:47.573Z  [Jira Webhook | Reverting Reopening] warn: Preventing reopening of closed report. Reverting CTB-1 back to 'Done'. 
2025-07-27T12:24:47.573Z  [Jira Integration | Revert Status] info: Attempting to revert Jira issue CTB-1 to 'Done' status 
2025-07-27T12:24:48.094Z  [Jira Integration | Revert Status] info: Available transitions for CTB-1: To Do, In Progress, Done 
2025-07-27T12:24:48.094Z  [Jira Integration | Revert Status] info: Found transition to 'Done' (ID: 31) for issue CTB-1 
2025-07-27T12:24:48.645Z  [Jira Integration | Revert Status] info: Successfully reverted Jira issue CTB-1 to 'Done' status 
2025-07-27T12:24:48.986Z  [Jira Webhook | Received] info: Event: jira:issue_updated, Issue: CTB-1 
2025-07-27T12:24:48.986Z  [Jira Webhook | Status Change] info: Issue CTB-1: To Do → Done 
2025-07-27T12:24:48.987Z  [Jira Integration | Process Closure] info: Processing closure request for CTB-1 with status Done 
2025-07-27T12:24:48.990Z  [Jira Integration | Already Closed] info: Report for CTB-1 is already closed. Ignoring closure request to prevent infinite loop. 
2025-07-27T12:28:16.392Z  [dashboardMetrics] info: Optimised module loaded 
