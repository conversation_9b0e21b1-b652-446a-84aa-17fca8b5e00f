import React, { useState } from "react";
import { Pie } from "react-chartjs-2";
import { Chart as ChartJ<PERSON>, ArcElement, <PERSON><PERSON><PERSON>, Legend } from "chart.js";

ChartJS.register(ArcElement, Tooltip, Legend);

interface ResearcherPieChartProps {
  programsWithResearchers: Array<{
    programId: number;
    programTitle: string;
    researchers: Array<{
      userId: number;
      username: string;
      reportCount: number;
    }>;
  }>;
}

const ResearcherPieChart: React.FC<ResearcherPieChartProps> = ({
  programsWithResearchers
}) => {
  const [selectedProgram, setSelectedProgram] = useState<number | "all">("all");

  // Generate dynamic shades of blue-700
  const generateDynamicColors = (count: number) => {
    const baseColor = [29, 78, 216]; // rgb values for blue-700
    return Array.from({ length: count }, (_, index) => {
      const shade = 1 - index / (count * 1.5);
      return `rgba(${baseColor[0]}, ${baseColor[1]}, ${baseColor[2]}, ${shade})`;
    });
  };

  // Aggregate researchers' report counts when "All Programs" is selected
  const aggregateResearchers = () => {
    const researcherMap: { [key: string]: { reportCount: number } } = {};

    programsWithResearchers.forEach(program => {
      program.researchers.forEach(researcher => {
        if (researcherMap[researcher.username]) {
          researcherMap[researcher.username].reportCount +=
            researcher.reportCount;
        } else {
          researcherMap[researcher.username] = {
            reportCount: researcher.reportCount
          };
        }
      });
    });

    return Object.entries(researcherMap).map(([username, { reportCount }]) => ({
      username,
      reportCount
    }));
  };

  const combinedResearchers =
    selectedProgram === "all"
      ? aggregateResearchers()
      : programsWithResearchers.find(
          program => program.programId === selectedProgram
        )?.researchers || [];

  const dynamicColors = generateDynamicColors(combinedResearchers.length);

  const chartData = {
    labels: combinedResearchers.map(researcher => researcher.username),
    datasets: [
      {
        label: "Report Count",
        data: combinedResearchers.map(researcher => researcher.reportCount),
        backgroundColor: dynamicColors,
        hoverBackgroundColor: dynamicColors.map(color =>
          color.replace("rgba(29, 78, 216", "rgba(59, 130, 246")
        ),
        borderWidth: 2,
        borderColor: "#ffffff"
      }
    ]
  };

  const chartOptions = {
    plugins: {
      legend: {
        position: "bottom" as const,
        labels: {
          padding: 20,
          usePointStyle: true,
          pointStyle: "circle",
          font: {
            size: 12,
            family: "'Inter', sans-serif"
          }
        }
      },
      tooltip: {
        backgroundColor: "rgba(17, 24, 39, 0.8)",
        padding: 12,
        bodyFont: {
          size: 13,
          family: "'Inter', sans-serif"
        },
        titleFont: {
          size: 14,
          family: "'Inter', sans-serif",
          weight: "bold"
        }
      }
    },
    layout: {
      padding: 20
    },
    responsive: true,
    maintainAspectRatio: false
  };

  return (
    <div className="overflow-hidden rounded-xl bg-white shadow-lg">
      <div className="bg-gradient-to-r from-blue-700 to-blue-600 p-4">
        <h2 className="text-xl font-semibold text-white">
          Researcher Report Distribution
        </h2>
      </div>

      <div className="p-6">
        <select
          id="program-filter"
          value={selectedProgram}
          onChange={e =>
            setSelectedProgram(
              e.target.value === "all" ? "all" : parseInt(e.target.value)
            )
          }
          className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2.5 text-sm text-gray-700 transition-all duration-200 ease-in-out focus:border-transparent focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Programs</option>
          {programsWithResearchers.map(program => (
            <option key={program.programId} value={program.programId}>
              {program.programTitle}
            </option>
          ))}
        </select>

        <div className="mt-6 h-[335px]">
          <Pie data={chartData} options={chartOptions} />
        </div>
      </div>
    </div>
  );
};

export default ResearcherPieChart;
