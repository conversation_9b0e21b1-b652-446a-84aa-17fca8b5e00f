import React from 'react';
import HtmlEditor from '../HtmlEditor';
import { ReportData } from '../../types/report.types';

interface ConclusionEditorProps {
  reportData: ReportData;
  onHtmlChange: (field: string, value: string) => void;
}

const ConclusionEditor: React.FC<ConclusionEditorProps> = ({ reportData, onHtmlChange }) => {
  return (
    <div className="space-y-8">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
          <h3 className="text-xl font-semibold text-gray-800">Conclusion</h3>
          <p className="text-sm text-gray-600 mt-1">Edit the conclusion content below</p>
        </div>
        <div className="p-6">
          <HtmlEditor
            field="conclusion"
            title="Conclusion"
            reportData={reportData}
            onHtmlChange={onHtmlChange as any}
          />
        </div>
      </div>
    </div>
  );
};

export default ConclusionEditor; 