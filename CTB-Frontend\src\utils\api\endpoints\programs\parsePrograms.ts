import moment from "moment";
import { BaseItem } from "../../axios";
import { RewardTiers } from "../../../../components/editor/inputs/programs/ProgramRewardsInput";
import { DATE_FORMAT } from "../../../..";
import { ProgramUpdateValues } from "../../../hooks/programs/useProgram";
import DOMPurify from "dompurify";

export type ProgramCreator = {
  displayName: string;
  pfp?: string;
};

export type ProgramAPIResponse = {
  program_id: number;
  is_activated: boolean;
  user_id: number;
  is_delete: boolean;
  private: boolean;
  private_access_users: number[];
  type: ProgramType;
  triage_service_opted: string;
  payout_range: string;
  program_title: string;
  description: string;
  scope: string;
  out_of_scope: string;
  known_vuln: string;
  other: string;
  targets: string;
  vpn: string;
  credentials: string;
  reward_policy: string;
  tos: string;
  pfp: string;
  createdAt: string;
  updatedAt: string;
  creator?: ProgramCreator;
  testing_type?: string;
  environment_type?: string;
  compliance_type?: string;
  other_compliance_type?: string;
  expected_start_date?: string;
  expected_end_date?: string;
  slack_channel_link: string;
  notification_methods?: string[];
  attachments?: string[];
  test_lead?: string;
  prepared_by?: string;
  reviewed_by?: string;
  approved_by?: string;
  jira_url?: string;
  jira_email?: string;
  jira_api_token?: string;
  jira_project_key?: string;
};

export type CTBProgram = {
  title: string;
  isActivated: boolean;
  isDelete: boolean;
  private: boolean;
  triaged: boolean;
  targets: ProgramTarget[];
  updatedAt: string;
  createdAt: string;
  userId?: number;
  privateAccessUsers?: number[];
  type?: ProgramType;
  payoutRange?: RewardTiers;
  description?: string;
  scope?: string;
  knownVulnerabilities?: string;
  other?: string;
  numTargets?: string;
  vpn?: string;
  credentials?: string;
  rewardPolicy?: string;
  termsOfService?: string;
  profilePicture?: string;
  outOfScope?: string;
  creator?: ProgramCreator;
  testing_type?: string;
  environment_type?: string;
  compliance_type?: string;
  other_compliance_type?: string;
  expected_start_date?: string;
  expected_end_date?: string;
  slack_channel_link: string;
  notification_methods?: string[];
  attachments?: string[];
  test_lead?: string;
  prepared_by?: string;
  reviewed_by?: string;
  approved_by?: string;
} & BaseItem;

export enum TargetType {
  WEB = "Web",
  IOS = "iOS",
  ANDROID = "Android",
  API = "API",
  WEB3 = "Web3",
  CODE = "Code",
  PROTOCOL = "Protocol",
  DATABASE = "Database",
  NETWORK = "Network/Infrastructure",
  CONTRACT = "Smart Contract",
  OTHER = "Other"
}

export type ProgramTarget = { targetName: string; targetType: TargetType };

export enum ProgramType {
  BugBounty = "Bug Bounty",
  VDP = "VDP",
  PTAAS = "PTAAS"
}

const parsePayoutRange = (range?: string): RewardTiers | undefined => {
  const values = range?.split(",").map(v => parseInt(v));

  return values && values.length === 4
    ? {
        low: values[0],
        medium: values[1],
        high: values[2],
        critical: values[3]
      }
    : undefined;
};

export const parseProgramDetails = (
  program: ProgramAPIResponse
): CTBProgram => {
  // Parse attachments properly
  let attachments: string[] = [];
  if (program.attachments) {
    if (typeof program.attachments === "string") {
      try {
        attachments = JSON.parse(program.attachments);
      } catch (e) {
        console.error("Failed to parse attachments:", e);
        attachments = [];
      }
    } else if (Array.isArray(program.attachments)) {
      attachments = program.attachments;
    }
  }

  return {
    id: program.program_id,
    isActivated: program.is_activated,
    userId: program.user_id,
    isDelete: program.is_delete,
    private: program.private,
    privateAccessUsers: program.private_access_users,
    type: program.type,
    triaged: program.triage_service_opted === "1",
    payoutRange: parsePayoutRange(program.payout_range),
    title: program.program_title,
    description: program.description,
    scope: program.scope,
    knownVulnerabilities: program.known_vuln,
    other: program.other,
    numTargets: JSON.parse(program.targets),
    vpn: program.vpn,
    credentials: program.credentials,
    rewardPolicy: program.reward_policy,
    termsOfService: program.tos,
    profilePicture: program.pfp,
    outOfScope: program.out_of_scope,
    targets: JSON.parse(program.targets) || [],
    creator: program.creator,
    updatedAt: moment(program.updatedAt).format(DATE_FORMAT),
    createdAt: moment(program.createdAt).format(DATE_FORMAT),
    testing_type: program.testing_type,
    compliance_type: program.compliance_type,
    other_compliance_type: program.other_compliance_type,
    environment_type: program.environment_type,
    expected_start_date: program.expected_start_date,
    expected_end_date: program.expected_end_date,
    slack_channel_link: program.slack_channel_link,
    notification_methods: program.notification_methods,
    attachments: attachments, // Use the properly parsed attachments
    test_lead: program.test_lead,
    prepared_by: program.prepared_by,
    reviewed_by: program.reviewed_by,
    approved_by: program.approved_by
  };
};

/**
 * Convert program update fields into form data
 * to be used in an API request
 */
export const prepareProgramFormData = ({
  details,
  icon,
  attachments
}: {
  details: ProgramUpdateValues & {
    payoutRange?: number[];
    triaged?: boolean;
    existingAttachments?: string[];
  };
  icon?: Blob;
  attachments?: Blob[];
}) => {
  // Format payload for backend API
  console.log("Details before constructing body:", {
    ...details,
    jira_url: details.jira_url ? "[EXISTS]" : "[MISSING]",
    jira_email: details.jira_email ? "[EXISTS]" : "[MISSING]",
    jira_api_token: details.jira_api_token ? "[EXISTS]" : "[MISSING]",
    jira_project_key: details.jira_project_key ? "[EXISTS]" : "[MISSING]",
    slack_channel_link: details.slack_channel_link ? "[EXISTS]" : "[MISSING]"
  });
  
  const body = {
    program_title: details.title,
    description: details.description,
    scope: details.scope,
    out_of_scope: details.outOfScope,
    known_vuln: details.knownVulnerabilities,
    other: details.other,
    payout_range: details.payoutRange?.join(","),
    targets: details.targets,
    vpn: details.vpn,
    tos: details.termsOfService,
    credentials: details.credentials,
    reward_policy: details.rewardPolicy,
    type: details.type,
    private: details.private,
    triage_service_opted: details.triaged,
    compliance_type: details.complianceType,
    other_compliance_type: details.otherComplianceType,
    testing_type: details.testingType,
    environment_type: details.environmentType,
    expected_start_date: details.expectedStartDate,
    expected_end_date: details.expectedEndDate,
    notification_methods: details.notification_methods,
    // Individual Jira fields
    jira_url: details.jira_url,
    jira_email: details.jira_email,
    jira_api_token: details.jira_api_token,
    jira_project_key: details.jira_project_key,
    // Slack channel link
    slack_channel_link: details.slack_channel_link,
    // Include the existing attachments that weren't deleted
    existing_attachments: details.existingAttachments || []
  };

  const formData = new FormData();
  formData.append("data", JSON.stringify(body));
  
  // Only append profile picture if provided
  if (icon) {
    formData.append("program_pfp", icon);
  }

  // Append all attachments if provided
  if (attachments && attachments.length > 0) {
    attachments.forEach(file => {
      formData.append("attachments", file);
    });
  }

  return formData;
};

export enum NotificationMethods {
  CTB = "notify-ctb",
  SLACK = "notify-slack",
  JIRA = "notify-jira"
}

export type ProgramOverview = {
  id: number;
  status: string;
  totalReports: number;
  testingPeriod: string | null;
  testingStatus: string;
  createdAt: string;
  description: string;
  expected_start_date?: string;
  type?: string;
};

export type UserDetails = {
  username: string;
  profilePicture: string;
  about: string;
  displayName: string;
};

export type ProgramOverviewResponse = {
  program: ProgramOverview;
  user: UserDetails;
};

export const parseProgramOverview = (data: any): ProgramOverviewResponse => {
  const about = data.user?.about?.trim();
  const rawDescription =
    data.program?.description?.trim() || "No description available";

  return {
    program: {
      id: data.program?.id ?? 0,
      status: data.program?.status ?? "Inactive",
      totalReports: data.program?.total_reports ?? 0,
      testingPeriod: data.program?.testing_period ?? "N/A",
      testingStatus: data.program?.testing_status ?? "N/A",
      type: data.program?.type,
      expected_start_date: data.program?.expected_start_date
        ? moment(data.program.expected_start_date).format("YYYY-MM-DD")
        : "Invalid date",
      createdAt: data.program?.created_at
        ? moment(data.program.created_at).format("YYYY-MM-DD")
        : "Invalid date",
      description: DOMPurify.sanitize(rawDescription) // Sanitize HTML before storing
    },
    user: {
      username: data.user?.username ?? "Unknown",
      profilePicture: data.user?.profile_picture ?? "",
      about: about || rawDescription, // Fallback to description if about is empty
      displayName: data.user?.display_name ?? data.user?.username ?? "Unknown"
    }
  };
};
