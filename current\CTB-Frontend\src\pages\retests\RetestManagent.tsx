import React, { useEffect, useState } from "react";
import { getRetestsByUser } from "../../utils/api/endpoints/retests/retests";
import { CTBRetest } from "../../utils/api/endpoints/retests/parseRetests";
import { useNavigate, useSearchParams } from "react-router-dom";
import usePageTitle from "../../utils/hooks/usePageTitle";
import RetestTable from "../../components/retests/Table/RetestTable";
import LoadingSpinner from "../../components/retests/utils/LoadingSpinner";
import { FiFilter, FiX } from "react-icons/fi";

const RetestManagement: React.FC = () => {
  const [retests, setRetests] = useState<CTBRetest[]>([]);
  const [filteredRetests, setFilteredRetests] = useState<CTBRetest[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const statusFilter = searchParams.get("status");
  
  usePageTitle("Vulnerability Retests");

  useEffect(() => {
    const fetchRetests = async () => {
      try {
        const response = await getRetestsByUser();
        setRetests(response.data);
      } catch (error) {
        console.error("Failed to fetch retests:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchRetests();
  }, []);

  // Apply filter when retests or statusFilter changes
  useEffect(() => {
    if (statusFilter && retests.length > 0) {
      const filtered = retests.filter(retest => retest.status === statusFilter);
      setFilteredRetests(filtered);
    } else {
      setFilteredRetests(retests);
    }
  }, [retests, statusFilter]);

  const handleRowClick = (retest: CTBRetest) => {
    navigate(`/dashboard/retests/${retest.retest_id}`);
  };

  const clearFilter = () => {
    setSearchParams({});
  };

  return (
    <div className="container mx-auto px-8 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-ctb-blue">
          Vulnerability Retests
        </h1>
        
        {statusFilter && (
          <div className="flex items-center bg-blue-50 text-blue-700 px-4 py-2 rounded-md">
            <FiFilter className="mr-2" />
            <span>Filtered by: <strong>{statusFilter}</strong></span>
            <button 
              onClick={clearFilter}
              className="ml-3 p-1 hover:bg-blue-100 rounded-full"
              title="Clear filter"
            >
              <FiX />
            </button>
          </div>
        )}
      </div>
      
      {loading ? (
        <LoadingSpinner />
      ) : filteredRetests.length === 0 ? (
        <div className="text-center py-10">
          <p className="text-lg text-gray-500 mb-4">
            {statusFilter 
              ? `No retests found with status "${statusFilter}".` 
              : "No retests found."}
          </p>
          {statusFilter && (
            <button 
              onClick={clearFilter}
              className="px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-md transition-colors"
            >
              Clear filter
            </button>
          )}
        </div>
      ) : (
        <RetestTable retests={filteredRetests} onRowClick={handleRowClick} />
      )}
    </div>
  );
};

export default RetestManagement;
