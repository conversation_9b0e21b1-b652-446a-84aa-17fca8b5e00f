import axios from "../../axios";

const URL = "/v2/invitation";

// Protected routes (requires authentication)
export const createInvitation = async (data: { email: string; role: number }) => {
  const response = await axios.post(URL, data);
  return response.data;
};

export const getInvitations = async () => {
  const response = await axios.get(URL);
  return response.data;
};

export const deleteInvitation = async (id: number) => {
  const response = await axios.delete(`${URL}/${id}`);
  return response.data;
};

// Public routes
export const verifyInvitation = async (token: string) => {
  const response = await axios.get(`${URL}/verify/${token}`);
  return response.data;
};

export const completeInvitedUserRegistration = async (token: string, data: {
  password: string;
  username: string;
  display_name?: string;
  country?: string;
}) => {
  const response = await axios.post(`${URL}/${token}/complete-registration`, data);
  return response.data;
};

// Legacy routes
export const legacyCreateInvitation = async (data: { email: string; role: number }) => {
  const response = await axios.post("/invitation/send", data);
  return response.data;
};

export const legacyVerifyInvitation = async (token: string) => {
  const response = await axios.get(`/invitation/validate/${token}`);
  return response.data;
};
